package com.quhong.dto;

import java.util.List;

public class InnerPublishMomentDTO {
    private String uid; // 发布朋友圈的用户
    private String text; // 朋友圈正文内容
    private int show; // 1 公开，2 朋友可见  3 仅自己可见
    private List<MomentImageDTO> imgs; // 朋友圈图片
    private String activeId; // 活动id，埋点用
    private Quote quote; // 引用对象，转发朋友圈或者发布链接
    @Deprecated
    private String location; // 地理位置，新版本已弃用该字段
    private Integer topicRid; // 话题rid

    public static class Quote {
        private int type; // 1转发动态 2分享链接 3YouTube链接 4Youstar官方链接(需要拼接uid&token) 5分享房间 6活动分享
        private String content; // 引用内容，原博内容或网页title
        private String icon; // 引用的图标，原博主头像、原博第一个图片、网站图标等
        private String action; // type=1为mid、type=2为普通link、type=3为YouTube链接
        private String videoId; // 视频id，YouTube才有值

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getVideoId() {
            return videoId;
        }

        public void setVideoId(String videoId) {
            this.videoId = videoId;
        }
    }

    public static class MomentImageDTO {
        private String url;
        private String width;
        private String height;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getWidth() {
            return width;
        }

        public void setWidth(String width) {
            this.width = width;
        }

        public String getHeight() {
            return height;
        }

        public void setHeight(String height) {
            this.height = height;
        }
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public int getShow() {
        return show;
    }

    public void setShow(int show) {
        this.show = show;
    }

    public String getActiveId() {
        return activeId;
    }

    public void setActiveId(String activeId) {
        this.activeId = activeId;
    }

    public Quote getQuote() {
        return quote;
    }

    public void setQuote(Quote quote) {
        this.quote = quote;
    }

    public List<MomentImageDTO> getImgs() {
        return imgs;
    }

    public void setImgs(List<MomentImageDTO> imgs) {
        this.imgs = imgs;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getTopicRid() {
        return topicRid;
    }

    public void setTopicRid(Integer topicRid) {
        this.topicRid = topicRid;
    }
}
