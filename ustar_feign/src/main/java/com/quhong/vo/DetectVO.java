package com.quhong.vo;

public class DetectVO {
    // 0:不安全、 1:安全
    private int isSafe;
    // 处理后的视频链接
    private String newVideoUrl;
    // 图片中命中的匹配文字，有值为命中的值，其他为没命中
    private String hitText;

    public DetectVO() {
    }

    public DetectVO(int isSafe) {
        this.isSafe = isSafe;
    }

    public DetectVO(int isSafe, String newVideoUrl) {
        this.isSafe = isSafe;
        this.newVideoUrl = newVideoUrl;
    }

    public int getIsSafe() {
        return isSafe;
    }

    public void setIsSafe(int isSafe) {
        this.isSafe = isSafe;
    }

    public String getNewVideoUrl() {
        return newVideoUrl;
    }

    public void setNewVideoUrl(String newVideoUrl) {
        this.newVideoUrl = newVideoUrl;
    }

    public String getHitText() {
        return hitText;
    }

    public void setHitText(String hitText) {
        this.hitText = hitText;
    }
}
