package com.quhong.feign;

import com.quhong.data.dto.FriendApplyDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;
import java.util.Set;


/**
 * 仅k8s服务可用
 */
@FeignClient(name = "ustar-user-info", url = "ustar-user-info:8080", fallback = IFriendService.DefaultFallback.class)
public interface IFriendService {

    /**
     * 获取好友列表
     */
    @PostMapping(value = "/inner/user_info/getFriendSet")
    ApiResult<Set<String>> getFriendSet(@RequestParam(value = "uid") String uid);

    /**
     * 获取好友数量
     */
    @PostMapping(value = "/inner/user_info/getFriendCount")
    ApiResult<Integer> getFriendCount(@RequestParam(value = "uid") String uid);

    /**
     * 分页获取好友列表
     */
    @PostMapping("/inner/user_info/getPageFriendList")
    ApiResult<List<String>> getPageFriendList(@RequestParam(value = "uid") String uid,
                                              @RequestParam(value = "start") int start,
                                              @RequestParam(value = "end") int end);

    /**
     * 获取好友列表
     */
    @PostMapping(value = "/inner/user_info/getAllOnlineSet")
    ApiResult<Set<String>> getAllOnlineSet();

    /**
     * 添加好友请求
     */
    @PostMapping(value = "/inner/user_info/addFriendApply")
    ApiResult<List<String>> addFriendApply(@RequestParam(value = "uid") String uid,
                                           @RequestParam(value = "aid") String aid,
                                           @RequestParam(value = "msg") String msg);

    /**
     * 成为好友
     */
    @PostMapping(value = "/inner/user_info/becomeFriend")
    ApiResult<?> becomeFriend(@RequestBody FriendApplyDTO dto) ;

    @Component
    class DefaultFallback implements IFriendService {
        private static final Logger logger = LoggerFactory.getLogger(IFriendService.class);

        @Override
        public ApiResult<Set<String>> getFriendSet(String uid) {
            logger.error("getFriendSet error uid={}", uid);
            return ApiResult.getError(HttpCode.SERVER_ERROR, Collections.emptySet());
        }

        @Override
        public ApiResult<Integer> getFriendCount(String uid) {
            logger.error("getFriendCount error uid={}", uid);
            return ApiResult.getError(HttpCode.SERVER_ERROR, 0);
        }

        @Override
        public ApiResult<List<String>> getPageFriendList(String uid, int start, int end) {
            logger.error("getPageFriendList error uid={} start={} end={}", uid, start, end);
            return ApiResult.getError(HttpCode.SERVER_ERROR, Collections.emptyList());
        }

        @Override
        public ApiResult<Set<String>> getAllOnlineSet() {
            logger.error("getAllOnlineSet error");
            return ApiResult.getError(HttpCode.SERVER_ERROR, Collections.emptySet());
        }

        @Override
        public ApiResult<List<String>> addFriendApply(String uid, String aid, String msg) {
            logger.error("addFriendApply error uid={} aid={} msg={}", uid, aid, msg);
            return ApiResult.getError(HttpCode.SERVER_ERROR, Collections.emptyList());
        }

        @Override
        public ApiResult<?> becomeFriend(FriendApplyDTO dto) {
            logger.error("becomeFriend error dto={}", dto);
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
    }
}
