package com.quhong.feign;

import com.alibaba.fastjson.JSON;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerMomentTopicUpdateDTO;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 仅k8s服务可用
 */
@FeignClient(name = "ustar-java-moment", url = "ustar-java-moment:8080", fallback = IMomentService.DefaultFallback.class)
public interface IMomentService {


    /**
     * 分享时增加朋友圈转发数
     */
    @PostMapping("/inner/moment/shareMoment")
    ApiResult<HttpCode> shareMoment(@RequestParam(value = "uid") String uid,
                                    @RequestParam(value = "mid") String mid);

    /**
     * 内部发送朋友圈，发布成功后返回data为null
     */
    @PostMapping("/inner/moment/publish")
    HttpResult<String> publish(@RequestBody InnerPublishMomentDTO dto);

    /**
     * 分享时增加朋友圈转发数
     */
    @PostMapping("/inner/moment/likeMoment")
    ApiResult<HttpCode> likeMoment(@RequestParam(value = "uid") String uid,
                                   @RequestParam(value = "mid") String mid);

    /**
     * 内部创建话题
     */
    @PostMapping("/inner/moment/createMomentTopic")
    ApiResult<Object> createMomentTopic(@RequestBody InnerMomentTopicUpdateDTO dto);

    /**
     * 内部更新话题
     */
    @PostMapping("/inner/moment/updateMomentTopic")
    ApiResult<Object> updateMomentTopic(@RequestBody InnerMomentTopicUpdateDTO dto);

    /**
     * 上报数数分享人数
     */
    @PostMapping("/inner/moment/reportShareMoment")
    ApiResult<HttpCode> reportShareMoment(@RequestParam(value = "uid") String uid,
                                          @RequestParam(value = "actionId") String actionId,
                                          @RequestParam(value = "shareType") int shareType,
                                          @RequestParam(value = "shareNum") int shareNum);

    /**
     * 朋友圈置顶
     *
     * @param mid    朋友圈id
     * @param opType 1置顶 2取消
     */
    @PostMapping("/inner/moment/pinMoment")
    ApiResult<HttpCode> pinMoment(@RequestParam(value = "mid") String mid,
                                  @RequestParam(value = "opType") Integer opType,
                                  @RequestParam(value = "endTime") Integer endTime);

    @Component
    class DefaultFallback implements IMomentService {
        private static final Logger logger = LoggerFactory.getLogger(IMomentService.class);

        @Override
        public ApiResult<HttpCode> shareMoment(String uid, String mid) {
            logger.error("shareMoment error uid={} mid={}", uid, mid);
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public HttpResult<String> publish(InnerPublishMomentDTO dto) {
            logger.error("publish moment error uid={} mid={}", dto.getUid(), JSON.toJSONString(dto));
            return HttpResult.getError();
        }

        @Override
        public ApiResult<HttpCode> likeMoment(String uid, String mid) {
            logger.error("likeMoment error uid={} mid={}", uid, mid);
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }


        @Override
        public ApiResult<Object> createMomentTopic(InnerMomentTopicUpdateDTO dto) {
            logger.error("createMomentTopic error uid={} dto={}", dto.getUid(), JSON.toJSONString(dto));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<Object> updateMomentTopic(InnerMomentTopicUpdateDTO dto) {
            logger.error("updateMomentTopic error aid={} dto={}", dto.getAid(), JSON.toJSONString(dto));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<HttpCode> reportShareMoment(String uid, String mid, int shareType, int shareNum) {
            logger.error("reportShareMoment error uid={} mid={} shareType={} shareNum={}",
                    uid, mid, shareType, shareNum);
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<HttpCode> pinMoment(String mid, Integer opType, Integer endTime) {
            logger.error("pinMoment error mid={} opType={}", mid, opType);
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
    }
}
