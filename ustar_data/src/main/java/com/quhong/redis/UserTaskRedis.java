package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/9/26
 */
@Component
public class UserTaskRedis {

    private static final Logger logger = LoggerFactory.getLogger(UserTaskRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainCluster;

    public void saveTnGetRewardRecord(String taskKey, String tnId) {
        String key = getTnGetRewardRecordKey(taskKey);
        try {
            mainCluster.opsForSet().add(key, tnId);
            mainCluster.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveTnGetRewardRecord error. taskKey={} tnId={} {}", taskKey, tnId, e.getMessage(), e);
        }
    }


    public boolean tnHasGotReward(String taskKey, String tnId) {
        try {
            return Boolean.TRUE.equals(mainCluster.opsForSet().isMember(getTnGetRewardRecordKey(taskKey), tnId));
        } catch (Exception e) {
            logger.error("tnHasGotReward error. taskKey={} tnId={} {}", taskKey, tnId, e.getMessage(), e);
        }
        return false;
    }

    public void incUserHasRewardCount(String uid, int count) {
        String key = getUserHasRewardCountKey();
        try {
            mainCluster.opsForHash().increment(key, uid, count);
            mainCluster.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incUserHasRewardCount error. uid={} count={} {}", uid, count, e.getMessage(), e);
        }
    }

    public int getUserHasRewardCount(String uid) {
        String key = getUserHasRewardCountKey();
        try {
            String strCount = (String)mainCluster.opsForHash().get(key, uid);
            if (!StringUtils.hasLength(strCount)) {
                return 0;
            }
            return Integer.parseInt(strCount);
        } catch (Exception e) {
            logger.error("getUserHasRewardCount error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    private String getTnGetRewardRecordKey (String taskKey) {
        return "set:getTaskRewardRecord_" + DateHelper.ARABIAN.formatDateInDay() + "_" + taskKey;
    }

    private String getUserHasRewardCountKey () {
        return "hash:userTaskHasRewardCount_" + DateHelper.ARABIAN.formatDateInDay();
    }

    /**
     * web版完成任务数
     */
    private String getWebUserHasRewardCountKey () {
        return "hash:webUserTaskHasRewardCount_" + DateHelper.ARABIAN.formatDateInDay();
    }

    public void incWebUserHasRewardCount(String uid, int count) {
        String key = getWebUserHasRewardCountKey();
        try {
            mainCluster.opsForHash().increment(key, uid, count);
            mainCluster.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incWebUserHasRewardCount error. uid={} count={} {}", uid, count, e.getMessage(), e);
        }
    }

    public int getWebUserHasRewardCount(String uid) {
        String key = getWebUserHasRewardCountKey();
        try {
            String strCount = (String) mainCluster.opsForHash().get(key, uid);
            if (!StringUtils.hasLength(strCount)) {
                return 0;
            }
            return Integer.parseInt(strCount);
        } catch (Exception e) {
            logger.error("getWebUserHasRewardCount error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }


    /**
     * 新用户停留房间任务状态
     */
    private String getUserStayInRoomKey(String uid) {
        return String.format("str:userStayInRoom:%s", uid);
    }

    public int getUserStayInRoomStatus(String uid) {
        String key = getUserStayInRoomKey(uid);
        try {
            String statusStr = mainCluster.opsForValue().get(key);
            if (ObjectUtils.isEmpty(statusStr)) {
                return 0;
            }
            return Integer.parseInt(statusStr);
        } catch (Exception e) {
            logger.error("getUserStayInRoomStatus error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    public void setUserStayInRoomStatus(String uid) {
        String key = getUserStayInRoomKey(uid);
        try {
            mainCluster.opsForValue().set(key, "1", ExpireTimeConstant.EXPIRE_DAYS_TEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setUserStayInRoomStatus error. uid={} e={}", uid, e.getMessage(), e);
        }
    }
}
