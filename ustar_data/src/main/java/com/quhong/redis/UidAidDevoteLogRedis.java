package com.quhong.redis;

import com.quhong.constant.MatchConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.utils.MatchUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class UidAidDevoteLogRedis {
    private static final Logger logger = LoggerFactory.getLogger(UidAidDevoteLogRedis.class);
    private String expireDate = "";
    private static final int DEFAULT_ACTION = 0; // 目前只有发送房间礼物一个场景

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;


    public long incrementData(String roomId, String uid, String aid, long beans) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getHashKey();
            String field = getField(roomId, uid, aid, DEFAULT_ACTION);
            Long totalValue = clusterRedis.opsForHash().increment(key, field, beans);
            if (!dateStr.equals(expireDate)) {
                clusterRedis.expire(key, 7, TimeUnit.DAYS);
                expireDate = dateStr;
            }
            return totalValue;
        } catch (Exception e) {
            logger.error("incrementData error roomId:{} uid:{} aid:{}", roomId, uid, aid, e);
            return 0;
        }
    }


    public Set<String> getAllField() {
        Set<String> result = new HashSet<>();
        try {
            Set<Object> fields = clusterRedis.opsForHash().keys(getHashKey());
            for (Object field : fields) {
                result.add(String.valueOf(field));
            }
            return result;
        } catch (Exception e) {
            logger.error("getAllField error. {}", e.getMessage(), e);
            return result;
        }
    }


    public Map<String, Long> getAllData() {
        try {
            Map<String, Long> resultMap = new HashMap<>();
            Map<Object, Object> map = clusterRedis.opsForHash().entries(getHashKey());
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                try {
                    String value = String.valueOf(entry.getValue());
                    resultMap.put(String.valueOf(entry.getKey()), Long.parseLong(value));
                } catch (Exception e) {
                    logger.error("getAllFieldData error key={} value={}", entry.getKey(), entry.getValue(), e);
                }
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("getAllFieldData error {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }


    public void removeField(String roomId, String uid, String aid) {
        try {
            String field = getField(roomId, uid, aid, DEFAULT_ACTION);
            clusterRedis.opsForHash().delete(getHashKey(), field);
        } catch (Exception e) {
            logger.error("removeField error.roomId:{} uid:{} aid:{}", roomId, uid, aid, e);
        }
    }

    public void removeAllField(Set<String> allField) {
        try {
            Object[] objs = allField.toArray();
            long num = clusterRedis.opsForHash().delete(getHashKey(), objs);
            logger.info("removeAllField num:{} success", num);
        } catch (Exception e) {
            logger.error("removeAllField error.msg:{}", e.getMessage(), e);
        }
    }

    private String getField(String roomId, String uid, String aid, int action) {
        return roomId + "_" + uid + "_" + aid + "_" +
                MatchUtils.getStepFromDayStart(MatchConstant.STEP_LENGTH) + "_" + action;
    }


    private String getHashKey() {
        return "hash:uid:aid:room:devote";
    }

}
