package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.PkGameInfo;
import com.quhong.data.PkHallInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/12/5
 */
@Component
public class PkGameRedis {

    private static final Logger logger = LoggerFactory.getLogger(PkGameRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    public void savePkGameInfo(String pkGameId, PkGameInfo pkGameInfo) {
        try {
            clusterTemplate.opsForValue().set(getPkGameInfoKey(pkGameId), JSONObject.toJSONString(pkGameInfo), ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save pk game info in redis error. pkGameId={}", pkGameId);
        }
    }

    public void deletePkGameInfo(String pkGameId) {
        try {
            clusterTemplate.delete(getPkGameInfoKey(pkGameId));
        } catch (Exception e) {
            logger.error("delete pk game info from redis. pkGameId={}", pkGameId);
        }
    }

    public PkGameInfo getPkGameInfo(String pkGameId) {
        try {
            String value = clusterTemplate.opsForValue().get(getPkGameInfoKey(pkGameId));
            return StringUtils.isEmpty(value) ? null : JSONObject.parseObject(value, PkGameInfo.class);
        } catch (Exception e) {
            logger.error("get pk game info from redis error. pkGameId={}", pkGameId);
            return null;
        }
    }

    public void saveUserInPkGame(String uid) {
        String key = getUserInGameKey();
        try {
            clusterTemplate.opsForSet().add(getUserInGameKey(), uid);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save user is in pk game. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public boolean isInPkGame(String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getUserInGameKey(), uid));
        } catch (Exception e) {
            logger.error("check user is in pk game. uid={} {}", uid, e.getMessage(), e);
            return false;
        }
    }

    public void removeUserInPkGame(String uid) {
        try {
            clusterTemplate.opsForSet().remove(getUserInGameKey(), uid);
        } catch (Exception e) {
            logger.error("removeUserInPkGame error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void addInvitedUser(String pkGameId, String uid) {
        try {
            clusterTemplate.opsForSet().add(getInvitedUserKey(pkGameId), uid);
        } catch (Exception e) {
            logger.error("add invited user to redis error. pkGameId={} uid={} {}", pkGameId, uid, e.getMessage(), e);
        }
    }

    public void deleteInvitedUser(String pkGameId) {
        try {
            clusterTemplate.delete(getInvitedUserKey(pkGameId));
        } catch (Exception e) {
            logger.error("delete invited user error. pkGameId={} {}", pkGameId, e.getMessage(), e);
        }
    }

    public boolean hasBeenInvited(String pkGameId, String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getInvitedUserKey(pkGameId), uid));
        } catch (Exception e) {
            logger.error("Check if user has already been invited. pkGameId={} uid={} {}", pkGameId, uid, e.getMessage(), e);
            return false;
        }
    }

    public void savePkHallInfo(PkHallInfo pkHallInfo) {
        try {
            clusterTemplate.opsForHash().put(getPkHallInfoKey(), pkHallInfo.getPkGameId(), JSONObject.toJSONString(pkHallInfo));
        } catch (Exception e) {
            logger.error("save pk game info in redis error. gameId={} {}", pkHallInfo.getPkGameId(), e.getMessage(), e);
        }
    }

    public Map<String, PkHallInfo> getPkHallInfoMap() {
        Map<String, PkHallInfo> resultMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getPkHallInfoKey());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put(entry.getKey() + "", JSONObject.parseObject(entry.getValue() + "", PkHallInfo.class));
            }
        } catch (Exception e) {
            logger.error("get pk hall info map error. {}", e.getMessage(), e);
        }
        return resultMap;
    }


    public void deletePkHallInfo(String pkGameId) {
        try {
            clusterTemplate.opsForHash().delete(getPkHallInfoKey(), pkGameId);
        } catch (Exception e) {
            logger.error("delete pk game info error. gameId={} {}", pkGameId, e.getMessage(), e);
        }
    }

    public void addPkGameWaitingTime(String gameId, int endTime) {
        try {
            clusterTemplate.opsForZSet().add(getPkGameWaitingKey(), gameId, endTime);
        } catch (Exception e) {
            logger.error("add pk game waiting time in redis error. gameId={} endTime={} {}", gameId, endTime, e.getMessage(), e);
        }
    }

    public int getPkGameWaitingTime(String gameId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getPkGameWaitingKey(), gameId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("get pk game waiting time error. gameId={} {}", gameId, e.getMessage(), e);
            return 0;
        }
    }

    public void deletePkGameWaitingTime(String gameId) {
        try {
            clusterTemplate.opsForZSet().remove(getPkGameWaitingKey(), gameId);
        } catch (Exception e) {
            logger.error("delete pk game waiting time error. gameId={} {}", gameId, e.getMessage(), e);
        }
    }

    public Set<String> getWaitEndPkGameIds(int timestamp) {
        try {
            return clusterTemplate.opsForZSet().rangeByScore(getPkGameWaitingKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get wait end pk game id from redis error. timestamp={} {}", timestamp, e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    public void addPkGameRunningTime(String gameId, int endTime) {
        try {
            clusterTemplate.opsForZSet().add(getPkGameRunningKey(), gameId, endTime);
        } catch (Exception e) {
            logger.error("add pk game running time in redis error. gameId={} endTime={} {}", gameId, endTime, e.getMessage(), e);
        }
    }

    public int getPkGameRunningTime(String gameId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getPkGameRunningKey(), gameId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("get pk game running time error. gameId={} {}", gameId, e.getMessage(), e);
            return 0;
        }
    }

    public void deletePkGameRunningTime(String gameId) {
        try {
            clusterTemplate.opsForZSet().remove(getPkGameRunningKey(), gameId);
        } catch (Exception e) {
            logger.error("delete pk game waiting time error. gameId={} {}", gameId, e.getMessage(), e);
        }
    }

    public Set<String> getHasEndedPkGameIds(int timestamp) {
        try {
            return clusterTemplate.opsForZSet().rangeByScore(getPkGameRunningKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get has ended pk game id from redis error. timestamp={} {}", timestamp, e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    public void incSendPkGiftNum(String pkGameId, String toUid, String fromUid, int number) {
        String key = getPkGiftNumKey(pkGameId, toUid);
        try {
            clusterTemplate.opsForZSet().incrementScore(key, fromUid, number);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incSendPkGiftNum error. pkGameId={} toUid={} fromUid={} number={} {}", pkGameId, toUid, fromUid, number, e.getMessage(), e);
        }
    }

    public Map<String, Integer> getPkGiftNumRankingMap(String pkGameId, String uid, int length) {
        String key = getPkGiftNumKey(pkGameId, uid);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return Collections.emptyMap();
        }
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    private String getPkGameInfoKey(String pkGameId) {
        return "str:pk_game_info_" + pkGameId;
    }

    private String getPkHallInfoKey() {
        return "hash:pk_hall_info";
    }

    private String getUserInGameKey() {
        return "set:pk_user_in_game";
    }

    private String getPkGameWaitingKey() {
        return "pk_game_waiting";
    }

    private String getPkGameRunningKey() {
        return "pk_game_running";
    }

    private String getInvitedUserKey(String pkGameId) {
        return "set:has_been_invited" + pkGameId;
    }

    private String getPkGiftCountKey(String pkGameId, String uid) {
        return pkGameId + "_" + uid;
    }

    private String getPkGiftNumKey(String pkGameId, String uid) {
        return "zset:pkGiftNum_" + pkGameId + "-" + uid;
    }
}
