package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 组件化模板redis处理方式
 */
@Lazy
@Component
public class ComponentRedis {
    private static final Logger logger = LoggerFactory.getLogger(ComponentRedis.class);
    private static final Integer MAX_SIZE = 30;


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    public Map<String, Integer> getHashAll(String hashKey) {
        Map<String, Integer> hashMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(hashKey);
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getHashAll error hashKey={} e={}", hashKey, e.getMessage(), e);
        }
        return hashMap;
    }

    public int incHashNum(String hashKey, String hKey, int num) {
        try {
            Long afterNum = clusterTemplate.opsForHash().increment(hashKey, hKey, num);
            clusterTemplate.expire(hashKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return afterNum.intValue();
        } catch (Exception e) {
            logger.error("incHashNum error hashKey={} e={}", hashKey, e);
            return num;
        }
    }

    public void setHashNum(String hashKey, String hKey, int num) {
        try {
            clusterTemplate.opsForHash().put(hashKey, hKey, String.valueOf(num));
            clusterTemplate.expire(hashKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setHashNum error hashKey={} e={}", hashKey, e);
        }
    }

    public void delHashData(String hashKey, String hKey) {
        try {
            clusterTemplate.opsForHash().delete(hashKey, hKey);
        } catch (Exception e) {
            logger.error("delHashData error hashKey={} e={}", hashKey, e);
        }
    }

    public int getHashValue(String hashKey, String hKey) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(hashKey, hKey);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getHashValue error hashKey={} hKey={}  e={}", hashKey, hKey, e);
            return 0;
        }
    }



    /**
     * str类型相关
     */
    public int getStrScore(String strKey) {
        try {
            String score = clusterTemplate.opsForValue().get(strKey);
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getStrScore error strKey={} e={}", strKey, e.getMessage(), e);
        }
        return 0;
    }

    public String getStrValue(String strKey) {
        try {
            String value = clusterTemplate.opsForValue().get(strKey);
            return value != null ? value : "";
        } catch (Exception e) {
            logger.info("getStrValue error strKey={} e={}", strKey, e.getMessage(), e);
        }
        return "";
    }

    public void setStrScore(String strKey, int score) {
        try {
            clusterTemplate.opsForValue().set(strKey, String.valueOf(score), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setStrScore error strKey={} score={} e={}", strKey, score, e.getMessage(), e);
        }
    }

    public boolean setStrIfAbsentScoreWithTime(String strKey, int score, long expireTime, TimeUnit timeUnit) {
        try {
            Boolean setFlag = clusterTemplate.opsForValue().setIfAbsent(strKey, String.valueOf(score), expireTime, timeUnit);
            return setFlag != null && setFlag;
        } catch (Exception e) {
            logger.info("setStrIfAbsentScoreWithTime error strKey={} score={} e={}", strKey, score, e.getMessage(), e);
        }
        return false;
    }

    public long incStrScore(String strKey, int score) {
        try {
            Long ret = clusterTemplate.opsForValue().increment(strKey, score);
            clusterTemplate.expire(strKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return ret == null ? 0 : ret;
        } catch (Exception e) {
            logger.info("incStrScore error strKey={} score={} e={}", strKey, score, e.getMessage(), e);
        }
        return 0;
    }

    public void setStrData(String strKey, String data) {
        try {
            clusterTemplate.opsForValue().set(strKey, data, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setStrData error strKey={} data={} e={}", strKey, data, e.getMessage(), e);
        }
    }

    /**
     * zset相关
     */
    public int getZSetRankScore(String zSetKey, String rankId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(zSetKey, rankId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getZSetRankScore error zSetKey={} rankId={}, e={}", zSetKey, rankId, e.getMessage(), e);
            return 0;
        }
    }

    public double getZSetRankDoubleScore(String zSetKey, String rankId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(zSetKey, rankId);
            return score != null ? score : 0;
        } catch (Exception e) {
            logger.info("getZSetRankDoubleScore error zSetKey={} rankId={}, e={}", zSetKey, rankId, e.getMessage(), e);
            return 0;
        }
    }

    public int incrZSetRankScoreWithTime(String zSetKey, String rankId, int score) {
        try {
            int curScore = getZSetRankScore(zSetKey, rankId);
            int nowScore = curScore + score;
            double rankScore = new BigDecimal(nowScore + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(zSetKey, rankId, rankScore);
            clusterTemplate.expire(zSetKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return nowScore;
        } catch (Exception e) {
            logger.info("incrZSetRankScoreWithTime error zSetKey={} rankId={} giftId={} score={}", zSetKey, rankId, score, e.getMessage(), e);
            return 0;
        }
    }

    public void addZSetRankScoreWithTime(String zSetKey, String rankId, int score) {
        try {
            double rankScore = new BigDecimal(score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(zSetKey, rankId, rankScore);
            clusterTemplate.expire(zSetKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addZSetRankScoreWithTime error zSetKey={} rankId={} giftId={} score={}", zSetKey, rankId, score, e.getMessage(), e);
        }
    }

    public int incrZSetRankScore(String zSetKey, String rankId, int score) {
        Double ret = null;
        try {
            ret = clusterTemplate.opsForZSet().incrementScore(zSetKey, rankId, score);
            clusterTemplate.expire(zSetKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrZSetRankScore error zSetKey={} rankId={} score={} e={}", zSetKey, rankId, score, e.getMessage(), e);
        }
        return ret == null ? 0 : ret.intValue();
    }

    public void addZSetRankScore(String zSetKey, String rankId, int score) {
        try {
            clusterTemplate.opsForZSet().add(zSetKey, rankId, score);
            clusterTemplate.expire(zSetKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addZSetRankScore error zSetKey={} rankId={} score={}, e={}", zSetKey, rankId, score, e.getMessage(), e);
        }
    }


    public double incrZSetRankScoreDouble(String zSetKey, String rankId, double score) {
        Double ret = null;
        try {
            ret = clusterTemplate.opsForZSet().incrementScore(zSetKey, rankId, score);
            clusterTemplate.expire(zSetKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrZSetRankScoreDouble error zSetKey={} rankId={} giftId={} score={}", zSetKey, rankId, score, e.getMessage(), e);
        }
        return ret == null ? 0 : ret;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getZSetRank(String zSetKey, String rankId) {
        try {
            Long rank = clusterTemplate.opsForZSet().reverseRank(zSetKey, rankId);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getZSetRank error zSetKey={} rankId={}", zSetKey, rankId, e);
            return 0;
        }
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     * 正序
     */
    public int getZSetPositiveRank(String zSetKey, String rankId) {
        try {
            Long rank = clusterTemplate.opsForZSet().rank(zSetKey, rankId);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getZSetPositiveRank error zSetKey={} rankId={}", zSetKey, rankId, e);
            return 0;
        }
    }

    /**
     * 获取排行榜
     * -获取top-n
     * -倒序不带分数
     */
    public List<String> getRankList(String zSetKey, int length) {
        List<String> rankingList = new ArrayList<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(zSetKey, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     * -获取start-end 排名
     * -倒序不带分数
     */
    public List<String> getRankList(String zSetKey, int start, int end) {
        List<String> rankingList = new ArrayList<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(zSetKey, start, end - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     * -获取top-n
     * -倒序带分数
     */
    public Map<String, Integer> getRankMap(String zSetKey, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(zSetKey, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 获取排行榜
     * -获取top-n
     * -正序不带分数
     */
    public List<String> getRangeRankList(String zSetKey, int length) {
        List<String> rankingList = new ArrayList<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(zSetKey, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     * -获取top-n
     * -正序带分数
     */
    public Map<String, Integer> getRangeRankMap(String zSetKey, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(zSetKey, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 分页获取带分数排行榜
     * -获取start-end排名
     * -正序不带分数
     */
    public Map<String, Integer> getRangeRankMapByPage(String zSetKey, int start, int end) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(zSetKey, start, end - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 通过分数获取成员
     * 正序
     */
    public Map<String, Integer> getRangeRankMapByScore(String zSetKey, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeByScoreWithScores(zSetKey, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 通过分数获取成员
     * 倒序
     */
    public Map<String, Integer> getRankMapByScore(String zSetKey, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(zSetKey, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 分页获取指定分数段带分数排行榜
     * 倒序
     * offset: 偏移多少
     * count: 取多少
     */
    public Map<String, Integer> getRankMapByScoreAPage(String zSetKey, int min, int max, int offset, int count) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(zSetKey, min, max, offset, count);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 移除排名
     */
    public void removeZSetData(String zSetKey, String rankId) {
        try {
            clusterTemplate.opsForZSet().remove(zSetKey, rankId);
        } catch (Exception e) {
            logger.info("removeZSetData error zSetKey={} rankId={}", zSetKey, rankId, e);
        }
    }

    /**
     * 获取成员数量
     */
    public int getZSetMemberNum(String zSetKey) {
        try {
            Long memberNum = clusterTemplate.opsForZSet().zCard(zSetKey);
            return memberNum == null ? 0 : memberNum.intValue();
        } catch (Exception e) {
            logger.info("getZSetMemberNum error zSetKey={} error={}", zSetKey, e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 集合set相关
     */
    public void addSetData(String setKey, String data) {
        try {
            clusterTemplate.opsForSet().add(setKey, data);
            clusterTemplate.expire(setKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addSetData error setKey={} data={}", setKey, data, e);
        }
    }

    public void removeSetData(String setKey, String data) {
        try {
            clusterTemplate.opsForSet().remove(setKey, data);
        } catch (Exception e) {
            logger.error("removeSetData error setKey={} e={}", setKey, data, e);
        }
    }

    public boolean isSetData(String setKey, String data) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(setKey, data));
        } catch (Exception e) {
            logger.error("isDeviceData error setKey={} data={}, e={}", setKey, data, e);
            return false;
        }
    }

    public int getSetNum(String setKey) {
        try {
            Long number = clusterTemplate.opsForSet().size(setKey);
            return ObjectUtils.isEmpty(number) ? 0 : number.intValue();
        } catch (Exception e) {
            logger.info("getSetNum error setKey={}  score={}", setKey, e);
            return 0;
        }
    }

    public Set<String> getSetMember(String setKey) {
        try {
            return clusterTemplate.opsForSet().members(setKey);
        } catch (Exception e) {
            logger.info("getSetMember error activityId={}  score={}", setKey, e);
            return Collections.emptySet();
        }
    }

    public String getSetOneMember(String setKey) {
        try {
            return clusterTemplate.opsForSet().randomMember(setKey);
        } catch (Exception e) {
            logger.info("getSetOneMember error setKey={}  score={}", setKey, e);
            return "";
        }
    }


    /**
     * List相关
     */
    public void addListData(String listKey, String data) {
        Long listSize = clusterTemplate.opsForList().leftPush(listKey, data);
        clusterTemplate.expire(listKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        if (null != listSize && listSize > MAX_SIZE) {
            clusterTemplate.opsForList().trim(listKey, 0, MAX_SIZE);
        }
    }

    public List<String> getListRecord(String listKey) {
        return getListData(listKey, MAX_SIZE);
    }

    public List<String> getListData(String listKey, int size) {
        try {
            List<String> jsonList = clusterTemplate.opsForList().range(listKey, 0, size - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            List<String> resultList = new ArrayList<>(MAX_SIZE);
            resultList.addAll(jsonList);
            return resultList;
        } catch (Exception e) {
            logger.error("getListData error", e);
            return Collections.emptyList();
        }
    }

}
