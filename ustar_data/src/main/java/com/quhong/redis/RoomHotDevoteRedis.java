package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class RoomHotDevoteRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomHotDevoteRedis.class);

    public static final int HOT_CHANGE_SIZE = 20;
    public static final int HOT_CHANGE_MAX_RANK = 99;
    public static final int ROOM_USER_RANK_DAY = 1;
    public static final int ROOM_USER_RANK_WEEK = 2;
    public static final int ROOM_USER_RANK_NUM = 20;

    private String rankingExpireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;


    public List<String> getHotRankList() {
        List<String> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getHotRankListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, String.class);
        } catch (Exception e) {
            logger.error("getHotRankList error ", e);
            return result;
        }
    }

    public void addExpireHotRankList() {
        try {
            clusterRedis.expire(getHotRankListKey(), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("addExpireHotRankList error msg={}", e.getMessage(), e);
        }
    }

    public void saveHotRankList(List<String> pageList) {
        try {
            clusterRedis.opsForValue().set(getHotRankListKey(), JSON.toJSONString(pageList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveHotRankList error pageListSize={}", pageList.size(), e);
        }
    }

    private String getHotRankListKey() {
        return "str:HotRankListKey";
    }


    public void setUserInRoomScore(String roomId, String aid, double score, int rankType) {
        try {
            String key = getRoomDevoteUserRankKey(roomId, rankType);
//            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterRedis.opsForZSet().add(key, aid, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(rankingExpireDate)) {
                clusterRedis.expire(key, 10, TimeUnit.MINUTES);
                rankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("setUserInRoomScore error roomId={} aid={} giftId={} score={}", roomId, aid, score, e);
        }
    }


    public void addAllUserInRoomScore(String roomId, Map<String, Double> sources, int rankType) {
        try {
            String key = getRoomDevoteUserRankKey(roomId, rankType);
            Set<ZSetOperations.TypedTuple<String>> tuples = new HashSet<>();
            for (Map.Entry<String, Double> entry : sources.entrySet()) {
                ZSetOperations.TypedTuple<String> one = new DefaultTypedTuple<>(entry.getKey(), entry.getValue());
                tuples.add(one);
            }
            clusterRedis.delete(key);
            clusterRedis.opsForZSet().add(key, tuples);
            clusterRedis.expire(key, 10, TimeUnit.MINUTES);
//            logger.info("addAllUserInRoomScore success roomId={}  rankType={} sources={}", roomId, rankType, sources);
        } catch (Exception e) {
            logger.info("addAllUserInRoomScore error roomId={}  rankType={} sources={}", roomId, rankType, sources, e);
        }
    }

    /**
     * 获取带分数排行榜,key不存在会返回empty不是null
     *
     * @param roomId
     * @param rankType 排行榜类型
     * @param length   排行榜长度
     */
    public Map<String, Long> getUserInRoomMap(String roomId, int rankType, int length) {
        String key = getRoomDevoteUserRankKey(roomId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterRedis.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return null;
        }
        Map<String, Long> linkedRankMap = new LinkedHashMap<>();
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
        }
        return linkedRankMap;
    }


    public long getUserInRoomScore(String roomId, String uid, int rankType) {
        try {
            String key = getRoomDevoteUserRankKey(roomId, rankType);
            Double score = clusterRedis.opsForZSet().score(key, uid);
            if (score != null) {
                return score.longValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getUserInRoomScore error roomId={} uid={} rankType={}", roomId, uid, rankType, e);
            return 0;
        }
    }

    public void addExpireAllUserInRoom(String roomId, int rankType) {
        try {
            String key = getRoomDevoteUserRankKey(roomId, rankType);
            clusterRedis.expire(key, 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.info("addExpireAllUserInRoom error roomId={}  rankType={}", roomId, rankType, e);
        }
    }


    /**
     * 房间用户贡献排行榜key
     *
     * @param roomId   房间id
     * @param rankType 排行榜类型 1: 最近24小时榜单 2: 最近7天榜单
     * @return key
     */
    private String getRoomDevoteUserRankKey(String roomId, int rankType) {
        return "zset:roomDevoteUserRank:" + roomId + ":" + rankType;
    }


    public List<String> getRoomUserRankList(String roomId) {
//        List<String> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getRoomUserRankListKey(roomId));
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseArray(json, String.class);
        } catch (Exception e) {
            logger.error("getRoomUserRankList error roomId={}", roomId, e);
            return null;
        }
    }

    public void saveRoomUserRankList(String roomId, List<String> pageList) {
        try {
            clusterRedis.opsForValue().set(getRoomUserRankListKey(roomId), JSON.toJSONString(pageList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveRoomUserRankList error roomId={}", roomId, e);
        }
    }

    public void addExpireRoomUserRankList(String roomId) {
        try {
            clusterRedis.expire(getRoomUserRankListKey(roomId), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("addExpireRoomUserRankList error roomId={}", roomId, e);
        }
    }

    /**
     * 房间内top3用户头像列表
     *
     * @param roomId
     * @return
     */
    private String getRoomUserRankListKey(String roomId) {
        return "str:RoomUserRankKey:" + roomId;
    }


    public List<String> getInactiveRoomList(int rankType) {
        List<String> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getInactiveRoomListKey(rankType));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, String.class);
        } catch (Exception e) {
            logger.error("getInactiveRoomList error ", e);
            return result;
        }
    }

    public void saveInactiveRoomList(List<String> pageList, int rankType) {
        try {
            clusterRedis.delete(getInactiveRoomListKey(rankType));
            clusterRedis.opsForValue().set(getInactiveRoomListKey(rankType), JSON.toJSONString(pageList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveInactiveRoomList error rankType {}", rankType, e);
        }
    }

    /**
     * 前10分钟不活跃房间列表key
     *
     * @return
     */
    private String getInactiveRoomListKey(int rankType) {
        return "str:InactiveRoomListKey:rankType:" + rankType;
    }

}
