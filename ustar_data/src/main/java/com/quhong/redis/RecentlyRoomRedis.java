package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class RecentlyRoomRedis {
    private static final Logger logger = LoggerFactory.getLogger(RecentlyRoomRedis.class);

//    @Resource(name = DataRedisBean.RECENTLY_ROOM)
//    private StringRedisTemplate recentlyRoomRedis;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    /**
     * 增加用户最近进房间记录
     */
    public void addRecentlyActor(String roomId, String uid) {
        try {
            String key = getRecentlyKey(uid);
            clusterRedis.opsForZSet().add(key, roomId, DateHelper.getNowSeconds());
            Long size = clusterRedis.opsForZSet().size(key);
            if (null != size && size >= 100) {
                // 移除10个旧数据
                clusterRedis.opsForZSet().removeRange(key, 0, 9);
            }
            clusterRedis.expire(key, 20, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addRecentlyActor error, roomId={} uid={} msg={}", roomId, uid, e.getMessage());
        }
    }

    /**
     * 获取所有最近进房间10条记录
     */
    public Set<String> getRecentlyRooms(String uid) {
        try {
            return clusterRedis.opsForZSet().reverseRange(getRecentlyKey(uid), 0, 9);
        } catch (Exception e) {
            logger.error("getNewUserOnline error. {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取所有最近进房间n条记录
     */
    public Set<String> getRecentlyNumRooms(String uid,int start,int end) {
        try {
            return clusterRedis.opsForZSet().reverseRange(getRecentlyKey(uid), start, end);
        } catch (Exception e) {
            logger.error("getNewUserOnline error. {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    private String getRecentlyKey(String uid) {
        return "recently_" + uid;
    }

    public void addEnterRoomClientTime(String uid, long requestTime) {
        try {
            clusterRedis.opsForValue().set(getEnterClientTimeKey(uid), requestTime + "", 1, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add enter room client time error. uid={} {}", uid, e.getMessage());
        }
    }

    /**
     * 判断删除流记录是否最近N秒写入
     */
    public boolean isRecentlyEnterRoom(String uid, int second) {
        try {
            Long expire = clusterRedis.getExpire(getEnterClientTimeKey(uid), TimeUnit.SECONDS);
            if (null == expire) {
                return false;
            }
            return expire >= TimeUnit.DAYS.toSeconds(1) - second;
        } catch (Exception e) {
            logger.error("get del stream record ttl error. uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    public long getEnterRoomClientTime(String uid) {
        try {
            String enterTime = clusterRedis.opsForValue().get(getEnterClientTimeKey(uid));
            if (null == enterTime) {
                return 0;
            }
            return Long.parseLong(enterTime);
        } catch (Exception e) {
            logger.error("get enter room client time error. uid={} {}", uid, e.getMessage());
            return 0;
        }
    }

    private String getEnterClientTimeKey(String uid) {
        return "room:enter:time:" + uid;
    }

    public void addRookieRoomFlow(String roomId, String uid) {
        try {
            String key = getRookieRoomFlowKey(roomId);
            int nowSeconds = DateHelper.getNowSeconds();
            clusterRedis.opsForZSet().add(key, uid, nowSeconds);
            clusterRedis.expire(key, 3, TimeUnit.DAYS);
            // 删除12小时以上的数据
            clusterRedis.opsForZSet().removeRangeByScore(key, 0, nowSeconds - 12 * 60 * 60);
        } catch (Exception e) {
            logger.error("add rookie room flow error. roomId={} uid={} {}", roomId, uid, e.getMessage());
        }
    }

    private String getRookieRoomFlowKey(String roomId) {
        return "zset:rookie:flow:" + roomId;
    }
}
