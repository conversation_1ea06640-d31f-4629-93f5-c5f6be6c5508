package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;



@Component
@Lazy
public class NewUserRoomRecommendRedis {
    /**
     * 用户停留推荐进房
     */
    private static final Logger logger = LoggerFactory.getLogger(NewUserRoomRecommendRedis.class);
    private static final Integer MAX_SIZE = 50;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    private String getRecommendGameKey(String uid, int gameType) {
        return String.format("str:RecommendGame:%s:%s", uid, gameType);
    }

    public void incRecommendGameNum(String uid, int gameType) {
        try {
            String key = getRecommendGameKey(uid, gameType);
            clusterRedis.opsForValue().increment(key, 1);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incRecommendGameNum error: {}", e.getMessage(), e);
        }
    }

    public int getRecommendGameNum(String uid, int gameType) {
        try {
            String playNum = clusterRedis.opsForValue().get(getRecommendGameKey(uid, gameType));
            return playNum == null ? 0 : Integer.parseInt(playNum);
        } catch (Exception e) {
            logger.error("getRecommendGameNum error: {}", e.getMessage(), e);
        }
        return 0;
    }


    /**
     * 用户在该房间发过礼物或者上麦的房间
     */
    private String getUserOnRoomGiftMicKey(String uid) {
        return "zset:UserOnRoomGiftMic:" + uid;
    }


    public void addUserOnRoomGiftMicRecord(String uid, String roomId) {

        try {
            String key = getUserOnRoomGiftMicKey(uid);
            clusterRedis.opsForZSet().add(key, roomId, DateHelper.getNowSeconds());
            Long size = clusterRedis.opsForZSet().size(key);
            if (null != size && size >= MAX_SIZE) {
                // 移除10个旧数据
                clusterRedis.opsForZSet().removeRange(key, 0, 9);
            }
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addUserOnRoomGiftMicRecord error, roomId={} uid={} msg={}", roomId, uid, e.getMessage());
        }
    }

    /**
     * 获取所有最近进房间n条记录
     */
    public Set<String> getUserOnRoomGiftMicRooms(String uid, int start, int end) {
        try {
            return clusterRedis.opsForZSet().reverseRange(getUserOnRoomGiftMicKey(uid), start, end);
        } catch (Exception e) {
            logger.error("getNewUserOnline error. {}", e.getMessage(), e);
            return Collections.emptySet();
        }
    }


    /**
     * 当天推荐过的房间去重
     */

    private String getAlreadyRecommendSetKey(String uid) {
        return String.format("set:NewUserAlreadyRecommend:%s:%s", DateHelper.ARABIAN.formatDateInDay(), uid);
    }

    public void addAlreadyRecommendSet(String uid, String roomId) {
        try {
            String key = getAlreadyRecommendSetKey(uid);
            clusterRedis.opsForSet().add(key, roomId);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addAlreadyRecommendSet error uid={} e={}", uid, e);
        }
    }

    public boolean isAlreadyRecommendSet(String uid, String roomId) {
        try {
            return Boolean.TRUE.equals(clusterRedis.opsForSet().isMember(getAlreadyRecommendSetKey(uid), roomId));
        } catch (Exception e) {
            logger.info("isAlreadyRecommendSet error uid={} e={}", uid, e.getMessage());
        }
        return false;
    }


}
