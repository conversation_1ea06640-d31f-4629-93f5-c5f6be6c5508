package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import com.quhong.enums.BaseDataResourcesConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class GoodsListHomeRedis {
    private static final Logger logger = LoggerFactory.getLogger(GoodsListHomeRedis.class);
    public static final int NEW_TYPE = 1;
    public static final int HOT_TYPE = 2;


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    private String rankingExpireDate = "";
    private String reachingExpireDate = "";


    /**
     * 商品上新榜
     */
    public void addNewGoodsRankingScore(int resType, int resId) {
        try {
            if (!BaseDataResourcesConstant.VALID_TYPE_LIST.contains(resType)) {
                logger.info("not support resType={} resId={}", resType, resId);
                return;
            }
            String key = getGoodsListHomeKey(NEW_TYPE);
            String itemKey = getGoodsItemKey(resType, resId);
            if (StringUtils.isEmpty(itemKey)) {
                logger.info("incrNewGoodRankingScore is empty warn resType={} resId={}", resType, resId);
                return;
            }
            clusterTemplate.opsForZSet().add(key, itemKey, DateHelper.getNowSeconds());
        } catch (Exception e) {
            logger.info("incrNewGoodRankingScore error resType={} resId={}", resType, resId, e);
        }
    }

    /**
     * 商品上新榜删除
     */
    public void deleteItemNewGoodsRanking(int resType, int resId) {
        try {
            String key = getGoodsListHomeKey(NEW_TYPE);
            String itemKey = getGoodsItemKey(resType, resId);
            clusterTemplate.opsForZSet().remove(key, itemKey);
        } catch (Exception e) {
            logger.info("incrNewGoodRankingScore error resType={} resId={}", resType, resId, e);
        }
    }

    /**
     * 商品销售榜
     */
    public int incrHotGoodsRankingScore(int resType, int resId) {
        try {
            if (!BaseDataResourcesConstant.VALID_TYPE_LIST.contains(resType)) {
                logger.info("not support resType={} resId={}", resType, resId);
                return 0;
            }
            String key = getGoodsListHomeKey(HOT_TYPE);
            String itemKey = getGoodsItemKey(resType, resId);
            if (StringUtils.isEmpty(itemKey)) {
                logger.info("incrNewGoodRankingScore is empty warn resType={} resId={}", resType, resId);
                return 0;
            }
            Double value = clusterTemplate.opsForZSet().incrementScore(key, itemKey, 1);
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrHotGoodsRankingScore error resType={} resId={}", resType, resId, e);
            return 0;
        }
    }


    /**
     * 商品销售榜删除
     */
    public void deleteItemHotGoodsRanking(int resType, int resId) {
        try {
            String key = getGoodsListHomeKey(HOT_TYPE);
            String itemKey = getGoodsItemKey(resType, resId);
            clusterTemplate.opsForZSet().remove(key, itemKey);
        } catch (Exception e) {
            logger.info("incrNewGoodRankingScore error resType={} resId={}", resType, resId, e);
        }
    }

    /**
     * 获取排行榜
     *
     * @param rankType 排行榜类型
     */
    public List<String> getRankingList(int rankType) {
        List<String> rankingList = new ArrayList<>();
        String key = getGoodsListHomeKey(rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, 8);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
//            logger.info("getRankingByType aid={} score={} rankType={}", rangeWithScore.getValue(), rangeWithScore.getScore().longValue(), rankType);
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }


    /**
     * 资源获得红点提示，活动获得、购买获得、好友赠送获得
     */
    public void saveGetResUidToRedis(String uid, int resType) {
        try {
            clusterTemplate.opsForValue().set(getResUidKey(uid), String.valueOf(resType), 14, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save saveGetResUidToRedis error. uid={} resType={} msg={}", uid, resType, e.getMessage());
        }
    }


    /**
     * 获取资源获得红点提示
     */
    public int getResTypeFromRedis(String uid) {
        try {
            String resType = clusterTemplate.opsForValue().get(getResUidKey(uid));
            return resType != null ? Integer.parseInt(resType) : 0;
        } catch (Exception e) {
            logger.error("get getResUidToRedis error. uid={}  msg={}", uid, e.getMessage());
            return 0;
        }
    }

    /**
     * 删除资源获得红点提示
     */
    public void removeGetResUidToRedis(String uid, int resType) {
        try {
            clusterTemplate.delete(getResUidKey(uid));
        } catch (Exception e) {
            logger.error("delete removeGetResUidToRedis error. uid={} resType={} msg={}", uid, resType, e.getMessage());
        }
    }


    private String getResUidKey(String uid) {
        return "str:res:get:uid:" + uid;
    }

    private String getGoodsListHomeKey(int rankType) {
        return "zset:store:goodsListHome:" + rankType;
    }

    private String getGoodsItemKey(int resType, int resId) {
        if (resType <= 0 || resId <= 0) {
            return "";
        }
        return resType + "_" + resId;
    }
}
