package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class WeeklyStarRedis {
    private static final Logger logger = LoggerFactory.getLogger(WeeklyStarRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    public void incrWeeklyStarScore(String uid, int giftId, int weekCount, int score) {
        try {
            String key = getWeeklyStarKey(weekCount, giftId);
            int curScore = getScore(uid, key);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrWeeklyStarScore uid={} giftId={} score={} total={} weekCount={}", uid, giftId, score, rankScore, weekCount);
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            clusterTemplate.expire(key, 14, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrWeeklyStarScore error uid={} giftId={} score={}", uid, giftId, score, e);
        }
    }

    /**
     * 获取带分数排行榜
     *
     * @param giftId    礼物id
     * @param weekCount 周活动id
     * @param length    排行榜长度
     */
    public Map<String, Integer> getRankingMap(int giftId, int weekCount, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getWeeklyStarKey(weekCount, giftId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
//            logger.info("getRankingMap key={} value={}", rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRank(String aid, int giftId, int weekCount) {
        try {
            Long rank = clusterTemplate.opsForZSet().reverseRank(getWeeklyStarKey(weekCount, giftId), aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getRank error aid={} giftId={} weekCount={}", aid, giftId, weekCount, e);
            return 0;
        }
    }

    /**
     * 获取分数
     */
    public int getScore(String aid, int giftId, int weekCount) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getWeeklyStarKey(weekCount, giftId), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getScore error aid={} giftId={} weekCount={}", aid, giftId, weekCount, e);
            return 0;
        }
    }

//    private String getWeeklyStarKey(int weekCount, int giftId) {
//        return "zset:weeklyStar:" + weekCount + ":" + giftId;
//    }

    private String getWeeklyStarKey(int weekCount, int giftId) {
        return "zset:weeklyStar:new:" + weekCount + ":" + giftId;
    }

    /**
     * 获取分数
     */
    public int getScore(String uid, String key) {
        try {
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getScore error uid={} key={}", uid, key, e);
            return 0;
        }
    }

    public int incrTop1Count(String uid) {
        try {
            return clusterTemplate.opsForHash().increment(getTop1CountNewKey(), uid, 1).intValue();
        } catch (Exception e) {
            logger.info("incrTop1Count error uid={}", uid, e);
            return 0;
        }
    }

    //    private String getTop1CountKey() {
//        return "hash:weeklyStarTop1Count";
//    }
    private String getTop1CountNewKey() {
        return "hash:weeklyStarTop1Count:new";
    }

    public int getTop1Count(String uid) {
        try {
            String value = (String) clusterTemplate.opsForHash().get(getTop1CountNewKey(), uid);
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            return Integer.parseInt(value);
        } catch (Exception e) {
            logger.info("getTop1Count error uid={}", uid, e);
            return 0;
        }
    }


    /**
     * str类型相关
     */
    private String getTop1ShowKey(String uid) {
        return String.format("str:weeklyStarTop1Count:show:%s", uid);
    }

    public int getTop1ShowScore(String uid) {
        try {
            String score = clusterTemplate.opsForValue().get(getTop1ShowKey(uid));
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getCommonStrScore error uid={} e={}", uid, e.getMessage(), e);
        }
        return 0;
    }

    public void setTop1ShowScore(String uid, int score) {
        try {
            String key = getTop1ShowKey(uid);
            clusterTemplate.opsForValue().set(key, String.valueOf(score), ExpireTimeConstant.EXPIRE_DAYS_FIFTEEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setCommonStrScore error uid={} score={} e={}", uid, score, e.getMessage(), e);
        }
    }

    public void delTop1Show(String uid) {
        try {
            String key = getTop1ShowKey(uid);
            clusterTemplate.delete(key);
        } catch (Exception e) {
            logger.info("getCommonStrScore error uid={} e={}", uid, e.getMessage(), e);
        }
    }

}
