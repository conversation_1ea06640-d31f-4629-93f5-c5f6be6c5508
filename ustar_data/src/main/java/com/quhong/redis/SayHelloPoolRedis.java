package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class SayHelloPoolRedis {
    private static final Logger logger = LoggerFactory.getLogger(SayHelloPoolRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;


    public void addSayHelloPool(Set<ZSetOperations.TypedTuple<String>> tuples) {
        try {
            String key = getSayHelloPoolKey();
            clusterRedis.opsForZSet().add(key, tuples);
            clusterRedis.expire(key, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add say hello pool error.", e);
        }
    }

    /**
     * 并发情况下会返回空集合
     */
    public Set<String> getAllSayHelloPool() {
        try {
            return clusterRedis.opsForZSet().reverseRangeByScore(getSayHelloPoolKey(), 0, 10000);
        } catch (Exception e) {
            logger.error("getAllSayHelloPool error.", e);
            return Collections.emptySet();
        }
    }

    public void removeSayHelloPool() {
        try {
            clusterRedis.delete(getSayHelloPoolKey());
        } catch (Exception e) {
            logger.error("add say hello pool error.", e);
        }
    }

    private String getSayHelloPoolKey() {
        return "zset:sayHelloPool";
    }
}
