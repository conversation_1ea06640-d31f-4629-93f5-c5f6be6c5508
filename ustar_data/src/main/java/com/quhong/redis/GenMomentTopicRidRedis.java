package com.quhong.redis;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.utils.FamilyBeautifulRidUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/6/2
 */
@Lazy
@Component
public class GenMomentTopicRidRedis {

    private static final Logger logger = LoggerFactory.getLogger(GenMomentTopicRidRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public void setGenMomentTopicRid(int rid) {
        try {
            redisTemplate.opsForValue().set(getGenMomentTopicRidKey(), rid + "");
        } catch (Exception e) {
            logger.error("setGenMomentTopicRid error. rid={} {}", rid, e.getMessage(), e);
        }
    }

    public int incGenMomentTopicRid() {
        try {
            Long rid = redisTemplate.opsForValue().increment(getGenMomentTopicRidKey());
            return rid != null ? rid.intValue() : 0;
        } catch (Exception e) {
            logger.error("incGenMomentTopicRid error. {}", e.getMessage(), e);
            return 0;
        }
    }

    private String getGenMomentTopicRidKey () {
        return "str:register_gen_moment_topic_rid";
    }

    public int getGenMomentTopicRid() {
        try (DistributeLock lock = new DistributeLock("genMomentTopicRid")) {
            lock.lock();
            int rid = incGenMomentTopicRid();
            while (FamilyBeautifulRidUtils.isBeautifulRid(rid + "") || rid < FamilyBeautifulRidUtils.MIN_RID) {
                rid++;
            }
            setGenMomentTopicRid(rid);
            return rid;
        } catch (Exception e) {
            logger.error("getGenMomentTopicRid error. {}", e.getMessage(), e);
        }
        return 0;
    }
}
