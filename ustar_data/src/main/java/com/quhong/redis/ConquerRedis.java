package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class ConquerRedis {
    private static final Logger logger = LoggerFactory.getLogger(ConquerRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    // 每个人征服房间排行榜
    public String getConquerRankKey(String conquerId) {
        // conquerId 征服活动id
        return "zset:conquer_rank_" + conquerId;
    }

    /**
     * 获取分数
     */
    public int getScore(String activityId, String aid) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getConquerRankKey(activityId), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getScore error activityId={} aid={} rankType={}", activityId, aid, e);
            return 0;
        }
    }


    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRank(String activityId, String aid) {
        try {
            Long rank = clusterTemplate.opsForZSet().reverseRank(getConquerRankKey(activityId), aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getRank error activityId={} aid={} rankType={}", activityId, aid, e);
            return 0;
        }
    }

    public int incConquerRank(String activityId, String uid, int delta) {
        try {
            Double value = clusterTemplate.opsForZSet().incrementScore(getConquerRankKey(activityId), uid, delta);
            clusterTemplate.expire(getConquerRankKey(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return null == value ? 0 : value.intValue();
        } catch (Exception e) {
            logger.info("incConquerRank error activityId={} uid={} error={}", activityId, uid, e);
            return 0;
        }
    }


    // 获取当前房间征服礼物【临时发送数量】
    // 所有房间的临时征服礼物的发送数量, 倒计时结束会删除
    private String getConquerRoomSumKey(String activityId) {
        return "zset:conquer_room_gift_sum:" + activityId;
    }

    public int getConquerTempRoomScore(String activityId, String roomId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getConquerRoomSumKey(activityId), roomId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getConquerRoomScore error activityId={} roomId={} error={}", activityId, roomId, e);
            return 0;
        }
    }

    public void addConquerTempNum(String activityId, String roomId, int leftNum) {
        try {
            clusterTemplate.opsForZSet().add(getConquerRoomSumKey(activityId), roomId, leftNum);
            clusterTemplate.expire(getConquerRoomSumKey(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addConquerTempNum error activityId={} roomId={} error={}", activityId, roomId, e);
        }
    }

    public void incrConquerTempNum(String activityId, String roomId, int leftNum) {
        try {
            clusterTemplate.opsForZSet().incrementScore(getConquerRoomSumKey(activityId), roomId, leftNum);
            clusterTemplate.expire(getConquerRoomSumKey(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addConquerTempNum error activityId={} roomId={} error={}", activityId, roomId, e);
        }
    }


    public void removeConquerTempNum(String activityId, String roomId) {
        try {
            clusterTemplate.opsForZSet().remove(getConquerRoomSumKey(activityId), roomId);
        } catch (Exception e) {
            logger.info("removeConquerTempNum error activityId={} roomId={} error={}", activityId, roomId, e);
        }
    }


    // 获取当前房间被征服等级
    private String getRoomConquerLevelKey(String activityId, String roomId) {
        return "str:conquer_current_room_level_" + activityId + "_" + roomId;
    }

    public void deleteRoomConquerLevel(String activityId, String roomId) {
        try {
            clusterTemplate.delete("current_room_level_" + roomId);
            clusterTemplate.delete(getRoomConquerLevelKey(activityId, roomId));
        } catch (Exception e) {
            logger.info("removeRoomConquerLevel error activityId={} aid={} error={}", activityId, roomId, e);
        }
    }


    public int getRoomConquerLevel(String activityId, String roomId) {
        try {
            String level = clusterTemplate.opsForValue().get(getRoomConquerLevelKey(activityId, roomId));
            return level != null ? Integer.parseInt(level) : 0;
        } catch (Exception e) {
            logger.info("getRoomConquerLevel error activityId={} aid={} error={}", activityId, roomId, e);
            return 0;
        }
    }

    public int getRoomConquerLevel(String roomId) {
        try {
            String level = clusterTemplate.opsForValue().get("current_room_level_" + roomId);
            if (StringUtils.isEmpty(level)) {
                return 0;
            }
            return Integer.parseInt(level);
        } catch (
                Exception e) {
            logger.info("get conquered error roomId={} {}", roomId, e.getMessage());
            return 0;
        }
    }

    public void setRoomConquerLevel(String activityId, String roomId, int level) {
        try {
            clusterTemplate.opsForValue().set("current_room_level_" + roomId, level + "", 1, TimeUnit.DAYS);
            clusterTemplate.opsForValue().set(getRoomConquerLevelKey(activityId, roomId), String.valueOf(level));
            clusterTemplate.expire(getRoomConquerLevelKey(activityId, roomId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setRoomConquerLevel error activityId={} aid={} error={}", activityId, roomId, e);
        }
    }

    // 用户在具体房间的贡献值
    // 倒计时结束会删除
    private String getRoomConquerRankKey(String activityId, String roomId) {
        return "zset:conquer_room_rank_" + activityId + "_" + roomId;
    }


    public void incrRoomConquerRank(String activityId, String roomId, String uid, int value) {
        try {
            clusterTemplate.opsForZSet().incrementScore(getRoomConquerRankKey(activityId, roomId), uid, value);
            clusterTemplate.expire(getRoomConquerRankKey(activityId, roomId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incRoomConquerRankKey error activityId={} aid={} error={}", activityId, roomId, e);
        }
    }


    public void addRoomConquerRank(String activityId, String roomId, String uid, int value) {
        try {
            clusterTemplate.opsForZSet().add(getRoomConquerRankKey(activityId, roomId), uid, value);
            clusterTemplate.expire(getRoomConquerRankKey(activityId, roomId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addRoomConquerRank error activityId={} aid={} error={}", activityId, roomId, e);
        }
    }

    public void deleteRoomConquerRankKey(String activityId, String roomId) {
        try {
            clusterTemplate.delete(getRoomConquerRankKey(activityId, roomId));
        } catch (Exception e) {
            logger.info("deleteRoomConquerRankKey error activityId={} aid={} error={}", activityId, roomId, e);
        }
    }


    public int getRoomConquerSumRank(String conquerId, String roomId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getRoomConquerSumRankKey(conquerId), roomId);
            return null == score ? 0 : score.intValue();
        } catch (Exception e) {
            logger.info("getRoomConquerSumRank error activityId={} roomId={} error={}", conquerId, roomId, e);
            return 0;
        }
    }

    public void delRoomConquerSumRank(String conquerId, String roomId) {
        try {
            clusterTemplate.opsForZSet().remove(getRoomConquerSumRankKey(conquerId), roomId);
        } catch (Exception e) {
            logger.info("delRoomConquerSumRank error activityId={} roomId={} error={}", conquerId, roomId, e);
        }
    }


    //
    private String getRoomConquerSumRankKey(String activityId) {
        return "zset:conquer_room_sum_" + activityId;
    }

    public void incrRoomConquerSumRank(String activityId, String roomId, int value) {
        try {
            clusterTemplate.opsForZSet().incrementScore(getRoomConquerSumRankKey(activityId), roomId, value);
            clusterTemplate.expire(getRoomConquerSumRankKey(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrRoomConquerSumRank error activityId={} aid={} error={}", activityId, roomId, e);
        }
    }


    /**
     * 获取排行榜
     */
    public List<String> getRankingList(String activityId, String roomId, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getRoomConquerRankKey(activityId, roomId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    public void setCountdownRoomList(String conquerId, String roomId, int conquerTime) {
        try {
            clusterTemplate.opsForZSet().add(getCountdownRoomListKey(conquerId), roomId, conquerTime);
            clusterTemplate.expire(getCountdownRoomListKey(conquerId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setCountdownRoomList error activityId={} roomId={} error={}", conquerId, roomId, e);
        }
    }

    public int getCountdownRoomList(String conquerId, String roomId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getCountdownRoomListKey(conquerId), roomId);
            return null == score ? 0 : score.intValue();
        } catch (Exception e) {
            logger.info("getCountdownRoomList error activityId={} roomId={} error={}", conquerId, roomId, e);
            return 0;
        }
    }

    public void delCountdownRoomList(String conquerId, String roomId) {
        try {
            clusterTemplate.opsForZSet().remove(getCountdownRoomListKey(conquerId), roomId);
        } catch (Exception e) {
            logger.info("delCountdownRoomList error activityId={} roomId={} error={}", conquerId, roomId, e);
        }
    }

    /**
     * 获取带分数排行榜
     */
    public Map<String, Integer> getRankingMap(String activityId) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet()
                .rangeByScoreWithScores(getCountdownRoomListKey(activityId), 0, DateHelper.getNowSeconds());
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    private String getCountdownRoomListKey(String conquerId) {
        return "zset:conquer_countdown_room_list_" + conquerId;
    }
}
