package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/7/5
 */
@Component
public class FriendshipRedis {

    private static final Logger logger = LoggerFactory.getLogger(FriendshipRedis.class);

    private static final int EXPIRE_HOURS = 12;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 保存好友关系
     *
     * @param uid
     * @param aid
     * @param status 0非好友 1是好友
     */
    public void saveDataToRedis(String uid, String aid, int status) {
        String key = getKey(uid, aid);
        try{
            redisTemplate.opsForValue().set(key, String.valueOf(status), EXPIRE_HOURS, TimeUnit.HOURS);
        }catch (Exception e){
            logger.error("save friendship data to redis error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
    }

    /**
     * 获取判断是否是好友的状态
     *
     * @param uid
     * @param aid
     * @return
     */
    public String getFriendStatus(String uid, String aid) {
        try{
            return redisTemplate.opsForValue().get(getKey(uid, aid));
        }catch (Exception e){
            logger.error("save friendship data to redis error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
        return "";
    }

    /**
     * 保存用户所以好友的uid
     *
     * @param uid
     * @param aidList
     */
    public void saveFriendsUidToRedis(String uid,List<String> aidList) {
        String[] aids = new String[aidList.size()];
        aids = aidList.toArray(aids);
        String key = getFriendsKey(uid);
        try{
            redisTemplate.opsForSet().add(key, aids);
            redisTemplate.expire(key, EXPIRE_HOURS, TimeUnit.HOURS);
        }catch (Exception e){
            logger.error("save friends data to redis error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void saveFriendUidToRedis(String uid, String aid) {
        String key = getFriendsKey(uid);
        try{
            redisTemplate.opsForSet().add(key, aid);
            redisTemplate.expire(key, EXPIRE_HOURS, TimeUnit.HOURS);
        }catch (Exception e){
            logger.error("save friend data to redis error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
    }

    /**
     * 获取用户所以好友的uid
     *
     * @param uid
     * @return
     */
    public Set<String> getFriendUidSet(String uid) {
        try{
            return redisTemplate.opsForSet().members(getFriendsKey(uid));
        }catch (Exception e){
            logger.error("get friend uid set error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 删除reids
     *
     * @param uid
     */
    public void removeFriendUidSet(String uid) {
        redisTemplate.delete(getFriendsKey(uid));
    }

    private String getKey(String uid, String aid) {
        return "string:friendship:" + generateFriendIndex(uid, aid);
    }

    private String getFriendsKey(String uid) {
        return "set:friends:" + uid;
    }

    public static String generateFriendIndex(String id1, String id2) {
        if (id1.compareTo(id2) > 0) {
            return id2 + "_" + id1;
        } else {
            return id1 + "_" + id2;
        }
    }
}
