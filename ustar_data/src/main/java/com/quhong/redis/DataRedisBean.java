package com.quhong.redis;

import com.quhong.core.config.ServerConfig;
import com.quhong.enums.ServerType;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration
public class DataRedisBean extends BaseRedisBean {
    public static final String USER_LEVEL = "redis_user_level_bean"; //py_name:user_level_rds
    public static final String VIP = "redis_vip_bean"; //py_name:vip_rds
    public static final String MIC_SOURCE = "mic_source_bean"; //py_name:mic_source_rds
    public static final String ROOM_TALK = "room_talk_bean"; //py_name room_talk_rds
    public static final String USER_MONITOR = "user_monitor"; //monitor_rds
    public static final String ROOM_ADMIN = "room_admin"; //room_admin_rds
    public static final String ROOM_MIC = "room_mic"; //room_mic_rds
    public static final String FINGER_GUESS = "finger_guess"; //finger_guess_rds
//    public static final String UID_ROOM = "uid_room"; //uid_room_rds
//    public static final String ROOM_UID = "room_uid"; //room_uid_rds
    public static final String ROOM_TIME_COUNT = "room_time_count"; //room_time_count_cache_rds
    public static final String ENTER_ROOM = "enter_room"; //enter_room_rds
//    public static final String MESSAGE_REDIS = "message_redis"; // redis2,7382,0
    public static final String BEAUTIFUL_ID_LIST = "beautiful_id_list"; // beautiful_id_list_rds
    public static final String NEW_THEME_FLAG = "new_theme_flag"; // newtheme_flag_rds

    public static final String ROOM_FORBID_MIC = "room_forbid_mic"; // room_forbid_mic_rds
    public static final String MAIN_CLUSTER = "mainClusterBean"; // room_forbid_mic_rds

    public static final String MIC_FRAME_EXPIRE = "mic_frame_expire"; // mic_frame_expire_rds
    public static final String JOIN_CARTON_EXPIRE = "join_carton_expire"; // user_gift_rds
    public static final String USER_TAKE_BUBBLE = "user_take_bubble"; // user_take_bubble
    public static final String USER_TAKE_MIC = "user_take_mic"; // get_user_mic_rds
    public static final String USER_SANDBOX = "user_sandbox"; // inner_sandbox_rds
    public static final String GAME_CACHE = "game_cache"; // game_cache_rds
    public static final String TRUTH_OR_DARE = "truth_or_dare"; // truth_or_dare_rds

    public static final String ACTIVITY = "activity"; // activity，活动相关的记录

//    public static final String CONFIG_REDIS = "config_bean"; // configCli

    public static final String RECENTLY_ROOM = "recently_room"; // recently_room_rds,6387,15
    public static final String ROOM_OTHER = "room_other"; // room_other_rds
    public static final String ROOM_KICK = "room_kick";  // room_kick_rds
    public static final String CREATE_ROOM = "create_room";  // create_room,6384,9
    public static final String FOLLOW = "follow";  // create_room,6382,1
    public static final String BLACK_LIST = "black_list";  // create_room,6381,2
    public static final String GIFT = "gift";  // create_room,6381,2
    public static final String ROOM_BEANS_DEVOTE = "room_beans_devote";
    public static final String ROOM_DEVOTE_CACHE = "room_devote_cache";
    public static final String FRIEND_LIST_REDIS = "friend_list_rds";

//    public static final String SUB_REDIS = "sub_redis"; // redis3,6380,0
//    public static final String QUEUE_REDIS = "queue_redis"; // redis3,6380,2
    public static final String GIFT_BADGE_REDIS = "gift_badge_redis";
    public static final String SNOWMAN_REDIS = "snowman_redis"; // redis1_6381,3
    public static final String PRIZE_POOL_REDIS = "prize_pool_rds"; // redis1_6379,1

    // 172.31.39.116  redis1
    // redis_6379_2 test 172.31.12.205:6387   production 172.31.39.116:6379
    private static final String Y_6379 = "y6379";
    // redis_6380_2 test 172.31.12.205:6387   production 172.31.39.116:6380
//    private static final String Y_6380 = "y6380";
    // redis_6381_2 test 172.31.12.205:6387   production 172.31.39.116:6381
//    private static final String Y_6381 = "y6381";
    // redis_6382_2 test 172.31.12.205:6387   production 172.31.39.116:6382
//    private static final String Y_6382 = "y6382";
    // redis_6382_2 test 172.31.12.205:6383   production 172.31.39.116:6383
//    private static final String Y_6383 = "y6383";
    // redis_6384 test 172.31.12.205:6384  redis_6384_2  production 172.31.39.116:6384
    private static final String Y_6384 = "y6384";
    // redis_6387_2 test 172.31.12.205:6387   production 172.31.39.116:6387
//    private static final String Y_6387 = "y6387";

    // 172.31.7.70  redis2
    // redis_7382_2 production 172.31.7.70:7382
    private static final String Y_7382 = "y7382";

    // 172.31.42.20  redis3
    // redis_6379 test 172.31.12.205:6379  production 172.31.42.20:6379
//    private static final String R_6379 = "r6379";
    // redis_6380 test 172.31.12.205:6380   production 172.31.42.20:6380
//    private static final String R_6380 = "r6380";
    // redis_6381 test 172.31.12.205:6381  production 172.31.42.20:6381
    private static final String R_6381 = "r6381";
    // redis_6382 test 172.31.12.205:6382   production 172.31.42.20:6382
    private static final String R_6382 = "r6382";
    // redis_6383 test 172.31.12.205:6383   production 172.31.42.20:6383
    private static final String R_6383 = "r6383";
    // redis_6385 test 172.31.12.205:6385   production 172.31.42.20:6385
    private static final String R_6385 = "r6385";
    private static final String R_6386 = "r6386";
    // redis_6387 test 172.31.12.205:6387   production 172.31.42.20:6387
    private static final String R_6387 = "r6387";
    // redis_6387 test 172.31.12.205:6388   production 172.31.42.20:6388
    private static final String R_6388 = "r6388";
    private static final String MAIN = "main";

//    @Bean(name = NEW_THEME_FLAG)
//    @Lazy
//    public StringRedisTemplate getNewThemeFlagBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 5
//            return createBean(Y_6379, NEW_THEME_FLAG);
//        } else {
//            // database 5
//            return createBean(R_6387, NEW_THEME_FLAG);
//        }
//    }

//    @Bean(name = GAME_CACHE)
//    @Lazy
//    public StringRedisTemplate getGameBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 14
//            return createBean(Y_6383, GAME_CACHE);
//        } else {
//            // database 14
//            return createBean(R_6383, GAME_CACHE);
//        }
//    }

//    @Bean(name = TRUTH_OR_DARE)
//    @Lazy
//    public StringRedisTemplate getTruthOrDareBean() {
//        // database 14
//        return createBean(R_6383, TRUTH_OR_DARE);
//    }

   /* @Bean(name = MESSAGE_REDIS)
    @Lazy
    public StringRedisTemplate getMessageBean() {
        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
            // database 0
            return createBean(Y_7382, MESSAGE_REDIS);
        } else {
            // database 0
            return createBean(R_6380, MESSAGE_REDIS);
        }
    }*/

//    @Bean(name = ROOM_ADMIN)
//    @Lazy
//    public StringRedisTemplate getRoomAdminBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 2
//            return createBean(Y_6380, ROOM_ADMIN);
//        } else {
//            // database 1
//            return createBean(R_6388, ROOM_ADMIN);
//        }
//    }

//    @Bean(name = FINGER_GUESS)
//    @Lazy
//    public StringRedisTemplate getFingerGuessBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 14
//            return createBean(Y_6383, FINGER_GUESS);
//        } else {
//            // database 5
//            return createBean(R_6387, FINGER_GUESS);
//        }
//    }

  /*  @Lazy
    @Bean(name = UID_ROOM)
    public StringRedisTemplate getUidRoomBean() {
        // database 14
        return createBean(R_6379, UID_ROOM);
    }*/

  /*  @Lazy
    @Bean(name = ROOM_UID)
    public StringRedisTemplate getRoomUidBean() {
        // database 15
        return createBean(R_6379, ROOM_UID);
    }*/

//    @Lazy
//    @Bean(name = ENTER_ROOM)
//    public StringRedisTemplate getEnterRoomBean() {
//        // database 12
//        return createBean(R_6385, ENTER_ROOM);
//    }

//    @Lazy
//    @Bean(name = BEAUTIFUL_ID_LIST)
//    public StringRedisTemplate getBeautifulIdListBean() {
//        // database 5
//        return createBean(R_6385, BEAUTIFUL_ID_LIST);
//    }

//    @Lazy
//    @Bean(name = USER_LEVEL)
//    public StringRedisTemplate getLevelBean() {
//        // database 1
//        return createBean(Y_6380, "level");
//    }

    @Lazy
    @Bean(name = VIP)
    public StringRedisTemplate getVipBean() {
        // database 0
        return createBean(R_6382, "vip");
    }

    @Lazy
    @Bean(name = MIC_SOURCE)
    public StringRedisTemplate getMicSourceBean() {
        // database 4
        return createBean(Y_6379, "mic_source");
    }

//    @Lazy
//    @Bean(name = ROOM_TALK)
//    public StringRedisTemplate getMicForbidBean() {
//        // database 3
//        return createBean(R_6383, "room_talk");
//    }

    @Lazy
    @Bean(name = USER_MONITOR)
    public StringRedisTemplate getUserMonitorBean() {
        // database 15
        return createBean(Y_6384, "user_monitor");
    }

//    @Bean(name = CREATE_ROOM)
//    @Lazy
//    public StringRedisTemplate getCreateRoomBean() {
//        // database 9
//        return createBean(Y_6384, "create_room");
//    }

//    @Bean(name = FOLLOW)
//    @Lazy
//    public StringRedisTemplate getUserFollowBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 1
//            return createBean(Y_6382, "follow");
//        } else {
//            return createBean(Y_6380, "follow");
//        }
//    }

//    @Bean(name = GIFT_BADGE_REDIS)
//    @Lazy
//    public StringRedisTemplate getGiftBagBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 4
//            return createBean(Y_6379, GIFT_BADGE_REDIS);
//        } else {
//            // database 8
//            return createBean(R_6387, GIFT_BADGE_REDIS);
//        }
//    }

//    @Bean(name = BLACK_LIST)
//    @Lazy
//    public StringRedisTemplate getBlackListBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 2
//            return createBean(Y_6381, "black_list");
//        } else {
//            // database 2
//            return createBean(R_6387, "black_list");
//        }
//    }

//    @Bean(name = GIFT)
//    @Lazy
//    public StringRedisTemplate getGiftBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 3
//            return createBean(Y_6379, "gift");
//        } else {
//            // database 7
//            return createBean(R_6387, "gift");
//        }
//    }

//    @Bean(name = ROOM_BEANS_DEVOTE)
//    @Lazy
//    public StringRedisTemplate getRoomBeansDevoteBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 2
//            return createBean(Y_6379, "room_beans_devote");
//        } else {
//            // database 2
//            return createBean(R_6387, "room_beans_devote");
//        }
//    }

//    @Bean(name = ROOM_DEVOTE_CACHE)
//    @Lazy
//    public StringRedisTemplate getRoomDevoteCacheBean() {
//        // db15
//        return createBean(R_6386, ROOM_DEVOTE_CACHE);
//    }

//    @Bean(name = SNOWMAN_REDIS)
//    @Lazy
//    public StringRedisTemplate getSnowmanBean() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // database 3
//            return createBean(Y_6381, SNOWMAN_REDIS);
//        } else {
//            // database 9
//            return createBean(R_6387, SNOWMAN_REDIS);
//        }
//    }

//    @Bean(name = FRIEND_LIST_REDIS)
//    @Lazy
//    public StringRedisTemplate getFriendListBean() {
//        // db12
//        return createBean(R_6386, FRIEND_LIST_REDIS);
//    }

    @Lazy
    @Bean(name = ROOM_MIC)
    public StringRedisTemplate getRoomMicLockBean() {
        // database 4
        return createBean(Y_6379, "room_mic");
    }

//    @Lazy
//    @Bean(name = ROOM_FORBID_MIC)
//    public StringRedisTemplate getRoomForbidMicBean() {
//        // database 4
//        return createBean(R_6383, "room_forbid_mic");
//    }

    @Bean(name = MAIN_CLUSTER)
    public StringRedisTemplate getMainClusterBean() {
        // database 4
        return createBean(MAIN, null);
    }

//    @Lazy
//    @Bean(name = MIC_FRAME_EXPIRE)
//    public StringRedisTemplate getMicFrameExpireBean() {
//        // database 10
//        return createBean(R_6383, "mic_frame_expire");
//    }

//    @Lazy
//    @Bean(name = JOIN_CARTON_EXPIRE)
//    public StringRedisTemplate getJoinCartonExpireBean() {
//        // database 10
//        return createBean(R_6385, "join_carton_expire");
//    }

//    @Lazy
//    @Bean(name = ROOM_TIME_COUNT)
//    public StringRedisTemplate getRoomTimeCountBean() {
//        // database 1
//        return createBean(R_6385, ROOM_TIME_COUNT);
//    }

//    @Lazy
//    @Bean(name = USER_TAKE_BUBBLE)
//    public StringRedisTemplate getUserTakeBubble() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // port database 6379 2
//            return createBean(Y_6384, "user_take_bubble");
//        } else {
//            return createBean(Y_6387, "user_take_bubble");
//            // test database 6387 4
//        }
//    }

//    @Lazy
//    @Bean(name = USER_TAKE_MIC)
//    public StringRedisTemplate getUserTakeMic() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // port database 6379 4
//            return createBean(Y_6379, "user_take_mic");
//        } else {
//            // test database 6387 4
//            return createBean(Y_6387, "user_take_mic");
//        }
//    }

//    @Bean(name = RECENTLY_ROOM)
//    @Lazy
//    public StringRedisTemplate getRecentlyRoom() {
//        // database 15
//        return createBean(Y_6387, "recently_room");
//    }

  /*  @Bean(name = SUB_REDIS)
    @Lazy
    public StringRedisTemplate getSubRedis() {
        // database 0
        return createBean(R_6380, SUB_REDIS);
    }*/

 /*   @Bean(name = QUEUE_REDIS)
    @Lazy
    public StringRedisTemplate getQueueRedis() {
        // database 2
        return createBean(R_6380, QUEUE_REDIS);
    }*/

//    @Lazy
//    @Bean(name = USER_SANDBOX)
//    public StringRedisTemplate getSandbox() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // port database 15
//            return createBean(Y_6381, "user_sandbox");
//        } else {
//            // test database 15
//            return createBean(R_6381, "user_sandbox");
//        }
//    }

    @Lazy
    @Bean(name = ACTIVITY)
    public StringRedisTemplate getActivityRedis() {
        // database 0
        return createBean(R_6381, ACTIVITY);
    }

   /* @Lazy
    @Bean(name = CONFIG_REDIS)
    public StringRedisTemplate getConfigRedis() {
        // port database 2
        return createBean(R_6379, "config");
    }*/

//    @Lazy
//    @Bean(name = ROOM_OTHER)
//    public StringRedisTemplate getRoomOther() {
//        // port database 3
//        return createBean(R_6382, "room_other");
//    }

//    @Lazy
//    @Bean(name = ROOM_KICK)
//    public StringRedisTemplate getRoomKick() {
//        // port database 4
//        return createBean(R_6385, "room_kick");
//    }

//    @Lazy
//    @Bean(name = PRIZE_POOL_REDIS)
//    public StringRedisTemplate getPrizePoolRds() {
//        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
//            // port database 6379 1
//            return createBean(Y_6379, PRIZE_POOL_REDIS);
//        } else {
//            // test database 6387 1
//            return createBean(Y_6387, PRIZE_POOL_REDIS);
//        }
//    }
}
