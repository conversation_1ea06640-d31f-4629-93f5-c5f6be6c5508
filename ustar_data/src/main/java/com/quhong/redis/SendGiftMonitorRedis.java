package com.quhong.redis;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@Component
@Lazy
public class SendGiftMonitorRedis {

    private static final Logger logger = LoggerFactory.getLogger(SayHelloPoolRedis.class);

    public static final List<Integer> MONITOR_GIFT_ID_LIST = ServerConfig.isProduct() ? Arrays.asList(90, 914, 915, 916, 849, 868, 962, 961, 810, 963, 965, 590) : Arrays.asList(110);
    public static final int FREEZE_VALUE = 200000; // 触发送礼冻结的送礼钻石数

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;



    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Set<String> getAllSendGiftMonitorUser() {
        try {
            return clusterRedis.opsForSet().members(getMonitorUserKey());
        } catch (Exception e) {
            logger.error("getAllSendGiftMonitorUser error. {}", e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public boolean isSendGiftFreezeUser(String uid) {
        try {
            String key = getFreezeUserKey(uid);
            String strValue = clusterRedis.opsForValue().get(key);
            return "1".equals(strValue);
        } catch (Exception e) {
            logger.error("isSendGiftFreezeUser error. uid={} {}", uid, e.getMessage(), e);
            return false;
        }
    }

    public void setSendGiftFreezeUser(String uid, int freezeDays) {
        try {
            String key = getFreezeUserKey(uid);
            if (ServerConfig.isProduct()) {
                clusterRedis.opsForValue().set(key, "1", freezeDays, TimeUnit.DAYS);
            } else {
                clusterRedis.opsForValue().set(key, "1", 5, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            logger.error("setFreezeUser error. uid={} freezeDays={} {}", uid, freezeDays, e.getMessage(), e);
        }
    }

    public void removeSendGiftFreezeUser(String uid) {
        try {
            clusterRedis.delete(getFreezeUserKey(uid));
        } catch (Exception e) {
            logger.error("removeSendGiftFreezeUser error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public int incMonitorUserSendGiftBeans(String uid, int beans) {
        try {
            String key = getSendGiftBeansKey();
            Long increment = clusterRedis.opsForHash().increment(key, uid, beans);
            clusterRedis.expire(key, 7, TimeUnit.DAYS);
            return increment.intValue();
        } catch (Exception e) {
            logger.error("incMonitorUserSendGiftBeans error. uid={} beans={} {}", uid, beans, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 送礼监控名单
     */
    private String getMonitorUserKey() {
        return "set:sendGiftMonitorUser";
    }

    /**
     * 送礼被冻结的用户
     */
    private String getFreezeUserKey(String uid) {
        return "str:sendGiftFreezeUser_" + uid;
    }

    /**
     * 被监控用户送礼钻石数
     */
    private String getSendGiftBeansKey() {
        return "hash:monitorUserSendGiftBeans_" + DateHelper.ARABIAN.formatDateInDay();
    }
}