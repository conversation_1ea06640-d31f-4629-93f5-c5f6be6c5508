package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.RoomPkGameInfo;
import com.quhong.data.RoomPkHallInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/12/5
 */
@Component
public class RoomPkGameRedis {

    private static final Logger logger = LoggerFactory.getLogger(RoomPkGameRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    public void savePkGameInfo(String pkGameId, RoomPkGameInfo pkGameInfo) {
        try {
            clusterTemplate.opsForValue().set(getRoomPkGameInfoKey(pkGameId), JSONObject.toJSONString(pkGameInfo), ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save room pk game info in redis error. pkGameId={}", pkGameId);
        }
    }

    public void deletePkGameInfo(String pkGameId) {
        try {
            clusterTemplate.delete(getRoomPkGameInfoKey(pkGameId));
        } catch (Exception e) {
            logger.error("delete room pk game info from redis. pkGameId={}", pkGameId);
        }
    }

    public RoomPkGameInfo getPkGameInfo(String pkGameId) {
        try {
            String value = clusterTemplate.opsForValue().get(getRoomPkGameInfoKey(pkGameId));
            return StringUtils.isEmpty(value) ? null : JSONObject.parseObject(value, RoomPkGameInfo.class);
        } catch (Exception e) {
            logger.error("get room pk game info from redis error. pkGameId={}", pkGameId);
            return null;
        }
    }

    public void addInvitedUser(String pkGameId, String roomId) {
        try {
            clusterTemplate.opsForSet().add(getInvitedUserKey(pkGameId), roomId);
        } catch (Exception e) {
            logger.error("add invited user to redis error. pkGameId={} roomId={} {}", pkGameId, roomId, e.getMessage(), e);
        }
    }

    public void deleteInvitedUser(String pkGameId) {
        try {
            clusterTemplate.delete(getInvitedUserKey(pkGameId));
        } catch (Exception e) {
            logger.error("delete invited user error. pkGameId={} {}", pkGameId, e.getMessage(), e);
        }
    }

    public boolean hasBeenInvited(String pkGameId, String roomId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getInvitedUserKey(pkGameId), roomId));
        } catch (Exception e) {
            logger.error("Check if user has already been invited. pkGameId={} roomId={} {}", pkGameId, roomId, e.getMessage(), e);
            return false;
        }
    }

    public void addInPkRoom(String roomId) {
        try {
            clusterTemplate.opsForSet().add(getInPkRoomKey(), roomId);
        } catch (Exception e) {
            logger.error("add in pk room to redis error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public void deleteInPkRoom(String roomId) {
        try {
            clusterTemplate.opsForSet().remove(getInPkRoomKey(), roomId);
        } catch (Exception e) {
            logger.error("delete in pk room error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public boolean roomInPk(String roomId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getInPkRoomKey(), roomId));
        } catch (Exception e) {
            logger.error("check room in pk. roomId={} {}", roomId, e.getMessage(), e);
            return false;
        }
    }

    public void saveRoomPkHallInfo(RoomPkHallInfo pkHallInfo) {
        try {
            clusterTemplate.opsForHash().put(getRoomPkHallInfoKey(), pkHallInfo.getPid(), JSONObject.toJSONString(pkHallInfo));
        } catch (Exception e) {
            logger.error("save room pk game info in redis error. gameId={} {}", pkHallInfo.getPid(), e.getMessage(), e);
        }
    }

    public Map<String, RoomPkHallInfo> getPkHallInfoMap() {
        Map<String, RoomPkHallInfo> resultMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getRoomPkHallInfoKey());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put(entry.getKey() + "", JSONObject.parseObject(entry.getValue() + "", RoomPkHallInfo.class));
            }
        } catch (Exception e) {
            logger.error("get room pk hall info map error. {}", e.getMessage(), e);
        }
        return resultMap;
    }


    public void deleteRoomPkHallInfo(String pkGameId) {
        try {
            clusterTemplate.opsForHash().delete(getRoomPkHallInfoKey(), pkGameId);
        } catch (Exception e) {
            logger.error("delete room pk game info error. gameId={} {}", pkGameId, e.getMessage(), e);
        }
    }


    public void addRoomPkWaitEndTime(String gameId, int endTime) {
        try {
            clusterTemplate.opsForZSet().add(getRoomPkWaitEndTimeKey(), gameId, endTime);
        } catch (Exception e) {
            logger.error("add room pk game wait end time in redis error. gameId={} endTime={} {}", gameId, endTime, e.getMessage(), e);
        }
    }

    public int getRoomPkWaitEndTime(String gameId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getRoomPkWaitEndTimeKey(), gameId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("get room pk game wait end time error. gameId={} {}", gameId, e.getMessage(), e);
            return 0;
        }
    }

    public void removeRoomPkWaitEndTime(String gameId) {
        try {
            clusterTemplate.opsForZSet().remove(getRoomPkWaitEndTimeKey(), gameId);
        } catch (Exception e) {
            logger.error("remove room pk game wait end time error. gameId={} {}", gameId, e.getMessage(), e);
        }
    }

    public Set<String> getWaitEndPkGameIds(int timestamp) {
        try {
            return clusterTemplate.opsForZSet().rangeByScore(getRoomPkWaitEndTimeKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get wait end room pk game id from redis error. timestamp={} {}", timestamp, e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    public void addRoomPkEndTime(String gameId, int endTime) {
        try {
            clusterTemplate.opsForZSet().add(getRoomPkEndTimeKey(), gameId, endTime);
        } catch (Exception e) {
            logger.error("add room pk game running time in redis error. gameId={} endTime={} {}", gameId, endTime, e.getMessage(), e);
        }
    }

    public int getRoomPkEndTime(String gameId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getRoomPkEndTimeKey(), gameId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("get room pk game end time error. gameId={} {}", gameId, e.getMessage(), e);
            return 0;
        }
    }

    public void removeRoomPkEndTime(String gameId) {
        try {
            clusterTemplate.opsForZSet().remove(getRoomPkEndTimeKey(), gameId);
        } catch (Exception e) {
            logger.error("remove room pk game end time error. gameId={} {}", gameId, e.getMessage(), e);
        }
    }

    public Set<String> getHasEndedPkGameIds(int timestamp) {
        try {
            return clusterTemplate.opsForZSet().rangeByScore(getRoomPkEndTimeKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get has ended room pk game id from redis error. timestamp={} {}", timestamp, e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    public void addPkGiftCount(String pkGameId, String uid) {
        try {
            String key = getPkGiftCountKey(pkGameId, uid);
            clusterTemplate.opsForValue().increment(key);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add pk gift count error. pkGameId={} uid={}", pkGameId, uid);
        }
    }

    public int getPkGiftCount(String pkGameId, String uid) {
        try {
            String value = clusterTemplate.opsForValue().get(getPkGiftCountKey(pkGameId, uid));
            return value != null ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("get pk gift count error. pkGameId={} uid={}", pkGameId, uid);
            return 0;
        }
    }

    public void incSendPkGiftNum(String pkGameId, String roomId, String fromUid, int devote) {
        String key = getRoomPkGiftNumKey(pkGameId, roomId);
        try {
            clusterTemplate.opsForZSet().incrementScore(key, fromUid, devote);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incSendPkGiftNum error. pkGameId={} roomId={} fromUid={} devote={} {}", pkGameId, roomId, fromUid, devote, e.getMessage(), e);
        }
    }

    public Map<String, Integer> getPkGiftNumRankingMap(String pkGameId, String roomId, int length) {
        String key = getRoomPkGiftNumKey(pkGameId, roomId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return Collections.emptyMap();
        }
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    private String getRoomPkGameInfoKey(String pkGameId) {
        return "str:room_pk_game_info_" + pkGameId;
    }

    private String getRoomPkHallInfoKey() {
        return "hash:room_pk_hall_info";
    }

    private String getRoomPkWaitEndTimeKey() {
        return "room_pk_game_waiting";
    }

    private String getRoomPkEndTimeKey() {
        return "room_pk_game_running";
    }

    private String getInvitedUserKey(String pkGameId) {
        return "set:has_been_invited" + pkGameId;
    }

    private String getInPkRoomKey() {
        return "set:room_in_pk_game";
    }

    private String getPkGiftCountKey(String pkGameId, String uid) {
        return pkGameId + "_" + uid;
    }

    private String getRoomPkGiftNumKey(String pkGameId, String roomId) {
        return "zset:pkGiftNum_" + pkGameId + "-" + roomId;
    }
}
