package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;

import com.quhong.data.SudGameInfo;
import com.quhong.data.SudGameSimpleInfo;
import com.quhong.mongo.data.SudGamePlayerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Component
public class SudGameRedis {

    private static final Logger logger = LoggerFactory.getLogger(SudGameRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public String getPlayerData(String uid) {
        String gameId = (String) redisTemplate.opsForHash().get(getSudGamePlayerKey(), uid);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return gameId;
    }

    public void savePlayerData(String uid, String gameId) {
        try {
            redisTemplate.opsForHash().put(getSudGamePlayerKey(), uid, gameId);
        } catch (Exception e) {
            logger.error("save player data error. {}", e.getMessage(), e);
        }
    }

    public Set<String> getPlayerUidSet() {
        Set<Object> keys = redisTemplate.opsForHash().keys(getSudGamePlayerKey());
        if (CollectionUtils.isEmpty(keys)) {
            return null;
        }
        return keys.stream().map(Object::toString).collect(Collectors.toSet());
    }

    public void removePlayerData(String uid) {
        try {
            redisTemplate.opsForHash().delete(getSudGamePlayerKey(), uid);
        } catch (Exception e) {
            logger.error("remove player data error. uid={}", uid, e);
        }
    }

    public List<SudGameInfo> getAllSudGameInfo() {
        try {
            List<SudGameInfo> gameInfoList = new ArrayList<>();
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getSudGameInfoKey());
            for (Object gameInfo : entries.values()) {
                gameInfoList.add(JSON.parseObject((String) gameInfo, SudGameInfo.class));
            }
            return gameInfoList;
        } catch (Exception e) {
            logger.error("get all sud game info error.", e);
        }
        return new ArrayList<>();
    }

    public SudGameInfo getSudGameInfo(String gameId) {
        try {
            String strObj = (String) redisTemplate.opsForHash().get(getSudGameInfoKey(), gameId);
            if (!StringUtils.isEmpty(strObj)) {
                return JSON.parseObject(strObj, SudGameInfo.class);
            }
        } catch (Exception e) {
            logger.error("get sud game info error.", e);
        }
        return null;
    }

    public String getGameIdByRoomId(String roomId) {
        String gameId = redisTemplate.opsForValue().get(getRoomSudGameKey(roomId));
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        String[] split = gameId.split("-");
        if (split.length == 2) {
            return split[0];
        }
        return gameId;
    }

    /**
     * 获取游戏类型 0表示没有正在进行的游戏
     */
    public int getGameTypeByRoomId(String roomId) {
        String gameId = redisTemplate.opsForValue().get(getRoomSudGameKey(roomId));
        if (StringUtils.isEmpty(gameId)) {
            return 0;
        }
        try {
            String[] split = gameId.split("-");
            if (split.length == 2) {
                return Integer.parseInt(split[1]);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return 0;
    }

    public String getGameIdAndTypeByRoomId(String roomId) {
        String gameId = redisTemplate.opsForValue().get(getRoomSudGameKey(roomId));
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return gameId;
    }

    public void saveSudGameInfo(SudGameInfo gameInfo) {
        try {
            String json = JSON.toJSONString(gameInfo);
            redisTemplate.opsForSet().add(getSudGameRoomSetKey(), gameInfo.getRoomId());
            redisTemplate.opsForHash().put(getSudGameInfoKey(), gameInfo.getGameId(), json);
            redisTemplate.opsForValue().set(getRoomSudGameKey(gameInfo.getRoomId()), buildRoomSudGameValue(gameInfo), ExpireTimeConstant.EXPIRE_DAYS_TWO, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save sud game info error. {}", e.getMessage(), e);
        }
    }

    public String buildRoomSudGameValue(SudGameInfo gameInfo) {
        return gameInfo.getGameId() + "-" + gameInfo.getGameType();
    }

    public void updateSudGameInfo(SudGameInfo gameInfo) {
        try {
            String json = JSON.toJSONString(gameInfo);
            redisTemplate.opsForHash().put(getSudGameInfoKey(), gameInfo.getGameId(), json);
        } catch (Exception e) {
            logger.error("remove sud game info error. {}", e.getMessage(), e);
        }
    }

    public void removeGameInfo(String gameId, String roomId) {
        try {
            redisTemplate.opsForHash().delete(getSudGameInfoKey(), gameId);
            redisTemplate.opsForSet().remove(getSudGameRoomSetKey(), roomId);
            redisTemplate.delete(getRoomSudGameKey(roomId));
        } catch (Exception e) {
            logger.error("remove sud game info error. {}", e.getMessage(), e);
        }
    }

    public int incSudGameBeansChangeSum(int gameType, int change) {
        String key = getSudGameBeansChangeSumKey(gameType);
        try {
            Long afterValue = redisTemplate.opsForValue().increment(key, change);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
            return afterValue != null ? afterValue.intValue() : 0;
        } catch (Exception e) {
            logger.error("incSudGameBeansChangeSum error. gameType={} change={} {}", gameType, change, e.getMessage(), e);
            return 0;
        }
    }

    public int getSudGameWarnLevel(int gameType) {
        try {
            String value = redisTemplate.opsForValue().get(getSudGameWarnLevelKey(gameType));
            return StringUtils.isEmpty(value) ? 0 : Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("getSudGameWarnLevel error. gameType={} {}", gameType, e.getMessage(), e);
            return 0;
        }
    }

    public void setSudGameWarnLevel(int gameType, int warnLevel) {
        String key = getSudGameWarnLevelKey(gameType);
        try {
            redisTemplate.opsForValue().set(key, warnLevel + "", ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("getSudGameWarnLevel error. gameType={} warnLevel={} {}", gameType, warnLevel, e.getMessage(), e);
        }
    }

    /**
     * 游戏玩家
     */
    private String getSudGamePlayerKey() {
        return "hash:sud_game_player";
    }

    /**
     * 游戏信息
     */
    private String getSudGameInfoKey() {
        return "hash:sud_game_info";
    }

    /**
     * 房间内游戏
     */
    private String getRoomSudGameKey(String roomId) {
        return "str:room_sud_game_" + roomId;
    }

    /**
     * 游戏的房间Set
     */
    private String getSudGameRoomSetKey() {
        return "set:sudGameRoom";
    }

    private String getSudGameBeansChangeSumKey(int gameType) {
        return "str:sudGameBeansChangeSum:" + gameType + "_" + DateHelper.ARABIAN.formatDateInDay();
    }

    private String getSudGameWarnLevelKey(int gameType) {
        return "str:platformWarnLevel:" + gameType + "_" + DateHelper.ARABIAN.formatDateInDay();
    }

    public void setAbnormalUser(int gameType, String uid) {
        String key = getAbnormalUserKey(gameType);
        try {
            redisTemplate.opsForSet().add(key, uid);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setAbnormalUser error. gameType={} uid={} {}", gameType, uid, e.getMessage(), e);
        }
    }

    public Set<String> getAbnormalUser(int gameType) {
        try {
            return redisTemplate.opsForSet().members(getAbnormalUserKey(gameType));
        } catch (Exception e) {
            logger.error("getAbnormalUser error. gameType={} {}", gameType, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    private String getAbnormalUserKey(int gameType) {
        return "set:abnormalUser" + gameType;
    }

    public int incPrizePoolBeans(String gameId, int beans) {
        String key = getPrizePoolBeansKey(gameId);
        try {
            Long afterValue = redisTemplate.opsForValue().increment(key, beans);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
            return afterValue != null ? afterValue.intValue() : 0;
        } catch (Exception e) {
            logger.error("incPrizePoolBeans error. gameId={} change={} {}", gameId, beans, e.getMessage(), e);
            return 0;
        }
    }

    private String getPrizePoolBeansKey(String gameId) {
        return "str:gamePrizePoolBeans:" + gameId;
    }

    public List<String> getInGameUserList(String roomId) {
        List<String> inGameList = Collections.emptyList();
        String gameId = getGameIdByRoomId(roomId);
        if (StringUtils.hasLength(gameId)) {
            SudGameInfo sudGameInfo = getSudGameInfo(gameId);
            if (sudGameInfo != null && !CollectionUtils.isEmpty(sudGameInfo.getPlayerList())) {
                inGameList = sudGameInfo.getPlayerList().stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
            }
        }
        return inGameList;
    }

    public SudGameSimpleInfo getSudGameSimpleInfo(String roomId) {
        SudGameSimpleInfo sudGameSimpleInfo = new SudGameSimpleInfo();
        String gameId = getGameIdByRoomId(roomId);
        if (StringUtils.hasLength(gameId)) {
            SudGameInfo sudGameInfo = getSudGameInfo(gameId);
            if (sudGameInfo != null && !CollectionUtils.isEmpty(sudGameInfo.getPlayerList())) {
                sudGameSimpleInfo.setGameId(gameId);
                sudGameSimpleInfo.setGameType(sudGameInfo.getGameType());
                sudGameSimpleInfo.setStatus(sudGameInfo.getStatus());
                sudGameSimpleInfo.setPlayerUidList(sudGameInfo.getPlayerList().stream().map(SudGamePlayerData::getUid).collect(Collectors.toList()));
            }
        }
        return sudGameSimpleInfo;
    }

    public SudGameInfo getSudGameInfoByRoomId(String roomId) {
        String gameId = getGameIdByRoomId(roomId);
        if (StringUtils.hasLength(gameId)) {
            return getSudGameInfo(gameId);
        }
        return null;
    }

    /**
     * spy游戏mic状态
     */
    private String getWhoIsSpyMicStatusKey(String roomId) {
        return "str:whoIsSpyMicStatus:" + roomId;
    }

    public void setWhoIsSpyMicStatus(String roomId, String micStatusInfo) {
        String key = getWhoIsSpyMicStatusKey(roomId);
        try {

            redisTemplate.opsForValue().set(key, micStatusInfo, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setWhoIsSpyMicStatusSum error. roomId={} micStatusInfo={} {}", roomId, micStatusInfo, e.getMessage(), e);
        }
    }

    public String getWhoIsSpyMicStatus(String roomId) {
        String key = getWhoIsSpyMicStatusKey(roomId);
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            logger.error("getWhoIsSpyMicStatus error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return null;
    }

    public void delWhoIsSpyMicStatus(String roomId) {
        String key = getWhoIsSpyMicStatusKey(roomId);
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            logger.error("delWhoIsSpyMicStatus error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

}
