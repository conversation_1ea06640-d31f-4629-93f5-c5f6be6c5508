package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.ChatHallMsgVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Component
@Lazy
public class ChatHallRedis {

    private static final Logger logger = LoggerFactory.getLogger(ChatHallRedis.class);
    private static final int MAX_SIZE = 30;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    private String getChatHallRecordKey() {
        return "list:chatHall";
    }

    public void addCHatHallRecord(ChatHallMsgVO vo) {
        Long listSize = redisTemplate.opsForList().leftPush(getChatHallRecordKey(), JSON.toJSONString(vo));
        if (null != listSize && listSize > MAX_SIZE * 8) {
            redisTemplate.opsForList().trim(getChatHallRecordKey(), 0, MAX_SIZE);
        }
    }

    public List<ChatHallMsgVO> getChatHallList() {
        try {
            List<String> jsonList = redisTemplate.opsForList().range(getChatHallRecordKey(), 0, MAX_SIZE - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            List<ChatHallMsgVO> resultList = new ArrayList<>(MAX_SIZE);
            for (String json : jsonList) {
                resultList.add(JSON.parseObject(json, ChatHallMsgVO.class));
            }
            return resultList;
        } catch (Exception e) {
            logger.error("getChatHallList error", e);
            return Collections.emptyList();
        }
    }

    public Long delChatHall(ChatHallMsgVO item) {
        try {
            return redisTemplate.opsForList().remove(getChatHallRecordKey(), 0, JSON.toJSONString(item));
        } catch (Exception e) {
            logger.error("delChatHall item:{} msg:{} error", item, e.getMessage(), e);
            return null;
        }
    }

    public static void main(String[] args) {
        String a = "{\"aid\":\"5e815ad9644f8e00320bf3f4\",\"bubbleId\":108,\"content\":\"\",\"gender\":2,\"head\":\"https://cloudcdn.qmovies.tv/user/C084C2853FC4E2BD3470072BF6EBEAEF.jpg?x-oss-process=image/resize,m_mfit,w_150,h_150/quality,Q_85/format,jpg\",\"msgId\":\"3650754451372834817\",\"msgInfo\":{\"img\":\"room_message/7E0C397627E434569A95346B938143CB.jpg\",\"width\":828,\"thumbnailStaticUrl\":\"https://cloudcdn.qmovies.tv/room_message/7E0C397627E434569A95346B938143CB.jpg?x-oss-process=image/resize,m_lfit,w_200,h_300/quality,Q_85/format,jpg\",\"url\":\"https://cloudcdn.qmovies.tv/room_message/7E0C397627E434569A95346B938143CB.jpg\",\"height\":1792,\"thumbnailUrl\":\"https://cloudcdn.qmovies.tv/room_message/7E0C397627E434569A95346B938143CB.jpg?x-oss-process=image/resize,m_lfit,w_200,h_300/quality,Q_85\"},\"msgType\":3,\"name\":\"ابناذناذماذنلطن\",\"pushed\":\"488\",\"vipLevel\":10}";
        ChatHallMsgVO vo = JSON.parseObject(a, ChatHallMsgVO.class);
        System.out.println(1);
    }

    private String getShareCountKey(String uid) {
        return "ser:shareCount:" + uid + ":" + DateHelper.DEFAULT.formatDateInDay(new Date());
    }

    public int incrShareCount(String uid) {
        String key = getShareCountKey(uid);
        Long increment = redisTemplate.opsForValue().increment(key, 1);
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return null == increment ? 0 : increment.intValue();
    }

}
