package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.TurntableGameInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2022/10/13
 */
@Component
public class TurntableGameRedis {

    private static final Logger logger = LoggerFactory.getLogger(TurntableGameRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 获取转盘游戏信息
     */
    public TurntableGameInfo getGameInfo(String gameId) {
        try {
            String strValue = clusterTemplate.opsForValue().get(getTurntableGameKey(gameId));
            if (StringUtils.isEmpty(strValue)) {
                return null;
            }
            return JSONObject.parseObject(strValue, TurntableGameInfo.class);
        } catch (Exception e) {
            logger.error("get turntable game info error. gameId={}", gameId);
            return null;
        }
    }

    public TurntableGameInfo getGameInfoByRoomId(String roomId) {
        String gameId = getGameIdFromRedis(roomId);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return getGameInfo(gameId);
    }

    /**
     * 设置转盘游戏信息
     */
    public void setGameInfo(TurntableGameInfo gameInfo) {
        String key = getTurntableGameKey(gameInfo.get_id());
        try {
            clusterTemplate.opsForValue().set(key, JSONObject.toJSONString(gameInfo));
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set turntable game info error. gameId={}", gameInfo.get_id());
        }
    }

    /**
     * 获取所有转盘游戏信息
     */
    public List<TurntableGameInfo> getAllTurntableGame() {
        List<TurntableGameInfo> gameInfoList = new ArrayList<>();
        try {
            List<Object> list = clusterTemplate.opsForHash().values(getRoomTurntableGameKey());
            if (!CollectionUtils.isEmpty(list)) {
                for (Object object : list) {
                    TurntableGameInfo gameInfo = getGameInfo((String) object);
                    if (gameInfo != null) {
                        gameInfoList.add(gameInfo);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("getAllTurntableGame error. {}", e.getMessage(), e);
        }
        return gameInfoList;
    }

    public void removeGameId(String roomId) {
        try {
            clusterTemplate.opsForHash().delete(getRoomTurntableGameKey(), roomId);
        } catch (Exception e) {
            logger.error("remove turntable game id error. roomId={}", roomId);
        }
    }

    public void setGameIdInRedis(String roomId, String gameId) {
        String key = getRoomTurntableGameKey();
        try {
            clusterTemplate.opsForHash().put(key, roomId, gameId);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set turntable gameId in redis error. roomId={} gameId={}", roomId, gameId);
        }
    }

    public String getGameIdFromRedis(String roomId) {
        try {
            return (String) clusterTemplate.opsForHash().get(getRoomTurntableGameKey(), roomId);
        } catch (Exception e) {
            logger.error("get turntable gameId from redis error. roomId={}", roomId);
            return "";
        }
    }

    public void setGameTimerRunning(String gameId, int endTime) {
        String key = getGameTimerRunningKey();
        try {
            clusterTemplate.opsForZSet().add(key, gameId, endTime);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set game timer running error. gameId={} endTime={}", gameId, endTime);
        }
    }

    public Set<String> getRunningEndGameIds(int timestamp) {
        try {
            return clusterTemplate.opsForZSet().rangeByScore(getGameTimerRunningKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get running end game ids error. timestamp={}", timestamp);
            return new HashSet<>();
        }
    }

    public void removeGameTimerRunning(String gameId) {
        try {
            clusterTemplate.opsForZSet().remove(getGameTimerRunningKey(), gameId);
        } catch (Exception e) {
            logger.error("remove game timer running error. gameId={}", gameId);
        }
    }

    public void setGameTimerWaiting(String gameId, int endTime) {
        String key = getGameTimerWaitingKey();
        try {
            clusterTemplate.opsForZSet().add(key, gameId, endTime);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set game timer waiting error. gameId={} endTime={}", gameId, endTime);
        }
    }

    public Set<String> getWaitingEndGameIds(int timestamp) {
        try {
            return clusterTemplate.opsForZSet().rangeByScore(getGameTimerWaitingKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get waiting end game ids error. timestamp={}", timestamp);
            return new HashSet<>();
        }
    }

    public void removeGameTimerWaiting(String gameId) {
        try {
            clusterTemplate.opsForZSet().remove(getGameTimerWaitingKey(), gameId);
        } catch (Exception e) {
            logger.error("remove game timer waiting error. gameId={}", gameId);
        }
    }

    private String getRoomTurntableGameKey() {
        return "hash:room_turntable_game_key";
    }

    private String getTurntableGameKey(String gameId) {
        return "str:turntable_game_" + gameId;
    }

    private String getGameTimerRunningKey() { return "zset:turntable_game_timer1"; }

    private String getGameTimerWaitingKey() { return "zset:turntable_game_timer2"; }
}
