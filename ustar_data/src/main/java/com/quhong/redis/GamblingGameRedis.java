package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/1/17
 */
@Component
@Lazy
public class GamblingGameRedis {

    private static final Logger logger = LoggerFactory.getLogger(GamblingGameRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 用户加入百顺游戏盈利白名单
     * @param uid       用户uid
     * @param diamonds  用户盈利额度
     */
    public void addBsGameProfitList(String uid, int diamonds) {
        String key = getBsGameProfitListKey();
        try {
            redisTemplate.opsForHash().put(key, uid, diamonds + "");
            redisTemplate.expire(key, 90, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addBsGameProfitList error. uid={} diamonds={} {}", uid, diamonds, e.getMessage(), e);
        }
    }

    /**
     * 增加百顺游戏用户盈利额
     * @param uid       用户uid
     * @param diamonds  增加的用户盈利额度
     */
    public void incrementProfit(String uid, int diamonds) {
        String key = getBsGameProfitListKey();
        try {
            if (redisTemplate.opsForHash().hasKey(key, uid)) {
                int afterValue = redisTemplate.opsForHash().increment(key, uid, diamonds).intValue();
                logger.info("incrementProfit. uid={} changeValue={} afterValue={}", uid, diamonds, afterValue);
                if (afterValue <= 0) {
                    // 用户盈利额消耗完后从盈利白名单中移除
                    redisTemplate.opsForHash().delete(key, uid);
                }
            }
        } catch (Exception e) {
            logger.error("incrementProfit error. uid={} diamonds={} {}", uid, diamonds, e.getMessage(), e);
        }
    }

    public void removeProfit(String uid) {
        try {
            redisTemplate.opsForHash().delete(getBsGameProfitListKey(), uid);
        } catch (Exception e) {
            logger.error("removeProfit error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 获取百顺游戏用户盈利额
     * @param uid       用户uid
     */
    public int getUserProfit(String uid) {
        String key = getBsGameProfitListKey();
        try {
            String strValue = (String) redisTemplate.opsForHash().get(key, uid);
            return StringUtils.hasLength(strValue) ? Integer.parseInt(strValue) : 0;
        } catch (Exception e) {
            logger.error("getUserProfit error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 用户加入百顺游戏亏损黑名单
     * @param uid       用户uid
     * @param diamonds  用户亏损额度
     */
    public void addBsGameLossList(String uid, int diamonds) {
        String key = getBsGameLossListKey();
        try {
            redisTemplate.opsForHash().put(key, uid, diamonds + "");
            redisTemplate.expire(key, 90, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addBsGameLossList error. uid={} diamonds={} {}", uid, diamonds, e.getMessage(), e);
        }
    }

    /**
     * 增加百顺游戏用户亏损额
     * @param uid       用户uid
     * @param diamonds  增加的用户亏损额度
     */
    public void incrementLoss(String uid, int diamonds) {
        String key = getBsGameLossListKey();
        try {
            if (redisTemplate.opsForHash().hasKey(key, uid)) {
                int afterValue = redisTemplate.opsForHash().increment(key, uid, diamonds).intValue();
                logger.info("incrementLoss. uid={} changeValue={} afterValue={}", uid, diamonds, afterValue);
                if (afterValue <= 0) {
                    // 用户亏损额消耗完后从亏损黑名单中移除
                    redisTemplate.opsForHash().delete(key, uid);
                }
            }
        } catch (Exception e) {
            logger.error("reduceLoss error. uid={} diamonds={} {}", uid, diamonds, e.getMessage(), e);
        }
    }

    public void removeLoss(String uid) {
        try {
            redisTemplate.opsForHash().delete(getBsGameLossListKey(), uid);
        } catch (Exception e) {
            logger.error("removeLoss error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 获取百顺游戏用户亏损额
     */
    public int getUserLoss(String uid) {
        String key = getBsGameLossListKey();
        try {
            String strValue = (String) redisTemplate.opsForHash().get(key, uid);
            return StringUtils.hasLength(strValue) ? Integer.parseInt(strValue) : 0;
        } catch (Exception e) {
            logger.error("getUserLoss error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    private String getBsGameProfitListKey() {
        return "hash:bsGameProfitList";
    }

    private String getBsGameLossListKey() {
        return "hash:bsGameLossList";
    }
}
