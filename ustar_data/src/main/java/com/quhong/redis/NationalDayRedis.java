package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class NationalDayRedis {
    private static final Logger logger = LoggerFactory.getLogger(NationalDayRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    public void incrScore(int activityId, String aid, int effectNum, int score) {
        try {
            String key = getActivityKey(activityId, effectNum);
            Double value = clusterTemplate.opsForZSet().incrementScore(key, aid, score);
            int intValue = 0;
            if (null != value) {
                intValue = value.intValue();
            }
            logger.info("incrScore activityId={} aid={} effectNum={} score={} total={}", activityId, aid, effectNum, score, intValue);
            clusterTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrScore error activityId={} aid={} effectNum={} score={}", activityId, aid, effectNum, score, e);
        }
    }


    /**
     * 获取分数
     */
    public int getScore(int activityId, String uid, int effectNum) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getActivityKey(activityId, effectNum), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getScore error activityId={} uid={} effectNum={}", activityId, uid, effectNum, e);
            return -1;
        }
    }

    private String getActivityKey(int activityId, int specialEffect) {
        return "zset:nationalDayActivity:" + activityId + ":" + specialEffect;
    }
}
