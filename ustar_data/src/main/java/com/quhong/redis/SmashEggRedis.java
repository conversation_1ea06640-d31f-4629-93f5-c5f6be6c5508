package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class SmashEggRedis {
    private static final Logger logger = LoggerFactory.getLogger(SmashEggRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final int MAX_SIZE = 30;
    private static final String GAME_KEY = "smash_egg_prize";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate prizePoolRedisTemplate;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String smashHashKey() {
        return "smash_egg_score";
    }


    public Integer getSmashLuckyValue(String uid) {
        try {
            String luckyValue = (String) prizePoolRedisTemplate.opsForHash().get(smashHashKey(), uid);
            if (StringUtils.isEmpty(luckyValue)) {
                return 0;
            }
            return Integer.parseInt(luckyValue);
        } catch (Exception e) {
            logger.error("getSmashLuckyValue error uid={}, e={}", uid, e);
        }
        return 0;
    }

    public int setSmashLuckyValue(String uid, int luckyValue){
        try {
            prizePoolRedisTemplate.opsForHash().put(smashHashKey(), uid, String.valueOf(luckyValue));
            return luckyValue;
        } catch (Exception e) {
            logger.error("setSmashLuckyValue error uid={}, e={}", uid, e.getMessage());
        }
        return 0;
    }

    public void delSmashLuckyValue(String uid){
        try {
            prizePoolRedisTemplate.opsForHash().delete(smashHashKey(), uid);
        } catch (Exception e) {
            logger.error("delSmashLuckyValue error uid={}, e={}", uid, e.getMessage());
        }
    }


    private String smashTTLKey() {
        return "smash_egg_ttl";
    }

    public void removeSmashTTL(String uid) {
        try {
            prizePoolRedisTemplate.opsForZSet().remove(smashTTLKey(), uid);
        } catch (Exception e) {
            logger.error("removeSmashTTL error uid={}, e={}", uid, e.getMessage());
        }
    }

    public void addSmashTTL(String uid, int endTime) {
        try {
            prizePoolRedisTemplate.opsForZSet().add(smashTTLKey(), uid, endTime);
        } catch (Exception e) {
            logger.error("addSmashTTL error uid={}, e={}", uid, e.getMessage());
        }
    }

    public Map<String, Integer> getSmashExpireTimeMapByScore(int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = smashTTLKey();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = prizePoolRedisTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }



    private String poolSizeKey() {
        return "list:pool_size:"+ GAME_KEY;
    }



    public int getPoolSize() {
        try {
            Long poolSize = prizePoolRedisTemplate.opsForList().size(poolSizeKey());
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("getPoolSize error score={}", e.getMessage());
            return 0;
        }
    }

    public void deletePoolSize() {
        try {
            prizePoolRedisTemplate.delete(poolSizeKey());
        } catch (Exception e) {
            logger.error("deletePoolSize error={}", e.getMessage());
        }
    }



    public void initPoolSize(List<String> rewardConfigList) {
        try {
            prizePoolRedisTemplate.opsForList().rightPushAll(poolSizeKey(), rewardConfigList);
            prizePoolRedisTemplate.expire(poolSizeKey(), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("initPoolSize error clusterTemplate={}  score={}", prizePoolRedisTemplate, e);
        }
    }


    // 抽奖
    public String drawSmashEggKey() {
        try {
            String prizeKey = prizePoolRedisTemplate.opsForList().leftPop(poolSizeKey());
            return prizeKey != null ? prizeKey : "";
        } catch (Exception e) {
            logger.error("drawSmashEggKey error e={}", e.getMessage());
            return "";
        }
    }


    /**
     * 砸蛋轮播记录
     */
    private String getSmashRollKey() {
        return "list:SmashEggRoll";
    }

    public void addSmashRollRecord(String uid, String drawType) {

        String drawRollKey = uid + "_" + drawType;
        Long listSize = clusterTemplate.opsForList().leftPush(getSmashRollKey(), drawRollKey);
        if (null != listSize && listSize > MAX_SIZE) {
            clusterTemplate.opsForList().trim(getSmashRollKey(), 0, MAX_SIZE);
        }
    }

    public List<String> getSmashRollRecordList() {
        try {
            List<String> jsonList = clusterTemplate.opsForList().range(getSmashRollKey(), 0, MAX_SIZE - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            List<String> resultList = new ArrayList<>(MAX_SIZE);
            resultList.addAll(jsonList);
            return resultList;
        } catch (Exception e) {
            logger.error("getSmashRollRecordList error", e);
            return Collections.emptyList();
        }
    }


}
