package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class IdentifyRedis {
    private static final Logger logger = LoggerFactory.getLogger(IdentifyRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String vipExpirePushDateKey(){
         return  "vip_expire_push_date";
    }

    public String getVipExpirePushDate() {
        try {
            return clusterTemplate.opsForValue().get(vipExpirePushDateKey());
        } catch (Exception e) {
            logger.info("getVipExpirePushDate error e={}", e.getMessage());
        }
        return "";
    }

    public void setVipExpirePushDate(String checkDate) {
        try {
            clusterTemplate.opsForValue().set(vipExpirePushDateKey(), checkDate, ExpireTimeConstant.EXPIRE_DAYS_TEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setVipExpirePushDate error e={}", e.getMessage());
        }
    }

    /**
     * vip新旧开关切换key
     */
    private String vipVersionHashKey() {
        return "hash:vip:version";
    }

    public void setVipVersionCache(String uid, int value) {
        try {
            clusterTemplate.opsForHash().put(vipVersionHashKey(), uid, String.valueOf(value));
        } catch (Exception e) {
            logger.info("setVipVersion error uid={}, e={}", uid, e.getMessage());
        }
    }

    public Integer getVipVersionCache(String uid) {
        try {
            String value = (String) clusterTemplate.opsForHash().get(vipVersionHashKey(), uid);
            return ObjectUtils.isEmpty(value) ? null : Integer.parseInt(value);
        } catch (Exception e) {
            logger.info("getVipVersion error uid={}, e={}", uid, e.getMessage());
            return null;
        }
    }
}
