package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class UserFeedbackRedis {
    private static final Logger logger = LoggerFactory.getLogger(UserFeedbackRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;


    public void setFeedbackStatus(String uid) {
        try {
            String key = getFeedbackKey(uid);
            clusterRedis.opsForValue().set(getFeedbackKey(uid), String.valueOf(1));
            clusterRedis.expire(key, 3, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setFeedbackStatus error: {}", e.getMessage(), e);
        }
    }


    public int getFeedbackStatus(String uid) {
        try {
            String status = clusterRedis.opsForValue().get(getFeedbackKey(uid));
            return status == null ? 0 : 1;
        } catch (Exception e) {
            logger.error("getFeedbackStatus error: {}", e.getMessage(), e);
        }
        return 0;
    }

    public void removeFeedbackStatus(String uid) {
        try {
            clusterRedis.delete(getFeedbackKey(uid));
        } catch (Exception e) {
            logger.error("removeFeedbackNum error:{}", e.getMessage(), e);
        }
    }

    private String getFeedbackKey(String uid) {
        return "str:userFeedback:" + uid;
    }
}
