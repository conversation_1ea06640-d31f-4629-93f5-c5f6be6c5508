package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Set;

@Lazy
@Component
public class BlockIpRedis {

    private static final Logger logger = LoggerFactory.getLogger(BlockIpRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public boolean isBlockIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }
        String key = getBlockIpKey();
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, ip));
        } catch (Exception e) {
            logger.error("error isBlockIp. ip={} msg={}", ip, e.getMessage(), e);
        }
        return false;
    }

    private String getBlockIpKey() {
        return "set:py_ban_ip_list";
    }

    public boolean addBlockIp(String ip) {
        String key = getBlockIpKey();
        try {
            redisTemplate.opsForSet().add(key, ip);
        } catch (Exception e) {
            logger.error("error addBlockIp. ip={} msg={}", ip, e.getMessage(), e);
        }
        return false;
    }

    public long delBlockIp(String ip) {
        String key = getBlockIpKey();
        try {
            Long count = redisTemplate.opsForSet().remove(key, ip);
            return count == null ? 0 : count;
        } catch (Exception e) {
            logger.error("error delBlockIp. ip={} msg={}", ip, e.getMessage(), e);
        }
        return 0;
    }

}
