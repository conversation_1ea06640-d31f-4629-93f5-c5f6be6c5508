package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.RoomMicData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class CheatGiftRedis {

    private static final Logger logger = LoggerFactory.getLogger(CheatGiftRedis.class);

    public static final String PRANK_GIFT = "prank";
    public static final String CHANG_VOICE_GIFT = "change_voice";
    public static final String DELIMITER = "#";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;


    public String getCheatGiftHashKey(String uid, String cheatGiftType) {
        return uid + DELIMITER + cheatGiftType;
    }

    public void setCheatGift(String uid, String data, String cheatGiftType) {
        try {
            redisTemplate.opsForHash().put(getCheatGiftKey(), getCheatGiftHashKey(uid, cheatGiftType), data);
        } catch (Exception e) {
            logger.error("set cheat gift url error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void delCheatGift(String uid, String cheatGiftType) {
        try {
            redisTemplate.opsForHash().delete(getCheatGiftKey(), getCheatGiftHashKey(uid, cheatGiftType));
        } catch (Exception e) {
            logger.error("set cheat gift url error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    private String getCheatGiftKey() {
        return "hash:cheatGift";
    }

    public void addCheatGiftTime(String uid, int endTime, String cheatGiftType) {
        try {
            redisTemplate.opsForZSet().add(getCheatGiftTimeKey(), getCheatGiftHashKey(uid, cheatGiftType), endTime);
        } catch (Exception e) {
            logger.error("get cheat gift time error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void delCheatGiftTime(String aid, String cheatGiftType) {
        try {
            redisTemplate.opsForZSet().remove(getCheatGiftTimeKey(), getCheatGiftHashKey(aid, cheatGiftType));
        } catch (Exception e) {
            logger.error("get cheat gift time error. uid={} {}", aid, e.getMessage(), e);
        }
    }

    public Set<String> getAllCheatGiftSet() {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getCheatGiftTimeKey(), 0, DateHelper.getNowSeconds());
        } catch (Exception e) {
            logger.error("get all cheat gift error. {}", e.getMessage(), e);
        }
        return null;
    }

    private String getCheatGiftTimeKey() {
        return "zset:cheatGiftTime";
    }

    public Map<String, CheatGiftData> fillCheatGift(List<RoomMicData> micList) {
        Map<String, CheatGiftData> map = new LinkedHashMap<>();
        try {
            for (RoomMicData micData : micList) {
                if (!StringUtils.isEmpty(micData.getUid())) {
                    map.put(micData.getUid(), new CheatGiftRedis.CheatGiftData());
                }
            }
            if (map.isEmpty()) {
                return Collections.emptyMap();
            }
            Map<String, String> linkedHashMap = new LinkedHashMap<>();
            for (String aid : map.keySet()) {
                linkedHashMap.put(getCheatGiftHashKey(aid, PRANK_GIFT), "");
                linkedHashMap.put(getCheatGiftHashKey(aid, CHANG_VOICE_GIFT), "");
            }
            List<Object> resultList = redisTemplate.opsForHash().multiGet(getCheatGiftKey(), Arrays.asList(linkedHashMap.keySet().toArray()));
            for (Object data : resultList) {
                for (String key : linkedHashMap.keySet()) {
                    // 还未赋值
                    if ("".equals(linkedHashMap.get(key))) {
                        linkedHashMap.put(key, null == data ? null : String.valueOf(data));
                        if (null != data) {
                            String[] split = key.split(DELIMITER);
                            String aid = split[0];
                            String cheatGiftType = split[1];
                            CheatGiftData cheatGiftData = map.get(aid);
                            if (PRANK_GIFT.equals(cheatGiftType)) {
                                cheatGiftData.setPrankMicFrame(String.valueOf(data));
                            } else if (CHANG_VOICE_GIFT.equals(cheatGiftType)) {
                                cheatGiftData.setVoiceType(Integer.parseInt(String.valueOf(data)));
                            }
                        }
                        // 赋值下一个
                        break;
                    }
                }
            }
            return map;
        } catch (Exception e) {
            logger.error("fill cheat gift url error. aidSet={} {}", map.keySet(), e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    public static class CheatGiftData {
        private String prankMicFrame; // 整蛊礼物麦位框
        private int voiceType; // 变声类型，0原声、1女声、2男声、3擎天柱、4孩童

        public String getPrankMicFrame() {
            return prankMicFrame;
        }

        public void setPrankMicFrame(String prankMicFrame) {
            this.prankMicFrame = prankMicFrame;
        }

        public int getVoiceType() {
            return voiceType;
        }

        public void setVoiceType(int voiceType) {
            this.voiceType = voiceType;
        }
    }
}
