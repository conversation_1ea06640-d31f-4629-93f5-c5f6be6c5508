package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.SudGameConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Component
public class GameRoomRedis {

    private static final Logger logger = LoggerFactory.getLogger(GameRoomRedis.class);
    public static final List<String> PRIZE_GAME_TYPE = Arrays.asList("17", "16", "18", "13", "slots", "horse_racing", "crash", "fishing", "fast3", "greedy");      // 数字-自研游戏、字符第三方游戏： 水果机、slot、Crash、Greedy、捕鱼

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public Map<String, Integer> getAllGameRoomType() {
        Map<String, Integer> ramadanMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getSudGameTypeKey());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                ramadanMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return ramadanMap;
        } catch (Exception e) {
            logger.error("getAllGameRoom error e={}", e.getMessage(), e);
            return ramadanMap;
        }
    }

    public List<Integer> getAllSudGameType() {
        try {
            List<Integer> gameInfoList = new ArrayList<>();
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getSudGameTypeKey());
            for (Object gameInfo : entries.values()) {
//                gameInfoList.add(JSON.parseObject((String) gameInfo, SudGameInfo.class));
                gameInfoList.add(JSON.parseObject((String) gameInfo, Integer.class));
            }
            return gameInfoList;
        } catch (Exception e) {
            logger.error("get all sud game info error.", e);
        }
        return new ArrayList<>();
    }

    public Integer getGameType(String roomId) {
        try {
            String strObj = (String) redisTemplate.opsForHash().get(getSudGameTypeKey(), roomId);
            if (!StringUtils.isEmpty(strObj)) {
                return Integer.parseInt(strObj);
//                return JSON.parseObject(strObj, SudGameInfo.class);
            }
        } catch (Exception e) {
            logger.error("get sud game info error.", e);
        }
        return null;
    }


    public void saveGameType(String roomId, int gameType) {
        try {
//            String json = JSON.toJSONString(gameInfo);
            redisTemplate.opsForHash().put(getSudGameTypeKey(), roomId, String.valueOf(gameType));
            redisTemplate.expire(getSudGameTypeKey(), 1, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save sud game info error. {}", e.getMessage(), e);
        }
    }


    public void removeGameType(String roomId) {
        try {
            redisTemplate.opsForHash().delete(getSudGameTypeKey(), roomId);
        } catch (Exception e) {
            logger.error("remove sud game info error. {}", e.getMessage(), e);
        }
    }

    /**
     * 游戏信息-测试用
     */
    private String getSudGameTypeKey() {
        return "hash:sud_game_room:test";
    }

    /**
     * 统计最近玩互动类游戏、概率类游戏的时间
     * key: gameType
     * value: 时间戳
     */
    private String getLastPlayGameTimeKey(String uid) {
        return String.format("hash:last_play_game_time:%s", uid);
    }

    /**
     * @param uid
     * @param gameType <10的为sud的互动游戏  10~1000为自研的中台游戏  >1000为百顺的概率游戏
     */
    public void updateLastPlayGameTime(String uid, String gameType) {
        try {
            redisTemplate.opsForHash().put(getLastPlayGameTimeKey(uid), gameType, String.valueOf(DateHelper.getNowSeconds()));
            redisTemplate.expire(getSudGameTypeKey(), ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("updateLastPlayGameTime error. {}", e.getMessage(), e);
        }
    }

    public Map<String, Integer> getAllLastPlayGameTime(String uid) {
        Map<String, Integer> playGameMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getLastPlayGameTimeKey(uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                playGameMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return playGameMap;
        } catch (Exception e) {
            logger.info("getAllLastPlayGameTime error e={}", e.getMessage(), e);
            return playGameMap;
        }
    }



    @Cacheable(value = "getShowBcGameEntry", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE, key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public boolean getShowBcGameEntry(String uid) {
        Map<String, Integer> allPlayGameTimeMap = getAllLastPlayGameTime(uid);
        int last15Time = DateHelper.getNowSeconds() - 30 * 86400;
        for (String gameType : allPlayGameTimeMap.keySet()) {
            int playTime = allPlayGameTimeMap.getOrDefault(gameType, 0);
            if (playTime > last15Time) {
                return true;
            }
        }
        return false;
    }


    /**
     * 统计社交主页各个type当前的人数
     * key: gameType
     * value: 当前在线人数
     */
    private String getNowGameTypePlayCountKey() {
        return "hash:now_game_type_play_count";
    }

    /**
     * @param gameType
     * @param onlineCount 在线人数或者最近24小时进房间访客人数
     * @see SudGameConstant#LUDO_GAME（gameType 新版游戏房的2-6）  1050 voice party  1051 watch video 1052 bc游戏统计
     * gameType（语音房的）飞行棋 10002   UMO 10003  消消乐 10004  多米诺 10005 克罗姆 10006 统计在线人数
     */
    public void updateGameOnline(String gameType, String onlineCount) {
        try {
            redisTemplate.opsForHash().put(getNowGameTypePlayCountKey(), gameType, onlineCount);
            redisTemplate.expire(getSudGameTypeKey(), ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("updateGameOnline error. {}", e.getMessage(), e);
        }
    }

    public Map<Integer, Integer> getAllOnlinePlayCount() {
        Map<Integer, Integer> playGameMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getNowGameTypePlayCountKey());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                playGameMap.put(Integer.parseInt(String.valueOf(entry.getKey())), Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return playGameMap;
        } catch (Exception e) {
            logger.info("getAllOnlinePlayCount error e={}", e.getMessage(), e);
            return playGameMap;
        }
    }

    public void removeGameOnlineKey() {
        try {
            redisTemplate.delete(getNowGameTypePlayCountKey());
        } catch (Exception e) {
            logger.error("removeGameOnlineKey error. {}", e.getMessage(), e);
        }
    }


    private static final Map<String, String> SET_EXPIRE_MAP = new HashMap<>();

    /**
     * 新增BC游戏访客记录
     */
    public void addBCGameRecord(String uid) {
        String key = getGameBCRecordKey();
        try {
            int nowTime = DateHelper.getNowSeconds();
            redisTemplate.opsForZSet().add(key, uid, nowTime);
            String strToday = DateHelper.ARABIAN.formatDateInDay();
            if (!strToday.equals(SET_EXPIRE_MAP.getOrDefault(key, ""))) {
                // 移除24小时前的旧数据
                redisTemplate.opsForZSet().removeRangeByScore(key, 0, nowTime - (int) TimeUnit.DAYS.toSeconds(1));
                redisTemplate.expire(key, 3, TimeUnit.DAYS);
                SET_EXPIRE_MAP.put(key, DateHelper.ARABIAN.formatDateInDay());
            }
        } catch (Exception e) {
            logger.error("addBCGameRecord error, uid={} {}", uid, e.getMessage());
        }
    }

    /**
     * 获取BC游戏访客Set （最近24小时内BC游戏用户）
     */
    public Set<String> getGameBCVisitorSet() {
        String key = getGameBCRecordKey();
        try {
            int nowTime = DateHelper.getNowSeconds();
            return redisTemplate.opsForZSet().rangeByScore(key, nowTime - (int) TimeUnit.DAYS.toSeconds(1), nowTime);
        } catch (Exception e) {
            logger.error("getGameBCVisitorSet error  {}", e.getMessage());
            return Collections.emptySet();
        }
    }

    /**
     * 获取BC游戏访客数量 （最近24小时玩bc游戏人数）
     */
    public int getGameBCNum() {

        String key = getGameBCRecordKey();
        try {
            int nowTime = DateHelper.getNowSeconds();
            Long count = redisTemplate.opsForZSet().count(key, nowTime - (int) TimeUnit.DAYS.toSeconds(1), nowTime);
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            logger.error("getGameBCNum error {}", e.getMessage());
            return 0;
        }
    }

    private String getGameBCRecordKey() {
        return "zset:gameBCRecord";
    }

    /**
     * 统计最近点击匹配游戏的时间
     * key: gameType
     * value: 时间戳
     */
    private String getMatchGameTimeKey(String uid) {
        return String.format("hash:last_match_game_time:%s", uid);
    }

    public void updateMatchGameTime(String uid, String gameType) {
        try {
            redisTemplate.opsForHash().put(getMatchGameTimeKey(uid), gameType, String.valueOf(DateHelper.getNowSeconds()));
            redisTemplate.expire(getSudGameTypeKey(), ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("updateMatchGameTime error. {}", e.getMessage(), e);
        }
    }

    public Map<String, Integer> getAllMatchGameTime(String uid) {
        Map<String, Integer> matchGameTimeMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getMatchGameTimeKey(uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                matchGameTimeMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return matchGameTimeMap;
        } catch (Exception e) {
            logger.error("getAllMatchGameTime error e={}", e.getMessage(), e);
        }
        return matchGameTimeMap;
    }


}
