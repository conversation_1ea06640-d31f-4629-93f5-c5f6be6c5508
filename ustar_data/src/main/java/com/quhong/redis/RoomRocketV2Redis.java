package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.RocketLevelV2Data;
import com.quhong.data.RocketLevelV2PoolStateData;
import com.quhong.mongo.data.RocketRewardConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/12/17
 */
@Lazy
@Component
public class RoomRocketV2Redis {

    private static final Logger logger = LoggerFactory.getLogger(RoomRocketV2Redis.class);
    private static final String ROOM_ROCKET_V_2 = "RoomRocketV2";


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    protected StringRedisTemplate redisTemplate;


    public void setRocketRank(String day, String roomId, int rocketLevel, String uid, int costBeans, int time) {
        try {
            String key = getRocketRankKey(day, roomId, rocketLevel);
            double rankingScore = getRankingScore(costBeans, time);
            redisTemplate.opsForZSet().add(key, uid, rankingScore);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set room rocket rank error. roomId={} rocketLevel={} uid={} costBeans={} time={} {}", roomId, rocketLevel, uid, costBeans, time, e.getMessage(), e);
        }
    }

    public List<String> getRocketRankingList(String day, String roomId, int rocketLevel, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getRocketRankKey(day, roomId, rocketLevel);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    public Map<String, Integer> getRocketRankingMap(String day, String roomId, int rocketLevel, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getRocketRankKey(day, roomId, rocketLevel);
        Set<ZSetOperations.TypedTuple<java.lang.String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<java.lang.String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public Integer getUserRocketRank(String day, String roomId, int rocketLevel, String uid) {
        try {
            String rankingKey = getRocketRankKey(day, roomId, rocketLevel);
            Long rank = redisTemplate.opsForZSet().reverseRank(rankingKey, uid);
            return rank == null ? 0 : rank.intValue() + 1;
        } catch (Exception e) {
            logger.error("getUserRocketRank error.roomId={} rocketLevel={} uid={} {}", roomId, rocketLevel, uid, e.getMessage(), e);
            return 0;
        }
    }

    public void removeRocketRanking(String day, String roomId, int rocketLevel) {
        try {
            redisTemplate.delete(getRocketRankKey(day, roomId, rocketLevel));
        } catch (Exception e) {
            logger.error("removeRocketRanking error. roomId={} rocketLevel={} {}", roomId, rocketLevel, e.getMessage(), e);
        }
    }

    public int getRocketRankingScore(String day, String roomId, int rocketLevel, String uid) {
        try {
            Double score = redisTemplate.opsForZSet().score(getRocketRankKey(day, roomId, rocketLevel), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRocketRankingScore error. roomId={} rocketLevel={} uid={} {}", roomId, rocketLevel, uid, e.getMessage(), e);
            return 0;
        }
    }


    /**
     * 获取分数
     */
    public int getCommonZSetRankingScore(String activityId, String uid) {
        try {
            Double score = redisTemplate.opsForZSet().score(getCommonZSetKey(activityId), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getCommonZSetRankingScore error activityId={} uid={}, e={}", activityId, uid, e.getMessage(), e);
            return 0;
        }
    }

    public void incrCommonZSetRankingScore(String activityId, String uid, int score) {
        try {
            String key = getCommonZSetKey(activityId);
            int curScore = getCommonZSetRankingScore(activityId, uid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            redisTemplate.opsForZSet().add(key, uid, rankScore);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrCommonZSetRankingScore error activityId={} aid={} giftId={} score={}", activityId, uid, score, e.getMessage(), e);
        }
    }


    /**
     * 获取带分数排行榜
     * 倒序
     */
    public Map<String, Integer> getCommonRankingMap(String activityId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 设置hash key 某个属性的值
     */
    public void setCommonHashData(String hashActivityId, String key, String data) {
        try {
            String nameKey = getCommonHashKey(hashActivityId);
            redisTemplate.opsForHash().put(nameKey, key, data);
            redisTemplate.expire(nameKey, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setCommonHashData error hashActivityId={} e={}", hashActivityId, e);
        }
    }

    /**
     * 增加hash key 某个属性的值
     */
    public int incCommonHashNum(String hashActivityId, String key, int num) {
        try {
            Long afterNum = redisTemplate.opsForHash().increment(getCommonHashKey(hashActivityId), key, num);
            redisTemplate.expire(getCommonHashKey(hashActivityId), ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
            return afterNum.intValue();
        } catch (Exception e) {
            logger.error("incCommonHashNum error hashActivityId={} e={}", hashActivityId, e);
            return num;
        }
    }

    /**
     * 获取hash key 某个属性的值
     */
    public int getCommonHashValue(String hashActivityId, String key) {
        try {
            String valueStr = (String) redisTemplate.opsForHash().get(getCommonHashKey(hashActivityId), key);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getCommonHashValue error hashActivityId={} key={}  e={}", hashActivityId, key, e);
            return 0;
        }
    }


    public String getCommonHashStrValue(String hashActivityId, String key) {
        try {
            return (String) redisTemplate.opsForHash().get(getCommonHashKey(hashActivityId), key);
        } catch (Exception e) {
            logger.info("getCommonHashStrValue error hashActivityId={} key={}  e={}", hashActivityId, key, e);
            return null;
        }
    }

    @Cacheable(value = "getRewardList", key = "T(String).valueOf(#p0).concat('-').concat(#p1).concat('-').concat(#p2).concat('-').concat(#p3)",
            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public List<RocketRewardConfigData.ResourceMeta> getRewardList(String day, String roomId, String aid, int level) {
        List<RocketRewardConfigData.ResourceMeta> result = new ArrayList<>();
        String key = getRewardListKey(day, roomId, aid, level);
        try {
            String json = redisTemplate.opsForValue().get(key);
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, RocketRewardConfigData.ResourceMeta.class);
        } catch (Exception e) {
            logger.error("getRewardList error key={}", key, e);
            return result;
        }
    }

    public void saveRewardList(List<RocketRewardConfigData.ResourceMeta> dataList, String day, String roomId, String aid, int level) {
        String key = getRewardListKey(day, roomId, aid, level);
        try {
            redisTemplate.opsForValue().set(key, JSON.toJSONString(dataList),
                    5, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveRewardList error key={}", key, e);
        }
    }


    private Double getRankingScore(int costBeans, int time) {
        return new BigDecimal((costBeans + "." + (2493043200L - time))).doubleValue();
    }


    private String getRocketRankKey(String dayStr, String roomId, int rocketLevel) {
        return "zset:v2:rocket_rank_" + dayStr + "_" + roomId + "_" + rocketLevel;
    }


    private String getRewardRecordKey(String roomId, int rocketLevel) {
        return "set:v2:rocket_reward_record_" + DateHelper.ARABIAN.formatDateInDay() + "_" + roomId + "_" + rocketLevel;
    }

    private String getCommonHashKey(String hashActivityId) {
        return String.format("hash:v2:%s", hashActivityId);
    }

    private String getCommonZSetKey(String activityId) {
        return String.format("zset:v2:%s", activityId);
    }

    public String getHashDetailKey(String dayStr) {
        return ROOM_ROCKET_V_2 + ":" + dayStr;
    }

    public String getHashDetailTotalKey(String dayStr) {
        return ROOM_ROCKET_V_2 + ":total:" + dayStr;
    }

    public String getZSetTotalKey(String dayStr, String roomId) {
        return ROOM_ROCKET_V_2 + ":aid:total:" + dayStr + ":" + roomId + "";
    }

    public String getHashLevelKey(String dayStr) {
        return ROOM_ROCKET_V_2 + ":rocketLevel:" + dayStr;
    }

    private String getRewardListKey(String day, String roomId, String aid, int level) {
        return String.format("str:rocket:v2:reward:day%s:roomId%s:aid%s:level%s", day, roomId, aid, level);
    }

    private String getPoolStateKey(String day, String roomId, int level) {
        return String.format("str:rocket:v2:pool:state:day%s:roomId%s:level%s", day, roomId, level);
    }

    public RocketLevelV2Data getRocketLevelV2Data(String day, String roomId) {
        RocketLevelV2Data rocketLevelV2Data = new RocketLevelV2Data();
        String jsonValue = getCommonHashStrValue(getHashLevelKey(day), roomId);
        if (StringUtils.hasLength(jsonValue)) {
            if (jsonValue.length() == 1) {
                rocketLevelV2Data.setLevel(Integer.parseInt(jsonValue));
            } else {
                rocketLevelV2Data = JSONObject.parseObject(jsonValue, RocketLevelV2Data.class);
            }
        }
        return rocketLevelV2Data;
    }

    public void setRocketLevelV2Data(String day, String roomId, RocketLevelV2Data rocketLevelV2Data) {
        setCommonHashData(getHashLevelKey(day), roomId,
                JSONObject.toJSONString(rocketLevelV2Data));
    }


    public void savePoolState(String day, String roomId, int level, RocketLevelV2PoolStateData data) {
        String key = getPoolStateKey(day, roomId, level);
        try {
            redisTemplate.opsForValue().set(key, JSONObject.toJSONString(data),
                    5, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveRewardList error key={}", key, e);
        }
    }

    public RocketLevelV2PoolStateData getPoolState(String day, String roomId, int level) {
        String key = getPoolStateKey(day, roomId, level);
        try {
            String json = redisTemplate.opsForValue().get(key);
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            if (StringUtils.hasLength(json)) {
                return JSONObject.parseObject(json, RocketLevelV2PoolStateData.class);
            }
        } catch (Exception e) {
            logger.error("getPoolState error key={}", key, e);
            return null;
        }
        return null;
    }
}
