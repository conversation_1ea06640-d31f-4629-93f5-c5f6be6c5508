package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.BackUserStateData;
import com.quhong.data.InviterUserStateData;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class BackUserStateRedis {
    private final static Logger logger = LoggerFactory.getLogger(BackUserStateRedis.class);
    public static final int BACK_DAY = 15;
    public static final int BACK_DAY_TIME = BACK_DAY * 86400;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public void saveBackUserState(BackUserStateData data, String uid) {
        try {
            String json = JSON.toJSONString(data);
            if (!StringUtils.isEmpty(json)) {
                String key = getBackKey(uid);
                clusterRedis.opsForValue().set(key, json);
                clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.error("saveBackUserState error msg={} ", e.getMessage(), e);
        }
    }


    public BackUserStateData getBackUserState(String uid) {
        try {
            String json = clusterRedis.opsForValue().get(getBackKey(uid));
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseObject(json, BackUserStateData.class);
        } catch (Exception e) {
            logger.error("getBackUserState error msg={} ", e.getMessage(), e);
            return null;
        }
    }


    public void saveInvitedUserState(BackUserStateData data, String uid) {
        try {
            String json = JSON.toJSONString(data);
            if (!StringUtils.isEmpty(json)) {
                String key = getInvitedKey(uid);
                clusterRedis.opsForValue().set(key, json);
                clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_45, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.error("saveBackUserState error msg={} ", e.getMessage(), e);
        }
    }


    public BackUserStateData getInvitedUserState(String uid) {
        try {
            String json = clusterRedis.opsForValue().get(getInvitedKey(uid));
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseObject(json, BackUserStateData.class);
        } catch (Exception e) {
            logger.error("getBackUserState error msg={} ", e.getMessage(), e);
            return null;
        }
    }


    public void saveInviterUserState(InviterUserStateData data, String uid, String aid) {
        try {
            String json = JSON.toJSONString(data);
            if (!StringUtils.isEmpty(json)) {
                String key = getInviterKey(uid, aid);
                clusterRedis.opsForValue().set(key, json);
                clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_45, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.error("saveInviterUserState error msg={} ", e.getMessage(), e);
        }
    }


    public InviterUserStateData getInviterUserState(String uid, String aid) {
        try {
            String json = clusterRedis.opsForValue().get(getInviterKey(uid, aid));
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseObject(json, InviterUserStateData.class);
        } catch (Exception e) {
            logger.error("getInviterUserState error msg={} ", e.getMessage(), e);
            return null;
        }
    }


    private String getBackKey(String uid) {
        return "str:back:user:state:" + uid;
    }

    private String getInvitedKey(String uid) {
        return "str:invited:user:state:" + uid;
    }

    private String getInviterKey(String uid, String aid) {
        return "str:inviter:user:state:" + uid + ":" + aid;
    }

    public BackUserStateData checkBackUser(ActorData actorData, boolean checkDay) {
        boolean isReg = isReg(actorData.getUid());
        if (!isReg) {
            int lastLogoutTime = isBackUser(actorData, false);
            BackUserStateData data = getBackUserState(actorData.getUid());
            if (data == null) {
                if (lastLogoutTime > 0) {
                    data = new BackUserStateData();
                    data.setUid(actorData.getUid());
                    data.setBackTime(DateHelper.getNowSeconds());
                    data.setDau2State(0);
                    data.setDau3State(0);
                    data.setDau5State(0);
                    data.setDau7State(0);
                    Set<String> daySet = new HashSet<>();
                    String dayStr = DateHelper.ARABIAN.formatDateInDay();
                    daySet.add(dayStr);
                    data.setDayActiveSet(daySet);
                    data.setLastLoginOutTime(lastLogoutTime);
                    saveBackUserState(data, data.getUid());
                    // 第一次记录让客户端弹窗
                    return data;
                }

            } else {
                int backTime = data.getBackTime();
                if (backTime + ResourceConstant.BACK_AC_DAY_TIME >= DateHelper.getNowSeconds()) {
                    // 在活动期内
                    if (checkDay) {
                        String dayStr = DateHelper.ARABIAN.formatDateInDay();
                        Set<String> daySet = data.getDayActiveSet();
                        daySet.add(dayStr);
                        int nowDau = daySet.size();
                        data.setDau2State(changeBackUserState(data.getDau2State(), nowDau, 2));
                        data.setDau3State(changeBackUserState(data.getDau3State(), nowDau, 3));
                        data.setDau5State(changeBackUserState(data.getDau5State(), nowDau, 5));
                        data.setDau7State(changeBackUserState(data.getDau7State(), nowDau, 7));
                        data.setDayActiveSet(daySet);
                        saveBackUserState(data, data.getUid());
                    }
                }
                return data;
            }
        }
        return null;
    }

    private int changeBackUserState(int oldState, int nowDau, int destDau) {
        if (oldState == 0) {
            if (nowDau >= destDau) {
                return 1;
            }
            return 0;
        } else {
            return oldState;
        }
    }

    public int isBackUser(ActorData actorData, boolean isCheckRedis) {
        return isBackUser(actorData, isCheckRedis, true);
    }

    /**
     * @param actorData
     * @param isCheckRedis
     * @param isAc
     * @return 大于0为有流失时间，为回流用户
     */
    public int isBackUser(ActorData actorData, boolean isCheckRedis, boolean isAc) {
        int lastLogoutTime = 0;
        if (actorData.getLastLogin() != null) {
            if (actorData.getLastLogin().getLogoutTime() != null) {
                lastLogoutTime = Math.toIntExact(actorData.getLastLogin().getLogoutTime());
            }
        }
        if (lastLogoutTime == 0) {
            logger.info("uid:{} not find lastLogoutTime", actorData.getUid());
            return 0;
        }
        int now = DateHelper.getNowSeconds();
        if (lastLogoutTime + BackUserStateRedis.BACK_DAY_TIME < now) {
            if (isCheckRedis) {
                BackUserStateData backUserStateData = getBackUserState(actorData.getUid());
                if (backUserStateData != null) {
                    return 0; // 完成回流活动后，再次流失15天以上
                }
            }
            return lastLogoutTime;
        } else {
            if (isCheckRedis) {
                BackUserStateData backUserStateData = getBackUserState(actorData.getUid());
                if (isAc) {
                    if (backUserStateData != null && backUserStateData.getBackTime() + ResourceConstant.BACK_AC_DAY_TIME >= now) {
                        // 回流用户在7天活动期内,展示banner入口
                        return backUserStateData.getLastLoginOutTime();
                    }
                } else {
                    if (backUserStateData != null) {
                        //非活动场景有数据就返回
                        return backUserStateData.getLastLoginOutTime();
                    }
                }
            }
            return 0;
        }
    }

    public boolean isReg(String uid) {
        return ActorUtils.isNewRegisterActor(uid, BackUserStateRedis.BACK_DAY);
    }

    /**
     * 获取被邀请者领奖数据
     *
     * @param uid
     * @return
     */
    public BackUserStateData getInvitedUserData(String uid) {
        BackUserStateData invitedUserData = getInvitedUserState(uid);
        if (invitedUserData != null) {
            if (invitedUserData.getDayActiveSet() == null) {
                invitedUserData.setDayActiveSet(new HashSet<>());
            }
            if (invitedUserData.getGiftMapStatus() == null) {
                invitedUserData.setGiftMapStatus(new HashMap<>());
            }
            if (invitedUserData.getDollarsMapStatus() == null) {
                invitedUserData.setDollarsMapStatus(new HashMap<>());
            }
            if (invitedUserData.getDayNewActiveSet() == null) {
                invitedUserData.setDayNewActiveSet(new HashSet<>());
            }
        } else {
            invitedUserData = new BackUserStateData();
            invitedUserData.setUid(uid);
            invitedUserData.setRegStatus(1);
            invitedUserData.setDau2State(0);
            invitedUserData.setDau3State(0);
            invitedUserData.setDau7State(0);
            invitedUserData.setDayNewActiveSet(new HashSet<>());
            invitedUserData.setDayActiveSet(new HashSet<>());
            invitedUserData.setGiftMapStatus(new HashMap<>());
            invitedUserData.setDollarsMapStatus(new HashMap<>());
        }
        return invitedUserData;
    }

    /**
     * 获取邀请者领奖数据
     *
     * @param uid
     * @param aid
     * @return
     */
    public InviterUserStateData getInvitedUserData(String uid, String aid) {
        InviterUserStateData yaoQingUserdata = getInviterUserState(uid, aid); // 邀请者领取数据
        if (yaoQingUserdata != null) {
        } else {
            yaoQingUserdata = new InviterUserStateData();
            yaoQingUserdata.setUid(uid);
            yaoQingUserdata.setAid(aid);
            yaoQingUserdata.setDayMapStatus(new HashMap<>());
            yaoQingUserdata.setGiftMapStatus(new HashMap<>());
            yaoQingUserdata.setWeekTimeList(new ArrayList<>());
            yaoQingUserdata.setWeekBeansList(new ArrayList<>());
            yaoQingUserdata.setWeekStatusList(new ArrayList<>());
            saveInviterUserState(yaoQingUserdata, uid, aid);
        }
        return yaoQingUserdata;
    }
}
