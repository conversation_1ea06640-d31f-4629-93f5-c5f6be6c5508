/*
package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.data.dto.PyBroadcastDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Lazy
@Service
public class RedisTaskService {
    protected static final Logger logger = LoggerFactory.getLogger(RedisTaskService.class);

    @Resource(name = DataRedisBean.SUB_REDIS)
    protected StringRedisTemplate subRedis;
    @Resource(name = DataRedisBean.QUEUE_REDIS)
    protected StringRedisTemplate queueRedis;

    public void pushQueue(String name, Object data) {
        try {
            queueRedis.opsForList().leftPush(getTaskQueueKey(name), JSON.toJSONString(data));
        } catch (Exception e) {
            logger.error("push task to queue error, name={} data={} {}", name, JSON.toJSONString(data), e.getMessage());
        }
    }

    public void broadcastMessage(String name, Object data) {
        try {
            subRedis.convertAndSend(getChannelKey(name), JSON.toJSONString(new PyBroadcastDTO(data)));
        } catch (Exception e) {
            logger.error("push task to channel error, name={} data={} {}", name, JSON.toJSONString(data), e.getMessage());
        }
    }

    private String getTaskQueueKey(String name) {
        return String.format("task_queue:%s", name);
    }

    private String getChannelKey(String name) {
        return String.format("message:%s", name);
    }
}
*/
