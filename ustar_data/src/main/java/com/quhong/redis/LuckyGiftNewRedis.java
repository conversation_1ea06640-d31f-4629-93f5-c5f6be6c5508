package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class LuckyGiftNewRedis {
    private static final Logger logger = LoggerFactory.getLogger(LuckyGiftNewRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String GAME_KEY = "lucky_gift_prize";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    private String poolSizeKey(int giftId) {
        return String.format("list:pool_size:%s:%s", GAME_KEY, giftId);
    }



    public int getPoolSize(int giftId) {
        try {
            Long poolSize = clusterRedis.opsForList().size(poolSizeKey(giftId));
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("getPoolSize error score={}", e.getMessage());
            return 0;
        }
    }

    public void deletePoolSize(int giftId) {
        try {
            clusterRedis.delete(poolSizeKey(giftId));
        } catch (Exception e) {
            logger.error("deletePoolSize error={}", e.getMessage());
        }
    }



    public void initPoolSize(int giftId, List<String> rewardConfigList) {
        try {
            clusterRedis.opsForList().rightPushAll(poolSizeKey(giftId), rewardConfigList);
            clusterRedis.expire(poolSizeKey(giftId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("initPoolSize error clusterTemplate={}  score={}", clusterRedis, e);
        }
    }


    // 抽奖
    public String drawLuckyGiftKey(int giftId) {
        try {
            String prizeKey = clusterRedis.opsForList().leftPop(poolSizeKey(giftId));
            return prizeKey != null ? prizeKey : "";
        } catch (Exception e) {
            logger.error("drawLuckyGiftKey error e={}", e.getMessage());
            return "";
        }
    }

}
