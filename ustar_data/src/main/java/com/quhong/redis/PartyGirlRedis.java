package com.quhong.redis;

import com.quhong.cache.CacheMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

@Component
@Lazy
public class PartyGirlRedis {
    private static final Logger logger = LoggerFactory.getLogger(PartyGirlRedis.class);
    private static final long CACHE_TIME_MILLIS = 60 * 60 * 1000L;
    private static final int ALL = 1;
    private final CacheMap<Integer, Set<String>> cacheMap;

    public PartyGirlRedis() {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public Set<String> getPartyGirl(Set<String> filterUidSet) {
        Set<String> partyGirls = getAllPartyGirl();
        if (CollectionUtils.isEmpty(filterUidSet)) {
            return null;
        }
        Set<String> uidSet = new HashSet<>();
        for (String uid : partyGirls) {
            if (filterUidSet.contains(uid)) {
                uidSet.add(uid);
            }
        }
        return uidSet;
    }

    public Set<String> getAllPartyGirlByRedis() {
        Set<String> uidSet = redisTemplate.opsForSet().members(getKey());
        if (!CollectionUtils.isEmpty(uidSet)) {
            return uidSet;
        }
        return new HashSet<>();
    }

    public void removePartyGirl(String uid, String tnId) {
        try {
            redisTemplate.opsForSet().remove(getKey(), uid);
            if (!StringUtils.isEmpty(tnId)) {
                redisTemplate.opsForSet().remove(getPtgTnIdKey(), tnId);
            }
        } catch (Exception e) {
            logger.error("remove party girl from redis error. uid={} tnId={} {}", uid, tnId, e.getMessage(), e);
        }
    }

    public boolean isPartyGirlByRedis(String uid) {
        return getAllPartyGirl().contains(uid);
    }

    public Set<String> getAllPartyGirl() {
        try {
            Set<String> partyGirlMap = cacheMap.getData(ALL);
            if (!CollectionUtils.isEmpty(partyGirlMap)) {
                return partyGirlMap;
            }
            partyGirlMap = redisTemplate.opsForSet().members(getKey());
            if (!CollectionUtils.isEmpty(partyGirlMap)) {
                cacheMap.cacheData(ALL, partyGirlMap);
                return partyGirlMap;
            }
        } catch (Exception e) {
            logger.error("get all party girl error.", e);
        }
        return new HashSet<>();
    }

    public boolean isPartyGirl(String uid) {
        try {
            return getAllPartyGirl().contains(uid);
        } catch (Exception e) {
            logger.error("get is party girl error.", e);
            return false;
        }
    }

    public int getPtgSendMsgCount(String uid, String aid) {
        try {
            Double score = redisTemplate.opsForZSet().score(getPtgSendMsgKey(), uid + "-" + aid);
            return null == score ? 0 : score.intValue();
        } catch (Exception e) {
            logger.info("get party girl send msg count error msg={}", e.getMessage());
            return 0;
        }
    }

    private String getPtgSendMsgKey() {
        return "ptg_flt_msg";
    }

    private String getKey() {
        return "n_ptg_set_key";
    }

    private String getPtgTnIdKey() {
        return "n_ptg_set_key_tn_id";
    }

}
