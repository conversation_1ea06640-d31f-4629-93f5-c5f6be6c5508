package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class DauUserRedis {
    private static final Logger logger = LoggerFactory.getLogger(DauUserRedis.class);
    private static final int EXPIRE_DAY = 30;
    private String expireDate = "";

    private static final int NEW_EXPIRE_DAY = 2;
    private String newExpireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public boolean hasLog(String uid, int beans) {
        try {
            String dateStr = DateHelper.DEFAULT.formatDateInDay();
            String key = getPlayerSendKey(dateStr);
            // redis中没有数据时才返回true
            boolean putSuccess = redisTemplate.opsForHash().putIfAbsent(key, uid, String.valueOf(beans));
            if (!dateStr.equals(expireDate)) {
                redisTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
                expireDate = dateStr;
            }
            return !putSuccess;
        } catch (Exception e) {
            logger.error("get is log error. uid={}", uid, e);
            return false;
        }
    }

    public boolean hasNewLog(String uid) {
        try {
            String dateStr = DateHelper.DEFAULT.formatDateInDay();
            String key = getPlayerSendNewKey(dateStr);
            // redis中没有数据时才返回true
            boolean putSuccess = redisTemplate.opsForHash().putIfAbsent(key, uid, "ok");
            if (!dateStr.equals(newExpireDate)) {
                redisTemplate.expire(key, NEW_EXPIRE_DAY, TimeUnit.DAYS);
                newExpireDate = dateStr;
            }
            return !putSuccess;
        } catch (Exception e) {
            logger.error("get is log error. uid={}", uid, e);
            return false;
        }
    }


    public Set<String> getAllDauUserByDay(String dateStr) {
        Set<String> result = new HashSet<>();
        try {
            String key = getPlayerSendKey(dateStr);
            Set<Object> fields = redisTemplate.opsForHash().keys(key);
            for (Object field : fields) {
                result.add(String.valueOf(field));
            }
            return result;
        } catch (Exception e) {
            logger.error("getAllDauUserByDay error. dateStr={}", dateStr, e);
            return result;
        }
    }

    public Map<String, String> getAllDauUserBeansByDay(String dateStr) {
        Map<String, String> hashMap = new HashMap<>();
        try {
            String key = getPlayerSendKey(dateStr);
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", String.valueOf(entry.getValue()));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getAllDauUserBeansByDay error dateStr={} e={}", dateStr, e.getMessage(), e);
        }
        return hashMap;
    }

    public boolean hasLogByDate(String uid, String dateStr) {
        try {
            String key = getPlayerSendKey(dateStr);
            return redisTemplate.opsForHash().hasKey(key, uid);
        } catch (Exception e) {
            logger.error("get is log error. uid={}", uid, e);
            return false;
        }
    }

    private String getPlayerSendKey(String dateStr) {
        return "dau_user_" + dateStr;
    }

    private String getPlayerSendNewKey(String dateStr) {
        return "hash:new:dau_user_" + dateStr;
    }
}
