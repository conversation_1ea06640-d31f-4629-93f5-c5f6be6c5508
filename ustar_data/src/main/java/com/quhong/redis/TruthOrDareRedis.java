package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.TruthOrDareInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/1/31
 */
@Component
@Lazy
public class TruthOrDareRedis {

    private static final Logger logger = LoggerFactory.getLogger(TruthOrDareRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 获取转盘游戏信息
     */
    public TruthOrDareInfo getGameInfo(String gameId) {
        try {
            String strValue = redisTemplate.opsForValue().get(getTruthDareGameKey(gameId));
            if (StringUtils.isEmpty(strValue)) {
                return null;
            }
            return JSONObject.parseObject(strValue, TruthOrDareInfo.class);
        } catch (Exception e) {
            logger.error("get turntable game info error. gameId={} {}", gameId, e.getMessage(), e);
            return null;
        }
    }

    public TruthOrDareInfo getGameInfoByRoomId(String roomId) {
        String gameId = getGameIdFromRedis(roomId);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return getGameInfo(gameId);
    }

    /**
     * 设置转盘游戏信息
     */
    public void saveGameInfo(TruthOrDareInfo gameInfo) {
        String key = getTruthDareGameKey(gameInfo.get_id());
        try {
            redisTemplate.opsForValue().set(key, JSONObject.toJSONString(gameInfo));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save turntable game info error. gameId={} {}", gameInfo.get_id(), e.getMessage(), e);
        }
    }

    /**
     * 获取所有转盘游戏信息
     */
    public List<TruthOrDareInfo> getAllTurntableGame() {
        List<TruthOrDareInfo> gameInfoList = new ArrayList<>();
        try {
            List<Object> list = redisTemplate.opsForHash().values(getRoomTruthDareKey());
            if (!CollectionUtils.isEmpty(list)) {
                for (Object object : list) {
                    TruthOrDareInfo gameInfo = getGameInfo((String) object);
                    if (gameInfo != null) {
                        gameInfoList.add(gameInfo);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("getAllTurntableGame error. {}", e.getMessage(), e);
        }
        return gameInfoList;
    }

    public void setGameIdInRedis(String roomId, String gameId) {
        String key = getRoomTruthDareKey();
        try {
            redisTemplate.opsForHash().put(key, roomId, gameId);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set turntable gameId in redis error. roomId={} gameId={} {}", roomId, gameId, e.getMessage(), e);
        }
    }

    public String getGameIdFromRedis(String roomId) {
        try {
            return (String) redisTemplate.opsForHash().get(getRoomTruthDareKey(), roomId);
        } catch (Exception e) {
            logger.error("get turntable gameId from redis error. roomId={} {}", roomId, e.getMessage(), e);
            return "";
        }
    }

    public void removeGameId(String roomId) {
        try {
            redisTemplate.opsForHash().delete(getRoomTruthDareKey(), roomId);
        } catch (Exception e) {
            logger.error("remove turntable game id error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public void setGameTimerRunning(String gameId, int endTime) {
        String key = getGameTimerRunningKey();
        try {
            redisTemplate.opsForZSet().add(key, gameId, endTime);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set game timer running error. gameId={} endTime={} {}", gameId, endTime, e.getMessage(), e);
        }
    }

    public Set<String> getRunningEndGameIds(int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getGameTimerRunningKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get running end game ids error. timestamp={} {}", timestamp, e.getMessage());
            return new HashSet<>();
        }
    }

    public void removeGameTimerRunning(String gameId) {
        try {
            redisTemplate.opsForZSet().remove(getGameTimerRunningKey(), gameId);
        } catch (Exception e) {
            logger.error("remove game timer running error. gameId={} {}", gameId, e.getMessage(), e);
        }
    }

    public void setGameTimerWaiting(String gameId, int endTime) {
        String key = getGameTimerWaitingKey();
        try {
            redisTemplate.opsForZSet().add(key, gameId, endTime);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set game timer waiting error. gameId={} endTime={} {}", gameId, endTime, e.getMessage(), e);
        }
    }

    public Set<String> getWaitingEndGameIds(int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getGameTimerWaitingKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get waiting end game ids error. timestamp={} {}", timestamp, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    public void removeGameTimerWaiting(String gameId) {
        try {
            redisTemplate.opsForZSet().remove(getGameTimerWaitingKey(), gameId);
        } catch (Exception e) {
            logger.error("remove game timer waiting error. gameId={} {}", gameId, e.getMessage(), e);
        }
    }

    private String getRoomTruthDareKey() {
        return "hash:truth_dare_turntable_game_key";
    }

    private String getTruthDareGameKey(String gameId) {
        return "str:truth_dare_turntable_game_" + gameId;
    }

    private String getGameTimerRunningKey() { return "zset:truth_dare_turntable_game_timer1"; }

    private String getGameTimerWaitingKey() { return "zset:truth_dare_turntable_game_timer2"; }
}
