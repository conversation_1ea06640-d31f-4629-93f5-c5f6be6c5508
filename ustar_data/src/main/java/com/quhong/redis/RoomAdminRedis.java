package com.quhong.redis;

import com.quhong.cache.CacheMap;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.RoomMemberData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/7/30
 */
@Lazy
@Component
public class RoomAdminRedis {

    private static final Logger logger = LoggerFactory.getLogger(RoomAdminRedis.class);

    private static final long CACHE_TIME_MILLIS = 25 * 60 * 60 * 1000L;
    //    @Resource(name = DataRedisBean.ROOM_ADMIN)
//    protected StringRedisTemplate redisTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;
    @Resource
    private RoomMemberDao roomMemberDao;

    private final CacheMap<String, String> cacheMap;

    public RoomAdminRedis() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }


    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public void addRoomAdmin(String roomId, String uid) {
        try {
//            redisTemplate.opsForSet().add(roomId, uid);
            String roomKey = getRoomKey(roomId);
            mainRedisTemplate.opsForSet().add(roomKey, uid);
        } catch (Exception e) {
            logger.error("add room admin to redis error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public boolean removeRoomAdmin(String roomId, String uid) {
        try {
//            redisTemplate.opsForSet().remove(roomId, uid);
            String roomKey = getRoomKey(roomId);
            Long remove = mainRedisTemplate.opsForSet().remove(roomKey, uid);
            return null != remove && remove > 0;
        } catch (Exception e) {
            logger.error("remove room admin to redis error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
            return false;
        }
    }

    public boolean isRoomAdmin(String roomId, String uid) {
        try {
            String roomKey = getRoomKey(roomId);
            Set<String> members = mainRedisTemplate.opsForSet().members(roomKey);
            if (CollectionUtils.isEmpty(members)) {
                members = reBuildCache(roomId);
            } else {
                String oldDay = cacheMap.getData(roomId);
                String nowDay = DateHelper.ARABIAN.formatDateInDay();
                if (!nowDay.equals(oldDay)) {
                    mainRedisTemplate.expire(roomKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
                    cacheMap.cacheData(roomId, nowDay);
                }
            }
            members.removeIf(aid -> aid.length() == 0);
            boolean isMember = members.contains(uid);
//            logger.info("isRoomAdmin roomId:{} uid:{} all_admin:{} isMember:{}", roomId, uid, members, isMember);
            return isMember;
//            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(roomId, uid));
        } catch (Exception e) {
            logger.error("get room admin data error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return false;
    }


    private Set<String> reBuildCache(String roomId) {
        Set<String> roomAdmin = new HashSet<>();
        List<RoomMemberData> roomMemberList = roomMemberDao.findAdminList(roomId);
        for (RoomMemberData data : roomMemberList) {
            roomAdmin.add(data.getAid());
        }
        String roomKey = getRoomKey(roomId);
        String[] toArray = roomAdmin.toArray(new String[0]);
        if (toArray.length == 0) {
            mainRedisTemplate.opsForSet().add(roomKey, "");
        } else {
            mainRedisTemplate.opsForSet().add(roomKey, toArray);
        }
        mainRedisTemplate.expire(roomKey, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        logger.info("reBuild room admin set cache roomId={} size={}", roomId, roomAdmin.size());
        return roomAdmin;
    }

    private String getRoomKey(String roomId) {
        return "set:room:admin:" + roomId;
    }
}
