package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 支付缓存redis
 * <AUTHOR>
 * @date 2025/7/4 11:10
 */
@Lazy
@Component
public class PayRedis {
    Logger logger = LoggerFactory.getLogger(PayRedis.class);

    // 商品折扣缓存key
    public static final String ONLINE_DISCOUNT_KEY = "str:onlinePayBounds:New";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 获取商品折扣配置
     */
    public String getDiscountConfig() {
        try {
            return clusterTemplate.opsForValue().get(ONLINE_DISCOUNT_KEY);
        } catch (Exception e) {
            logger.error("get discount error e={}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 配置商品折扣
     */
    public void setDiscountConfig(String discountConfig) {
        try {
            clusterTemplate.opsForValue().set(ONLINE_DISCOUNT_KEY, discountConfig);
        } catch (Exception e) {
            logger.error("setDiscountConfig error e={}", e.getMessage(), e);
        }
    }


    /**
     * 获取商品折扣
     */
    public float getDiscount() {
        try {
            String value = clusterTemplate.opsForValue().get(ONLINE_DISCOUNT_KEY);
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            String[] timeArr = value.split("-");
            int startTime = Integer.parseInt(timeArr[0]);
            int endTime = Integer.parseInt(timeArr[1]);
            float discount = Float.parseFloat(timeArr[2]);
            int status = Integer.parseInt(timeArr[3]);
            if (status == 0) {
                return 0;
            }

            int nowSeconds = DateHelper.getNowSeconds();
            if (nowSeconds < startTime || nowSeconds > endTime || discount > 1) {
                return 0;
            }
            return discount;
        } catch (Exception e) {
            logger.error("get discount error. onlinePayBounds={}", e.getMessage(), e);
            return 0;
        }
    }
}
