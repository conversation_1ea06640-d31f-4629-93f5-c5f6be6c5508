package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/12/17
 */
@Lazy
@Component
public class RoomRocketRedis {

    private static final Logger logger = LoggerFactory.getLogger(RoomRocketRedis.class);


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    protected StringRedisTemplate redisTemplate;


    public void setRoomRocketLevel(String roomId, int rocketLevel) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getRocketLevelKey(roomId);
            redisTemplate.opsForHash().put(key, dateStr, String.valueOf(rocketLevel));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set room rocket level error. roomId={}, rocketLevel={} {}", roomId, rocketLevel, e.getMessage(), e);
        }
    }

    public int getRoomRocketLevel(String roomId) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String value = (String) redisTemplate.opsForHash().get(getRocketLevelKey(roomId), dateStr);
            if (StringUtils.isEmpty(value)) {
                return 1;
            }
            return Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("get room rocket level error. roomId={} {}", roomId, e.getMessage(), e);
            return 1;
        }
    }

    public void setRocketRewardEndTime(String roomId, int rocketLevel, int endTime) {
        try {
            String key = getRewardEndTimeKey(roomId);
            redisTemplate.opsForHash().put(key, String.valueOf(rocketLevel), String.valueOf(endTime));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set rocket reward end time error. roomId={}, rocketLevel={} {}", roomId, endTime, e.getMessage(), e);
        }
    }

    public Map<Integer, Integer> getAllRewardEndTime(String roomId) {
        Map<Integer, Integer> resultMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getRewardEndTimeKey(roomId));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put(Integer.parseInt((String) entry.getKey()), Integer.parseInt((String) entry.getValue()));
            }
            return resultMap;
        } catch (Exception e) {
            logger.info("getAllRewardEndTime error. roomId={} {}", roomId, e.getMessage(), e);
            return resultMap;
        }
    }

    public void addRocketEnergy(String roomId, int rocketLevel, int energyValue) {
        try {
            String key = getRocketEnergyKey(roomId);
            redisTemplate.opsForHash().increment(key, String.valueOf(rocketLevel), energyValue);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add room rocket energy error. roomId={}, rocketLevel={}, energyValue={} {}", roomId, rocketLevel, energyValue, e.getMessage(), e);
        }
    }

    public void removeRocketEnergy(String roomId, int rocketLevel) {
        try {
            redisTemplate.opsForHash().delete(roomId, String.valueOf(rocketLevel));
        } catch (Exception e) {
            logger.error("remove room rocket energy error. roomId={}, rocketLevel={} {}", roomId, rocketLevel, e.getMessage(), e);
        }
    }

    public int getRocketEnergy(String roomId, int rocketLevel) {
        try {
            String value = (String) redisTemplate.opsForHash().get(getRocketEnergyKey(roomId), String.valueOf(rocketLevel));
            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("get room rocket energy error. roomId={}, rocketLevel={} {}", roomId, rocketLevel, e.getMessage(), e);
            return 0;
        }
    }

    public void setRocketRank(String roomId, int rocketLevel, String uid, int costBeans, int time) {
        try {
            String key = getRocketRankKey(roomId, rocketLevel);
            double rankingScore = getRankingScore(costBeans, time);
            redisTemplate.opsForZSet().add(key, uid, rankingScore);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set room rocket rank error. roomId={} rocketLevel={} uid={} costBeans={} time={} {}", roomId, rocketLevel, uid, costBeans, time, e.getMessage(), e);
        }
    }

    public List<String> getRocketRankingList(String roomId, int rocketLevel) {
        List<String> rankingList = new ArrayList<>();
        String key = getRocketRankKey(roomId, rocketLevel);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, 3 - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    public Integer getUserRocketRank(String roomId, int rocketLevel, String uid) {
        try {
            String rankingKey = getRocketRankKey(roomId, rocketLevel);
            Long rank = redisTemplate.opsForZSet().reverseRank(rankingKey, uid);
            return rank == null ? 0 : rank.intValue() + 1;
        } catch (Exception e) {
            logger.error("getUserRocketRank error.roomId={} rocketLevel={} uid={} {}", roomId, rocketLevel, uid, e.getMessage(), e);
            return 0;
        }
    }

    public void removeRocketRanking(String roomId, int rocketLevel) {
        try {
            redisTemplate.delete(getRocketRankKey(roomId, rocketLevel));
        } catch (Exception e) {
            logger.error("removeRocketRanking error. roomId={} rocketLevel={} {}", roomId, rocketLevel, e.getMessage(), e);
        }
    }

    public int getRocketRankingScore(String roomId, int rocketLevel, String uid) {
        try {
            Double score = redisTemplate.opsForZSet().score(getRocketRankKey(roomId, rocketLevel), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRocketRankingScore error. roomId={} rocketLevel={} uid={} {}", roomId, rocketLevel, uid, e.getMessage(), e);
            return 0;
        }
    }

    public void addRocketRewardRecord(String roomId, int rocketLevel, String uid) {
        try {
            String rewardRecordKey = getRewardRecordKey(roomId, rocketLevel);
            redisTemplate.opsForSet().add(rewardRecordKey, uid);
            redisTemplate.expire(rewardRecordKey, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addRocketRewardRecord error. roomId={} rocketLevel={} uid={} {}", roomId, rocketLevel, uid, e.getMessage(), e);
        }
    }

    public void removeRocketRewardRecord(String roomId, int rocketLevel) {
        try {
            redisTemplate.delete(getRewardRecordKey(roomId, rocketLevel));
        } catch (Exception e) {
            logger.error("removeRocketRewardRecord error. roomId={} rocketLevel={} {}", roomId, rocketLevel, e.getMessage(), e);
        }
    }

    public boolean hasGainedReward(String roomId, int rocketLevel, String uid) {
        try {
            Boolean result = redisTemplate.opsForSet().isMember(getRewardRecordKey(roomId, rocketLevel), uid);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            logger.error("getRocketRankingScore error. roomId={} rocketLevel={} uid={} {}", roomId, rocketLevel, uid, e.getMessage(), e);
            return true;
        }
    }

    /**
     * 将后台火箭等级换成客户端显示的火箭等级
     */
    public int getShowRocketLevel(int rocketLevel) {
        int level = rocketLevel % 3;
        return level == 0 ? 3 : level;
    }

    private Double getRankingScore(int costBeans, int time) {
        return new BigDecimal((costBeans + "." + (2493043200L - time))).doubleValue();
    }

    private String getRocketLevelKey(String roomId) {
        return "hash:rocket_level_" + DateHelper.ARABIAN.formatDateInDay() + "_" + roomId;
    }

    private String getRocketEnergyKey(String roomId) {
        return "hash:rocket_energy_" + DateHelper.ARABIAN.formatDateInDay() + "_" + roomId;
    }

    private String getRocketRankKey(String roomId, int rocketLevel) {
        return "zset:rocket_rank_" + DateHelper.ARABIAN.formatDateInDay() + "_" + roomId + "_" + rocketLevel;
    }

    private String getRewardEndTimeKey(String roomId) {
        return "hash:rocket_reward_end_time_" + DateHelper.ARABIAN.formatDateInDay() + "_" + roomId;
    }

    private String getRewardRecordKey(String roomId, int rocketLevel) {
        return "set:rocket_reward_record_" + DateHelper.ARABIAN.formatDateInDay() + "_" + roomId + "_" + rocketLevel;
    }
}
