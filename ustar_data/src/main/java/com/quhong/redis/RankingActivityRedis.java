package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class RankingActivityRedis {
    private static final Logger logger = LoggerFactory.getLogger(RankingActivityRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    @Resource(name = DataRedisBean.ACTIVITY)
    private StringRedisTemplate activityTemplate;
    @Resource
    private ConquerRedis conquerRedis;
    private String rankingExpireDate = "";
    private String reachingExpireDate = "";


    /**
     * 冲榜活动排行榜
     */
    public void incrRankingScore(String activityId, String aid, int giftId, int score, int rankType, int rankGiftId, int rankGender) {
        try {
            String key = getRankingActivityKey(activityId, rankType, rankGiftId, rankGender);
            int curScore = getScore(activityId, aid, rankType, rankGiftId, rankGender);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrRankingScore activityId={} aid={} giftId={} score={} total={} rankType={} rankGiftId={}",
                    activityId, aid, giftId, score, rankScore, rankType, rankGiftId);
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(rankingExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                rankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrRankingScore error activityId={} aid={} giftId={} score={}", activityId, aid, giftId, score, e);
        }
    }

    private String getRankingListKey(String activityId, int rankType, int rankGiftId, String conquerId, int rankGender) {
        String key;
        if (rankType == ActivityConstant.CONQUER_RANK) {
            key = conquerRedis.getConquerRankKey(conquerId);
        } else {
            key = getRankingActivityKey(activityId, rankType, rankGiftId, rankGender);
        }
        return key;
    }

    /**
     * 获取排行榜
     *
     * @param activityId 活动id
     * @param rankType   排行榜类型
     * @param rankGiftId 指定礼物的排行榜
     * @param length     排行榜长度
     */
    public List<String> getRankingList(String activityId, int rankType, int rankGiftId, int length, String conquerId, int rankGender) {
        List<String> rankingList = new ArrayList<>();
        String key = getRankingListKey(activityId, rankType, rankGiftId, conquerId, rankGender);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            logger.info("getRankingByType aid={} score={} rankType={}", rangeWithScore.getValue(), rangeWithScore.getScore().longValue(), rankType);
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动id
     * @param rankType   排行榜类型
     * @param rankGiftId 指定礼物的排行榜
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(String activityId, int rankType, int rankGiftId, int length, String conquerId, int rankGender) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getRankingListKey(activityId, rankType, rankGiftId, conquerId, rankGender);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRank(String activityId, String aid, int rankType, int rankGiftId, int rankGender) {
        try {
            Long rank = clusterTemplate.opsForZSet().reverseRank(getRankingActivityKey(activityId, rankType, rankGiftId, rankGender), aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getRank error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }

    /**
     * 获取分数
     */
    public int getScore(String activityId, String aid, int rankType, int rankGiftId, int rankGender) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getRankingActivityKey(activityId, rankType, rankGiftId, rankGender), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getScore error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }

    private String getRankingActivityKey(String activityId, int rankType, int rankGiftId, int rankGender) {
        return "zset:rankingActivity:" + activityId + ":" + rankType + (0 == rankGiftId ? "" : ":" + rankGiftId) + (0 == rankGender ? "" : ":" + rankGender);
    }


    /**
     * 获取支持者排行榜
     */
    public List<String> getSupportRoomUserRankingList(String activityId, String roomId, int rankGiftId, int rankGender, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getSupportRoomUserKey(activityId, roomId, rankGiftId, rankGender);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 增加支持者分数
     */
    public int incrSupportRoomUserScore(String activityId, String roomId, String uid, int rankGiftId, int rankGender, int score) {
        try {
            String key = getSupportRoomUserKey(activityId, roomId, rankGiftId, rankGender);
            int curScore = getSupportRoomUserScore(activityId, roomId, rankGiftId, rankGender, uid);
            int nowSocre = curScore + score;
            double rankScore = new BigDecimal(nowSocre + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return nowSocre;
        } catch (Exception e) {
            logger.info("incrCommonZSetRankingScore error activityId={} aid={} giftId={} score={}", activityId, uid, score, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取支持者分数
     */
    public int getSupportRoomUserScore(String activityId, String roomId, int rankGiftId, int rankGender, String uid) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getSupportRoomUserKey(activityId, roomId, rankGiftId, rankGender), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getCommonZSetRankingScore error activityId={} uid={}, e={}", activityId, uid, e.getMessage(), e);
            return 0;
        }
    }

    private String getSupportRoomUserKey(String activityId, String roomId, int rankGiftId, int rankGender) {
        return String.format("zset:rankingActivity:supportRoomUser:%s:%s:%s:%s", activityId, roomId, rankGiftId, rankGender);
    }


    public int getReachingScore(String activityId, String uid, int rankType, int calculateMethod) {
        try {
            String key = getReachingActivityKey(activityId, rankType, calculateMethod);
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getReachingScore error activityId={} uid={} rankType={} calculateMethod={}",
                    activityId, uid, rankType, calculateMethod, e);
            return 0;
        }
    }

    public int incrReachingScore(String activityId, String aid, int giftId, int score, int rankType, int calculateMethod) {
        try {
            String key = getReachingActivityKey(activityId, rankType, calculateMethod);
            Double value = clusterTemplate.opsForZSet().incrementScore(key, aid, score);
            logger.info("incrReachingScore activityId={} aid={} giftId={} score={} total={} rankType={} calculateMethod={}",
                    activityId, aid, giftId, score, value, rankType, calculateMethod);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(reachingExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                reachingExpireDate = dateStr;
            }
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrReachingScore error activityId={} aid={} giftId={} score={} calculateMethod={}",
                    activityId, aid, giftId, score, calculateMethod, e);
            return 0;
        }
    }

    private String getReachingActivityKey(String activityId, int rankType, int calculateMethod) {
        return "zset:reachingActivity:" + activityId + ":" + rankType + ":" + calculateMethod;
    }

    public void incrAssociateScore(String activityId, String uid, String aid, int score, int rankType, int rankGiftId) {
        try {
            String key = getRankingAssociateKey(activityId, uid, rankType, rankGiftId);
            Double value = activityTemplate.opsForZSet().incrementScore(key, aid, score);
            logger.info("incrAssociateScore activityId={} uid={} aid={} score={} total={} rankType={} rankGiftId={}",
                    activityId, uid, aid, score, value, rankType, rankGiftId);
            activityTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incrAssociateScore activityId={} uid={} aid={} score={} rankType={} rankGiftId={}",
                    activityId, uid, aid, score, rankType, rankGiftId, e);
        }
    }

    /**
     * 提高写入效率，Jedis不支持集群使用pipeLine，故采用单点Redis
     */
    @SuppressWarnings("all")
    public void incrAssociateSendScore(String activityId, String uid, Set<String> aidSet, int score, int rankType, int rankGiftId) {
        try {
            if (aidSet.isEmpty()) {
                return;
            }
            RedisSerializer<String> keySerializer = (RedisSerializer<String>) activityTemplate.getKeySerializer();
            byte[] key = keySerializer.serialize(getRankingAssociateKey(activityId, uid, rankType, rankGiftId));
            activityTemplate.executePipelined((RedisCallback<Object>) pipeLine -> {
                try {
                    for (String aid : aidSet) {
                        pipeLine.zIncrBy(key, score, keySerializer.serialize(aid));
                        logger.info("incrAssociateSendScore activityId={} uid={} aid={} score={} rankType={} rankGiftId={}",
                                activityId, uid, aid, score, rankType, rankGiftId);
                    }
                    pipeLine.expire(key, TimeUnit.DAYS.toSeconds(180));
                } catch (Exception e) {
                    logger.error("incrAssociateSendScore activityId={} uid={} score={} rankType={} rankGiftId={}",
                            activityId, uid, score, rankType, rankGiftId, e);
                }
                return null;
            });
        } catch (Exception e) {
            logger.error("incrAssociateSendScore activityId={} uid={} aid={} score={} rankType={} rankGiftId={}",
                    activityId, uid, aidSet, score, rankType, rankGiftId, e);
        }
    }

    /**
     * 提高写入效率，Jedis不支持集群使用pipeLine，故采用单点Redis
     */
    @SuppressWarnings("all")
    public void incrAssociateReceiveScore(String activityId, String uid, Set<String> aidSet, int score, int rankType, int rankGiftId) {
        try {
            if (aidSet.isEmpty()) {
                return;
            }
            RedisSerializer<String> keySerializer = (RedisSerializer<String>) activityTemplate.getKeySerializer();
            byte[] member = keySerializer.serialize(uid);
            activityTemplate.executePipelined((RedisCallback<Object>) pipeLine -> {
                try {
                    for (String aid : aidSet) {
                        byte[] key = keySerializer.serialize(getRankingAssociateKey(activityId, aid, rankType, rankGiftId));
                        pipeLine.zIncrBy(key, score, member);
                        pipeLine.expire(key, TimeUnit.DAYS.toSeconds(180));
                        logger.info("incrAssociateReceiveScore activityId={} uid={} aid={} score={} rankType={} rankGiftId={}",
                                activityId, uid, aid, score, rankType, rankGiftId);
                    }
                } catch (Exception e) {
                    logger.error("incrAssociateReceiveScore activityId={} uid={} score={} rankType={} rankGiftId={}",
                            activityId, uid, score, rankType, rankGiftId, e);
                }
                return null;
            });
        } catch (Exception e) {
            logger.error("incrAssociateReceiveScore activityId={} uid={} aid={} score={} rankType={} rankGiftId={}",
                    activityId, uid, aidSet, score, rankType, rankGiftId, e);
        }
    }

    /**
     * 获取关联用户
     */
    public Associate getAssociate(String activityId, String uid, int rankType, int rankGiftId) {
        String key = getRankingAssociateKey(activityId, uid, rankType, rankGiftId);
        Set<ZSetOperations.TypedTuple<String>> tuples = activityTemplate.opsForZSet().reverseRangeWithScores(key, 0, 0);
        if (null == tuples) {
            return null;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : tuples) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                return null;
            }
            Associate associate = new Associate();
            associate.setUid(rangeWithScore.getValue());
            associate.setScore(rangeWithScore.getScore().intValue());
            return associate;
        }
        return null;
    }

    private String getRankingAssociateKey(String activityId, String uid, int rankType, int rankGiftId) {
        return "zset:rankAssoc:" + activityId + ":" + uid + ":" + rankType + ":" + rankGiftId;
    }

    public static class Associate {
        private String uid;
        private String head;
        private int score;

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }
    }

}
