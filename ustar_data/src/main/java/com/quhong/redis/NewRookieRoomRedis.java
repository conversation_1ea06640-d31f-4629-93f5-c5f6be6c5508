package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.quhong.cache.CacheMap;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.ForYouListV2VO;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Lazy
public class NewRookieRoomRedis {

    private static final Logger logger = LoggerFactory.getLogger(NewRookieRoomRedis.class);

    private static final long CACHE_TIME_MILLIS = 5 * 60 * 1000L;
    private static final int ALL = 1;
    private static final int ALL_USER = 2;
    private final CacheMap<Integer, Set<String>> cacheMap;

    public NewRookieRoomRedis() {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private OperationConfigRedis operationConfigRedis;

    public Set<String> getNewRookieRoomByRedis() {
        Set<String> uidSet = redisTemplate.opsForSet().members(getKey());
        if (!CollectionUtils.isEmpty(uidSet)) {
            return uidSet;
        }
        return new HashSet<>();
    }

    public boolean isNewRookieRoomByRedis(String roomId) {
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getKey(), roomId));
    }

    public Set<String> getNewRookieUserByRedis() {
        Set<String> uidSet = redisTemplate.opsForSet().members(getStaffKey());
        if (!CollectionUtils.isEmpty(uidSet)) {
            return uidSet;
        }
        return new HashSet<>();
    }

    public Set<String> getNewRookieUserByCache() {
        try {
            Set<String> rookieRoomIdSet = cacheMap.getData(ALL_USER);
            if (!CollectionUtils.isEmpty(rookieRoomIdSet)) {
                return rookieRoomIdSet;
            }
            rookieRoomIdSet = redisTemplate.opsForSet().members(getStaffKey());
            if (!CollectionUtils.isEmpty(rookieRoomIdSet)) {
                cacheMap.cacheData(ALL_USER, rookieRoomIdSet);
                return rookieRoomIdSet;
            }
        } catch (Exception e) {
            logger.error("getNewRookieUserByCache error. {}", e.getMessage(), e);
        }
        return new HashSet<>();
    }

    public boolean isNewRookieUserByRedis(String uid) {
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getStaffKey(), uid));
    }

    //    @Cacheable(value = "canSeeRookieRoom", key = "#p0", condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
//            cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Boolean canSeeRookieRoom(String uid) {
        // 仅面向新设备注册的新用户，且注册30天内可见，超过30天列表不展示
        return ActorUtils.isNewRegisterActor(uid, 30);
    }

    public Set<String> getAllNewRookieRoom() {
        try {
            Set<String> rookieRoomIdSet = cacheMap.getData(ALL);
            if (!CollectionUtils.isEmpty(rookieRoomIdSet)) {
                return rookieRoomIdSet;
            }
            rookieRoomIdSet = redisTemplate.opsForSet().members(getKey());
            if (!CollectionUtils.isEmpty(rookieRoomIdSet)) {
                cacheMap.cacheData(ALL, rookieRoomIdSet);
                return rookieRoomIdSet;
            }
        } catch (Exception e) {
            logger.error("getAllNewRookieRoom error. {}", e.getMessage(), e);
        }
        return new HashSet<>();
    }

    public boolean isNewRookieRoom(String roomId) {
        try {
            return getAllNewRookieRoom().contains(roomId);
        } catch (Exception e) {
            logger.error("isNewRookieRoom error. {}", e.getMessage(), e);
            return false;
        }
    }

    public void addRookieRoomHangUpUser(String roomId, String uid) {
        String key = getRookieRoomHangUpUserKey(roomId);
        try {
            redisTemplate.opsForSet().add(key, uid);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addRookieRoomHangUpUser error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public boolean isRookieRoomHangUpUser(String roomId, String uid) {
        String key = getRookieRoomHangUpUserKey(roomId);
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, uid));
        } catch (Exception e) {
            logger.error("isRookieRoomHangUpUser error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
            return false;
        }
    }

    public void removeRookieRoomHangUpUser(String roomId, String uid) {
        String key = getRookieRoomHangUpUserKey(roomId);
        try {
            redisTemplate.opsForSet().remove(key, uid);
        } catch (Exception e) {
            logger.error("removeRookieRoomHangUpUser error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    /**
     * 新增迎新房挂机记录
     *
     * @param roomId 房间id
     * @param uid    用户uid
     * @param status 状态：0非挂机 1挂机
     */
    public void addRookieRoomUserHangUpRecord(String roomId, String uid, int status) {
        String key = getRookieRoomUserHangUpRecordKey(roomId, uid);
        try {
            Long listSize = redisTemplate.opsForList().leftPush(key, status + "");
            if (listSize != null && listSize > 20) {
                redisTemplate.opsForList().rightPop(key);
            }
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_MINUTES_THREE, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("addRookieRoomHangUpRecord error. roomId={} status={} {}", roomId, status, e.getMessage(), e);
        }
    }

    /**
     * 获取迎新房用户挂机记录
     */
    public List<String> getRookieRoomUserHangUpRecord(String roomId, String uid) {
        String key = getRookieRoomUserHangUpRecordKey(roomId, uid);
        try {
            return redisTemplate.opsForList().range(key, 0, -1);
        } catch (Exception e) {
            logger.error("getRookieRoomHangUpRecord error. roomId={} {}", roomId, e.getMessage(), e);
            return null;
        }
    }

    public void addRookieRoomBlacklist(String roomId) {

        // 最近60分钟内房间累计踢人≥3人，当天移出推荐池
        int endTime = DateHelper.getNowSeconds();
        int startTime = endTime - (int) TimeUnit.MINUTES.toSeconds(60);
        if (roomKickRedis.getRoomKickUserCount(roomId, startTime, endTime) < 3) {
            return;
        }
        if (!isNewRookieRoom(roomId)) {
            setKickOutRoomRecord(roomId);
        } else {
            setKickOutRoomRecord(roomId);
            String key = getRookieRoomBlacklistKey();
            try {
                redisTemplate.opsForSet().add(key, roomId);
                redisTemplate.expire(key, 1, TimeUnit.DAYS);
            } catch (Exception e) {
                logger.error("addRookieRoomBlacklist error. roomId={} {}", roomId, e.getMessage(), e);
            }
        }
    }

    public Set<String> getRookieRoomBlacklist() {
        String key = getRookieRoomBlacklistKey();
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("getRookieRoomBlacklist error. {}", e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    /**
     * 新增迎新房挂机记录
     *
     * @param roomId 房间id
     * @param status 状态：0非挂机 1挂机
     */
    public void addRookieRoomHangUpRecord(String roomId, int status) {
        String key = getRookieRoomHangUpRecordKey(roomId);
        try {
            Long listSize = redisTemplate.opsForList().leftPush(key, status + "");
            if (listSize != null && listSize > 10) {
                redisTemplate.opsForList().rightPop(key);
            }
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_MINUTES_THREE, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("addRookieRoomHangUpRecord error. roomId={} status={} {}", roomId, status, e.getMessage(), e);
        }
    }

    /**
     * 获取迎新房用户挂机记录
     */
    public List<String> getRookieRoomHangUpRecord(String roomId) {
        String key = getRookieRoomHangUpRecordKey(roomId);
        try {
            return redisTemplate.opsForList().range(key, 0, -1);
        } catch (Exception e) {
            logger.error("getRookieRoomHangUpRecord error. roomId={} {}", roomId, e.getMessage(), e);
            return null;
        }
    }

    private String getKey() {
        return "op:new:rookie:room";
    }

    private String getStaffKey() {
        return "op:new:staff";
    }

    private String getRookieRoomHangUpUserKey(String roomId) {
        return "set:rookieRoomHangUpUser_" + roomId;
    }

    private String getRookieRoomUserHangUpRecordKey(String roomId, String uid) {
        return "list:rookieRoomUserHangUpRecord_" + roomId + "_" + uid;
    }

    private String getRookieRoomHangUpRecordKey(String roomId) {
        return "list:rookieRoomHangUpRecord_" + roomId;
    }

    private String getRookieRoomBlacklistKey() {
        return "set:rookieRoomBlacklist" + DateHelper.ARABIAN.formatDateInDay();
    }

    public void setNonHangUpRecord(String roomId) {
        String key = getNonHangUpRecordKey(roomId);
        try {
            redisTemplate.opsForValue().set(key, DateHelper.getNowSeconds() + "");
            redisTemplate.expire(key, 5, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("addHangUpRoom error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public boolean isHangUpRoom(String roomId) {
        String key = getNonHangUpRecordKey(roomId);
        // 房间全麦位上的用户2分钟内没人的声音的就是挂机房间
        try {
            String strValue = redisTemplate.opsForValue().get(key);
            if (StringUtils.hasLength(strValue)) {
                int activeTime = Integer.parseInt(strValue);
                if (activeTime >= DateHelper.getNowSeconds() - (int) TimeUnit.MINUTES.toSeconds(2)) {
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("isHangUpRoom error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return true;
    }

    public void setKickOutRoomRecord(String roomId) {
        String key = getKickOutRoomKey(roomId);
        try {
            redisTemplate.opsForValue().set(key, DateHelper.getNowSeconds() + "");
            redisTemplate.expire(key, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setKickOutRoomRecord error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public boolean isKickOutRoomRecord(String roomId, int hour) {
        String key = getKickOutRoomKey(roomId);
        // 房间最近hour小时有踢过人就是踢人房间
        try {
            String strValue = redisTemplate.opsForValue().get(key);
            if (StringUtils.hasLength(strValue)) {
                int activeTime = Integer.parseInt(strValue);
                if (activeTime >= DateHelper.getNowSeconds() - (int) TimeUnit.HOURS.toSeconds(hour)) {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            logger.error("isKickOutRoom error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return true;
    }


    public Long addAllUserByRedis(Set<String> addUsers) {
        String[] toArray = addUsers.toArray(new String[0]);
        String key = getStaffKey();
        return redisTemplate.opsForSet().add(key, toArray);
    }

    public Long removeAllUserByRedis(Set<String> rmUsers) {
        String[] toArray = rmUsers.toArray(new String[0]);
        String key = getStaffKey();
        return redisTemplate.opsForSet().remove(key, toArray);
    }

    public Long addAllRoomByRedis(Set<String> addUsers) {
        String[] toArray = addUsers.toArray(new String[0]);
        String key = getKey();
        return redisTemplate.opsForSet().add(key, toArray);
    }

    public Long removeAllRoomByRedis(Set<String> rmUsers) {
        String[] toArray = rmUsers.toArray(new String[0]);
        String key = getKey();
        return redisTemplate.opsForSet().remove(key, toArray);
    }


    private String getNonHangUpRecordKey(String roomId) {
        return "str:nonHangUpRecord_" + roomId;
    }

    private String getKickOutRoomKey(String roomId) {
        return "str:kickOutRoomRecord_" + roomId;
    }

    /**
     * 官方迎新房配置
     */
    // 国家到房间的映射 hash结构 country -> Set<roomId>
    private String getOfficialWelcomeCountryToRoomsKey() {
        return "hash:official:welcome:country:rooms";
    }

    // 房间到国家的映射 hash结构 roomId -> Set<country>
    private String getOfficialWelcomeRoomToCountriesKey() {
        return "hash:official:welcome:room:countries";
    }

    // 获取国家对应的所有房间
    public Set<String> getOfficialWelcomeRoomsByCountry(String countryCode) {
        try {
            if (StringUtils.isEmpty(countryCode)) {
                return Collections.emptySet();
            }

            String roomsJson = (String) redisTemplate.opsForHash().get(getOfficialWelcomeCountryToRoomsKey(), countryCode);
            if (!StringUtils.isEmpty(roomsJson)) {
                return JSON.parseObject(roomsJson, new TypeReference<Set<String>>(){});
            }
        } catch (Exception e) {
            logger.error("getOfficialWelcomeRoomsByCountry error. countryCode={} e={}", countryCode, e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    /**
     * 获取所有官方迎新房ID
     * @return 所有官方迎新房ID集合
     */
    public Set<String> getAllOfficialWelcomeRooms() {
        try {
            return redisTemplate.opsForHash().keys(getOfficialWelcomeRoomToCountriesKey()).stream().map(Object::toString).collect(Collectors.toSet());
        } catch (Exception e) {
            logger.error("getAllOfficialWelcomeRooms error. e={}", e.getMessage(), e);
            return new HashSet<>();
        }
    }


    private String getSocialRoomListKey() {
        return "str:socialRoomList";
    }

    private String getAllRecommendOldUserKey() {
        return "str:allRecommendOldUser";
    }

    /**
     * 数据用于 8.59 星空首页推荐（推荐用户）（适用新,老用户策略），
     * 星空首页（推荐用户）meet friend-all（适用新用户策略），
     * ALL-new（推荐房间）（适用新用户策略）
     *
     * @return
     */
    public List<ForYouListV2VO> getSocialRoomList() {
        List<ForYouListV2VO> result = new ArrayList<>();
        try {
            String json = redisTemplate.opsForValue().get(getSocialRoomListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, ForYouListV2VO.class);
        } catch (Exception e) {
            logger.error("get socialRoomList error", e);
            return result;
        }
    }

    /**
     * all-recommend 老用户
     */
    public List<ForYouListV2VO> getAllRecommendOldUserList() {
        List<ForYouListV2VO> result = new ArrayList<>();
        try {
            String json = redisTemplate.opsForValue().get(getAllRecommendOldUserKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, ForYouListV2VO.class);
        } catch (Exception e) {
            logger.error("get getAllRecommendOldUserList error", e);
            return result;
        }
    }


    @Cacheable(value = "getAllNewRoomScoreWeight", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public JSONObject getAllNewRoomScoreWeight() {
        return operationConfigRedis.getAllNewRoomScoreWeight();
    }

}
