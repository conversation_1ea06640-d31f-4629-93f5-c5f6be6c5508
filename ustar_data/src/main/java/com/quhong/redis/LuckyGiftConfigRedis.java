package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class LuckyGiftConfigRedis {
    private static final Logger logger = LoggerFactory.getLogger(LuckyGiftConfigRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String GAME_KEY = "lucky_gift_prize";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;



    private String poolSizeKey() {
        return "list:pool_size:"+ GAME_KEY;
    }



    public int getPoolSize() {
        try {
            Long poolSize = clusterRedis.opsForList().size(poolSizeKey());
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("getPoolSize error score={}", e.getMessage());
            return 0;
        }
    }

    public void deletePoolSize() {
        try {
            clusterRedis.delete(poolSizeKey());
        } catch (Exception e) {
            logger.error("deletePoolSize error={}", e.getMessage());
        }
    }



    public void initPoolSize(List<String> rewardConfigList) {
        try {
            clusterRedis.opsForList().rightPushAll(poolSizeKey(), rewardConfigList);
            clusterRedis.expire(poolSizeKey(), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("initPoolSize error clusterTemplate={}  score={}", clusterRedis, e);
        }
    }


    // 抽奖
    public String drawSmashEggKey() {
        try {
            String prizeKey = clusterRedis.opsForList().leftPop(poolSizeKey());
            return prizeKey != null ? prizeKey : "";
        } catch (Exception e) {
            logger.error("drawSmashEggKey error e={}", e.getMessage());
            return "";
        }
    }


}
