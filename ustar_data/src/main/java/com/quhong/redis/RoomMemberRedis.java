package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.RoomMemberData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
@Component
@Lazy
public class RoomMemberRedis {

    private static final Logger logger = LoggerFactory.getLogger(RoomMemberRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainCluster;
    @Resource
    private RoomMemberDao memberDao;

    public boolean isExists(String roomId) {
        return Boolean.TRUE.equals(mainCluster.hasKey(getMemberListKey(roomId)));
    }

    /**
     * 保存房间会员到redis
     */
    public void addMemberToRedis(String roomId, String memberUid) {
        String key = getMemberListKey(roomId);
        try {
            mainCluster.opsForSet().add(key, memberUid);
            mainCluster.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add member data to redis error. roomId={} memberUid={} {}", roomId, memberUid, e.getMessage(), e);
        }
    }

    /**
     * 移除房间会员
     */
    public void removeMember(String roomId, String memberUid) {
        String key = getMemberListKey(roomId);
        try {
            mainCluster.opsForSet().remove(key, memberUid);
            mainCluster.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("remove member data from redis error. roomId={} memberUid={} {}", roomId, memberUid, e.getMessage(), e);
        }
    }

    /**
     * 保存房间会员列表到redis
     */
    public void saveMemberListToRedis(String roomId, Set<String> memberUidSet) {
        String key = getMemberListKey(roomId);
        try {
            String[] toArray = memberUidSet.stream().map(String::valueOf).toArray(String[]::new);
            mainCluster.opsForSet().add(key, toArray);
            mainCluster.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save member data list to redis error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    /**
     * 校验是否是房间会员
     */
    public boolean isMember(String roomId, String aid) {
        try {
            if (!isExists(roomId)) {
                reBuildCache(roomId);
            }
            return Boolean.TRUE.equals(mainCluster.opsForSet().isMember(getMemberListKey(roomId), aid));
        } catch (Exception e) {
            logger.error("check isMember error, roomId={} aid={} {}", roomId, aid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取房间会员数量
     */
    public int getMemberCount(String roomId) {
        try {
            if (!isExists(roomId)) {
                Set<String> stringSet = reBuildCache(roomId);
                return stringSet.size();
            }
            Long size = mainCluster.opsForSet().size(getMemberListKey(roomId));
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("get room member count error. roomId={} {}", roomId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取房间所有会员
     */
    public Set<String> getMemberList(String roomId) {
        try {
            if (!isExists(roomId)) {
                return reBuildCache(roomId);
            }
            return mainCluster.opsForSet().members(getMemberListKey(roomId));
        } catch (Exception e) {
            logger.error("get room member list error. roomId={} {}", roomId, e.getMessage(), e);
            return null;
        }
    }

    private Set<String> reBuildCache(String roomId) {
        List<RoomMemberData> roomAllMembers = memberDao.getRoomAllMembers(roomId);
        Set<String> memberUidSet;
        if (!CollectionUtils.isEmpty(roomAllMembers)) {
            memberUidSet = roomAllMembers.stream().map(RoomMemberData::getAid).collect(Collectors.toSet());
            saveMemberListToRedis(roomId, memberUidSet);
        } else {
            memberUidSet = Collections.emptySet();
        }
        return memberUidSet;
    }

    private String getMemberListKey(String roomId) {
        return "set:memberList:" + roomId;
    }
}
