package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.analysis.VideoRoomRecordEvent;
import com.quhong.constant.ExpireTimeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class ShuShuAnalyseRedis {
    private final static Logger logger = LoggerFactory.getLogger(ShuShuAnalyseRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public void saveVideoRoomRecord(VideoRoomRecordEvent data) {
        try {
            String json = JSON.toJSONString(data);
            String roomId = data.getRoomid();
            String uid = data.getUid();
            if (!StringUtils.isEmpty(json) && StringUtils.hasLength(roomId) && StringUtils.hasLength(uid)) {
                clusterRedis.opsForValue().set(getKey(roomId, uid), json);
                clusterRedis.expire(getKey(roomId, uid), ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.error("saveVideoRoomRecord error msg={} ", e.getMessage(), e);
        }
    }


    public VideoRoomRecordEvent getVideoRoomRecord(String roomId, String uid) {
        try {
            String json = clusterRedis.opsForValue().get(getKey(roomId, uid));
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseObject(json, VideoRoomRecordEvent.class);
        } catch (Exception e) {
            logger.error("getVideoRoomRecord error msg={} ", e.getMessage(), e);
            return null;
        }
    }

    public void deleteVideoRoomRecord(String roomId, String uid) {
        try {
            clusterRedis.delete(getKey(roomId, uid));

        } catch (Exception e) {
            logger.error("deleteVideoRoomRecord error msg={} ", e.getMessage(), e);
        }
    }

    private String getKey(String roomId, String uid) {
        return "str:shu:analyse:video:room:" + roomId + ":" + uid;
    }


}
