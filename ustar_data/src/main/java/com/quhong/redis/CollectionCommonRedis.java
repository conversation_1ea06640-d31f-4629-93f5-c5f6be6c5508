package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;


@Lazy
@Component
public class CollectionCommonRedis {
    private static final Logger logger = LoggerFactory.getLogger(CollectionCommonRedis.class);
    private static final Integer MAX_SIZE = 30;


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * hash key
     */
    private String getCommonHashKey(String hashKey) {
        return String.format("hash:collection:%s", hashKey);
    }

    /**
     * 获取hash key所有值
     */
    public Map<String, Integer> getCommonHashAll(String hashKey) {
        Map<String, Integer> hashMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getCommonHashKey(hashKey));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getCommonHashAll error hashKey={} e={}", hashKey, e.getMessage(), e);
        }
        return hashMap;
    }


    /**
     * 获取hash key所有值
     */
    public Map<String, String> getCommonHashAllMapStr(String hashKey) {
        Map<String, String> hashMap = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getCommonHashKey(hashKey));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", String.valueOf(entry.getValue()));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getCommonHashAllMapStr error hashKey={} e={}", hashKey, e.getMessage(), e);
        }
        return hashMap;
    }


    /**
     * 增加hash key 某个属性的值
     */
    public int incCommonHashNum(String hashKey, String key, int num) {
        try {
            Long afterNum = clusterTemplate.opsForHash().increment(getCommonHashKey(hashKey), key, num);
            clusterTemplate.expire(getCommonHashKey(hashKey), ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            return afterNum.intValue();
        } catch (Exception e) {
            logger.error("incCommonHashNum error hashKey={} e={}", hashKey, e);
            return num;
        }
    }

    /**
     * 设置hash key 某个属性的值
     */
    public void setCommonHashNum(String hashKey, String key, int num) {
        try {
            clusterTemplate.opsForHash().put(getCommonHashKey(hashKey), key, String.valueOf(num));
        } catch (Exception e) {
            logger.error("setCommonHashNum error hashKey={} e={}", hashKey, e);
        }
    }

    public void setExpire(String hashKey) {
        try {
            clusterTemplate.expire(getCommonHashKey(hashKey), ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setCommonHashNum error hashKey={} e={}", hashKey, e);
        }
    }


    /**
     * 设置hash key 某个属性的值
     */
    public void setCommonHashData(String hashKey, String key, String data) {
        try {
            clusterTemplate.opsForHash().put(getCommonHashKey(hashKey), key, data);
            clusterTemplate.expire(getCommonHashKey(hashKey), ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setCommonHashData error hashKey={} e={}", hashKey, e);
        }
    }

    /**
     * 获取hash key 某个属性的值
     */
    public int getCommonHashValue(String hashKey, String key) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(getCommonHashKey(hashKey), key);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getCommonHashValue error hashKey={} key={}  e={}", hashKey, key, e);
            return 0;
        }
    }

    public String getCommonHashStrValue(String hashKey, String key) {
        try {
            return (String) clusterTemplate.opsForHash().get(getCommonHashKey(hashKey), key);
        } catch (Exception e) {
            logger.info("getCommonHashStrValue error hashKey={} key={}  e={}", hashKey, key, e);
            return null;
        }
    }

    public Set<String> getCommonHashAllKeyStr(String hashKey) {
        Set<String> result = new CopyOnWriteArraySet<>();
        try {
            String key = getCommonHashKey(hashKey);
            Set<Object> fields = clusterTemplate.opsForHash().keys(getCommonHashKey(hashKey));
            for (Object field : fields) {
                result.add(String.valueOf(field));
            }
            return result;
        } catch (Exception e) {
            logger.error("getCommonHashAllKeyStr error. hashKey={}", hashKey, e);
            return result;
        }
    }

    public void setCommonHashDataAll(String hashKey, Map<String, String> dataMap) {
        try {
            String key = getCommonHashKey(hashKey);
            clusterTemplate.opsForHash().putAll(key, dataMap);
        } catch (Exception e) {
            logger.info("setCommonHashDataAll error hashKey={} dataMap={}  e={}", hashKey, dataMap, e);
        }
    }

    public boolean delCommonKey(String hashKey) {
        try {
            String key = getCommonHashKey(hashKey);
            return Boolean.TRUE.equals(clusterTemplate.delete(key));
        } catch (Exception e) {
            logger.info("delCommonKey error hashKey={}  e={}", hashKey, e);
        }
        return false;
    }
}
