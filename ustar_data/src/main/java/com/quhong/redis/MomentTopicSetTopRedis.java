package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.MomentTopicSetTopData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.IndexBannerData;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.service.HomeBannerService;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class MomentTopicSetTopRedis {
    private static final Logger logger = LoggerFactory.getLogger(MomentTopicSetTopRedis.class);
    /**
     * 倒序排序
     */
    public static final Comparator<MomentTopicSetTopData> SORT_NUM_DESC = Comparator.comparing(MomentTopicSetTopData::getSortNum).reversed();


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    @Resource
    private ActorDao actorDao;


    @Resource
    private HomeBannerService homeBannerService;

    /**
     * hash key
     */
    private String getSetTopHashKey() {
        return String.format("hash:moment:topic:set:top");
    }


    public List<MomentTopicSetTopData> getValidTopicHotList() {
        List<MomentTopicSetTopData> topicIdList = new ArrayList<>();
        Map<Integer, MomentTopicSetTopData> allMap = getSetTopHashAllCache();
        int nowTime = DateHelper.getNowSeconds();
        for (MomentTopicSetTopData topData : allMap.values()) {
            if (topData.getStartTime() <= nowTime && nowTime < topData.getEndTime()) {
                topicIdList.add(topData);
            }
        }
        topicIdList.sort(SORT_NUM_DESC);
        return topicIdList;
    }

    /**
     * 获取有效的话题置顶列表（带用户过滤）
     */
    public List<MomentTopicSetTopData> getValidTopicHotList(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return getValidTopicHotList();
        }

        List<MomentTopicSetTopData> topicIdList = new ArrayList<>();
        Map<Integer, MomentTopicSetTopData> allMap = getSetTopHashAllCache();
        int nowTime = DateHelper.getNowSeconds();
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        if (actorData == null) {
            logger.warn("getValidTopicHotList actorData is null, uid={}", uid);
            return getValidTopicHotList();
        }

        for (MomentTopicSetTopData topData : allMap.values()) {
            if (topData.getStartTime() <= nowTime && nowTime < topData.getEndTime()) {
                // 检查过滤条件
                if (checkFilterValid(actorData, topData)) {
                    topicIdList.add(topData);
                }
            }
        }
        topicIdList.sort(SORT_NUM_DESC);
        return topicIdList;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Map<Integer, MomentTopicSetTopData> getSetTopHashAllCache() {
        return getSetTopHashAll();
    }

    /**
     * 获取hash key所有值
     */
    public Map<Integer, MomentTopicSetTopData> getSetTopHashAll() {
        Map<Integer, MomentTopicSetTopData> hashMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getSetTopHashKey());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                MomentTopicSetTopData setTopData = JSONObject.parseObject(String.valueOf(entry.getValue()), MomentTopicSetTopData.class);
                hashMap.put(Integer.valueOf(String.valueOf(entry.getKey())), setTopData);
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getSetTopHashAll error e={}", e.getMessage(), e);
        }
        return hashMap;
    }

    public void setSetTopHashData(Integer topicId, MomentTopicSetTopData setTopData) {
        clusterTemplate.opsForHash().put(getSetTopHashKey(), String.valueOf(topicId), JSONObject.toJSONString(setTopData));
        clusterTemplate.expire(getSetTopHashKey(), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
    }

    /**
     * 删除hash key 某个属性的值
     */
    public void delSetTopHashData(String key) {
        clusterTemplate.opsForHash().delete(getSetTopHashKey(), key);
    }

    /**
     * 检查过滤条件是否有效
     * 参考 HomeBannerService.checkFilterValid 方法
     */
    private boolean checkFilterValid(ActorData actorData, MomentTopicSetTopData data) {
        if (data.getFilterType() == 0 || data.getFilterType() == HomeBannerService.FILTER_TYPE_LIMITED_RECHARGE) {
            return true;
        }
        int bcGameSwitch = homeBannerService.getBCGameSwitch(actorData);
        IndexBannerData filterData = new IndexBannerData();
        filterData.setFilterType(data.getFilterType());
        filterData.setFilterItem(data.getFilterItem());
        return homeBannerService.checkFilterValid(actorData, filterData, bcGameSwitch);
    }
}
