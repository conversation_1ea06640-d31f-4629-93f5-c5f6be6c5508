package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.GreetWeightData;
import com.quhong.data.NewGreetData;
import com.quhong.mysql.data.GreetUserData;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 打招呼redis
 */
@Lazy
@Component
public class NewGreetRedis {

    private static final Logger logger = LoggerFactory.getLogger(NewGreetRedis.class);
    public static final List<GreetWeightData> GREET_USER_CATEGORY_LIST = new ArrayList<>();
    private static final Integer DAILY_MAX_NUM = 3;
    static {
        GREET_USER_CATEGORY_LIST.add(new GreetWeightData("",1, "派对女王/迎新用户", 0, 0, 0, 0, 0, 0, 0, 1754459300));
        GREET_USER_CATEGORY_LIST.add(new GreetWeightData("", 2, "当天注册新用户", 0, 0, 0, 0, 0, 0, 0, 1754459300));
        GREET_USER_CATEGORY_LIST.add(new GreetWeightData("",3, "7天内注册新用户", 0, 0, 0, 0, 0, 0, 0, 1754459300));
        GREET_USER_CATEGORY_LIST.add(new GreetWeightData("", 4, "30天内注册新用户", 0, 0, 0, 0, 0, 0, 0, 1754459300));
        GREET_USER_CATEGORY_LIST.add(new GreetWeightData("", 5, "注册超过30天老用户", 0, 0, 0, 0, 0, 0, 0, 1754459300));
    }


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;


    private String getNewGreetKey(String uid, String aid) {
        return String.format("str:newGreet:%s", ActorUtils.generateMsgIndex(uid, aid));
    }

    public void setUserNewGreet(String uid, String aid, String newGreetData) {
        try {
            String key = getNewGreetKey(uid, aid);
            clusterRedis.opsForValue().set(key, newGreetData);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setUserNewGreet error: {}", e.getMessage(), e);
        }
    }


    public NewGreetData getUserNewGreetConfig(String uid, String aid) {
        try {
            String newGreetStr = clusterRedis.opsForValue().get(getNewGreetKey(uid, aid));
            return ObjectUtils.isEmpty(newGreetStr) ? null : JSONObject.parseObject(newGreetStr, NewGreetData.class);
        } catch (Exception e) {
            logger.error("getUserNewGreetConfig error: {}", e.getMessage(), e);
        }
        return null;
    }

    public void removeUserNewGreet(String uid, String aid) {
        try {
            clusterRedis.delete(getNewGreetKey(uid, aid));
        } catch (Exception e) {
            logger.error("removeUserNewGreet error:{}", e.getMessage(), e);
        }
    }

    /**
     * 打招呼次数key
     */
    private String getGreetNumKey(String uid) {
        return String.format("str:greetNum:%s:%s", uid,  DateHelper.ARABIAN.formatDateInDay());
    }

    public void setGreetNum(String uid, int num) {
        try {
            String key = getGreetNumKey(uid);
            clusterRedis.opsForValue().set(key, String.valueOf(num));
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setGreetNum error: {}", e.getMessage(), e);
        }
    }

    public int getGreetNum(String uid) {
        try {
            String greetNumStr = clusterRedis.opsForValue().get(getGreetNumKey(uid));
            return ObjectUtils.isEmpty(greetNumStr) ? DAILY_MAX_NUM : Integer.parseInt(greetNumStr);
        } catch (Exception e) {
            logger.error("getGreetNum error: {}", e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 打招呼用户权重key
     */
    private String getGreetUserWeightKey() {
        return "str:greetUserWeight";
    }

    public void setGreetUserWeight(List<GreetWeightData> greetUserWeightList) {
        try {
            String key = getGreetUserWeightKey();
            clusterRedis.opsForValue().set(key, JSONObject.toJSONString(greetUserWeightList));
        } catch (Exception e) {
            logger.error("setGreetUserWeight error: {}", e.getMessage(), e);
        }
    }

    public List<GreetWeightData> getGreetUserWeight() {
        try {
            String greetUserWeightStr = clusterRedis.opsForValue().get(getGreetUserWeightKey());
            return ObjectUtils.isEmpty(greetUserWeightStr) ? GREET_USER_CATEGORY_LIST : JSONObject.parseArray(greetUserWeightStr, GreetWeightData.class);
        } catch (Exception e) {
            logger.error("getGreetUserWeight error: {}", e.getMessage(), e);
        }
        return GREET_USER_CATEGORY_LIST;
    }

    /**
     * 打招呼用户推荐key
     */
    private String getGreetUserRecommendKey(String countryCode) {
        return "str:greetUserRecommend:" + countryCode;
    }

    public void setGreetUserRecommend(String countryCode, List<GreetUserData> recommendGreetList) {
        try {
            String key = getGreetUserRecommendKey(countryCode);
            clusterRedis.opsForValue().set(key, JSONObject.toJSONString(recommendGreetList));
            if (ServerConfig.isProduct()) {
                clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_MINUTES_FIVE, TimeUnit.MINUTES);
            } else {
                clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_MINUTES_ONE, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            logger.error("setGreetUserRecommend error: {}", e.getMessage(), e);
        }
    }

    public List<GreetUserData> getGreetUserRecommend(String countryCode) {
        try {
            String recommendGreetListStr = clusterRedis.opsForValue().get(getGreetUserRecommendKey(countryCode));
            return ObjectUtils.isEmpty(recommendGreetListStr) ? null : JSONObject.parseArray(recommendGreetListStr, GreetUserData.class);
        } catch (Exception e) {
            logger.error("getGreetUserRecommend error: {}", e.getMessage(), e);
        }
        return null;
    }


    /**
     * 打招呼用户推荐曝光key
     */
    private String getGreetUserExposureHashKey(String dateStr) {
        return String.format("hash:greetUserExposure:%s", dateStr);
    }

    public Map<String, Integer> getGreetUserExposureAll(String dateStr) {
        Map<String, Integer> hashMap = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterRedis.opsForHash().entries(getGreetUserExposureHashKey(dateStr));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getGreetUserExposureAll error dateStr={} e={}", dateStr, e.getMessage(), e);
        }
        return hashMap;
    }

    public void incGreetUserExposureNum(String dateStr, String uid) {
        try {
            clusterRedis.opsForHash().increment(getGreetUserExposureHashKey(dateStr), uid, 1);
            clusterRedis.expire(getGreetUserExposureHashKey(dateStr), ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incGreetUserExposureNum error dateStr={} e={}", dateStr, e.getMessage(), e);
        }
    }

}
