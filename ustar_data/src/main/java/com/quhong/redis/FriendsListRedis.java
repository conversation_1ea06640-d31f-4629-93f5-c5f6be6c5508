package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.data.FriendsData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Lazy
public class FriendsListRedis {
    private static final Logger logger = LoggerFactory.getLogger(FriendsListRedis.class);

    private static final String CACHE_FLAG = "cacheFlag";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private FriendsDao friendsDao;

    /**
     * uid与aid是否是好友
     */
    public boolean isFriend(String uid, String aid) {
        String key = getFriendListKey(uid);
        try {
            if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
                return reBuildCache(uid).contains(aid);
            }
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, aid));
        } catch (Exception e) {
            logger.error("get isFriend error, uid={} aid={} msg={}", uid, aid, e.getMessage());
            return false;
        }
    }

    public int getFriendCount(String uid) {
        String key = getFriendListKey(uid);
        if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
            return reBuildCache(uid).size();
        }
        return Objects.requireNonNull(redisTemplate.opsForSet().size(key)).intValue() - 1;
    }

    public Set<String> getFriendList(String uid) {
        String key = getFriendListKey(uid);
        try {
            if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
                return reBuildCache(uid);
            }
            Set<String> members = redisTemplate.opsForSet().members(key);
            if (CollectionUtils.isEmpty(members)) {
                return Collections.emptySet();
            }
            members.removeIf(CACHE_FLAG::equals);
            return members;
        } catch (Exception e) {
            logger.error("get friend list error. uid={} {}", uid, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public void saveFriendToRedis(String uid, String friendUid) {
        String key = getFriendListKey(uid);
        try {
            if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
                reBuildCache(uid);
                return;
            }
            redisTemplate.opsForSet().add(key, friendUid);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save friend data to redis error, uid={} friendUid={} msg={}", uid, friendUid, e.getMessage());
        }
    }

    public void removeFromRedis(String uid, String friendUid) {
        try {
            redisTemplate.opsForSet().remove(getFriendListKey(uid), friendUid);
        } catch (Exception e) {
            logger.error("remove friend data to redis error, uid={} friendUid={} msg={}", uid, friendUid, e.getMessage());
        }
    }

    private Set<String> reBuildCache(String uid) {
        long millis = System.currentTimeMillis();
        List<FriendsData> allData = friendsDao.findDataList(uid);
        Set<String> friendSet;
        if (CollectionUtils.isEmpty(allData)) {
            friendSet = new HashSet<>();
        } else {
            friendSet = allData.stream().map(k -> {
                if (uid.equals(k.getUidFirst())) {
                    return k.getUidSecond();
                } else {
                    return k.getUidFirst();
                }
            }).collect(Collectors.toSet());
        }
        // 加入标记位
        friendSet.add(CACHE_FLAG);
        String[] toArray = friendSet.toArray(new String[0]);
        String key = getFriendListKey(uid);
        redisTemplate.opsForSet().add(key, toArray);
        redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        logger.info("reBuildCache uid={} size={} cost={}", uid, friendSet.size(), System.currentTimeMillis() - millis);
        return friendSet;
    }

    private String getFriendListKey(String uid) {
        return "set:friendList_" + uid;
    }
}
