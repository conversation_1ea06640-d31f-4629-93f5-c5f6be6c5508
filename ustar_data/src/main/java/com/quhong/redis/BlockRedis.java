package com.quhong.redis;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.BlockTnConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class BlockRedis {

    private static final Logger logger = LoggerFactory.getLogger(BlockRedis.class);

    public static final double FOREVER_BLOCK = 3000000000.0;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public String checkBlock(String tnId, int blockType) {
        double blockCtime = 0;
        switch (blockType) {
            case BlockTnConstant.BLOCK_MIC:
                blockCtime = getBlockMicCtime(tnId);
                break;
            case BlockTnConstant.BLOCK_ROOM_FILE:
                blockCtime = getBlockRoomFileCtime(tnId);
                break;
            case BlockTnConstant.BLOCK_CREATE_ROOM:
                blockCtime = getBlockCreateRoomCtime(tnId);
                break;
            case BlockTnConstant.BLOCK_MSG:
                blockCtime = getBlockMsgCtime(tnId);
                break;
            case BlockTnConstant.BLOCK_LOGIN:
                blockCtime = getBlockLoginCtime(tnId);
//                if (blockCtime > 0 && getFakeSevenDays(tnId) > 0) {
//                    blockCtime = FOREVER_BLOCK;
//                }
                break;
            case BlockTnConstant.BLOCK_PROFILE:
                blockCtime = getBlockProfileCtime(tnId);
                break;
            default:
                break;
        }
        if (blockCtime == 0) {
            return "";
        }
        if (blockCtime >= FOREVER_BLOCK) {
            return "2099-12-31 00:00";
        }
        long ctime = (int) blockCtime * 1000L;
        String date = DateHelper.ARABIAN.formatDateTime(new Date(ctime));
        return date;
    }

    private double getBlockMicCtime(String tnId) {
        if (StringUtils.isEmpty(tnId)) {
            return 0;
        }
        Double score = redisTemplate.opsForZSet().score(getBlockMicKey(), tnId);
        if (score != null) {
            return score;
        }
        return 0;
    }

    public double getBlockRoomFileCtime(String tnId) {
        if (StringUtils.isEmpty(tnId)) {
            return 0;
        }
        Double score = redisTemplate.opsForZSet().score(getBlockRoomFileKey(), tnId);
        if (score != null) {
            return score;
        }
        return 0;
    }

    public double getBlockCreateRoomCtime(String tnId) {
        if (StringUtils.isEmpty(tnId)) {
            return 0;
        }
        Double score = redisTemplate.opsForZSet().score(getBlockCreateRoomKey(), tnId);
        if (score != null) {
            return score;
        }
        return 0;
    }

    public double getBlockMsgCtime(String tnId) {
        if (StringUtils.isEmpty(tnId)) {
            return 0;
        }
        Double score = redisTemplate.opsForZSet().score(getBlockMsgKey(), tnId);
        if (score != null) {
            return score;
        }
        return 0;
    }


    public double getBlockLoginCtime(String tnId) {
        if (StringUtils.isEmpty(tnId)) {
            return 0;
        }
        Double score = redisTemplate.opsForZSet().score(getBlockLoginKey(), tnId);
        if (score != null) {
            return score;
        }
        return 0;
    }

    public double getBlockProfileCtime(String tnId) {
        if (StringUtils.isEmpty(tnId)) {
            return 0;
        }
        Double score = redisTemplate.opsForZSet().score(getBlockProfileKey(), tnId);
        if (score != null) {
            return score;
        }
        return 0;
    }

    public long delBlockKeyTnId(String key, String tnId) {
        if (StringUtils.isEmpty(tnId)) {
            return 0;
        }
        Long score = redisTemplate.opsForZSet().remove(key, tnId);
        if (score != null) {
            return score;
        }
        return 0;
    }

    public Set<String> getAllRangeTnId(String key, int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(key, 0, timestamp);
        } catch (Exception e) {
            logger.error("getAllRangeTnId error. key={} timestamp={} {}", key, timestamp, e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    public int getFakeSevenDays(String tnId) {
        try {
            String timeOut = redisTemplate.opsForValue().get(getFakeSevenDaysKey(tnId));
            return timeOut != null ? Integer.parseInt(timeOut) : 0;
        } catch (Exception e) {
            logger.error("getFakeSevenDays error tnId={}, e={}", tnId, e.getMessage(), e);
            return 0;
        }
    }

    private String getBlockMicKey() {
        return "block:mic:tnid";
    }

    private String getBlockRoomFileKey() {
        return "block:room:file:tnid";
    }

    private String getBlockCreateRoomKey() {
        return "block:create:room:tnid";
    }

    private String getBlockMsgKey() {
        return "block:private:msgtnid";
    }

    private String getBlockLoginKey() {
        return "block:device:tnid";
    }

    private String getBlockProfileKey() {
        return "block:update:profile:tnid";
    }

    private String getFakeSevenDaysKey(String tnId) {
        return "str:fake_seven_days:tn_id:" + tnId;
    }

    // 封禁用户发私信消息key
    private String getBlockUserPrivateMsgKey(String uid) {
        return String.format("str:block_user_private_msg:%s", uid);
    }

    @CacheEvict(value = "getBlockUserPrivateMsgStatus", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public void addBlockUserPrivateMsg(String uid, int timeOut) {
        try {
            String key = getBlockUserPrivateMsgKey(uid);
            int timeOutDay = timeOut < 0 ? 3000 : timeOut;
            redisTemplate.opsForValue().set(key, String.valueOf(timeOut), timeOutDay, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addBlockUserPrivateMsg error aid={} timeOut={}, e={}", uid, timeOut, e.getMessage(), e);
        }
    }

    @CacheEvict(value = "getBlockUserPrivateMsgStatus", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public void removeBlockUserPrivateMsgScore(String uid) {
        try {
            String key = getBlockUserPrivateMsgKey(uid);
            redisTemplate.delete(key);
        } catch (Exception e) {
            logger.error("removeBlockUserPrivateMsg error aid={}, e={}", uid, e.getMessage(), e);
        }
    }

    @Cacheable(value = "getBlockUserPrivateMsgStatus", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public int getBlockUserPrivateMsgStatus(String uid) {
        try {
            String timeOut = redisTemplate.opsForValue().get(getBlockUserPrivateMsgKey(uid));
            return timeOut != null ? Integer.parseInt(timeOut) : 0;
        } catch (Exception e) {
            logger.error("getBlockUserPrivateMsgStatus error uid={}, e={}", uid, e.getMessage(), e);
            return 0;
        }
    }


    // 封禁用户发公屏消息key
    private String getBlockUserPublicMsgKey(String uid) {
        return String.format("str:block_user_public_msg:%s", uid);
    }

    @CacheEvict(value = "getBlockUserPublicMsgStatus", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public void addBlockUserPublicMsg(String uid, int timeOut) {
        try {
            String key = getBlockUserPublicMsgKey(uid);
            int timeOutDay = timeOut < 0 ? 3000 : timeOut;
            redisTemplate.opsForValue().set(key, String.valueOf(timeOut), timeOutDay, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addBlockUserPublicMsg error aid={} timeOut={}, e={}", uid, timeOut, e.getMessage(), e);
        }
    }

    @CacheEvict(value = "getBlockUserPublicMsgStatus", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public void removeBlockUserPublicMsg(String uid) {
        try {
            String key = getBlockUserPublicMsgKey(uid);
            redisTemplate.delete(key);
        } catch (Exception e) {
            logger.error("removeBlockUserPublicMsg error aid={}, e={}", uid, e.getMessage(), e);
        }
    }

    @Cacheable(value = "getBlockUserPublicMsgStatus", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public int getBlockUserPublicMsgStatus(String uid) {
        try {
            String timeOut = redisTemplate.opsForValue().get(getBlockUserPublicMsgKey(uid));
            return timeOut != null ? Integer.parseInt(timeOut) : 0;
        } catch (Exception e) {
            logger.error("getBlockUserPublicMsgStatus error uid={}, e={}", uid, e.getMessage(), e);
            return 0;
        }
    }


}
