package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class LuckyDiceRedis {

    private static final Logger logger = LoggerFactory.getLogger(LuckyDiceRedis.class);

    private static final int MIN_POOL = 90;
    private static final int MAX_POOL = 105;

    private static final int EXPIRE_DAY = 3;
    private String expireDate = "";

    private static final String LUCKY_NUM_KEY = "str:luckyDiceNumber";
    private static final String PRIZE_POOL_KEY = "str:luckyDicePrizePool";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 增加今日获奖金额
     */
    public void addWinToday(String uid, int score) {
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        String key = getWinTodayKey(dateStr);
        redisTemplate.opsForHash().increment(key, uid, score);
        if (!dateStr.equals(expireDate)) {
            redisTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
            expireDate = dateStr;
        }
    }

    /**
     * 获取今日获奖金额
     */
    public int getWinToday(String uid) {
        String valueStr = (String) redisTemplate.opsForHash().get(getWinTodayKey(DateHelper.ARABIAN.formatDateInDay()), uid);
        return ObjectUtils.isEmpty(valueStr) ? 0 : Integer.parseInt(valueStr);
    }

    private String getWinTodayKey(String dateStr) {
        return "hash:luckyDiceWin:" + dateStr;
    }

    /**
     * 设置本期幸运数字
     *
     * @param luckyNumList List 内部0,1,2为幸运数字，3为倍数，4为有效期开始时间，5为有效期结束时间
     */
    public void setLuckyNumber(List<Integer> luckyNumList) {
        if (luckyNumList.size() != 6) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        redisTemplate.opsForValue().set(LUCKY_NUM_KEY, JSON.toJSONString(luckyNumList));
    }

    /**
     * 获取本期幸运数字
     *
     * @param allData 是否获取所有数据
     * @return List 内部0,1,2为幸运数字，3为倍数
     */
    public List<Integer> getLuckyNumber(boolean allData) {
        try {
            List<Integer> luckyNumList = JSON.parseObject(redisTemplate.opsForValue().get(LUCKY_NUM_KEY), new TypeReference<List<Integer>>() {
            });
            if (null == luckyNumList || luckyNumList.size() != 6) {
                return null;
            }
            if (allData) {
                return luckyNumList;
            }
            int nowSeconds = DateHelper.getNowSeconds();
            if (nowSeconds >= luckyNumList.get(4) && nowSeconds < luckyNumList.get(5) || (luckyNumList.get(4) == 0 && luckyNumList.get(5) == 0)) {
                // 在有效期内显示，有效期过后消失，消失后不会有幸运数的中奖
                return luckyNumList.subList(0, 4);
            }
            return null;
        } catch (Exception ignore) {
            return null;
        }
    }

    /**
     * 获取今天浮动的奖池抽成
     */
    private double getTodayPoolRate() {
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        String key = "str:luckyDicePoolRate:" + dateStr;
        String poolRateStr = redisTemplate.opsForValue().get(key);
        if (!ObjectUtils.isEmpty(poolRateStr)) {
            return Double.parseDouble(poolRateStr);
        }
        double newPoolRate = ThreadLocalRandom.current().nextInt(MIN_POOL, MAX_POOL) / 100d;
        redisTemplate.opsForValue().set(key, String.valueOf(newPoolRate), 1, TimeUnit.DAYS);
        logger.info("lucky dice init pool rate, date={} rate={}", dateStr, newPoolRate);
        return newPoolRate;
    }

    /**
     * 增加奖池金额
     */
    public void addPrizePool(double score) {
        redisTemplate.opsForValue().increment(PRIZE_POOL_KEY, score * getTodayPoolRate());
    }

    /**
     * 减少奖池金额
     */
    public void reducePrizePool(int delta) {
        redisTemplate.opsForValue().increment(PRIZE_POOL_KEY, (double) -Math.abs(delta));
    }

    /**
     * 获取奖池金额
     */
    public int getPrizePool() {
        String valueStr = redisTemplate.opsForValue().get(PRIZE_POOL_KEY);
        if (null == valueStr) {
            valueStr = "50000";
            redisTemplate.opsForValue().set(PRIZE_POOL_KEY, valueStr);
            logger.info("init lucky dice prize pool.");
        }
        return ObjectUtils.isEmpty(valueStr) ? 0 : Double.valueOf(valueStr).intValue();
    }
}
