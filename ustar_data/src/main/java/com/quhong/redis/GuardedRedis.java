package com.quhong.redis;

import com.quhong.cache.CacheMap;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.dao.GuardedDao;
import com.quhong.mysql.data.GuardedData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/4/19
 */
@Lazy
@Component
public class GuardedRedis {

    private static final Logger logger = LoggerFactory.getLogger(GuardedRedis.class);

    private static final int RANKING_EXPIRE_DAYS = 90; // 守护排行榜过期时间(天)
    private static final int REFRESH_INTERVAL_TIME = 85 * 24 * 60 * 60; // 守护排行榜刷新间隔时间(秒)
    public static final int MAX_RANK_SIZE = 130; // 排行榜数据只保留前130
    private static final long CACHE_TIME_MILLIS = 24 * 60 * 60 * 1000L;

    public static final String GUARD_LOCK_KEY = "guard_send_gift_"; // 分布式锁

    private final CacheMap<String, Integer> cacheMap;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    @Resource
    private GuardedDao guardedDao;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public GuardedRedis() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    /**
     * 保存每日守护记录
     */
    public void saveDailyGuardRecord(String uid, String aid) {
        String key = getDailyGuardKey();
        try {
            clusterTemplate.opsForSet().add(key, uid + aid);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveDailyGuardRecord error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
    }

    /**
     * 校验是否完成每日守护
     */
    public boolean hasCompletedDailyGuard(String uid, String aid) {
        String key = getDailyGuardKey();
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(key, uid + aid));
        } catch (Exception e) {
            logger.error("hasCompletedDailyGuard error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取分数
     */
    public long getGuardRankingScore(String uid, String aid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getGuardRankKey(uid), aid);
            return score != null ? score.longValue() : 0L;
        } catch (Exception e) {
            logger.error("getGuardRankingScore error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 清空排行榜
     */
    public void delGuardRankingScore(String uid) {
        try{
            clusterTemplate.delete(getGuardRankKey(uid));
        } catch (Exception e) {
            logger.error("delGuardRankingScore error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 获取带分数的守护排行榜
     */
    public Map<String, Long> getGuardRankingMap(String uid) {
        Map<String, Long> linkedRankMap = new LinkedHashMap<>();
        String key = getGuardRankKey(uid);
        try {
            int refreshRankTime = getRefreshRankTime(uid);
            if (refreshRankTime == 0 || DateHelper.getNowSeconds() > refreshRankTime + REFRESH_INTERVAL_TIME) {
                // 刷新排行榜
                delGuardRankingScore(uid);
                List<GuardedData> guarderRankList = guardedDao.getGuarderRankList(uid, MAX_RANK_SIZE);
                if (!CollectionUtils.isEmpty(guarderRankList)) {
                    for (GuardedData data : guarderRankList) {
                        updateGuardRankingScore(data.getUid(), data.getGuardian(), data.getGuardValue());
                    }
                }
                // 保存刷新时间
                saveRefreshRankTime(uid);
            }
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, MAX_RANK_SIZE);
            if (null == rangeWithScores) {
                return linkedRankMap;
            }
            for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                    continue;
                }
                linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
            }
        } catch (Exception e) {
            logger.error("getGuardRankingMap error. uid={} {}", uid, e.getMessage(), e);
        }
        return linkedRankMap;
    }

    /**
     * 更新排行榜
     */
    public void updateGuardRank(String uid, String aid, long score) {
        String key = getGuardRankKey(uid);
        try {
            // 获取第130名成绩
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, MAX_RANK_SIZE - 1, MAX_RANK_SIZE - 1);
            if (rangeWithScores == null) {
                updateGuardRankingScore(uid, aid, score);
            } else {
                for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                    if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                        logger.info("tenthPlaceScore=0");
                        return;
                    }
                    long tenthPlaceScore = rangeWithScore.getScore().longValue();
                    logger.info("score={}, tenthPlaceScore={}", score, tenthPlaceScore);
                    if (score <= tenthPlaceScore) {
                        return;
                    }
                }
                updateGuardRankingScore(uid, aid, score);
                if (getGuardRankSize(uid) > MAX_RANK_SIZE) {
                    // 删除前130之后的数据
                    removeRankData(uid);
                }
            }
        } catch (Exception e) {
            logger.error("updateGuardRank error. uid={} aid={} score={} {}", uid, aid, score, e.getMessage(), e);
        }
    }

    private void removeRankData(String uid) {
        String key = getGuardRankKey(uid);
        // 获取第131名的成绩
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, MAX_RANK_SIZE, MAX_RANK_SIZE);
        if (rangeWithScores == null) {
            return;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getScore()) {
                return;
            }
            clusterTemplate.opsForZSet().removeRangeByScore(key, 0, rangeWithScore.getScore());
        }
    }

    /**
     * 更新排行榜分数
     */
    public void updateGuardRankingScore(String uid, String aid, long score) {
        try {
            String key = getGuardRankKey(uid);
            double rankScore = new BigDecimal(score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrGuardRankingScore. uid={} aid={} score={} total={}", uid, aid, score, rankScore);
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            clusterTemplate.expire(key, RANKING_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incrGuardRankingScore error. uid={} aid={} score={} {}", uid, aid, score, e.getMessage(), e);
        }
    }

    /**
     * 获取排行榜长度
     */
    public int getGuardRankSize(String uid) {
        String cacheKey = "guardRankSize_" + uid;
        int rankSize = 0;
        if (cacheMap.hasData(cacheKey)) {
            rankSize = cacheMap.getData(cacheKey);
            if (rankSize > MAX_RANK_SIZE) {
                cacheMap.cacheData(cacheKey, MAX_RANK_SIZE);
            }
            return rankSize;
        }
        try {
            Long size = clusterTemplate.opsForZSet().size(getGuardRankKey(uid));
            rankSize = size != null ? size.intValue() : 0;
            cacheMap.cacheData(cacheKey, rankSize);
            return rankSize;
        } catch (Exception e) {
            logger.error("getGuardRankSize error. uid={} {}", uid, e.getMessage(), e);
            return rankSize;
        }
    }
    
    private void saveRefreshRankTime(String uid) {
        String key = getRefreshRankTimeKey();
        try {
            clusterTemplate.opsForZSet().add(key, uid, DateHelper.getNowSeconds());
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveRefreshRankTime error. uid={} {}", uid, e.getMessage(), e);
        }
    }


    private int getRefreshRankTime(String uid) {
        String cacheKey = "refreshRankTime_" + uid;
        if (cacheMap.hasData(cacheKey)) {
            return cacheMap.getData(cacheKey);
        }
        try {
            Double score = clusterTemplate.opsForZSet().score(getRefreshRankTimeKey(), uid);
            int refreshTime = score != null ? score.intValue() : 0;
            cacheMap.cacheData(cacheKey, refreshTime);
            return refreshTime;
        } catch (Exception e) {
            logger.error("getRefreshRankTime error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    private String getDailyGuardKey() {
        return "set:dailyGuard_" + DateHelper.ARABIAN.formatDateInDay();
    }

    private String getGuardRankKey(String uid) {
        return "zset:guardRank_" + uid;
    }

    private String getRefreshRankTimeKey() {
        return "zset:refreshRankTime";
    }
}
