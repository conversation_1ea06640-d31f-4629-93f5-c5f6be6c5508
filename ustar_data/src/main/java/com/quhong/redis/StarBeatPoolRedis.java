package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.StarBeatPoolRewardData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class StarBeatPoolRedis {
    private final static Logger logger = LoggerFactory.getLogger(StarBeatPoolRedis.class);
    public final static int LEVEL_0 = 0;
    public final static int LEVEL_1 = 1;
    public final static int LEVEL_2 = 2;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    private String dateStr = "";

    public void savePoolReward(StarBeatPoolRewardData data, int type) {
        try {
            String json = JSON.toJSONString(data);
            if (!StringUtils.isEmpty(json)) {
                clusterRedis.opsForValue().set(getRewardKey(type), json);
            }
        } catch (Exception e) {
            logger.error("saveGameStatus error msg={} ", e.getMessage(), e);
        }
    }


    public StarBeatPoolRewardData getPoolReward(int type) {
        try {
            String json = clusterRedis.opsForValue().get(getRewardKey(type));
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseObject(json, StarBeatPoolRewardData.class);
        } catch (Exception e) {
            logger.error("getGameStatus error msg={} ", e.getMessage(), e);
            return null;
        }
    }


    private String getRewardKey(int type) {
        return "str:star:beat:reward:" + type;
    }


    private String getPrizeIndexKey() {
        return "str:starBeat:prizeIndex";
    }

    public int getPrizeIndexNum() {
        try{
            String score = clusterRedis.opsForValue().get(getPrizeIndexKey());
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.error("getPrizeIndex error e={}", e.getMessage(), e);
        }
        return 0;
    }

    public int incPrizeIndexNum() {
        try {
            Long afterNum = clusterRedis.opsForValue().increment(getPrizeIndexKey());
            return afterNum == null ? 0 : afterNum.intValue();
        } catch (Exception e) {
            logger.error("incPrizeIndexNum error e={}", e.getMessage(), e);
        }
        return 0;
    }
}
