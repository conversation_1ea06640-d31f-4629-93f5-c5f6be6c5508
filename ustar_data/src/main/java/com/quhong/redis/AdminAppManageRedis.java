package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

@Lazy
@Component
public class AdminAppManageRedis {
    private static final Logger logger = LoggerFactory.getLogger(AdminAppManageRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    public int addAreaBlackUser(String uid) {
        try {
            Long size = clusterTemplate.opsForSet().add(getAreaBlackUser(), uid);
//            clusterTemplate.expire(getAreaBlackUser(), 90, TimeUnit.DAYS);
            return size != null ? size.intValue():0;
        } catch (Exception e) {
            logger.info("addAreaBlackUser error uid={} e={}", uid, e);
            return 0;
        }
    }

    public Set<String> getAllAreaBlackUser() {
        try {
            return clusterTemplate.opsForSet().members(getAreaBlackUser());
        } catch (Exception e) {
            logger.error("get all area black user error {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }


    public int delAreaBlackUser(String uid) {
        try {
            Long size = clusterTemplate.opsForSet().remove(getAreaBlackUser(), uid);
//            clusterTemplate.expire(getAreaBlackUser(), 90, TimeUnit.DAYS);
            return size != null ? size.intValue():0;
        } catch (Exception e) {
            logger.info("delAreaBlackUser error uid={} e={}", uid, e);
            return 0;
        }
    }


    public void addUnpaidBill(String uid) {
        try {
            clusterTemplate.opsForSet().add(getUnpaidBill(), uid);
//            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save user is in pk game. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public boolean isUnpaidBill(String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getUnpaidBill(), uid));
        } catch (Exception e) {
            logger.error("isUnpaidBill. uid={} {}", uid, e.getMessage(), e);
            return false;
        }
    }

    public void removeUnpaidBill(String uid) {
        try {
            clusterTemplate.opsForSet().remove(getUnpaidBill(), uid);
        } catch (Exception e) {
            logger.error("removeUnpaidBill error. uid={} {}", uid, e.getMessage(), e);
        }
    }


    private String getAreaBlackUser() {
        // key兼容单点迁移集群的
        return "area_black_user";
    }

    private String getUnpaidBill() {
        // key兼容单点迁移集群的
        return "set:unpaid_bill";
    }


}
