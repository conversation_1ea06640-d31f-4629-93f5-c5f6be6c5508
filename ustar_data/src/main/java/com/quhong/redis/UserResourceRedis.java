package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
@Component
@Lazy
public class UserResourceRedis {

    private static final Logger logger = LoggerFactory.getLogger(UserResourceRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public void saveUserWearEntryEffect(String uid, String url) {

    }

    private String getWearEntryEffectKey() {
        return "hash:wearEntryEffect";
    }
}
