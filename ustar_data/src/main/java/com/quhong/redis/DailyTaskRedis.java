package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Lazy
@Component
public class DailyTaskRedis {

    private static final Logger logger = LoggerFactory.getLogger(DailyTaskRedis.class);

    private static final String DAILY_TASK_FINISH_PRE = "daily_task_finish";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public boolean checkFinish(String uid, String date) {
        String finish = redisTemplate.opsForValue().get(getTaskFinishKey(uid, date));
        if (finish != null) {
            int result = Integer.parseInt(finish);
            return result > 0;
        }
        return false;
    }

    private String getTaskFinishKey(String uid, String date) {
        return DAILY_TASK_FINISH_PRE + "_" + uid + "_" + date;
    }

}
