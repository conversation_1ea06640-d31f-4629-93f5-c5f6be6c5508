package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Component
@Lazy
public class RechargeMoneyRedis {

    private static final Logger logger = LoggerFactory.getLogger(RechargeMoneyRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public BigDecimal getRechargeAmount(String uid) {
        String value = redisTemplate.opsForValue().get(getRechargeKey(uid));
        if (value == null || "".equals(value)) {
            return new BigDecimal("0");
        }
        JSONObject object = JSON.parseObject(value);
        BigDecimal rechargeMoney = new BigDecimal(String.valueOf(object.get("recharge_money")));
        return rechargeMoney;
    }

    private String getRechargeKey(String uid) {
        return "str:user_honor_recharge:" + uid;
    }
}
