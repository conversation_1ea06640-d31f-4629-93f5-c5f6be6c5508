package com.quhong.redis;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.utils.FamilyBeautifulRidUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/6/2
 */
@Lazy
@Component
public class GenFamilyRidRedis {

    private static final Logger logger = LoggerFactory.getLogger(GenFamilyRidRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public void setGenFamilyRid(int rid) {
        try {
            redisTemplate.opsForValue().set(getGenFamilyRidKey(), rid + "");
        } catch (Exception e) {
            logger.error("setGenFamilyRid error. rid={} {}", rid, e.getMessage(), e);
        }
    }

    public int incGenFamilyRid() {
        try {
            Long rid = redisTemplate.opsForValue().increment(getGenFamilyRidKey());
            return rid != null ? rid.intValue() : 0;
        } catch (Exception e) {
            logger.error("incGenFamilyRid error. {}", e.getMessage(), e);
            return 0;
        }
    }

    private String getGenFamilyRidKey () {
        return "str:register_gen_family_rid";
    }

    public int getGenFamilyRid() {
        try (DistributeLock lock = new DistributeLock("genFamilyRid")) {
            lock.lock();
            int rid = incGenFamilyRid();
            while (FamilyBeautifulRidUtils.isBeautifulRid(rid + "") || rid < FamilyBeautifulRidUtils.MIN_RID) {
                rid++;
            }
            setGenFamilyRid(rid);
            return rid;
        } catch (Exception e) {
            logger.error("getGenFamilyRid error. {}", e.getMessage(), e);
        }
        return 0;
    }
}
