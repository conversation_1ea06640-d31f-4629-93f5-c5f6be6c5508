package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class NationalDayV2Redis {
    private static final Logger logger = LoggerFactory.getLogger(NationalDayV2Redis.class);
    private static final int COMMON_EXPIRE_DAYS = 45;
    private static final int FREE_NUM_EXPIRE_DAYS = 45;
    private String rankingExpireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String nationalDayJoinV2Key(String activityId) {
        return "set:national_day_join_v2:" + activityId;
    }

    public int getJoinUserNums(String activityId) {
        try {
            Long joinUserNums = clusterTemplate.opsForSet().size(nationalDayJoinV2Key(activityId));
            return ObjectUtils.isEmpty(joinUserNums) ? 0 : joinUserNums.intValue();
        } catch (Exception e) {
            logger.info("getJoinUserNums error activityId={}  score={}", activityId, e);
            return 0;
        }
    }

    public void setJoinUserNums(String activityId, String uid) {
        try {

            boolean joinUserFlag = Boolean.TRUE.equals(clusterTemplate.hasKey(nationalDayJoinV2Key(activityId)));
            clusterTemplate.opsForSet().add(nationalDayJoinV2Key(activityId), uid);
            if (!joinUserFlag) {
                clusterTemplate.expire(nationalDayJoinV2Key(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("setJoinUserNums error activityId={}  score={}", activityId, e);
        }
    }

    public boolean isJoinUser(String activityId, String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(nationalDayJoinV2Key(activityId), uid));
        } catch (Exception e) {
            logger.info("setJoinUserNums error activityId={}  score={}", activityId, e);
        }
        return false;
    }


    private String nationalDayV2Like(String activityId) {
        return "set:national_day_v2_like:" + activityId;
    }

    public int getLikeUserNums(String activityId) {
        try {
            Long likeUserNums = clusterTemplate.opsForSet().size(nationalDayV2Like(activityId));
            return ObjectUtils.isEmpty(likeUserNums) ? 0 : likeUserNums.intValue();
        } catch (Exception e) {
            logger.info("getLikeUserNums error activityId={}  score={}", activityId, e);
            return 0;
        }
    }

    public void setLikeUserNums(String activityId, String uid) {
        try {

            boolean likeUserFlag = Boolean.TRUE.equals(clusterTemplate.hasKey(nationalDayV2Like(activityId)));
            clusterTemplate.opsForSet().add(nationalDayV2Like(activityId), uid);
            if (!likeUserFlag) {
                clusterTemplate.expire(nationalDayV2Like(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("setLikeUserNums error activityId={}  score={}", activityId, e);
        }
    }

    public boolean isLikeUser(String activityId, String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(nationalDayV2Like(activityId), uid));
        } catch (Exception e) {
            logger.info("isLikeUser error activityId={}  score={}", activityId, e);
        }
        return false;
    }

    // 奖池key
    private String getDrawPoolKey(String activityId) {
        return "list:national_day_pool:" + activityId;
    }

    public int getPoolSize(String activityId) {
        try {
            Long poolSize = clusterTemplate.opsForList().size(getDrawPoolKey(activityId));
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getJoinUserNums error activityId={}  score={}", activityId, e);
            return 0;
        }
    }


    public void initPoolSize(String activityId, List<String> rewardConfigList) {
        try {
            clusterTemplate.opsForList().rightPushAll(getDrawPoolKey(activityId), rewardConfigList);
            clusterTemplate.expire(getDrawPoolKey(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            logger.info("initPoolSize error activityId={}  score={}", activityId, e);
        }
    }

    public void deletePoolSize(String activityId) {
        try {
            clusterTemplate.delete(getDrawPoolKey(activityId));
        } catch (Exception e) {
            logger.info("deletePoolSize error activityId={}  score={}", activityId, e);
        }
    }

    // 抽奖
    public String drawLuckyKey(String activityId) {
        try {
            String drawLuckyKey = clusterTemplate.opsForList().leftPop(getDrawPoolKey(activityId));
            return drawLuckyKey != null ? drawLuckyKey : "";
        } catch (Exception e) {
            logger.info("getJoinUserNums error activityId={}  score={}", activityId, e);
            return "";
        }
    }


    /**
     * @param activityId 活动名称
     * @return key
     */
    private String getNationalDayV2ActivityKey(String activityId) {
        return "zset:NationalDayV2:" + activityId;
    }

    /**
     * 获取分数
     */
    public int getNationalDayV2RankingScore(String activityId, String aid) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getNationalDayV2ActivityKey(activityId), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getNationalDayV2RankingScore error activityId={} aid={} rankType={}", activityId, aid, e);
            return 0;
        }
    }

    public void incrNationalDayV2RankingScore(String activityId, String aid, int score) {
        try {
            String key = getNationalDayV2ActivityKey(activityId);
            int curScore = getNationalDayV2RankingScore(activityId, aid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(rankingExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                rankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrNationalDayV2RankingScore error activityId={} aid={} giftId={} score={}", activityId, aid, score, e);
        }
    }


    private String nationalDayAwardV2Key(String activityId) {
        return "set:national_day_award_v2:" + activityId;
    }

    public void setAwardV2User(String activityId, String uid) {
        try {

            String awardKey = nationalDayAwardV2Key(activityId);

            boolean awardUserFlag = Boolean.TRUE.equals(clusterTemplate.hasKey(awardKey));
            clusterTemplate.opsForSet().add(awardKey, uid);
            if (!awardUserFlag) {
                clusterTemplate.expire(awardKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("setAwardV2User error activityId={}  score={}", activityId, e);
        }
    }

    public boolean isAwardUser(String activityId, String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(nationalDayAwardV2Key(activityId), uid));
        } catch (Exception e) {
            logger.info("isAwardUser error activityId={}  score={}", activityId, e);
        }
        return true;
    }


    //助力相关key

    private String nationalDayV2HelpTotalUserKey(String activityId) {
        return "set:national_day_v2_help_total_user:" + activityId + ":" + DateHelper.ARABIAN.formatDateInDay();
    }

    public void setHelpTotalUser(String activityId, String uid) {
        try {

            String dailyKey = nationalDayV2HelpTotalUserKey(activityId);
            boolean tnKeyUserFlag = Boolean.TRUE.equals(clusterTemplate.hasKey(dailyKey));
            clusterTemplate.opsForSet().add(dailyKey, uid);
            if (!tnKeyUserFlag) {
                clusterTemplate.expire(dailyKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("setHelpDailyUser error activityId={}  error={}", activityId, e);
        }
    }

    public void removeHelpTotalUser(String activityId, String uid) {
        try {
            String dailyKey = nationalDayV2HelpTotalUserKey(activityId);
            clusterTemplate.opsForSet().remove(dailyKey, uid);
        } catch (Exception e) {
            logger.info("removeHelpDailyUser error activityId={}  error={}", activityId, e);
        }
    }

    public boolean isHelpTotalUser(String activityId, String uid) {
        try {
            String dailyKey = nationalDayV2HelpTotalUserKey(activityId);
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(dailyKey, uid));
        } catch (Exception e) {
            logger.info("isHelpDailyUser error activityId={}  error={}", activityId, e);
        }
        return false;
    }


    // 对
    private String nationalDayV2HelpTnKey(String activityId) {
        return "set:national_day_v2_help_tn:" + activityId + ":" + DateHelper.ARABIAN.formatDateInDay();
    }

    public void setHelpTnUser(String activityId, String tnId) {
        try {

            String tnKey = nationalDayV2HelpTnKey(activityId);
            boolean tnKeyUserFlag = Boolean.TRUE.equals(clusterTemplate.hasKey(tnKey));
            clusterTemplate.opsForSet().add(tnKey, tnId);
            if (!tnKeyUserFlag) {
                clusterTemplate.expire(tnKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("setHelpTnUser error activityId={}  error={}", activityId, e);
        }
    }


    public void removeHelpTnUser(String activityId, String tnId) {
        try {
            String tnKey = nationalDayV2HelpTnKey(activityId);
            clusterTemplate.opsForSet().remove(tnKey, tnId);
        } catch (Exception e) {
            logger.info("setHelpTnUser error activityId={}  error={}", activityId, e);
        }
    }


    public boolean isHelpTnUser(String activityId, String tnId) {
        try {
            String tnKey = nationalDayV2HelpTnKey(activityId);
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(tnKey, tnId));
        } catch (Exception e) {
            logger.info("isHelpTnUser error activityId={}  error={}", activityId, e);
        }
        return false;
    }


    // 我的助力用户集合
    private String nationalDayV2HelpKey(String activityId, String aid) {
        return "set:national_day_v2_help:" + activityId + ":" + aid;
    }

    public Set<String> getHelpUser(String activityId, String aid) {
        try {

            String helpKey = nationalDayV2HelpKey(activityId, aid);
            return clusterTemplate.opsForSet().members(helpKey);

        } catch (Exception e) {
            logger.info("getHelpUser error activityId={}  score={}", activityId, e);
        }
        return Collections.emptySet();
    }

    public void setHelpUser(String activityId, String aid, String uid) {
        try {

            String helpKey = nationalDayV2HelpKey(activityId, aid);
            boolean tnKeyUserFlag = Boolean.TRUE.equals(clusterTemplate.hasKey(helpKey));
            clusterTemplate.opsForSet().add(helpKey, uid);
            if (!tnKeyUserFlag) {
                clusterTemplate.expire(helpKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("setHelpUser error activityId={}  score={}", activityId, e);
        }
    }

    public boolean isHelpUser(String activityId, String aid, String uid) {
        try {
            String helpKey = nationalDayV2HelpKey(activityId, aid);
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(helpKey, uid));
        } catch (Exception e) {
            logger.info("isHelpUser error activityId={}  score={}", activityId, e);
        }
        return false;
    }

    public int getHelpUserNums(String activityId, String aid) {
        try {
            String helpKey = nationalDayV2HelpKey(activityId, aid);
            Long helpUserNums = clusterTemplate.opsForSet().size(helpKey);
            return ObjectUtils.isEmpty(helpUserNums) ? 0 : helpUserNums.intValue();
        } catch (Exception e) {
            logger.info("getHelpUserNums error activityId={}  score={}", activityId, e);
            return 0;
        }
    }

    public boolean removerOneHelpUser(String activityId, String uid,String aid) {
        try {
            String helpKey = nationalDayV2HelpKey(activityId, uid);
            clusterTemplate.opsForSet().remove(helpKey, aid);
        } catch (Exception e) {
            logger.info("isHelpUser error activityId={}  score={}", activityId, e);
        }
        return false;
    }

    public String getNationalDayV3InviteHelpValue(String activityId, String uid, String aid) {
        try {
            String value = clusterTemplate.opsForValue().get(nationalDayV3InviteHelpKey(activityId,uid,aid));
            return value != null ? value : "";
        } catch (Exception e) {
            logger.info("getCommonStrValue error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return "";
    }

    public void setNationalDayV3InviteHelp(String activityId, String uid, String aid) {
        try {
            String key = nationalDayV3InviteHelpKey(activityId,uid,aid);
            clusterTemplate.opsForValue().set(key, "1", ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setCommonStrScore error activityId={}  e={}", activityId, e.getMessage(), e);
        }
    }

    private String nationalDayV3InviteHelpKey(String activityId, String uid, String aid) {
        return "str:national_day_v3_invite_help:" + activityId + "u:" + uid + "a:" + aid;
    }



}
