package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.GameExtraConfig;
import com.quhong.data.vo.NoHonorExpConfigVO;
import com.quhong.monitor.MonitorSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@Component
@Lazy
public class OperationConfigRedis {
    private static final Logger logger = LoggerFactory.getLogger(OperationConfigRedis.class);
    private static final String EXTRA_GAME_CONFIG = "hash:extraGameConfig";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private MonitorSender monitorSender;

    private String getVideoRoomKey() {
        return "zet:operation:videoRoom";
    }

    public void addVideoOperationRoom(String roomId) {
        try {
            redisTemplate.opsForSet().add(getVideoRoomKey(), roomId);
        } catch (Exception e) {
            logger.error("addVideoRoom error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public void delVideoOperationRoom(String roomId) {
        try {
            redisTemplate.opsForSet().remove(getVideoRoomKey(), roomId);
        } catch (Exception e) {
            logger.error("delVideoRoom error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public Set<String> getAllVideoOperationRoom() {
        try {
            return redisTemplate.opsForSet().members(getVideoRoomKey());
        } catch (Exception e) {
            logger.error("getAllVideoOperationRoom error. {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    public void addTopMomentUser(String uid) {
        try {
            redisTemplate.opsForSet().add("set:topMomentUser", uid);
        } catch (Exception e) {
            logger.error("add top moment user error. uid={}", uid, e);
        }
    }

    public void delTopMomentUser(String uid) {
        try {
            redisTemplate.opsForSet().remove("set:topMomentUser", uid);
        } catch (Exception e) {
            logger.error("del top moment user error. uid={}", uid, e);
        }
    }

    public Set<String> listTopMomentUser() {
        try {
            return redisTemplate.opsForSet().members("set:topMomentUser");
        } catch (Exception e) {
            logger.error("list top moment user error.", e);
            return Collections.emptySet();
        }
    }

    public boolean isTopMomentUser(String uid) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember("set:topMomentUser", uid));
        } catch (Exception e) {
            logger.error("is top moment user error. uid={}", uid, e);
            return false;
        }
    }

    public void setMomentScoreWeight(JSONObject jsonObject) {
        if (null == jsonObject) {
            return;
        }
        redisTemplate.opsForValue().set("str:momentScoreWeight", jsonObject.toJSONString());
    }

    public JSONObject getMomentScoreWeight() {
        try {
            String jsonStr = redisTemplate.opsForValue().get("str:momentScoreWeight");
            logger.info("getMomentScoreWeight jsonStr={}", jsonStr);
            if (null == jsonStr) {
                return new JSONObject();
            }
            return (JSONObject) JSONObject.parse(jsonStr);
        } catch (Exception e) {
            logger.error("get moment recommend score weight error.");
            return new JSONObject();
        }
    }

    public void setForYouScoreWeight(JSONObject jsonObject) {
        if (null == jsonObject) {
            return;
        }
        redisTemplate.opsForValue().set("str:forYouScoreWeight", jsonObject.toJSONString());
    }

    public JSONObject getForYouScoreWeight() {
        try {
            String jsonStr = redisTemplate.opsForValue().get("str:forYouScoreWeight");
            logger.info("getForYouScoreWeight jsonStr={}", jsonStr);
            if (null == jsonStr) {
                return new JSONObject();
            }
            return (JSONObject) JSONObject.parse(jsonStr);
        } catch (Exception e) {
            logger.error("get for you list score weight error.");
            return new JSONObject();
        }
    }

    public void setRobotConfig(JSONObject jsonObject) {
        if (null == jsonObject) {
            return;
        }
        redisTemplate.opsForValue().set("str:robotConfig", jsonObject.toJSONString());
    }

    public JSONObject getRobotConfig() {
        try {
            String jsonStr = redisTemplate.opsForValue().get("str:robotConfig");
            logger.info("getRobotConfig jsonStr={}", jsonStr);
            if (null == jsonStr) {
                return new JSONObject();
            }
            return (JSONObject) JSONObject.parse(jsonStr);
        } catch (Exception e) {
            logger.error("get robot config error.");
            return new JSONObject();
        }
    }

    public NoHonorExpConfigVO getConfigData() {
        String value = redisTemplate.opsForValue().get(getNoHonorExpKey());
        if (value != null) {
            NoHonorExpConfigVO vo = new NoHonorExpConfigVO();
            String[] timeArr = value.split("-");
            vo.setStartTime(Integer.parseInt(timeArr[0]));
            vo.setEndTime(Integer.parseInt(timeArr[1]));
            vo.setStatus(Integer.parseInt(timeArr[2]));
            return vo;
        } else {
            return null;
        }

    }


    public void updateData(NoHonorExpConfigVO dto) {

        int startTime = dto.getStartTime();
        int endTime = dto.getEndTime();
        int status = dto.getStatus();
        String discountFormat = String.format("%s-%s-%s", startTime, endTime, status);
        redisTemplate.opsForValue().set(getNoHonorExpKey(), discountFormat);

        String startFormat = DateHelper.ARABIAN.formatDateTime(new Date(startTime * 1000L));
        String endFormat = DateHelper.ARABIAN.formatDateTime(new Date(endTime * 1000L));
        String statusSwitch = status == 1 ? "开" : "关";
        String warnFormat = String.format("【GMT+3沙特时间】开始时间: %s, 结束时间: %s,开关状态: %s",
                startFormat, endFormat, statusSwitch);
        if (ServerConfig.isProduct()) {
            monitorSender.info("diamonds", "线上不计荣誉积分配置更新", warnFormat);
        } else {
            monitorSender.info("ustar_java_exception", "测试环境不计荣誉积分配置更新", warnFormat);
        }
    }

    private String getNoHonorExpKey() {
        return "str:nohonor:exp:config:key";
    }

    public List<GameExtraConfig> listGameExtraConfig() {
        List<GameExtraConfig> configList = new ArrayList<>();
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(EXTRA_GAME_CONFIG);
        for (Object value : entries.values()) {
            configList.add(JSONObject.parseObject(String.valueOf(value), GameExtraConfig.class));
        }
        configList.sort(Comparator.comparing(GameExtraConfig::getGameType));
        return configList;
    }

    public void addGameExtraConfig(GameExtraConfig configData) {
        redisTemplate.opsForHash().put(EXTRA_GAME_CONFIG, String.valueOf(configData.getGameType()), JSONObject.toJSONString(configData));
    }

    private GameExtraConfig getGameExtraConfig(int gameType) {
        return JSONObject.parseObject((String) redisTemplate.opsForHash().get(EXTRA_GAME_CONFIG, String.valueOf(gameType)), GameExtraConfig.class);
    }

    /**
     * 获取游戏额外奖励配置
     *
     * @param gameType 2Ludo、3Umo、4消消乐、5多米诺、20LuckyWheel、21猜拳
     */
    public int randomExtraGameReward(String uid, int gameType, int reward) {
        GameExtraConfig config = getGameExtraConfig(gameType);
        if (null == config || 0 == config.getEnable()) {
            return reward;
        }
        if (!ObjectUtils.isEmpty(config.getStartTime()) && !ObjectUtils.isEmpty(config.getEndTime())) {
            try {
                LocalTime startTime = LocalTime.parse(config.getStartTime());
                LocalTime endTime = LocalTime.parse(config.getEndTime());
                LocalTime nowTime = ZonedDateTime.now(ZoneId.of("UTC+3")).toLocalTime();
                if (!isWithinRange(nowTime, startTime, endTime)) {
                    return reward;
                }
            } catch (Exception e) {
                logger.error("date str format error. startTime={} endTime={}", config.getStartTime(), config.getEndTime());
                return reward;
            }
        }
        if (ThreadLocalRandom.current().nextInt(0, 101) <= config.getProbability()) {
            int extraTimes = config.getExtraTimes();
            if (0 == extraTimes) {
                // 2~5倍随机
                extraTimes = ThreadLocalRandom.current().nextInt(2, 6);
            }
            logger.info("randomExtraGameReward uid={} gameType={} reward={} times={}", uid, gameType, reward, extraTimes);
            return reward * extraTimes;
        }
        return reward;
    }

    public boolean isWithinRange(LocalTime time, LocalTime startTime, LocalTime endTime) {
        return !time.isBefore(startTime) && !time.isAfter(endTime);
    }

    public JSONObject getAllNewOldUserScoreWeight() {
        try {
            String jsonStr = redisTemplate.opsForValue().get("str:allNewOldUserScoreWeight");
            logger.info("allNewOldUserScoreWeight jsonStr={}", jsonStr);
            if (null == jsonStr) {
                return new JSONObject();
            }
            return (JSONObject) JSONObject.parse(jsonStr);
        } catch (Exception e) {
            logger.error("get getAllNewOldUserScoreWeight list score weight error.");
            return new JSONObject();
        }
    }

    public JSONObject getAllNewOldUserMeetScoreWeight() {
        try {
            String jsonStr = redisTemplate.opsForValue().get("str:getAllNewOldUserMeetScoreWeight");
            logger.info("getAllNewOldUserMeetScoreWeight jsonStr={}", jsonStr);
            if (null == jsonStr) {
                return new JSONObject();
            }
            return (JSONObject) JSONObject.parse(jsonStr);
        } catch (Exception e) {
            logger.error("getAllNewOldUserMeetScoreWeight score weight error.");
            return new JSONObject();
        }
    }

    public JSONObject getAllNewRookieRoomScoreWeight() {
        try {
            String jsonStr = redisTemplate.opsForValue().get("str:allNewRookieRoomScoreWeight");
            logger.info("allNewRookieRoomScoreWeight jsonStr={}", jsonStr);
            if (null == jsonStr) {
                return new JSONObject();
            }
            return (JSONObject) JSONObject.parse(jsonStr);
        } catch (Exception e) {
            logger.error("get getAllNewRookieRoomScoreWeight list score weight error.");
            return new JSONObject();
        }
    }

    public JSONObject getAllNewRoomScoreWeight() {
        try {
            String jsonStr = redisTemplate.opsForValue().get("str:allNewRoomScoreWeight");
            logger.info("getAllNewRoomScoreWeight jsonStr={}", jsonStr);
            if (null == jsonStr) {
                return new JSONObject();
            }
            return (JSONObject) JSONObject.parse(jsonStr);
        } catch (Exception e) {
            logger.error("get getAllNewRoomScoreWeight list score weight error.");
            return new JSONObject();
        }
    }

    public void saveAllNewRoomScoreWeight(String jsonStr) {
        redisTemplate.opsForValue().set("str:allNewRoomScoreWeight", jsonStr);
    }

    public Set<String> highQualityRoomList() {
        return redisTemplate.opsForSet().members("set:highQualityRoomList");
    }

    public void addQualityRoomList(String roomId) {
        redisTemplate.opsForSet().add("set:highQualityRoomList", roomId);
    }
    public Long addAllQualityRoomByRedis(Set<String> addRooms) {
        String[] toArray = addRooms.toArray(new String[0]);
        return redisTemplate.opsForSet().add("set:highQualityRoomList", toArray);
    }

    public void removeQualityRoomList(String roomId) {
        redisTemplate.opsForSet().remove("set:highQualityRoomList", roomId);
    }

    public Set<String> bigRRoomList() {
        return redisTemplate.opsForSet().members("set:bigRRoomList");
    }

    public void addBigRRoomList(String roomId) {
        redisTemplate.opsForSet().add("set:bigRRoomList", roomId);
    }

    public void removeBigRRoomList(String roomId) {
        redisTemplate.opsForSet().remove("set:bigRRoomList", roomId);
    }

    public Long addAllBigRRoomByRedis(Set<String> addRooms) {
        String[] toArray = addRooms.toArray(new String[0]);
        return redisTemplate.opsForSet().add("set:bigRRoomList", toArray);
    }
}
