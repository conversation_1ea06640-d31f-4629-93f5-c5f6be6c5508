package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.PopularListSimplyVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class FcmMsgRedis {

    private static final Logger logger = LoggerFactory.getLogger(FcmMsgRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    /**
     * 推送逻辑: 房间上麦8人时推送fcm给关注房间的用户, 12小时冷却
     * 被推送用户: 关注房间的用户
     */
    private String getPushFollowRoomKey(String roomId) {
        return String.format("str:PushFollowRoom:%s", roomId);
    }

    public void setPushFollowRoomStatus(String roomId, int timeout) {
        try {
            String key = getPushFollowRoomKey(roomId);
            clusterRedis.opsForValue().set(key, String.valueOf(timeout));
            clusterRedis.expire(key, timeout, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("setPushFollowRoomStatus error: {}", e.getMessage(), e);
        }
    }


    public int getPushFollowRoomStatus(String roomId) {
        try {
            String timeout = clusterRedis.opsForValue().get(getPushFollowRoomKey(roomId));
            return timeout == null ? 0 : Integer.parseInt(timeout);
        } catch (Exception e) {
            logger.error("getPushFollowRoomStatus error: {}", e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 推送逻辑: 好友上麦时推送fcm给7天之前加的好友, 4小时冷却
     * 被推送用户: 最近7天加的好友
     */
    private String getPushFriendUserKey(String uid) {
        return String.format("str:PushFriendUser:%s", uid);
    }

    public void setPushFriendUserStatus(String uid, int timeout) {
        try {
            String key = getPushFriendUserKey(uid);
            clusterRedis.opsForValue().set(key, String.valueOf(timeout));
            clusterRedis.expire(key, timeout, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("setPushFriendUserStatus error: {}", e.getMessage(), e);
        }
    }


    public int getPushFriendUserStatus(String uid) {
        try {
            String timeout = clusterRedis.opsForValue().get(getPushFriendUserKey(uid));
            return timeout == null ? 0 : Integer.parseInt(timeout);
        } catch (Exception e) {
            logger.error("getPushFriendUserStatus error: {}", e.getMessage(), e);
        }
        return 0;
    }

    // 女性用户上麦时长统计
    private String getFemaleOnMicKey() {
        return String.format("ZSet:PushFemaleOnMic:%s", DateHelper.ARABIAN.formatDateInDay());
    }

    public int incFemaleOnMicTime(String uid, int micTime) {
        try {
            String key = getFemaleOnMicKey();
            Double afterTime = clusterRedis.opsForZSet().incrementScore(key, uid, micTime);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_TWO, TimeUnit.DAYS);
            return afterTime != null ? afterTime.intValue() : micTime;
        } catch (Exception e) {
            logger.error("incPushFemaleOnMicTime error: {}", e.getMessage(), e);
        }
        return micTime;
    }


    public int getFemaleOnMicTime(String uid) {
        try {
            Double afterTime = clusterRedis.opsForZSet().score(getFemaleOnMicKey(), uid);
            return afterTime == null ? 0 : afterTime.intValue();
        } catch (Exception e) {
            logger.error("getPushFemaleOnMicTime error: {}", e.getMessage(), e);
        }
        return 0;
    }

    public void addFemaleOnMicTime(String uid, int micTime) {
        try {
            clusterRedis.opsForZSet().add(getFemaleOnMicKey(), uid, micTime);
        } catch (Exception e) {
            logger.error("addFemaleOnMicTime error: {}", e.getMessage(), e);
        }
    }

    // 推送
    private String getPushMaleSameCountryUserKey(String uid) {
        return String.format("str:MaleSameCountryUser:%s", uid);
    }

    public void setPushMaleSameCountryUserStatus(String uid, int timeout) {
        try {
            String key = getPushMaleSameCountryUserKey(uid);
            clusterRedis.opsForValue().set(key, String.valueOf(timeout));
            clusterRedis.expire(key, timeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("setPushSameMaleUserStatus error: {}", e.getMessage(), e);
        }
    }


    public int getPushMaleSameCountryUserStatus(String uid) {
        try {
            String timeout = clusterRedis.opsForValue().get(getPushMaleSameCountryUserKey(uid));
            return timeout == null ? 0 : Integer.parseInt(timeout);
        } catch (Exception e) {
            logger.error("getPushSameMaleUserStatus error: {}", e.getMessage(), e);
        }
        return 0;
    }


    /**
     * 推送逻辑: 房间进入Hot列表TOP4  8小时
     * 被推送用户: 日活用户
     */
    private String getTop4RoomKey(String roomId) {
        return String.format("str:top4Room:%s", roomId);
    }

    public void setTop4RoomStatus(String roomId, int timeout) {
        try {
            String key = getTop4RoomKey(roomId);
            clusterRedis.opsForValue().set(key, String.valueOf(timeout));
            clusterRedis.expire(key, timeout, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("setTop4RoomStatus error: {}", e.getMessage(), e);
        }
    }

    public int getTop4RoomStatus(String roomId) {
        try {
            String timeout = clusterRedis.opsForValue().get(getTop4RoomKey(roomId));
            return timeout == null ? 0 : Integer.parseInt(timeout);
        } catch (Exception e) {
            logger.error("getTop4RoomStatus error: {}", e.getMessage(), e);
        }
        return 0;
    }



    /**
     * 不活跃用户推送相关方法
     */
    private String getHashNoActivePushKey() {
        return String.format("hash:noActivePush:%s", DateHelper.ARABIAN.formatDateInDay());
    }

    public boolean getNoActiveUsersExist() {
        try {
            String key = getHashNoActivePushKey();
            return clusterRedis.hasKey(key);
        } catch (Exception e) {
            logger.error("getNoActiveUsers error={}", e.getMessage(), e);
        }
        return false;
    }

    /**
     * 存储不活跃用户到Redis中，按时区分组
     */
    public void storeNoActiveUsers(String timeHour, String userList) {
        try {
            String key = getHashNoActivePushKey();
            // 存储24小时
            clusterRedis.opsForHash().put(key, timeHour, userList);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("storeNoActiveUsers error: timeHour={}, error={}", timeHour, e.getMessage(), e);
        }
    }

    /**
     * 获取不活跃用户列表
     */
    public String getNoActiveUsers(String timeHour) {
        try {
            String key = getHashNoActivePushKey();
            return (String) clusterRedis.opsForHash().get(key, timeHour);
        } catch (Exception e) {
            logger.error("getNoActiveUsers error: timeHour={}, error={}", timeHour, e.getMessage(), e);
        }
        return null;
    }



    /**
     * popular 房间列表
     */
    private String getPopularListKey() {
        return "str:popularList:area1:page1";
    }

    public List<PopularListSimplyVO> getPopularList() {
        List<PopularListSimplyVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getPopularListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, PopularListSimplyVO.class);
        } catch (Exception e) {
            logger.error("get popularList error e={}", e.getMessage());
            return result;
        }
    }


}
