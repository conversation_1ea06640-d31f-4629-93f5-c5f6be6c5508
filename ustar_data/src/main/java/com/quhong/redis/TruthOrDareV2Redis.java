package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.TruthOrDareInfo;
import com.quhong.data.TruthOrDareV2Info;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/1/31
 */
@Component
@Lazy
public class TruthOrDareV2Redis {

    private static final Logger logger = LoggerFactory.getLogger(TruthOrDareV2Redis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    private String getTruthDareV2GameKey(String gameId) {
        return "str:truth_dare_v2_game:" + gameId;
    }

    /**
     * 获取转盘游戏信息
     */
    public TruthOrDareV2Info getGameInfo(String gameId) {
        try {
            String strValue = redisTemplate.opsForValue().get(getTruthDareV2GameKey(gameId));
            if (StringUtils.isEmpty(strValue)) {
                return null;
            }
            return JSONObject.parseObject(strValue, TruthOrDareV2Info.class);
        } catch (Exception e) {
            logger.error("getGameInfo error. gameId={} {}", gameId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 设置转盘游戏信息
     */
    public void saveGameInfo(TruthOrDareV2Info gameInfo) {
        String key = getTruthDareV2GameKey(gameInfo.getGameId());
        try {
            redisTemplate.opsForValue().set(key, JSONObject.toJSONString(gameInfo));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveGameInfo error. gameId={} {}", gameInfo.getGameId(), e.getMessage(), e);
        }
    }


    /**
     * 获取所有转盘游戏信息
     */
    private String getRoomTruthDareV2Key() {
        return "hash:truth_dare_v2_game_key";
    }

    public List<TruthOrDareV2Info> getAllTruthDareV2Game() {
        List<TruthOrDareV2Info> gameInfoList = new ArrayList<>();
        try {
            List<Object> list = redisTemplate.opsForHash().values(getRoomTruthDareV2Key());
            if (CollectionUtils.isEmpty(list)){
                return Collections.emptyList();
            }
            for (Object object : list) {
                TruthOrDareV2Info gameInfo = getGameInfo((String) object);
                if (gameInfo != null) {
                    gameInfoList.add(gameInfo);
                }
            }
        } catch (Exception e) {
            logger.error("getAllTurntableGame error. {}", e.getMessage(), e);
        }
        return gameInfoList;
    }

    public TruthOrDareV2Info getGameInfoByRoomId(String roomId) {
        String gameId = getGameIdByRoomId(roomId);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return getGameInfo(gameId);
    }

    public void saveGameId(String roomId, String gameId) {
        String key = getRoomTruthDareV2Key();
        try {
            redisTemplate.opsForHash().put(key, roomId, gameId);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveGameId error. roomId={} gameId={} {}", roomId, gameId, e.getMessage(), e);
        }
    }

    public String getGameIdByRoomId(String roomId) {
        try {
            return (String) redisTemplate.opsForHash().get(getRoomTruthDareV2Key(), roomId);
        } catch (Exception e) {
            logger.error("getGameIdByRoomId error. roomId={} {}", roomId, e.getMessage(), e);
            return "";
        }
    }

    public void removeGameId(String roomId) {
        try {
            redisTemplate.opsForHash().delete(getRoomTruthDareV2Key(), roomId);
        } catch (Exception e) {
            logger.error("remove turntable game id error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    /**
     * 等待结束的key
     */
    private String getGameTimerWaitingKey() { return "zset:truth_dare_v2_game_timer"; }

    public void setGameTimeOut(String gameId, int endTime) {
        String key = getGameTimerWaitingKey();
        try {
            redisTemplate.opsForZSet().add(key, gameId, endTime);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set game timer waiting error. gameId={} endTime={} {}", gameId, endTime, e.getMessage(), e);
        }
    }

    public Set<String> getWaitingEndGameIds(int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getGameTimerWaitingKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get waiting end game ids error. timestamp={} {}", timestamp, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    public void removeGameTimerWaiting(String gameId) {
        try {
            redisTemplate.opsForZSet().remove(getGameTimerWaitingKey(), gameId);
        } catch (Exception e) {
            logger.error("remove game timer waiting error. gameId={} {}", gameId, e.getMessage(), e);
        }
    }

    /**
     * 等待选择话题的key
     */
    private String getGameSelectTopicTimeKey() { return "zset:truth_dare_v2_select_topic"; }

    public void setGameSelectTopicTimeOut(String gameId, int endTime) {
        String key = getGameSelectTopicTimeKey();
        try {
            redisTemplate.opsForZSet().add(key, gameId, endTime);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setGameSelectTopicTimeOut error. gameId={} endTime={} {}", gameId, endTime, e.getMessage(), e);
        }
    }

    public Set<String> getGameSelectTopicEndIds(int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getGameSelectTopicTimeKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("getGameSelectTopicEndGameIds error. timestamp={} {}", timestamp, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    public void removeGameSelectTopicTime(String gameId) {
        try {
            redisTemplate.opsForZSet().remove(getGameSelectTopicTimeKey(), gameId);
        } catch (Exception e) {
            logger.error("removeGameSelectTopicTime error. gameId={} {}", gameId, e.getMessage(), e);
        }
    }

    /**
     * 维护uid在roomId#gameId
     */
    private String getUserInGameHKey() {
        return "hash:truth_dare_v2_in_game";
    }

    private String getGameHValue(String roomId, String gameId) {
        return String.format("%s#%s", roomId, gameId);
    }

    public void setUserInGameHValue(String uid, String roomId, String gameId) {
        String key = getUserInGameHKey();
        try {
            redisTemplate.opsForHash().put(getUserInGameHKey(), uid, getGameHValue(roomId, gameId));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setUserInGameHStatus error. uid={} roomId={} gameId={} {}", uid, roomId, gameId, e.getMessage(), e);
        }
    }

    /**
     * 删除hash key 某个属性的值
     */
    public void delUserInGameHValue(String uid) {
        try {
            redisTemplate.opsForHash().delete(getUserInGameHKey(), uid);
        } catch (Exception e) {
            logger.error("delUserInGameHValue error uid={} e={}", uid, e);
        }
    }

    public String getUserInGameHValue(String uid) {
        try {
            return (String) redisTemplate.opsForHash().get(getUserInGameHKey(), uid);
        } catch (Exception e) {
            logger.info("getUserInGameHValue error uid={} e={}", uid, e.getMessage(), e);
            return null;
        }
    }


}
