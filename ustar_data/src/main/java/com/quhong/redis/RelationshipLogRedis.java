package com.quhong.redis;

import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class RelationshipLogRedis {
    private static final Logger logger = LoggerFactory.getLogger(RelationshipLogRedis.class);
    private static final long EXPIRE_DAYS = 1;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 增加一键关注并喜欢的埋点数据
     */
    public void addRelationshipUser(String uid, int count) {
        try {
            boolean isNewUser = ActorUtils.isNewRegisterActor(uid);
            String key = isNewUser ? getNewUserKey() : getReturnUserKey();
            redisTemplate.opsForHash().put(key, uid, count + "");
            redisTemplate.expire(key, EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add relationship log user error. uid={} count={}", uid, count, e);
        }
    }

    /**
     * 获取一键关注并喜欢的埋点数据
     */
    public Map<Object, Object> getRelationshipUser(boolean isNewUser) {
        try {
            String key = isNewUser ? getNewUserKey() : getReturnUserKey();
            return redisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            logger.error("get relationship log user error. isNewUser={}", isNewUser, e);
            return new HashMap<>();
        }
    }

    /**
     * 增加回访用户
     */
    public void addReturnUser(String uid) {
        try {
            String key = getReturnUserSetKey();
            redisTemplate.opsForSet().add(key, uid);
            redisTemplate.expire(key, EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add return user error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 获取回访人数
     */
    public int getReturnUser() {
        try {
            Long size = redisTemplate.opsForSet().size(getReturnUserSetKey());
            return null == size ? 0 : size.intValue();
        } catch (Exception e) {
            logger.info("get return user error.{}", e.getMessage(), e);
            return 0;
        }
    }

    private String getReturnUserSetKey() {
        return "return_user_" + LocalDate.now();
    }

    private String getReturnUserKey() {
        return "relationship_return_" + LocalDate.now();
    }

    private String getNewUserKey() {
        return "relationship_new_" + LocalDate.now();
    }
}
