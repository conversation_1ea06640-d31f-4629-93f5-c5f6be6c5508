package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.enums.RoomConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class VoiceMicThemeRedis {

    private static final Logger logger = LoggerFactory.getLogger(VoiceMicThemeRedis.class);
    private static final int MAX_SIZE = 30;
    public static final int LIVE_TYPE = 1;
    public static final int VIDEO_TYPE = 2;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    private String getRecentlyThemeKey(String roomId, int type) {
        return "str:voice:recently:mic:theme:" + type + "" + roomId;
    }

    public void setRecentlyTheme(String roomId, int micTheme, int type) {
        redisTemplate.opsForValue().set(getRecentlyThemeKey(roomId,type), String.valueOf(micTheme), ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
    }

    public void deleteRecentlyTheme(String roomId, int type) {
        redisTemplate.delete(getRecentlyThemeKey(roomId,type));
    }

    public int getRecentlyTheme(String roomId, int type) {
        String value = redisTemplate.opsForValue().get(getRecentlyThemeKey(roomId,type));
        int micTheme = RoomConstant.MIC_THEME_DEFAULT_ID;
        if (value == null) {
            return micTheme;
        }
        try {
            micTheme = Integer.parseInt(value);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return micTheme;
    }
}
