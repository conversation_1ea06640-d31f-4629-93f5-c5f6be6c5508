package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Lazy
@Component
public class ThirdApiCallRedis {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final int TN_LOGIN = 0; // 图灵顿
    public static final int SHU_MEI_TEXT_DETECT = 1; // 数美文本检测
    public static final int SHU_MEI_IMAGE_DETECT = 2; // 数美图片检测
    public static final int FIREBASE_SEND_SMS = 3; // firebase短信发送检测

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    private String expireDate;

    public long incApiCallNum(int type) {
        String strDate = DateHelper.ARABIAN.formatDateInDay();;
        String key = getThirdApiCallNumKey(strDate);
        long count = 0 ;
        try {
            count = redisTemplate.opsForHash().increment(key, type + "", 1);
            if (!strDate.equals(expireDate)) {
                redisTemplate.expire(key, 3, TimeUnit.DAYS);
                expireDate = strDate;
            }
        } catch (Exception e) {
            logger.error("incApiCallNum error. type={} strDate={} {}", type, strDate, e.getMessage(), e);
        }
        return count;
    }

    public Map<Integer, Integer> getApiCallNumMap(String strDate) {
        String key = getThirdApiCallNumKey(strDate);
        Map<Integer, Integer> resultMap = new HashMap<>(4);
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put(Integer.parseInt((String) entry.getKey()), Integer.parseInt((String) entry.getValue()));
            }
        } catch (Exception e) {
            logger.error("getApiCallNumMap error. strDate={} {}", strDate, e.getMessage(), e);
        }
        return resultMap;
    }

    private String getThirdApiCallNumKey(String strDate) {
        return "hash:thirdApiCallNum_" + strDate;
    }
}
