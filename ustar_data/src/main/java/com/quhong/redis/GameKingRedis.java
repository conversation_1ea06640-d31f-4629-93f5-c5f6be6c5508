package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
@Component
@Lazy
public class GameKingRedis {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 2023-12-17 00:00:00(GMT +3)
     */
    private static final int ACTIVITY_START_TIME = 1702760400;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainCluster;

    private int expireWeekNum = 0;

    public void updateRanking(String uid, int diamonds) {
        if (DateHelper.getNowSeconds() < 1710018000) {
            // 2024-03-10 00:00:00 (GMT+3) 开始
            return;
        }
        int weekNum = getCurWeekNum();
        String key = getGameKingRankingKey(weekNum);
        int beforeScore = getRankingScore(uid, weekNum);
        int score = beforeScore + diamonds;
        double rankScore = new BigDecimal( score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
        try {
            mainCluster.opsForZSet().add(key, uid, rankScore);
            if (expireWeekNum != weekNum) {
                mainCluster.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
                expireWeekNum = weekNum;
            }
        } catch (Exception e) {
            logger.error("updateRanking error. uid={} diamonds={} {}", uid, diamonds, e.getMessage(), e);
        }
    }

    public int getRankingScore(String uid, int weekNum) {
        String key = getGameKingRankingKey(weekNum);
        try {
            Double score = mainCluster.opsForZSet().score(key, uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRankingScore error. uid={} weekNum={} {}", uid, weekNum, e.getMessage(), e);
            return 0;
        }
    }

    public int getRank(String uid, int weekNum) {
        String key = getGameKingRankingKey(weekNum);
        try {
            Long rank = mainCluster.opsForZSet().reverseRank(key, uid);
            return rank == null ? 0 : rank.intValue() + 1;
        } catch (Exception e) {
            logger.error("getRank error. uid={} weekNum={} {}", uid, weekNum, e.getMessage(), e);
            return 0;
        }
    }

    public Map<String, Integer> getRankingMap(int weekNum, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getGameKingRankingKey(weekNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = mainCluster.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public Map<String, Integer> getRangeRankingMap(int weekNum, int min, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getGameKingRankingKey(weekNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = mainCluster.opsForZSet().reverseRangeByScoreWithScores(key, min, Integer.MAX_VALUE, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public int getLastWeekNum() {
        return getCurWeekNum() - 1;
    }

    public int getCurWeekNum() {
        return (DateHelper.getNowSeconds() - ACTIVITY_START_TIME) / (int)TimeUnit.DAYS.toSeconds(7) + 1;
    }

    private String getGameKingRankingKey(int weekNum) {
        return "zset:gameKingRanking_" + weekNum;
    }
}
