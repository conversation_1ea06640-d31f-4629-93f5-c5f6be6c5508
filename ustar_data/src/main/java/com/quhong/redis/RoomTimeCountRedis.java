/*
package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

*/
/**
 * <AUTHOR>
 * @date 2022/8/8
 *//*

@Component
@Lazy
public class RoomTimeCountRedis {

    private static final Logger logger = LoggerFactory.getLogger(RoomTimeCountRedis.class);

    @Resource(name = DataRedisBean.ROOM_TIME_COUNT)
    private StringRedisTemplate redisTemplate;

    public void incInRoomTime(String uid, int roomTime) {
        String key = getRoomTimeCountKey(DateHelper.DEFAULT.formatDateInDay());
        redisTemplate.opsForHash().increment(key, uid, roomTime);
        redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);

    }

    public void incInOwnRoomTime(String uid, int roomTime) {
        String key = getOwnRoomTimeCountKey(DateHelper.DEFAULT.formatDateInDay());
        redisTemplate.opsForHash().increment(key, uid, roomTime);
        redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
    }

    public Map<String, Integer> getAllOwnRoomTimeCount(String strDate) {
        Map<String, Integer> resultMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getOwnRoomTimeCountKey(strDate));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                try {
                    resultMap.put(String.valueOf(entry.getKey()), Integer.parseInt(String.valueOf(entry.getValue())));
                } catch (Exception e) {
                    logger.error("parse own room time count error. key={}, value={} {}", entry.getKey(), entry.getValue(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            logger.error("get yesterday all own room time count error.{}", e.getMessage(), e);
        }
        return resultMap;
    }

    public Map<String, Integer> getAllRoomTimeCount(String strDate) {
        Map<String, Integer> resultMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getRoomTimeCountKey(strDate));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                try {
                    resultMap.put(String.valueOf(entry.getKey()), Integer.parseInt(String.valueOf(entry.getValue())));
                } catch (Exception e) {
                    logger.error("parse room time count error. key={}, value={} {}", entry.getKey(), entry.getValue(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            logger.error("get yesterday all room time count error.{}", e.getMessage(), e);
        }
        return resultMap;
    }


    private String getRoomTimeCountKey(String strDate) {
        return "room_time_count_date_" + strDate;
    }

    private String getOwnRoomTimeCountKey(String strDate) {
        return "own_room_time_count_date_" + strDate;
    }
}
*/
