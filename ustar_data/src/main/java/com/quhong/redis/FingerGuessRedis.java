package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.cache.CacheMap;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.FingerGuessInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Component
public class FingerGuessRedis {

    private static final Logger logger = LoggerFactory.getLogger(FingerGuessRedis.class);

    private static final long CACHE_TIME_MILLIS = 5 * 60 * 1000L;
    private final CacheMap<String, Map<String, Integer>> cacheMap;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public FingerGuessRedis() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    public void setFingerGuessIdInRedis(String fingerGuessId) {
        try {
            clusterRedis.opsForList().leftPush(getFingerGuessIdsKey(), fingerGuessId);
        } catch (Exception e) {
            logger.error("set finger guess id in redis error. fingerGuessId={} {}", fingerGuessId, e.getMessage(), e);
        }
    }

    public void removeFingerGuessId(String fingerGuessId) {
        try {
            clusterRedis.opsForList().remove(getFingerGuessIdsKey(), 0, fingerGuessId);
        } catch (Exception e) {
            logger.error("remove finger guess id from redis error. fingerGuessId={} {}", fingerGuessId, e.getMessage(), e);
        }
    }

    public int getAllGuessNum() {
        try {
            return Objects.requireNonNull(clusterRedis.opsForList().size(getFingerGuessIdsKey())).intValue();
        } catch (Exception e) {
            logger.error("get all guess num error. {}", e.getMessage(), e);
        }
        return 0;
    }

    public List<String> findList(int start, int end) {
        try {
            return clusterRedis.opsForList().range(getFingerGuessIdsKey(), start, end);
        } catch (Exception e) {
            logger.error("get finger guess data list error. start={} end={} {}", start, end, e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public void saveFingerGuessInfo(FingerGuessInfo fingerGuessInfo) {
        try {
            clusterRedis.opsForHash().put(getFingerGuessInfoKey(), fingerGuessInfo.getGameId(), JSONObject.toJSONString(fingerGuessInfo));
        } catch (Exception e) {
            logger.error("save finger guess info in redis error. fingerGuessId={} {}", fingerGuessInfo.getGameId(), e.getMessage(), e);
        }
    }

    public FingerGuessInfo getFingerGuessInfo(String fingerGuessId) {
        try {
            String strValue = (String) clusterRedis.opsForHash().get(getFingerGuessInfoKey(), fingerGuessId);
            if (StringUtils.isEmpty(strValue)) {
                return null;
            }
            return JSONObject.parseObject(strValue, FingerGuessInfo.class);
        } catch (Exception e) {
            logger.error("get finger guess info from redis error. fingerGuessId={} {}", fingerGuessId, e.getMessage(), e);
            return null;
        }
    }

    public void removeFingerGuessInfo(String fingerGuessId) {
        try {
            clusterRedis.opsForHash().delete(getFingerGuessInfoKey(), fingerGuessId);
        } catch (Exception e) {
            logger.error("remove finger guess info from redis error. fingerGuessId={} {}", fingerGuessId, e.getMessage(), e);
        }
    }

    public void saveFingerGuessWaitTime(String fingerGuessId, int timestamp) {
        try {
            clusterRedis.opsForZSet().add(getGuessGameWaitKey(), fingerGuessId, timestamp);
        } catch (Exception e) {
            logger.error("save finger guess wait time in redis error. fingerGuessId={} timestamp={} {}", fingerGuessId, timestamp, e.getMessage(), e);
        }
    }

    public Map<String, Integer> getAllWithScores() {
        try {
            Set<ZSetOperations.TypedTuple<String>> set = clusterRedis.opsForZSet().reverseRangeWithScores(getGuessGameWaitKey(), 0, -1);
            if (null == set) {
                return Collections.emptyMap();
            }
            Map<String, Integer> linkedMap = new LinkedHashMap<>();
            for (ZSetOperations.TypedTuple<String> tuple : set) {
                if (null == tuple.getValue() || null == tuple.getScore()) {
                    logger.error("invalid data key={} score={}", tuple.getValue(), tuple.getScore());
                    continue;
                }
                linkedMap.put(tuple.getValue(), tuple.getScore().intValue());
            }
            return linkedMap;
        } catch (Exception e) {
            logger.error("getAllWithScores error. {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    public Set<String> getFingerGuessWaitEndId(int timestamp) {
        try {
            return clusterRedis.opsForZSet().rangeByScore(getGuessGameWaitKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get finger guess wait end id from redis error. timestamp={} {}", timestamp, e.getMessage(), e);
        }
        return new HashSet<>();
    }

    public void removeFingerGuessWaitTime(String fingerGuessId) {
        try {
            clusterRedis.opsForZSet().remove(getGuessGameWaitKey(), fingerGuessId);
        } catch (Exception e) {
            logger.error("remove finger guess wait time from redis error. fingerGuessId={} {}", fingerGuessId, e.getMessage(), e);
        }
    }

    /**
     * 获取排行榜礼物数量
     */
    public int getRankingGiftNum(String uid, int rankType, int giftId) {
        try {
            Double score = clusterRedis.opsForZSet().score(getRankingKey(rankType, giftId), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("get finger guess ranking gift num error. uid={} rankType={} giftId={}", uid, rankType, giftId, e);
            return 0;
        }
    }

    /**
     * 保存猜拳的排行榜
     */
    public void saveRanking(String uid, int rankType, int giftId, int num, int ctime) {
        try {
            String rankingKey = getRankingKey(rankType, giftId);
            clusterRedis.opsForZSet().add(rankingKey, uid, getRankingScore(num, ctime));
            clusterRedis.expire(rankingKey, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save ranking in redis error. uid={} rankType={} giftId={} num={} ctime={} {}", uid, rankType, giftId, num, ctime, e.getMessage(), e);
        }
    }

    /**
     * 获取用户排名 (0为找不到)
     */
    public Integer getFingerGuessRank(String uid, int rankType, int giftId) {
        try {
            String rankingKey = getRankingKey(rankType, giftId);
            Long rank = clusterRedis.opsForZSet().reverseRank(rankingKey, uid);
            return rank == null ? 0 : rank.intValue() + 1;
        } catch (Exception e) {
            logger.error("get finger guess rank from redis error. uid={} rankType={} giftId={} {}", uid, rankType, giftId, e.getMessage(), e);
            return 0;
        }
    }

    public void clearRanking(int rankType, int giftId) {
        try {
            clusterRedis.delete(getRankingKey(rankType, giftId));
            cacheMap.remove(getCacheMapKey(rankType, giftId));
        } catch (Exception e) {
            logger.error("delete ranking from redis error.rankType={} giftId={} {}", rankType, giftId, e.getMessage(), e);
        }
    }

    /**
     * 获取排行榜
     *
     * @param rankType 排行榜类型
     * @param giftId   礼物id
     */
    public Map<String, Integer> getRankingList(int rankType, int giftId) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        try {
            String key = getRankingKey(rankType, giftId);
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterRedis.opsForZSet().reverseRangeWithScores(key, 0, 19);
            if (null == rangeWithScores) {
                return linkedRankMap;
            }
            for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                    continue;
                }
                linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
            }
        } catch (Exception e) {
            logger.error("get finger guess game ranking list error. rankType={} giftId={}  {}", rankType, giftId, e.getMessage(), e);
        }
        return linkedRankMap;
    }

    /**
     * 计算排行榜分值 (分越大排行名次越高)
     */
    private Double getRankingScore(Integer num, Integer ctime) {
        return new BigDecimal(num + "." + (2493043200L - ctime)).doubleValue();
    }

    private String getFingerGuessIdsKey() {
        return "list:finger_guess_ids";
    }

    private String getFingerGuessInfoKey() {
        return "hash:finger_guess_info";
    }

    private String getGuessGameWaitKey() {
        return "zset:guess_game_wait";
    }

    private String getRankingKey(int rankType, int giftId) {
        return "zset:finger_guess_ranking:" + rankType + "-" + giftId;
    }

    private String getCacheMapKey(int rankType, int giftId) {
        return "rankList:" + rankType + "-" + giftId;
    }
}
