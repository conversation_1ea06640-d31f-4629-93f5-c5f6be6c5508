package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.PopularListNewVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class GiftPlayRedis {
    private static final Logger logger = LoggerFactory.getLogger(GiftPlayRedis.class);
    private String unlockGiftExpireDate = "";
    private static final Integer COMMON_EXPIRE_DAYS = 30;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 进阶礼物升级玩法
     */
    private String hashAdvancedGiftKey(String objId, String uid) {
        return String.format("hash:AdvancedGift:%s:%s", objId, uid);
    }

    public void incAdvancedGiftHashNum(String objId, String uid, String key, int num, int playTimeOut) {
        try {
            String playKey = hashAdvancedGiftKey(objId, uid);
            clusterTemplate.opsForHash().increment(playKey, key, num);
            Long expireTime = clusterTemplate.getExpire(playKey);
            if (expireTime == null || expireTime < 0) {
                clusterTemplate.expire(playKey, playTimeOut, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            logger.info("incAdvancedGiftHashNum error objId={}, uid={} e={}", objId, uid, e.getMessage(), e);
        }
    }

    public Map<String, Integer> getAllAdvancedGiftValue(String objId, String uid) {
        Map<String, Integer> hashMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(hashAdvancedGiftKey(objId, uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.info("getAllAdvancedGiftValue error objId={} uid={} e={}", objId, uid, e);
            return hashMap;
        }
    }

    public void removeAdvancedGiftKey(String objId, String uid) {
        try {
            clusterTemplate.delete(hashAdvancedGiftKey(objId, uid));
        } catch (Exception e) {
            logger.info("removeAdvancedGiftKey error objId={} uid={} e={}", objId, uid, e.getMessage(), e);
        }
    }

    /**
     * 获取key redis过期时间
     */
    public long getAdvancedGiftExpire(String objId, String uid) {
        try {
            Long expire = clusterTemplate.getExpire(hashAdvancedGiftKey(objId, uid), TimeUnit.SECONDS);
            if (null == expire) {
                return 0;
            }
            return expire;
        } catch (Exception e) {
            logger.error("getAdvancedGiftExpire error.  uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }


    /**
     * 解锁礼物玩法
     */

    // 每个礼物当前解锁值
    private String strUnlockGiftKey(String objId, String uid, Integer giftId) {
        return String.format("str:UnlockGift:%s:%s:%s", objId, uid, giftId);
    }

    public int incUnlockGiftNum(String objId, String uid, Integer giftId, int num) {
        try {
            String unlockGiftKey = strUnlockGiftKey(objId, uid, giftId);
            Long afterValue = clusterTemplate.opsForValue().increment(unlockGiftKey, num);
            clusterTemplate.expire(unlockGiftKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return afterValue == null ? 0 : afterValue.intValue();
        } catch (Exception e) {
            logger.info("incUnlockGiftNum error objId={}, uid={}, giftId={}, e={}", objId, uid, giftId, e.getMessage(), e);
        }
        return 0;
    }

    public int getUnlockGiftNum(String objId, String uid, Integer giftId) {
        try {
            String unlockGiftKey = strUnlockGiftKey(objId, uid, giftId);
            String unlockGiftNum = clusterTemplate.opsForValue().get(unlockGiftKey);
            return unlockGiftNum == null ? 0 : Integer.parseInt(unlockGiftNum);
        } catch (Exception e) {
            logger.info("getUnlockGiftNum error objId={}, uid={}, giftId={}, e={}", objId, uid, giftId, e.getMessage(), e);
        }
        return 0;
    }


    public void removeUnlockGiftNumKey(String objId, String uid, Integer giftId) {
        try {
            clusterTemplate.delete(strUnlockGiftKey(objId, uid, giftId));
        } catch (Exception e) {
            logger.info("removeUnlockGiftNumKey error objId={} uid={} e={}", objId, uid, e.getMessage(), e);
        }
    }

    // 15天使用期key
    private String getUnlockGiftExpireKey(String objId, Integer giftId) {
        return String.format("zset:UnlockGiftExpire:%s:%s", objId, giftId);
    }

    public int getUnlockGiftExpireScore(String objId, String uid, Integer giftId) {
        try {
            String key = getUnlockGiftExpireKey(objId, giftId);
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            return score == null ? 0 : score.intValue();
        } catch (Exception e) {
            logger.info("getUnlockGiftExpireScore error uid={}", uid, e);
            return 0;
        }
    }

    public void setUnlockGiftExpireScore(String objId, String uid, Integer giftId, int score) {
        try {
            String key = getUnlockGiftExpireKey(objId, giftId);
            clusterTemplate.opsForZSet().add(key, uid, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(unlockGiftExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                unlockGiftExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("setUnlockGiftExpireScore error uid={} score={}", uid, score, e);
        }
    }

    public Map<String, Integer> getUnlockGiftExpireMapByScore(String objId, Integer giftId, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getUnlockGiftExpireKey(objId, giftId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public void removeUnlockGiftExpire(String objId, Integer giftId, String uid) {
        try {
            String key = getUnlockGiftExpireKey(objId, giftId);
            clusterTemplate.opsForZSet().remove(key, uid);
        } catch (Exception e) {
            logger.info("removeUnlockGiftExpire error uid={}", uid, e);
        }
    }


    private String selectUnlockGiftKey(String objId, String uid) {
        return String.format("str:selectUnlockGift:%s:%s", objId, uid);
    }

    public int getSelectUnlockGift(String objId, String uid, Integer defaultGiftId) {
        try {
            String selectGiftId = clusterTemplate.opsForValue().get(selectUnlockGiftKey(objId, uid));
            return selectGiftId == null ? defaultGiftId : Integer.parseInt(selectGiftId);
        } catch (Exception e) {
            logger.info("getSelectUnlockGift error objId={}, uid={}, e={}", objId, uid, e.getMessage(), e);
        }
        return defaultGiftId;
    }

    public void setSelectUnlockGift(String objId, String uid, Integer giftId) {
        try {
            clusterTemplate.opsForValue().set(selectUnlockGiftKey(objId, uid), String.valueOf(giftId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setSelectUnlockGift error objId={}, uid={}, e={}", objId, uid, e.getMessage(), e);
        }
    }

    public void deleteSelectUnlockGift(String objId, String uid) {
        try {
            clusterTemplate.delete(selectUnlockGiftKey(objId, uid));
        } catch (Exception e) {
            logger.info("deleteSelectUnlockGift error objId={}, uid={}, e={}", objId, uid, e.getMessage(), e);
        }
    }

    private String getPopularListKey(int area, int page) {
        return String.format("str:popularList:area%d:page%d", area, page);
    }

    public String getPopularListOne(){
        try {
            String json = clusterTemplate.opsForValue().get(getPopularListKey(1, 1));
            if (StringUtils.isEmpty(json)) {
                return "";
            }
            List<PopularListNewVO> popularList = JSON.parseArray(json, PopularListNewVO.class);
            if(popularList != null && popularList.size() > 0){
                PopularListNewVO popularVO = popularList.get(0);
                return popularVO.getRoomId();
            }
        } catch (Exception e) {
            logger.error("get popularList error error={}", e.getMessage(), e);
        }
        return "";
    }

    private String getSocialRoomListKey() {
        return "str:socialRoomList";
    }

    public List<PopularListNewVO> getSocialRoomListNew() {
        List<PopularListNewVO> result = new ArrayList<>();
        try {
            String json = clusterTemplate.opsForValue().get(getSocialRoomListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, PopularListNewVO.class);
        } catch (Exception e) {
            logger.error("get getSocialRoomList error", e);
            return result;
        }
    }
}
