package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import com.quhong.handler.HttpEnvData;
import com.quhong.utils.AppVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 房间访客
 *
 * <AUTHOR>
 * @date 2024/9/25
 */
@Component
@Lazy
public class RoomVisitorsRedis {

    private static final Logger logger = LoggerFactory.getLogger(RecentlyRoomRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    /**
     * 控制显示访客开关 ture打开 false关闭
     * 注：关闭后各首页、搜索结果、房间内都只展示实际当前在线用户
     */
    public static final boolean SHOW_VISITOR = false;
    private static final Map<String, String> SET_EXPIRE_MAP = new HashMap<>();

    /**
     * 新增房间访客记录
     */
    public void addRoomVisitorRecord(String roomId, String uid) {
        if (!SHOW_VISITOR) {
            return;
        }
        String key = getRoomVisitorRecordKey(roomId);
        try {
            int nowTime = DateHelper.getNowSeconds();
            clusterRedis.opsForZSet().add(key, uid, nowTime);
            String strToday = DateHelper.ARABIAN.formatDateInDay();
            if (!strToday.equals(SET_EXPIRE_MAP.getOrDefault(key, ""))) {
                // 移除24小时前的旧数据
                clusterRedis.opsForZSet().removeRangeByScore(key, 0, nowTime - (int)TimeUnit.DAYS.toSeconds(1));
                clusterRedis.expire(key, 3, TimeUnit.DAYS);
                SET_EXPIRE_MAP.put(key, DateHelper.ARABIAN.formatDateInDay());
            }
        } catch (Exception e) {
            logger.error("RoomVisitorRecord error, roomId={} uid={} {}", roomId, uid, e.getMessage());
        }
    }

    /**
     * 获取房间访客Set （最近24小时内进房间用户）
     */
    public Set<String> getRoomVisitorSet(String roomId) {
        if (!SHOW_VISITOR) {
            return Collections.emptySet();
        }
        String key = getRoomVisitorRecordKey(roomId);
        try {
            int nowTime = DateHelper.getNowSeconds();
            return clusterRedis.opsForZSet().rangeByScore(key, nowTime - (int)TimeUnit.DAYS.toSeconds(1), nowTime);
        } catch (Exception e) {
            logger.error("getRoomVisitorSet error, roomId={} {}", roomId, e.getMessage());
            return Collections.emptySet();
        }
    }

    /**
     * 获取房间访客数量 （最近24小时内进房间用户）
     */
    public int getRoomVisitorNum(String roomId) {
        if (!SHOW_VISITOR) {
            return 0;
        }
        String key = getRoomVisitorRecordKey(roomId);
        try {
            int nowTime = DateHelper.getNowSeconds();
            Long count = clusterRedis.opsForZSet().count(key, nowTime - (int) TimeUnit.DAYS.toSeconds(1), nowTime);
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRoomVisitorNum error, roomId={} {}", roomId, e.getMessage());
            return 0;
        }
    }


    /**
     * 新增游戏房访客记录
     */
    public void addGameRoomTypeVisitorRecord(Integer gameType, String uid) {

        String key = getGameRoomTypeVisitorRecordKey(gameType);
        try {
            int nowTime = DateHelper.getNowSeconds();
            clusterRedis.opsForZSet().add(key, uid, nowTime);
            String strToday = DateHelper.ARABIAN.formatDateInDay();
            if (!strToday.equals(SET_EXPIRE_MAP.getOrDefault(key, ""))) {
                // 移除16小时前的旧数据
                clusterRedis.opsForZSet().removeRangeByScore(key, 0, nowTime - (int)TimeUnit.HOURS.toSeconds(16));
                clusterRedis.expire(key, 3, TimeUnit.DAYS);
                SET_EXPIRE_MAP.put(key, DateHelper.ARABIAN.formatDateInDay());
            }
        } catch (Exception e) {
            logger.error("addGameRoomTypeVisitorRecord error, roomId={} uid={} {}", gameType, uid, e.getMessage());
        }
    }
    /**
     * 获取游戏类型访客数量（最近8小时内进游戏类型用户）
     */
    public int getGameRoomTypeNum(Integer gameType) {
        String key = getGameRoomTypeVisitorRecordKey(gameType);
        try {
            int nowTime = DateHelper.getNowSeconds();
            Long count = clusterRedis.opsForZSet().count(key, nowTime - (int) TimeUnit.HOURS.toSeconds(8), nowTime);
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            logger.error("getGameRoomTypeNum error, roomId={} {}", gameType, e.getMessage());
            return 0;
        }
    }



    public boolean showRoomVisitor(int os, int versioncode) {
        return SHOW_VISITOR && AppVersionUtils.versionCheck(8592, versioncode, os);
    }

    public boolean showRoomVisitor(HttpEnvData envData) {
        return SHOW_VISITOR && AppVersionUtils.versionCheck(8592, envData);
    }

    private String getRoomVisitorRecordKey(String roomId) {
        return "zset:roomVisitorRecord_" + roomId;
    }

    private String getGameRoomTypeVisitorRecordKey(Integer type) {
        return "zset:gameRoomTypeVisitorRecord_" + type;
    }
}
