package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class CelebrityActivityRedis {
    private static final Logger logger = LoggerFactory.getLogger(CelebrityActivityRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    private String celebrityExpireDate = "";
    private String sendBeanExpireDate = "";
    private String celebrityLikeExpireDate = "";


    /**
     * 名人活动排行榜
     */
    public int incrCelebrityScore(String activityId, String aid, int giftId, int score) {
        try {
            String key = getCelebrityActivityKey(activityId);
            int curScore = getScore(activityId, giftId);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrCelebrityScore activityId={} aid={} giftId={} score={} total={}", activityId, aid, giftId, score, rankScore);
            clusterTemplate.opsForZSet().add(key, giftId + "", rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(celebrityExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                celebrityExpireDate = dateStr;
            }
            return curScore + score;
        } catch (Exception e) {
            logger.info("incrCelebrityScore error activityId={} aid={} giftId={} score={}", activityId, aid, giftId, score, e);
            return 0;
        }
    }

    /**
     * 获取排行榜
     *
     * @param activityId 活动id
     * @param length     排行榜长度
     */
    public List<String> getRankingList(String activityId, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getCelebrityActivityKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            logger.info("getRankingByType giftId={} score={}", rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动id
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(String activityId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCelebrityActivityKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取分数
     */
    public int getScore(String activityId, int giftId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getCelebrityActivityKey(activityId), giftId + "");
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getScore error activityId={}", activityId, e);
            return 0;
        }
    }

    private String getCelebrityActivityKey(String activityId) {
        return "zset:celebrityActivity:" + activityId;
    }

    public Set<String> getLikeMembers(String uid, String activityId) {
        try {
            return clusterTemplate.opsForSet().members(getLikeMembersKey(activityId, uid));
        } catch (Exception e) {
            logger.error("getLikeMembers error. uid={} activityId={}", uid, activityId, e);
            return Collections.emptySet();
        }
    }

    public int addCelebrityLike(String uid, String activityId, String giftId) {
        try {
            String celebrityLikeKey = getCelebrityLikeKey(activityId);
            Double value = clusterTemplate.opsForZSet().incrementScore(celebrityLikeKey, giftId, 1);
            if (null == value) {
                return 0;
            }
            logger.info("addCelebrityLike uid={} activityId={} giftId={} totalLikes={}", uid, activityId, giftId, value.intValue());
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(celebrityLikeExpireDate)) {
                clusterTemplate.expire(celebrityLikeKey, 180, TimeUnit.DAYS);
                celebrityLikeExpireDate = dateStr;
            }
            String likeMembersKey = getLikeMembersKey(activityId, uid);
            clusterTemplate.opsForSet().add(likeMembersKey, giftId);
            clusterTemplate.expire(likeMembersKey, 25, TimeUnit.HOURS);
            return value.intValue();
        } catch (Exception e) {
            logger.error("addCelebrityLike error, uid={} activityId={} giftId={}", uid, activityId, giftId);
            return 0;
        }
    }

    public int getCelebrityLikes(String activityId, int giftId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getCelebrityLikeKey(activityId), giftId + "");
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getCelebrityLikes error, activityId={} giftId={}", activityId, giftId);
            return 0;
        }
    }

    /**
     * 用于防刷
     */
    private String getLikeMembersKey(String activityId, String uid) {
        return "set:celebrityDailyLike:" + activityId + ":" + uid + ":" + DateHelper.ARABIAN.formatDateInDay();
    }

    private String getCelebrityLikeKey(String activityId) {
        return "set:celebrityLike:" + activityId;
    }

    private String getCelebritySendBeanKey(String activityId) {
        return "zset:celebritySendBean:" + activityId;
    }

    public void incrCelebritySendBeanScore(String activityId, String aid, int giftId, int score) {
        try {
            String key = getCelebritySendBeanKey(activityId);
            Double value = clusterTemplate.opsForZSet().incrementScore(key, giftId + "", score);
            int intValue = 0;
            if (null != value) {
                intValue = value.intValue();
            }
            logger.info("incrCelebritySendBeanScore activityId={} aid={} giftId={} score={} total={}", activityId, aid, giftId, score, intValue);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(sendBeanExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                sendBeanExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrCelebritySendBeanScore error activityId={} aid={} giftId={} score={}", activityId, aid, giftId, score, e);
        }
    }

    public Map<Integer, Integer> getSendBeanMap(String activityId, int length) {
        Map<Integer, Integer> hashMap = new HashMap<>();
        String key = getCelebritySendBeanKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return hashMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            hashMap.put(Integer.valueOf(rangeWithScore.getValue()), rangeWithScore.getScore().intValue());
        }
        return hashMap;
    }
}
