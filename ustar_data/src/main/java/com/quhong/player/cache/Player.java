package com.quhong.player.cache;

import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.data.ActorData;
import com.quhong.datas.PlayerData;
import com.quhong.enums.ClientOS;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Player extends TaskQueue {
    private static final Logger logger = LoggerFactory.getLogger(Player.class);

    private static final long SHORT_UPDATE_INTERVAL = 30 * 1000L;
    private static final long UPDATE_INTERVAL = 5 * 60 * 1000L;

    protected PlayerData playerData;
    protected String uid;
    protected long updateTime;
    protected ActorData actorData;

    public Player(String uid){
        this.uid = uid;
    }

    public PlayerData getPlayerData() {
        return playerData;
    }

    public void setPlayerData(PlayerData playerData) {
        this.playerData = playerData;
        update();
    }

    public long getSessionId() {
        if(playerData == null){
            return 0;
        }
        return playerData.getSessionId();
    }

    public void setSessionId(long sessionId) {
        this.playerData.setSessionId(sessionId);
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public ActorData getActorData() {
        return actorData;
    }

    public void setActorData(ActorData actorData) {
        this.actorData = actorData;
    }

    public void update(){
        updateTime = System.currentTimeMillis();
    }

    public boolean needUpdate(){
        // sessionId为-1，表示用户离线，较短时间过期
        if(playerData.getSessionId() == -1){
            return System.currentTimeMillis() > updateTime + SHORT_UPDATE_INTERVAL;
        }else {
            return System.currentTimeMillis() > updateTime + UPDATE_INTERVAL;
        }
    }

    public int getOs(){
        if(actorData != null){
            return actorData.getIntOs();
        }
        logger.error("get os. can not find actorData. uid+{}",this.uid);
        return ClientOS.NONE;
    }
}
