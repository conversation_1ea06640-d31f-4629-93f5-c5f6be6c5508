package com.quhong.player.cache;

import com.quhong.cache.CacheMap;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.datas.PlayerData;
import com.quhong.mongo.dao.ActorDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

public class PlayerCacheMap extends CacheMap<String, Player> {
    private static final Logger logger = LoggerFactory.getLogger(PlayerCacheMap.class);

    private static final long EXPIRE_TIME_MILLS = 3600 * 1000L;
    @Autowired
    private BasePlayerRedis playerRedis;
    @Autowired
    private ActorDao actorDao;

    public PlayerCacheMap() {
        super(EXPIRE_TIME_MILLS);
    }

    @PostConstruct
    public void postInit(){
        start();
    }

    public Player getPlayer(String uid) {
        if(StringUtils.isEmpty(uid)){
            try{
                throw new Exception("get player. uid is null");
            }catch (Exception e){
                logger.error(e.getMessage(), e);
            }
        }
        Player player = getData(uid, true);
        if(player == null){
            player = createPlayer(uid);
            if(player != null){
                cacheData(player.getUid(), player);
            }
        }else{
            if(player.needUpdate()){
                PlayerData playerData = playerRedis.getPlayerDataFromRedis(uid);
                if(playerData == null){
                    logger.info("can not find playerData. remove cache player. uid={}", uid);
                    remove(uid);
                    return null;
                }
                player.setPlayerData(playerData);
            }
        }
        return player;
    }

    public PlayerData updatePlayer(String uid){
        PlayerData playerData = playerRedis.getPlayerDataFromRedis(uid);
        if(playerData == null){
            logger.info("can not find playerData. remove cache player. uid={}", uid);
            remove(uid);
            return null;
        }
        return updatePlayer(playerData);
    }

    /**
     * 上下线时更新Player
     */
    public PlayerData updatePlayer(PlayerData playerData){
        Player player = getData(playerData.getUid(), true);
        if(player == null){
            player = createPlayer(playerData.getUid());
            player.setPlayerData(playerData);
        }else{
            player.setPlayerData(playerData);
        }
        return playerData;
    }

    public Player createPlayer(String uid){
        PlayerData playerData = playerRedis.getPlayerDataFromRedis(uid);
        if(playerData == null){
            logger.info("can not find playerData. uid={}", uid);
            return null;
        }
        Player player = new Player(uid);
        player.setPlayerData(playerData);
        player.setActorData(actorDao.getActorData(uid));
        return player;
    }
}
