package com.quhong.video.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.cache.CacheMap;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.data.ActorData;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.*;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.redis.ServiceRedis;
import com.quhong.utils.PageUtils;
import com.quhong.video.data.NaviVideoData;
import com.quhong.video.data.VideoInfoData;
import com.quhong.video.data.VideoListData;
import com.quhong.vo.Mic<PERSON>erson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Lazy
@Component
public class VideoRedis extends ServiceRedis {
    private static final Logger logger = LoggerFactory.getLogger(VideoRedis.class);

    private static final int VIDEO_PAGE_SIZE = 10;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private OperationConfigRedis operationConfigRedis;
    @Autowired
    private RoomPwdRedis roomPwdRedis;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private RoomVisitorsRedis roomVisitorsRedis;

    private final CacheMap<Integer, ApiResult<Object>> videoListCacheMap;

    public VideoRedis() {
        videoListCacheMap = new CacheMap<>(30 * 1000L);
    }

    public VideoListData getVideoList(String roomId) {
        String key = getListKey();
        String json = (String) redisTemplate.opsForHash().get(key, roomId);
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JSON.parseObject(json, VideoListData.class);
    }

    public void clearVideoList(String roomId) {
        redisTemplate.opsForHash().delete(getListKey(), roomId);
    }

    public void saveVideoList(VideoListData data) {
        String roomId = data.getRoomId();
        String key = getListKey();
        try {
            String json = JSON.toJSONString(data);
            redisTemplate.opsForHash().put(key, roomId, json);
        } catch (Exception e) {
            logger.error("save video list from redis error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public List<VideoListData> getAllVideoList() {
        List<VideoListData> list = new ArrayList<>();
        try {
            Map<Object, Object> map = redisTemplate.opsForHash().entries(getListKey());
            for (Object json : map.values()) {
                list.add(JSON.parseObject((String) json, VideoListData.class));
            }
        } catch (Exception e) {
            logger.error("get all video list error. {}", e.getMessage(), e);
        }
        return list;
    }

    private String getListKey() {
        return "video_list";
    }

    public int incVersion(String roomId) {
        return doIncVersion(getVersionKey(roomId));
    }

    public int getVersion(String roomId) {
        return doGetVersion(getVersionKey(roomId));
    }

    private String getVersionKey(String roomId) {
        return "video_list_version_" + roomId;
    }

    public DistributeLock createLock(String roomId) {
        return new DistributeLock(getLockKey(roomId), 5);
    }

    private String getLockKey(String roomId) {
        return "video_lock_" + roomId;
    }

    public ApiResult<Object> getNaviVideoListFromCache(int page, HttpEnvData envData) {
        if (videoListCacheMap.hasData(page)) {
            return videoListCacheMap.getData(page);
        }
        ApiResult<Object> result = getNaviVideoList(page,envData);
        if (result.isOk()) {
            videoListCacheMap.cacheData(page, result);
        }
        return result;
    }

    public ApiResult<Object> getNaviVideoList(int page, HttpEnvData envData) {
        try {
            List<NaviVideoData> resultList = new ArrayList<>();
            List<VideoListData> allVideoList = getAllVideoList();
            NaviVideoData naviVideoData;
//            Set<String> allVideoOperationRoom = operationConfigRedis.getAllVideoOperationRoom();
//            List<NaviVideoData> topList = new ArrayList<>();
            for (VideoListData videoListData : allVideoList) {
                if (CollectionUtils.isEmpty(videoListData.getVideoList())) {
                    continue;
                }
                // 房间用户数量
                int personCount = roomPlayerRedis.getRoomActorsCount(videoListData.getRoomId());

                if (0 == personCount || roomPwdRedis.hasPwdFromCache(videoListData.getRoomId())) {
                    continue;
                }
                if (whiteTestDao.isMemberByType(videoListData.getRoomId(), WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                    continue;
                }
                if (roomVisitorsRedis.showRoomVisitor(envData)) {
                    personCount = roomVisitorsRedis.getRoomVisitorNum(videoListData.getRoomId());
                }

                naviVideoData = new NaviVideoData();
                naviVideoData.setRoomId(videoListData.getRoomId());
                // 获取当前的视频标题
                naviVideoData.setTitle(videoListData.getCurVideo().getTitle());
                naviVideoData.setPersonCount(personCount);
                naviVideoData.setThumbnails(videoListData.getCurVideo().getThumbnails());
//                if (allVideoOperationRoom.contains(videoListData.getRoomId())) {
//                    String inRoomId = roomPlayerRedis.getActorRoomStatus(RoomUtils.getRoomHostId(videoListData.getRoomId()));
//                    // 房主在房间才置顶
//                    if (videoListData.getRoomId().equals(inRoomId)) {
//                        topList.add(naviVideoData);
//                        continue;
//                    }
//                }
                resultList.add(naviVideoData);
            }
            Comparator<NaviVideoData> personCountDesc = Comparator.comparing(NaviVideoData::getPersonCount).reversed();
            Comparator<NaviVideoData> roomIdAsc = Comparator.comparing(NaviVideoData::getRoomId);
            resultList.sort(personCountDesc.thenComparing(roomIdAsc));
//            if (!topList.isEmpty()) {
//                // 多个运营视频房间，人少的在前面
//                topList.sort(Comparator.comparing(NaviVideoData::getPersonCount).thenComparing(NaviVideoData::getRoomId));
//                resultList.addAll(0, topList);
//            }
            PageUtils.PageData<NaviVideoData> pageData = PageUtils.getPageData(resultList, page, VIDEO_PAGE_SIZE);
            // 处理麦位用户
            fillMicPersonList(pageData.list);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("list", pageData.list);
            jsonObject.put("nextUrl", pageData.nextPage == 0 ? "" : pageData.nextPage + "");
            return new ApiResult<>().ok(jsonObject);
        } catch (Exception e) {
            logger.error("get navi topic list error {}", e.getMessage(), e);
            return new ApiResult<>().error(HttpCode.SERVER_ERROR);
        }
    }

    private void fillMicPersonList(List<NaviVideoData> list) {
        ActorData actorData;
        MicPerson micPerson;
        for (NaviVideoData videoData : list) {
            List<String> micList = roomMicRedis.getRoomMicList(videoData.getRoomId());
            if (micList.size() < 4) {
                Set<String> roomActors = roomPlayerRedis.getRoomActors(videoData.getRoomId());
                for (String roomActor : roomActors) {
                    if (!micList.contains(roomActor) && micList.size() < 4) {
                        micList.add(roomActor);
                    }
                }
            }
            List<MicPerson> micPersonList = new ArrayList<>();
            for (String uid : micList) {
                actorData = actorDao.getActorDataFromCache(uid);
                if (null == actorData) {
                    continue;
                }
                micPerson = new MicPerson();
                micPerson.setUid(uid);
                micPerson.setUserHead(ImageUrlGenerator.generateMiniUrl(actorData.getHead()));
                micPersonList.add(micPerson);
                if (micPersonList.size() == 4) {
                    break;
                }
            }
            videoData.setMicPersonList(micPersonList);
        }
    }

    public List<MicPerson> getMicPersonList(String roomId, List<String> roomActors, int maxSize) {
        ActorData actorData;
        MicPerson micPerson;
        List<String> micList = roomMicRedis.getRoomMicList(roomId);
//        Set<String> roomActors = roomPlayerRedis.getRoomActors(roomId);
        for (String roomActor : roomActors) {
            if (!micList.contains(roomActor)) {
                micList.add(roomActor);
            }
            if (micList.size() == maxSize + 5) {
                break;
            }
        }
        List<MicPerson> micPersonList = new ArrayList<>();
        for (String uid : micList) {
            actorData = actorDao.getActorDataFromCache(uid);
            if (null == actorData) {
                continue;
            }
            micPerson = new MicPerson();
            micPerson.setUid(uid);
            micPerson.setUserHead(ImageUrlGenerator.generateMiniUrl(actorData.getHead()));
            micPersonList.add(micPerson);
            if (micPersonList.size() == maxSize) {
                break;
            }
        }
        return micPersonList;
    }


    public List<VideoInfoData> pageVideoList(int page, int pageSize, boolean filterLockRoom) {
        try {
            if (pageSize == -1) {
                pageSize = VIDEO_PAGE_SIZE;
            }
            List<VideoInfoData> resultList = new ArrayList<>();
            List<VideoListData> allVideoList = getAllVideoList();
            VideoInfoData videoInfoData;
            for (VideoListData videoListData : allVideoList) {
                if (CollectionUtils.isEmpty(videoListData.getVideoList())) {
                    continue;
                }
                if (filterLockRoom && roomPwdRedis.hasPwdFromCache(videoListData.getRoomId())) {
                    //过滤掉加密房间
                    continue;
                }
                videoInfoData = new VideoInfoData();
                BeanUtils.copyProperties(videoListData, videoInfoData);
                videoInfoData.setVideoList(videoListData.getVideoList());
                // 获取当前的视频标题
                videoInfoData.setTitle(videoListData.getCurVideo().getTitle());
                // 房间用户数量
                int personCount = roomPlayerRedis.getRoomActorsCount(videoListData.getRoomId());
                if (0 == personCount) {
                    continue;
                }
                videoInfoData.setPersonCount(personCount);
                // 视频房缩略图
                videoInfoData.setThumbnails(videoListData.getCurVideo().getThumbnails());
                resultList.add(videoInfoData);
            }
            Comparator<VideoInfoData> personCountDesc = Comparator.comparing(VideoInfoData::getPersonCount).reversed();
            resultList.sort(personCountDesc);
            PageUtils.PageData<VideoInfoData> pageData = PageUtils.getPageData(resultList, page, pageSize);
            for (VideoInfoData videoData : pageData.list) {
                String roomId = videoData.getRoomId();
                Set<String> roomActors = roomPlayerRedis.getRoomActors(roomId);
                videoData.setActorList(roomActors);
                String roomOwnerUid = videoData.getRoomId().split("r:")[1];
                ActorData actorData = actorDao.getActorDataFromCache(roomOwnerUid);
                videoData.setOwnerIcon(actorData.getHead());
            }
            return pageData.list;
        } catch (BeansException e) {
            logger.error("get video list by page error. {}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }
}
