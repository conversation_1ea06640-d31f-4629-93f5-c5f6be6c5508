package com.quhong.video.data;

import java.util.List;
import java.util.Set;

public class VideoInfoData {

    private String roomId;
    private String title;
    private int personCount;
    private int pwd;//1房间加锁，有密码
    private List<VideoData> videoList;
    private int playIndex;
    private int videoVersion; //视频版本号
    private String ownerIcon; //房主头像
    private Set<String> actorList; //再房用户列表
    private String thumbnails; //视频房缩略图

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getPersonCount() {
        return personCount;
    }

    public void setPersonCount(int personCount) {
        this.personCount = personCount;
    }

    public int getPwd() {
        return pwd;
    }

    public void setPwd(int pwd) {
        this.pwd = pwd;
    }

    public List<VideoData> getVideoList() {
        return videoList;
    }

    public void setVideoList(List<VideoData> videoList) {
        this.videoList = videoList;
    }

    public int getPlayIndex() {
        return playIndex;
    }

    public void setPlayIndex(int playIndex) {
        this.playIndex = playIndex;
    }

    public int getVideoVersion() {
        return videoVersion;
    }

    public void setVideoVersion(int videoVersion) {
        this.videoVersion = videoVersion;
    }

    public String getOwnerIcon() {
        return ownerIcon;
    }

    public void setOwnerIcon(String ownerIcon) {
        this.ownerIcon = ownerIcon;
    }

    public Set<String> getActorList() {
        return actorList;
    }

    public void setActorList(Set<String> actorList) {
        this.actorList = actorList;
    }

    public String getThumbnails() {
        return thumbnails;
    }

    public void setThumbnails(String thumbnails) {
        this.thumbnails = thumbnails;
    }
}
