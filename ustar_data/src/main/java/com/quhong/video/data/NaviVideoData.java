package com.quhong.video.data;

import com.quhong.vo.MicPerson;

import java.util.List;

public class NaviVideoData {

    private String roomId;
    private String thumbnails;
    private String title;
    private int personCount;
    private int pwd;//1房间有秘密
    private List<MicPerson> micPersonList;

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getThumbnails() {
        return thumbnails;
    }

    public void setThumbnails(String thumbnails) {
        this.thumbnails = thumbnails;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getPersonCount() {
        return personCount;
    }

    public void setPersonCount(int personCount) {
        this.personCount = personCount;
    }

    public int getPwd() {
        return pwd;
    }

    public void setPwd(int pwd) {
        this.pwd = pwd;
    }

    public List<MicPerson> getMicPersonList() {
        return micPersonList;
    }

    public void setMicPersonList(List<MicPerson> micPersonList) {
        this.micPersonList = micPersonList;
    }
}
