package com.quhong.video.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.service.data.BaseRedisHashData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class VideoListData extends BaseRedisHashData {
    private static final Logger logger = LoggerFactory.getLogger(VideoListData.class);

    private String roomId;
    private List<VideoData> videoList;
    private int playIndex;
    private int videoVersion;

    public VideoListData() {

    }

    @JSONField(serialize = false)
    public VideoData getCurVideo() {
        if (videoList.size() == 0) {
            return null;
        }
        if (playIndex < -1) {
            playIndex = 0;
        }
        if (playIndex >= videoList.size()) {
            playIndex = 0;
        }
        return videoList.get(playIndex);
    }

    @JSONField(serialize = false)
    public VideoData getNextVideo() {
        if (videoList.size() == 0) {
            return null;
        }
        if (playIndex < -1) {
            playIndex = 0;
        }
        int index = playIndex + 1;
        if (index >= videoList.size()) {
            return null;
        }
        return videoList.get(index);
    }

    public int searchPlayIndex(String videoId) {
        if (videoList.size() == 0) {
            return -1;
        }
        for (int i = 0; i < videoList.size(); i++) {
            VideoData videoData = videoList.get(i);
            if (videoData == null) {
                logger.error("videoData is null. index={} list.size={}", i, videoList.size());
                continue;
            }
            if (videoData.getVideoId().equals(videoId)) {
                return i;
            }
        }
        return -1;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public List<VideoData> getVideoList() {
        return videoList;
    }

    public void setVideoList(List<VideoData> videoList) {
        this.videoList = videoList;
    }

    public int getVideoVersion() {
        return videoVersion;
    }

    public void setVideoVersion(int videoVersion) {
        this.videoVersion = videoVersion;
    }

    public int getPlayIndex() {
        return playIndex;
    }

    public void setPlayIndex(int playIndex) {
        this.playIndex = playIndex;
    }
}
