package com.quhong.video.data;

public class VideoData {
    /**
     * youtube的videoId
     */
    private String videoId;
    /**
     * 标题
     */
    private String title;
    /**
     * 缩略图
     */
    private String thumbnails;

    private int syncProgressTime;

    private int status; // 1 播放 2 暂停

    private int syncTimeSec; // 上次同步时间，单位秒

    private int duration; // 总播放时长

    public VideoData(){

    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getThumbnails() {
        return thumbnails;
    }

    public void setThumbnails(String thumbnails) {
        this.thumbnails = thumbnails;
    }

    public int getSyncProgressTime() {
        return syncProgressTime;
    }

    public void setSyncProgressTime(int syncProgressTime) {
        this.syncProgressTime = syncProgressTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getSyncTimeSec() {
        return syncTimeSec;
    }

    public void setSyncTimeSec(int syncTimeSec) {
        this.syncTimeSec = syncTimeSec;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }
}
