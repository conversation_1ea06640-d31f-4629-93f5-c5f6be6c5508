package com.quhong.video;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomConstant;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.CommonDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RoomTheme;
import com.quhong.msg.obj.RoomVideoInfoObject;
import com.quhong.msg.room.RoomMicThemeChangeMsg;
import com.quhong.msg.room.RoomVideoInfoPushMsg;
import com.quhong.msg.room.VideoSwitchPushMsg;
import com.quhong.mysql.dao.RoomMicThemeDao;
import com.quhong.mysql.dao.UploadBackgroundDao;
import com.quhong.mysql.dao.VideoLogDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.RoomMicThemeData;
import com.quhong.mysql.data.UploadBackgroundData;
import com.quhong.mysql.data.VideoLogData;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.RoomUtils;
import com.quhong.video.data.SyncVideoData;
import com.quhong.video.data.VideoData;
import com.quhong.video.data.VideoListData;
import com.quhong.video.data.VideoProgressData;
import com.quhong.video.enums.RoomVideoHttpCode;
import com.quhong.video.enums.VideoStatus;
import com.quhong.video.redis.VideoRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 视频服务
 */
public class VideoCoreService {
    private static final Logger logger = LoggerFactory.getLogger(VideoCoreService.class);

    protected static final int SYNC_TYPE_OPT = 1;

    protected static final int SWITCH_OPEN = 1;
    protected static final int SWITCH_CLOSE = 2;
    protected static final int SWITCH_CHANGE_VIDEO = 3;

    @Autowired
    protected VideoRedis videoRedis;
    @Autowired
    protected RoomWebSender roomWebSender;
    @Autowired
    protected ActorDao actorDao;
    @Autowired
    protected MongoRoomDao mongoRoomDao;
    @Autowired
    protected VideoLogDao videoLogDao;
    @Resource
    private CommonDao commonDao;
    @Resource
    private UploadBackgroundDao uploadBackgroundDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private RoomMicThemeDao roomMicThemeDao;

    public ApiResult<VideoListData> getVideoList(String roomId) {
        try {
            VideoListData data = videoRedis.getVideoList(roomId);
            if (data != null) {
                return new ApiResult<VideoListData>().ok(data);
            }
            data = new VideoListData();
            data.setRoomId(roomId);
            data.setVideoVersion(0);
            data.setVideoList(new ArrayList<>());
            data.setPlayIndex(0);
            return new ApiResult<VideoListData>().ok(data);
        } catch (Exception e) {
            logger.error("get room video list error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return new ApiResult<VideoListData>().error(HttpCode.SERVER_ERROR);
    }

    public ApiResult<VideoListData> addVideoList(String roomId, String optUid, List<VideoData> videoList, int playIndex) {
        DistributeLock lock = videoRedis.createLock(roomId);
        try {
            lock.lock();
            ApiResult<VideoListData> videoListResult = getVideoList(roomId);
            if (videoListResult.isError()) {
                // 获取列表错误
                return videoListResult;
            }
            VideoListData videoListData = videoListResult.getData();
            videoListData.setVideoList(videoList);
            videoListData.setPlayIndex(playIndex);
            int version = videoRedis.incVersion(roomId);
            videoListData.setVideoVersion(version);
            videoRedis.saveVideoList(videoListData);
            sendMsg(videoListData, roomId, optUid, 0);
            return new ApiResult<VideoListData>().ok(videoListData);
        } catch (Exception e) {
            logger.error("add videoListData error. roomId={} optUid={} {}", roomId, optUid, e.getMessage(), e);
        } finally {
            lock.unlock();
        }
        return new ApiResult<VideoListData>().error(RoomVideoHttpCode.SERVER_ERROR);
    }
//
//    /**
//     * 下一个视频
//     *
//     * @param roomId
//     * @return
//     */
//    public ApiResult<VideoListData> nextVideo(String roomId, String uid) {
//        DistributeLock lock = videoRedis.createLock(roomId);
//        try {
//            lock.lock();
//            ApiResult<VideoListData> videoListResult = getVideoList(roomId);
//            if (videoListResult.isError()) {
//                // 获取列表错误
//                return videoListResult;
//            }
//            VideoListData videoListData = videoListResult.getData();
//            int playIndex = videoListData.getPlayIndex();
//            int size = videoListData.getVideoList().size();
//            if (size == 0) {
//                playIndex = -1;
//            } else {
//                playIndex = (playIndex + 1) % size;
//            }
//            videoListData.setPlayIndex(playIndex);
//            int version = videoRedis.incVersion(roomId);
//            videoListData.setVideoVersion(version);
//            videoRedis.saveVideoList(videoListData);
//            sendMsg(videoListData, roomId, uid, 0);
//            return new ApiResult<VideoListData>().ok(videoListData);
//        } catch (Exception e) {
//            logger.error("next room video error. oomId={} optUid={} {}", roomId, e.getMessage(), e);
//        } finally {
//            lock.unlock();
//        }
//        return new ApiResult<VideoListData>().error(RoomVideoHttpCode.SERVER_ERROR);
//    }

    public ApiResult<VideoListData> syncVideo(SyncVideoData syncVideoData) {
        if (syncVideoData.getProgressTime() <= 0) {
            syncVideoData.setProgressTime(0);
        }
        DistributeLock lock = videoRedis.createLock(syncVideoData.getRoomId());
        try {
            lock.lock();
            ApiResult<VideoListData> videoListResult = getVideoList(syncVideoData.getRoomId());
            if (videoListResult.isError()) {
                // 获取列表错误
                return videoListResult;
            }
            VideoListData videoListData = videoListResult.getData();
            VideoData videoData = videoListData.getCurVideo();
            if (videoData == null) {
                logger.info("can not find video. playIndex={} size={} roomId={}", videoListData.getPlayIndex(), videoListData.getVideoList().size(), syncVideoData.getRoomId());
                return ApiResult.getError(RoomVideoHttpCode.CAN_NOT_FIND_VIDEO);
            }
            if (!videoData.getVideoId().equals(syncVideoData.getVideoId())) {
                int playIndex = videoListData.searchPlayIndex(syncVideoData.getVideoId());
                if (playIndex == -1) {
                    logger.info("can not find video. serverVideoId={} videoId={} roomId={}", videoData.getVideoId(), syncVideoData.getVideoId(), syncVideoData.getRoomId());
                    return ApiResult.getError(RoomVideoHttpCode.PARAM_ERROR);
                } else {
                    logger.info("change video. oldPlayIndex={} playIndex={} serverVideoId={} videoId={} roomId={}", videoListData.getPlayIndex(), playIndex, videoData.getVideoId(), syncVideoData.getVideoId(), syncVideoData.getRoomId());
                    videoListData.setPlayIndex(playIndex);
                }
            }
            if (syncVideoData.getStatus() == VideoStatus.CLOSE) {
                int playIndex = videoListData.getPlayIndex() + 1;
                if (playIndex >= videoListData.getVideoList().size()) {
                    logger.info("video play end. roomId={} uid={}", syncVideoData.getRoomId(), syncVideoData.getUid());
                    closeVideo(syncVideoData.getRoomId(), syncVideoData.getUid());
                    videoListData.getCurVideo().setStatus(VideoStatus.CLOSE);
                    videoListData.setVideoVersion(videoListData.getVideoVersion() + 1);
                    return new ApiResult<VideoListData>().ok(videoListData);
                } else {
                    logger.info("video play next video. playIndex={} roomId={} uid={}", playIndex, syncVideoData.getRoomId(), syncVideoData.getUid());
                    syncVideoData.setStatus(VideoStatus.PLAY);
                    videoListData.setPlayIndex(playIndex);
                }
            }
            doSyncTime(videoListData, syncVideoData);
            int version = videoRedis.incVersion(syncVideoData.getRoomId());
            videoListData.setVideoVersion(version);
            videoRedis.saveVideoList(videoListData);
            sendMsg(videoListData, syncVideoData.getRoomId(), syncVideoData.getUid(), syncVideoData.getSyncType());
            return new ApiResult<VideoListData>().ok(videoListData);
        } catch (Exception e) {
            logger.error("sync video error. roomId={} optUid={} {}", syncVideoData.getRoomId(), e.getMessage(), e);
        } finally {
            lock.unlock();
        }
        return new ApiResult<VideoListData>().error(RoomVideoHttpCode.SERVER_ERROR);
    }

    protected VideoListData doSyncTime(VideoListData videoListData, SyncVideoData syncVideoData) {
        VideoData videoData = videoListData.getCurVideo();
        videoData.setSyncProgressTime(syncVideoData.getProgressTime());
        videoData.setSyncTimeSec(DateHelper.getNowSeconds());
        if (syncVideoData.getStatus() != VideoStatus.NONE) {
            videoData.setStatus(syncVideoData.getStatus());
        }
        return videoListData;
    }

    /**
     * 获取进度数据
     *
     * @param videoData
     * @return
     */
    public VideoProgressData getProgress(VideoData videoData) {
        int progressTime;
        if (videoData.getStatus() == VideoStatus.PLAY) {
            int curTime = DateHelper.getNowSeconds();
            progressTime = curTime + videoData.getSyncProgressTime() - videoData.getSyncTimeSec();
            if (progressTime < 0) {
                progressTime = 0;
            }
        } else {
            progressTime = videoData.getSyncProgressTime();
        }
        if (progressTime >= videoData.getDuration()) {
            progressTime = videoData.getDuration();
        }
        VideoProgressData progressData = new VideoProgressData();
        progressData.setProgressTime(progressTime);
        progressData.setTotalTime(videoData.getDuration());
        return progressData;
    }

    public void endVideo(VideoData videoData, String roomId) {
        VideoProgressData progressData = getProgress(videoData);
        VideoLogData logData = new VideoLogData();
        logData.setRoomId(roomId);
        logData.setVideoId(videoData.getVideoId());
        logData.setVideoTitle(videoData.getTitle());
        logData.setDuration(videoData.getDuration());
        logData.setPlayTime(progressData.getProgressTime());
        logData.setCtime((long) DateHelper.getNowSeconds());
        videoLogDao.insert(logData);
    }

    public void closeVideo(String roomId, String optUid) {
        closeVideo(roomId, optUid, true);
    }

    public void closeVideo(String roomId, String optUid, boolean isChangeTheme) {
        DistributeLock lock = videoRedis.createLock(roomId);
        try {
            lock.lock();
            mongoRoomDao.updateRoomVideoSwitch(roomId, 0);

            VideoListData videoListData = videoRedis.getVideoList(roomId);
            if (videoListData == null) {
                return;
            }
            // 切换主题到麦位主题
            if (isChangeTheme) {
                changeMicDefaultTheme(roomId);
            }

            VideoData videoData = videoListData.getCurVideo();
            if (videoData != null) {
                endVideo(videoData, videoListData.getRoomId());
            }
            logger.info("close video. videoId={} roomId={} optUid={}", videoData.getVideoId(), roomId, optUid);
            videoRedis.clearVideoList(roomId);
            sendVideoSwitchMsg(roomId, RoomUtils.getRoomHostId(roomId), SWITCH_CLOSE);
        } catch (Exception e) {
            logger.error("close video error. roomId={} optUid={} {}", roomId, e.getMessage(), e);
        } finally {
            lock.unlock();
        }
    }

//    private void changeMicDefaultTheme2(String roomId) {
//        MongoRoomData roomData = mongoRoomDao.findData(roomId);
//        int nowMicTheme = roomData.getMic_theme() == 0 ? RoomConstant.MIC_THEME_DEFAULT_ID : roomData.getMic_theme();
//        int oldMicTheme = voiceMicThemeRedis.getRecentlyTheme(roomData.getRid(), VoiceMicThemeRedis.VIDEO_TYPE);
//        if (oldMicTheme != RoomConstant.MIC_THEME_DEFAULT_ID && oldMicTheme != 0) {
//            if (AppVersionUtils.versionCheck(849, switchDTO)) {
//                logger.info("voice room recover from video micTheme={} roomId={} uid={}", oldMicTheme, roomData.getRid(), uid);
//                roomThemeService.changeMicTheme(roomData, "", oldMicTheme);
//            } else {
//                if (nowMicTheme != RoomConstant.MIC_THEME_DEFAULT_ID) {
//                    //手动关闭视频以后，切换房间麦位主题为default
//                    doChangeMicThemeDefault(roomData);
//                }
//            }
//            voiceMicThemeRedis.deleteRecentlyTheme(roomId,VoiceMicThemeRedis.VIDEO_TYPE);
//        }else if (nowMicTheme != RoomConstant.MIC_THEME_DEFAULT_ID) {
//            doChangeMicThemeDefault(roomData);
//        }
//    }

    private void changeMicDefaultTheme(String roomId) {
        MongoRoomData roomData = mongoRoomDao.findData(roomId);
        int nowMicTheme = roomData.getMic_theme() == 0 ? RoomConstant.MIC_THEME_DEFAULT_ID : roomData.getMic_theme();
        // if (nowMicTheme != RoomConstant.MIC_THEME_DEFAULT_ID) {
        //     doChangeMicThemeDefault(roomData);
        // }

        RoomMicThemeData roomMicThemeData = roomMicThemeDao.selectMicThemeById(nowMicTheme);
        if (roomMicThemeData.getThemeType() > 0) {
            doChangeMicThemeDefault(roomData);
        }

    }

    public String doChangeMicThemeDefault(MongoRoomData roomData) {
        return doChangeMicThemeDefault(roomData, null);
    }

    public String doChangeMicThemeDefault(MongoRoomData roomData, Integer reqMode) {
        //更新房间主题图、麦位主题
        String roomId = roomData.getRid();
        int micTheme = RoomConstant.MIC_THEME_DEFAULT_ID;
        int themeBackType = RoomConstant.THEME_BG_TYPE_DEFAULT;
        String backUrl = getThemeBgUrl(roomData, 131, micTheme);
        mongoRoomDao.updateMicTheme(roomId, micTheme, themeBackType);
        //发送mars消息
        if (reqMode != null) {
            if (reqMode == RoomConstant.LIVE_ROOM_MODE) {
                micTheme = RoomConstant.MIC_THEME_LIVE_DEFAULT_ID;
            }
        } else {
            if (roomData.getRoomMode() == RoomConstant.LIVE_ROOM_MODE) {
                micTheme = RoomConstant.MIC_THEME_LIVE_DEFAULT_ID;
            }
        }

        RoomMicThemeChangeMsg changeMsg = new RoomMicThemeChangeMsg();
        changeMsg.setThemeId(micTheme);
        changeMsg.setBackUrl(backUrl == null ? "" : backUrl);
        changeMsg.setUid("");
        changeMsg.setUsername("");
        changeMsg.setHead("");
        changeMsg.setUserOpt(0);
        roomWebSender.sendRoomWebMsg(roomId, "", changeMsg, true);
        logger.info("room_back_url--doChangeMicThemeDefault 2166 roomData roomId:{} RoomMode:{} reqMode:{} changeMsg:{} ",
                roomData.getRid(), roomData.getRoomMode(), reqMode, changeMsg);
        return backUrl;
    }

    private String getThemeBgUrl(MongoRoomData roomData, int tid, int micTheme) {
        if (micTheme != 1) {
            RoomTheme roomTheme = commonDao.findOne(new Query(Criteria.where("tid").is(tid)), RoomTheme.class);
            if (roomTheme != null) {
                return roomTheme.getBgurl();
            }
            return "";
        }
        return getRoomLastTheme(roomData);
    }

    private String getRoomLastTheme(MongoRoomData roomData) {
        int theme = roomData.getTheme();
        if (theme >= 1000) {
            UploadBackgroundData bgData = uploadBackgroundDao.selectOne(theme);
            if (null != bgData) {
                return bgData.getBgUrl();
            }
        } else if (theme > 0) {
            RoomTheme roomTheme = commonDao.findOne(new Query(Criteria.where("tid").is(theme)), RoomTheme.class);
            if (null != roomTheme) {
                String thumbnailUrl = ImageUrlGenerator.generateRoomTopicUrl(roomTheme.getBgurl());
                return thumbnailUrl;
            }
        }
        return "";
    }

    protected void sendMsg(VideoListData videoListData, String roomId, String fromUid, int syncType) {
        VideoData videoData = videoListData.getCurVideo();
        if (videoData == null) {
            return;
        }
        VideoProgressData progressData = getProgress(videoData);
        RoomVideoInfoPushMsg msg = new RoomVideoInfoPushMsg();
        RoomVideoInfoObject videoInfoObject = new RoomVideoInfoObject();
        videoInfoObject.setVideoId(videoData.getVideoId());
        videoInfoObject.setTitle(videoData.getTitle());
        videoInfoObject.setThumbnails(videoData.getThumbnails());
        if (progressData.isEnd()) {
            videoInfoObject.setStatus(VideoStatus.CLOSE);
        } else {
            videoInfoObject.setStatus(videoData.getStatus());
        }
        videoInfoObject.setDuration(progressData.getTotalTime());
        videoInfoObject.setProgressTime(progressData.getProgressTime());
        msg.setVideoInfo(videoInfoObject);
        msg.setVideoVersion(videoListData.getVideoVersion());
        msg.setSyncType(syncType);
        roomWebSender.sendRoomWebMsg(roomId, fromUid, msg, true);
    }

    public void sendVideoSwitchMsg(String roomId, String uid, int switchType) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (actorData == null) {
                    logger.error("send video switch error. can not find actor data. roomId={} uid={}", roomId, uid);
                    return;
                }
                VideoSwitchPushMsg msg = new VideoSwitchPushMsg();
                msg.setVideo_switch(switchType);
                msg.setName(actorData.getName());
                msg.setAid(uid);
                msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(uid)));
                logger.info("send video switch msg to room. switchType={} roomId={} uid={}", switchType, roomId, uid);
                roomWebSender.sendRoomWebMsg(roomId, null, msg, true);
            }
        });
    }

}
