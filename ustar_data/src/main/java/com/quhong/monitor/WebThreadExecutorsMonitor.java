package com.quhong.monitor;

import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.context.WebServerApplicationContext;
import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
import org.springframework.boot.web.server.WebServer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ThreadPoolExecutor;

@Component
public class WebThreadExecutorsMonitor {
    private static final Logger logger = LoggerFactory.getLogger(WebThreadExecutorsMonitor.class);
    private static final int POOL_WARNING_COUNT = 100;

    @Autowired(required = false)
    private WebServerApplicationContext applicationContext;
    private ThreadPoolExecutor executor;
    /**
     * 告警检查的checker，用于通知告警开始和结束
     */
    private MonitorChecker monitorChecker;
    /**
     * 上一次线程池完成总数，用于计算
     */
    private long lastCompleteCount;
    /**
     * 警告开始时间
     */
    private long warningStartTime = 0;
    private int warningBlockCount = POOL_WARNING_COUNT;

    public WebThreadExecutorsMonitor() {
        this.monitorChecker = new MonitorChecker("tomcat_threads");
    }

    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(60 * 1000) {
            @Override
            protected void execute() {
                tickMonitor();
            }
        });
    }

    public void tickMonitor() {
        if (executor == null) {
            WebServer webServer = applicationContext.getWebServer();
            if (webServer instanceof TomcatWebServer) {
                executor = (ThreadPoolExecutor) ((TomcatWebServer) webServer).getTomcat().getConnector().getProtocolHandler().getExecutor();
            }
        }
        // 最大线程数
        int maxPoolSize = executor.getMaximumPoolSize();
        // 活跃线程数
        int activeCount = executor.getActiveCount();
        // 等待任务数量
        long waitingTaskCount = executor.getQueue().size();
        // 完成任务总数
        long completeCount = executor.getCompletedTaskCount();
        // 每分钟完成的任务数
        long completeCountPerMin = 0;
        if (lastCompleteCount != 0) {
            completeCountPerMin = completeCount - lastCompleteCount;
        }
        // 每分钟记录一次
        lastCompleteCount = completeCount;
        // 活跃线程数达到总线程数的80% 开始告警
        if (activeCount >= maxPoolSize * 0.8) {
            if (!monitorChecker.inWarning()) {
                sendMonitor(activeCount, maxPoolSize, waitingTaskCount);
            }
        } else {
            if (warningStartTime > 0) {
                warningStartTime = 0;
                logger.info("tomcat threads. stop warning.");
                monitorChecker.stopWarning();
            }
        }
        logger.info("task pool activePerLarge={} / {} waitingCount={} completeCountPerMin={} completeCount={} pool=tomcat", activeCount, maxPoolSize, waitingTaskCount, completeCountPerMin, completeCount);
    }

    private void sendMonitor(long activeCount, long largestPoolSize, long waitingTaskCount) {
        if (monitorChecker.inWarning()) {
            return;
        }
        String content = "Thread pool tomcat backlog of tasks. \nactivePerLarge=" + activeCount + "/" + largestPoolSize + "\nwaitingTaskCount=" + waitingTaskCount;
        monitorChecker.startWarning("Thread pool tomcat backlog of tasks.", content);
    }
}
