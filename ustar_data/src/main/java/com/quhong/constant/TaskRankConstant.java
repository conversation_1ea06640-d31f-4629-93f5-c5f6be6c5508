package com.quhong.constant;

import java.util.Arrays;
import java.util.List;

/**
 * taskRank活动相关常量
 */
public class TaskRankConstant {

    public static final int STATUS_INIT = 0;         // 未发布
    public static final int STATUS_ONLINE = 1;       // 线上状态
    public static final int STATUS_OFFLINE = 2;      // 下线状态

    public static final int TASK_INIT = 0;             // 未完成
    public static final int TASK_ENABLE_DRAW = 1;       // 已完成可领取
    public static final int TASK_FINISH = 2;          // 已领取

    public static final int RANK_TOP_N = 20;
    public static final List<String> RECHARGE_ITEM_LIST = Arrays.asList("google", "apple", "huawei", "admin");

    public static final int EXTRA_PARAM_TYPE_NOT = 0;        // 无过滤
    public static final int EXTRA_PARAM_TYPE_GIFT = 1;       // 礼物过滤
    public static final int EXTRA_PARAM_TYPE_GAME = 2;       // 游戏过滤

    // 榜单key
    public static final String RANK_PERSON_RECEIVE = "rank_person_receive";                               // 个人收礼流水
    public static final String RANK_PERSON_SEND = "rank_person_send";                                     // 个人送礼流水
    public static final String RANK_GAME_EARN = "rank_game_earn";                                         // 个人游戏赚钻
    public static final String RANK_RECHARGE_BEAN = "rank_recharge_bean";                                 // 个人充值钻石
    public static final String RANK_ROOM_DEVOTE = "rank_room_devote";                                     // 房间礼物流水榜
    public static final String RANK_ROOM_EVENT_DEVOTE = "rank_room_event_devote";                         // 房间event期间礼物流水

    // 任务key
    public static final String TASK_ON_MIC_TIME = "task_on_mic_time";                                        // 麦上时长：统计个人在任意语音房的麦上时长
    public static final String TASK_ON_MY_ROOM_MIC_TIME = "task_on_my_room_mic_time";                        // 个人语音房麦上时长：统计在个人语音房的麦上时长
    public static final String TASK_INVITE_DEVICE = "task_invite_device";                                    // 个人语音房邀请上麦设备数(房主)，统计房主在个人语音房向X设备发出邀请上麦，设备排重
    public static final String TASK_INVITE_DEVICE_VALID = "task_invite_device_valid";                        // 个人语音房有效上麦设备数：统计个人语音房上麦时长达标1分钟的设备数量，不限用户上麦方式，设备排重
    public static final String TASK_PLAY_ORDER_GAME = "task_play_order_game";                                // 个人语音房玩指定游戏局数：统计在个人语音房开启指定游戏的局数，不限由谁开启，限制游戏类型，需单选一款游戏
    public static final String TASK_CREATE_EVENT = "task_create_event";                                      // 个人语音房创建event场次：统计个人语音房创建event场次
    public static final String TASK_ROOM_MEMBER = "task_room_member";                                        // 个人语音房新增Member：统计个人语音房新增房间成员
    public static final String TASK_PERSON_EARN_GAME_BEAN = "task_person_earn_game_bean";                    // 个人玩指定游戏赢取钻石：统计个人玩指定游戏赢取的钻石数量
    public static final String TASK_PERSON_RECHARGE_BEAN = "task_person_recharge_bean";                      // 个人充值钻石：统计个人充值的钻石数量(充值渠道包含gp\apple\huawei)
    public static final String TASK_PERSON_FRIEND = "task_person_friend";                                    // 个人新增好友：统计个人新增好友设备数，互相添加为好友才被统计

    public static final String TASK_PERSON_SEND_GIFT_BEAN = "task_person_send_gift_bean";                    // 个人送出钻石礼物流水：统计个人送出钻石礼物流水，不限送礼场景
    public static final String TASK_PERSON_RECEIVE_GIFT_BEAN = "task_person_receive_gift_bean";              // 个人收到钻石礼物流水：统计个人收到钻石礼物流水,不限收礼场景
    public static final String TASK_ROOM_GIFT_BEAN = "task_room_gift_bean";                                  // 个人语音房钻石礼物流水：统计个人语音房钻石礼物流水
    public static final String TASK_ROOM_EVENT_GIFT_BEAN = "task_room_event_gift_bean";                      // 个人语音房event钻石礼物流水：统计个人语音房event期间的礼物流水
    public static final String TASK_ROOM_ORDER_GIFT_BEAN = "task_room_order_gift_bean";                      // 个人语音房指定钻石礼物流水：统计个人语音房指定钻石礼物流水
    public static final String TASK_ROOM_EVENT_ORDER_GIFT_BEAN = "task_room_event_order_gift_bean";          // 个人语音房event指定钻石礼物流水：统计个人语音房event期间指定钻石礼物流水
    public static final String TASK_PERSON_RECEIVE_ORDER_GIFT_BEAN = "task_person_receive_order_gift_bean";  // 个人收到指定钻石礼物流水：统计个人收到指定钻石礼物流水
    public static final String TASK_PERSON_SEND_ORDER_GIFT_BEAN = "task_person_send_order_gift_bean";        // 个人送出指定钻石礼物流水：统计个人送出指定钻石礼物流水

}
