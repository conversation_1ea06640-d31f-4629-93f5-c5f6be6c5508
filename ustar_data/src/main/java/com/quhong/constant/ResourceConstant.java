package com.quhong.constant;

import com.quhong.enums.BaseDataResourcesConstant;

import java.util.HashMap;
import java.util.Map;

public class ResourceConstant {
    public static final String OTHER = "other"; // 其他资源
    public static final String GIFT = "gift"; // 背包礼物
    public static final String MIC = "mic"; // 麦位框
    public static final String BUDDLE = "buddle"; // 气泡框
    public static final String RIDE = "ride"; // 入场动画
    public static final String RIPPLE = "ripple"; // 麦位声波
    public static final String DIAMOND = "diamond"; // 钻石
    public static final String BADGE = "badge"; // 勋章
    public static final String FLOAT_SCREEN = "float_screen"; // 浮屏
    public static final String BACK_GROUND = "background";   // 房间背景
    public static final String HONOR_TITLE = "honor_title";   // 荣誉称号
    public static final String ENTRY_EFFECT = "entry_effect";   // 进房通知

    // 幸运抽奖使用
    public static final String HEART = "heart"; // 心心
    public static final String ONCE_AGAIN = "once_again"; // 再来一次
    public static final String THANKS = "thanks"; // 谢谢惠顾
    public static final Map<String, Integer> NAME_TYPE_MAP = new HashMap<>();

    // 回归活动配置
    public static final int BACK_AC_DAY_TIME = 7 * 86400;
    public static final String BACK_AC_DESC = "back user activity";
    public static final String BACK_AC_RECHARGE_DESC = "Back User-recharge reward";
    public static final String BACK_AC_ACTIVE_DESC = "Back User-active days reward";
    public static final String BACK_AC_FRIENDS_DESC = "Back User-make friends reward";
    public static final String BACK_AC_GIFT_DESC = "Back User-send gift reward";

    // 邀请裂变活动
    public static final String INVITE_FISSION_AC_DESC = "invite fission activity";

    static {
        NAME_TYPE_MAP.put(BADGE, BaseDataResourcesConstant.TYPE_BADGE);
        NAME_TYPE_MAP.put(MIC, BaseDataResourcesConstant.TYPE_MIC);
        NAME_TYPE_MAP.put(RIDE, BaseDataResourcesConstant.TYPE_RIDE);
        NAME_TYPE_MAP.put(GIFT, BaseDataResourcesConstant.TYPE_BAG_GIFT);
        NAME_TYPE_MAP.put(BUDDLE, BaseDataResourcesConstant.TYPE_BUDDLE);
        NAME_TYPE_MAP.put(RIPPLE, BaseDataResourcesConstant.TYPE_RIPPLE);
        NAME_TYPE_MAP.put(FLOAT_SCREEN, BaseDataResourcesConstant.TYPE_FLOAT_SCREEN);
        NAME_TYPE_MAP.put(BACK_GROUND, BaseDataResourcesConstant.TYPE_MINE_BACKGROUND);
        NAME_TYPE_MAP.put(HONOR_TITLE, BaseDataResourcesConstant.TYPE_HONOR_TITLE);
        NAME_TYPE_MAP.put(ENTRY_EFFECT, BaseDataResourcesConstant.TYPE_ENTRY_EFFECT);
    }
}
