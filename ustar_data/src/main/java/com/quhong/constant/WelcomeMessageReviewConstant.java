package com.quhong.constant;

/**
 * 欢迎消息审核相关常量
 *
 * <AUTHOR>
 * @date 2025/8/25 16:18
 */
public class WelcomeMessageReviewConstant {

    /**
     * 审核状态常量
     */
    public static final Integer REVIEW_STATUS_PENDING = 1;    // 待审核
    public static final Integer REVIEW_STATUS_APPROVED = 2;   // 通过
    public static final Integer REVIEW_STATUS_REJECTED = 3;   // 拒绝

    /**
     * 拒绝原因最大长度
     */
    public static final int REJECT_REASON_MAX_LENGTH = 50;

    /**
     * 分表数量
     */
    public static final int TABLE_COUNT = 16;

    /**
     * 表名前缀
     */
    public static final String TABLE_PREFIX = "t_welcome_message_review";

    /**
     * 审核状态字符串常量
     */
    public static final String STATUS_PENDING_STR = "PENDING";
    public static final String STATUS_APPROVED_STR = "APPROVED";
    public static final String STATUS_REJECTED_STR = "REJECTED";
}
