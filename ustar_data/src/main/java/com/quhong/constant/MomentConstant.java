package com.quhong.constant;


public class MomentConstant {

    /**
     * MomentNotice action_atype 1点赞 2评论 3发布朋友圈@别人 4评论区@别人 5评论点赞 6回复评论 7礼物打赏
     * 8 移除话题 9 话题设置热门 10 话题取消热门 11 话题设置管理员 12 话题取消管理员 13 话题审核通过 14 话题审核拒绝
     * 15 话题拉黑用户 16 用户首次关注话题
     */
    public static final int NOTICE_LIKE = 1;
    public static final int NOTICE_COMMENT = 2;
    public static final int NOTICE_PUBLISH_AT = 3;
    public static final int NOTICE_COMMENT_AT = 4;
    public static final int NOTICE_COMMENT_LIKE = 5;
    public static final int NOTICE_COMMENT_REPLAY = 6;
    public static final int NOTICE_MOMENT_REWARD = 7;

    public static final int NOTICE_MOMENT_TOPIC_UNBIND = 8;
    public static final int NOTICE_MOMENT_TOPIC_SET_HOT= 9;
    public static final int NOTICE_MOMENT_TOPIC_CANCEL_HOT= 10;
    public static final int NOTICE_MOMENT_TOPIC_SET_ADMIN= 11;
    public static final int NOTICE_MOMENT_TOPIC_CANCEL_ADMIN= 12;
    public static final int NOTICE_MOMENT_TOPIC_PASS= 13;
    public static final int NOTICE_MOMENT_TOPIC_REFUSE= 14;
    public static final int NOTICE_MOMENT_TOPIC_ADD_BLACK= 15;
    public static final int NOTICE_MOMENT_TOPIC_ADD_FOLLOW= 16;

    /**
     * MomentData show 浏览权限  1 公开，2 朋友可见，3 仅自己可见
     *
     *
     */
    public static final int MOMENT_PUBLIC = 1;
    public static final int MOMENT_FRIENDS = 2;
    public static final int MOMENT_PRIVATE = 3;

    public static final int QUOTE_REPOST = 1; // 转发
    public static final int QUOTE_LINK = 2; // 链接
    public static final int QUOTE_YOUTUBE_LINK = 3; // YouTube链接
    public static final int QUOTE_OFFICIAL_LINK = 4; // YouStar官方链接
    public static final int QUOTE_SHARE_ROOM = 5; // 房间分享
    public static final int QUOTE_SHARE_ACTIVITY = 6; // 活动分享
    public static final int QUOTE_SHARE_FAMILY = 7; // 家族分享
    public static final int QUOTE_SHARE_ROOM_ACTIVITY = 8; // 房间活动分享

    public static final int TOPIC_ROLE_OWNER = 1; // 1 话题创建者
    public static final int TOPIC_ROLE_ADMIN = 2; // 2 话题管理员
    public static final int TOPIC_ROLE_MEMBER = 3;// 3 话题关注用户
    public static final int TOPIC_ROLE_ONLY_USE = 4;// 4 话题仅使用用户
    public static final int TOPIC_ROLE_BLACK = -1; // -1 话题拉黑用户

    public static final String NOT_CHECK_IMAGE_PREFIX = "not_check_image"; // 不做图片审核
}
