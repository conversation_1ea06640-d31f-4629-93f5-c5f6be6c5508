package com.quhong.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * roomBanner开关相关常量
 */
public class BannerSwitchConstant {

    public static final String LUDO_SWITCH = "ludo";   // ludo开关
    public static final String UMO_SWITCH = "umo_switch";   // umo开关
    public static final String MONSTER_CRUSH_SWITCH = "monster_crush_switch";   // 消消乐开关
    public static final String DOMINO_SWITCH = "domino_switch";   // 多米诺开关
    public static final String CARROM_POOL_SWITCH = "carrom_pool_switch";   // carrom_pool开关
    public static final String LUCKY_WHEEL_SWITCH = "lucky_wheel_switch";   // 幸运转盘开关
    public static final String SMART_WHEEL_SWITCH = "smart_wheel";   // 真心话转盘开关
    public static final String ROOM_VOTE_SWITCH = "room_vote";   // 房间投票开关
    public static final String DICE_SWITCH = "dice_switch";   // 骰子游戏开关
    public static final String LUCKY_NUM_SWITCH = "lucky_num_function";   // 幸运数字开关
    public static final String REPEAT_MSG_SWITCH = "repeat_msg_switch";   // 重复消息开关
    public static final String LUCKY_DICE_SWITCH = "lucky_dice_switch";   // 幸运骰子开关
    public static final String ROOM_GATHERING_SWITCH = "room_gathering_switch";   // 召集广播开关
    public static final String PK_SWITCH = "pk_switch";   // PK开关
    public static final String LUCKY_BOX_SWITCH = "lucky_box_switch";   // 幸运红包开关
    public static final String GUESS_SWITCH = "guess_switch";   // 猜拳开关
    public static final String VIDEO_SWITCH = "video_switch";   // 看youtube视频开关

    public static final Map<String, String> BANNER_SWITCH_CN_MAP = new HashMap<>();
    static {
        BANNER_SWITCH_CN_MAP.put(LUDO_SWITCH, "ludo开关");
        BANNER_SWITCH_CN_MAP.put(UMO_SWITCH, "umo开关");
        BANNER_SWITCH_CN_MAP.put(MONSTER_CRUSH_SWITCH, "消消乐开关");
        BANNER_SWITCH_CN_MAP.put(DOMINO_SWITCH, "多米诺开关");
        BANNER_SWITCH_CN_MAP.put(CARROM_POOL_SWITCH, "carrom_pool开关");
        BANNER_SWITCH_CN_MAP.put(LUCKY_WHEEL_SWITCH, "幸运转盘开关");
        BANNER_SWITCH_CN_MAP.put(SMART_WHEEL_SWITCH, "真心话转盘开关");
        BANNER_SWITCH_CN_MAP.put(ROOM_VOTE_SWITCH, "房间投票开关");
        BANNER_SWITCH_CN_MAP.put(DICE_SWITCH, "骰子游戏开关");
        BANNER_SWITCH_CN_MAP.put(LUCKY_NUM_SWITCH, "幸运数字开关");
        BANNER_SWITCH_CN_MAP.put(REPEAT_MSG_SWITCH, "重复消息开关");
        BANNER_SWITCH_CN_MAP.put(LUCKY_DICE_SWITCH, "幸运骰子开关");
        BANNER_SWITCH_CN_MAP.put(ROOM_GATHERING_SWITCH, "召集广播开关");
        BANNER_SWITCH_CN_MAP.put(PK_SWITCH, "PK开关");
        BANNER_SWITCH_CN_MAP.put(LUCKY_BOX_SWITCH, "幸运红包开关");
        BANNER_SWITCH_CN_MAP.put(GUESS_SWITCH, "猜拳开关");
        BANNER_SWITCH_CN_MAP.put(VIDEO_SWITCH, "看youtube视频开关");
    }

}
