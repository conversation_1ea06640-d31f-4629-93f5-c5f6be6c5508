package com.quhong.constant;

import com.quhong.enums.CommonMqTaskConstant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * taskRank活动相关常量
 */
public class ActivityComponentConstant {

    // 组件类型
    public static final int COMPONENT_TYPE_RANK = 0;    // 榜单组件
    public static final int COMPONENT_TYPE_TASK = 1;    // 任务组件
    public static final int COMPONENT_TYPE_DRAW = 2;   // 抽奖组件

    // 组件统计维度
    public static final int DIMEN_PERSON = 0;   // 个人
    public static final int DIMEN_ROOM = 1;     // 房间


    // 组件统计类型
    public static final int SCORE_TYPE_ACTION = 0;     // 次数
    public static final int SCORE_TYPE_DIAMOND = 1;    // 钻石数
    public static final int SCORE_TYPE_NUMBER = 2;     // 数量
    public static final int SCORE_TYPE_MONEY = 3;      // 金额

    public static final int STATUS_INIT = 0;         // 未发布
    public static final int STATUS_ONLINE = 1;       // 线上状态
    public static final int STATUS_OFFLINE = 2;      // 下线状态

    // 任务及榜单周期
    public static final int PERIOD_DAILY = 0;        // 日
    public static final int PERIOD_WEEK = 1;         // 周
    public static final int PERIOD_ACTIVITY = 2;     // 活动周期


    // 任务状态
    public static final int TASK_TYPE_COMMON = 0;    // 普通任务
    public static final int TASK_TYPE_NODE = 1;      // 节点任务
    public static final int TASK_TYPE_GROUP = 2;     // 组任务

    // 任务奖励领取方式
    public static final int REWARD_WAY_0 = 0;        // 手动领取
    public static final int REWARD_WAY_1 = 1;        // 自动下发

    // 任务状态
    public static final int TASK_STATUS_INIT = 0;             // 未完成
    public static final int TASK_STATUS_REWARD = 1;           // 已完成可领取
    public static final int TASK_STATUS_FINISH = 2;           // 已完成

    public static final int RANK_TOP_N = 20;
    public static final List<String> RECHARGE_ITEM_LIST = Arrays.asList("google", "apple", "huawei");

    // 榜单key
    public static final String RANK_PERSON_GIFT_SEND = "rank_person_gift_send";             // 个人送礼榜单
    public static final String RANK_PERSON_GIFT_RECEIVE= "rank_person_gift_receive";        // 个人收礼榜单
    public static final String RANK_PERSON_GAME_EARN_DIAMOND = "rank_person_game_earn_diamond";    // 个人游戏赚钻石榜单
    public static final String RANK_PERSON_RECHARGE_DIAMOND = "rank_person_recharge_diamond";      // 个人充值钻石榜单
    public static final String RANK_PERSON_RECHARGE_MONEY = "rank_person_recharge_money";          // 个人充值金额榜单
    public static final String RANK_ROOM_GIFT_DEVOTE = "rank_room_gift_devote";             // 房间礼物榜单
    public static final String RANK_ROOM_EVENT_DEVOTE = "rank_room_event_devote";           // 房间event期间礼物榜单


    // 使用浮点数的榜单key
    public static final List<String> FLOAT_RANK_KEY_LIST = Arrays.asList(RANK_PERSON_RECHARGE_MONEY);

    // 任务key
    public static final String TASK_ON_MIC_TIME = "task_on_mic_time";                                        // 麦上时长：统计个人在任意语音房的麦上时长
    public static final String TASK_ON_MY_ROOM_MIC_TIME = "task_on_my_room_mic_time";                        // 个人语音房麦上时长：统计在个人语音房的麦上时长
    public static final String TASK_ON_MIC_ROOM_DEVICE_VALID = "task_invite_device_valid";                   // 个人语音房有效上麦设备数：统计个人语音房上麦时长达标1分钟的设备数量，不限用户上麦方式，设备排重
    public static final String TASK_INVITE_DEVICE = "task_invite_device";                                    // 个人语音房邀请上麦设备数(房主)，统计房主在个人语音房向X设备发出邀请上麦，设备排重

    public static final String TASK_PLAY_ORDER_GAME = "task_play_order_game";                                // 个人语音房玩指定游戏局数：统计在个人语音房开启指定游戏的局数，不限由谁开启，限制游戏类型，需单选一款游戏
    public static final String TASK_CREATE_EVENT = "task_create_event";                                      // 个人语音房创建event场次：统计个人语音房创建event场次
    public static final String TASK_ROOM_MEMBER = "task_room_member";                                        // 个人语音房新增Member：统计个人语音房新增房间成员
    public static final String TASK_PERSON_EARN_GAME_BEAN = "task_person_earn_game_bean";                    // 个人玩指定游戏赢取钻石：统计个人玩指定游戏赢取的钻石数量
    public static final String TASK_PERSON_RECHARGE_BEAN = "task_person_recharge_bean";                      // 个人充值钻石：统计个人充值的钻石数量(充值渠道包含gp\apple\huawei)
    public static final String TASK_PERSON_FRIEND = "task_person_friend";                                    // 个人新增好友：统计个人新增好友设备数，互相添加为好友才被统计

    public static final String TASK_PERSON_SEND_GIFT_BEAN = "task_person_send_gift_bean";                    // 个人送出钻石礼物流水：统计个人送出钻石礼物流水，不限送礼场景
    public static final String TASK_PERSON_RECEIVE_GIFT_BEAN = "task_person_receive_gift_bean";              // 个人收到钻石礼物流水：统计个人收到钻石礼物流水,不限收礼场景
    public static final String TASK_ROOM_GIFT_BEAN = "task_room_gift_bean";                                  // 个人语音房钻石礼物流水：统计个人语音房钻石礼物流水
    public static final String TASK_ROOM_EVENT_GIFT_BEAN = "task_room_event_gift_bean";                      // 个人语音房event钻石礼物流水：统计个人语音房event期间的礼物流水
    public static final String TASK_ROOM_ORDER_GIFT_BEAN = "task_room_order_gift_bean";                      // 个人语音房指定钻石礼物流水：统计个人语音房指定钻石礼物流水
    public static final String TASK_ROOM_EVENT_ORDER_GIFT_BEAN = "task_room_event_order_gift_bean";          // 个人语音房event指定钻石礼物流水：统计个人语音房event期间指定钻石礼物流水
    public static final String TASK_PERSON_RECEIVE_ORDER_GIFT_BEAN = "task_person_receive_order_gift_bean";  // 个人收到指定钻石礼物流水：统计个人收到指定钻石礼物流水
    public static final String TASK_PERSON_SEND_ORDER_GIFT_BEAN = "task_person_send_order_gift_bean";        // 个人送出指定钻石礼物流水：统计个人送出指定钻石礼物流水

    // 消息item对应业务item的Map
    public static final String SEND_GIFT_ITEM = "send_gift";
    public static final String RECEIVE_GIFT_ITEM = "receive_gift";
    public static final String RECHARGE_ITEM = "recharge_item";

    public static final Map<String, List<String>> MSG_ITEM_BUSINESS_MAP = new HashMap<>();
    public static final String RANK_ROOM_PREFIX = "rank_room";         // 房间维度榜单前缀
    public static final String RANK_PREFIX = "rank";                   // 用户维度榜单前缀
    public static final String TASK_ROOM_PREFIX = "task_room";         // 房间维度任务前缀
    public static final String TASK_PREFIX = "task";                   // 用户维度任务前缀

    static {
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.ON_MIC_TIME, Arrays.asList(TASK_ON_MIC_TIME, TASK_ON_MY_ROOM_MIC_TIME, TASK_ON_MIC_ROOM_DEVICE_VALID));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.INVITE_USER_ACTION, Arrays.asList(TASK_INVITE_DEVICE));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_LUDO, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_JACKAROO, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_CARROM_POOL, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_MONSTER_CRUSH, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_BALOOT, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_UMO, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_DOMINO, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_WHEEL, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.CREATE_VOTE, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_PK_GAME, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_TRUTH_DARE_WHEEL, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.PLAY_TRUTH_DARE_V2_WHEEL, Arrays.asList(TASK_PLAY_ORDER_GAME));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.CREATE_ROOM_EVENT, Arrays.asList(TASK_CREATE_EVENT));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.JOIN_ROOM_MEMBER, Arrays.asList(TASK_ROOM_MEMBER));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.WIN_LUDO, Arrays.asList(TASK_PERSON_EARN_GAME_BEAN, RANK_PERSON_GAME_EARN_DIAMOND));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.WIN_JACKAROO, Arrays.asList(TASK_PERSON_EARN_GAME_BEAN, RANK_PERSON_GAME_EARN_DIAMOND));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.WIN_CARROM_POOL, Arrays.asList(TASK_PERSON_EARN_GAME_BEAN, RANK_PERSON_GAME_EARN_DIAMOND));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.WIN_MONSTER_CRUSH, Arrays.asList(TASK_PERSON_EARN_GAME_BEAN, RANK_PERSON_GAME_EARN_DIAMOND));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.WIN_BALOOT, Arrays.asList(TASK_PERSON_EARN_GAME_BEAN, RANK_PERSON_GAME_EARN_DIAMOND));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.WIN_UMO, Arrays.asList(TASK_PERSON_EARN_GAME_BEAN, RANK_PERSON_GAME_EARN_DIAMOND));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.WIN_DOMINO, Arrays.asList(TASK_PERSON_EARN_GAME_BEAN, RANK_PERSON_GAME_EARN_DIAMOND));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.WIN_LUCKY_WHEEL, Arrays.asList(TASK_PERSON_EARN_GAME_BEAN, RANK_PERSON_GAME_EARN_DIAMOND));
        MSG_ITEM_BUSINESS_MAP.put(CommonMqTaskConstant.ADD_FRIEND, Arrays.asList(TASK_PERSON_FRIEND));
        MSG_ITEM_BUSINESS_MAP.put(SEND_GIFT_ITEM, Arrays.asList(RANK_PERSON_GIFT_SEND, RANK_ROOM_GIFT_DEVOTE, RANK_ROOM_EVENT_DEVOTE, TASK_PERSON_SEND_GIFT_BEAN, TASK_ROOM_GIFT_BEAN, TASK_ROOM_EVENT_GIFT_BEAN, TASK_ROOM_ORDER_GIFT_BEAN, TASK_ROOM_EVENT_ORDER_GIFT_BEAN, TASK_PERSON_SEND_ORDER_GIFT_BEAN));
        MSG_ITEM_BUSINESS_MAP.put(RECEIVE_GIFT_ITEM, Arrays.asList(RANK_PERSON_GIFT_RECEIVE, TASK_PERSON_RECEIVE_GIFT_BEAN, TASK_PERSON_RECEIVE_ORDER_GIFT_BEAN));
        MSG_ITEM_BUSINESS_MAP.put(RECHARGE_ITEM, Arrays.asList(RANK_PERSON_RECHARGE_DIAMOND, RANK_PERSON_RECHARGE_MONEY, TASK_PERSON_RECHARGE_BEAN));
    }


}
