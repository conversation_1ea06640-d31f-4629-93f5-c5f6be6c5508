package com.quhong.constant;

/**
 * <AUTHOR>
 * @date 2022/6/8
 */
public class FriendConstant {

    public static final Integer SOURCE_FRIEND_LIKE = 1;
    public static final Integer SOURCE_FRIEND_FOLLOW = 2;
    public static final Integer SOURCE_FRIEND_ADD = 3;
    public static final Integer SOURCE_FRIEND_MATCH = 4 ; // 交友匹配
    public static final Integer SOURCE_SAY_HELLO = 5; // pro版招呼成为好友

    // sstatus 状态值
    public static final Integer TALK_SHOW_OFFLINE = 0;
    public static final Integer TALK_SHOW_ONLINE = 1;
    public static final Integer TALK_SHOW_BUSY = 2;
    public static final Integer TALK_SHOW_SELF_ROOM = 3;
    public static final Integer TALK_SHOW_OTHER_ROOM = 4;
    public static final Integer USER_IN_GAME = 5;

    public static final Integer TALK_STATUS_OFFLINE = 0; // 用户在线状态：离线
    public static final Integer TALK_STATUS_ONLINE = 1; // 用户在线状态：在线
    public static final Integer TALK_VSTATUS_NOT_DISTURB = 0; // 视频拒绝聊天状态
    public static final Integer TALK_VSTATUS_ACCEPT = 1; // 接收视频聊天邀请

    public static final Integer OPERATE_DEFAULT = 0; // 还未处理好友申请
    public static final Integer OPERATE_AGREE = 1; // 同意好友申请
    public static final Integer OPERATE_REJECT = 2; // 拒绝好友申请

    public static final Integer FRIEND_NUM_ADD = 1; // 新增好友
    public static final Integer FRIEND_NUM_DELETE = 2; // 删除好友

    public static final Integer FRIEND = 0; // 已成为好友

    public static final int LIST_TYPE_FRIENDS = 1; // 好友列表
    public static final int LIST_TYPE_FOLLOWING = 2; // 关注列表
    public static final int LIST_TYPE_FOLLOWERS = 3; // 粉丝列表
}
