package com.quhong.constant;


public class MoneyTypeConstant {

    /**
     * 类型
     * */
    public static final Integer HONOR_TYPE = 1;                      // 荣誉充值类型[google/apple/huawei/中台/admin_honor]
    public static final Integer NON_HONOR_TYPE = 5;                  // 非荣誉充值类型
    public static final Integer FINGER_GUESS_WIN = 70;               // 猜拳游戏胜利
    public static final Integer MOMENT_RECEIVE_GIFT = 212;           // 朋友圈接收礼物奖励
    public static final Integer SEND_GIFT = 301;                     // 发礼物
    public static final Integer RECEIVE_GIFT = 302;                  // 接收礼物返钻
    public static final Integer LUDO_WIN = 924;                      // LUDO游戏胜利
    public static final Integer FRUIT_PARTY_WIN = 921;               // 水果机游戏中奖
    public static final Integer UMO_WIN = 927;                       // UMO游戏胜利
    public static final Integer GAME_ANGEL_DEVIL_WIN = 936;          // 天使恶魔游戏返钻
    public static final Integer GAME_HORSE_RACE_WIN = 938;           // 赛马游戏返钻
    public static final Integer GAME_CRASH_WIN = 940;                // 火箭 游戏返钻
    public static final Integer SLOTS_GAME_WIN = 945;                // 老虎机
    public static final Integer FISH_GAME_WIN = 947;                 // 扑鱼游戏奖励
    public static final Integer FAST_GAME_WIN = 952;                 // Fast游戏奖励

    // 砸蛋相关
    public static final Integer SMASH_COST_TYPE = 910;
    public static final Integer SMASH_AWARD_TYPE = 911;

    /**
     * 任务转盘
     */
    public static final Integer TASK_TURNTABLE_TYPE = 215;


    /**
     *  title及desc
     */

    // 砸蛋相关
    public static final String SMASH_COST_TITLE = "Egg smashing game fee";
    public static final String SMASH_COST_DESC = "Egg smashing game fee";
    public static final String SMASH_AWARD_TITLE = "Egg smashing game rewards";
    public static final String SMASH_AWARD_DESC = "Egg smashing game rewards";


    public static final String TASK_TURNTABLE_TITLE = "Task turntable";
    public static final String TASK_TURNTABLE_DESC = "Task turntable game rewards";

    // 回归活动
    public static final int BACK_AC_DIAMOND_TYPE = 501;
    public static final String BACK_AC_DIAMOND_TITLE = "Back User Activity rewards";

    //邀请裂变活动
    public static final int INVITE_FISSION_AC_DIAMOND_TYPE = 502;
    public static final String INVITE_FISSION_AC_DIAMOND_TITLE = "Invite rewards";

    //捕鱼活动 (活动模块小游戏)
    public static final int CATCH_FISH_TYPE = 915;
    public static final String CATCH_FISH_FEE_TITLE = "Fishing King game fee";
    public static final String CATCH_FISH_REWARD_TITLE = "Fishing King rewards";
}
