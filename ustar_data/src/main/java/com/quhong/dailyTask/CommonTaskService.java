package com.quhong.dailyTask;

import com.alibaba.fastjson.JSON;
import com.quhong.data.CommonMqTopicData;
import com.quhong.enums.CommonMqTaskConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/9/25
 */
@Component
public class CommonTaskService {

    private static final Logger logger = LoggerFactory.getLogger(CommonTaskService.class);

    private static final String USER_TASK_KEY = "user_daily_task";
    private static final String ROOM_LEVEL_TASK_KEY = "user_daily_task.room_level";
    private static final String USER_FRIEND_LEVEL_KEY = "user_friend_level";
    @Resource
    private RabbitTemplate rabbitTemplate;

    public void sendCommonTaskMq(CommonMqTopicData data) {
        try {
            String jsonString = JSON.toJSONString(data);
            String routingKey;
            String item = data.getItem();
            if (CommonMqTaskConstant.WELCOME_USER_IN_ROOM.equals(item) || CommonMqTaskConstant.KICK_OUT_ROOM.equals(item)
                    || CommonMqTaskConstant.USER_MSG_VIOLATION.equals(item) || CommonMqTaskConstant.USER_PROFILE_VIOLATION.equals(item)
                    || CommonMqTaskConstant.USER_MOMENT_VIOLATION.equals(item) || CommonMqTaskConstant.ROOM_PROFILE_VIOLATION.equals(item)
                    || CommonMqTaskConstant.DEVICE_RISK_LOGIN.equals(item) || CommonMqTaskConstant.DEVICE_ACCOUNT_BAN.equals(item)) {
                routingKey = USER_FRIEND_LEVEL_KEY;
            } else {
                if (CommonMqTaskConstant.ON_MIC_TIME.equals(data.getItem()) || CommonMqTaskConstant.SEND_ROOM_GIFT.equals(data.getItem())
                        || CommonMqTaskConstant.JOIN_ROOM_MEMBER.equals(data.getItem()) || CommonMqTaskConstant.INVITE_USER_ON_MIC.equals(data.getItem())
                        || CommonMqTaskConstant.TEST_ADD_EXP_ITEM.equals(data.getItem())) {
                    routingKey = ROOM_LEVEL_TASK_KEY;
                } else {
                    routingKey = USER_TASK_KEY;
                }
                if (CommonMqTaskConstant.SEND_ROOM_GIFT.equals(item) || CommonMqTaskConstant.INVITE_USER_ON_MIC.equals(item)
                        || CommonMqTaskConstant.DAILY_LOGIN.equals(item)) {
                    routingKey = routingKey + "." + USER_FRIEND_LEVEL_KEY;
                }
            }
            // logger.info("send fanout task data to mq.routingKey={} data={}", routingKey, jsonString);
            rabbitTemplate.convertAndSend(CommonMqTaskConstant.TASK_COMMON_EXCHANGE, routingKey, jsonString);
        } catch (Exception e) {
            logger.error("send fanout task data to mq error. data={}", JSON.toJSONString(data), e);
        }
    }
}
