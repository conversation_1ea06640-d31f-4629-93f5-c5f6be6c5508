package com.quhong.dailyTask;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.UserLevelTaskData;
import com.quhong.enums.UserLevelConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class UserLevelTaskService {
    private static final Logger logger = LoggerFactory.getLogger(UserLevelTaskService.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;


    public void upMicTask(UserLevelTaskData data) {
        // 满一分钟才计算
        if (data.getValue() >= UserLevelConstant.UP_MIC_PER) {
            sendTaskDataToMq(data);
        }
    }

    public void stayRoomTask(UserLevelTaskData data) {
        // 满两分钟才计算
        if (data.getValue() >= UserLevelConstant.STAY_ROOM_PER) {
            sendTaskDataToMq(data);
        }
    }

    public void sendTaskDataToMq(UserLevelTaskData data) {
        try {
            data.setDate_str(DateHelper.DEFAULT.formatDateInDay(new Date()));
//            logger.info("send user level daily task to mq. uid={} item={}", data.getUid(), data.getItem());
            rabbitTemplate.convertAndSend(UserLevelConstant.FOUNT_NAME, "", JSON.toJSONString(data));
        } catch (Exception e) {
            logger.error("send user level daily task to mq error. errorMsg={} data={}", data, e.getMessage());
        }
    }

}
