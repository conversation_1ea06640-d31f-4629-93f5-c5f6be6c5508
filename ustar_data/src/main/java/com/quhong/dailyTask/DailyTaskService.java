package com.quhong.dailyTask;

import com.alibaba.fastjson.JSON;
import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.DailyTaskMqData;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class DailyTaskService {
    private static final Logger logger = LoggerFactory.getLogger(DailyTaskService.class);

    public static final String ROUTE_KEY = "daily_task";

    public static final int TASK_ROOM_TEXT_MSG = 1;
    public static final int TASK_PLAY_END = 4;
    public static final int TASK_MIC_TIME = 5;
    public static final int TASK_CHAT_MSG = 6;

    private static final int CACHE_TIME = 3600 * 1000;

    private static final int EXPIRE_DAY = 2;


    private static final Map<Integer, Integer> COMPLETE_MAP = new HashMap<>();

    static {
        // 每日任务完成情况
        // 播放一首歌曲
        COMPLETE_MAP.put(TASK_PLAY_END, 1);
        // 上麦10分钟
        COMPLETE_MAP.put(TASK_MIC_TIME, 600);
        // 发送5条文字消息
        COMPLETE_MAP.put(TASK_ROOM_TEXT_MSG, 5);
        //发送私信消息
        COMPLETE_MAP.put(TASK_CHAT_MSG, 5);
    }

    public static int getDailyTaskRequestNum(int taskId) {
        Integer value = COMPLETE_MAP.get(taskId);
        return value == null ? 0 : value;
    }

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    private CacheMap<String, Boolean> cacheMap;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public DailyTaskService() {
        this.cacheMap = new CacheMap<>(CACHE_TIME);
    }

    public boolean playEnd(String uid) {
        return executeDailyTask(TASK_PLAY_END, uid, 1, true);
    }

    public boolean sendRoomText(String uid) {
        return executeDailyTask(TASK_ROOM_TEXT_MSG, uid, 1, true);
    }

    public boolean roomMicTime(String uid, int timeSecond, boolean completeRecordAndSend) {
        return executeDailyTask(TASK_MIC_TIME, uid, timeSecond, completeRecordAndSend);
    }

    public boolean sendChatMsg(String uid) {
        return executeDailyTask(TASK_CHAT_MSG, uid, 1, true);
    }

    public boolean executeDailyTask(int taskId, String uid, int addValue, boolean completeRecordAndSend) {
        if (checkComplete(taskId, uid)) {
            return true;
        }
        if (!completeRecordAndSend) {
            // 未完成，不处理
            int retValue = getTaskValue(taskId, uid);
            if (retValue + addValue < COMPLETE_MAP.get(taskId)) {
                return false;
            }
        }
        // 将增加值写入redis
        int retValue = incTaskValue(taskId, uid, addValue);
        // 发送mq
        sendToMq(taskId, uid, addValue);
        // 再次检查是否完成，如果完成，写入缓存
        return checkComplete(taskId, uid, retValue);
    }

    private boolean checkComplete(int taskId, String uid) {
        String cacheKey = getCacheKey(taskId, uid);
        Boolean result = cacheMap.getData(cacheKey);
        if (result != null) {
            return result;
        }
        int value = getTaskValue(taskId, uid);
        return checkComplete(taskId, uid, value);
    }

    private boolean checkComplete(int taskId, String uid, int value) {
        Integer reqValue = COMPLETE_MAP.get(taskId);
        if (reqValue == null) {
            logger.error("can not find complete map. taskId={} uid={}", taskId, uid);
            return false;
        }
        logger.info("daily task process. {}/{}. taskId={} uid={}", value, reqValue, taskId, uid);
        boolean ret = reqValue <= value;
        if (ret) {
            String cacheKey = getCacheKey(taskId, uid);
            logger.info("daily task complete. taskId={} uid={}", taskId, uid);
            cacheMap.cacheData(cacheKey, true);
        }
        return ret;
    }

    private String getCacheKey(int taskId, String uid) {
        return DateHelper.ARABIAN.formatDateInDay() + "_" + uid + "_" + taskId;
    }

    private void sendToMq(int taskId, String uid, int addNum) {
        DailyTaskMqData taskMqData = new DailyTaskMqData();
        taskMqData.setTask_id(taskId);
        taskMqData.setFinish_date(DateHelper.ARABIAN.formatDateInDay2());
        taskMqData.setUid(uid);
        taskMqData.setNum(addNum);
        sendToMq(taskMqData);
    }

    public void sendToMq(DailyTaskMqData data) {
        try {
            logger.info("send daily task to mq. taskId={} num={} uid={}", data.getTask_id(), data.getNum(), data.getUid());
            rabbitTemplate.convertAndSend("", ROUTE_KEY, JSON.toJSONString(data));
        } catch (Exception e) {
            logger.error("send daily task to mq error. {}", e.getMessage(), e);
        }
    }

    private int getTaskValue(int taskId, String uid) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getRedisKey(dateStr, taskId);
            String valueStr = (String) redisTemplate.opsForHash().get(key, uid);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);
        } catch (Exception e) {
            logger.error("get daily task value error.taskId={} uid={}", taskId, uid);
        }
        return 0;
    }

    private int incTaskValue(int taskId, String uid, int addValue) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getRedisKey(dateStr, taskId);
            int value = redisTemplate.opsForHash().increment(key, uid, addValue).intValue();
            redisTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
            return value;
        } catch (Exception e) {
            logger.error("inc daily task value. taskId={} addValue={} uid={}", taskId, uid, addValue);
        }
        return 0;
    }

    private String getRedisKey(String dateStr, int taskId) {
        return "daily_task_hash_" + dateStr + "_" + taskId;
    }
}
