package com.quhong.mysql.dao;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.HeartRecordEvent;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.data.HeartRecordData;
import com.quhong.mysql.mapper.ustar_log.HeartRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Lazy
@Service
public class HeartRecordDao extends MonthShardingDao<HeartRecordMapper> {
    private static final Logger logger = LoggerFactory.getLogger(HeartRecordDao.class);

    @Resource
    private HeartRecordMapper heartRecordMapper;
    @Resource
    private ActorDao actorDao;
    @Autowired(required = false)
    private EventReport eventReport;

    public HeartRecordDao() {
        super("t_heart_record");
    }

    public void insert(HeartRecordData heartRecordData) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(heartRecordData.getCtime()));
        createTable(suffix);
        heartRecordMapper.insert(suffix, heartRecordData);
    }

    public boolean changeHeart(String uid, int change, String title, String remark) {
        if (change == 0) {
            return false;
        }
        DistributeLock lock = new DistributeLock(getLockKey(uid));
        try {
            lock.lock();
            MongoActorData actor = actorDao.findActorDataFromDB(uid);
            if (null == actor) {
                return false;
            }
            int heart = actor.getHeartGot() + change;
            if (change < 0 && heart < 0) {
                logger.info("changeHeart actor heart not enough, uid={} left={} change={}", uid, actor.getHeartGot(), change);
                return false;
            }
            // 记录心心流水
            insert(new HeartRecordData(uid, change, actor.getHeartGot(), heart, title, remark));
            // 保存心心数, mongodb不支持事务，放到最后处理
            actorDao.updateHeart(uid, heart);
            // 上报数数
            doHeartRecordEvent(uid, change, heart, title, remark);
            return true;
        } catch (Exception e) {
            logger.error("changeHeart error. uid={} change={}", uid, change, e);
            return false;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 用于撤回金币，如果不足时扣到0（上面的不足不会扣）
     * @param uid 用户uid
     * @param change 改变的值
     * @param title 标题
     * @param remark 备注
     * @return boolean
     */
    public boolean changeHeartByUid(String uid, int change, String title, String remark) {
        if (change == 0) {
            return false;
        }
        DistributeLock lock = new DistributeLock(getLockKey(uid));
        try {
            lock.lock();
            MongoActorData actor = actorDao.findActorDataFromDB(uid);
            if (null == actor) {
                return false;
            }
            int originalHeart = actor.getHeartGot();
            int heart = originalHeart + change;
            if (change < 0 && heart < 0) {
                heart = 0;
            }
            // 记录心心流水
            insert(new HeartRecordData(uid, change, originalHeart, heart, title, remark));
            // 保存心心数, mongodb不支持事务，放到最后处理
            actorDao.updateHeart(uid, heart);
            // 上报数数
            doHeartRecordEvent(uid, change, heart, title, remark);
            return true;
        } catch (Exception e) {
            logger.error("changeHeartByUid error. uid={} change={}", uid, change, e);
            return false;
        } finally {
            lock.unlock();
        }
    }

    private String getLockKey(String uid) {
        return "change_heart_" + uid;
    }


    private void doHeartRecordEvent(String uid, int changed, int afterHeart, String title, String remark) {
        HeartRecordEvent event = new HeartRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setHeart_changed(changed);
        event.setHeart_after_change(afterHeart);
        event.setHeart_title(title);
        event.setHeart_remark(remark);
        eventReport.track(new EventDTO(event));
    }
}
