package com.quhong.mysql.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.cache.CacheMap;
import com.quhong.data.condition.GiftCondition;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.mapper.ustar.GiftMapper;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Lazy
public class GiftDao {
    private static final Logger logger = LoggerFactory.getLogger(GiftDao.class);
    private static final String ALL = "ALL";
    private static final String LUCKY_GIFT_KEY = "lucky_gift_key";
    private static final String SIMILAR_GIFT_KEY = "similar_gift_key";

    private final CacheMap<String, List<GiftData>> cacheMap;
    private final CacheMap<String, List<GiftData>> similarGiftMap;
    private final CacheMap<String, Set<Integer>> seCacheMap;
    private final CacheMap<Integer, GiftData> giftCacheMap;
    private static final long CACHE_TIME_MILLIS = 60 * 1000L;

    /**
     * game_play可以随意搭配showDetail值, 特殊玩法对应特殊showDetail
     * 什么时候加字段，什么时候在game_play 加值?
     * 答: 建议给礼物增加属性时, 可以表里加字段; 如果是给礼物加某种游戏玩法则game_play加值
     *    原则是: 一个礼物只有一种礼物玩法, 一个礼物可以有多个属性
     *    给礼物加属性值增加字段不影响给该礼物增加游戏玩法, 如果给礼物增加游戏玩法也加字段会比较混乱, 不方便管理
     */
    // 礼物玩法 -- 对应zipInfo 的 ztype   -- 对应表里的字段game_play方便过滤, 以后可以考虑去掉ztype
    public static final Integer GAME_PLAY_DETAIL_GIFT = 0;     // 默认值
    public static final Integer GAME_PLAY_ANGEL_GIFT = 1;     // 旧版整蛊天使礼物  弃用
    public static final Integer GAME_PLAY_DEMON_GIFT = 2;     // 旧版整蛊恶魔礼物  弃用
    public static final Integer GAME_PLAY_PRANK_GIFT = 3;     // 新版整蛊礼物  --对应客户端tag: 6
    public static final Integer GAME_PLAY_LABEL_GIFT = 4;     // 带礼物介绍的礼物  --对应客户端tag: 不需要
    public static final Integer GAME_PLAY_LUCKY_GIFT = 5;     // 幸运礼物  --对应客户端tag: 5
    public static final Integer GAME_PLAY_CHANGE_VOICE = 6;   // 变声礼物  --对应客户端tag: 9
    public static final Integer GAME_PLAY_RANDOM_GIFT = 7;    // 盲盒礼物  --对应客户端tag: 10
    public static final Integer GAME_PLAY_ADVANCED_GIFT = 8;  // 进阶礼物  --对应客户端tag: 8
    public static final Integer GAME_PLAY_LOCK_GIFT = 9;      // 解锁礼物  --对应客户端tag: 18
    public static final Integer GAME_PLAY_SIMILAR_GIFT = 10;   // 同类礼物  --对应客户端tag: 不需要

    // 客户端礼物面板点击礼物上方展示横幅样式 -- 对应zipInfo 的 showDetail
    public static final String SHOW_DETAIL_KEY = "showDetail";      // showDetail 常量
    public static final Integer SHOW_DETAIL_NOT_GIFT = 0;        // 不展示普通礼物banner
    public static final Integer SHOW_DETAIL_COMMON_GIFT = 1;     // 展示普通礼物banner
    public static final Integer SHOW_DETAIL_RANDOM_GIFT = 2;     // 展示盲盒礼物banner
    public static final Integer SHOW_DETAIL_JACKPOT_GIFT = 3;    // 展示钻石版幸运礼物banner
    public static final Integer SHOW_DETAIL_SIMILAR_GIFT = 4;    // 展示同类礼物banner
    public static final Integer SHOW_DETAIL_LEVEL_BADGE = 5;     // 展示礼物勋章banner

    // 融合动画相关
    public static final Integer FUSION_MODE_0 = 0;      // 无
    public static final Integer FUSION_MODE_3 = 3;      // 指定用户
    public static final Integer FUSION_MODE_4 = 4;      // 指定家族


    @Resource
    private GiftMapper giftMapper;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
//    @Resource(name = DataRedisBean.GIFT)
//    private StringRedisTemplate giftRedis;

    public GiftDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
        this.seCacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
        this.similarGiftMap = new CacheMap<>(CACHE_TIME_MILLIS);
        this.giftCacheMap = new CacheMap<>(30 * 60 * 1000L);
    }

    /**
     * 通过 listType 查询私信发送礼物列表
     */
    public List<GiftData> listMsgGiftByType(int listType) {
        //1、redis缓存查询
        List<GiftData> cacheList = getMsgGiftCache(listType);
        if (cacheList != null) {
            return cacheList;
        }
        //2、db查询
        QueryWrapper<GiftData> query = new QueryWrapper<>();
        if (listType == 1) {
            query.eq("gstatus", 1)
                    .eq("grtype", 0)
                    .in("gatype", Arrays.asList(1, 3, 5, 7, 8))
                    .orderByAsc("gsorder");
        } else if (listType == 3) {
            query.eq("gstatus", 1)
                    .eq("gatype", 6)
                    .eq("grtype", 0)
                    .orderByAsc("gsorder");
        } else {
            query.eq("gstatus", 1)
                    .eq("gatype", 2)
                    .eq("grtype", 0)
                    .orderByAsc("gsorder");
        }
        cacheList = giftMapper.selectList(query);
        //3、存储缓存
        cacheMsgGift(listType, cacheList);
        return cacheList;
    }

    public void clearVipGiftsCache() {
        cacheMap.remove(ALL);
        seCacheMap.remove(ALL);
    }

    public List<GiftData> getVipGifts() {
        List<GiftData> vipGifts = cacheMap.getData(ALL);
        if (vipGifts != null) {
            return vipGifts;
        }
        QueryWrapper<GiftData> query = new QueryWrapper<>();
        query.eq("gtype", 3)
                .eq("status", 1)
                .eq("grtype", 0)
                .orderByAsc("gsorder");
        vipGifts = giftMapper.selectList(query);
        cacheMap.cacheData(ALL, vipGifts);
        return vipGifts;
    }


    public List<GiftData> getSimilarGiftList(int personal) {
        List<GiftData> similarGiftList = similarGiftMap.getData(SIMILAR_GIFT_KEY);
        if (similarGiftList != null) {
            return similarGiftList;
        }
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();

        if(personal > 0){
            queryWrapper.eq("gstatus", 1);
        }else {
            queryWrapper.eq("status", 1);
        }
        queryWrapper.gt("parent_id", 0);
        similarGiftList = giftMapper.selectList(queryWrapper);
        similarGiftMap.cacheData(SIMILAR_GIFT_KEY, similarGiftList);
        return similarGiftList;
    }

    /**
     * 通过缓存查询私信礼物列表
     */
    private List<GiftData> getMsgGiftCache(int listType) {
        String key = getMsgGiftListKey(listType);
        String value = redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return JSON.parseArray(value, GiftData.class);
    }

    /**
     * 私信礼物列表生成缓存
     */
    private void cacheMsgGift(int listType, List<GiftData> list) {
        String key = getMsgGiftListKey(listType);
        String value = JSON.toJSONString(list);
        redisTemplate.opsForValue().set(key, value, 5, TimeUnit.MINUTES);
    }

    private String getMsgGiftListKey(int listType) {
        return "str_msg_gift_list_" + listType;
    }

    public Set<Integer> getSpecialEffectSetFromCache() {
        Set<Integer> set = seCacheMap.getData(ALL);
        if (null != set) {
            return set;
        }
        set = getSpecialEffectSet();
        if (!CollectionUtils.isEmpty(set)) {
            seCacheMap.cacheData(ALL, set);
        }
        return set;
    }

    /**
     * 获取特殊特效礼物id集合
     */
    private Set<Integer> getSpecialEffectSet() {
        try {
            Set<String> members = redisTemplate.opsForSet().members(getSpecialEffectKey());
            if (null == members) {
                return new HashSet<>();
            }
            Set<Integer> result = new HashSet<>(members.size());
            for (String member : members) {
                result.add(Integer.valueOf(member));
            }
            return result;
        } catch (Exception e) {
            logger.error("get special effect set error. {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    private String getSpecialEffectKey() {
        return "set:special_effect_key";
    }

    public GiftData getGiftFromCache(int giftId) {
        if (giftCacheMap.hasData(giftId)) {
            return giftCacheMap.getData(giftId);
        }
        GiftData giftData = getGiftFromDb(giftId);
        giftCacheMap.cacheData(giftId, giftData);
        return giftData;
    }

    public GiftData getGiftFromDb(int giftId) {
        QueryWrapper<GiftData> query = new QueryWrapper<>();
        query.eq("rid", giftId);
        return giftMapper.selectOne(query);
    }

    public List<GiftData> getHotGiftList() {
        String key = "hot_gift";
        if (cacheMap.hasData(key)) {
            return cacheMap.getData(key);
        }
        QueryWrapper<GiftData> query = new QueryWrapper<>();
        query.eq("status", 1)
                .eq("grtype", 0)
                .ne("gtype", 3)
                .eq("gptype", 1);
        List<GiftData> list = giftMapper.selectList(query);
        cacheMap.cacheData(key, list);
        return list;
    }


    // 运营系统相关
    public IPage<GiftData> selectPageList(GiftCondition condition) {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int gtype = condition.getGtype();
        int gptype = condition.getGptype();
        int gatype = condition.getGatype();
        int gplatform = condition.getGplatform();
        int status = condition.getStatus();
        int hot = condition.getHot();
        int parentStatus = condition.getParentStatus();
        int activityStatus = condition.getActivityStatus();
        int bagGift = condition.getBagGift();
        String search = condition.getSearch();

        IPage<GiftData> dataPage = new Page<>(page == 0 ? 1 : page, pageSize == 0 ? 30 : pageSize);

        if(gtype != -1){
            queryWrapper.eq("gtype", gtype);
        }

        if (gptype != -1) {
            if (gptype == 99) {
                queryWrapper.in("gatype", Arrays.asList(1, 3, 5, 7));
            } else {
                queryWrapper.eq("gptype", gptype);
            }
        }

        if (gatype != -1) {
            queryWrapper.eq("gatype", gatype);
        }

        if (gplatform != -1) {
            queryWrapper.eq("gplatform", gplatform);
        }

        if (hot != -1) {
            queryWrapper.eq("hot", hot);
        }

        if (status != -1) {
            if (gptype == 99) {
                queryWrapper.eq("gstatus", status);
            } else {
                queryWrapper.eq("status", status);
            }
        }

        if (!ObjectUtils.isEmpty(search)) {
            queryWrapper.and(wrapper -> wrapper.like("gname", search).or().like("gnamear", search).or().like("rid", search));
        }

        // 同类礼物筛选
        if(parentStatus == 0){
            queryWrapper.eq("parent_id", 0);
        } else if (parentStatus == 1) {
            queryWrapper.gt("parent_id", 0);
        }

        // 活动礼物tag筛选
        if(activityStatus != -1){
            queryWrapper.eq("activity_status", activityStatus);
        }

        if(bagGift != -1){
            queryWrapper.eq("bag_gift", bagGift);
        }

        // 排序规则
        if (gptype == -1) {
            queryWrapper.orderByDesc("rid");
        } else {
            if (gptype == 99) {
                queryWrapper.orderByDesc("gstatus").orderByAsc("gsorder");
            } else {
                queryWrapper.orderByDesc("status").orderByAsc("forder").orderByAsc("rid");
            }
        }
        // 礼物说明配置不为空
        if (condition.getIsGiftDes() != null) {
            if (condition.getIsGiftDes() == 0) {
                queryWrapper.lambda().eq(GiftData::getGprop, 0);
            } else {
                queryWrapper.lambda().ne(GiftData::getGprop, 0);
            }
        }
        return giftMapper.selectPage(dataPage, queryWrapper);
    }

    public GiftData selectOne(int id) {
        return giftMapper.selectById(id);
    }

    public GiftData selectLastIdOne() {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("rid");
        queryWrapper.last("limit 1");
        return giftMapper.selectOne(queryWrapper);
    }

    public List<GiftData> getGiftPersonByParentId(int parentId) {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        return giftMapper.selectList(queryWrapper);
    }

    public GiftData getGiftByParentId(int similarGiftId,int parentId) {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        queryWrapper.ne("rid", similarGiftId);
        queryWrapper.last("limit 1");
        return giftMapper.selectOne(queryWrapper);
    }

    private String getGiftPanelKey() {
        return "str:gift:panel";
    }

    private String getGiftPanelVersionKey() {
        return "str:panel:version";
    }


    public void cacheGiftToRedis(GiftData data) {
        try {
            if (data != null){
                Map<String, String> giftMap = JSONObject.parseObject(JSON.toJSONString(data), new TypeReference<Map<String, String>>() {});
                logger.info("giftMap: {}", giftMap);
            }
            redisTemplate.delete(getMsgGiftListKey(1));
            redisTemplate.delete(getGiftPanelKey());
            redisTemplate.opsForValue().increment(getGiftPanelVersionKey());
        } catch (Exception e) {
            logger.error("cacheGiftToRedis error: {}", e.getMessage(), e);
        }

    }

    public int insertOne(GiftData data) {
        cacheGiftToRedis(data);
        return giftMapper.insert(data);
    }

    public int updateOne(GiftData data) {
        cacheGiftToRedis(data);
        return giftMapper.updateById(data);
    }


    public IPage<GiftData> selectBlindBoxPageList(int status, int page, int pageSize) {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        IPage<GiftData> dataPage = new Page<>(page == 0 ? 1 : page, pageSize == 0 ? 30 : pageSize);
        if (status != -1) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.eq("blind_box", 1);

        // 排序规则
        queryWrapper.orderByDesc("rid");

        return giftMapper.selectPage(dataPage, queryWrapper);
    }


    public Set<Integer> getFusionAnimationGiftSet() {
        String key = "fusionAnimationGift";
        if (seCacheMap.hasData(key)) {
            return seCacheMap.getData(key);
        }
        QueryWrapper<GiftData> query = new QueryWrapper<>();
        query.eq("is_fusion_animation", 1);
        List<GiftData> list = giftMapper.selectList(query);
        Set<Integer> giftIdSet;
        if (CollectionUtils.isEmpty(list)) {
            giftIdSet = Collections.emptySet();
        } else {
            giftIdSet = list.stream().map(GiftData::getRid).collect(Collectors.toSet());
        }
        seCacheMap.cacheData(key, giftIdSet);
        return giftIdSet;
    }

    public void addGiftIdInBlindBox(int giftId) {
        String key = getGiftInBlindBoxKey();
        try {
            redisTemplate.opsForSet().add(key, giftId + "");
            redisTemplate.expire(key, 300, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addGiftIdInBlindBox error. giftId={} {}", giftId, e.getMessage(), e);
        }
    }

    public Set<String> getAllGiftIdInBlindBox() {
        try {
            Set<String> allGift = redisTemplate.opsForSet().members(getGiftInBlindBoxKey());
            if (allGift == null) {
                return Collections.emptySet();
            }
            return allGift;
        } catch (Exception e) {
            logger.error("getAllGiftIdInBlindBox error. {}", e.getMessage(), e);
            return Collections.emptySet();
        }
    }


    public String getGiftInBlindBoxKey() {
        return "set:giftInBlindBox";
    }

    // 幸运礼物玩法相关配置
    public IPage<GiftData> selectLuckGiftPageList(int status, int page, int pageSize) {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        IPage<GiftData> dataPage = new Page<>(page == 0 ? 1 : page, pageSize == 0 ? 30 : pageSize);
        if (status != -1) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.eq("game_play", GAME_PLAY_LUCKY_GIFT);

        // 排序规则
        queryWrapper.orderByDesc("rid");

        return giftMapper.selectPage(dataPage, queryWrapper);
    }

    /**
     * 获取幸运礼物id集合
     */

    public Set<Integer> getLuckyGiftSetFromCache() {
        Set<Integer> set = seCacheMap.getData(LUCKY_GIFT_KEY);
        if (null != set) {
            return set;
        }
        set = getLuckyGiftSet();
        if (!CollectionUtils.isEmpty(set)) {
            seCacheMap.cacheData(LUCKY_GIFT_KEY, set);
        }
        return set;
    }


    private Set<Integer> getLuckyGiftSet() {
        try {
            QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("game_play", GAME_PLAY_LUCKY_GIFT);
            List<GiftData> luckyGiftList = giftMapper.selectList(queryWrapper);
            Set<Integer> result = new HashSet<>();
            for (GiftData giftData : luckyGiftList) {
                result.add(giftData.getRid());
            }
            return result;
        } catch (Exception e) {
            logger.error("getLuckyGiftSet set error. {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取奖池幸运礼物图标列表
     */
    public List<String> selectLuckGiftList() {
        List<String> resultList = new ArrayList<>();
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("game_play", GAME_PLAY_LUCKY_GIFT);
        queryWrapper.orderByAsc("price");
        List<GiftData> giftDataList = giftMapper.selectList(queryWrapper);
        for (GiftData giftData : giftDataList) {
            JSONObject jsonObject = JSON.parseObject(giftData.getZipInfo());
            if (jsonObject.getIntValue("jackpot") == 1) {
                resultList.add(giftData.getGicon());
            }
        }
        return resultList;
    }

    /**
     * 获取所有背包礼物列表
     */
    public List<GiftData> selectBagGiftList() {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bag_gift", 1);
        return giftMapper.selectList(queryWrapper);
    }

    /**
     * 获取所有开启定时上下线的礼物列表
     */
    public List<GiftData> selectScheduledGifts() {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("schedule_time", 1);
        return giftMapper.selectList(queryWrapper);
    }

    public List<GiftData> getGiftPanelData(int showArea, int gpType) {
        QueryWrapper<GiftData> query = new QueryWrapper<>();
        query.eq("status", 1)
                .eq("grtype", 0)
                // vip礼物不展示在普通边板里
                .ne("gtype", 3)
                .eq("gptype", gpType);
        if (showArea == 1 || showArea == 2) {
            query.eq("gstatus", 1);
        }
        return giftMapper.selectList(query);
    }

    /**
     * 批量更新礼物的bagGift状态
     * @param giftIds 礼物ID集合
     * @param bagGiftStatus 要设置的bagGift状态值
     * @return 更新的记录数
     */
    public int updateBagGiftStatus(List<Integer> giftIds, int bagGiftStatus) {
        if (CollectionUtils.isEmpty(giftIds)) {
            return 0;
        }

        UpdateWrapper<GiftData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("rid", giftIds);
        updateWrapper.set("bag_gift", bagGiftStatus);
        cacheGiftToRedis(null);
        return giftMapper.update(null, updateWrapper);
    }

    public GiftData getBagGiftOne(int giftId) {
        QueryWrapper<GiftData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rid", giftId);
        queryWrapper.eq("bag_gift", 1);
        return giftMapper.selectOne(queryWrapper);
    }
}
