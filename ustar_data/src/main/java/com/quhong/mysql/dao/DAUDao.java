package com.quhong.mysql.dao;

import com.quhong.analysis.DAUEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.NewDAUEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.mysql.data.DAUData;
import com.quhong.mysql.mapper.ustar_log.DAUMapper;
import com.quhong.redis.DauUserRedis;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class DAUDao extends MonthShardingDao<DAUMapper> {
    private static final Logger logger = LoggerFactory.getLogger(DAUDao.class);
    public static final Integer DAU_SOURCE_REGISTER = 1;
    public static final Integer DAU_SOURCE_LOGIN = 2;
    public static final Integer DAU_SOURCE_FRONT = 3;   // app前置
    public static final Integer DAU_SOURCE_START = 4;   // app前置

    @Resource
    private DauUserRedis dauUserRedis;
    @Resource
    private EventReport eventReport;

    public DAUDao() {
        super("s_dau");
    }

    public void updateDAU(ActorData actorData, int dauSourceType) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                try {
                    String uid = actorData.getUid();
                    int beans = actorData.getBeans();
                    if (dauUserRedis.hasLog(uid, beans)) {
                        return;
                    }
                    DAUData dauData = new DAUData();
                    dauData.setUserId(actorData.getUid());
                    dauData.setFbGender(actorData.getFb_gender());
                    dauData.setChannel(actorData.getChannel());
                    dauData.setPackageName(actorData.getAppPackageName());
                    dauData.setOs(actorData.getIntOs());
                    dauData.setIsNew(ActorUtils.isNewRegisterActor(actorData.getUid()) ? 1 : 0);
                    dauData.setCtime(DateHelper.getNowSeconds());
                    dauData.setMtime(dauData.getCtime());
                    insert(dauData, actorData, dauSourceType);
                } catch (Exception e) {
                    logger.error("update dau error. uid={} {}", actorData.getUid(), e.getMessage(), e);
                }
            }
        });
    }

    public void updateNewDAUEvent(ActorData actorData, int dauSourceType) {
        try {
            if (actorData == null) {
                return;
            }
            String uid = actorData.getUid();
            if (dauUserRedis.hasNewLog(uid)) {
                return;
            }
            int isNew = ActorUtils.isNewRegisterActor(actorData.getUid()) ? 1 : 0;
            eventReport.track(new EventDTO(new NewDAUEvent(uid, actorData.getVersion_code(),
                    actorData.getVersion_name(), isNew, dauSourceType, actorData.getTn_id())));
        } catch (Exception e) {
            logger.error("update new dau error. uid={} {}", actorData.getUid(), e.getMessage(), e);
        }
    }


    private void insert(DAUData dauData, ActorData actorData, int dauSourceType) {
        try {
            String tableSuffix = DateHelper.DEFAULT.getTableSuffix(DateHelper.formatDate(dauData.getCtime()));
            createTable(tableSuffix);
            tableMapper.insert(tableSuffix, dauData);
            eventReport.track(new EventDTO(new DAUEvent(dauData.getUserId(), actorData.getVersion_code(), dauData.getIsNew(), actorData.getVersion_name(), dauSourceType)));
        } catch (Exception e) {
            logger.error("insert dau data error. {}", e.getMessage(), e);
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Set<String> getActiveUserSetCache(int days) {
        return getActiveUserSet(days);
    }


    public Set<String> getActiveUserSet(int days) {
        int nowTime = DateHelper.getNowSeconds();
        List<String> strDateList = DateSupport.ARABIAN.getDaysBetween(nowTime - (int) TimeUnit.DAYS.toSeconds(days), nowTime);
        Set<String> activeUserSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(strDateList)) {
            for (String strDate : strDateList) {
                Set<String> dauUserSet = dauUserRedis.getAllDauUserByDay(strDate);
                if (!CollectionUtils.isEmpty(dauUserSet)) {
                    activeUserSet.addAll(dauUserSet);
                }
            }
        }
        logger.info("getActiveUserSet. days={} size={}", days, activeUserSet.size());
        return activeUserSet;
    }




    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Set<String> getActiveUserSet(int days, int limit) {
        int nowTime = DateHelper.getNowSeconds();
        List<String> strDateList = DateSupport.ARABIAN.getDaysBetween(nowTime - (int) TimeUnit.DAYS.toSeconds(days), nowTime);
        Collections.shuffle(strDateList);
        Set<String> activeUserSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(strDateList)) {
            for (String strDate : strDateList) {
                Set<String> dauUserSet = dauUserRedis.getAllDauUserByDay(strDate);
                if (!CollectionUtils.isEmpty(dauUserSet)) {
                    for (String aid : dauUserSet) {
                        if (limit > 0 && activeUserSet.size() >= limit) {
                            logger.info("getActiveUserSet. days={} size={}", days, activeUserSet.size());
                            return activeUserSet;
                        }
                        activeUserSet.add(aid);
                    }
                }
            }
        }
        logger.info("getActiveUserSet. days={} size={}", days, activeUserSet.size());
        return activeUserSet;
    }

    public Set<String> getActiveUserSetNoCache(int days, int limit) {
        int nowTime = DateHelper.getNowSeconds();
        List<String> strDateList = DateSupport.ARABIAN.getDaysBetween(nowTime - (int) TimeUnit.DAYS.toSeconds(days), nowTime);
        Collections.shuffle(strDateList);
        Set<String> activeUserSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(strDateList)) {
            for (String strDate : strDateList) {
                Set<String> dauUserSet = dauUserRedis.getAllDauUserByDay(strDate);
                if (!CollectionUtils.isEmpty(dauUserSet)) {
                    List<String> dauUserList = new ArrayList<>(dauUserSet);
                    Collections.shuffle(dauUserList);
                    for (String aid : dauUserList) {
                        if (limit > 0 && activeUserSet.size() >= limit) {
                            logger.info("getActiveUserSetNoCache. days={} size={}", days, activeUserSet.size());
                            return activeUserSet;
                        }
                        activeUserSet.add(aid);
                    }
                }
            }
        }
        return activeUserSet;
    }

    public int getLast7SdauCount(String uid) {
        try {
            int end = DateHelper.getNowSeconds();
            int start = end - 7 * 86400;
            List<String> suffixList = DateHelper.ARABIAN.getTableSuffixList(start, end);
            List<String> retList = getExistTableSuffixList(suffixList);
            if (retList.isEmpty()) {
                logger.error("getExistTableSuffixList start={}", start);
                return 0;
            }
            Integer ret = tableMapper.getSdauCount(retList, uid, start);
            return ret != null ? ret : 0;
        } catch (Exception e) {
            logger.error("getSdauCount error ", e);
            return 0;
        }
    }

}
