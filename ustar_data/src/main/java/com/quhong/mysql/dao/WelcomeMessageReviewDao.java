package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.constant.WelcomeMessageReviewConstant;
import com.quhong.mysql.data.WelcomeMessageReviewData;
import com.quhong.mysql.mapper.ustar.WelcomeMessageReviewMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 房间欢迎消息dao
 *
 * <AUTHOR>
 * @date 2025/8/25 15:33
 */
@Lazy
@Component
public class WelcomeMessageReviewDao {

    private static final Logger logger = LoggerFactory.getLogger(WelcomeMessageReviewDao.class);

    public static final String TABLE_PRE = WelcomeMessageReviewConstant.TABLE_PREFIX;

    @Resource
    private WelcomeMessageReviewMapper mapper;

    /**
     * 分页获取欢迎消息审核数据
     */
    public List<WelcomeMessageReviewData> selectPageList(int page, int pageSize, WelcomeMessageReviewData condition) {
        // 如果指定了roomId，则查询对应的分表
        String tableName = getTableName(condition.getRoomId());
        int offset = (page - 1) * pageSize;
        List<WelcomeMessageReviewData> records = mapper.selectPageList(tableName, condition, offset, pageSize);
        return records;
    }

    /**
     *
     * @param condition
     * @return
     */
    public long selectCount(WelcomeMessageReviewData condition) {
        String tableName = getTableName(condition.getRoomId());
        return mapper.selectCount(tableName, condition);
    }


    /**
     *
     * @param roomId
     * @param reviewActionList
     * @param page
     * @param pageSize
     * @return
     */
    public List<WelcomeMessageReviewData> selectPageListByStatus(String roomId , List<Integer> reviewActionList, int page, int pageSize) {
        // 如果指定了roomId，则查询对应的分表
        String tableName = getTableName(roomId);
        int offset = (page - 1) * pageSize;
        return mapper.selectPageListByStatus(tableName, roomId, reviewActionList, offset, pageSize);
    }

    public long selectActionListCount(String roomId , List<Integer> reviewActionList) {
        String tableName = getTableName(roomId);
        return mapper.selectActionListCount(tableName, reviewActionList);
    }

    /**
     * 插入欢迎消息审核数据
     *
     * @param data 欢迎消息审核信息
     * @return 返回插入操作影响的行数
     */
    public int insert(WelcomeMessageReviewData data) {
        String tableName = getTableName(data);
        return mapper.insert(tableName, data);
    }


    public void batchInsert(String roomId, List<WelcomeMessageReviewData> insertList) {
        String tableName = getTableName(roomId);
        mapper.batchInsert(tableName, insertList);
    }

    /**
     * 删除欢迎消息
     *
     * @return 影响行数
     */
    public int delete(String roomId, Integer id) {
        String tableName = getTableName(roomId);
        return mapper.delete(tableName, roomId, id);
    }

    /**
     * 更新欢迎消息,同时把审核状态更新为待审核
     *
     * @return 影响行数
     */
    public int updateContent(String roomId, Integer id, String messageContent) {
        String tableName = getTableName(roomId);
        int updateTime =  (int) (System.currentTimeMillis() / 1000);;
        return mapper.updateContent(tableName, roomId, id, messageContent, updateTime);
    }

    /**
     * 更新欢迎消息审核状态
     *
     * @return 影响行数
     */
    public int updateReviewAction(String roomId, Integer id, String operatorUid, Integer reviewAction, String rejectReason) {
        String tableName = getTableName(roomId);
        int updateTime =  (int) (System.currentTimeMillis() / 1000);;
        return mapper.updateReviewAction(tableName, roomId, id, operatorUid, reviewAction, rejectReason, updateTime);
    }

    /**
     * 根据ID查询单条记录
     */
    public WelcomeMessageReviewData selectById(String roomId, Integer id) {
        String tableName = getTableName(roomId);
        return mapper.selectById(tableName, roomId, id);
    }





    private String getTableName(WelcomeMessageReviewData data) {
        return getTableName(data.getRoomId());
    }

    /**
     * 获取分表名
     */
    private String getTableName(String roomId) {
        if (roomId == null || roomId.isEmpty()) {
            throw new IllegalArgumentException("roomId不能为空");
        }
        // 按roomId最后一位分16个表
        char lastChar = roomId.charAt(roomId.length() - 1);
        return TABLE_PRE + "_" + lastChar;
    }
}
