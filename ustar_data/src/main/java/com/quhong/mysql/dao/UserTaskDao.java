package com.quhong.mysql.dao;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.UserTaskData;
import com.quhong.mysql.mapper.ustar_log.UserTaskMapper;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Component
public class UserTaskDao extends MonthShardingDao<UserTaskMapper> {

    private static final Logger logger = LoggerFactory.getLogger(UserTaskDao.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String expireDate;

    private static final List<String> TASK_KEY_LIST = Arrays.asList("on_mic_time_1", "on_mic_time_2", "on_mic_time_3", "on_mic_time_4",
            "daily_login", "send_room_msg_2", "send_room_gift_2", "invite_new_users_on_mic", "post_moment", "like_or_comment_moment_2",
            "send_moment_gift", "add_new_friend", "watch_video_time", "play_wheel", "gift_cost_diamond_1", "gift_cost_diamond_2",
            "gift_cost_diamond_3", "gift_cost_diamond_4", "play_fruit_machine", "play_ludo", "play_umo", "play_monster_crush", "win_finger_guess",
            "win_ludo", "win_umo", "win_monster_crush", "get_new_room_member", "daily_recharge",
            "web_daily_on_mic_time_10_minute", "web_greeting_become_friend", "web_daily_send_room_gift", "web_daily_invite_new_users_on_mic",
            "web_daily_post_moment", "web_daily_like_or_comment_moment", "web_daily_add_new_friend", "web_daily_play_ludo", "web_daily_play_umo", "web_daily_play_monster_crush", "web_daily_play_carrom_pool", "web_daily_play_jackaroo",
            "web_daily_play_baloot", "web_daily_play_domino", "web_daily_follow_room"
            );

    public UserTaskDao() {
        super("t_user_task");
    }

    public List<UserTaskData> getTaskListByUid(String uid, int taskDate) {
        List<Object> hashKeys = getHashKeysByUid(uid);
        List<UserTaskData> list = new ArrayList<>();
        try {
            List<Object> objects = clusterTemplate.opsForHash().multiGet(getUserTaskKey(taskDate + ""), hashKeys);
            if (!CollectionUtils.isEmpty(objects)) {
                for (Object object : objects) {
                    if (object == null) {
                        continue;
                    }
                    list.add(JSONObject.parseObject((String) object, UserTaskData.class));
                }
            }
        } catch (Exception e) {
            logger.error("getTaskListByUid error. uid={} taskDate={}", uid, taskDate, e);
        }
        return list;
    }

    @Cacheable(value = "userTaskCache", key = "#p0 + #p1 + #p2", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public UserTaskData getTaskByTaskKey(String uid, int taskDate, String taskKey) {
        try {
            String strValue = (String) clusterTemplate.opsForHash().get(getUserTaskKey(taskDate + ""), getHashKey(uid, taskKey));
            if (StringUtils.hasLength(strValue)) {
                return JSONObject.parseObject(strValue, UserTaskData.class);
            }
        } catch (Exception e) {
            logger.error("getTaskById error. uid={} taskDate={} taskKey={}", uid, taskDate, taskKey, e);
        }
        return null;
    }

    @CacheEvict(value = "userTaskCache", key = "#p0.uid + #p0.taskDate + #p0.taskKey", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public void insert(UserTaskData data) {
        saveUserTask(data);
    }

    @CacheEvict(value = "userTaskCache", key = "#p0.uid + #p0.taskDate + #p0.taskKey", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public void update(UserTaskData data) {
        saveUserTask(data);
    }

    public List<UserTaskData> getTaskListByTaskDate(int taskDate) {
        String key = getUserTaskKey(taskDate + "");
        List<UserTaskData> list = new ArrayList<>();
        try {
            List<Object> objects = clusterTemplate.opsForHash().values(key);
            if (!CollectionUtils.isEmpty(objects)) {
                for (Object object : objects) {
                    list.add(JSONObject.parseObject((String) object, UserTaskData.class));
                }
            }
        } catch (Exception e) {
            logger.error("getTaskListByTaskDate error. taskDate={} {}", taskDate, e.getMessage(), e);
        }
        return list;
    }

    public void saveUserTask(UserTaskData data) {
        String dateStr = data.getTaskDate() + "";
        String key = getUserTaskKey(dateStr);
        try {
            clusterTemplate.opsForHash().put(key, getHashKey(data.getUid(), data.getTaskKey()) , JSONObject.toJSONString(data));
            if (!dateStr.equals(expireDate)) {
                clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
                expireDate = dateStr;
            }
        } catch (Exception e) {
            logger.error("saveUserTask error. uid={} taskDate={} taskKey={} {}", data.getUid(), data.getTaskDate(), data.getTaskKey(), e.getMessage(), e);
        }
    }

    private List<Object> getHashKeysByUid(String uid) {
        List<Object> hashKeys = new ArrayList<>();
        TASK_KEY_LIST.forEach(k -> hashKeys.add(getHashKey(uid, k)));
        return hashKeys;
    }

    private String getHashKey(String uid, String taskKey) {
        return uid + "_" + taskKey;
    }

    private String getUserTaskKey(String strDate) {
        return "hash:userTask:" + strDate;
    }
}
