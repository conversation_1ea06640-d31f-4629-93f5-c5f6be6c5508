package com.quhong.mysql.mapper.ustar_log;

import com.quhong.data.UserTaskData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
public interface UserTaskMapper extends ShardingMapper {

    @Select("SELECT * FROM t_user_task_${tableSuffix} where uid=#{uid} and task_date=#{taskDate}")
    List<UserTaskData> getTaskListByUid(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("taskDate") Integer taskDate);

    @Select("<script>" +
            "SELECT * FROM t_user_task_${tableSuffix} where uid=#{uid} and task_date=#{taskDate} and task_key in " +
            "<foreach collection = 'taskKeyList' open = '(' item = 'taskKey' separator = ',' close = ')'>" +
            "#{taskKey}" +
            "</foreach>" +
            "</script>")
    List<UserTaskData> getTaskByTaskKeys(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("taskDate") Integer taskDate, @Param("taskKeyList") List<String> taskKeyList);

    @Select("<script>" +
            "SELECT * FROM t_user_task_${tableSuffix} where uid=#{uid} and task_date=#{taskDate} and task_key=#{taskKey} " +
            "</script>")
    UserTaskData getTaskByTaskKey(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("taskDate") Integer taskDate, @Param("taskKey") String taskKey);

    @Insert("INSERT INTO t_user_task_${tableSuffix} (uid, task_date, task_key, task_num, task_value, status, ctime, mtime) VALUES (#{item.uid}, #{item.taskDate}, #{item.taskKey}, #{item.taskNum}, #{item.taskValue}, #{item.status}, #{item.ctime}, #{item.mtime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") UserTaskData data);

    @Update("UPDATE t_user_task_${tableSuffix} SET uid = #{item.uid}, task_date = #{item.taskDate}, task_key = #{item.taskKey}, task_num = #{item.taskNum}, task_value = #{item.taskValue}, status = #{item.status}, mtime = #{item.mtime} WHERE id = #{item.id}")
    void update(@Param("tableSuffix") String tableSuffix, @Param("item") UserTaskData data);

    @Select("SELECT count(*) FROM t_user_task_${tableSuffix} where uid=#{uid} and task_date=#{taskDate} and status=1")
    int selectHasRewardCount(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("taskDate") Integer taskDate);

    @Select("SELECT * FROM t_user_task_${tableSuffix} where task_date=#{taskDate}")
    List<UserTaskData> getTaskListByTaskDate(@Param("tableSuffix") String tableSuffix, @Param("taskDate") Integer taskDate);
}
