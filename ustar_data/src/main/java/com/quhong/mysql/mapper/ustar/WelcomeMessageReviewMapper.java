package com.quhong.mysql.mapper.ustar;

import com.quhong.mysql.data.WelcomeMessageReviewData;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 房间欢迎消息审核mapper
 * <AUTHOR>
 * @date 2025/8/25 15:35
 */
public interface WelcomeMessageReviewMapper {

    /**
     * 插入欢迎消息审核数据
     */
    @Insert("INSERT INTO ${tableName} (submitter_uid, room_id, message_content, review_action, reject_reason, operator_uid, submit_time, update_time) " +
            "VALUES (#{data.submitterUid}, #{data.roomId}, #{data.messageContent}, #{data.reviewAction}, #{data.rejectReason}, #{data.operatorUid}, #{data.submitTime}, #{data.updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "data.id")
    int insert(@Param("tableName") String tableName, @Param("data") WelcomeMessageReviewData data);

    /**
     * 批量插入欢迎消息审核数据
     */
    @Insert({
            "<script>",
            "INSERT INTO ${tableName} (submitter_uid, room_id, message_content, review_action, reject_reason, operator_uid, submit_time, update_time) VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.submitterUid}, #{item.roomId}, #{item.messageContent}, #{item.reviewAction}, #{item.rejectReason}, #{item.operatorUid}, #{item.submitTime}, #{item.updateTime})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(@Param("tableName") String tableName, @Param("list") List<WelcomeMessageReviewData> list);

    /**
     * 更新审核状态
     */
    @Update("UPDATE ${tableName} SET review_action = #{reviewAction}, reject_reason = #{rejectReason}, operator_uid = #{operatorUid}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateReviewAction(@Param("tableName") String tableName, @Param("roomId") String roomId, @Param("id") Integer id,
                          @Param("operatorUid") String operatorUid, @Param("reviewAction") Integer reviewAction,
                          @Param("rejectReason") String rejectReason, @Param("updateTime") Integer updateTime);

    /**
     * 更新消息内容
     */
    @Update("UPDATE ${tableName} SET message_content = #{messageContent}, review_action = 1, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateContent(@Param("tableName") String tableName, @Param("roomId") String roomId, @Param("id") Integer id,
                     @Param("messageContent") String messageContent, @Param("updateTime") Integer updateTime);

    /**
     * 删除欢迎消息
     */
    @Delete("DELETE FROM ${tableName} WHERE id = #{id}")
    int delete(@Param("tableName") String tableName, @Param("roomId") String roomId, @Param("id") Integer id);

    /**
     * 分页查询欢迎消息审核数据
     */
    @Select({
            "<script>",
            "SELECT id, submitter_uid, room_id, message_content, review_action, reject_reason, operator_uid, submit_time, update_time ",
            "FROM ${tableName} ",
            "WHERE 1=1 ",
            "<if test='condition.roomId != null and condition.roomId != \"\"'>",
            "AND room_id = #{condition.roomId} ",
            "</if>",
            "<if test='condition.reviewAction != null'>",
            "AND review_action = #{condition.reviewAction} ",
            "</if>",
            "<if test='condition.operatorUid != null and condition.operatorUid != \"\"'>",
            "AND operator_uid = #{condition.operatorUid} ",
            "</if>",
            "ORDER BY submit_time DESC ",
            "LIMIT #{offset}, #{pageSize}",
            "</script>"
    })
    List<WelcomeMessageReviewData> selectPageList(@Param("tableName") String tableName,
                                                 @Param("condition") WelcomeMessageReviewData condition,
                                                 @Param("offset") int offset,
                                                 @Param("pageSize") int pageSize);

    /**
     * 查询总数
     */
    @Select({
            "<script>",
            "SELECT COUNT(*) FROM ${tableName} ",
            "WHERE 1=1 ",
            "<if test='condition.roomId != null and condition.roomId != \"\"'>",
            "AND room_id = #{condition.roomId} ",
            "</if>",
            "<if test='condition.reviewAction != null'>",
            "AND review_action = #{condition.reviewAction} ",
            "</if>",
            "<if test='condition.operatorUid != null and condition.operatorUid != \"\"'>",
            "AND operator_uid = #{condition.operatorUid} ",
            "</if>",
            "</script>"
    })
    long selectCount(@Param("tableName") String tableName, @Param("condition") WelcomeMessageReviewData condition);

    /**
     * 根据ID查询单条记录
     */
    @Select("SELECT id, submitter_uid, room_id, message_content, review_action, reject_reason, operator_uid, submit_time, update_time " +
            "FROM ${tableName} WHERE id = #{id}")
    WelcomeMessageReviewData selectById(@Param("tableName") String tableName, @Param("roomId") String roomId, @Param("id") Integer id);
}
