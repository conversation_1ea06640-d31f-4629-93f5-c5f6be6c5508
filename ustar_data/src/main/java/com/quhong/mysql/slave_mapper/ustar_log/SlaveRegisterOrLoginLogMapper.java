package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.mysql.data.RegisterOrLoginLogData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface SlaveRegisterOrLoginLogMapper extends ShardingMapper {

    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT(tn_id)) from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT tn_id from t_register_login_log_${tableSuffix} ",
            "WHERE third_uid=#{thirdUid} AND <![CDATA[ ctime >= #{start} AND ctime < #{end} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Integer deviceCountByThirdUid(@Param("suffixList") List<String> suffixList, @Param("thirdUid") String thirdUid, @Param("start") Integer startTime, @Param("end") Integer endTime);


    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT(tn_id)) from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT tn_id from t_register_login_log_${tableSuffix} ",
            "WHERE ip=#{ip} AND <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Integer deviceCountByIp(@Param("suffixList") List<String> suffixList, @Param("ip") String ip, @Param("startTime") Integer startTime, @Param("endTime") Integer endTime);

    @Select({
            "<script>",
            "SELECT * from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid,tn_id from t_register_login_log_${tableSuffix} ",
            "WHERE req_id=#{reqId} ",
            "</foreach>",
            " ) as stat ",
            " limit 1",
            "</script>"
    })
    RegisterOrLoginLogData findOneByReqId(@Param("suffixList") List<String> suffixList, @Param("reqId") String reqId);



    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT(tn_id)) from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT tn_id from t_register_login_log_${tableSuffix} ",
            "WHERE uid=#{uid} AND <![CDATA[ ctime >= #{start} AND ctime < #{end} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Integer deviceCountByUid(@Param("suffixList") List<String> suffixList, @Param("uid") String uid, @Param("start") Integer startTime, @Param("end") Integer endTime);

}
