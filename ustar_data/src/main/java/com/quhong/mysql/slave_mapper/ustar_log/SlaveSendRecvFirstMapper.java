package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.data.SendRecvFirstJoinData;
import com.quhong.mysql.data.SendRecvFirstData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 *
 */
public interface SlaveSendRecvFirstMapper {

    @Insert("insert into ${tableName} (send_uid,receive_uid,gift_id,gift_num,mtime) values (#{item.sendUid},#{item.receiveUid},#{item.giftId},#{item.giftNum},#{item.mtime}) on duplicate key update gift_num = gift_num + #{item.giftNum}, mtime = #{item.mtime}")
    void insert(@Param("tableName") String tableName, @Param("item") SendRecvFirstData data);

    @Update("update ${tableName} set gift_num = gift_num + #{item.giftNum}, mtime = #{item.mtime} where send_uid = #{item.sendUid} and receive_uid = #{item.receiveUid} and gift_id = #{item.giftId}")
    int incGiftNum(@Param("tableName") String tableName, @Param("item") SendRecvFirstData data);


    @Select({
            "<script>",
            "select id,send_uid,receive_uid,gift_id,gift_num,mtime from ${tableName} where send_uid=#{sendUid} and receive_uid in ",
            "<foreach item='item' collection='uidList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<SendRecvFirstData> selectBySendUid(@Param("tableName") String tableName, @Param("sendUid") String sendUid, @Param("uidList") List<String> uidList);

    @Insert({
            "<script>",
            "<foreach collection='list' item='item' index='index' separator=';'>",
            "insert into ${tableName} (send_uid,receive_uid,gift_id,gift_num,mtime) values (#{item.sendUid},#{item.receiveUid},#{item.giftId},#{item.giftNum},#{item.mtime}) ",
            "on duplicate key update gift_num = gift_num + #{item.giftNum}, mtime = #{item.mtime}",
            "</foreach>",
            "</script>"
    })
    void batchInsert(@Param("tableName") String tableName, @Param("list") List<SendRecvFirstData> insertList);

    @Update({
            "<script>",
            "<foreach collection='list' item='item' index='index' separator=';'>",
            "update ${tableName} set gift_num = gift_num + #{item.giftNum}, mtime = #{item.mtime} where send_uid = #{item.sendUid} and receive_uid = #{item.receiveUid} and gift_id = #{item.giftId}",
            "</foreach>",
            "</script>"
    })
    void batchIncGiftNum(@Param("tableName") String tableName, @Param("list") List<SendRecvFirstData> updateList);


    @Select("SELECT b.send_uid,b.gift_id,b.gift_num FROM ${tableName} AS b RIGHT JOIN " +
            "(SELECT receive_uid,gift_id,max(gift_num) AS gift_num FROM ${tableName} where receive_uid=#{receive_uid} GROUP BY gift_id ) a" +
            " ON a.gift_id = b.gift_id AND a.gift_num = b.gift_num  AND a.receive_uid = b.receive_uid  GROUP BY b.gift_id,b.gift_num")
    List<SendRecvFirstJoinData> getFirstJoinData(@Param("tableName") String tableName, @Param("receive_uid") String receiveUid);


    @Select("select gift_id, gift_num from ${tableName} where receive_uid=#{receive_uid} and send_uid=#{send_uid}")
    List<SendRecvFirstData> getGidNumList(@Param("tableName") String tableName, @Param("receive_uid") String receiveUid, @Param("send_uid") String sendUid);
}
