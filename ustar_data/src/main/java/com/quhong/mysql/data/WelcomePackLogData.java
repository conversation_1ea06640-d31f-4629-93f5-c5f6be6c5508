package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.dao.WelcomePackLogDao;

/**
 * 礼包投递记录
 *
 * <AUTHOR>
 * @date 2025/9/4 15:49
 */

@TableName("t_welcome_pack_log")
public class WelcomePackLogData {

    @TableId(type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 用户ID
     */
    private String uid;
    
    /**
     * 礼包ID
     */
    private Integer packId;

    /**
     * 投递官方消息id
     */
    private String officialMsgId;

    /**
     * 礼包名称
     */
    private String packName;
    
    /**
     * 发送人
     */
    private String sendName;
    
    /**
     * 撤销人
     */
    private String revertName;
    
    /**
     * 发送状态 1已发送 2已撤回
     */
    private Integer status;
    
    /**
     * 递送时间
     */
    private Integer ctime;

    /**
     * 撤回时间（根据发送状态判断，如果是发送则是发送时间）
     */
    private Integer utime;
    public WelcomePackLogData() {
    }

    public WelcomePackLogData(String uid, Integer packId, String packName, String sendName) {
        this.uid = uid;
        this.packId = packId;
        this.packName = packName;
        this.sendName = sendName;
        this.revertName = "";
        this.status = WelcomePackLogDao.SEND_STATUS;
        this.ctime = DateHelper.getNowSeconds();
        this.utime = DateHelper.getNowSeconds();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getPackId() {
        return packId;
    }

    public void setPackId(Integer packId) {
        this.packId = packId;
    }

    public String getOfficialMsgId() {
        return officialMsgId;
    }

    public void setOfficialMsgId(String officialMsgId) {
        this.officialMsgId = officialMsgId;
    }

    public String getPackName() {
        return packName;
    }

    public void setPackName(String packName) {
        this.packName = packName;
    }

    public String getSendName() {
        return sendName;
    }

    public void setSendName(String sendName) {
        this.sendName = sendName;
    }

    public String getRevertName() {
        return revertName;
    }

    public void setRevertName(String revertName) {
        this.revertName = revertName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }
}
