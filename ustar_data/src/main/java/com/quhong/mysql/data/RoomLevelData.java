package com.quhong.mysql.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_room_level")
public class RoomLevelData {
    @TableId(type = IdType.AUTO)
    private Integer rid;
    private String roomId;
    private Long exp;
    private Integer level;
    private Integer ctime;
    private Integer mtime;
    @TableField(exist = false)
    private Integer createStatus; // 非数据库字段，是否第一次创建记录 1是 ，其他不是

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Long getExp() {
        return exp;
    }

    public void setExp(Long exp) {
        this.exp = exp;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCreateStatus() {
        return createStatus;
    }

    public void setCreateStatus(Integer createStatus) {
        this.createStatus = createStatus;
    }

    @Override
    public String toString() {
        return "RoomLevelData{" +
                "rid=" + rid +
                ", roomId='" + roomId + '\'' +
                ", exp=" + exp +
                ", level=" + level +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
