package com.quhong.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * caffeine缓存配置
 */
@EnableCaching(proxyTargetClass = true)
@Configuration
public class CaffeineCacheConfig {
    public static final String EXPIRE_5S_AFTER_WRITE = "expire5sAfterWrite";
    public static final String EXPIRE_15S_AFTER_WRITE = "expire15sAfterWrite";
    public static final String EXPIRE_10M_AFTER_WRITE = "expire10mAfterWrite";
    public static final String EXPIRE_30M_AFTER_WRITE = "expire30mAfterWrite";
    public static final String EXPIRE_1M_AFTER_WRITE = "expire1mAfterWrite";

    @Primary
    @Bean(EXPIRE_15S_AFTER_WRITE)
    public CacheManager expire15sAfterWrite() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(15, TimeUnit.SECONDS)
                .initialCapacity(100)
                .maximumSize(1000));
        return cacheManager;
    }

    @Lazy
    @Bean(EXPIRE_5S_AFTER_WRITE)
    public CacheManager expire5sAfterWrite() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(5, TimeUnit.SECONDS)
                .initialCapacity(100)
                .maximumSize(1000));
        return cacheManager;
    }

    @Lazy
    @Bean(EXPIRE_1M_AFTER_WRITE)
    public CacheManager expire1mAfterWrite() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .initialCapacity(100)
                .maximumSize(1000));
        return cacheManager;
    }

    @Lazy
    @Bean(EXPIRE_10M_AFTER_WRITE)
    public CacheManager expire10mAfterWrite() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .initialCapacity(100)
                .maximumSize(10000));
        return cacheManager;
    }

    @Lazy
    @Bean(EXPIRE_30M_AFTER_WRITE)
    public CacheManager expire30mAfterWrite() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .initialCapacity(100)
                .maximumSize(10000));
        return cacheManager;
    }

    /**
     * 默认20分钟过期，访问后刷新时间
     */
    @Lazy
    @Bean
    public Cache<String, Object> caffeineCache() {
        return Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterAccess(20, TimeUnit.MINUTES)
                // 初始的缓存空间大小
                .initialCapacity(100)
                // 缓存的最大条数
                .maximumSize(10000)
                .build();
    }
}
