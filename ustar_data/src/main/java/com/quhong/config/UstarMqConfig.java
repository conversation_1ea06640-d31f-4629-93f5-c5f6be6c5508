package com.quhong.config;

import com.quhong.mq.config.MQConfiguration;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UstarMqConfig extends MQConfiguration {
    public static final String MSG_QUEUE_NAME = "ustar_java_queue";
    public static final String MSG_EXCHANGE_NAME = "ustar_java_queue";
    public static final String MSG_ROUTE_KEY = "ustar.*";

    @Bean
    public RabbitTemplate getDailyTaskTemplate() {
        RabbitTemplate template = new RabbitTemplate(connectionFactory());
        template.setExchange(MSG_EXCHANGE_NAME);
        //The routing key is set to the name of the queue by the broker for the default exchange.
        template.setRoutingKey(MSG_ROUTE_KEY);
        //Where we will synchronously receive messages from
        template.setDefaultReceiveQueue(MSG_QUEUE_NAME);
        return template;
    }

//    	@Bean
//	// Every queue is bound to the default direct exchange
//	public Queue getRecordMsgQueue() {
//		return new Queue(MSG_QUEUE_NAME, true);
//	}
//
//	@Bean
//	public Binding binding() {
//		return BindingBuilder.bind(getRecordMsgQueue()).to(msgExchange()).with(MSG_ROUTE_KEY);
//	}
//
//	@Bean
//	public TopicExchange msgExchange() {
//		return new TopicExchange(MSG_EXCHANGE_NAME);
//	}
}
