package com.quhong.config;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.LuckyGiftLotteryData;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@Lazy
@PropertySource(value = "classpath:gift.yml", encoding = "UTF-8", factory = YamlCorePropertySourceFactory.class)
@ConfigurationProperties(prefix = "gift")
public class GiftYamlConfig {

    private Map<Integer, List<Integer>> giftBadgeMapProd; // 接收某礼物达到一定数量时下发勋章，[礼物id，礼物数]
    private Map<Integer, List<Integer>> giftBadgeIdMapProd; // 接收某礼物达到一定数量时下发勋章，[礼物id，勋章id]
    private Map<Integer, List<Integer>> giftBadgeMapTest; // 接收某礼物达到一定数量时下发勋章，[礼物id，礼物数]
    private Map<Integer, List<Integer>> giftBadgeIdMapTest; // 接收某礼物达到一定数量时下发勋章，[礼物id，勋章id]

    private Map<Integer, List<Integer>> giftBadgeSendMap; // 发送某礼物达到一定数量时下发勋章，[礼物id，礼物数]
    private Map<Integer, List<Integer>> giftBadgeIdSendMap; // 发送某礼物达到一定数量时下发勋章，[礼物id，勋章id]
    private Map<Integer, List<Integer>> conquerNumLevelMapProd;
    private Map<Integer, List<Integer>> conquerNumLevelMapTest;
    private Map<Integer, List<LuckyGiftLotteryData>> luckyGiftConfigMapProd;
    private Map<Integer, List<LuckyGiftLotteryData>> luckyGiftConfigMapTest;
    private List<Integer> conquerNumListProd;
    private List<Integer> conquerNumListTest;


    public Map<Integer, List<Integer>> getGiftBadgeMapProd() {
        return giftBadgeMapProd;
    }

    public void setGiftBadgeMapProd(Map<Integer, List<Integer>> giftBadgeMapProd) {
        this.giftBadgeMapProd = giftBadgeMapProd;
    }

    public Map<Integer, List<Integer>> getGiftBadgeIdMapProd() {
        return giftBadgeIdMapProd;
    }

    public void setGiftBadgeIdMapProd(Map<Integer, List<Integer>> giftBadgeIdMapProd) {
        this.giftBadgeIdMapProd = giftBadgeIdMapProd;
    }

    public Map<Integer, List<Integer>> getGiftBadgeMapTest() {
        return giftBadgeMapTest;
    }

    public void setGiftBadgeMapTest(Map<Integer, List<Integer>> giftBadgeMapTest) {
        this.giftBadgeMapTest = giftBadgeMapTest;
    }

    public Map<Integer, List<Integer>> getGiftBadgeIdMapTest() {
        return giftBadgeIdMapTest;
    }

    public void setGiftBadgeIdMapTest(Map<Integer, List<Integer>> giftBadgeIdMapTest) {
        this.giftBadgeIdMapTest = giftBadgeIdMapTest;
    }

    public Map<Integer, List<Integer>> getGiftBadgeSendMap() {
        return giftBadgeSendMap;
    }

    public void setGiftBadgeSendMap(Map<Integer, List<Integer>> giftBadgeSendMap) {
        this.giftBadgeSendMap = giftBadgeSendMap;
    }

    public Map<Integer, List<Integer>> getGiftBadgeIdSendMap() {
        return giftBadgeIdSendMap;
    }

    public void setGiftBadgeIdSendMap(Map<Integer, List<Integer>> giftBadgeIdSendMap) {
        this.giftBadgeIdSendMap = giftBadgeIdSendMap;
    }

    public List<Integer> getConquerNumLevelMap(int levelNow) {
        if (ServerConfig.isProduct()) {
            return new ArrayList<>(conquerNumLevelMapProd.get(levelNow));
        } else {
            return new ArrayList<>(conquerNumLevelMapTest.get(levelNow));
        }
    }

    public Map<Integer, List<Integer>> getConquerNumLevelMapProd() {
        return conquerNumLevelMapProd;
    }

    public void setConquerNumLevelMapProd(Map<Integer, List<Integer>> conquerNumLevelMapProd) {
        this.conquerNumLevelMapProd = conquerNumLevelMapProd;
    }

    public Map<Integer, List<Integer>> getConquerNumLevelMapTest() {
        return conquerNumLevelMapTest;
    }

    public void setConquerNumLevelMapTest(Map<Integer, List<Integer>> conquerNumLevelMapTest) {
        this.conquerNumLevelMapTest = conquerNumLevelMapTest;
    }

    public List<Integer> getConquerNumList() {
        if (ServerConfig.isProduct()) {
            return conquerNumListProd;
        } else {
            return conquerNumListTest;
        }
    }

    public List<Integer> getConquerNumListProd() {
        return conquerNumListProd;
    }

    public void setConquerNumListProd(List<Integer> conquerNumListProd) {
        this.conquerNumListProd = conquerNumListProd;
    }

    public List<Integer> getConquerNumListTest() {
        return conquerNumListTest;
    }

    public void setConquerNumListTest(List<Integer> conquerNumListTest) {
        this.conquerNumListTest = conquerNumListTest;
    }

    public Map<Integer, List<LuckyGiftLotteryData>> getLuckyGiftConfigMapProd() {
        return luckyGiftConfigMapProd;
    }

    public void setLuckyGiftConfigMapProd(Map<Integer, List<LuckyGiftLotteryData>> luckyGiftConfigMapProd) {
        this.luckyGiftConfigMapProd = luckyGiftConfigMapProd;
    }

    public Map<Integer, List<LuckyGiftLotteryData>> getLuckyGiftConfigMapTest() {
        return luckyGiftConfigMapTest;
    }

    public void setLuckyGiftConfigMapTest(Map<Integer, List<LuckyGiftLotteryData>> luckyGiftConfigMapTest) {
        this.luckyGiftConfigMapTest = luckyGiftConfigMapTest;
    }

    public Map<Integer, List<LuckyGiftLotteryData>> getLuckyGiftConfigMap() {
        if (ServerConfig.isProduct()) {
            return luckyGiftConfigMapProd;
        } else {
            return luckyGiftConfigMapTest;
        }
    }

    public Map<Integer, List<Integer>> getGiftBadgeMap() {
        if (ServerConfig.isProduct()) {
            return giftBadgeMapProd;
        } else {
            return giftBadgeMapTest;
        }
    }


    public Map<Integer, List<Integer>> getGiftBadgeIdMap() {
        if (ServerConfig.isProduct()) {
            return giftBadgeIdMapProd;
        } else {
            return giftBadgeIdMapTest;
        }
    }
}
