package com.quhong.config;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import org.bson.types.ObjectId;

import java.lang.reflect.Type;

@SuppressWarnings("unchecked")
public class ObjectIdDeserializer implements ObjectDeserializer {
    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        return (T) new ObjectId(parser.getLexer().stringVal());
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}