package com.quhong.config;

import com.quhong.redis.DataRedisBean;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Objects;

/**
 * redis缓存配置，需要手动@EnableCaching
 */
@Configuration
public class RedisCacheConfig {
    public static final String REDIS_10MINUTES = "redis_10minutes";
    public static final String REDIS_1MINUTES = "redis_1minutes";
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainCluster;


    @Lazy
    @Bean(REDIS_10MINUTES)
    public CacheManager redis_10minutes() {
        //配置缓存管理器为json序列化器
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new FastJson2JsonRedisSerializer<>(Object.class)));
        //使用自定义的配置构建缓存管理器
        return RedisCacheManager.builder(Objects.requireNonNull(mainCluster.getConnectionFactory())).cacheDefaults(config).build();
    }

    @Lazy
    @Bean(REDIS_1MINUTES)
    public CacheManager redis_1minutes() {
        //配置缓存管理器为json序列化器
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(1))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new FastJson2JsonRedisSerializer<>(Object.class)));
        //使用自定义的配置构建缓存管理器
        return RedisCacheManager.builder(Objects.requireNonNull(mainCluster.getConnectionFactory())).cacheDefaults(config).build();
    }
}
