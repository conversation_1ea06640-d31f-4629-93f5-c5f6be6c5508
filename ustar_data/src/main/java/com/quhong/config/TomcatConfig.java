package com.quhong.config;

import com.quhong.config.manager.NoSessionManager;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TomcatConfig {

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> cookieProcessorCustomizer() {
        // 自定义session管理器,关闭session
        return factory -> factory.addContextCustomizers(context -> context.setManager(new NoSessionManager()));
    }
}