package com.quhong.userMonitor;

import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 用户警告，封禁等
 */
@Lazy
@Component
public class UserMonitorRedis {
    private static final Logger logger = LoggerFactory.getLogger(UserMonitorRedis.class);
    private static final long CACHE_TIME_MILLIS = 3 * 60 * 60 * 1000L;
    private static final int ALL = 1;
    private final CacheMap<Integer, Set<String>> cacheMap;


    @Resource(name = DataRedisBean.USER_MONITOR)
    private StringRedisTemplate redisTemplate;


    public UserMonitorRedis() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    public boolean isMember(String uid) {
        try {
            Double value = redisTemplate.opsForZSet().score(getMonitorKey(), uid);
            if (value == null) {
                return false;
            }
            long time = value.longValue();
            // 当前时间小于释放时间，则封禁中
            return DateHelper.getNowSeconds() < time;
        } catch (Exception e) {
            logger.error("check user monitor error.uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    public Set<String> getAllFreezeBanUsers() {
        try {
            Set<String> allUsers = cacheMap.getData(ALL);
            if (!CollectionUtils.isEmpty(allUsers)) {
                return allUsers;
            }
            allUsers = redisTemplate.opsForZSet().range(getMonitorKey(), 0, -1);
            if (!CollectionUtils.isEmpty(allUsers)) {
                cacheMap.cacheData(ALL, allUsers);
                return allUsers;
            }
        } catch (Exception e) {
            logger.error("get all party girl error.", e);
        }
        return Collections.emptySet();
    }

    public boolean isFreezeBan(String uid) {
        try {
            return getAllFreezeBanUsers().contains(uid);
        } catch (Exception e) {
            logger.error("isFreezeBan error.", e);
            return false;
        }
    }

    public void addMonitorUser(String uid, long score) {
        try {
            String key = getMonitorKey();
            redisTemplate.opsForZSet().add(key, uid, score);
            redisTemplate.expire(key, 90, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setUnlockGiftExpireScore error uid={} score={}", uid, score, e);
        }
    }

    public void delMonitorUser(String uid) {
        try {
            String key = getMonitorKey();
            redisTemplate.opsForZSet().remove(key, uid);
        } catch (Exception e) {
            logger.info("delMonitorUser error uid={} ", uid, e);
        }
    }

    public Set<String> getAllMonitorUser(int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getMonitorKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("getAllMonitorUser error. timestamp={} {}", timestamp, e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    private String getMonitorKey() {
        return "monitor_user";
    }
}
