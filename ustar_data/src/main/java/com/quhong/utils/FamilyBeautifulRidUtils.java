package com.quhong.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/2
 */
public class FamilyBeautifulRidUtils {

    public static final int MIN_RID = 10000;
    public static final int MAX_RID = 999999;

    // 连续3位相同数字的组合
    private static final List<String> LIST_1 = Arrays.asList("000","111","222","333","444","555","444","555","666","777","888","999");
    //连续4位数字递增或递减的数字组合
    private static final List<String> LIST_2 = Arrays.asList("0123","1234","2345","3456","4567","5678","6789","9876","8765","7654","6543","5432","4321","3210");
    // 连续2位相同数字组合
    private static final List<String> LIST_3 = Arrays.asList("00","11","22","33","44","55","66","77","88","99");
    // 连续2位数字递增或递减的数字组合
    private static final List<String> LIST_4 = Arrays.asList("01","12","23","34","45","56","67","78","89","98","87","76","65","54","43","32","21","10");

    //连续3位数字递增或递减的数字组合
    private static final List<String> LIST_5 = Arrays.asList("012","123","234","345","456","567","678","789","987","876","765","654","543","432","321","210");

    /**
     * 判断rid是否符合靓号规则
     */
    public static boolean isBeautifulRid(String strRid) {
        // 至少含有1组连续3位或以上相同数字以上的组合
        if (isBeautifulRid1(strRid, LIST_1)) {
            return true;
        }
        // 至少含有1组连续3位数字递增或递减的数字组合
        if (isBeautifulRid1(strRid, LIST_5)) {
            return true;
        }

        // 至少含有1组连续4位数字递增或递减的数字组合
//        if (isBeautifulRid1(strRid, LIST_2)) {
//            return true;
//        }
        // 至少含有2组或以上连续2位相同数字组合
//        if (isBeautifulRid2(strRid, LIST_3)) {
//            return true;
//        }
        // 至少含有2组或以上连续2位数字递增或递减的数字组合
//        if (isBeautifulRid2(strRid, LIST_4)) {
//            return true;
//        }
        return false;
    }

    /**
     * 获取所以符合靓号规则的rid (共计：12493个)
     */
    public static List<String> getAllBeautifulRids () {
        List<String> beautifulRid = new ArrayList<>();
        for (int i = MIN_RID; i <= MAX_RID; i++) {
            String strRid = String.valueOf(i);
            if (isBeautifulRid(strRid)) {
                beautifulRid.add(strRid);
            }
        }
        return beautifulRid;
    }

    private static boolean isBeautifulRid1(String strRid, List<String> list) {
        for (String s : list) {
            if (strRid.contains(s)) {
                return true;
            }
        }
        return false;
    }

    private static boolean isBeautifulRid2(String strRid, List<String> list) {
        for (String s1 : list) {
            if (strRid.contains(s1)) {
                String strRid1 = strRid.replaceFirst(s1, "---");
                for (String s2 : list) {
                    if (strRid1.contains(s2)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}
