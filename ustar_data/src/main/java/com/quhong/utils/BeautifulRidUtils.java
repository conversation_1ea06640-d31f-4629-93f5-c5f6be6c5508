package com.quhong.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/19
 */
public class BeautifulRidUtils {

    public static final int MIN_RID = 1000000;
    public static final int MAX_RID = 99999999;

    // 连续3位相同数字的组合
    private static final List<String> LIST_1 = Arrays.asList("000", "111", "222", "333", "444", "555", "444", "555", "666", "777", "888", "999");
    // 连续3位数字递增或递减的数字组合
    private static final List<String> LIST_2 = Arrays.asList("012", "123", "234", "345", "456", "567", "678", "789", "987", "876", "765", "654", "543", "432", "321", "210");
    //连续4位数字递增或递减的数字组合
    private static final List<String> LIST_3 = Arrays.asList("0123", "1234", "2345", "3456", "4567", "5678", "6789", "9876", "8765", "7654", "6543", "5432", "4321", "3210");
    // 连续2位相同数字组合
    private static final List<String> LIST_4 = Arrays.asList("00", "11", "22", "33", "44", "55", "66", "77", "88", "99");
    // 连续2位数字递增或递减的数字组合
    private static final List<String> LIST_5 = Arrays.asList("01", "12", "23", "34", "45", "56", "67", "78", "89", "98", "87", "76", "65", "54", "43", "32", "21", "10");
    // 连续2组、连续2位数字递增或递减的数字组合
    private static final List<String> LIST_6 = Arrays.asList("0101", "1212", "2323", "3434", "4545", "5656", "6767", "7878", "8989", "9898", "8787", "7676", "6565", "5454", "4343", "3232", "2121", "1010");

    /**
     * 判断rid是否符合靓号规则
     */
    public static boolean isBeautifulRid(String numStr) {
        // 至少含有1组连续3位或以上相同数字以上的组合
        if (isBeautifulRid1(numStr, LIST_1)) {
            return true;
        }
        // 至少含有1组连续4位数字递增或递减的数字组合
        if (isBeautifulRid1(numStr, LIST_3)) {
            return true;
        }
        if (numStr.length() == 7) {
            return isExcluded7(numStr);
        } else if (numStr.length() == 8) {
            return isExcluded8(numStr);
        }
        return false; // 不符合任何排除规则
    }

    private static boolean isExcluded7(String numStr) {
        // 检查 AABB 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2\\d*")) return true;

        // 检查 ABAB 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\1\\2\\d*")) return true;

        // 检查 ABBABB 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\2(\\d)\\1\\1\\d*")) return true;

        // 检查 ABAABA 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\1\\2\\2\\1\\d*")) return true;

        // 检查 AABAAB 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2\\1\\1\\d*")) return true;

        // 检查 ABCABC 类型
        if (numStr.matches("\\d*(\\d)(\\d)(\\d)\\1\\2\\3\\d*")) return true;

        // 检查 ABCCBA 类型
        if (numStr.matches("\\d*(\\d)(\\d)(\\d)\\3\\2\\1\\d*")) return true;

        return false; // 不符合任何排除规则
    }

    private static boolean isExcluded8(String numStr) {
        // 检查 AABBCC 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2(\\d)\\3\\d*")) return true;
        // 检查 ABABAB 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\1\\2\\1\\2\\d*")) return true;
        // 检查 ABBABB 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\2(\\d)\\1\\1\\d*")) return true;
        // 检查 ABAABA 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\1\\2\\2\\1\\d*")) return true;
        // 检查 AABAAB 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2\\1\\1\\d*")) return true;
        // 检查 AABBAA 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2\\2\\1\\1\\d*")) return true;
        // 检查 ABCABC 类型
        if (numStr.matches("\\d*(\\d)(\\d)(\\d)\\1\\2\\3\\d*")) return true;
        // 检查 ABCCBA 类型
        if (numStr.matches("\\d*(\\d)(\\d)(\\d)\\3\\2\\1\\d*")) return true;

//        // 检查 AAABBB 类型
//        if (numStr.matches("\\d*(\\d)\\1\\1(\\d)\\2\\2\\d*")) return true;

        // 检查 ABCDABCD 类型
//        if (numStr.matches("\\d*(\\d)(\\d)(\\d)(\\d)\\1\\2\\3\\4\\d*")) return true;

        // 检查 ABCDDCBA 类型
//        if (numStr.matches("\\d*(\\d)(\\d)(\\d)(\\d)\\4\\3\\2\\1\\d*")) return true;
        return false; // 不符合任何排除规则
    }

    /**
     * 获取所以符合靓号规则的rid (8000000-9000000中符合条件的共计：883277个)
     */
    public static List<String> getAllBeautifulRids() {
        List<String> beautifulRid = new ArrayList<>();
        for (int i = MIN_RID; i <= MAX_RID; i++) {
            String strRid = String.valueOf(i);
            if (isBeautifulRid(strRid)) {
                beautifulRid.add(strRid);
            }
        }
        return beautifulRid;
    }

    private static boolean isBeautifulRid1(String strRid, List<String> list) {
        for (String s : list) {
            if (strRid.contains(s)) {
                return true;
            }
        }
        return false;
    }

    private static boolean isBeautifulRid2(String strRid, List<String> list) {
        for (String s1 : list) {
            if (strRid.contains(s1)) {
                String strRid1 = strRid.replaceFirst(s1, "---");
                for (String s2 : list) {
                    if (strRid1.contains(s2)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private static boolean isBeautifulRid3(String strRid, List<String> list) {
        for (String s1 : list) {
            if (strRid.contains(s1)) {
                String strRid1 = strRid.replaceFirst(s1, "--");
                for (String s2 : list) {
                    if (strRid1.contains(s2)) {
                        String strRid2 = strRid1.replaceFirst(s2, "--");
                        for (String s3 : list) {
                            if (strRid2.contains(s3)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }
}
