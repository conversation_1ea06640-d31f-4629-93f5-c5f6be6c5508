package com.quhong.utils;

import com.quhong.core.utils.DateHelper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public class MatchUtils {
    public static String generateLikeIndex(String id1, String id2) {
        if (id1.compareTo(id2) > 0) {
            return id2 + "#" + id1;
        } else {
            return id1 + "#" + id2;
        }
    }

    public static String generateFriendIndex(String id1, String id2) {
        if (id1.compareTo(id2) > 0) {
            return id2 + "_" + id1;
        } else {
            return id1 + "_" + id2;
        }
    }

    public static List<String> getABFriend(String id1, String id2) {
        List<String> list = new ArrayList<>();
        if (id1.compareTo(id2) > 0) {
            list.add(id2);
            list.add(id1);
        } else {
            list.add(id1);
            list.add(id2);
        }
        return list;
    }

    /**
     * 格式化贡献数值
     */
    public static String formatDevotes(long devotes) {
        return  formatDevotes (devotes,RoundingMode.HALF_UP);
    }

    /**
     * 格式化贡献数值
     */
    public static String formatDevotes(long devotes,RoundingMode mode) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f + "").setScale(1, mode) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f + "").setScale(1, mode) + "K";
        } else {
            return devotes + "";
        }
    }


    public static int getStepFromDayStart(int stepLength) {
        if (stepLength != 0) {
            long startTimeSec = DateHelper.DEFAULT.getTodayStartTime() / 1000;
            int nowSec = (int) (DateHelper.getNowSeconds() - startTimeSec);
            return nowSec / stepLength + 1;
        }
        return 0;
    }


    public static int getPreStepByStep(int step, int stepLength) {
        if (stepLength == 0) {
            return --step;
        } else {
            int maxStep = 86400 / stepLength;
            if (step == 1) {
                return maxStep;
            } else {
                return --step;
            }
        }
    }

    public static int getIndexByScore(int score, List<Integer> srcList) {
        List<Integer> tempLevelNumList = new ArrayList<>(srcList);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }
}
