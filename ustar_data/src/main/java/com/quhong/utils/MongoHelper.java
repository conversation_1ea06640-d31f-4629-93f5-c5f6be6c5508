package com.quhong.utils;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MongoHelper {
    private static final Logger logger = LoggerFactory.getLogger(MongoHelper.class);

    public static ObjectId getObjectId(int time){
        StringBuilder objectId = new StringBuilder("");
        objectId.append(Integer.toHexString(time));
        while(objectId.length() < 24) {
            objectId.append("0");
        }
        return new ObjectId(objectId.toString());
    }
}
