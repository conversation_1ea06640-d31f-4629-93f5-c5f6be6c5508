package com.quhong.utils;

import java.util.List;

public class PageUtils {

    public static <E> PageData<E> getPageData(List<E> reqList, int reqPage, int pageSize) {
        if (reqList.size() == 0) {
            PageData<E> pageData = new PageData<E>();
            pageData.totalPage = 0;
            pageData.page = 0;
            pageData.list = reqList;
            pageData.pageSize = pageSize;
            pageData.nextPage = 0;
            pageData.totalSize = 0;
            return pageData;
        }
        int mod = reqList.size() % pageSize;
        int totalPage;
        if (mod > 0) {
            totalPage = reqList.size() / pageSize + 1;
        } else {
            totalPage = reqList.size() / pageSize;
        }
        if (reqPage > totalPage) {
            reqPage = totalPage;
        }
        if (reqPage == 0) {
            reqPage = 1;
        }
        int start = (reqPage - 1) * pageSize;
        int end = reqPage * pageSize;
        if (end > reqList.size()) {
            end = reqList.size();
        }
        List<E> retList = reqList.subList(start, end);
        PageData<E> pageData = new PageData<E>();
        pageData.totalPage = totalPage;
        pageData.page = reqPage;
        pageData.list = retList;
        pageData.pageSize = pageSize;
        int nextPage = pageData.page + 1;
        if (pageData.page >= pageData.totalPage) {
            nextPage = 0;
        }
        pageData.nextPage = nextPage;
        pageData.totalSize = reqList.size();
        return pageData;
    }

    public static class PageData<E> {
        public List<E> list;
        public int page;
        public int totalPage;
        public int pageSize;
        public int nextPage;
        public int totalSize;

        public PageData() {

        }
    }
}
