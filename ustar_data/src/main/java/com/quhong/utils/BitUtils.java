package com.quhong.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class BitUtils {
    // 最大支持标签id范围
    public static final int MAX_TAGS = 100;

    // 标签列表转字节数组位图
    public static byte[] tagsToBytes(List<Integer> tags, int maxTags) {
        if (tags == null || tags.isEmpty()) {
            return new byte[0];
        }
        int byteSize = (maxTags + 7) / 8; // 向上取整
        byte[] result = new byte[byteSize];

        for (Integer tag : tags) {
            if (tag > 0 && tag <= maxTags) {
                int byteIndex = (tag - 1) / 8;
                int bitIndex = (tag - 1) % 8;
                result[byteIndex] |= (byte) (1 << bitIndex);
            }
        }
        return result;
    }


    // 重载方法保持兼容性
    public static byte[] tagsToBytes(List<Integer> tags) {
        return tagsToBytes(tags, MAX_TAGS); // 默认支持标签ID范围1-100
    }

    // 计算重合标签数量
    public static int getMatchCount(byte[] tags1, byte[] tags2) {
        int count = 0;
        int minLength = Math.min(tags1.length, tags2.length);

        for (int i = 0; i < minLength; i++) {
            count += Integer.bitCount(tags1[i] & tags2[i] & 0xFF);
        }
        return count;
    }

    // 字节数组转标签列表
    public static List<Integer> bytesToTags(byte[] tagBytes) {
        List<Integer> tags = new ArrayList<>();
        for (int i = 0; i < tagBytes.length; i++) {
            for (int j = 0; j < 8; j++) {
                if ((tagBytes[i] & (1 << j)) != 0) {
                    tags.add(i * 8 + j + 1);
                }
            }
        }
        return tags;
    }

    public static void main(String[] args) {
        List<Integer> tags = Arrays.asList(3, 4, 1, 22, 33, 44, 55, 66, 77, 88);
        byte[] bytes1 = tagsToBytes(Collections.emptyList());
        byte[] bytes2 = tagsToBytes(Collections.emptyList());
        System.out.println(getMatchCount(bytes1, bytes2));
    }
}
