package com.quhong.utils;

import com.quhong.core.utils.DateHelper;
import org.bson.types.ObjectId;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class ActorUtils {

    // 海湾国家区域: 沙特、阿联酋、科威特、阿曼、卡塔尔、巴林
    public static final List<String> GULF_REGION_LIST = Arrays.asList("SA", "AE", "KW", "OM", "QA", "BH");
    // 穆斯林国家区域: 伊拉克、 埃及、 阿尔及利亚、 摩洛哥、 利比亚、 约旦、 土耳其、 叙利亚、 黎巴嫩、 也门、 巴勒斯坦、 索马里、 伊朗、 苏丹
    public static final List<String> MUSLIM_REGION_LIST = Arrays.asList("IQ", "EG", "DZ", "MA", "LY", "JO", "TR", "SY", "LB", "YE", "PS", "SO", "IR", "SD");
    // 欧美国家区域：突尼斯 、法国 、意大利 、加拿大 、美国 、德国 、西班牙 、英国 、芬兰 、奥地利 、瑞典 、希腊 、瑞士 、挪威 、丹麦 、葡萄牙 、比利时 、荷兰 、孟加拉国 、印度尼西亚 、印度 、菲律宾 、巴基斯坦
    public static final List<String> EUROPEAN_AMERICAN_REGION_LIST = Arrays.asList("TN", "FR", "IT", "CA", "US", "DE", "ES", "GB", "FI", "AT", "SE", "GR", "CH", "NO", "DK", "PT", "BE", "NL", "BD", "ID", "IN", "PH", "PK");
    // 非洲国家区域: 斯里兰卡 、吉布提 、科摩罗 、毛利塔尼亚 、埃塞俄比亚 、巴西
    public static final List<String> AFRICAN_REGION_LIST = Arrays.asList("LK", "DJ", "KM", "MR", "ET", "BR");

    /**
     * 获取用户注册天数
     */
    public static int getRegDays(String uid) {
        int day = (int) ((System.currentTimeMillis() - new ObjectId(uid).getTimestamp() * 1000L) / (1000 * 3600 * 24));
        return day == 0 ? 1 : day;
    }

    /**
     * 是否当天新注册用户
     */
    public static boolean isNewRegisterActor(String uid) {
        long startTime = DateHelper.DEFAULT.getTodayStartTime();
        return isNewRegisterActor(uid, startTime);
    }

    /**
     * 是否当天新注册用户
     *
     * @param startTime 这天的开始时间，单位 毫秒
     */
    public static boolean isNewRegisterActor(String uid, long startTime) {
        long endTime = DateHelper.DEFAULT.getDayOffset(startTime, 1);
        long registerTime = new ObjectId(uid).getTimestamp() * 1000L;
        return (registerTime >= startTime && registerTime < endTime);
    }

    /**
     * 是否是指定日期注册的用户
     *
     * @param date 指定的日期，yyyy-MM-dd
     */
    public static boolean isNewRegisterActor(String uid, String date) {
        Integer[] timeArr = DateHelper.ARABIAN.getTimeWhereRange(date);
        long registerTime = new ObjectId(uid).getTimestamp();
        return (registerTime >= timeArr[0] && registerTime < timeArr[1]);
    }

    /**
     * 是否是指定日期指定时段注册的用户
     *
     * @param date 指定的日期，yyyy-MM-dd
     * @param hour 时段，0~23
     */
    public static boolean isNewRegisterActor(String uid, String date, int hour) {
        Integer[] timeArr = DateHelper.ARABIAN.getTimeWhereRange(date);
        // 按照小时偏移
        int start = timeArr[0] + hour * 3600;
        int end = start + 3600;
        long registerTime = new ObjectId(uid).getTimestamp();
        return (registerTime >= start && registerTime < end);
    }

    /**
     * 是否最近n天注册的用户
     *
     * @param day 最近n天
     */
    public static boolean isNewRegisterActor(String uid, int day) {
        long registerTime = new ObjectId(uid).getTimestamp();
        return (registerTime + day * 24 * 60 * 60L) > DateHelper.getNowSeconds();
    }

    public static boolean isNewRegisterActor(String uid, long endTime, int offDay) {
        long startTime = DateHelper.ARABIAN.getDayOffset(endTime, offDay);
        long registerTime = new ObjectId(uid).getTimestamp() * 1000L;
        return (registerTime >= startTime && registerTime < endTime);
    }

    public static boolean isSameCountry(String fromCountry, String toCountry) {
        if (StringUtils.isEmpty(fromCountry) || fromCountry.trim().length() < 2
                || StringUtils.isEmpty(toCountry) || toCountry.trim().length() < 2) {
            return false;
        }
        return fromCountry.substring(0, 2).equalsIgnoreCase(toCountry.substring(0, 2));
    }

    public static String getCountryCode(String country) {
        if (StringUtils.isEmpty(country) || country.trim().length() < 2) {
            return null;
        }
        return country.substring(0, 2).toLowerCase();
    }

    public static String getUpperCaseCountryCode(String country) {
        if (StringUtils.isEmpty(country) || country.trim().length() < 2) {
            return null;
        }
        return country.substring(0, 2).toUpperCase();
    }

    /**
     * 2021-08-01 16:00:00以后的数据为810新版本
     */
    public static boolean is810NewVersion(String uid) {
        return new ObjectId(uid).getTimestamp() > **********;
    }

    /**
     * 获取uid最后一位字符
     */
    public static char getUidEndChar(String uid) {
        return uid.charAt(uid.length() - 1);
    }

    public static boolean isNewDeviceAccount(String uid, String firstTnId) {
        return isNewDeviceAccount(uid, 7, firstTnId);
    }

    public static boolean isNewDeviceAccount(String uid, int day, String firstTnId) {
        return isNewRegisterActor(uid, day) && StringUtils.hasLength(firstTnId);
    }

    public static String getStrObjectIdByTime(int timeStamp){
        return new ObjectId(new Date(timeStamp * 1000L)).toString();
    }

    public static String generateMsgIndex(String id1, String id2){
        if(id1.compareTo(id2) > 0){
            return id2 + "#" + id1;
        }else{
            return id1 + "#" + id2;
        }
    }

    public static int getRegionByCountryCode(String countryCode) {
        if (ObjectUtils.isEmpty(countryCode)) {
            return 100;
        }
        if (GULF_REGION_LIST.contains(countryCode)) {
            return 1;
        } else if (MUSLIM_REGION_LIST.contains(countryCode)) {
            return 2;
        } else if (EUROPEAN_AMERICAN_REGION_LIST.contains(countryCode)) {
            return 3;
        } else if (AFRICAN_REGION_LIST.contains(countryCode)) {
            return 4;
        } else {
            return 100;
        }
    }

}
