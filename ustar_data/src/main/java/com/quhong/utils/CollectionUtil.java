package com.quhong.utils;


import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 集合工具类
 */
public class CollectionUtil {

    /**
     * 根据list 获取任意属性的列表
     */
    public static <T, K> List<T> getPropertyList(Collection<K> list, Function<K, T> mapper, T def) {
        return Optional.ofNullable(list).filter(l -> !l.isEmpty())
                .map(l -> l.stream().map(mapper).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElseGet(() -> Optional.ofNullable(def).map(Collections::singletonList).orElse(null));
    }

    /**
     * 根据list 获取任意属性的集合 ids 字符串
     */
    public static <T, K> String getProperties(Collection<K> list, Function<K, T> mapper) {
        return Optional.ofNullable(list).filter(l -> !l.isEmpty())
                .map(l -> list.stream().map(mapper).filter(Objects::nonNull)
                        .map(String::valueOf).distinct()
                        .collect(Collectors.joining(","))).orElse("-1");
    }

    /**
     * 将List转化为 key map
     */
    public static <T, K> Map<K, T> listToKeyMap(Collection<T> list, Function<T, K> keyMapper) {
        return Optional.ofNullable(list).filter(l -> !l.isEmpty())
                .map(l -> l.stream().collect(Collectors.toMap(keyMapper, Function.identity(), (x, y) -> y)))
                .orElse(Collections.emptyMap());
    }

    /**
     * 将List转化为 key List map
     */
    public static <T, K> Map<K, List<T>> listToKeyListMap(Collection<T> list, Function<T, K> keyMapper) {
        return list.stream().collect(Collectors.groupingBy(keyMapper));
    }

    /**
     * list 对象转属性set
     */
    public static <T, K> Set<K> listToPropertySet(Collection<T> list, Function<T, K> setMapper) {
        return Optional.ofNullable(list).filter(l -> !l.isEmpty())
                .map(l -> l.stream().map(setMapper).collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
    }

    /**
     * 集合分割
     *
     * @param list      需要分割的列表
     * @param splitSize 分割大小
     */
    public static <T> List<List<T>> split(Collection<T> list, int splitSize) {
        long group = (long) Math.ceil((double) list.size() / splitSize);
        return Stream.iterate(0, n -> n + splitSize).limit(group).map(i ->
                list.stream().skip(i).limit(splitSize).collect(Collectors.toList())
        ).collect(Collectors.toList());
    }
}
