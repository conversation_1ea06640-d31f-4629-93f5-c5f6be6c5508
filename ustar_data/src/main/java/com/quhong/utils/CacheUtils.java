package com.quhong.utils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

public class CacheUtils {
    private static final Logger logger = LoggerFactory.getLogger(CacheUtils.class);

    private static final Cache<String, Object> caffeineCache = Caffeine.newBuilder()
            // 设置最后一次写入或访问后经过固定时间过期
            .expireAfterAccess(20, TimeUnit.MINUTES)
            // 初始的缓存空间大小
            .initialCapacity(1000)
            // 缓存的最大条数
            .maximumSize(50000)
            .build();

    public static <T> T get(String key) {
        try {
            // noinspection unchecked
            return (T) caffeineCache.getIfPresent(key);
        } catch (Exception e) {
            logger.error("get from cache error. key={} {}", key, e.getMessage());
            return null;
        }
    }

    public static boolean hasKey(String key) {
        // 刷新过期时间
        return get(key) != null;
    }

    public static void put(String key, Object value) {
        caffeineCache.put(key, value);
    }

    public static void remove(String key) {
        caffeineCache.invalidate(key);
    }
}
