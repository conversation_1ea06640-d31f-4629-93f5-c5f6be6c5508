package com.quhong.data;

import com.quhong.enums.RoomRoleType;

public class RoomRoleData implements Comparable<RoomRoleData> {
    private String uid;
    /**
     * @see com.quhong.enums.RoomRoleType
     */
    private int role;
    private int viceHost; // 0 否，1 副房主

    public RoomRoleData() {

    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getViceHost() {
        return viceHost;
    }

    public void setViceHost(int viceHost) {
        this.viceHost = viceHost;
    }

    public boolean isHostOrVice() {
        return role == 0 || viceHost == 1;
    }

    public boolean isAdmin() {
        return role == 0 || viceHost == 1 || role == 1;
    }

    /**
     * 1 房主
     * 2 副房主
     * 3 管理员
     * 4 会员
     * 5 观众
     */
    public int getReportRole() {
        if (this.role == RoomRoleType.HOST){
            return 1;
        }
        if (this.viceHost == 1){
            return 2;
        }
        if (this.role == RoomRoleType.MANAGER){
            return 3;
        }
        if (this.role == RoomRoleType.MEMBER){
            return 4;
        }
        return 5;
    }

    @Override
    public int compareTo(RoomRoleData o) {
        if (this.role == 0) {
            return -1;
        } else if (o.role == 0) {
            return 1;
        }
        int delta = this.role - o.role;
        if (delta == 0) {
            if (this.viceHost == 1) {
                return -1;
            } else if (o.viceHost == 1) {
                return 1;
            } else {
                return 0;
            }
        }
        return delta;
    }
}
