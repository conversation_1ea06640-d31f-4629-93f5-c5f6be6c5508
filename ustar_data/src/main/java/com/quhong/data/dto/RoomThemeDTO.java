package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
public class RoomThemeDTO extends HttpEnvData {

    /**
     * 房间主题id
     */
    private int themeId;
    private boolean pushAll;  // 主题变化消息是否推给全房间(包含自己)
    private boolean noCheckRole;  // 修改麦位主题是否不校验身份

    public int getThemeId() {
        return themeId;
    }

    public void setThemeId(int themeId) {
        this.themeId = themeId;
    }

    public boolean isPushAll() {
        return pushAll;
    }

    public void setPushAll(boolean pushAll) {
        this.pushAll = pushAll;
    }

    public boolean isNoCheckRole() {
        return noCheckRole;
    }

    public void setNoCheckRole(boolean noCheckRole) {
        this.noCheckRole = noCheckRole;
    }
}
