package com.quhong.executors;

import com.quhong.core.clusters.ClusterConnector;
import com.quhong.core.clusters.ClusterGroup;
import com.quhong.core.clusters.servers.ClusterServerConnector;
import com.quhong.core.executors.AbstractMsgExecutor;
import com.quhong.core.msg.ServerHeader;
import com.quhong.core.msg.ServerMsg;
import com.quhong.datas.PlayerData;

public abstract class BaseServerMsgExecutor<M extends ServerMsg> extends AbstractMsgExecutor<ClusterServerConnector, M> {
    public BaseServerMsgExecutor(int cmd) {
        super(cmd);
    }

    protected void sendMsg(PlayerData playerData, ServerMsg msg){
        if(msg.getHeader() == null){
            ServerHeader header = new ServerHeader();
            msg.setHeader(header);
        }
        msg.getHeader().setSessionId(playerData.getSessionId());
        msg.getHeader().setUid(playerData.getUid());
        ClusterGroup.sendMsg(ClusterGroup.fetchServerIdFromSessionId(msg.getSessionId()), msg);
    }

    protected void sendMsg(ClusterConnector connector, PlayerData playerData, ServerMsg msg){
        if(msg.getHeader() == null){
            ServerHeader header = new ServerHeader();
            msg.setHeader(header);
        }
        msg.getHeader().setSessionId(playerData.getSessionId());
        msg.getHeader().setUid(playerData.getUid());
        connector.sendMsg(msg);
    }
}
