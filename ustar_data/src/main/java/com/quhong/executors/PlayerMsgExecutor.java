package com.quhong.executors;

import com.quhong.core.clusters.servers.ClusterServerConnector;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.msg.ServerMsg;
import com.quhong.datas.PlayerData;
import com.quhong.player.cache.Player;
import com.quhong.player.cache.PlayerCacheMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class PlayerMsgExecutor<M extends ServerMsg> extends BaseServerMsgExecutor<M> {
    private static final Logger logger = LoggerFactory.getLogger(PlayerMsgExecutor.class);

    @Autowired
    private PlayerCacheMap cacheMap;

    public PlayerMsgExecutor(int cmd) {
        super(cmd);
    }

    @Override
    public void execute(ClusterServerConnector connector, M msg) {
        Player player = cacheMap.getPlayer(msg.getUid());
        if (player == null) {
            logger.error("playerData can not found. uid={} sessionId={} cmd={}", msg.getUid(), msg.getSessionId(), msg.getCmd());
            return;
        }
        player.getPlayerData().setSessionId(msg.getSessionId());
        player.add(new Task() {
            @Override
            protected void execute() {
                doExecute(connector, player, msg);
            }
        });
    }

    protected abstract void doExecute(ClusterServerConnector connector, Player player, M msg);

    protected boolean sendMsg(String uid, ServerMsg msg) {
        Player player = cacheMap.getPlayer(msg.getUid());
        if (player == null) {
            logger.error("playerData can not found. uid={} sessionId={} cmd={}", msg.getUid(), msg.getSessionId(), msg.getCmd());
            return false;
        }
        PlayerData playerData = player.getPlayerData();
        if (playerData == null || playerData.getSessionId() == -1) {
            return false;
        }
        sendMsg(playerData, msg);
        return true;
    }
}
