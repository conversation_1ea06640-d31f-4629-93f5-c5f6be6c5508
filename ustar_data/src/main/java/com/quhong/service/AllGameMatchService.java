package com.quhong.service;

import com.quhong.data.TurntableGameInfo;
import com.quhong.enums.SudGameConstant;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.SudGameData;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.redis.TurntableGameRedis;
import com.quhong.room.redis.RoomKickRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


@Lazy
@Service
public class AllGameMatchService {
    private static final Logger logger = LoggerFactory.getLogger(AllGameMatchService.class);
    private static final List<Integer> GAME_OTHER_LIST = Arrays.asList(SudGameConstant.LUDO_GAME, SudGameConstant.UMO_GAME,
            SudGameConstant.MONSTER_CRUSH_GAME, SudGameConstant.DOMINO_GAME, SudGameConstant.CARROM_POOL_GAME,
            SudGameConstant.BILLIARD_GAME, SudGameConstant.JACKAROO_GAME, SudGameConstant.BALOOT_GAME);
    public static final int GAME_LUCKY_WHEEL = 1;
    public static final int LUCKY_GAME_WAITING = 1;  // 等待其他人加入
    public static final int LUCKY_GAME_RUNNING = 2;  // 游戏进行中

    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;

    /**
     * 游戏房查找 - 按优先级查找合适的游戏房间
     *
     * 房间查找优先级：
     * 第一优先级：查找该游戏下 "已创建且等待开始" 状态的房间（如：已满员前，倒计时等待中）
     * 第二优先级：若上一步无满足条件的房间，则查找该游戏下 "已开始" 状态的房间（如：游戏正在进行中）
     * 第三优先级：若前两步均无满足条件的房间，随机选择一个"未开始" 状态的其他游戏房间（如：刚创建，尚有空位）
     *
     * @param uid 用户ID
     * @param gameType 游戏类型
     * @param roomType 房间类型
     * @return 匹配到的房间ID，如果没有找到返回null
     */
    public String findGameRoomByPriority(String uid, int gameType, int roomType) {
        logger.info("开始查找游戏房间 uid={}, gameType={}, roomType={}", uid, gameType, roomType);

        // 特殊处理：gameType=1 对应 Lucky Wheel 游戏
        if (gameType == GAME_LUCKY_WHEEL) {
            return findLuckyWheelGameRoom(uid);
        }

        // 第一优先级：查找该游戏下 "已创建且等待开始" 状态的房间
        String roomId = findWaitingGameRooms(uid, gameType, roomType);
        if (!StringUtils.isEmpty(roomId)) {
            return roomId;
        }

        // 第二优先级：查找该游戏下 "已开始" 状态的房间
        roomId = findProcessingGameRooms(uid, gameType, roomType);
        if (!StringUtils.isEmpty(roomId)) {
            return roomId;
        }

        // 第三优先级：随机选择一个"未开始" 状态的其他游戏房间
        roomId = findRandomUnstartedGameRoom(uid, roomType);
        if (!StringUtils.isEmpty(roomId)) {
            return roomId;
        }

        logger.info("未找到合适的游戏房间 uid={}, gameType={}", uid, gameType);
        return null;
    }

    /**
     * 第一优先级：查找该游戏下 "已创建且等待开始" 状态的房间
     * 包括 GAME_MATCHING（匹配中)
     */
    private String findWaitingGameRooms(String uid, int gameType, int roomType) {
        try {
            // 查找匹配中的游戏
            List<SudGameData> matchingGames = sudGameDao.findMatchingByGameType(gameType, roomType);
            // 随机打乱顺序
            Collections.shuffle(matchingGames);

            // 查找合适的房间
            for (SudGameData gameData : matchingGames) {
                if (isRoomSuitable(gameData.getRoomId(), uid)) {
                    return gameData.getRoomId();
                }
            }
        } catch (Exception e) {
            logger.error("findWaitingGameRooms uid={}, gameType={}, error={}", uid, gameType, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 第二优先级：查找该游戏下 "已开始" 状态的房间
     */
    private String findProcessingGameRooms(String uid, int gameType, int roomType) {
        try {
            List<SudGameData> processingGames = sudGameDao.findProcessingByGameType(gameType, roomType);
            Collections.shuffle(processingGames);

            for (SudGameData gameData : processingGames) {
                if (isRoomSuitable(gameData.getRoomId(), uid)) {
                    return gameData.getRoomId();
                }
            }
        } catch (Exception e) {
            logger.error("findProcessingGameRooms uid={}, gameType={}, error={}", uid, gameType, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 第三优先级：随机选择一个"未开始" 状态的其他游戏房间
     */
    private String findRandomUnstartedGameRoom(String uid, int roomType) {
        try {
            // 获取所有游戏类型的匹配中游戏
            for (int gameType : GAME_OTHER_LIST) {
                try {

                    List<SudGameData> games = sudGameDao.findMatchingByGameType(gameType, roomType);
                    if (CollectionUtils.isEmpty(games)) {
                        continue;
                    }

                    for (SudGameData gameData : games) {
                        if (isRoomSuitable(gameData.getRoomId(), uid)) {
                            return gameData.getRoomId();
                        }
                    }
                } catch (Exception e) {
                    logger.warn("findRandomUnstartedGameRoom gameType={}, error: {}", gameType, e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error("查找随机未开始游戏房间失败 uid={}, error={}", uid, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 检查房间是否适合用户加入
     */
    private boolean isRoomSuitable(String roomId, String uid) {
        try {
            // 检查房间是否存在
            MongoRoomData roomData = mongoRoomDao.findData(roomId);
            if (roomData == null) {
                return false;
            }

            // 检查房间是否有密码
            if (!StringUtils.isEmpty(roomData.getPwd())) {
                return false;
            }

            // 检查用户是否被房间拉黑
            if (roomBlacklistDao.isBlock(roomId, uid)) {
                return false;
            }

            // 检查用户是否被踢出房间
            if (roomKickRedis.isKick(roomId, uid)) {
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.error("检查房间适用性失败 roomId={}, uid={}, error={}", roomId, uid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 查找Lucky Wheel游戏房间 (gameType=1)
     * 按优先级查找：等待中 -> 进行中 -> 其他游戏的等待中房间
     */
    private String findLuckyWheelGameRoom(String uid) {
        try {
            List<TurntableGameInfo> allLuckyWheelGames = turntableGameRedis.getAllTurntableGame();

            if (CollectionUtils.isEmpty(allLuckyWheelGames)) {
                logger.info("当前没有Lucky Wheel游戏 uid={}", uid);
                return findRandomUnstartedGameRoom(uid, SudGameConstant.VOICE_ROOM);
            }
            Collections.shuffle(allLuckyWheelGames);
            // 第一优先级：查找等待中的Lucky Wheel游戏
            String roomId = findLuckyWheelByStatus(allLuckyWheelGames, uid, LUCKY_GAME_WAITING);
            if (!StringUtils.isEmpty(roomId)) {
                return roomId;
            }
            // 第二优先级：查找进行中的Lucky Wheel游戏
            roomId = findLuckyWheelByStatus(allLuckyWheelGames, uid, LUCKY_GAME_RUNNING);
            if (!StringUtils.isEmpty(roomId)) {
                return roomId;
            }
            // 第三优先级：查找其他游戏类型的等待中房间
            roomId = findRandomUnstartedGameRoom(uid, SudGameConstant.VOICE_ROOM);
            if (!StringUtils.isEmpty(roomId)) {
                return roomId;
            }

        } catch (Exception e) {
            logger.error("findLuckyWheelGameRoom uid={}, error={}", uid, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据状态查找Lucky Wheel游戏房间
     */
    private String findLuckyWheelByStatus(List<TurntableGameInfo> games, String uid, int status) {
        for (TurntableGameInfo gameInfo : games) {
            if (gameInfo.getStatus() == status) {
                if (isRoomSuitable(gameInfo.getRoomId(), uid)) {
                    return gameInfo.getRoomId();
                }
            }
        }
        return null;
    }
}

