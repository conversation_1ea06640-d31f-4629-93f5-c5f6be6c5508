package com.quhong.service;

import com.quhong.cache.CacheMap;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Lazy
@Service
public class WearResourcesService {
    private static final Logger logger = LoggerFactory.getLogger(WearResourcesService.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private BubbleDao bubbleDao;
    @Resource
    private MicFrameDao micFrameDao;
    @Resource
    private ExtraMicFrameDao extraMicFrameDao;
    @Resource
    private VipMicFrameDao vipMicFrameDao;
    @Resource
    private JoinCartonDao joinCartonDao;
    @Resource
    private RippleDao rippleDao;
    @Resource
    private FloatScreenDao floatScreenDao;

    private final CacheMap<String, Map<String, String>> uidResCacheMap;
    private final CacheMap<String, Integer> uidRefreshCacheMap;
    private static final int SEVEN_DAYS = 7;


    public WearResourcesService() {
        uidResCacheMap = new CacheMap<>(5 * 60 * 1000L);
        uidRefreshCacheMap = new CacheMap<>(24 * 60 * 60 * 1000L);
    }

    @PostConstruct
    public void postInit() {
        uidResCacheMap.start();
        uidRefreshCacheMap.start();
    }


    private Map<String, String> reSetAllResources(String uid) {
        Map<String, String> map = new HashMap<>();
        map.put(String.valueOf(BaseDataResourcesConstant.TYPE_MIC)
                , String.valueOf(getWearResIdDb(uid, BaseDataResourcesConstant.TYPE_MIC)));
        map.put(String.valueOf(BaseDataResourcesConstant.TYPE_RIDE)
                , String.valueOf(getWearResIdDb(uid, BaseDataResourcesConstant.TYPE_RIDE)));
        map.put(String.valueOf(BaseDataResourcesConstant.TYPE_BUDDLE)
                , String.valueOf(getWearResIdDb(uid, BaseDataResourcesConstant.TYPE_BUDDLE)));
        map.put(String.valueOf(BaseDataResourcesConstant.TYPE_RIPPLE)
                , String.valueOf(getWearResIdDb(uid, BaseDataResourcesConstant.TYPE_RIPPLE)));
        map.put(String.valueOf(BaseDataResourcesConstant.TYPE_FLOAT_SCREEN)
                , String.valueOf(getWearResIdDb(uid, BaseDataResourcesConstant.TYPE_FLOAT_SCREEN)));
        clusterRedis.opsForHash().putAll(getResourcesKey(uid), map);
        clusterRedis.expire(getResourcesKey(uid), SEVEN_DAYS, TimeUnit.DAYS);
        return map;
    }


    private int getWearResIdDb(String uid, int resType) {
        int wearId = BaseDataResourcesConstant.COMMON_DEFAULT_ID;
        switch (resType) {
            case BaseDataResourcesConstant.TYPE_MIC:
                MicFrameData oldMicFrameData = micFrameDao.findDataByStatus(uid, 1);
                if (oldMicFrameData != null) {
                    wearId = oldMicFrameData.getMic_id();
                } else {
                    ExtraMicFrameData oldExtraMicFrameData = extraMicFrameDao.findDataByStatus(uid, 1);
                    if (oldExtraMicFrameData != null) {
                        wearId = oldExtraMicFrameData.getMic_id();
                    } else {
                        VipMicFrameData oldVipMicFrameData = vipMicFrameDao.findDataByStatus(uid, 1);
                        if (oldVipMicFrameData != null) {
                            wearId = oldVipMicFrameData.getMic_id();
                        }
                    }
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIDE:
                JoinCartonData joinCartonData = joinCartonDao.findData(uid, 1);
                if (joinCartonData != null) {
                    wearId = joinCartonData.getJoin_carton_id();
                }
                break;
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                BubbleData wearData = bubbleDao.findDataByStatus(uid, 1);
                if (wearData != null) {
                    wearId = wearData.getBuddle_id();
                } else {
                    wearId = BaseDataResourcesConstant.DEFAULT_BUDDLE_ID;
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                RippleData rippleData = rippleDao.findDataByStatus(uid, 1);
                if (rippleData != null) {
                    wearId = rippleData.getRipple_id();
                } else {
                    wearId = BaseDataResourcesConstant.DEFAULT_RIPPLE_ID;
                }
                break;
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN:
                FloatScreenData floatScreenData = floatScreenDao.findDataByStatus(uid, 1);
                if (floatScreenData != null) {
                    wearId = floatScreenData.getScreen_id();
                }
                break;
            default:
                break;
        }
        return wearId;
    }

    /**
     * @param uid
     * @param resType
     * @return
     * @see BaseDataResourcesConstant type仅支持 mic,ride,buddle,ripple,float_screen
     */
    public int getWearResId(String uid, int resType) {
        try {
            String valueStr = (String) clusterRedis.opsForHash().get(getResourcesKey(uid), String.valueOf(resType));
            Integer resId =  !StringUtils.isEmpty(valueStr) ? Integer.parseInt(valueStr) : null;
            if (resId == null) {
                if (Boolean.FALSE.equals(clusterRedis.hasKey(getResourcesKey(uid)))) {
                    Map<String, String> resMap = reSetAllResources(uid);
                    resId = Integer.parseInt(resMap.get(String.valueOf(resType)));
                } else {
                    resId = getWearResIdDb(uid, resType);
                    clusterRedis.opsForHash().put(getResourcesKey(uid), String.valueOf(resType), String.valueOf(resId));
                }
            }
            if (!uidRefreshCacheMap.hasData(uid)) {
                uidRefreshCacheMap.cacheData(uid, 1);
                clusterRedis.expire(getResourcesKey(uid), SEVEN_DAYS, TimeUnit.DAYS);
            }
            return resId;
        } catch (Exception e) {
            logger.error("getWearResId error uid={} ,resType={} msg={}", uid, resType, e.getMessage(), e);
            return 0;
        }
    }

    public int getWearResIdFromCache(String uid, int resType) {
        Map<String, String> resMap = getAllWearResIdFromCache(uid);
        return Integer.parseInt(resMap.getOrDefault(String.valueOf(resType), "0"));
    }

    public Map<String, String> getAllWearResIdFromCache(String uid) {
        Map<String, String> resMap = uidResCacheMap.getData(uid);
        if (CollectionUtils.isEmpty(resMap)) {
            resMap = getAllWearResId(uid);
            uidResCacheMap.cacheData(uid, resMap);
        }
        return resMap;
    }

    /**
     * * @see BaseDataResourcesConstant type仅支持 mic,ride,buddle,ripple,float_screen
     *
     * @param uid
     * @return
     */
    public Map<String, String> getAllWearResId(String uid) {
        Map<String, String> result = new HashMap<>();
        try {
            Map<Object, Object> allRes = clusterRedis.opsForHash().entries(getResourcesKey(uid));
            if (!CollectionUtils.isEmpty(allRes)) {
                for (Map.Entry<Object, Object> entry : allRes.entrySet()) {
                    try {
                        result.put(String.valueOf(entry.getKey()), String.valueOf(entry.getValue()));
                    } catch (Exception e) {
                        logger.error("getAllWearResId error. {}", e.getMessage(), e);
                    }
                }
            } else {
                result = reSetAllResources(uid);
            }
            return result;
        } catch (Exception e) {
            logger.error("getAllWearResId error {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }


    public void setWearResIdByDb(String uid, int resType) {
        int wearId = getWearResIdDb(uid, resType);
        setWearResIdById(uid, resType, wearId);
    }

    public void setWearResIdById(String uid, int resType, int wearId) {
        clusterRedis.opsForHash().put(getResourcesKey(uid), String.valueOf(resType), String.valueOf(wearId));
    }

    public void deleteWearResIdByType(String uid, int resType) {

        switch (resType) {
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                clusterRedis.opsForHash().put(getResourcesKey(uid), String.valueOf(resType), String.valueOf(BaseDataResourcesConstant.DEFAULT_BUDDLE_ID));
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                clusterRedis.opsForHash().put(getResourcesKey(uid), String.valueOf(resType), String.valueOf(BaseDataResourcesConstant.DEFAULT_RIPPLE_ID));
                break;
            default:
                clusterRedis.opsForHash().put(getResourcesKey(uid), String.valueOf(resType), String.valueOf(BaseDataResourcesConstant.COMMON_DEFAULT_ID));
                break;
        }

    }

    private String getResourcesKey(String uid) {
        return String.format("hash:OwnerResources:%s", uid);
    }
}
