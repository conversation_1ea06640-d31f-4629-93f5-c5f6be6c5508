package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.room.RoomWebSender;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Lazy
@Service
public class OfficialMsgService {

    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private RoomWebSender roomWebSender;

    /**
     * 官方通知
     */
    public void officialMsgPush(String uid, String title, String noticeBody, int atype) {
        OfficialData officialData = new OfficialData(title, noticeBody, uid);
        officialData.setAtype(atype);
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg(null, null, uid, msg, true);
        }
    }

    public void officialMsgPush(OfficialData officialData) {
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            NoticeNewData noticeNewData = new NoticeNewData();
            noticeNewData.setOfficial_id(officialData.get_id().toString());
            noticeNewData.setAid(officialData.getTo_uid());
            noticeNewData.setNtype(officialData.getNtype());
            noticeNewData.setStatus(1);
            noticeNewData.setCtime(DateHelper.getNowSeconds());
            noticeNewDao.save(noticeNewData);

            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            msg.setMsg_type(officialData.getNtype());
            roomWebSender.sendPlayerWebMsg(null, null, officialData.getTo_uid(), msg, true);
        }
    }

    /**
     * 删除官方消息
     * @param officialMsgId 消息id
     * @return void
     */
    public void deleteMsg(String officialMsgId) {
        officialDao.deleteMsg(officialMsgId);
    }
}
