package com.quhong.service.redis;

import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 付费redis
 */
@Component
@Lazy
public class RechargeRedis {
    private static final Logger logger = LoggerFactory.getLogger(RechargeRedis.class);
    private static final long CACHE_TIME_MILLIS = 60 * 60 * 1000L;
    private static final int ALL = 1;
    private final CacheMap<Integer, Set<String>> cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    private long date;
    private long chatDate;

    /**
     * 添加到最近登录充值列表
     *
     * @param uid
     */
    public void addToOnlineRechargeUser(String uid) {
        redisTemplate.opsForZSet().add(getLatestOnlineRechargeUserKey(), uid, DateHelper.getNowSeconds());
        if (date == 0 || DateHelper.getNowSeconds() - date > 86400) {
            redisTemplate.expire(getLatestOnlineRechargeUserKey(), 2, TimeUnit.DAYS);
            long time = DateHelper.UTC.getDayOffset(-90) / 1000;
            redisTemplate.opsForZSet().removeRangeByScore(getLatestOnlineRechargeUserKey(), 0, time);
            date = DateHelper.getNowSeconds();
        }
    }

    /**
     * 获取最近充值列表
     *
     * @param size
     * @return
     */
    public Set<ZSetOperations.TypedTuple<String>> getLatestOnlineUser(int size) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(getLatestOnlineRechargeUserKey(), 0, size);
    }

    private String getLatestOnlineRechargeUserKey() {
        return "zset:online:recharge:user";
    }

    /**
     * 添加到最近聊天user
     *
     * @param uid
     */
    public void addToLatestChatRechargeUser(String uid) {
        redisTemplate.opsForZSet().add(getLastChatRechargeUserKey(), uid, DateHelper.getNowSeconds());
        if (chatDate == 0 || DateHelper.getNowSeconds() - chatDate > 86400) {
            redisTemplate.expire(getLastChatRechargeUserKey(), 2, TimeUnit.DAYS);
            long time = DateHelper.UTC.getDayOffset(-90) / 1000;
            redisTemplate.opsForZSet().removeRangeByScore(getLastChatRechargeUserKey(), 0, time);
            chatDate = DateHelper.getNowSeconds();
        }
    }

    /**
     * 获取最近聊天列表
     *
     * @param size
     * @return
     */
    public Set<ZSetOperations.TypedTuple<String>> getLatestChatUser(int size) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(getLastChatRechargeUserKey(), 0, size);
    }

    private String getLastChatRechargeUserKey() {
        return "zset:chat:recharge:user";
    }


    public boolean isRechargeUserByCache(String uid) {
        return getAllRechargeUserFromCache().contains(uid);
    }

    public Set<String> getAllRechargeUserFromCache() {
        if (cacheMap.hasData(ALL)) {
            return cacheMap.getData(ALL);
        }
        return getAllRechargeUser();
    }

    public synchronized Set<String> getAllRechargeUser() {
        try {
            if (cacheMap.hasData(ALL)) {
                return cacheMap.getData(ALL);
            }
            Set<String> partyGirlMap = redisTemplate.opsForSet().members(getRechargeSetKey());
            if (!CollectionUtils.isEmpty(partyGirlMap)) {
                cacheMap.cacheData(ALL, partyGirlMap);
                return partyGirlMap;
            }
        } catch (Exception e) {
            logger.error("getAllRechargeUser error.", e);
        }
        return new HashSet<>();
    }

    /**
     * 是否付费用户
     *
     * @param uid
     * @return
     */
    public boolean isRechargeUser(String uid) {
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getRechargeSetKey(), uid));
    }

    private String getRechargeSetKey() {
        return "set:honor_recharge";
    }

    public void addRechargeUser(String uid) {
        try {
            redisTemplate.opsForSet().add(getRechargeSetKey(), uid);
        } catch (Exception e) {
            logger.error("addRechargeUser error.", e);
        }
    }


    private String getRechargeOfferKey() {
        return "str:recharge_discount";
    }

    public int hasRechargeOffer() {

        String hasRecharge = redisTemplate.opsForValue().get(getRechargeOfferKey());
        if (StringUtils.isEmpty(hasRecharge)) {
            return 0;
        } else {
            return 1;
        }
    }
}
