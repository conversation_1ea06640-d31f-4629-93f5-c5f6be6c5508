package com.quhong.service.redis;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

public abstract class ServiceRedis {
    private static final Logger logger = LoggerFactory.getLogger(ServiceRedis.class);

    protected static final int EXPIRE_DAY = 2;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    protected StringRedisTemplate redisTemplate;

    protected int doIncVersion(String key){
        try{
            long value = redisTemplate.opsForValue().increment(key);
            if(value > Integer.MAX_VALUE - 16){
                value = 0;
                redisTemplate.opsForValue().set(key, value + "");
            }
            redisTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
            return (int)value;
        }catch (Exception e){
            logger.error("music play list. inc version error. {}", e.getMessage(), e);
        }
        return 0;
    }

    protected int doGetVersion(String key){
        try{
            String value = redisTemplate.opsForValue().get(key);
            if(StringUtils.isEmpty(value)){
                return 0;
            }
            return Integer.parseInt(value);
        }catch (Exception e){
            logger.error("music play list. get version error. {}", e.getMessage(), e);
        }
        return 0;
    }
}
