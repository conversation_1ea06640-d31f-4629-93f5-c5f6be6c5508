package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.BadgeListDTO;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.mongo.dao.BadgeDao;
import com.quhong.mongo.dao.BadgeListDao;
import com.quhong.mongo.data.BadgeData;
import com.quhong.mongo.data.BadgeListData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.AchievementBadgeDao;
import com.quhong.mysql.data.AchievementBadgeData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Lazy
@Service
public class BadgeService {
    private static final Logger logger = LoggerFactory.getLogger(BadgeService.class);
    private static final String ACHIEVE_DESC = "AchieveBadge";
    private static final String LEVEL_DESC = "LevelBadge";

    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private AchievementBadgeDao achievementBadgeDao;
    @Resource
    private BadgeListDao badgeListDao;
    @Resource
    private BadgeDao badgeDao;


    /**
     * 根据数量获取等级，返回值范围[0,list.Size()]
     */
    public int getLevelFromSoredList(List<Integer> list, int num) {
        if (CollectionUtils.isEmpty(list) || num <= 0) {
            return 0;
        }
        for (int i = 0; i < list.size(); i++) {
            int listValue = list.get(i);
            if (listValue > num) {
                return i;
            }
            if (listValue == num) {
                return i + 1;
            }
        }
        return list.size();
    }

    /**
     * 根据数量获取等级，返回值范围[0,list.Size()]
     */
    public int getLevelFromSoredList(List<Long> list, long num) {
        if (CollectionUtils.isEmpty(list) || num <= 0) {
            return 0;
        }
        for (int i = 0; i < list.size(); i++) {
            long listValue = list.get(i);
            if (listValue > num) {
                return i;
            }
            if (listValue == num) {
                return i + 1;
            }
        }
        return list.size();
    }

    public void doGiftBadge(String aid, int giftId, int num, int oldNum,
                            Map<Integer, List<Integer>> giftNumMap, Map<Integer, List<Integer>> badgeIdMap) {
        if (!giftNumMap.containsKey(giftId) || !badgeIdMap.containsKey(giftId)) {
            return;
        }
        doGiftBadge(aid, num, oldNum, badgeIdMap.get(giftId), giftNumMap.get(giftId));
    }

    public void doGiftBadge(String aid, int num, int oldNum, List<Integer> badgeIdList, List<Integer> giftNumList) {
        int levelNow = getLevelFromSoredList(giftNumList, oldNum);
        if (levelNow >= giftNumList.size()) {
            return;
        }
        int levelUpNum = giftNumList.get(levelNow);
        if (levelUpNum > 0 && num >= levelUpNum) {
            int levelNew = getLevelFromSoredList(giftNumList, num);
            logger.info("badge level up, uid={} levelNow={} levelNew={} num={} oldNum={} levelUpNum={}", aid, levelNow, levelNew, num, oldNum, levelUpNum);
            if (levelNow > 0) {
                BadgeData badgeData = badgeDao.getBadgeData(aid, badgeIdList.get(levelNow - 1));
                if (null != badgeData) {
                    badgeDao.deleteBadge(badgeData);
                }
            }
            // 下标从0开始
            giveBadgeToUser(aid, badgeIdList.get(levelNew - 1), num);
        }
    }

    /**
     * 下发等级勋章，每次判断勋章是否存在，不存在即为升级
     */
    public void doGiftBadge(String aid, int num, List<Integer> badgeIdList, List<Integer> giftNumList) {
        int levelNew = getLevelFromSoredList(giftNumList, num);
        BadgeData oldBadgeData = badgeDao.getBadgeData(aid, badgeIdList.get(levelNew - 1));
        if (null == oldBadgeData) {
            logger.info("badge level up, uid={} levelNew={} num={}", aid, levelNew, num);
            badgeDao.removeBadges(aid, badgeIdList);
            // 下标从0开始
            giveBadgeToUser(aid, badgeIdList.get(levelNew - 1), num);
        }
    }

    public void giveBadgeToUser(String aid, int badgeId, int num) {
        logger.info("giveBadgeToUser uid={} badgeId={} num={}", aid, badgeId, num);
        BadgeListData badgeListData = badgeListDao.findData(badgeId);
        if (null == badgeListData) {
            logger.error("badge not exists, uid={} badgeId={}", aid, badgeId);
            return;
        }
        BadgeData oldBadgeData = badgeDao.getBadgeData(aid, badgeId);
        boolean newBadge = false;
        if (null == oldBadgeData) {
            List<BadgeData> wearBadgeList = badgeDao.getWearBadgeList(aid);
            List<Integer> badgeStatus = new ArrayList<>();
            for (BadgeData badgeData : wearBadgeList) {
                badgeStatus.add(badgeData.getStatus());
            }
            int st = 0;
            if (badgeStatus.size() != 3) {
                for (int status = 1; status <= 3; status++) {
                    if (!badgeStatus.contains(status)) {
                        st = status;
                        break;
                    }
                }
            }
//            BadgeData badgeData = new BadgeData();
//            badgeData.setUid(aid);
//            badgeData.setBadge_id(badgeId);
//            badgeData.setStatus(st);
//            badgeData.setGet_time(DateHelper.getNowSeconds());
//            // 1767196800表示永久
//            badgeData.setEnd_time(Integer.MAX_VALUE);
//            badgeDao.save(badgeData);
            sendReward(aid, badgeId, BaseDataResourcesConstant.ACTION_GET_WEAR,LEVEL_DESC,0);
            newBadge = true;

        }
        logger.info("change badge success, uid={} badgeId={} newBadge={}", aid, badgeId, newBadge);
    }

    /**
     * 下发成就勋章
     *
     * @param uid       用户uid
     * @param badgeType 下发勋章类型
     * @param getNum    获得的数量
     */
    public void doAchieveBadge(String uid, int badgeType, long countNum, long getNum) {
        try {

            List<AchievementBadgeData> achieveBadgeDataList = achievementBadgeDao.getAchievementFromCache(badgeType);

            List<Long> countNumList = achieveBadgeDataList.stream().map(AchievementBadgeData::getCountNum).distinct().collect(Collectors.toList());
            Map<Long, AchievementBadgeData> achieveBadgeMap = achieveBadgeDataList.stream().collect(Collectors.toMap(AchievementBadgeData::getCountNum, Function.identity()));

            // 发送礼物前的等级

            int levelNow = getLevelFromSoredList(countNumList, countNum - getNum);

            logger.info("doAchieveBadge1 countNumList: {}, achieveBadgeMap: {}, levelNow:{}", countNumList, achieveBadgeMap, levelNow);
            if (levelNow >= countNumList.size()) {
                return;
            }

            // 下一等级所需数量
            long levelUpNum = countNumList.get(levelNow);

            logger.info("doAchieveBadge2 levelUpNum: {}", levelUpNum);
            if (levelUpNum > 0 && countNum >= levelUpNum) {
                int levelNew = getLevelFromSoredList(countNumList, countNum);
                AchievementBadgeData achievementBadgeData = achieveBadgeDataList.get(levelNew - 1);
                if (achievementBadgeData != null) {
                    long lastCountNum = achievementBadgeData.getLastCountNum();
                    AchievementBadgeData lastBadgeInfo = achieveBadgeMap.get(lastCountNum);
                    if (lastBadgeInfo != null) {
                        int lastBadgeId = lastBadgeInfo.getBadgeId();
                        BadgeData lastBadgeData = badgeDao.getBadgeData(uid, lastBadgeId);
                        if (lastBadgeData != null && lastBadgeData.getStatus() > 0) {
                            lastBadgeData.setStatus(0);
                            badgeDao.upsert(lastBadgeData);
                        }
                    }
                    int currentBadgeId = achievementBadgeData.getBadgeId();
                    sendReward(uid, currentBadgeId, BaseDataResourcesConstant.ACTION_GET_WEAR);
                }
                logger.info("doAchieveBadge3 levelNew: {}, achievementBadgeData: {}", levelNew, achievementBadgeData);
            }
        } catch (Exception e) {
            logger.error("doAchieveBadge error uid: {}, badgeType:{}, countNum:{}, getNum:{}, message:{}", uid, badgeType, countNum, getNum, e.getMessage(), e);
        }
    }

    /**
     * 补发成就勋章
     *
     * @param uid       用户uid
     * @param badgeType 下发勋章类型
     * @param countNum  当前数量
     */
    public void supplyAchieveBadge(String uid, int badgeType, long countNum) {
        try {
            List<AchievementBadgeData> achieveBadgeDataList = achievementBadgeDao.getAchievementFromCache(badgeType);
            for (AchievementBadgeData badgeData : achieveBadgeDataList) {
                if (countNum >= badgeData.getCountNum()) {
                    sendReward(uid, badgeData.getBadgeId(), BaseDataResourcesConstant.ACTION_GET);
                }
            }
        } catch (Exception e) {
            logger.error("supplyAchieveBadge error uid: {}, badgeType:{}, countNum:{}, getNum:{}, message:{}", uid, badgeType, countNum, e.getMessage(), e);
        }
    }

    /**
     * 降级删除成就勋章
     *
     * @param uid       用户uid
     * @param badgeType 移除勋章类型
     * @param countNum  已减数量之后的统计数量
     * @param reduceNum 减少的数量
     */
    public void deleteAchieveBadge(String uid, int badgeType, int countNum, int reduceNum) {
        try {
            List<AchievementBadgeData> achieveBadgeDataList = achievementBadgeDao.getAchievementFromCache(badgeType);
            List<Long> countNumList = achieveBadgeDataList.stream().map(AchievementBadgeData::getCountNum).distinct().collect(Collectors.toList());
            Map<Long, AchievementBadgeData> achieveBadgeMap = achieveBadgeDataList.stream().collect(Collectors.toMap(AchievementBadgeData::getCountNum, Function.identity()));

            // 删除好友前的等级
            int levelNow = getLevelFromSoredList(countNumList, countNum + reduceNum);
            if (levelNow <= 0) {
                return;
            }

            // 上一等级所需数量
            long levelDownNum = countNumList.get(levelNow - 1);
            if (levelDownNum > 0 && countNum < levelDownNum) {
                int levelNew = getLevelFromSoredList(countNumList, countNum);
                AchievementBadgeData achievementBadgeData = achieveBadgeDataList.get(levelNew);
                if (achievementBadgeData != null) {
                    int currentBadgeId = achievementBadgeData.getBadgeId();
                    BadgeData badgeData = badgeDao.getBadgeData(uid, currentBadgeId);
                    if (badgeData != null) {
                        sendReward(uid, currentBadgeId, BaseDataResourcesConstant.ACTION_DELETE);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("deleteAchieveBadge error uid: {}, badgeType:{}, countNum:{}, getNum:{}, message:{}", uid, badgeType, countNum, reduceNum, e.getMessage(), e);
        }
    }

    private void sendReward(String uid, int badgeId, int badgeAction) {
        sendReward(uid, badgeId, badgeAction, ACHIEVE_DESC,1);
    }

    public void sendReward(String uid, int badgeId, int badgeAction, String desc,int officialMsg) {
        logger.info("send msg to mq. toUid={}, badgeId={}", uid, badgeId);
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(uid);
        resourcesDTO.setResId(String.valueOf(badgeId));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_BADGE);
        resourcesDTO.setItemsSourceDetail(desc);
        resourcesDTO.setDesc(desc);
        resourcesDTO.setActionType(badgeAction);
        resourcesDTO.setOfficialMsg(officialMsg);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(-1);
        mqSenderService.asyncHandleResources(resourcesDTO);
    }


    /**
     * 构建BadgeListDTO
     */
    public BadgeListDTO buildBadgeListDTO(Integer slang, BadgeListData data) {
        BadgeListDTO badgeListDTO = new BadgeListDTO();
        badgeListDTO.setBid(data.getBadge_id());
        badgeListDTO.setIcon(data.getIcon());
        badgeListDTO.setSmallIcon(data.getSmall_icon());
        badgeListDTO.setReleaseTime(data.get_id().getTimestamp());
        badgeListDTO.setBadgeType(data.getBadge_type());
        badgeListDTO.setName(slang == 2 ? data.getAr_name() : data.getName());
        badgeListDTO.setDesc(slang == 2 ? data.getAr_desc() : data.getDesc());
        badgeListDTO.setSvgaIcon(StringUtils.isEmpty(data.getSvga_icon()) ? "" : data.getSvga_icon());
        badgeListDTO.setHonorLevel(data.getHonor_level());
        badgeListDTO.setSubBadgeOrder(data.getSub_badge_order());
        badgeListDTO.setpBadgeId(data.getP_badge_id());
        badgeListDTO.setAchieveType(data.getAchieveType());
        badgeListDTO.setCondition(data.getCondition());
        return badgeListDTO;
    }

    public String getBadgeGetTime(Integer timeSeconds) {
        LocalDateTime time = LocalDateTime.ofEpochSecond(timeSeconds, 0, ZoneOffset.ofHours(3));
        return time.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
    }

}
