package com.quhong.service;

import com.quhong.constant.VipFeatureConstant;
import com.quhong.data.ActorData;
import com.quhong.data.vo.ActorRoomStatusVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BlackListDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.IdentifyRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Lazy
@Component
public class ActorCommonService {

    private static final Logger logger = LoggerFactory.getLogger(ActorCommonService.class);
    public static final Map<Integer, String> OLD_VIP_MEDAL_MAP = new HashMap<>();
    public static final Map<Integer, String> NEW_VIP_MEDAL_MAP = new HashMap<>();
    static {
        OLD_VIP_MEDAL_MAP.put(1, "https://cdn3.qmovies.tv/youstar/op_1755570867_VIP1.png");
        OLD_VIP_MEDAL_MAP.put(2, "https://cdn3.qmovies.tv/youstar/op_1755570867_VIP2.png");
        OLD_VIP_MEDAL_MAP.put(3, "https://cdn3.qmovies.tv/youstar/op_1755570867_VIP3.png");
        OLD_VIP_MEDAL_MAP.put(4, "https://cdn3.qmovies.tv/youstar/op_1755570867_VIP4.png");
        OLD_VIP_MEDAL_MAP.put(5, "https://cdn3.qmovies.tv/youstar/op_1755570868_VIP5.png");
        OLD_VIP_MEDAL_MAP.put(6, "https://cdn3.qmovies.tv/youstar/op_1755570868_VIP6.png");
        OLD_VIP_MEDAL_MAP.put(10, "https://cdn3.qmovies.tv/youstar/op_1755570867_VIP_QUEEN.png");

        NEW_VIP_MEDAL_MAP.put(1, "https://cloudcdn.qmovies.tv/vipResource/op_1756261073_vip1.png");
        NEW_VIP_MEDAL_MAP.put(2, "https://cloudcdn.qmovies.tv/vipResource/op_1756261089_vip2.png");
        NEW_VIP_MEDAL_MAP.put(3, "https://cloudcdn.qmovies.tv/vipResource/op_1756261104_vip3.png");
        NEW_VIP_MEDAL_MAP.put(4, "https://cloudcdn.qmovies.tv/vipResource/op_1756261137_vip4.png");
        NEW_VIP_MEDAL_MAP.put(5, "https://cloudcdn.qmovies.tv/vipResource/op_1756261176_vip5.png");
        NEW_VIP_MEDAL_MAP.put(6, "https://cloudcdn.qmovies.tv/vipResource/op_1756261208_vip6.png");
        NEW_VIP_MEDAL_MAP.put(10, "https://cloudcdn.qmovies.tv/vipResource/op_1756261242_queen.png");
    }

    @Resource
    private ActorDao actorDao;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private IdentifyRedis identifyRedis;

    /**
     * uid查看aid在房间状态
     * @return vo
     */
    public ActorRoomStatusVO getActorRoomStatus(String uid, String aid){
        ActorData actorDataRds = actorDao.getActorData(aid);
        return getActorRoomStatus(uid, aid, actorDataRds.getAccept_talk());
    }

    public ActorRoomStatusVO getActorRoomStatus(String uid, String aid, int acceptTalk){
        try {
            ActorRoomStatusVO vo = new ActorRoomStatusVO();
            if(acceptTalk != 1 || blackListDao.isBlock(aid, uid) || roomBlacklistDao.isBlock(RoomUtils.formatRoomId(aid), uid)){
                return null;
            }
            String roomId = roomPlayerRedis.getActorRoomStatus(aid);
            if(StringUtils.isEmpty(roomId) || RoomUtils.isGameRoom(roomId)){
                return null;
            }
            if (whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                return null;
            }

            MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
            if (mongoRoomData == null) {
                return null;
            }
            vo.setInRoomId(roomId);
            vo.setInRoomName(mongoRoomData.getName());
            vo.setInRoomHead(ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead()));
            return vo;
        }catch (Exception e){
            logger.error("getActorRoomStatus error {}", e.getMessage(), e);
        }
        return null;
    }

    public int getRejectGreetStatus(String uid){
        int defaultStatus = ActorUtils.isNewRegisterActor(uid, 30) ? 0 : 1;
        return (int) actorConfigDao.getLongUserConfig(uid, ActorConfigDao.REJECT_GREET, defaultStatus);
    }


    public int getVipVersion(String uid){
        Integer vipVersion = identifyRedis.getVipVersionCache(uid);
        if (vipVersion != null) {
            return vipVersion;
        }
        vipVersion = (int) actorConfigDao.getLongUserConfig(uid, ActorConfigDao.VIP_VERSION, 1);
        identifyRedis.setVipVersionCache(uid, vipVersion);
        return vipVersion;
    }

    public String getCommonVipMedal(String uid, Integer vipLevel){
        if (vipLevel == null || vipLevel <= 0) {
            return null;
        }
        int vipVersion = getVipVersion(uid);
        return vipVersion == VipFeatureConstant.VIP_VERSION_0 ? OLD_VIP_MEDAL_MAP.get(vipLevel) : NEW_VIP_MEDAL_MAP.get(vipLevel);
    }
}
