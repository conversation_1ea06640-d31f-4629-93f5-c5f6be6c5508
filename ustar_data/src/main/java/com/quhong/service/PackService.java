package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.ResTypeEnum;
import com.quhong.mongo.dao.PackConfigDao;
import com.quhong.mongo.data.PackConfigData;
import com.quhong.mongo.data.PackData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.HeartRecordDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

@Lazy
@Service
public class PackService {

    public final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private PackConfigDao packConfigDao;

    /**
     * 发送礼包,支持下发钻石,不带消息
     *
     * @param aid     下发用户
     * @param packKey 资源key
     * @param getWay  获得途径
     */
    public boolean sendPackage(String aid, String packKey, int aType, String title, String desc, int getWay) {
        PackConfigData packConfigData = packConfigDao.findByKey(packKey);
        if (null == packConfigData || CollectionUtils.isEmpty(packConfigData.getPackList())) {
            logger.error("cannot find pack config. packKey={}", packKey);
            return false;
        }
        return sendPackage(aid, aType, title, desc, getWay, packConfigData);
    }

    /**
     * 发送礼包,支持下发钻石,不带消息
     *
     * @param aid     下发用户
     * @param getWay  获得途径
     */
    public boolean sendPackage(String aid, int aType, String title, String desc, int getWay, PackConfigData packConfigData) {
        for (PackData packData : packConfigData.getPackList()) {
            if (packData.getResType() == ResTypeEnum.COIN.getType()) {
                heartRecordDao.changeHeart(aid, packData.getNum(), title, desc);
            } else if (packData.getResType() == ResTypeEnum.DIAMONDS.getType()) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setRoomId("");
                moneyDetailReq.setUid(aid);
                moneyDetailReq.setAtype(aType);
                moneyDetailReq.setChanged(packData.getNum());
                moneyDetailReq.setTitle(title);
                moneyDetailReq.setDesc(desc);
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            } else {
                mqSenderService.asyncHandleResources(getResourcesDTO(aid, packData, 2, getWay));
            }
        }
        return true;
    }

    private ResourcesDTO getResourcesDTO(String aid, PackData packData, int actionType, int getWay) {
        ResourcesDTO dto = new ResourcesDTO();
        dto.setUid(aid);
        dto.setResId(String.valueOf(packData.getResId()));
        dto.setResType(packData.getResType());
        if (BaseDataResourcesConstant.TYPE_BAG_GIFT == packData.getResType()) {
            dto.setNum(packData.getNum());
            dto.setDays(BaseDataResourcesConstant.FOREVER_DAY);
        } else {
            dto.setDays(packData.getNum());
        }
        dto.setGetWay(getWay);
        dto.setActionType(actionType);
        dto.setmTime(DateHelper.getNowSeconds());
        // 自然天过期
        dto.setGainType(0);
        return dto;
    }
}
