package com.quhong.service.data;

import com.quhong.core.utils.DateHelper;

import java.util.concurrent.TimeUnit;

/**
 * 存在hash中的字段
 */
public class BaseRedisHashData {
    protected static final long EXPIRE_SECONDS = TimeUnit.DAYS.toSeconds(5);

    private long updateTime; // 时间戳，单位秒

    public BaseRedisHashData(){
        update();
    }

    /**
     * 更新当前时间
     */
    public void update(){
        this.updateTime = DateHelper.getNowSeconds();
    }

    public boolean expire(){
        long curTime = DateHelper.getNowSeconds();
        return curTime - updateTime > EXPIRE_SECONDS;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }
}
