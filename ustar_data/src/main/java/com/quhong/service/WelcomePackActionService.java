package com.quhong.service;


import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.vo.WelcomePackReceiverVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ResTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.PackData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.WelcomePackMsg;
import com.quhong.msg.obj.PackInfoObject;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.WelcomePackConfigDao;
import com.quhong.mysql.dao.WelcomePackLogDao;
import com.quhong.mysql.data.WelcomePackConfigData;
import com.quhong.mysql.data.WelcomePackLogData;
import com.quhong.room.RoomWebSender;
import com.quhong.vo.PageVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 礼包发送服务
 *
 * <AUTHOR>
 * @date 2025/9/4 11:47
 */
@Service
public class WelcomePackActionService {
    private static final Logger logger = LoggerFactory.getLogger(WelcomePackActionService.class);

    // 永久
    private static final String PERMANENT = "Permanent";
    private static final String PERMANENT_AR = "دائم";

    // 发送title
    public static final String SEND_TITLE = "Delivery gift package";
    // 撤回title
    public static final String WITHDRAW_TITLE = "Return Delivery gift package";

    public static final int SEND_ATYPE = 1009;
    public static final int WITHDRAW_ATYPE = 1010;

    // 操作来源 admin 和operation
    public static final int ADMIN_SOURCE = 1;
    public static final int OPERATION_SOURCE = 2;

    @Resource
    private ActorDao actorDao;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private WelcomePackLogDao welcomePackLogDao;
    @Resource
    private WelcomePackConfigDao welcomePackConfigDao;

    /**
     * 发送欢迎礼包
     *
     * @param aidSet  发送列表
     * @param packId  礼包id
     * @param account 操作用户名称
     * @param role    操作用户权限等级
     * @param source  操作来源（1-admin 2-operation）
     */
    public synchronized void sendWelcomePack(Set<String> aidSet, Integer packId, String account, int role, int source) {
        if (CollectionUtils.isEmpty(aidSet)) {
            throw new CommonH5Exception(new HttpCode(1, "Please enter the user rid to deliver the gift package and verify before sending it"));
        }
        WelcomePackConfigData config = welcomePackConfigDao.getById(packId);
        if (null == config) {
            throw new CommonH5Exception(new HttpCode(1, "Gift package not found, please refresh or check if the gift package has been deleted"));
        }
        if (OPERATION_SOURCE == source && !config.getOperationRoleLevel().contains(role)) {
            throw new CommonH5Exception(new HttpCode(1, "Your permission level is not within the scope of the gift package permission level"));
        }
        if (ADMIN_SOURCE == source && !config.getAdminRoleLevel().contains(role)) {
            throw new CommonH5Exception(new HttpCode(1, "Your permission level is not within the scope of the gift package permission level"));
        }
        if (CollectionUtils.isEmpty(config.getPackList())) {
            logger.info("send welcome pack. pack is empty. packId={}", packId);
            throw new CommonH5Exception(new HttpCode(1, "Gift package reward is empty"));
        }
        for (String aid : aidSet) {
            for (PackData packData : config.getPackList()) {
                if (packData.getResType() == BaseDataResourcesConstant.TYPE_COIN) {
                    heartRecordDao.changeHeart(aid, packData.getNum(), SEND_TITLE, SEND_TITLE);
                } else if (packData.getResType() == BaseDataResourcesConstant.TYPE_DIAMOND) {
                    MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                    moneyDetailReq.setRandomId();
                    moneyDetailReq.setRoomId("");
                    moneyDetailReq.setUid(aid);
                    moneyDetailReq.setAtype(SEND_ATYPE);
                    moneyDetailReq.setChanged(packData.getNum());
                    moneyDetailReq.setTitle(SEND_TITLE);
                    moneyDetailReq.setDesc(SEND_TITLE);
                    mqSenderService.asyncChargeDiamonds(moneyDetailReq);
                } else {
                    mqSenderService.asyncHandleResources(getResourcesDTO(aid, packData, 2));
                }
            }
            WelcomePackLogData welcomePackLogData = new WelcomePackLogData(aid, packId, config.getName(), account);
            // 官方通知及im消息
            String officialDataId = sendOfficialData(welcomePackLogData, config);
            // 保存投递记录
            welcomePackLogData.setOfficialMsgId(officialDataId);
            welcomePackLogDao.insert(welcomePackLogData);
        }
    }

    /**
     * 获取资源dto
     *
     * @param aid          用户uid
     * @param packData     礼包数据
     * @param actionType   操作类型
     * @return             资源DTO
     */
    public static ResourcesDTO getResourcesDTO(String aid, PackData packData, int actionType) {
        ResourcesDTO dto = new ResourcesDTO();
        dto.setUid(aid);
        dto.setResId(String.valueOf(packData.getResId()));
        dto.setResType(packData.getResType());
        dto.setActionType(actionType);
        if (BaseDataResourcesConstant.TYPE_BAG_GIFT == packData.getResType()) {
            dto.setNum(packData.getNum());
            dto.setDays(1);
        } else {
            dto.setDays(packData.getNum());
        }
        dto.setGetWay(BaseDataResourcesConstant.TYPE_WELCOME_PACK_GET);
        dto.setmTime(DateHelper.getNowSeconds());
        // 自然天过期
        dto.setGainType(0);
        return dto;
    }

    /**
     * 发送官方通知及im消息
     *
     * @param logData           礼包发送记录数据
     * @param welcomePackConfig 礼包配置数据
     * @return void
     */
    private String sendOfficialData(WelcomePackLogData logData, WelcomePackConfigData welcomePackConfig) {
        ActorData actorData = actorDao.getActorDataFromCache(logData.getUid());
        int slang = actorData.getSlang();
        // 推送礼包信息
        WelcomePackMsg msg = sendWelcomePackMsg(logData.getUid(), slang, welcomePackConfig);
        // 推送官方消息
        OfficialData officialData = new OfficialData();
        officialData.set_id(new ObjectId());
        officialData.setTo_uid(logData.getUid());
        officialData.setTitle(slang == SLangType.ENGLISH ? welcomePackConfig.getTitle() : welcomePackConfig.getTitleAr());
        officialData.setBody(slang == SLangType.ENGLISH ? welcomePackConfig.getDesc() : welcomePackConfig.getDescAr());
        officialData.setValid(1);
        officialData.setAtype(0);
        officialData.setNews_type(6);
        officialData.setUrl(welcomePackConfig.getUrl());
        List<PackInfoObject> packList = msg.getPackList();
        List<OfficialData.AwardInfo> awardList = new ArrayList<>();
        for (PackInfoObject packInfoObject : packList) {
            awardList.add(new OfficialData.AwardInfo(packInfoObject.getName(), packInfoObject.getIcon(), packInfoObject.getTag()));
        }
        officialData.setAward_list(awardList);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialMsgService.officialMsgPush(officialData);
        return officialData.get_id().toString();
    }

    /**
     * 发送礼包消息
     *
     * @param uid        用户id
     * @param slang      语言
     * @param configData 礼包配置数据
     * @return com.quhong.im.message.WelcomePackMsg
     */
    private WelcomePackMsg sendWelcomePackMsg(String uid, int slang, WelcomePackConfigData configData) {
        WelcomePackMsg msg = new WelcomePackMsg();
        msg.setTitle(SLangType.ENGLISH == slang ? configData.getTitle() : configData.getTitleAr());
        msg.setDesc(SLangType.ENGLISH == slang ? configData.getDesc() : configData.getDescAr());
        for (PackData packData : configData.getPackList()) {
            msg.getPackList().add(getPackInfoObject(packData, slang));
        }
        roomWebSender.sendPlayerWebMsg(null, null, uid, msg, true);
        return msg;
    }

    /**
     * 获取礼包信息对象
     *
     * @param packData 礼包数据
     * @param slang    语言
     * @return com.quhong.im.message.PackInfoObject
     */
    private PackInfoObject getPackInfoObject(PackData packData, int slang) {
        PackInfoObject packInfoObject = new PackInfoObject();
        packInfoObject.setIcon(packData.getIcon());
        ResTypeEnum typeEnum = ResTypeEnum.getByType(packData.getResType());
        // 历史原因。金币，钻石的ResType()和ResTypeEnum枚举中的不一样，需要手动转，ResTypeEnum枚举中是老的值
        if (BaseDataResourcesConstant.TYPE_DIAMOND==packData.getResType()) {
            typeEnum = ResTypeEnum.getByType(ResTypeEnum.DIAMONDS.getType());
        }
        if (BaseDataResourcesConstant.TYPE_COIN==packData.getResType()){
            typeEnum = ResTypeEnum.getByType(ResTypeEnum.COIN.getType());
        }
        if (null == typeEnum) {
            logger.error("cannot find res. packData={}", JSON.toJSONString(packData));
            return packInfoObject;
        }
        packInfoObject.setName(SLangType.ENGLISH == slang ? typeEnum.getNameEn() : typeEnum.getNameAr());
        if (-1 == packData.getNum()) {
            packInfoObject.setTag(SLangType.ENGLISH == slang ? PERMANENT : PERMANENT_AR);
        } else {
            packInfoObject.setTag(SLangType.ENGLISH == slang ? String.format(typeEnum.getTagEn(), packData.getNum()) : String.format(typeEnum.getTagAr(), packData.getNum()));
        }
        return packInfoObject;
    }

    /**
     * 检查rid是否存在
     *
     * @param ridsText rids
     * @return
     */
    public PageVO<WelcomePackReceiverVO> check(String ridsText) {
        PageVO<WelcomePackReceiverVO> pageVO = new PageVO<>();
        List<WelcomePackReceiverVO> list = new ArrayList<>();
        if (!StringUtils.hasText(ridsText)) {
            return pageVO;
        }
        String[] rids = ridsText.trim().replace("，", ",").split(",");
        for (String strRid : rids) {
            WelcomePackReceiverVO vo = new WelcomePackReceiverVO();
            vo.setRid(strRid.trim());
            ActorData actorData = null;
            try {
                actorData = actorDao.getActorByStrRid(strRid.trim());
            } catch (NumberFormatException e) {
                vo.setIsValidRid(0);
                list.add(vo);
                continue;
            }
            if (actorData == null) {
                vo.setIsValidRid(0);
                list.add(vo);
                continue;
            }
            vo.setIsValidRid(1);
            vo.setUid(actorData.getUid());
            vo.setGender(actorData.getFb_gender());
            vo.setName(actorData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            vo.setCountry(actorData.getCountry());
            vo.setRegisterTime(new ObjectId(actorData.getUid()).getTimestamp());
            list.add(vo);
        }
        pageVO.setList(list);
        return pageVO;
    }
}
