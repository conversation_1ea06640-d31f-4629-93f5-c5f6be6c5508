package com.quhong.service;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mysql.dao.UserLotteryTicketsDao;
import com.quhong.mysql.dao.UserLotteryTicketsLogDao;
import com.quhong.mysql.data.UserLotteryTicketsData;
import com.quhong.mysql.data.UserLotteryTicketsLogData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/9/25
 */
@Lazy
@Service
public class UserLotteryTicketsService {

    private static final Logger logger = LoggerFactory.getLogger(UserLotteryTicketsService.class);

    @Resource
    private UserLotteryTicketsDao userLotteryTicketsDao;
    @Resource
    private UserLotteryTicketsLogDao userLotteryTicketsLogDao;

    public int getUserTicketsNum(String uid) {
        UserLotteryTicketsData ticketsData = userLotteryTicketsDao.selectByUid(uid);
        if (null == ticketsData) {
            return 0;
        }
        return ticketsData.getNum();
    }

    /**
     * 增加用户抽奖卷数
     *
     * @param uid 用户uid
     * @param num 要增加的数量
     * @param desc 描述
     * @return 用户抽奖卷剩余数量
     */
    public int addTicketsNum(String uid, int num, String desc) {
        if (num <= 0) {
            logger.error("addTicketsNum num param error. uid={} num={}", uid, num);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        try (DistributeLock lock = new DistributeLock(getLockKey(uid))) {
            lock.lock();
            int gBalance = 0;
            UserLotteryTicketsData ticketsData = userLotteryTicketsDao.selectByUid(uid);
            if (ticketsData == null) {
                ticketsData = new UserLotteryTicketsData();
                ticketsData.setUid(uid);
                ticketsData.setNum(num);
                userLotteryTicketsDao.insert(ticketsData);
            } else {
                gBalance = ticketsData.getNum();
                ticketsData.setNum(ticketsData.getNum() + num);
                userLotteryTicketsDao.update(ticketsData);
            }
            userLotteryTicketsLogDao.insert(new UserLotteryTicketsLogData(uid, num, gBalance, ticketsData.getNum(), desc, DateHelper.getNowSeconds()));
            return ticketsData.getNum();
        }
    }

    /**
     * 减少用户抽奖卷数
     * @param uid 用户uid
     * @param num 要减少的数量
     * @param desc 描述
     * @return 用户抽奖卷剩余数量
     */
    public int reduceTicketsNum(String uid, int num, String desc) {
        if (num <= 0) {
            logger.error("addTicketsNum num param error. uid={} num={}", uid, num);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        try (DistributeLock lock = new DistributeLock(getLockKey(uid))) {
            lock.lock();
            UserLotteryTicketsData ticketsData = userLotteryTicketsDao.selectByUid(uid);
            if (ticketsData == null || ticketsData.getNum() < num) {
                logger.info("Insufficient number of raffle tickets. uid={} changed={} num={}", uid, num, ticketsData != null ? ticketsData.getNum() : 0);
                throw new CommonException(HttpCode.TICKETS_NUM_NOT_ENOUGH);
            }
            int gBalance = ticketsData.getNum();
            ticketsData.setNum(ticketsData.getNum() - num);
            userLotteryTicketsDao.update(ticketsData);
            userLotteryTicketsLogDao.insert(new UserLotteryTicketsLogData(uid, -num, gBalance, ticketsData.getNum(), desc, DateHelper.getNowSeconds()));
            return ticketsData.getNum();
        }
    }

    private String getLockKey(String uid) {
        return "userLotteryTickets:" + uid;
    }
}
