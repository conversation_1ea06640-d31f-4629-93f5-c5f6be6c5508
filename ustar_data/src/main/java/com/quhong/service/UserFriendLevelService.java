package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.cache.CacheMap;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.dao.UserFriendLevelDao;
import com.quhong.mysql.data.RoomLevelData;
import com.quhong.mysql.data.UserFriendLevelData;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户等级
 */
@Lazy
@Service
public class UserFriendLevelService {
    private static final Logger logger = LoggerFactory.getLogger(UserFriendLevelService.class);
    /**
     * 缓存60分钟
     */
    private static final long CACHE_TIME_MILLIS = 60 * 60 * 1000L;
    private static final long DAY_CACHE_TIME_MILLIS = 25 * 60 * 60 * 1000L;
    private final CacheMap<String, String> uidDateCacheMap;
    private final CacheMap<String, UserFriendLevelData> cacheMap;
    public static final List<Long> EXP_LIST = Arrays.asList(0L, 50L, 100L, 200L, 400L, 700L, 1100L);

    public static final int BASE_LEVEL = 3;
    public static final long BASE_EXP = 251;

    @Resource
    private UserFriendLevelDao userFriendLevelDao;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;


    public UserFriendLevelService() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
        this.uidDateCacheMap = new CacheMap<>(DAY_CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
        uidDateCacheMap.start();
    }


    public UserFriendLevelData getUserFriendDataCache(String uid) {
        UserFriendLevelData levelData = cacheMap.getData(uid);
        if (null != levelData) {
            return levelData;
        }
        return getUserFriendData(uid);
    }

    public UserFriendLevelData getUserFriendData(String uid) {
        UserFriendLevelData levelData = getUserFriendLevelDataFromRedis(uid);
        if (null != levelData) {
            return levelData;
        }
        levelData = findUserFriendLevelDB(uid);
        if (levelData == null) {
            logger.info("can not find user friend level data. uid={}", uid);
            return null;
        }
        cacheUserFriendLevelData(levelData);
        return levelData;
    }

    /**
     * 从0开始最高为6级，0为等级最差的
     *
     * @param uid
     * @return
     */
    public int getUserFriendLevel(String uid) {
        int level = getUserFriendLevelFromRedis(uid);
        if (level >= 0) {
            return level;
        }
        return BASE_LEVEL;
    }


    public UserFriendLevelData initUserFriendLevelData(String uid) {
        int nowSeconds = DateHelper.getNowSeconds();
        UserFriendLevelData levelData = new UserFriendLevelData();
        levelData.setUid(uid);
        levelData.setLevel(UserFriendLevelService.BASE_LEVEL);
        levelData.setExp(UserFriendLevelService.BASE_EXP);
        levelData.setRiskDesc("");
        levelData.setMtime(nowSeconds);
        levelData.setCtime(nowSeconds);
        levelData = userFriendLevelDao.insertMy(levelData);
        if (levelData == null) {
            logger.error("can not init user friend level data. uid={}", uid);
            return null;
        }
        cacheUserFriendLevelData(levelData);
        return levelData;
    }

    public void updateUserLevel(UserFriendLevelData userFriendLevelData) {
        try {
            userFriendLevelData.setMtime(DateHelper.getNowSeconds());
            userFriendLevelDao.updateByRid(userFriendLevelData);
            cacheUserFriendLevelData(userFriendLevelData);
        } catch (Exception e) {
            logger.error("update level error. uid={}", userFriendLevelData.getUid(), e);
        }
    }


    private int getUserFriendLevelFromRedis(String uid) {
        try {
            String ufLevel = getLevelFromRedis(uid);
            if (StringUtils.isEmpty(ufLevel)) {
                int level = BASE_LEVEL;
                UserFriendLevelData levelData = getUserFriendData(uid);
                if (levelData == null) {
                    setUserFriendLevelToRedis(uid, level);
                } else {
                    level = levelData.getLevel();
                    setUserFriendLevelToRedis(uid, level);
                }
                return level;
            }
            return Integer.parseInt(ufLevel);
        } catch (Exception e) {
            logger.error("get user level from redis error. uid={} {}", uid, e.getMessage(), e);
        }
        return BASE_LEVEL;
    }

    private String getLevelFromRedis(String uid) {
        try {
            String key = getUserFriendLevelKey(uid);
            String value = mainRedisTemplate.opsForValue().get(key);
            if (!StringUtils.isEmpty(value)) {
                String nowDay = DateHelper.ARABIAN.formatDateInDay();
                String oldDay = uidDateCacheMap.getData(uid);
                if (!nowDay.equals(oldDay)) {
                    mainRedisTemplate.expire(getUserFriendLevelKey(uid), ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
                    uidDateCacheMap.cacheData(uid, nowDay);
                }
            }
            return value;
        } catch (Exception e) {
            logger.error("set level to redis error. uid={} {}", uid, e.getMessage(), e);
        }
        return "";
    }

    public void setUserFriendLevelToRedis(String uid, int level) {
        try {
            String key = getUserFriendLevelKey(uid);
            mainRedisTemplate.opsForValue().set(key, level + "", ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set level to redis error. uid={} {}", uid, e.getMessage(), e);
        }
    }


    private UserFriendLevelData findUserFriendLevelDB(String uid) {
        return userFriendLevelDao.findOne(uid);
    }

    private UserFriendLevelData getUserFriendLevelDataFromRedis(String uid) {
        try {
            String roomLevelDataJson = mainRedisTemplate.opsForValue().get(getUserFriendLevelDataKey(uid));
            if (StringUtils.isEmpty(roomLevelDataJson)) {
                return null;
            }
            return JSON.parseObject(roomLevelDataJson, UserFriendLevelData.class);
        } catch (Exception e) {
            logger.error("get UserFriend level data from redis error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    private void cacheUserFriendLevelData(UserFriendLevelData levelData) {
        try {
            String key = getUserFriendLevelDataKey(levelData.getUid());
            String json = JSON.toJSONString(levelData);
            mainRedisTemplate.opsForValue().set(key, json, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
            setUserFriendLevelToRedis(levelData.getUid(), levelData.getLevel());
            cacheMap.cacheData(levelData.getUid(), levelData);
        } catch (Exception e) {
            logger.error("cache UserFriend level data error. {}", e.getMessage(), e);
        }
    }


    private String getUserFriendLevelDataKey(String uid) {
        return "str:user_friend_level_data:" + uid;
    }

    private String getUserFriendLevelKey(String uid) {
        return "str:user:friend:level:new:" + uid;
    }
}
