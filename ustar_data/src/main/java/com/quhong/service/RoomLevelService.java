package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.cache.CacheMap;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.dao.RoomLevelDao;
import com.quhong.mysql.data.RoomLevelData;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户等级
 */
@Lazy
@Service
public class RoomLevelService {
    private static final Logger logger = LoggerFactory.getLogger(RoomLevelService.class);
    /**
     * 缓存60分钟
     */
    private static final long CACHE_TIME_MILLIS = 60 * 60 * 1000L;
    private static final long DAY_CACHE_TIME_MILLIS = 25 * 60 * 60 * 1000L;
    private static final int TIME_OUT = 7;
    private final CacheMap<String, RoomLevelData> cacheMap;
    private final CacheMap<String, String> roomIdDateCacheMap;
    private static final List<String> LEVEL_RIPPLE_LIST = Arrays.asList("https://cdn3.qmovies.tv/youstar/op_1695697258_3.webp",
            "https://cdn3.qmovies.tv/youstar/op_1695697258_4.webp",
            "https://cdn3.qmovies.tv/youstar/op_1695697258_5.webp",
            "https://cdn3.qmovies.tv/youstar/op_1695697258_6.webp",
            "https://cdn3.qmovies.tv/youstar/op_1695697258_7.webp",
            "https://cdn3.qmovies.tv/youstar/op_1695697259_8.webp");

    @Resource
    private RoomLevelDao roomLevelDao;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;


    public RoomLevelService() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
        this.roomIdDateCacheMap = new CacheMap<>(DAY_CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
        roomIdDateCacheMap.start();
    }

    public int getRoomLevel(String roomId) {
        int level = getRoomLevelFromRedis(roomId);
        if (level >= 0) {
            return level;
        }
        RoomLevelData levelData = findRoomLevel(roomId);
        if (levelData == null) {
            logger.error("can not find level data. roomId={}", roomId);
            return 0;
        }
        return levelData.getLevel();
    }

    private int getRoomLevelFromRedis(String roomId) {
        try {
            String key = getNewRoomLevelKey(roomId);
            String roomLevel = mainRedisTemplate.opsForValue().get(key);
            if (StringUtils.isEmpty(roomLevel)) {
                int level = 0;
                RoomLevelData roomLevelData = findRoomLevel(roomId);
                if (roomLevelData == null) {
                    setRoomLevelToRedis(roomId, 0);
                } else {
                    level = roomLevelData.getLevel();
                    setRoomLevelToRedis(roomId, level);
                }
                return level;
            }
            String nowDay = DateHelper.ARABIAN.formatDateInDay();
            String oldDay = roomIdDateCacheMap.getData(roomId);
            if (!nowDay.equals(oldDay)) {
                mainRedisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
                roomIdDateCacheMap.cacheData(roomId, nowDay);
            }
            return Integer.parseInt(roomLevel);
        } catch (Exception e) {
            logger.error("get user level from redis error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    public void setRoomLevelToRedis(String roomId, int level) {
        try {
            String key = getNewRoomLevelKey(roomId);
            mainRedisTemplate.opsForValue().set(key, level + "", ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set room level to redis error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }


    private String getNewRoomLevelKey(String roomId) {
        return "str:room:level:new:" + roomId;
    }

    private RoomLevelData findRoomLevel(String roomId) {
        return roomLevelDao.findOne(roomId);
    }


    private RoomLevelData insertRoomLevelData(RoomLevelData roomLevelData) {
        return roomLevelDao.insertMy(roomLevelData);
    }


    public RoomLevelData getRoomLevelData(String roomId) {
        RoomLevelData roomLevelData = getRoomLevelDataFromRedis(roomId);
        if (null != roomLevelData) {
            return roomLevelData;
        }
        roomLevelData = findRoomLevel(roomId);
        if (roomLevelData == null) {
            logger.info("can not find room level data. roomId={}", roomId);
            return null;
        }
        cacheRoomLevelData(roomLevelData);
        return roomLevelData;
    }


    public RoomLevelData getRoomLevelDataCache(String roomId) {
        RoomLevelData roomLevelData = cacheMap.getData(roomId);
        if (null != roomLevelData) {
            return roomLevelData;
        }
        return getRoomLevelData(roomId);
    }


    private RoomLevelData getRoomLevelDataFromRedis(String uid) {
        try {
            String roomLevelDataJson = mainRedisTemplate.opsForValue().get(getRoomLevelDataKey(uid));
            if (StringUtils.isEmpty(roomLevelDataJson)) {
                return null;
            }
            return JSON.parseObject(roomLevelDataJson, RoomLevelData.class);
        } catch (Exception e) {
            logger.error("get room level data from redis error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    private void cacheRoomLevelData(RoomLevelData roomLevelData) {
        try {
            cacheMap.cacheData(roomLevelData.getRoomId(), roomLevelData);
            String key = getRoomLevelDataKey(roomLevelData.getRoomId());
            String json = JSON.toJSONString(roomLevelData);
            mainRedisTemplate.opsForValue().set(key, json, TIME_OUT, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("cache room level data error. {}", e.getMessage(), e);
        }
    }

    private String getRoomLevelDataKey(String roomId) {
        return "str:room_level_data:" + roomId;
    }

    public void updateUserLevel(RoomLevelData roomLevelData) {
        try {
            roomLevelDao.updateByRoomId(roomLevelData);
            cacheRoomLevelData(roomLevelData);
        } catch (Exception e) {
            logger.error("update room level error. uid={}", roomLevelData.getRoomId(), e);
        }
    }

    public RoomLevelData initRoomLevelData(RoomLevelData data) {
        RoomLevelData roomLevelData = insertRoomLevelData(data);
        if (roomLevelData == null) {
            logger.error("can not init room level data. data={}", data);
            return null;
        }
        cacheRoomLevelData(roomLevelData);
        return roomLevelData;
    }

    public int getStepLevel(int level) {
        if (level <= 0) {
            return 0;
        } else {
            int ret = (level - 1) / 25 + 1;
            return Math.min(8, ret);
        }
    }

    public int getStepLevel(String roomId) {
        return getStepLevel(getRoomLevel(roomId));
    }

    public String getRippleUrl(String roomId) {
        int stepLevel = getStepLevel(getRoomLevel(roomId));
//        logger.info("getRippleUrl--roomId:{} stepLevel:{}",roomId,stepLevel);
        if (stepLevel < 3) {
            return "";
        } else {
            return LEVEL_RIPPLE_LIST.get(stepLevel - 3);
        }

    }
}
