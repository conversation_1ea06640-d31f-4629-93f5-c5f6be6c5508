package com.quhong.service;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.VideoRoomRecordEvent;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.ResTypeEnum;
import com.quhong.mongo.dao.PackConfigDao;
import com.quhong.mongo.data.PackConfigData;
import com.quhong.mongo.data.PackData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.redis.ShuShuAnalyseRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

@Lazy
@Service
public class ShuShuAnalyseService {

    public final Logger logger = LoggerFactory.getLogger(ShuShuAnalyseService.class);

    @Resource
    private ShuShuAnalyseRedis shuShuAnalyseRedis;
    @Autowired(required = false)
    private EventReport eventReport;

    /**
     * @param closeType  0 手动关闭youtube 1  直接点击退出语音房 2 房主切换进入视频房间导致关闭 3  异常退出
     *                   4 房主进入其他房间导致房主退出前面视频房
     */
    public void closeYouToBeVideoEvent(String roomId, String uid, int closeType) {
        if (RoomUtils.isHomeowner(uid, roomId)) {
            VideoRoomRecordEvent event = shuShuAnalyseRedis.getVideoRoomRecord(roomId, uid);
            if (event != null) {
                event.setUid(uid);
                event.setRoomid(roomId);
                event.setEnd_ctime(DateHelper.getNowSeconds());
                event.setVideo_room_run_duration(event.getEnd_ctime() - event.getStart_ctime());
                event.setVideo_room_close_type(closeType);
                eventReport.track(new EventDTO(event));
                shuShuAnalyseRedis.deleteVideoRoomRecord(roomId,uid);
            }
        }
    }

    /**
     *
     * @param openType  0 手动开启youtube 1 开启语音房自动开启youtube(房主上次退出房间时，未手动关闭youtube)
     */
    public void openYouToBeVideoEvent(String roomId, String uid, int openType) {
        if (RoomUtils.isHomeowner(uid, roomId)) {
            VideoRoomRecordEvent event = shuShuAnalyseRedis.getVideoRoomRecord(roomId, uid);
            if (event != null) {
                event.setStart_ctime(DateHelper.getNowSeconds());
                event.setVideo_room_open_type(openType);
            } else {
                event = new VideoRoomRecordEvent();
                event.setUid(uid);
                event.setRoomid(roomId);
                event.setStart_ctime(DateHelper.getNowSeconds());
                event.setVideo_room_open_type(openType);
            }
            shuShuAnalyseRedis.saveVideoRoomRecord(event);
        }
    }
}
