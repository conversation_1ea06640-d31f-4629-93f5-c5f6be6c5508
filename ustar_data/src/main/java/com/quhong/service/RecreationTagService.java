package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.SudGameInfo;
import com.quhong.data.TruthOrDareInfo;
import com.quhong.data.TruthOrDareV2Info;
import com.quhong.data.TurntableGameInfo;
import com.quhong.enums.RoomConstant;
import com.quhong.enums.VideoActionType;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.RoomMicDao;
import com.quhong.mysql.data.RoomMicData;
import com.quhong.redis.DataRedisBean;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Lazy
@Service
public class RecreationTagService {
    private static final Logger logger = LoggerFactory.getLogger(RecreationTagService.class);

    // 0普通语聊房 1Youtube 2幸运转盘 3Ludo 4真心话大冒险 10Bumper Blaster
    public static final int COMMON_TAG = 0;
    public static final int YOUTUBE_TAG = 1;
    public static final int TURNTABLE_TAG = 2;
    public static final int LUDO_TAG = 3;
    public static final int TRUTH_DARE_TAG = 4;
    public static final int LIVE_ROOM_MODE_TAG = 5;
    public static final int BUMPER_BLASTER_TAG = 10;
    public static final int ROOM_EVENT_ACTIVITY_TAG = 15;
    public static final int TRUTH_DARE_THEME_GAME_TAG = 100;   // 真心话大冒险游戏
    public static final String ROOM_EVENT_ACTIVITY_NAME_EN = "Sparkling YouStar";
    public static final String ROOM_EVENT_ACTIVITY_NAME_AR = "تألق يوستار";

    @Resource
    private RoomMicDao roomMicDao;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public int getRecreationTag(MongoRoomData roomData, Map<String, TurntableGameInfo> turntableRoomMap,
                                Map<String, SudGameInfo> sudGameRoomMap, Map<String, TruthOrDareInfo> truthDareRoomMap,
                                Set<String> roomEventActivitySet, Map<String, TruthOrDareV2Info> truthDareV2RoomMap) {
        if (RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode()) {
            // 只要有一个麦位开启摄像头就显示LIVE标签
            List<RoomMicData> micList = roomMicDao.getDataListFromRedis(roomData.getRid());
            if (null != micList && micList.stream().anyMatch(micData -> !StringUtils.isEmpty(micData.getUid()) && micData.getVideoStatus() == 1)) {
                return LIVE_ROOM_MODE_TAG;
            }
        }

        if (roomEventActivitySet.contains(roomData.getRid())) {
            return ROOM_EVENT_ACTIVITY_TAG;
        }

        int gameTag = getRecentlyCreateGameTag(roomData.getRid(), turntableRoomMap, sudGameRoomMap, truthDareRoomMap, truthDareV2RoomMap);
        if (gameTag > 0) {
            return gameTag;
        }
        if (VideoActionType.OPEN == roomData.getVideo_switch()) {
            return YOUTUBE_TAG;
        }

        // if (turntableRoomMap.containsKey(roomData.getRid())) {
        //     return TURNTABLE_TAG;
        // }
        // if (truthDareRoomMap.containsKey(roomData.getRid())) {
        //     return TRUTH_DARE_TAG;
        // }
        return 0;
    }

    public int getSudGameTag(SudGameInfo sudGameInfo) {
        if (sudGameInfo == null) {
            return 0;
        }
        if (sudGameInfo.getGameType() == 1) {
            return BUMPER_BLASTER_TAG;
        } else if (sudGameInfo.getGameType() == 2) {
            return LUDO_TAG;
        } else if (sudGameInfo.getGameType() == 6) {
            return 6;
        } else if (sudGameInfo.getGameType() > 0) {
            return 9 + sudGameInfo.getGameType();
        }
        return 0;
    }

    /**
     * 所有游戏差异返回tag, 同一个层级取最近创建
     * @return tag
     */

    public int getRecentlyCreateGameTag(String roomId, Map<String, TurntableGameInfo> turntableRoomMap,
                                        Map<String, SudGameInfo> sudGameRoomMap, Map<String, TruthOrDareInfo> truthDareRoomMap,
                                        Map<String, TruthOrDareV2Info> truthDareV2RoomMap){
        Map<Integer, Integer> tagAndGameCtimeMap = new HashMap<>();
        TurntableGameInfo turntableGameInfo = turntableRoomMap.get(roomId);
        if (turntableGameInfo != null){
            tagAndGameCtimeMap.put(TURNTABLE_TAG, turntableGameInfo.getCtime());
        }
        SudGameInfo sudGameInfo = sudGameRoomMap.get(roomId);
        if (sudGameInfo != null){
            tagAndGameCtimeMap.put(getSudGameTag(sudGameInfo), sudGameInfo.getCreateTime());
        }
        TruthOrDareInfo truthOrDareInfo = truthDareRoomMap.get(roomId);
        if (truthOrDareInfo != null){
            tagAndGameCtimeMap.put(TRUTH_DARE_TAG, truthOrDareInfo.getCtime());
        }

        TruthOrDareV2Info truthOrDareV2Info = truthDareV2RoomMap.get(roomId);
        if (truthOrDareV2Info != null){
            tagAndGameCtimeMap.put(TRUTH_DARE_THEME_GAME_TAG, new ObjectId(truthOrDareV2Info.getGameId()).getTimestamp());
        }
        return tagAndGameCtimeMap.entrySet().stream().max(Map.Entry.comparingByValue()).map(Map.Entry::getKey).orElse(0);
    }


    public Map<String, SudGameInfo> getAllSudGameInfo() {
        try {
            Map<String, SudGameInfo> gameInfoMap = new HashMap<>();
            Map<Object, Object> entries = clusterRedis.opsForHash().entries("hash:sud_game_info");
            for (Object obj : entries.values()) {
                SudGameInfo gameInfo = JSON.parseObject((String) obj, SudGameInfo.class);
                gameInfoMap.put(gameInfo.getRoomId(), gameInfo);
            }
            return gameInfoMap;
        } catch (Exception e) {
            logger.error("get all sud game info error.", e);
            return Collections.emptyMap();
        }
    }


    private String getRoomEventZsetActivityKey() {
        return "zset:activity:roomEventFlag";
    }

    public void addCommonZSetScore(String roomId, int score) {
        try {
            String key = getRoomEventZsetActivityKey();
            clusterRedis.opsForZSet().add(key, roomId, score);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addCommonZSetScore error roomId={} giftId={} e={}", roomId, score, e.getMessage(), e);
        }
    }

    public void removeCommonZSetScore(int now) {
        try {
            String key = getRoomEventZsetActivityKey();
            clusterRedis.opsForZSet().removeRangeByScore(key, 0, now);
        } catch (Exception e) {
            logger.info("removeCommonZSetScore error e:{}", e.getMessage(), e);
        }
    }

    public Set<String> getAllCommonZSetValue() {
        try {
            String key = getRoomEventZsetActivityKey();
            Set<String> roomSet = clusterRedis.opsForZSet().range(key, 0, -1);
            return roomSet;
        } catch (Exception e) {
            logger.error("getCommonZSetValue error.  e:{}", e.getMessage(), e);
        }
        return null;
    }
}

