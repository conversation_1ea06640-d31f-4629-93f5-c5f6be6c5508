package com.quhong.service.mysql;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.msg.push.ClientLogReportMsg;
import com.quhong.mysql.data.ClientLogData;
import com.quhong.mysql.mapper.ustar.ClientLogMapper;
import com.quhong.redis.DataRedisBean;
import com.quhong.room.RoomWebSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Lazy
@Service
public class ClientLogService extends ServiceImpl<ClientLogMapper, ClientLogData> {
    private static final Logger logger = LoggerFactory.getLogger(ClientLogService.class);
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private RoomWebSender roomWebSender;

    public void saveClientLog(ClientLogData clientLog) {
        try {
            save(clientLog);
        } catch (Exception e) {
            logger.error("saveClientLog error msg={}", e.getMessage(), e);
        }
    }

    public List<ClientLogData> clientLogList(String uid) {
        if (null == uid) {
            return lambdaQuery()
                    .orderByDesc(ClientLogData::getCtime)
                    .last("limit 30")
                    .list();
        }
        return lambdaQuery()
                .eq(ClientLogData::getUid, uid)
                .orderByDesc(ClientLogData::getCtime)
                .last("limit 20")
                .list();
    }

    private String getClientLogReqFlagKey(String uid) {
        return "str:clientLogReq:" + uid;
    }

    /**
     * 仅运营平台调用，向用户请求客户端日志
     */
    public void addClientLogReqFlag(String uid, int logEndTime) {
        logger.info("add client log req flag. uid={} logEndTime={}", uid, logEndTime);
        roomWebSender.sendPlayerWebMsg(null, null, uid, new ClientLogReportMsg(logEndTime), true);
        redisTemplate.opsForValue().set(getClientLogReqFlagKey(uid), String.valueOf(logEndTime), 1, TimeUnit.DAYS);
    }

    /**
     * 用户确认收到日志请求后删除标记
     */
    public void delClientLogReqFlag(String uid) {
        logger.info("del client log req flag. uid={}", uid);
        redisTemplate.delete(getClientLogReqFlagKey(uid));
    }

    public int getLogEndTime(String uid) {
        try {
            String value = redisTemplate.opsForValue().get(getClientLogReqFlagKey(uid));
            return ObjectUtils.isEmpty(value) ? 0 : Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
