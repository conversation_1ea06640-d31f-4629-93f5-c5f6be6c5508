package com.quhong.service.mysql;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.mysql.data.NationalDayActivities;
import com.quhong.mysql.mapper.ustar_log.NationalDayActivitiesMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

@Lazy
@Service
public class NationalDayService extends ServiceImpl<NationalDayActivitiesMapper, NationalDayActivities> {
    protected static final Logger logger = LoggerFactory.getLogger(NationalDayService.class);

    public List<NationalDayActivities> getNationalDayActivitiesFromDb() {
        String queryTime = ServerConfig.isProduct() ? DateSupport.ARABIAN.yyyyMMdd() : DayTimeSupport.ARABIAN.yyyyMMddHH();
        return lambdaQuery()
                .ge(NationalDayActivities::getStartTime, queryTime)
                .lt(NationalDayActivities::getEndTime, queryTime)
                .list();
    }

    public NationalDayActivities getNationalDayById(int id) {
        return lambdaQuery().getBaseMapper().selectById(id);
    }
}
