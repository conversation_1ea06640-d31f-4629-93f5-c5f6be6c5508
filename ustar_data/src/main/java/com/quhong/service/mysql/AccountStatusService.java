package com.quhong.service.mysql;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.cache.CacheMap;
import com.quhong.constant.AccountConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.AccountStatusData;
import com.quhong.mysql.data.MySQLActorData;
import com.quhong.mysql.mapper.ustar.AccountStatusMapper;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Set;

@Lazy
@Service
public class AccountStatusService extends ServiceImpl<AccountStatusMapper, AccountStatusData> {
    private static final Logger logger = LoggerFactory.getLogger(AccountStatusService.class);

    private final CacheMap<String, Set<String>> delAccountUidMap;

    public AccountStatusService() {
        delAccountUidMap = new CacheMap<>(30 * 60 * 1000L);
    }

    @PostConstruct
    public void postInit() {
        delAccountUidMap.start();
    }

    /**
     * 获取所有已被删除账号的用户uid
     */
    public Set<String> getDelAccountUid() {
        Set<String> all = delAccountUidMap.getData("ALL");
        if (null != all) {
            return all;
        }
        synchronized (delAccountUidMap) {
            all = delAccountUidMap.getData("ALL");
            if (null != all) {
                return all;
            }
            all = getDelAccountUidFromDB();
            delAccountUidMap.cacheData("ALL", all);
        }
        return all;
    }

    public Set<String> getDelAccountUidFromDB() {
        List<AccountStatusData> list = lambdaQuery()
                .select(AccountStatusData::getUid)
                .eq(AccountStatusData::getStatus, AccountConstant.DELETED)
                .list();
        logger.info("get del account uid set from db size={}", list.size());
        return CollectionUtil.listToPropertySet(list, AccountStatusData::getUid);
    }

    public void deleteAccountStatus(String uid) {
        try {
            QueryWrapper<AccountStatusData> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("uid", uid);
            queryWrapper.eq("status", AccountConstant.DELETE_FLAG);
            remove(queryWrapper);
        } catch (Exception e) {
            logger.error("deleteAccountStatus msg={}", e.getMessage(), e);
        }
    }

    public List<AccountStatusData> getAllPyStatusZero(int endTime) {
        List<AccountStatusData> list = lambdaQuery()
                .select(AccountStatusData::getId,AccountStatusData::getUid)
                .eq(AccountStatusData::getPy_status, 0)
                .le(AccountStatusData::getApplyTime,endTime)
                .list();
        logger.info("getPyStatusZeroAll size={}", list.size());
        return list;
    }


    public void updatePyStatus(int id) {
        try {
            LambdaUpdateWrapper<AccountStatusData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AccountStatusData::getId, id)
                    .set(AccountStatusData::getPy_status, 1)
                    .set(AccountStatusData::getMtime, DateHelper.getNowSeconds());
            update(lambdaUpdateWrapper);
        } catch (Exception e) {
            logger.error("update mysql actor data error {}", e.getMessage());
        }
    }


}
