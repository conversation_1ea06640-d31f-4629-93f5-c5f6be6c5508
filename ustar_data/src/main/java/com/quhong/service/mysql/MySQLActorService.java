package com.quhong.service.mysql;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.MySQLActorData;
import com.quhong.mysql.mapper.ustar.MySQLActorMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Lazy
@Service
public class MySQLActorService extends ServiceImpl<MySQLActorMapper, MySQLActorData> {
    public static final Logger logger = LoggerFactory.getLogger(MySQLActorService.class);

    public void updateActor(String uid, int accountStatus) {
        try {
            LambdaUpdateWrapper<MySQLActorData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(MySQLActorData::getActorUid, uid)
                    .set(MySQLActorData::getAccountStatus, accountStatus)
                    .set(MySQLActorData::getMtime, DateHelper.getNowSeconds());
            update(lambdaUpdateWrapper);
        } catch (Exception e) {
            logger.error("update mysql actor data error {}", e.getMessage());
        }
    }

    public void updateLoginData(MySQLActorData data) {
        try {
            LambdaUpdateWrapper<MySQLActorData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(MySQLActorData::getActorUid, data.getActorUid())
                    .set(MySQLActorData::getRid, data.getRid())
                    .set(MySQLActorData::getAppPackageName, data.getAppPackageName())
                    .set(MySQLActorData::getVersionCode, data.getVersionCode())
                    .set(MySQLActorData::getCountry, StringUtils.isEmpty(data.getCountry()) ? "" : data.getCountry())
                    .set(MySQLActorData::getLoginType, data.getLoginType())
                    .set(MySQLActorData::getUid, data.getUid())
                    .set(MySQLActorData::getLang, data.getLang())
                    .set(MySQLActorData::getIsDelete, data.getIsDelete())
                    .set(MySQLActorData::getAccountStatus, data.getAccountStatus())
                    .set(MySQLActorData::getChannel, data.getChannel())
                    .set(MySQLActorData::getIp, data.getIp())
                    .set(MySQLActorData::getTnId, data.getTnId())
                    .set(MySQLActorData::getName, data.getName())
                    .set(MySQLActorData::getHead, data.getHead())
                    .set(MySQLActorData::getFbGender, data.getFbGender())
                    .set(MySQLActorData::getMtime, DateHelper.getNowSeconds())
                    .set(MySQLActorData::getLastLoginTime, DateHelper.getNowSeconds());
            update(lambdaUpdateWrapper);
        } catch (Exception e) {
            logger.error("update mysql actor data error {}", e.getMessage());
        }
    }


    public boolean insertActor(MySQLActorData mySQLActorData) {
        try {
            return save(mySQLActorData);
        } catch (Exception e) {
            logger.error("insert mysql actor data error {}", e.getMessage());
            return false;
        }
    }

    public int getMaxGenRid() {
        try {
            // 创建wapper，查询最大的id
            QueryWrapper<MySQLActorData> wrapper = new QueryWrapper<>();
            wrapper.select("max(gen_id) as genId");
            MySQLActorData actor = getBaseMapper().selectOne(wrapper);
//            MySQLActorData actor = lambdaQuery().getBaseMapper().selectOne(wrapper);
            return actor.getGenId() == null ? 0 : actor.getGenId();
        } catch (Exception e) {
            logger.error("getMaxGenRid mysql actor data error {}", e.getMessage(), e);
            return 0;
        }
    }


    public void updateBaseInfo(MySQLActorData data) {
        try {
            LambdaUpdateWrapper<MySQLActorData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(MySQLActorData::getActorUid, data.getActorUid())
                    .set(MySQLActorData::getAppPackageName, data.getAppPackageName())
                    .set(MySQLActorData::getVersionCode, data.getVersionCode())
                    .set(MySQLActorData::getName, data.getName())
                    .set(MySQLActorData::getHead, data.getHead())
                    .set(MySQLActorData::getFbGender, data.getFbGender())
                    .set(MySQLActorData::getMtime, DateHelper.getNowSeconds())
                    .set(MySQLActorData::getLastLoginTime, DateHelper.getNowSeconds());
            update(lambdaUpdateWrapper);
        } catch (Exception e) {
            logger.error("updateBaseInfo data error {}", e.getMessage());
        }
    }

}

