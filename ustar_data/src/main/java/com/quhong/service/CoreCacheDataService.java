package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.data.vo.LimitedRecharge;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 缓存数据服务 - Core模块简版
 * 主要用于获取首充用户充值优惠信息
 */
@Lazy
@Service
public class CoreCacheDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(CoreCacheDataService.class);


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    /**
     * 获取用户首充充值优惠信息，带缓存
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public LimitedRecharge getLimitedRechargeInfoCache(String limitedRechargeKey, String uid) {
        LimitedRecharge info;
        String jsonValue = getCommonHashStrValue(limitedRechargeKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            info = JSONObject.parseObject(jsonValue, LimitedRecharge.class);
        } else {
            info = new LimitedRecharge();
        }
        return info;
    }

    public String getCommonHashStrValue(String hashActivityId, String key) {
        try {
            return (String) clusterTemplate.opsForHash().get(getCommonHashKey(hashActivityId), key);
        } catch (Exception e) {
            logger.info("getCommonHashStrValue error hashActivityId={} key={}  e={}", hashActivityId, key, e);
            return null;
        }
    }
    private String getCommonHashKey(String hashActivityId) {
        return String.format("hash:activity:%s", hashActivityId);
    }

}
