package com.quhong.service;

import com.quhong.constant.ResourceConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Lazy
@Component
public class ActivityUtilService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityUtilService.class);

    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private HeartRecordDao heartRecordDao;

    private ResourcesDTO giftsMqVoToResDTO(GiftsMqVo vo) {
        if (vo == null) {
            return null;
        } else {
            String uid = vo.getUid();
            String stype = vo.getStype();
            int sourceId = vo.getSource_id();
            int day = vo.getDay();
            int num = vo.getNum();
            if (ResourceConstant.GIFT.equals(stype)) {
                if (num > 0) {
                    ResourcesDTO dto = new ResourcesDTO();
                    dto.setUid(uid);
                    dto.setResId(String.valueOf(sourceId));
                    dto.setResType(ResourceConstant.NAME_TYPE_MAP.get(stype));
                    dto.setActionType(BaseDataResourcesConstant.ACTION_GET);
                    dto.setGetWay(4);
                    dto.setmTime(DateHelper.getNowSeconds());
                    dto.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
                    dto.setDays(day > 0 ? day : -1);
                    dto.setNum(num);
                    return dto;
                } else {
                    return null;
                }
            } else if (ResourceConstant.MIC.equals(stype) || ResourceConstant.BUDDLE.equals(stype) ||
                    ResourceConstant.RIDE.equals(stype) || ResourceConstant.RIPPLE.equals(stype) ||
                    ResourceConstant.BADGE.equals(stype) || ResourceConstant.FLOAT_SCREEN.equals(stype) ||
                    ResourceConstant.BACK_GROUND.equals(stype) || ResourceConstant.HONOR_TITLE.equals(stype) ||
                    ResourceConstant.ENTRY_EFFECT.equals(stype)) {
                ResourcesDTO dto = new ResourcesDTO();
                dto.setUid(uid);
                dto.setResId(String.valueOf(sourceId));
                if (ResourceConstant.BACK_GROUND.equals(stype)) {
                    dto.setRoomId(RoomUtils.formatRoomId(uid));
                }
                dto.setResType(ResourceConstant.NAME_TYPE_MAP.get(stype));
                if(ResourceConstant.HONOR_TITLE.equals(stype)){
                    dto.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
                }else {
                    dto.setActionType(BaseDataResourcesConstant.ACTION_GET);
                }
                dto.setmTime(DateHelper.getNowSeconds());
                dto.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
                dto.setDays(day > 0 ? day : -1);
                dto.setNum(num != 0 ? num : 1);
                return dto;
            } else {
                return null;
            }
        }
    }

    public void handleResources(GiftsMqVo vo) {
        ResourcesDTO dto = giftsMqVoToResDTO(vo);
        if (dto != null) {
            mqSenderService.asyncHandleResources(dto);
        } else {
            logger.info("ResourcesDTO is null vo={}", vo);
        }
    }

    public void distributionRewardResource(String uid, Integer sourceId, String rewardType, Integer days, Integer num, String title, String desc, int officialMsg) {
        if (ResourceConstant.HEART.equals(rewardType)) {
            logger.info("send coin reward. uid={}, num={}, title={}", uid, num, title);
            heartRecordDao.changeHeart(uid, num, title, desc);
        } else {
            logger.info("send gift to mq. toUid={}, sourceId={}, rewardType={} time={}", uid, sourceId, rewardType, days);

            Integer resType = ResourceConstant.NAME_TYPE_MAP.get(rewardType);
            if(resType == null){
                logger.error("distributionRewardResource not find resType: {}, uid:{}, sourceId:{}", resType, uid, sourceId);
            }
            ResourcesDTO resourcesDTO = new ResourcesDTO();
            resourcesDTO.setUid(uid);
            if (ResourceConstant.BACK_GROUND.equals(rewardType)) {
                resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
            }
            resourcesDTO.setResId(String.valueOf(sourceId));
            resourcesDTO.setResType(resType);
            resourcesDTO.setItemsSourceDetail(desc);
            resourcesDTO.setDesc(desc);
            resourcesDTO.setEmptyWearType(1);
            if (ResourceConstant.HONOR_TITLE.equals(rewardType)){
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
            }else {
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
            }
            resourcesDTO.setOfficialMsg(officialMsg);
            resourcesDTO.setmTime(DateHelper.getNowSeconds());
            resourcesDTO.setNum(num != 0 ? num : 1);
            resourcesDTO.setDays(days != 0 ? days : -1);
            mqSenderService.asyncHandleResources(resourcesDTO);
        }
    }

}
