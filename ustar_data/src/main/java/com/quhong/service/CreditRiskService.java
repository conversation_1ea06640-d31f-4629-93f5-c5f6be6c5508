package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.cache.CacheMap;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.RiskDeviceDao;
import com.quhong.mysql.dao.RiskUserDao;
import com.quhong.mysql.data.RiskDeviceData;
import com.quhong.mysql.data.RiskUserData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Lazy
@Service
public class CreditRiskService {
    private static final Logger logger = LoggerFactory.getLogger(CreditRiskService.class);
    public static final int MAX_SCORE = 1000;
    public static final int AVAILABLE_STATUS = 1;
    public static final int UNAVAILABLE_STATUS = 2;
    public static final int WHITE_STATUS = 3;
    public static final int TN_DEVICE_TYPE = 1;
    public static final int IP_DEVICE_TYPE = 2;
    public static final long EXPIRE_TIME = 3 * 60 * 1000;
    public static final int MIN_SCORE = -1000;
    private static final boolean IS_CREDIT_INTERCEPT = false;

    public static final int TYPE_DEVICE_ZERO = 1001;
    public static final int TYPE_DEVICE_DEDUCT_RELATED = 1002;
    public static final int TYPE_DEVICE_RELATED = 1003;
    public static final int TYPE_USER_PROFILE = 1004;
    public static final int TYPE_USER_IMG = 1005;
    public static final int TYPE_SCREEN_MSG = 1006;
    public static final int TYPE_SCREEN_IMG = 1007;
    public static final int TYPE_MOMENT_MSG = 1008;
    public static final int TYPE_MOMENT_IMG = 1009;
    public static final int TYPE_DAU = 2001;
    public static final int TYPE_GIFT_MIC = 2002;
    public static final int TYPE_CHARGE = 2003;
    public static final int TYPE_OPERATION_WHITE_CHANGE = 3001;

    public static final Map<Integer, String> TYPE_MAP_DESC = new HashMap<Integer, String>() {
        {
            put(TYPE_DEVICE_ZERO, "图灵设备命中信用分清零加入黑名单");
            put(TYPE_DEVICE_DEDUCT_RELATED, "图灵设备命中信用减分以及关联账号设备数及关联ip数设备减分");
            put(TYPE_USER_PROFILE, "个人信息文字为脏词减分");
            put(TYPE_USER_IMG, "个人图片鉴定为脏图减分");
            put(TYPE_SCREEN_MSG, "公屏消息鉴定为脏词减分");
            put(TYPE_SCREEN_IMG, "公屏图片鉴定为脏图减分");
            put(TYPE_MOMENT_MSG, "动态文字鉴定为脏词减分");
            put(TYPE_MOMENT_IMG, "动态图片鉴定为脏图减分");
            put(TYPE_DAU, "日活每日加分");
            put(TYPE_GIFT_MIC, "礼物及上麦时长每小时加分");
            put(TYPE_CHARGE, "充值100刀加入白名单");
            put(TYPE_OPERATION_WHITE_CHANGE, "运营系统设置白名单及加减分");
        }
    };
    @Resource
    private RiskDeviceDao riskDeviceDao;
    @Resource
    private RiskUserDao riskUserDao;
    @Resource
    private ActorDao actorDao;
    @Autowired(required = false)
    private EventReport eventReport;

    private CacheMap<String, Integer> cacheMap;

    public CreditRiskService() {
        cacheMap = new CacheMap<>(EXPIRE_TIME);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public void handleRiskUserScore(String uid, int score) {
        try {
            if (score == 0) {
            } else if (score > 0) {
                // 加分
                RiskUserData riskUserData = riskUserDao.selectOne(uid);
                if (riskUserData != null && riskUserData.getStatus() == AVAILABLE_STATUS) {
                    int oldScore = riskUserData.getScore();
                    if (oldScore != MAX_SCORE) {
                        int toScore = oldScore + score;
                        riskUserData.setScore(Math.min(toScore, MAX_SCORE));
                        riskUserData.setMtime(DateHelper.getNowSeconds());
                        riskUserDao.update(riskUserData);
                        logger.info("add risk user score success uid={} add={} newScore={}", uid, score, riskUserData.getScore());
                    }
                } else {
                    logger.info("add risk user score fail because not record or unavailable uid={} riskUserData={}", uid, riskUserData);
                }

            } else {
                // 减分
                RiskUserData riskUserData = riskUserDao.selectOne(uid);
                if (riskUserData != null) {
                    if (riskUserData.getStatus() == AVAILABLE_STATUS) {
                        int oldScore = riskUserData.getScore();
                        if (oldScore != 0) {
                            int toScore = oldScore + score;
                            riskUserData.setScore(Math.max(toScore, 0));
                            riskUserData.setMtime(DateHelper.getNowSeconds());
                            riskUserDao.update(riskUserData);
                            logger.info("deduct risk user score success update old uid={} minus={} newScore={}", uid, score, riskUserData.getScore());
                        }
                    }
                } else {
                    JSONObject jsonObject = actorDao.findActorJSONFromDB(uid);
                    if (jsonObject != null) {
                        String thirdUid = jsonObject.getString("uid");
                        int loginType = jsonObject.getIntValue("login_type");
                        if (!StringUtils.isEmpty(thirdUid) && loginType > 0) {
                            int toScore = MAX_SCORE + score;
                            int now = DateHelper.getNowSeconds();
                            riskUserData = new RiskUserData(uid, thirdUid, AVAILABLE_STATUS, loginType, Math.max(toScore, 0), now, now);
                            riskUserDao.insert(riskUserData);
                            logger.info("deduct risk user score success insert new uid={} minus={} newScore={}", uid, score, riskUserData.getScore());
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("handleRiskUserScore error uid={} score={} msg={}", uid, score, e.getMessage(), e);
        }
    }

    public void deductDeviceScore(String tnId, int score) {
        deductRiskDeviceScore(tnId, TN_DEVICE_TYPE, score);
    }

    public void deductIpScore(String ip, int score) {
        deductRiskDeviceScore(ip, IP_DEVICE_TYPE, score);
    }

    /**
     * @param keyId
     * @param keyType
     * @param score   需要减去的分数，入参为正数
     */
    private void deductRiskDeviceScore(String keyId, int keyType, int score) {
        try {
            if (StringUtils.isEmpty(keyId)) {
                return;
            }
            RiskDeviceData riskDeviceData = riskDeviceDao.selectOne(keyId);
            int now = DateHelper.getNowSeconds();
            if (riskDeviceData != null) {
                int toScore = riskDeviceData.getScore() - score;
                riskDeviceData.setScore(toScore);
                riskDeviceData.setMtime(now);
                riskDeviceDao.update(riskDeviceData);
//                logger.info("deduct risk device score success update old uid={} minus={} newScore={}", keyId, -score, riskDeviceData.getScore());
            } else {
                riskDeviceDao.insert(new RiskDeviceData(keyId, AVAILABLE_STATUS, keyType, -score, now, now));
//                logger.info("deduct risk device score success insert new keyId={} minus={} newScore={}", keyId, -score, -score);
            }
        } catch (Exception e) {
//            logger.error("deductRiskDeviceScore error keyId={} keyType={} score={} msg={}", keyId, keyType, score, e.getMessage(), e);
        }
    }

    public int getCreditRiskScoreByUid(String uid, String tnId, String ip) {
        if (!IS_CREDIT_INTERCEPT) {
            return MAX_SCORE;
        }
        String key = getKey(uid, tnId, ip);
        Integer toScore = cacheMap.getData(key);
        if (toScore != null) {
            return toScore;
        }
        int totalScore = MAX_SCORE;
        int status = AVAILABLE_STATUS;
        RiskUserData riskUserData = riskUserDao.selectOne(uid);
        if (riskUserData != null) {
            totalScore = riskUserData.getScore();
            status = riskUserData.getStatus();
        }
        if (status == WHITE_STATUS) {
            cacheMap.cacheData(key, MAX_SCORE);
            return MAX_SCORE;
        }
        if (!StringUtils.isEmpty(tnId)) {
            RiskDeviceData tnDeviceData = riskDeviceDao.selectOne(tnId);
            if (tnDeviceData != null) {
                totalScore = totalScore + tnDeviceData.getScore();
            }
        }
        if (!StringUtils.isEmpty(ip)) {
            RiskDeviceData ipDeviceData = riskDeviceDao.selectOne(ip);
            if (ipDeviceData != null) {
                totalScore = totalScore + ipDeviceData.getScore();
            }
        }
        toScore = Math.max(totalScore, 0);
        cacheMap.cacheData(key, toScore);
        return toScore;
    }

    public int getCreditRiskScoreByThirdUid(String thirdUid, String tnId, String ip) {
        if (!IS_CREDIT_INTERCEPT) {
            return MAX_SCORE;
        }

        String key = getKey(thirdUid, tnId, ip);
        Integer toScore = cacheMap.getData(key);
        if (toScore != null) {
            return toScore;
        }

        int totalScore = MAX_SCORE;
        int status = AVAILABLE_STATUS;
        RiskUserData riskUserData = riskUserDao.selectOneByThirdUid(thirdUid);
        if (riskUserData != null) {
            totalScore = riskUserData.getScore();
            status = riskUserData.getStatus();
        }
        if (status == WHITE_STATUS) {
            cacheMap.cacheData(key, MAX_SCORE);
            return MAX_SCORE;
        }

        if (!StringUtils.isEmpty(tnId)) {
            RiskDeviceData tnDeviceData = riskDeviceDao.selectOne(tnId);
            if (tnDeviceData != null) {
                totalScore = totalScore + tnDeviceData.getScore();
            }
        }

        if (!StringUtils.isEmpty(ip)) {
            RiskDeviceData ipDeviceData = riskDeviceDao.selectOne(ip);
            if (ipDeviceData != null) {
                totalScore = totalScore + ipDeviceData.getScore();
            }
        }

        toScore = Math.max(totalScore, 0);
        cacheMap.cacheData(key, toScore);
        return toScore;
    }


    public void creditRisk(String uid, String tnId, String ip, int actionType, int userNum, int deviceNum, int ipNum) {
//        handleRiskUserScore(uid, -userNum);
//        deductDeviceScore(tnId, deviceNum);
//        deductIpScore(ip, ipNum);
    }


    public void asyncCreditRisk(String uid, String tnId, String ip, int actionType, int userNum, int deviceNum, int ipNum) {
//        BaseTaskFactory.getFactory().addSlow(new Task() {
//            @Override
//            protected void execute() {
//                creditRisk(uid, tnId, ip, actionType, userNum, deviceNum, ipNum);
//            }
//        });
    }


    private String getKey(String keyId, String tnId, String ip) {
        StringBuilder sb = new StringBuilder();
        if (!StringUtils.isEmpty(keyId)) {
            sb.append(keyId);
        }
        sb.append("-");
        if (!StringUtils.isEmpty(tnId)) {
            sb.append(tnId);
        }
        sb.append("-");
        if (!StringUtils.isEmpty(ip)) {
            sb.append(ip);
        }
        return sb.toString();
    }

    /**
     * 重置用户积分
     *
     * @param uid
     * @param score
     */
    public void reSetRiskUserScore(String uid, Integer score, Integer status) {
        try {
            RiskUserData riskUserData = riskUserDao.selectOne(uid);
            if (riskUserData != null) {
                int oldScore = riskUserData.getScore();
                int oldStatus = riskUserData.getStatus();
                int toStatus = status == null ? riskUserData.getStatus() : status;
                if (score != null) {
                    riskUserData.setScore(Math.max(Math.min(score, CreditRiskService.MAX_SCORE), 0));
                }
                riskUserData.setStatus(toStatus);
                riskUserData.setMtime(DateHelper.getNowSeconds());
                riskUserDao.update(riskUserData);
                logger.info("reset update risk user success  uid={} oldScore={} oldStatus={} newScore={} newStatus={}", uid, oldScore, oldStatus, riskUserData.getScore(), riskUserData.getStatus());
            } else {
                JSONObject jsonObject = actorDao.findActorJSONFromDB(uid);
                if (jsonObject != null) {
                    String thirdUid = jsonObject.getString("uid");
                    int loginType = jsonObject.getIntValue("login_type");
                    if (!StringUtils.isEmpty(thirdUid) && loginType > 0) {
                        int now = DateHelper.getNowSeconds();
                        int toScore = score == null ? CreditRiskService.MAX_SCORE : Math.max(Math.min(score, CreditRiskService.MAX_SCORE), 0);
                        int toStatus = status == null ? AVAILABLE_STATUS : status;
                        riskUserData = new RiskUserData(uid, thirdUid, toStatus, loginType, toScore, now, now);
                        riskUserDao.insert(riskUserData);
                        logger.info("reset insert risk user success  uid={}  newScore={} newStatus={}", uid, riskUserData.getScore(), riskUserData.getStatus());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("reSetRiskUserScore error uid={} score={} status={} msg={}", uid, score, status, e.getMessage(), e);
        }
    }


    /**
     * 重置图灵id，或者ip积分
     *
     * @param keyId
     * @param keyType
     * @param score
     */
    public void reSetRiskDeviceScore(String keyId, int keyType, Integer score) {
        try {
            if (StringUtils.isEmpty(keyId) || score == null) {
                return;
            }
            RiskDeviceData riskDeviceData = riskDeviceDao.selectOne(keyId);
            int now = DateHelper.getNowSeconds();
            if (riskDeviceData != null) {
                riskDeviceData.setScore(score);
                riskDeviceData.setMtime(now);
                riskDeviceDao.update(riskDeviceData);
                logger.info("reset risk device score success update keyId={} newScore={}", keyId, riskDeviceData.getScore());
            } else {
                riskDeviceDao.insert(new RiskDeviceData(keyId, AVAILABLE_STATUS, keyType, score, now, now));
                logger.info("reset risk device score success insert new keyId={}newScore={}", keyId, score);
            }
        } catch (Exception e) {
            logger.error("reSetRiskDeviceScore error keyId={} keyType={} score={} msg={}", keyId, keyType, score, e.getMessage(), e);
        }
    }

    private int getAfterScoreByUid(String uid, String tnId, String ip) {
        int totalScore = MAX_SCORE;
        int status = AVAILABLE_STATUS;
        RiskUserData riskUserData = riskUserDao.selectOne(uid);
        if (riskUserData != null) {
            totalScore = riskUserData.getScore();
            status = riskUserData.getStatus();
        }
        if (status == WHITE_STATUS) {
            return 9999;
        }
        if (status == UNAVAILABLE_STATUS) {
            return -9999;
        }
        if (!StringUtils.isEmpty(tnId)) {
            RiskDeviceData tnDeviceData = riskDeviceDao.selectOne(tnId);
            if (tnDeviceData != null) {
                totalScore = totalScore + tnDeviceData.getScore();
            }
        }
        if (!StringUtils.isEmpty(ip)) {
            RiskDeviceData ipDeviceData = riskDeviceDao.selectOne(ip);
            if (ipDeviceData != null) {
                totalScore = totalScore + ipDeviceData.getScore();
            }
        }
        return Math.max(totalScore, 0);
    }
}
