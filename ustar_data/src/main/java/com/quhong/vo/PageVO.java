package com.quhong.vo;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/4
 */
public class PageVO<T> {

    protected List<T> list = Collections.emptyList();
    private String nextUrl = "";

    public PageVO() {
    }

    public PageVO(List<T> list) {
        this.list = list;
    }

    public PageVO(List<T> list, String nextUrl) {
        this.list = list;
        this.nextUrl = nextUrl;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }

    public void setNextUrl(int page, int pageSize) {
        this.nextUrl = list.size() < pageSize ? "" : String.valueOf(page + 1);
    }
}
