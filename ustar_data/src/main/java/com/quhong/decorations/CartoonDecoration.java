package com.quhong.decorations;

import com.quhong.mongo.dao.JoinCartonDao;
import com.quhong.mongo.dao.JoinSourceDao;
import com.quhong.mongo.data.JoinCartonData;
import com.quhong.mongo.data.JoinSourceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

@Component
public class CartoonDecoration {
    private static final Logger logger = LoggerFactory.getLogger(CartoonDecoration.class);
    // vip的不移除
    private static final List<Integer> vipList = Arrays.asList(1, 2, 3, 4, 5, 6);

    @Autowired
    private JoinCartonDao cartonDao;
    @Autowired
    private JoinSourceDao sourceDao;

    /**
     * 添加svip资源
     *
     * @param uid
     * @param cartoonId
     * @param endTime
     */
    public void putOnSvip(String uid, int cartoonId, long endTime, boolean isNewSubscription) {
        logger.info("join cartoon put on. cartoonId={} endTime={} uid={}", cartoonId, endTime, uid);
        List<JoinCartonData> list = cartonDao.findList(uid);
        boolean putOn = false;
        if(isNewSubscription) {
            // 检查并移除相关的坐骑
            checkAndRemove(list, uid, cartoonId);
            putOn = checkPutOn(list, uid, cartoonId);
        }
        // list是已经经过checkAndRemove处理过的数据
        for (JoinCartonData data : list) {
            if (data.getJoin_carton_id() == 1) {
                // cartoon id 为1的下掉
                cartonDao.updateCartoonStatus(data, 0);
            }
        }
        // 更新新的坐骑
        cartonDao.updateCartoon(uid, cartoonId, endTime, putOn);
    }

    private boolean checkAndRemove(List<JoinCartonData> list, String uid, int cartoonId) {
        if (list == null) {
            return false;
        }
        Iterator<JoinCartonData> iter = list.iterator();
        boolean removeVip = false;
        while (iter.hasNext()) {
            JoinCartonData cartonData = iter.next();
            int itemCartoonId = cartonData.getJoin_carton_id();
            JoinSourceData sourceData = sourceDao.getSourceData(cartonData.getJoin_carton_id());
            if (sourceData == null) {
                logger.error("join cartoon put on. can not find source data. cartoonId={} uid+{}", cartonData.getJoin_carton_id(), uid);
                continue;
            }
            if (sourceData.getJoin_type() == 2) {
                // honor不处理
                continue;
            }
            if (vipList.indexOf(itemCartoonId) != -1) {
                removeVip = takeOffVipCartoons(cartonData);
                continue;
            }
            if (itemCartoonId == cartoonId) {
                continue;
            }
            if (itemCartoonId >= 40) {
                continue;
            }
            // 其他的移除
            cartonDao.removeCartoon(cartonData);
            iter.remove();
        }
        return removeVip;
    }

    private boolean checkPutOn(List<JoinCartonData> list, String uid, int cartoonId) {
        if (list == null) {
            return true;
        }
        boolean hasNoVipOn = false;
        Iterator<JoinCartonData> iter = list.iterator();
        while (iter.hasNext()) {
            JoinCartonData cartonData = iter.next();
            int itemCartoonId = cartonData.getJoin_carton_id();
            if(itemCartoonId == 1){
                continue;
            }
            if (vipList.indexOf(itemCartoonId) == -1) {
                if(cartonData.getStatus() > 0) {
                    hasNoVipOn = true;
                }
            }
        }
        // 有非vip佩戴，则不自动佩戴
        if(hasNoVipOn){
            return false;
        }
        return true;
    }

    private boolean takeOffVipCartoons(JoinCartonData cartonData) {
        if (cartonData.getStatus() > 0) {
            logger.info("take off vip cartoon. cartoonId={} uid={}", cartonData.getJoin_carton_id(), cartonData.getUid());
            cartonDao.updateCartoonStatus(cartonData, 0);
            return true;
        }
        return false;
    }

    /**
     * 卸载掉对应动画，但并不删除
     *
     * @param uid
     * @param cartoonId
     */
    public void takeOff(String uid, int cartoonId) {
        logger.info("take off cartoon. cartoonId={} uid={}", cartoonId, uid);
        JoinCartonData cartonData = cartonDao.findCartoonData(uid, cartoonId);
        if (cartonData == null) {
            return;
        }
        cartonDao.removeCartoon(cartonData);
    }
}
