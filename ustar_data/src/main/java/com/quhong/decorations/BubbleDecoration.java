package com.quhong.decorations;

import com.quhong.mongo.dao.BubbleDao;
import com.quhong.mongo.data.BubbleData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class BubbleDecoration {
    private static final Logger logger = LoggerFactory.getLogger(BubbleDecoration.class);

    private static final List<Integer> TAKE_OFF_LIST = Arrays.asList(1, 5, 6);

    @Autowired
    private BubbleDao bubbleDao;


    public void putOnSvip(String uid, int bubbleId, long endTime, boolean isNewSubscription) {
        logger.info("bubble put on. bubbleId={} endTime={} uid={}", bubbleId, endTime, uid);
        boolean putOn = false;
        if(isNewSubscription) {
            putOn = checkAndRemove(uid);
        }
        // 更新新的坐骑
        bubbleDao.updateBubble(uid, bubbleId, endTime, putOn);
    }

    private boolean checkAndRemove(String uid){
        // bubbleId为1，卸载掉
        boolean putOn;
        boolean hasNoVipOn = false;
        BubbleData bubbleData = bubbleDao.findDataByStatus(uid, 1);
        if(bubbleData != null){
            if(TAKE_OFF_LIST.indexOf(bubbleData.getBuddle_id()) != -1){
                bubbleDao.updateBubbleStatus(bubbleData, 0);
            }else if(bubbleData.getBuddle_id() != 1){
                hasNoVipOn = true;
            }
        }
        if(hasNoVipOn){
            putOn = false;
        }else{
            putOn = true;
        }
        return putOn;
    }

    public void takeOff(String uid, int bubbleId) {
        logger.info("take off bubble. bubbleId={} uid={}", bubbleId, uid);
        BubbleData bubbleData = bubbleDao.findData(uid, bubbleId);
        if(bubbleData == null){
            return;
        }
        bubbleDao.removeBubble(bubbleData);
    }

}
