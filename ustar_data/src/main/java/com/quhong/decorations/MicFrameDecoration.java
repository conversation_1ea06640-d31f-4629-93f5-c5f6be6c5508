package com.quhong.decorations;

import com.quhong.mongo.dao.ExtraMicFrameDao;
import com.quhong.mongo.dao.MicFrameDao;
import com.quhong.mongo.dao.VipMicFrameDao;
import com.quhong.mongo.data.ExtraMicFrameData;
import com.quhong.mongo.data.MicFrameData;
import com.quhong.mongo.data.VipMicFrameData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MicFrameDecoration {
    private static final Logger logger = LoggerFactory.getLogger(MicFrameDecoration.class);

    @Autowired
    private VipMicFrameDao vipMicFrameDao;
    @Autowired
    private MicFrameDao micFrameDao;
    @Autowired
    private ExtraMicFrameDao extraMicFrameDao;

    public MicFrameDecoration(){

    }

    public void putOnSvip(String uid, int micFrameId, long endTime, boolean isNewSubscription) {
        logger.info("mic frame put on. micFrameId={} endTime={} uid={}", micFrameId, endTime, uid);
        boolean putOn = false;
        if(isNewSubscription) {
           putOn = checkPutOn(uid);
        }
        extraMicFrameDao.updateMicFrame(uid, micFrameId, endTime, putOn);
    }

    private boolean checkPutOn(String uid){
        VipMicFrameData vipMicFrameData = vipMicFrameDao.findDataByStatus(uid, 1);
        if(vipMicFrameData != null){
            // 下掉vip mic frame data.
            logger.info("take off vip frame data. vipMicFrameId={} uid={}", vipMicFrameData.getMic_id(), uid);
            vipMicFrameDao.updateMicFrameStatus(vipMicFrameData, 0);
            return true;
        }
        MicFrameData micFrameData = micFrameDao.findDataByStatus(uid, 1);
        ExtraMicFrameData extraMicFrameData = extraMicFrameDao.findDataByStatus(uid, 1);
        if(micFrameData == null && extraMicFrameData == null){
            return true;
        }
        return false;
    }

    public void takeOff(String uid, int micFrameId) {
        logger.info("take off mic frame. micFrameId={} uid={}", micFrameId, uid);
        ExtraMicFrameData micFrameData = extraMicFrameDao.findData(uid, micFrameId);
        if(micFrameData == null){
            return;
        }
        extraMicFrameDao.removeMicFrame(micFrameData);
    }
}
