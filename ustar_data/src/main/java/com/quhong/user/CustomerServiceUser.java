package com.quhong.user;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.SpringUtils;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 客服id
 */
public class CustomerServiceUser {
    private Set<String> CUSTOMER_USER = null; // 后续可能做多客服，这里预留
    private static String uid;
    private static final String uid10000 = ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "655c4c24b661b86b85455f3b";

    public static String getUid() {
        if (uid == null) {
            uid = SpringUtils.getProperty("customer_service_uid");
        }
        return null == uid ? uid10000 : uid;
    }



    public Set<String> getCUSTOMER_USER() {
        return CUSTOMER_USER;
    }

    public void setCUSTOMER_USER(Set<String> CUSTOMER_USER) {
        this.CUSTOMER_USER = CUSTOMER_USER;
    }
}
