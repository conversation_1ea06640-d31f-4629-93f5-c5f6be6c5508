package com.quhong.mongo.dao;

import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.FollowRoomData;
import com.quhong.mongo.data.JoinSourceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Lazy
@Component
public class FollowRoomDao {
    private static final Logger logger = LoggerFactory.getLogger(FollowRoomDao.class);
    public static final String TABLE_NAME = "follow_room";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public List<FollowRoomData> selectPage(String uid, int start, int size) {
        try {
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(Criteria.where("uid").is(uid)),
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "c_time")),
                    Aggregation.skip(start),
                    Aggregation.limit(size)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, FollowRoomData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取房间的全部粉丝
     */
    public List<FollowRoomData> getRoomAllFans(String roomId) {
        try {
            Query query = new Query(Criteria.where("room_id").is(roomId));
            return mongoTemplate.find(query, FollowRoomData.class);
        } catch (Exception e) {
            logger.error("get room all fans error. roomId={}", roomId);
            return null;
        }
    }

    /**
     * 获取房间粉丝数量
     */
    public int getRoomFansCount(String roomId) {
        try {
            Criteria criteria = Criteria.where("room_id").is(roomId);
            return (int) mongoTemplate.count(new Query(criteria), FollowRoomData.class);
        } catch (Exception e) {
            logger.error("get room fans count error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    public List<FollowRoomData> selectPageByRoomId(String roomId, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            criteria.and("room_id").is(roomId);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, FollowRoomData.class);
        } catch (Exception e) {
            logger.error("selectPageByRoomId error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<FollowRoomData> selectPage(String uid, String roomId, int start, int size) {
        try {
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(Criteria.where("uid").is(uid).and("room_id").is(roomId)),
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "c_time")),
                    Aggregation.skip(start),
                    Aggregation.limit(size)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, FollowRoomData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public List<FollowRoomLookUpData> selectLookUpPage(String uid, int start, int size) {
        try {
            List<AggregationOperation> operationList = new ArrayList<>();
            AggregationOperation match = Aggregation.match(Criteria.where("uid").is(uid));
            LookupOperation roomLookupOperation = LookupOperation.newLookup()
                    .from(MongoRoomDao.TABLE_NAME)
                    .localField("room_id")
                    .foreignField("rid")
                    .as("room_info");

            UnwindOperation unwindOperation = Aggregation.unwind("room_info");

            ProjectionOperation project = Aggregation.project()
                    .and("room_info.rid").as("room_id")
                    .and("room_info.online").as("online");

            // 分页与排序操作，字段未在上面体现出来
            SkipOperation skip = Aggregation.skip((long) start);
            LimitOperation limit = Aggregation.limit(size);
            SortOperation sort = Aggregation.sort(Sort.Direction.DESC, "online");

            operationList.add(match);
            operationList.add(roomLookupOperation);
            operationList.add(unwindOperation);
            operationList.add(project);
            operationList.add(sort);
            operationList.add(skip);
            operationList.add(limit);

            Aggregation aggregation = Aggregation.newAggregation(operationList);
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, FollowRoomLookUpData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("selectLookUpPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public UpdateResult upsert(String roomId, String uid) {
        try {
            Query query = new Query(Criteria.where("uid").is(uid).and("room_id").is(roomId));
            Update update = new Update();
            update.set("ctime", DateHelper.getNowSeconds());
            return mongoTemplate.upsert(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("upsert follow room error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
            return null;
        }
    }

    public void delete(String roomId, String uid) {
        try {
            Query query = new Query(Criteria.where("uid").is(uid).and("room_id").is(roomId));
            mongoTemplate.remove(query, TABLE_NAME);
        } catch (Exception e) {
            logger.error("delete follow room error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public long deleteByUid(String uid) {
        try {
            Query query = new Query(Criteria.where("uid").is(uid));
            DeleteResult deleteResult = mongoTemplate.remove(query, TABLE_NAME);
            return deleteResult.getDeletedCount();
        } catch (Exception e) {
            logger.error("deleteByUid error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    public long deleteByRoomId(String roomId) {
        try {
            Query query = new Query(Criteria.where("room_id").is(roomId));
            DeleteResult deleteResult = mongoTemplate.remove(query, TABLE_NAME);
            return deleteResult.getDeletedCount();
        } catch (Exception e) {
            logger.error("deleteByRoomId error. roomId={} {}", roomId, e.getMessage(), e);
            return 0;
        }
    }

    public static class FollowRoomLookUpData {
        String room_id;
        int online;

        public String getRoom_id() {
            return room_id;
        }

        public void setRoom_id(String room_id) {
            this.room_id = room_id;
        }

        public int getOnline() {
            return online;
        }

        public void setOnline(int online) {
            this.online = online;
        }

        @Override
        public String toString() {
            return "FollowRoomLookUpData{" +
                    "room_id='" + room_id + '\'' +
                    ", online=" + online +
                    '}';
        }
    }

}
