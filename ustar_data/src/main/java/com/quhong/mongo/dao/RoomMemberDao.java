package com.quhong.mongo.dao;

import com.mongodb.client.result.UpdateResult;
import com.quhong.cache.CacheMap;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.RoomRoleData;
import com.quhong.enums.RoomRoleType;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.RoomMemberData;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 房间管理
 */
@Lazy
@Component
public class RoomMemberDao {
    private static final Logger logger = LoggerFactory.getLogger(RoomMemberDao.class);
    private final CacheMap<String, RoomRoleData> cacheMap = new CacheMap<>(60 * 1000L);

    public static final String TABLE_NAME = "rmember";

    public static final int UTYPE_MANAGER = 2;       // 管理员
    public static final int UTYPE_MEMBER = 1;        // 房间会员
    public static final int UTYPE_VICE_HOST = 4;     // 副房主

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource
    private MongoRoomDao roomDao;

    @PostConstruct
    public void postInit() {
        this.cacheMap.start();
    }

    public String getCacheKey(String roomId, String uid) {
        return roomId + "_" + uid;
    }

    public RoomRoleData getRoleDataFromCache(String roomId, String uid) {
        RoomRoleData roleData = cacheMap.getData(getCacheKey(roomId, uid));
        if (null != roleData) {
            return roleData;
        }
        return getRoleData(roomId, uid);
    }

    public void delRoomMember(String roomId, String aid) {
        UpdateResult updateResult = removeRoomMember(roomId, aid);
        int rowCount = (int) updateResult.getModifiedCount();
        if (rowCount > 0) {
            roomDao.updateMemnum(roomId, -rowCount);
        }
    }

    /**
     * 海阔去角色的map
     *
     * @param roomId   房间id
     * @param actorSet
     * @return
     */
    public Map<String, RoomRoleData> getRoleDataMap(String roomId, Set<String> actorSet) {
        Map<String, RoomRoleData> retMap = new HashMap<>();
        Set<String> remainSet = new HashSet<>();
        for (String uid : actorSet) {
            RoomRoleData roomRoleData = cacheMap.getData(getCacheKey(roomId, uid));
            if (null != roomRoleData) {
                retMap.put(uid, roomRoleData);
            } else {
                remainSet.add(uid);
            }
        }
        if (!remainSet.isEmpty()) {
            List<RoomMemberData> list = findList(roomId, remainSet);
            for (RoomMemberData memberData : list) {
                RoomRoleData roomRoleData = createRoleData(roomId, memberData.getAid(), memberData);
                retMap.put(roomRoleData.getUid(), roomRoleData);
            }
        }
        // 填充房主
        RoomRoleData hostData = createHostData(roomId);
        retMap.put(hostData.getUid(), hostData);
        return retMap;
    }

    public RoomRoleData getRoleData(String roomId, String uid) {
        if (isRoomHost(roomId, uid)) {
            RoomRoleData roomRoleData = new RoomRoleData();
            roomRoleData.setRole(RoomRoleType.HOST);
            return roomRoleData;
        }
        RoomMemberData memberData = findData(roomId, uid);
        return createRoleData(roomId, uid, memberData);
    }

    public RoomRoleData createHostData(String roomId) {
        RoomRoleData roomRoleData = new RoomRoleData();
        roomRoleData.setUid(RoomUtils.getRoomHostId(roomId));
        roomRoleData.setRole(RoomRoleType.HOST);
        return roomRoleData;
    }

    public RoomRoleData createRoleData(String roomId, String uid, RoomMemberData memberData) {
        RoomRoleData roomRoleData = new RoomRoleData();
        roomRoleData.setUid(uid);
        if (isRoomHost(roomId, uid)) {
            roomRoleData.setRole(RoomRoleType.HOST);
            return roomRoleData;
        }
        if (memberData != null) {
            switch (memberData.getUtype()) {
                case UTYPE_VICE_HOST:
                    roomRoleData.setRole(RoomRoleType.MANAGER);
                    roomRoleData.setViceHost(1);
                    break;
                case UTYPE_MANAGER:
                    roomRoleData.setRole(RoomRoleType.MANAGER);
                    break;
                case UTYPE_MEMBER:
                    roomRoleData.setRole(RoomRoleType.MEMBER);
                    break;
                default:
                    roomRoleData.setRole(RoomRoleType.AUDIENCE);
                    break;
            }
        } else {
            roomRoleData.setRole(RoomRoleType.AUDIENCE);
        }
        cacheMap.cacheData(getCacheKey(roomId, uid), roomRoleData);
        return roomRoleData;
    }

    public int getRoleContainMember(String roomId, String uid) {
        if (isRoomHost(roomId, uid)) {
            return RoomRoleType.HOST;
        }
        RoomMemberData memberData = findData(roomId, uid);
        if (memberData != null) {
            if (memberData.getUtype() == UTYPE_MANAGER || memberData.getUtype() == UTYPE_VICE_HOST) {
                return RoomRoleType.MANAGER;
            } else if (memberData.getUtype() == UTYPE_MEMBER) {
                return RoomRoleType.MEMBER;
            }
        }
        return RoomRoleType.AUDIENCE;
    }

    public int getRoleContainMember(RoomMemberData memberData, String roomId, String uid) {
        if (isRoomHost(roomId, uid)) {
            return RoomRoleType.HOST;
        }
        if (memberData != null) {
            if (memberData.getUtype() == UTYPE_MANAGER || memberData.getUtype() == UTYPE_VICE_HOST) {
                return RoomRoleType.MANAGER;
            } else if (memberData.getUtype() == UTYPE_MEMBER) {
                return RoomRoleType.MEMBER;
            }
        }
        return RoomRoleType.AUDIENCE;
    }

    public RoomMemberData findManagerData(String roomId, String uid) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(uid).and("valid").is(1).and("utype").is(UTYPE_MANAGER);
            return mongoTemplate.findOne(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("room member find data error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return null;
    }

    public List<RoomMemberData> findAdminList(String roomId) {
        try {
//            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1).and("utype").gte(UTYPE_MANAGER);
            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1).and("utype").in(Arrays.asList(UTYPE_MANAGER, UTYPE_VICE_HOST));
            return mongoTemplate.find(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("find room member data list error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return null;
    }

    public List<RoomMemberData> listViceHostManager(String roomId) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1).and("utype").is(UTYPE_VICE_HOST);
            return mongoTemplate.find(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("room member find data error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public Boolean isViceHostManager(String uid, String roomId) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(uid).and("valid").is(1).and("utype").is(UTYPE_VICE_HOST);
            return mongoTemplate.findOne(new Query(criteria), RoomMemberData.class) != null;
        } catch (Exception e) {
            logger.error("find room member data error. uid={} roomId={} {}", uid, roomId, e.getMessage(), e);
        }
        return false;
    }

    public List<RoomMemberData> selectPage(String aid, int start, int size) {
        try {
            Criteria criteria = Criteria.where("aid").is(aid).and("valid").is(1);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "mtime")),
                    Aggregation.skip(start),
                    Aggregation.limit(size)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, RoomMemberData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public List<RoomMemberData> selectRoomMemberPage(String roomId, String key, int start, int size) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("name").regex(Pattern.compile(".*?" + key + ".*")).and("valid").is(1);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "utype")),
                    Aggregation.skip(start),
                    Aggregation.limit(size)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, RoomMemberData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

//    public List<RoomMemberData> selectRoomMemberPage(String roomId, int start, int size) {
//        try {
//            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1);
//            Aggregation aggregation = Aggregation.newAggregation(
//                    Aggregation.match(criteria),
//                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "utype").and(Sort.by(Sort.Direction.ASC, "mtime"))),
//                    Aggregation.skip(start),
//                    Aggregation.limit(size)
//            );
//            return mongoTemplate.aggregate(aggregation, TABLE_NAME, RoomMemberData.class).getMappedResults();
//        } catch (Exception e) {
//            logger.error("selectPage error.{}", e.getMessage(), e);
//        }
//        return new ArrayList<>();
//    }

    public List<RoomMemberData> selectRoomMemberPage(String roomId, int start, int size) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1);
            Query query = new Query(criteria);
            Sort sort =Sort.by(Sort.Direction.DESC, "utype");
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, RoomMemberData.class);
        } catch (Exception e) {
            logger.error("selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public List<RoomMemberData> selectMemberPage(String uid, int start, int size) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("valid").is(1);
            Query query = new Query(criteria);
            query.skip(start).limit(size);
            return mongoTemplate.find(query, RoomMemberData.class);
        } catch (Exception e) {
            logger.error("select member room page error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public List<RoomMemberData> findRoomMemberPage(String roomId, String aid, int start, int size) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(aid).and("utype").in(Arrays.asList(2, 4)).and("valid").is(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "utype");
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, RoomMemberData.class);
        } catch (Exception e) {
            logger.error("select member room page error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取房间管理员及副房主
     */
    public List<RoomMemberData> findRoomAdminAndVice(String roomId) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("utype").in(Arrays.asList(2, 4)).and("valid").is(1);
            Query query = new Query(criteria);
            return mongoTemplate.find(query, RoomMemberData.class);
        } catch (Exception e) {
            logger.error("findRoomAdminAndViceerror.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<RoomMemberData> findRoomMemberPage(String roomId, int start, int size) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("utype").is(1).and("valid").is(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.ASC, "mtime");
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, RoomMemberData.class);
        } catch (Exception e) {
            logger.error("select member room page error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public int getMemberCount(String roomId) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1);
            return (int) mongoTemplate.count(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("get room member count error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int findAdminCount(String roomId) {
        try {
//            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1).and("utype").gte(UTYPE_MANAGER);
            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1).and("utype")
                    .in(Arrays.asList(UTYPE_MANAGER, UTYPE_VICE_HOST));
            return (int) mongoTemplate.count(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("find room admin count error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    public int getBecomeMemberCount(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("valid").is(1);
            return (int) mongoTemplate.count(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("get become member count error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    public RoomMemberData findData(String roomId, String uid) {
        try {
            if (StringUtils.isEmpty(roomId)) {
                return null;
            }
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(uid).and("valid").is(1);
            return mongoTemplate.findOne(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("room member find data error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return null;
    }

    public RoomMemberData findDataValidAndInvalid(String roomId, String uid) {
        try {
            if (StringUtils.isEmpty(roomId)) {
                return null;
            }
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(uid);
            return mongoTemplate.findOne(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("room member find data error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return null;
    }

    public RoomMemberData findData(String roomId, int rid) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("rid").is(rid).and("valid").is(1);
            return mongoTemplate.findOne(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("room member find data error. roomId={} rid={} {}", roomId, rid, e.getMessage(), e);
        }
        return null;
    }

    public int findMemberUtype(String roomId, String uid) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(uid).and("valid").is(1);
            RoomMemberData data = mongoTemplate.findOne(new Query(criteria), RoomMemberData.class);
            return data != null ? data.getUtype() : 0;
        } catch (Exception e) {
            logger.error("room member find data error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
            return 0;
        }
    }

    public List<RoomMemberData> findList(String roomId, Collection<String> remainSet) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").in(remainSet).and("valid").is(1);
            return mongoTemplate.find(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("room member find data error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return null;
    }

    public RoomMemberData findData(String roomId, String uid, int utype) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(uid).and("utype").is(utype).and("valid").is(1);
            return mongoTemplate.findOne(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("room member find data error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return null;
    }

    public static boolean isRoomHost(String roomId, String uid) {
        if (roomId != null && roomId.length() >= 2 && roomId.substring(2).equals(uid)) {
            return true;
        }
        return false;
    }

    public List<RoomMemberDao.RoomMemberLookUpData> selectLookUpPage(String uid, int start, int size) {
        try {
            List<AggregationOperation> operationList = new ArrayList<>();
            AggregationOperation match = Aggregation.match(Criteria.where("aid").is(uid).and("valid").is(1));
            LookupOperation roomLookupOperation = LookupOperation.newLookup()
                    .from(MongoRoomDao.TABLE_NAME)
                    .localField("roomId")
                    .foreignField("rid")
                    .as("room_info");

            UnwindOperation unwindOperation = Aggregation.unwind("room_info");

            ProjectionOperation project = Aggregation.project()
                    .and("room_info.rid").as("room_id")
                    .and("room_info.online").as("online");

            // 分页与排序操作，字段未在上面体现出来
            SkipOperation skip = Aggregation.skip((long) start);
            LimitOperation limit = Aggregation.limit(size);
            SortOperation sort = Aggregation.sort(Sort.Direction.DESC, "online");

            operationList.add(match);
            operationList.add(roomLookupOperation);
            operationList.add(unwindOperation);
            operationList.add(project);
            operationList.add(sort);
            operationList.add(skip);
            operationList.add(limit);

            Aggregation aggregation = Aggregation.newAggregation(operationList);
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, RoomMemberDao.RoomMemberLookUpData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("selectLookUpPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public UpdateResult updateUtype(String roomId, String aid, int utype) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(aid).and("valid").is(1);
            Update update = new Update();
            update.set("utype", utype);
            update.set("mtime", DateHelper.getNowSeconds());
            return mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update member data error. roomId={} aid={} {}", roomId, aid, e.getMessage(), e);
            return null;
        }
    }

    public UpdateResult removeRoomMember(String roomId, String aid) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(aid).and("valid").is(1);
            Update update = new Update();
            update.set("mtime", DateHelper.getNowSeconds());
            update.set("valid", 0);
            return mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("remove room member data error. roomId={} aid={} {}", roomId, aid, e.getMessage(), e);
            return null;
        }
    }

    public UpdateResult cancelViceHost(String roomId, String aid) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(aid).and("valid").is(1).and("utype").is(UTYPE_VICE_HOST);
            Update update = new Update();
            update.set("utype", UTYPE_MANAGER);
            update.set("mtime", DateHelper.getNowSeconds());
            return mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update member data error. roomId={} aid={} {}", roomId, aid, e.getMessage(), e);
            return null;
        }
    }

    public UpdateResult cancelAdmin(String roomId, String aid) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("aid").is(aid).and("valid").is(1).and("utype").gte(UTYPE_MANAGER);
            Update update = new Update();
            update.set("utype", UTYPE_MEMBER);
            update.set("mtime", DateHelper.getNowSeconds());
            return mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update member data error. roomId={} aid={} {}", roomId, aid, e.getMessage(), e);
            return null;
        }
    }

    public List<RoomMemberData> getMemberRoomPage(String uid, String key, int start, int pageSize) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("valid").is(1).orOperator(Criteria.where("rrid").is(key), Criteria.where("rname").regex(Pattern.compile(".*?" + key + ".*")));
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.skip(start),
                    Aggregation.limit(pageSize)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, RoomMemberData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("get member room data page error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<RoomMemberData> getRoomAllMembers(String roomId) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1);
            return mongoTemplate.find(new Query(criteria), RoomMemberData.class);
        } catch (Exception e) {
            logger.error("get room all members error. roomId={} {}", roomId, e.getMessage(), e);
            return null;
        }
    }

    public void save(RoomMemberData memberData) {
        try {
            mongoTemplate.save(memberData);
        } catch (Exception e) {
            logger.error("save room member data error. roomId={} aid={} {}", memberData.getRoomId(), memberData.getAid(), e.getMessage(), e);
        }
    }


    /**
     * 删除我的房间会员
     *
     * @param roomId
     */
    public int delAllRoomMember(String roomId) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId).and("valid").is(1);
            Update update = new Update();
            update.set("valid", 0);
            UpdateResult updateResult = mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
            long modifiedCount = updateResult.getModifiedCount();
            return (int) modifiedCount;

        } catch (Exception e) {
            logger.error("delAllRoomMember error. roomId={}", roomId, e);
            return 0;
        }
    }

    /**
     * 删除我加入的房间会员
     *
     * @param uid
     */
    public int delJoinAllRoomMember(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("valid").is(1);
            Update update = new Update();
            update.set("valid", 0);
            UpdateResult updateResult = mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
            long modifiedCount = updateResult.getModifiedCount();
            return (int) modifiedCount;

        } catch (Exception e) {
            logger.error("delAllRoomMember error. uid={}", uid, e);
            return 0;
        }
    }


    public static class RoomMemberLookUpData {
        String room_id;
        int online;

        public String getRoom_id() {
            return room_id;
        }

        public void setRoom_id(String room_id) {
            this.room_id = room_id;
        }

        public int getOnline() {
            return online;
        }

        public void setOnline(int online) {
            this.online = online;
        }

        @Override
        public String toString() {
            return "RoomMemberLookUpData{" +
                    "room_id='" + room_id + '\'' +
                    ", online=" + online +
                    '}';
        }
    }
}
