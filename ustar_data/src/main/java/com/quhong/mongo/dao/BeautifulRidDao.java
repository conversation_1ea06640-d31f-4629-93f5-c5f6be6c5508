package com.quhong.mongo.dao;

import com.alibaba.druid.util.StringUtils;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BeautifulRidData;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class BeautifulRidDao {
    private static final Logger logger = LoggerFactory.getLogger(BeautifulRidDao.class);

    public static final String TABLE_NAME = "beautiful_rid";

    public static final int BUFFER_TIME = 24 * 3600;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;

    public int isBeautifulRid(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            return mongoTemplate.exists(new Query(criteria), BeautifulRidData.class) ? 1 : 0;
        } catch (Exception e) {
            logger.error("find beautiful rid data error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    public BeautifulRidData findData(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            return mongoTemplate.findOne(new Query(criteria), BeautifulRidData.class);
        } catch (Exception e) {
            logger.error("find beautiful rid data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public BeautifulRidData findDataByBeautifulRid(String beautifulRid) {
        try {
            BeautifulRidData ridData = null;
            try {
                Criteria criteria2 = Criteria.where("beautiful_rid").is(Integer.parseInt(beautifulRid));
                ridData = mongoTemplate.findOne(new Query(criteria2), BeautifulRidData.class);
            } catch (NumberFormatException ignored) {
            }
            if (null != ridData) {
                return ridData;
            }
            Criteria criteria = Criteria.where("beautiful_rid").is(beautifulRid);
            return mongoTemplate.findOne(new Query(criteria), BeautifulRidData.class);
        } catch (Exception e) {
            logger.error("find beautiful rid data error. beautifulRid={} {}", beautifulRid, e.getMessage(), e);
        }
        return null;
    }

    public void insert(String uid, String alphaRid, int alphaLevel, int realRid, int honorLevel, long endTime, Integer canChangeBeautifulRid) {
        try {
            int now = DateHelper.getNowSeconds();
            BeautifulRidData data = new BeautifulRidData();
            data.setUid(uid);
            data.setReal_rid(realRid);
            data.setBeautiful_rid(alphaRid);
            data.setAlpha_level(alphaLevel);
            data.setC_time(now);
            data.setBuffer_time(endTime + BUFFER_TIME);
            data.setEnd_time(endTime);
            data.setHonor_level(honorLevel);
            if (canChangeBeautifulRid != null) {
                data.setCan_change_beautiful_rid(canChangeBeautifulRid);
            }
            mongoTemplate.insert(data);
        } catch (Exception e) {
            logger.error("insert BeautifulRidData data error. rid={} uid={} {}", alphaRid, uid, e.getMessage(), e);
        }
    }

    public void updateEndTime(String uid, String rid, int alphaLevel, long endTime, int honorLevel, Integer canChangeBeautifulRid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            Update update = new Update();
            update.set("end_time", endTime);
            update.set("beautiful_rid", rid);
            update.set("alpha_level", alphaLevel);
            if (honorLevel > 0) {
                update.set("honor_level", honorLevel);
            }
            if (canChangeBeautifulRid != null) {
                update.set("can_change_beautiful_rid", canChangeBeautifulRid);
            }
            update.set("buffer_time", endTime + BUFFER_TIME);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateEndTime BeautifulRidData data error. rid={} uid={} {}", rid, uid, e.getMessage(), e);
        }
    }

    public void removeBeautifulRidFromDb(BeautifulRidData data) {
        try {
            Criteria criteria = Criteria.where("_id").is(data.get_id());
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("remove BeautifulRid data from db error. b_rid={} uid={} {}", data.getBeautiful_rid(), data.getUid(), e.getMessage(), e);
        }
    }

    public List<BeautifulRidData> listByEndTime(int start, int end) {
        Criteria criteria = Criteria.where("end_time").gt(start).lte(end);
        List<BeautifulRidData> list = mongoTemplate.find(new Query(criteria), BeautifulRidData.class);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("listByEndTime list is null  end = {}", end);
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 是否通知
     */
    public boolean isNotify(String uid) {
        try {
            return Boolean.TRUE.equals(mainRedisTemplate.opsForSet().isMember(getNotifyExpireKey(), uid));
        } catch (Exception e) {
            logger.error("get isNotify error, uid={} msg={}", uid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 增加通知
     */
    public void addNotify(String uid) {
        try {
            mainRedisTemplate.opsForSet().add(getNotifyExpireKey(), uid);
        } catch (Exception e) {
            logger.error("add isNotify error, uid={} msg={}", uid, e.getMessage(), e);
        }
    }

    /**
     * 删除通知
     */
    public void deleteNotify(String uid) {
        try {
            mainRedisTemplate.opsForSet().remove(getNotifyExpireKey(), uid);
        } catch (Exception e) {
            logger.error("delete isNotify error, uid={} msg={}", uid, e.getMessage(), e);
        }
    }

    private String getNotifyExpireKey() {
        return "user_rid_notify_manger";
    }

    /**
     * 荣耀模块相关操作
     */
    public void updateChangeBeautifulRid(String uid, int enableChangeBeautifulRid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            Update update = new Update();
            update.set("can_change_beautiful_rid", enableChangeBeautifulRid);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateChangeBeautifulRid data error. rid={} uid={} {}", uid, e.getMessage(), e);
        }
    }

    // 延续靓号过期时间
    public void continueEndTime(String uid, long endTime) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            Update update = new Update();
            update.set("end_time", endTime);
            update.set("buffer_time", endTime + BUFFER_TIME);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("continueEndTime BeautifulRidData data error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public BeautifulRidData findDataByTime(String uid, long now) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("end_time").gt(now);
            return mongoTemplate.findOne(new Query(criteria), BeautifulRidData.class);
        } catch (Exception e) {
            logger.error("find beautiful rid data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }


    public BeautifulRidData findDataByUidRid(String uid, int rid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("beautiful_rid").is(rid);
            return mongoTemplate.findOne(new Query(criteria), BeautifulRidData.class);
        } catch (Exception e) {
            logger.error("find beautiful rid data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public BeautifulRidData findRealRidDataByUidRid(String uid, int rid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("real_rid").is(rid);
            return mongoTemplate.findOne(new Query(criteria), BeautifulRidData.class);
        } catch (Exception e) {
            logger.error("findRealRidDataByRidAndTime data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<BeautifulRidData> findAll() {
        return mongoTemplate.findAll(BeautifulRidData.class);
    }

    /**
     * 靓号等级颜色划分：
     * 红色(5)：1-2位数、字母+数字。例如：11、UAE、UAE12
     * 咖色(4)：3位数字。例如：333、456、321
     * 玫红(3)：4位数字。例如：3456、5435、67859
     * 蓝色(2)：5位数字。例如：34567、54355、67859
     * 绿色(1)：6位数字。例如：345356、456454、3458935
     */
    public int getAlphaLevel(String uniqueId) {
        if (!StringUtils.isNumber(uniqueId)) {
            return 5;
        }
        switch (uniqueId.length()) {
            case 1:
            case 2:
                return 5;
            case 3:
                return 4;
            case 4:
                return 3;
            case 5:
                return 2;
            default:
                return 1;
        }
    }
}
