package com.quhong.mongo.dao;

import com.mongodb.client.result.UpdateResult;
import com.quhong.constant.TaskRankConstant;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.TaskRankTemplateData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Component
public class TaskRankTemplateDao {
    private static final Logger logger = LoggerFactory.getLogger(TaskRankTemplateDao.class);
    public static final String TABLE_NAME = "task_rank_template";


    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;


    /**
     * 获取所有正在运行的活动
     */
    public List<TaskRankTemplateData> getTaskRankingActivity() {
        try {
            Query query = new Query(Criteria.where("status").is(TaskRankConstant.STATUS_ONLINE));
            return mongoTemplate.find(query, TaskRankTemplateData.class);
        } catch (Exception e) {
            logger.error("getTaskRankingActivity error={}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 运营系统操作
     */
    public List<TaskRankTemplateData> selectTaskRankTemplatePage(String search, int status, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (status != -1) {
                criteria.and("status").is(status);
            }
            if (!StringUtils.isEmpty(search)) {
                criteria.orOperator(Criteria.where("nameEn").regex(".*?" + search + ".*?"), Criteria.where("nameAr").regex(".*?" + search + ".*?"));
            }
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, TaskRankTemplateData.class);
        } catch (Exception e) {
            logger.error("taskRankTemplateData selectPage error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public long selectCount(String search, int status) {
        // 查询条件
        Criteria criteria = new Criteria();
        if (status != -1) {
            criteria.and("status").is(status);
        }
        if (!StringUtils.isEmpty(search)) {
            criteria.orOperator(Criteria.where("nameEn").regex(".*?" + search + ".*?"), Criteria.where("nameAr").regex(".*?" + search + ".*?"));
        }
        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }

    public TaskRankTemplateData getDataByID(String docId) {
        try {
            return mongoTemplate.findById(docId, TaskRankTemplateData.class);
        } catch (Exception e) {
            logger.error("TaskRankTemplateData query error. msg = {}", e.getMessage(), e);
        }
        return null;
    }

    public TaskRankTemplateData insert(TaskRankTemplateData data) {
        try {
            return mongoTemplate.insert(data);
        } catch (Exception e) {
            logger.error("TaskRankTemplateData insert error. msg = {}", e.getMessage(), e);
        }
        return null;
    }

    public UpdateResult updateData(String docId, Update update) {
        try {
            return mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(docId)), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("TaskRankTemplateData  updateData error.  id={} {}", docId, e.getMessage(), e);
            return null;
        }
    }

}
