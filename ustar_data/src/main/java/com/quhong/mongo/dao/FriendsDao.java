package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.FriendsData;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/6/6
 */
@Component
public class FriendsDao {

    private static final Logger logger = LoggerFactory.getLogger(FriendsDao.class);

    private static final long CACHE_TIME_MILLIS = 10 * 60 * 1000L;
    private static final long LIST_CACHE_TIME_MILLIS = 30 * 1000L;
    private final CacheMap<String, FriendsData> cacheMap;
    private final CacheMap<String, List<FriendsData>> listCacheMap;
    public static final String TABLE_NAME = "friends";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
        listCacheMap.start();
    }

    public FriendsDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
        this.listCacheMap = new CacheMap<>(LIST_CACHE_TIME_MILLIS);
    }

    public List<FriendsData> findDataList(String uid, Integer start, Integer pageSize) {
        try {
            Criteria criteria = new Criteria();
            criteria.orOperator(Criteria.where("uid_first").is(uid), Criteria.where("uid_second").is(uid));
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            Query query = new Query(criteria).with(sort).skip(start).limit(pageSize);
            return mongoTemplate.find(query, FriendsData.class);
        } catch (Exception e) {
            logger.error("find friend page list error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<FriendsData> findDataList(String uid) {
        try {
            Criteria criteria = new Criteria();
            criteria.orOperator(Criteria.where("uid_first").is(uid), Criteria.where("uid_second").is(uid));
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            Query query = new Query(criteria).with(sort);
            return mongoTemplate.find(query, FriendsData.class);
        } catch (Exception e) {
            logger.error("find friend list error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public FriendsData findData(String uid, String aid) {
        String friendIndex = generateFriendIndex(uid, aid);
        if (cacheMap.hasData(friendIndex)) {
            return cacheMap.getData(friendIndex);
        }
        FriendsData friendsData = findDataFromDb(uid, aid);
        cacheMap.cacheData(friendIndex, friendsData);
        return friendsData;
    }

    public FriendsData findDataFromDb(String uid, String aid) {
        try {
            String friendIndex = generateFriendIndex(uid, aid);
            Criteria criteria = Criteria.where("friend_index").is(friendIndex);
            return mongoTemplate.findOne(new Query(criteria), FriendsData.class);
        } catch (Exception e) {
            logger.error("find friend data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void save(FriendsData friendsData) {
        try {
            friendsData.setFriendIndex(generateFriendIndex(friendsData.getUidFirst(), friendsData.getUidSecond()));
            mongoTemplate.save(friendsData);
            cacheMap.cacheData(generateFriendIndex(friendsData.getUidFirst(), friendsData.getUidSecond()), friendsData);
            listCacheMap.remove(friendsData.getUidFirst());
            listCacheMap.remove(friendsData.getUidSecond());
        } catch (Exception e) {
            logger.error("save friends data error. uid={} aid={} {}", friendsData.getUidFirst(), friendsData.getUidSecond(), e.getMessage(), e);
        }
    }

    public void removeFromDB(FriendsData friendsData) {
        try {
            mongoTemplate.remove(friendsData);
            cacheMap.remove(generateFriendIndex(friendsData.getUidFirst(), friendsData.getUidSecond()));
            listCacheMap.remove(friendsData.getUidFirst());
            listCacheMap.remove(friendsData.getUidSecond());
        } catch (Exception e) {
            logger.error("remove friends data error. uid={} aid={} {}", friendsData.getUidFirst(), friendsData.getUidSecond(), e.getMessage(), e);
        }
    }

    public int getFriendCount(String uid) {
        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("uid_first").is(uid), Criteria.where("uid_second").is(uid));
        return (int) mongoTemplate.count(new Query(criteria), FriendsData.class);
    }

    public List<FriendsData> findAllFriend(String uid, boolean modify) {
        List<FriendsData> dataList = listCacheMap.getData(uid);
        if (!CollectionUtils.isEmpty(dataList)) {
            if (modify) {
                return new ArrayList<>(dataList);
            }
            return dataList;
        }
        dataList = findAllData(uid);
        if (!CollectionUtils.isEmpty(dataList)) {
            listCacheMap.cacheData(uid, dataList);
        }
        return dataList;
    }

    private List<FriendsData> findAllData(String uid) {
        try {
            Criteria criteria = new Criteria();
            criteria.orOperator(Criteria.where("uid_first").is(uid), Criteria.where("uid_second").is(uid));
            return mongoTemplate.find(new Query(criteria), FriendsData.class);
        } catch (Exception e) {
            logger.error("find online friend list error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<FriendsData> findAllDataByLimit(String uid) {
        try {
            Criteria criteria = new Criteria();
            criteria.orOperator(Criteria.where("uid_first").is(uid), Criteria.where("uid_second").is(uid));
            Query query = new Query(criteria);
            query.limit(500);
            return mongoTemplate.find(query, FriendsData.class);
        } catch (Exception e) {
            logger.error("find online friend list error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public boolean isFriend(String uid, String aid) {
        return findData(uid, aid) != null;
    }

    /**
     * 生成唯一索引
     */
    private String generateFriendIndex(String id1, String id2) {
        if (id1.compareTo(id2) > 0) {
            return id2 + "_" + id1;
        } else {
            return id1 + "_" + id2;
        }
    }
}
