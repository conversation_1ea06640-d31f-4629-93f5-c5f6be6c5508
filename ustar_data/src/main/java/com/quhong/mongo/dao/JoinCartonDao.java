package com.quhong.mongo.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ResourceGroupData;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.IdentifyType;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.JoinCartonData;
import com.quhong.service.WearResourcesService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 进场动画
 */
@Component
public class JoinCartonDao {
    private static final Logger logger = LoggerFactory.getLogger(JoinCartonDao.class);

    public static final String TABLE_NAME = "join_carton";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    //    @Resource(name = DataRedisBean.JOIN_CARTON_EXPIRE)
//    private StringRedisTemplate expireRedisTemplate;
    @Resource
    private WearResourcesService wearResourcesService;

    public JoinCartonDao() {

    }

    public int getJoinCartonId(String uid, int identify, int vipLevel) {
        JoinCartonData cartonData = findData(uid, 1);
        if (cartonData != null) {
            return cartonData.getJoin_carton_id();
        }
        if (identify == IdentifyType.VIP && vipLevel == 1) {
//            // 兼容老版本
//            if (findOldData(uid, 1) != null) {
//            }
            return 1;
        }
        return 0;
    }

    public List<JoinCartonData> findByUidSet(Set<String> uidSet, int status) {
        try {
            Criteria criteria = Criteria.where("uid").in(uidSet).and("status").is(status);
            return mongoTemplate.find(new Query(criteria), JoinCartonData.class);
        } catch (Exception e) {
            logger.error("get join carton data from db error. uidSet={} {}", uidSet, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public JoinCartonData findData(String uid, int status) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("status").is(status);
            return mongoTemplate.findOne(new Query(criteria), JoinCartonData.class);
        } catch (Exception e) {
            logger.error("get join carton data from db error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public JoinCartonData getWearingJoinCartoon(String uid, int joinCartonId) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("join_carton_id").is(joinCartonId).and("status").is(1);
            return mongoTemplate.findOne(new Query(criteria), JoinCartonData.class);
        } catch (Exception e) {
            logger.error("get join carton data from db error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<JoinCartonData> findList(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            return mongoTemplate.find(new Query(criteria), JoinCartonData.class);
        } catch (Exception e) {
            logger.error("get join carton list from db error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<JoinCartonData> findList(String uid, int start, int size) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("join_carton_id").ne(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort);
            query.skip(start).limit(size);
            return mongoTemplate.find(query, JoinCartonData.class);
        } catch (Exception e) {
            logger.error("get join carton list from db error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    @Deprecated
    public void updateCartoon(String uid, int cartoonId, long endTime, boolean putOn) {
        try {
            logger.info("update cartoon. cartoonId={} endTime={} putOn={} uid={}", cartoonId, endTime, putOn, uid);
            JoinCartonData cartonData = findCartoonData(uid, cartoonId);
            if (cartonData == null) {
                cartonData = new JoinCartonData();
                cartonData.setUid(uid);
                cartonData.setJoin_carton_id(cartoonId);
                cartonData.setC_time(DateHelper.getNowSeconds());
                cartonData.setStatus(putOn ? 1 : 0);
                cartonData.setEnd_time(endTime);
                mongoTemplate.insert(cartonData);
            } else {
                Criteria criteria = Criteria.where("uid").is(uid).and("join_carton_id").is(cartoonId);
                Update update = new Update();
                update.set("end_time", endTime);
                mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
//                expireRedisTemplate.opsForZSet().remove(getExpireKey(), formatValue(uid, cartoonId, cartonData.getEnd_time()));
            }
//            expireRedisTemplate.opsForZSet().add(getExpireKey(), formatValue(uid, cartoonId, endTime), endTime);
        } catch (Exception e) {
            logger.error("update join cartoon data error. cartoonId={}  uid={} {}", cartoonId, uid, e.getMessage(), e);
        }
    }

    public void updateCartoonStatus(JoinCartonData cartonData, int status) {
        try {
            Criteria criteria = Criteria.where("_id").is(cartonData.get_id());
            Update update = new Update();
            update.set("status", status);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
            if (status == 0) {
                int wearId = wearResourcesService.getWearResId(cartonData.getUid(), BaseDataResourcesConstant.TYPE_RIDE);
                if (wearId != BaseDataResourcesConstant.COMMON_DEFAULT_ID && wearId == cartonData.getJoin_carton_id()) {
                    wearResourcesService.deleteWearResIdByType(cartonData.getUid(), BaseDataResourcesConstant.TYPE_RIDE);
                }
            } else {
                wearResourcesService.setWearResIdById(cartonData.getUid(), BaseDataResourcesConstant.TYPE_RIDE, cartonData.getJoin_carton_id());
            }
        } catch (Exception e) {
            logger.error("update join cartoon status error.  status={} cartoonId={}  uid={} {}", status, cartonData.getJoin_carton_id(), cartonData.getUid(), e.getMessage(), e);
        }
    }

    public void removeCartoon(JoinCartonData cartonData) {
        logger.info("remove cartoon endTime={} uid={}", cartonData.getEnd_time(), cartonData.getUid());
        try {
            removeCartoonFromDb(cartonData);
            int wearId = wearResourcesService.getWearResId(cartonData.getUid(), BaseDataResourcesConstant.TYPE_RIDE);
            if (wearId != BaseDataResourcesConstant.COMMON_DEFAULT_ID && wearId == cartonData.getJoin_carton_id()) {
                wearResourcesService.deleteWearResIdByType(cartonData.getUid(), BaseDataResourcesConstant.TYPE_RIDE);
            }
//            expireRedisTemplate.opsForZSet().remove(getExpireKey(), formatValue(cartonData.getUid(), cartonData.getJoin_carton_id(), cartonData.getEnd_time()));
        } catch (Exception e) {
            logger.error("remove join cartoon data error. cartoonId={}  uid={} {}", cartonData.getJoin_carton_id(), cartonData.getUid(), e.getMessage(), e);
        }
    }

    private void removeCartoonFromDb(JoinCartonData cartonData) {
        try {
            Criteria criteria = Criteria.where("_id").is(cartonData.get_id());
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("remove join cartoon data from db error. uid={} {}", cartonData.getUid(), e.getMessage(), e);
        }
    }

    public JoinCartonData findOldData(String uid, int join_carton_id) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("join_carton_id").is(join_carton_id).and("end_time").gt(DateHelper.getNowSeconds());
            return mongoTemplate.findOne(new Query(criteria), JoinCartonData.class);
        } catch (Exception e) {
            logger.error("get join carton data from db error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public JoinCartonData findCartoonData(String uid, int join_carton_id) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("join_carton_id").is(join_carton_id);
            return mongoTemplate.findOne(new Query(criteria), JoinCartonData.class);
        } catch (Exception e) {
            logger.error("get join carton data from db error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void insertDB(String uid, int cartoonId, long endTime, boolean putOn, String checkDate) {
        try {
            logger.info("update cartoon. cartoonId={} days={} putOn={} uid={}", cartoonId, endTime, putOn, uid);
            JoinCartonData cartonData = new JoinCartonData();
            cartonData.setUid(uid);
            cartonData.setJoin_carton_id(cartoonId);
            cartonData.setC_time(DateHelper.getNowSeconds());
            cartonData.setStatus(putOn ? 1 : 0);
            cartonData.setEnd_time(endTime);
            if (!StringUtils.isEmpty(checkDate)) {
                cartonData.setCheck_date(checkDate);
            }
            mongoTemplate.insert(cartonData);
            if (putOn) {
                wearResourcesService.setWearResIdById(uid, BaseDataResourcesConstant.TYPE_RIDE, cartoonId);
            }
        } catch (Exception e) {
            logger.error("insert join cartoon data error. cartoonId={}  uid={} {}", cartoonId, uid, e.getMessage(), e);
        }
    }

    public void updateCartoonByTime(String uid, int cartoonId, long endTime, int status, JoinCartonData oldCartonData) {
        updateCartoonByTime(uid, cartoonId, endTime, status, oldCartonData, "");
    }


    public void updateCartoonByTime(String uid, int cartoonId, long endTime, int status, JoinCartonData oldCartonData, String checkDate) {
        try {
            logger.info("update cartoon. cartoonId={} endTime={} status={} uid={}", cartoonId, endTime, status, uid);
            Criteria criteria = Criteria.where("uid").is(uid).and("join_carton_id").is(cartoonId);
            Update update = new Update();
            update.set("end_time", endTime);
            update.set("status", status);
            if (!StringUtils.isEmpty(checkDate)) {
                update.set("check_date", checkDate);
            }
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);

            if (status == 1) {
                wearResourcesService.setWearResIdById(uid, BaseDataResourcesConstant.TYPE_RIDE, cartoonId);
            }
//            if (null != oldCartonData) {
//                expireRedisTemplate.opsForZSet().remove(getExpireKey(), formatValue(uid, cartoonId, oldCartonData.getEnd_time()));
//            }
//            expireRedisTemplate.opsForZSet().add(getExpireKey(), formatValue(uid, cartoonId, endTime), endTime);
        } catch (Exception e) {
            logger.error("update join cartoon data error. cartoonId={}  uid={} {}", cartoonId, uid, e.getMessage(), e);
        }
    }

    public List<JoinCartonData> listByEndTime(int start, int end) {
        Criteria criteria = Criteria.where("end_time").gt(start).lte(end);
        List<JoinCartonData> list = mongoTemplate.find(new Query(criteria), JoinCartonData.class);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("listByEndTime list is null  end = {}", end);
            return new ArrayList<>();
        }
        return list;
    }

    private String formatValue(String uid, int cartoonId, long endTime) {
        return uid + "_" + cartoonId + "_" + endTime;
    }

    private String getExpireKey() {
        return "user_join_manger";
    }



    public List<ResourceGroupData> findResourceGroupList(List<Integer> resourceList) {
        try {
            Criteria criteria = new Criteria();
            criteria.and("join_carton_id").in(resourceList);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.project("join_carton_id"),
                    Aggregation.group("join_carton_id").first("join_carton_id").as("resourceId").count().as("count")
            );

            return mongoTemplate.aggregate(aggregation, TABLE_NAME, ResourceGroupData.class).getMappedResults();

        } catch (Exception e) {
            logger.error("findResourceGroupList resourceList={} {}", resourceList, e.getMessage(), e);
        }
        return Collections.emptyList();
    }
}
