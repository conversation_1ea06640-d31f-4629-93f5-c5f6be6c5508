package com.quhong.mongo.dao;

import com.quhong.data.OfficialDeleteData;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.OfficialData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Lazy
@Component
public class OfficialDao {
    private static final Logger logger = LoggerFactory.getLogger(OfficialDao.class);

    public static final String TABLE_NAME = "official";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public OfficialData findData(String id) {
        try {
            if ("None".equals(id)) {
                return null;
            }
            Criteria criteria = Criteria.where("_id").is(new ObjectId(id));
            return mongoTemplate.findOne(new Query(criteria), OfficialData.class);
        } catch (Exception e) {
            logger.info("get official data error. id={} {}", id, e.getMessage(), e);
        }
        return null;
    }

    public OfficialData findOfficialLastData(String uid) {
        try {
            Criteria criteria = Criteria.where("to_uid").is(uid).and("valid").is(1).and("delete_start").ne(1).and("ntype").ne(1);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            Query query = new Query(criteria).with(sort);
            query.limit(1);
            return mongoTemplate.findOne(query, OfficialData.class);
        } catch (Exception e) {
            logger.info("get official last one data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public OfficialData findActivityLastData(String uid) {
        try {
            Criteria criteria = Criteria.where("to_uid").is(uid).and("valid").is(1).and("delete_start").ne(1).and("ntype").is(1);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            Query query = new Query(criteria).with(sort);
            query.limit(1);
            return mongoTemplate.findOne(query, OfficialData.class);
        } catch (Exception e) {
            logger.info("get activity last one data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public OfficialData findByOfficialPushIdData(String uid,String officialPushId) {
        try {
            Criteria criteria = Criteria.where("official_push_id").is(officialPushId).and("to_uid").is(uid);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, OfficialData.class);
        } catch (Exception e) {
            logger.info("get activity last one data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public OfficialData save(OfficialData data) {
        try {
            return mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save official data error uid={} {}", data.getTo_uid(), e.getMessage());
            return data;
        }
    }

    public List<OfficialData> findNotificationPage(String uid, int start, Integer pageSize) {
        try {
            Criteria criteria = Criteria.where("to_uid").is(uid).and("delete_start").ne(1).and("ntype").ne(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            query.with(sort);
            query.skip(start).limit(pageSize);
            return mongoTemplate.find(query, OfficialData.class);
        } catch (Exception e) {
            logger.error("find notification official data page error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<OfficialData> findActivityPage(String uid, int start, Integer pageSize) {
        try {
            Criteria criteria = Criteria.where("to_uid").is(uid).and("delete_start").ne(1).and("ntype").is(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            query.with(sort);
            query.skip(start).limit(pageSize);
            return mongoTemplate.find(query, OfficialData.class);
        } catch (Exception e) {
            logger.error("find activity official data page error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void deleteNotificationMsg(String uid) {
        try {
            Criteria criteria = Criteria.where("to_uid").is(uid).and("delete_start").ne(1).and("ntype").ne(1);
            Update update = new Update();
            update.set("delete_start", 1);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("delete activity notice. uid={} {}", e.getMessage(), e);
        }
    }

    public void deleteActivityMsg(String uid) {
        try {
            Criteria criteria = Criteria.where("to_uid").is(uid).and("delete_start").ne(1).and("ntype").is(1);
            Update update = new Update();
            update.set("delete_start", 1);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("delete activity notice. uid={} {}", e.getMessage(), e);
        }
    }


    /**
     * 清除数据相关
     */
    public List<OfficialDeleteData> getLastDayOfficialDataList(int endTime, int batchSize) {
        try {
            ObjectId endId = new ObjectId(new Date(endTime * 1000L));
            Criteria criteria = Criteria.where("_id").lte(endId);
            Query query = new Query(criteria);
            query.limit(batchSize);
            return mongoTemplate.find(query, OfficialDeleteData.class);
        } catch (Exception e) {
            logger.error("get getOfficialDataList error={}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public void clearOfficialMsg(List<String> objIdList) {
        try {
            Criteria criteria = Criteria.where("_id").in(objIdList);
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("clearOfficialMsg. error={}", e.getMessage(), e);
        }
    }


    /**
     * 删除官方消息
     * @param officialMsgId 消息id
     * @return void
     */
    public void deleteMsg(String officialMsgId) {
        try {
            Criteria criteria = Criteria.where("_id").is(new ObjectId(officialMsgId));
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("delete official message by id error. id={} {}", officialMsgId, e.getMessage(), e);
        }
    }
}
