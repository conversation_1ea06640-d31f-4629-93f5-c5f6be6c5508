package com.quhong.mongo.dao;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.FriendApplyData;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
@Component
public class FriendApplyDao {

    private static final Logger logger = LoggerFactory.getLogger(FriendApplyDao.class);

    public static final String TABLE_NAME = "friend_apply";
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public List<FriendApplyData> findDataList(String uid, Integer start, Integer pageSize){
        try{
            Criteria criteria = Criteria.where("aid").is(uid);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            query.with(sort);
            query.skip(start).limit(pageSize);
            return mongoTemplate.find(query, FriendApplyData.class);
        }catch (Exception e){
            logger.error("find friend apply data list error. uid={} {}", uid, e.getMessage() ,e);
        }
        return null;
    }

    public void cleanUnread(String uid){
        try{
            Criteria criteria = Criteria.where("aid").is(uid).and("is_new").is(1);
            Update update = new Update();
            update.set("is_new", 0);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        }catch (Exception e){
            logger.error("clean unread. uid={} {}", e.getMessage(), e);
        }
    }

    public FriendApplyData findData(String uid, String aid){
        try{
            Criteria criteria = Criteria.where("uid").is(uid).and("aid").is(aid);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, FriendApplyData.class);
        }catch (Exception e){
            logger.error("find friend apply data error. uid={} aid={} {}", uid, aid, e.getMessage() ,e);
        }
        return null;
    }

    public FriendApplyData findData(String uid, String aid, Integer optType){
        try{
            Criteria criteria = Criteria.where("uid").is(uid).and("aid").is(aid).and("opt_type").is(optType);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, FriendApplyData.class);
        }catch (Exception e){
            logger.error("find friend apply data error. uid={} aid={} {}", uid, aid, e.getMessage() ,e);
        }
        return null;
    }

    public int getNewFriendsNum(String uid) {
        try{
            Criteria criteria = Criteria.where("aid").is(uid).and("is_new").is(1);
            Query query = new Query(criteria);
            return (int)mongoTemplate.count(query, FriendApplyData.class);
        }catch (Exception e){
            logger.error("find new friend num error. uid={} {}", uid, e.getMessage() ,e);
        }
        return 0;
    }

    public void removeFriendApplyFromDb(FriendApplyData friendApplyData){
        try{
            Criteria criteria = Criteria.where("_id").is(friendApplyData.get_id());
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        }catch (Exception e){
            logger.error("remove friend apply data from db error. uid={} {}", friendApplyData.getUid(), e.getMessage(), e);
        }
    }

    public void removeFriendApplyFromDb(String uid, String aid){
        try{
            Criteria criteria = new Criteria();
            criteria.orOperator(Criteria.where("uid").is(uid).and("aid").is(aid), Criteria.where("uid").is(aid).and("aid").is(uid));
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        }catch (Exception e){
            logger.error("remove friend apply data from db error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
    }

    public void save(FriendApplyData friendApplyData) {
        try{
            mongoTemplate.save(friendApplyData, TABLE_NAME);
        }catch (Exception e){
            logger.error("save friend apply data from db error. uid={} {}", friendApplyData.getUid(), e.getMessage(), e);
        }
    }

    public void updateOptType(FriendApplyData friendApplyData) {
        try{
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(friendApplyData.get_id()));
            Update update = new Update();
            update.set("opt_type", friendApplyData.getOptType());
            mongoTemplate.updateFirst(query, update, FriendApplyData.class);
        }catch (Exception e){
            logger.error("update friend apply data from db error. uid={} aid={} {}", friendApplyData.getUid(), friendApplyData.getAid(), e.getMessage(), e);
        }
    }

    public void saveFriendApplyCount(String uid, String aid) {
        String key = getKey(uid, aid);
        try {
            redisTemplate.opsForValue().increment(key);
          //  redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_MINUTES_TEN, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save friend apply count error. {}", e.getMessage(), e);
        }
    }

    public int getFriendApplyCount(String uid, String aid){
        String key = getKey(uid, aid);
        try {
            String value = redisTemplate.opsForValue().get(key);
            if(StringUtils.isEmpty(value)){
                return 0;
            }
            return Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("get friend apply count error. {}", e.getMessage(), e);
        }
        return 0;
    }

    public void removeFriendApplyByUid(String uid){
        try{
            Criteria criteria = new Criteria();
            criteria.orOperator(Criteria.where("uid").is(uid), Criteria.where("aid").is(uid));
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        }catch (Exception e){
            logger.error("removeFriendApplyByUid db error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 查询友谊任务列表
     */
    public List<FriendApplyData> findFriendshipTaskList(String uid, String aid, int startTime, int offset, int pageSize) {
        try {
            Criteria criteria =null;

           if (uid != null) {
                // 查询用户发送的申请
               criteria=Criteria.where("uid").is(uid);
            } else if (aid != null) {
                // 查询用户收到的申请
               criteria=Criteria.where("aid").is(aid);
            } else {
                return new ArrayList<>();
            }

            // 添加时间和状态过滤条件
            criteria.and("ctime").gte(startTime);
            criteria.and("opt_type").ne(0);

            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            query.with(sort);
            query.skip(offset).limit(pageSize);

            return mongoTemplate.find(query, FriendApplyData.class);

        } catch (Exception e) {
            logger.error("查询友谊任务列表失败, uid={}, aid={}, error={}", uid, aid, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public String getKey(String uid, String aid) {
        return "friendApplyCount:" + uid + "-" + aid;
    }

}
