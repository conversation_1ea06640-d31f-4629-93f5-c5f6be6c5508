package com.quhong.mongo.data;

import com.alibaba.fastjson.JSONObject;
import com.quhong.mongo.dao.TaskRankTemplateDao;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 任务榜单模板
 */
@Document(collection = TaskRankTemplateDao.TABLE_NAME)
public class TaskRankTemplateData {
    @Id
    private String activityId;
    private String nameEn;                             // 英文活动名
    private String nameAr;                             // 阿语活动名
    private String url;                                // h5地址
    private int startTime;                             // 活动开始时间
    private int endTime;                               // 活动结束时间
    private int status;                                // 活动状态： 0: 未发布【此状态活动中也不统计数据】  1: 已发布【可统计数据】  2: 已结束
    private int testStatus;                            // 灰度测试： 0: 否  1: 是
    private String headUrl;                            // 头图Url
    private String headGifUrl;                         // 头图动态Url
    private int webStyle;                              // 模板web样式
    private String country;                            // 区域限制: 仅选中区域用户可完成活动任务与上榜
    private int gender;                                // 性别限制: 仅选中性别可完成活动任务与上榜 0:全部 1:男 2: 女
    private boolean giftEnable;                        // 是否配置礼物
    private List<ActivityGift> giftList;               // 活动礼物
    private boolean dailyTaskEnable;                   // 是否配置每日任务
    private List<TaskRankConfig> dailyTaskList;            // 日任务配置
    private boolean periodTaskEnable;                  // 是否配置里程碑任务
    private List<TaskRankConfig> periodTaskList;           // 总任务配置
    private boolean rankConfigEnable;                  // 是否配置榜单
    private TaskRankConfig rankConfig;                     // 榜单相关配置
    private String activityRule;                       // 活动规则
    private Integer ctime;                             // 创建时间
    private Integer mtime;                             // 更新时间


    public static class ActivityGift {
        private Integer giftId; // 活动礼物id
        private String giftNameEn; // 活动礼物名称
        private String giftNameAr; // 阿语活动礼物名称
        private Integer giftPrice; // 活动礼物价格
        private String giftIcon; // 活动礼物图片地址

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public String getGiftNameEn() {
            return giftNameEn;
        }

        public void setGiftNameEn(String giftNameEn) {
            this.giftNameEn = giftNameEn;
        }

        public String getGiftNameAr() {
            return giftNameAr;
        }

        public void setGiftNameAr(String giftNameAr) {
            this.giftNameAr = giftNameAr;
        }

        public Integer getGiftPrice() {
            return giftPrice;
        }

        public void setGiftPrice(Integer giftPrice) {
            this.giftPrice = giftPrice;
        }

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }
    }

    public static class TaskRankConfig {
        // 任务或榜单公参
        private String taskRankKey;        // 任务key
        private String extraParam;         // 某些榜单额外配置 如指定礼物id、指定游戏key、指定设备数

        // 任务独有参数
        private String title;              // 标题
        private String subTitle;           // 子标题
        private Integer currentProcess;    // 当前进度
        private Integer totalProcess;      // 完成值
        private Integer status;            // 任务状态 0: 未完成 1: 已完成可领取 2: 已领取
        private String resourceKey;             // 奖励资源key
        private ResourceKeyConfigData resourceKeyConfigData;  // 奖励资源


        // 仅对任务-【送出指定礼物流水】 有效
        private Integer userType;           // 用户类型: 0: 全部用户  1: 注册0-7天新设备用户
        private Integer giftOrigin;         // 礼物来源: 0: 全部来源  1: 非背包礼物

        // 榜单独有参数
        private List<RankRewardConfig> rankRewardList; // 排行榜奖励配置

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getSubTitle() {
            return subTitle;
        }

        public void setSubTitle(String subTitle) {
            this.subTitle = subTitle;
        }

        public String getTaskRankKey() {
            return taskRankKey;
        }

        public void setTaskRankKey(String taskRankKey) {
            this.taskRankKey = taskRankKey;
        }

        public Integer getCurrentProcess() {
            return currentProcess;
        }

        public void setCurrentProcess(Integer currentProcess) {
            this.currentProcess = currentProcess;
        }

        public Integer getTotalProcess() {
            return totalProcess;
        }

        public void setTotalProcess(Integer totalProcess) {
            this.totalProcess = totalProcess;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getExtraParam() {
            return extraParam;
        }

        public void setExtraParam(String extraParam) {
            this.extraParam = extraParam;
        }

        public String getResourceKey() {
            return resourceKey;
        }

        public void setResourceKey(String resourceKey) {
            this.resourceKey = resourceKey;
        }

        public ResourceKeyConfigData getResourceKeyConfigData() {
            return resourceKeyConfigData;
        }

        public void setResourceKeyConfigData(ResourceKeyConfigData resourceKeyConfigData) {
            this.resourceKeyConfigData = resourceKeyConfigData;
        }

        public List<RankRewardConfig> getRankRewardList() {
            return rankRewardList;
        }

        public void setRankRewardList(List<RankRewardConfig> rankRewardList) {
            this.rankRewardList = rankRewardList;
        }

        public Integer getUserType() {
            return userType;
        }

        public void setUserType(Integer userType) {
            this.userType = userType;
        }

        public Integer getGiftOrigin() {
            return giftOrigin;
        }

        public void setGiftOrigin(Integer giftOrigin) {
            this.giftOrigin = giftOrigin;
        }
    }

    public static class RankRewardConfig {
        private Integer rankNum;        // 奖励对象, 1, 2, 3, 4, 5, 6
        private String resourceKey;
        private ResourceKeyConfigData resourceKeyConfigData;

        public Integer getRankNum() {
            return rankNum;
        }

        public void setRankNum(Integer rankNum) {
            this.rankNum = rankNum;
        }

        public String getResourceKey() {
            return resourceKey;
        }

        public void setResourceKey(String resourceKey) {
            this.resourceKey = resourceKey;
        }

        public ResourceKeyConfigData getResourceKeyConfigData() {
            return resourceKeyConfigData;
        }

        public void setResourceKeyConfigData(ResourceKeyConfigData resourceKeyConfigData) {
            this.resourceKeyConfigData = resourceKeyConfigData;
        }
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getTestStatus() {
        return testStatus;
    }

    public void setTestStatus(int testStatus) {
        this.testStatus = testStatus;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getHeadGifUrl() {
        return headGifUrl;
    }

    public void setHeadGifUrl(String headGifUrl) {
        this.headGifUrl = headGifUrl;
    }

    public int getWebStyle() {
        return webStyle;
    }

    public void setWebStyle(int webStyle) {
        this.webStyle = webStyle;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public boolean isGiftEnable() {
        return giftEnable;
    }

    public void setGiftEnable(boolean giftEnable) {
        this.giftEnable = giftEnable;
    }

    public List<ActivityGift> getGiftList() {
        return giftList;
    }

    public void setGiftList(List<ActivityGift> giftList) {
        this.giftList = giftList;
    }

    public boolean isDailyTaskEnable() {
        return dailyTaskEnable;
    }

    public void setDailyTaskEnable(boolean dailyTaskEnable) {
        this.dailyTaskEnable = dailyTaskEnable;
    }

    public List<TaskRankConfig> getDailyTaskList() {
        return dailyTaskList;
    }

    public void setDailyTaskList(List<TaskRankConfig> dailyTaskList) {
        this.dailyTaskList = dailyTaskList;
    }

    public boolean isPeriodTaskEnable() {
        return periodTaskEnable;
    }

    public void setPeriodTaskEnable(boolean periodTaskEnable) {
        this.periodTaskEnable = periodTaskEnable;
    }

    public List<TaskRankConfig> getPeriodTaskList() {
        return periodTaskList;
    }

    public void setPeriodTaskList(List<TaskRankConfig> periodTaskList) {
        this.periodTaskList = periodTaskList;
    }

    public boolean isRankConfigEnable() {
        return rankConfigEnable;
    }

    public void setRankConfigEnable(boolean rankConfigEnable) {
        this.rankConfigEnable = rankConfigEnable;
    }

    public TaskRankConfig getRankConfig() {
        return rankConfig;
    }

    public void setRankConfig(TaskRankConfig rankConfig) {
        this.rankConfig = rankConfig;
    }

    public String getActivityRule() {
        return activityRule;
    }

    public void setActivityRule(String activityRule) {
        this.activityRule = activityRule;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
