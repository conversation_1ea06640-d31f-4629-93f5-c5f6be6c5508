package com.quhong.mongo.data;

import com.quhong.mongo.dao.JoinCartonDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = JoinCartonDao.TABLE_NAME)
public class JoinCartonData {
    @Id
    private ObjectId _id;

    private String uid;
    /**
     * 进场动画id
     */
    private int join_carton_id;
    /**
     * 是否有效
     */
    private int status;

    private int honor;

    private long c_time;
    /**
     * 结束时间
     */
    private long end_time;

//    private int left_times; // 剩余的天次

    private String check_date = ""; // 用来判断今天是否次数已经减去

    public JoinCartonData() {

    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getJoin_carton_id() {
        return join_carton_id;
    }

    public void setJoin_carton_id(int join_carton_id) {
        this.join_carton_id = join_carton_id;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getHonor() {
        return honor;
    }

    public void setHonor(int honor) {
        this.honor = honor;
    }

    public long getC_time() {
        return c_time;
    }

    public void setC_time(long c_time) {
        this.c_time = c_time;
    }

    public long getEnd_time() {
        return end_time;
    }

    public void setEnd_time(long end_time) {
        this.end_time = end_time;
    }

    public String getCheck_date() {
        return check_date;
    }

    public void setCheck_date(String check_date) {
        this.check_date = check_date;
    }
}
