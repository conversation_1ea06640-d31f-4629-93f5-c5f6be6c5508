package com.quhong.mongo.data;

import com.quhong.mongo.dao.SignTableDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/21
 */
@Document(collection = SignTableDao.TABLE_NAME)
public class SignTableData {

    @Id
    private String _id;

    /**
     * 上次弹窗日期
     */
    private String check_date;

    /**
     * 上次签到日期
     */
    private String last_sign;

    /**
     * 连续签到天数
     */
    private int combo;

    /**
     * 本轮签到记录
     */
    private List<Integer> sign_record;

    /**
     * 判断弹窗日期
     */
    private String boom_check;

    /**
     * 连续签到天数 会一直叠加，中间有一天没签到都为零
     */
    private int continu;

    /**
     * 是否已转换
     */
    private int cover;

    /**
     * 累积签到次数
     */
    private int signCount;

    /**
     * V8641 签到类型
     * 0: 正常签到 (第4天及以后)
     * 1: 设备新用户签到 (前3天)
     * 2: 设备老用户签到 (前3天)
     */
    private int signType;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getCheck_date() {
        return check_date;
    }

    public void setCheck_date(String check_date) {
        this.check_date = check_date;
    }

    public String getLast_sign() {
        return last_sign;
    }

    public void setLast_sign(String last_sign) {
        this.last_sign = last_sign;
    }

    public int getCombo() {
        return combo;
    }

    public void setCombo(int combo) {
        this.combo = combo;
    }

    public List<Integer> getSign_record() {
        return sign_record;
    }

    public void setSign_record(List<Integer> sign_record) {
        this.sign_record = sign_record;
    }

    public String getBoom_check() {
        return boom_check;
    }

    public void setBoom_check(String boom_check) {
        this.boom_check = boom_check;
    }

    public int getContinu() {
        return continu;
    }

    public void setContinu(int continu) {
        this.continu = continu;
    }

    public int getSignCount() {
        return signCount;
    }

    public void setSignCount(int signCount) {
        this.signCount = signCount;
    }

    public int getCover() {
        return cover;
    }

    public void setCover(int cover) {
        this.cover = cover;
    }

    public int getSignType() {
        return signType;
    }

    public void setSignType(int signType) {
        this.signType = signType;
    }

}
