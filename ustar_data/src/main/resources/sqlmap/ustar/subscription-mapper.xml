<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.SubscriptionMapper">

    <resultMap id="BaseResultMap" type="com.quhong.mysql.data.SubscriptionData">
        <id column="rid" jdbcType="INTEGER" property="rid"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="start_at" jdbcType="BIGINT" property="startAt"/>
        <result column="end_at" jdbcType="BIGINT" property="endAt"/>
        <result column="os" jdbcType="INTEGER" property="os"/>
        <result column="auto_renew_status" jdbcType="TINYINT" property="autoRenewStatus"/>
        <result column="fstatus" jdbcType="TINYINT" property="fstatus"/>
        <result column="ctime" jdbcType="BIGINT" property="ctime"/>
        <result column="mtime" jdbcType="BIGINT" property="mtime"/>
        <result column="original_order_id" jdbcType="VARCHAR" property="originalOrderId"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="verify_time" jdbcType="BIGINT" property="verifyTime"/>
    </resultMap>

    <sql id="fieldsSql">
        user_id, product_id, start_at, end_at, os, auto_renew_status, fstatus, ctime, mtime, original_order_id, order_id, verify_time
    </sql>

    <sql id="itemsSql">
        #{userId}, #{productId}, #{startAt}, #{endAt}, #{os}, #{autoRenewStatus}, #{fstatus}, #{ctime}, #{mtime}, #{originalOrderId}, #{orderId}, #{verifyTime}
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="rid"
            keyColumn="rid" parameterType="com.quhong.mysql.data.SubscriptionData">
        INSERT INTO t_svip_subscriptions (<include refid="fieldsSql"/>)
        VALUES (<include refid="itemsSql"/>)
    </insert>
    <update id="update">
        UPDATE t_svip_subscriptions
        set start_at = #{startAt},`end_at` = #{endAt}, os = #{os}, auto_renew_status = #{autoRenewStatus},fstatus = #{fstatus}, original_order_id= #{originalOrderId},order_id=#{orderId},mtime = #{mtime}, verify_time = #{verifyTime} WHERE rid = #{rid} LIMIT 1
    </update>
    <select id="getData" resultMap="BaseResultMap">
        SELECT
            rid, <include refid="fieldsSql"/>
        FROM t_svip_subscriptions
        WHERE user_id = #{userId}
    </select>
    <select id="getDataByOriginalOrderId" resultMap="BaseResultMap">
        SELECT
        rid, <include refid="fieldsSql"/>
        FROM t_svip_subscriptions
        WHERE original_order_id = #{originalOrderId} limit 1
    </select>
    <select id="getVerifyList" resultMap="BaseResultMap">
        SELECT
        rid, <include refid="fieldsSql"/>
        FROM t_svip_subscriptions
        WHERE os = #{os} and end_at &gt; #{endTime} and verify_time &lt; #{verifyTime} and fstatus != 4;
    </select>
    <select id="getExpiredList" resultMap="BaseResultMap">
        SELECT
        rid, <include refid="fieldsSql"/>
        FROM t_svip_subscriptions
        WHERE end_at &lt; #{endTime} and fstatus in (1,2)
    </select>
</mapper>
