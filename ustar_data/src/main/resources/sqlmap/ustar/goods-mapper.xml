<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.GoodsMapper">

  <resultMap id="BaseResultMap" type="com.quhong.mysql.data.GoodsData">
    <id column="rid" jdbcType="INTEGER" property="rid" />
    <result column="productId" jdbcType="VARCHAR" property="productId" />
    <result column="beans" jdbcType="INTEGER" property="beans" />
    <result column="reward" jdbcType="INTEGER" property="reward" />
    <result column="fstatus" jdbcType="TINYINT" property="fstatus" />
    <result column="price" jdbcType="VARCHAR" property="price" />
    <result column="forder" jdbcType="INTEGER" property="forder" />
    <result column="showInfo" jdbcType="VARCHAR" property="showInfo" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <select id="getGoodsList" resultMap="BaseResultMap">
        SELECT
            productId,
            beans,
            reward,
            price,
            showInfo,
            fstatus
        FROM t_goods
        ORDER BY forder, beans
  </select>

    <select id="getProductList" resultMap="BaseResultMap">
        SELECT
            productId,
            beans,
            reward,
            price,
            showInfo,
            fstatus
        FROM t_goods
        WHERE fstatus = 1
        ORDER BY beans
    </select>

</mapper>
