<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.RoomManageMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.RoomManageData">
        <result column="id" property="id"></result>
        <result column="roomId" property="room_id"></result>
        <result column="all_mics_mute" property="allMicsMute"></result>
        <result column="ctime" property="ctime"></result>
        <result column="mtime" property="mtime"></result>
     </resultMap>
    <sql id="baseSql">
        room_id,all_mics_mute,ctime,mtime
     </sql>
    <sql id="itemSql">
        #{item.roomId},#{item.allMicsMute},#{item.ctime},#{item.mtime}
    </sql>
    <select id="getData" resultMap="baseResultMap">
        select id, <include refid="baseSql"/> from t_room_manage where room_id=#{roomId} limit 1
    </select>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into t_room_manage (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>
    <update id="updateMute">
        update t_room_manage set all_mics_mute=#{item.allMicsMute},mtime=#{item.mtime} where id=#{item.id} limit 1
    </update>
</mapper>
