<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar_log.ReportLogMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.ReportLogData">
        <result column="uid" property="uid"/>
        <result column="report_id" property="reportId"/>
        <result column="information" property="information"/>
        <result column="os" property="os"/>
        <result column="slang" property="slang"/>
        <result column="report_type" property="reportType"/>
        <result column="content_type" property="contentType"/>
        <result column="content" property="content"/>
        <result column="c_time" property="ctime"/>
        <result column="m_time" property="mtime"/>
        <result column="error" property="error"/>
    </resultMap>
    <sql id="baseSql">
        uid,report_id,information,os,slang,report_type,content_type,content,c_time,m_time,error
    </sql>
    <sql id="itemSql">
        #{item.uid},#{item.reportId},#{item.information},#{item.os},#{item.slang},#{item.reportType},#{item.contentType},#{item.content},#{item.ctime},#{item.mtime},#{item.error}
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="item.id" keyColumn="id">
        insert into t_report_log_${tableSuffix} (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>
</mapper>
