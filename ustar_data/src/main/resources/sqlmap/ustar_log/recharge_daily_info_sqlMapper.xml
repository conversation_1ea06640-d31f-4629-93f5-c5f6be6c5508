<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar_log.RechargeDailyInfoMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.RechargeDailyInfoData">
        <result column="id" jdbcType="INTEGER" property="id"></result>
        <result column="recharge_date" jdbcType="DATE"  property="rechargeDate"></result>
        <result column="pay_type" jdbcType="INTEGER" property="payType"></result>
        <result column="uid" jdbcType="VARCHAR" property="uid"></result>
        <result column="recharge_money" jdbcType="DOUBLE" property="rechargeMoney"></result>
        <result column="recharge_diamond" jdbcType="INTEGER" property="rechargeDiamond"></result>
        <result column="s_type" jdbcType="INTEGER" property="sType"></result>
        <result column="register_time" jdbcType="INTEGER" property="registerTime"></result>
        <result column="recharge_time" jdbcType="INTEGER" property="rechargeTime"></result>
        <result column="os" jdbcType="INTEGER" property="os"></result>
        <result column="first_charge" jdbcType="INTEGER" property="firstCharge"></result>
        <result column="sub_type" jdbcType="VARCHAR" property="subType"></result>
    </resultMap>

    <select id = "selectUserRechargeAmount" resultType="decimal">
        SELECT IFNULL(sum(recharge_money),0) FROM t_recharge_daily_info
        WHERE s_type in ( 0, 1, 4, 6)
            AND uid = #{uid}
            AND recharge_time > #{rechargeTime}
        GROUP BY uid
    </select>

    <select id="selectRechargeUserUid" resultType="java.lang.String" parameterType="com.quhong.data.UserRechargeAmountData">
        SELECT uid FROM t_recharge_daily_info
        WHERE s_type in ( 0, 1, 4, 6)
        <if test="uidList.size() > 0">
            AND uid IN
            <foreach item="item" collection="uidList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
          AND recharge_time > #{rechargeTime}
        GROUP BY uid
        HAVING IFNULL(sum(recharge_money),0) > #{rechargeAmount}
        LIMIT #{rechargeUserCount}
    </select>

    <select id="getRechargeTotalNum" resultType="java.lang.Integer">
        SELECT count(1) FROM t_recharge_daily_info
        WHERE s_type in ( 0, 1, 4, 6)
          AND uid = #{uid}
    </select>

    <select id="getUserTotalRecharge" resultType="com.quhong.mysql.data.UserTotalRechargeData" parameterType="java.lang.String">
        SELECT uid, IFNULL(sum(recharge_money), 0) as totalRechargeMoney, IFNULL(sum(recharge_diamond), 0) as totalRechargeDiamond, IFNULL(pay_type, 0) as payType
        FROM `t_recharge_daily_info` WHERE uid = #{uid} and s_type in (0,1,4,6);
    </select>

    <select id="getUserRechargeInfoList" resultMap="baseResultMap">
        SELECT id, recharge_date, pay_type, uid, recharge_money, recharge_diamond, s_type, register_time, recharge_time, os, first_charge, sub_type
        FROM `t_recharge_daily_info`
        WHERE uid = #{uid} AND s_type in (0,1,4,6) AND <![CDATA[ recharge_time >= #{startTime} AND recharge_time < #{endTime} ]]>
    </select>

    <select id="getHistoryRechargeTotalNum" resultType="java.lang.Integer">
        SELECT count(1) FROM t_recharge_daily_info
        WHERE s_type in ( 0, 1, 4, 6)
          AND uid = #{uid} AND <![CDATA[ recharge_time < #{endTime} ]]>
    </select>
    <select id = "selectUserRechargeByDay"  resultType="java.lang.String">
        SELECT uid FROM t_recharge_daily_info
        WHERE  <![CDATA[ recharge_time >= #{startTime} AND recharge_time < #{endTime} ]]>
        GROUP BY uid
        HAVING IFNULL(sum(recharge_money),0) > #{rechargeAmount}
    </select>
    <select id="getUserTotalRechargeBean" resultType="java.lang.Integer">
        SELECT IFNULL(sum(recharge_diamond), 0)
        FROM `t_recharge_daily_info`
        WHERE uid = #{uid} AND s_type in (0,1,4,6) AND <![CDATA[ recharge_time >= #{startTime} AND recharge_time < #{endTime} ]]>
    </select>
    <select id="getUserTotalRechargeBeanByUidList" resultType="java.lang.Integer">
        SELECT IFNULL(sum(recharge_diamond), 0)
        FROM `t_recharge_daily_info`
        WHERE uid IN
        <foreach item="item" collection="aidSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND s_type in (0,1,4,6) AND <![CDATA[ recharge_time >= #{startTime} AND recharge_time < #{endTime} ]]>
    </select>

    <select id="getAllRechargeInfoList" resultMap="baseResultMap">
        SELECT id, recharge_date, pay_type, uid, recharge_money, recharge_diamond, s_type, register_time, recharge_time, os, first_charge, sub_type
        FROM `t_recharge_daily_info`
        WHERE s_type in (0,1,4,6) AND <![CDATA[ recharge_time >= #{startTime} ]]>
    </select>

</mapper>
