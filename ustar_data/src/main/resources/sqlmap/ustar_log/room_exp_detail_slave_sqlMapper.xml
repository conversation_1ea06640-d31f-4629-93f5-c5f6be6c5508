<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.SlaveRoomExpDetailMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.RoomExpDetailData">
        <result column="id" property="id"></result>
        <result column="room_id" property="room_id"></result>
        <result column="date_str" property="date_str"></result>
        <result column="up_mic_people" property="up_mic_people"></result>
        <result column="up_mic_people_ids" property="up_mic_people_ids"></result>
        <result column="up_mic_invite" property="up_mic_invite"></result>
        <result column="up_mic_invite_ids" property="up_mic_invite_ids"></result>
        <result column="up_mic_time" property="up_mic_time"></result>
        <result column="send_gift_person" property="send_gift_person"></result>
        <result column="send_gift_person_ids" property="send_gift_person_ids"></result>
        <result column="send_gift_diamonds" property="send_gift_diamonds"></result>
        <result column="new_member_add" property="new_member_add"></result>
        <result column="new_member_add_ids" property="new_member_add_ids"></result>
        <result column="today_exp" property="today_exp"></result>
        <result column="ctime" property="ctime"></result>
        <result column="mtime" property="mtime"></result>
    </resultMap>


    <select id="getAllByDay" resultMap="baseResultMap" parameterType="String">
        SELECT
        room_id,
        date_str,
        up_mic_people,
        up_mic_invite,
        up_mic_time,
        send_gift_person,
        send_gift_diamonds,
        new_member_add,
        today_exp,
        mtime
        FROM t_room_exp_detail_${dateMonth}
        WHERE date_str = #{dateStr}
    </select>


</mapper>
