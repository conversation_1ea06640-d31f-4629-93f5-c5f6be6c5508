<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar_log.VideoLogMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.VideoLogData">
    </resultMap>
    <sql id="baseSql">
        room_id, video_id, video_title, play_time, duration, ctime
    </sql>
    <sql id="itemSql">
        #{item.roomId},#{item.videoId},#{item.videoTitle},#{item.playTime},#{item.duration},#{item.ctime}
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="item.id" keyColumn="id">
        insert into s_video_log_${tableSuffix} (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>
</mapper>
