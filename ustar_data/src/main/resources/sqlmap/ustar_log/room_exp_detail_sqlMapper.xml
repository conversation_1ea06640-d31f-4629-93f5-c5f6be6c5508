<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar_log.RoomExpDetailMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.RoomExpDetailData">
        <result column="id" property="id"></result>
        <result column="room_id" property="room_id"></result>
        <result column="date_str" property="date_str"></result>
        <result column="up_mic_people" property="up_mic_people"></result>
        <result column="up_mic_people_ids" property="up_mic_people_ids"></result>
        <result column="up_mic_invite" property="up_mic_invite"></result>
        <result column="up_mic_invite_ids" property="up_mic_invite_ids"></result>
        <result column="up_mic_time" property="up_mic_time"></result>
        <result column="send_gift_person" property="send_gift_person"></result>
        <result column="send_gift_person_ids" property="send_gift_person_ids"></result>
        <result column="send_gift_diamonds" property="send_gift_diamonds"></result>
        <result column="new_member_add" property="new_member_add"></result>
        <result column="new_member_add_ids" property="new_member_add_ids"></result>
        <result column="today_exp" property="today_exp"></result>
        <result column="ctime" property="ctime"></result>
        <result column="mtime" property="mtime"></result>
    </resultMap>
    <sql id="baseSql">
        room_id
        ,date_str,up_mic_people,up_mic_people_ids,up_mic_invite,up_mic_invite_ids,up_mic_time
        ,send_gift_person,send_gift_person_ids,send_gift_diamonds,new_member_add,new_member_add_ids
        ,today_exp,ctime,mtime
    </sql>
    <sql id="itemSql">
        #{item.room_id},
        #{item.date_str},
        #{item.up_mic_people},
        #{item.up_mic_people_ids},
        #{item.up_mic_invite},
        #{item.up_mic_invite_ids},
        #{item.up_mic_time},
        #{item.send_gift_person},
        #{item.send_gift_person_ids},
        #{item.send_gift_diamonds},
        #{item.new_member_add},
        #{item.new_member_add_ids},
        #{item.today_exp},
        #{item.ctime},
        #{item.mtime}
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="item.id" keyColumn="id">
        insert into t_room_exp_detail_${tableSuffix} (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>
    <update id="update">
        update t_room_exp_detail_${tableSuffix}
        set room_id=#{item.room_id},
            date_str=#{item.date_str},
            up_mic_people=#{item.up_mic_people},
            up_mic_people_ids=#{item.up_mic_people_ids},
            up_mic_invite=#{item.up_mic_invite},
            up_mic_invite_ids=#{item.up_mic_invite_ids},
            up_mic_time=#{item.up_mic_time},
            send_gift_person=#{item.send_gift_person},
            send_gift_person_ids=#{item.send_gift_person_ids},
            send_gift_diamonds=#{item.send_gift_diamonds},
            new_member_add=#{item.new_member_add},
            new_member_add_ids=#{item.new_member_add_ids},
            today_exp=#{item.today_exp},
            ctime=#{item.ctime},
            mtime=#{item.mtime}
        where id = #{item.id}
    </update>
    <select id="getByRoomId" resultMap="baseResultMap">
        select *
        from t_room_exp_detail_${tableSuffix}
        where room_id = #{room_id}
          and date_str = #{dateStr}
    </select>

    <select id="getAllIds" resultMap="baseResultMap" parameterType="String">
        SELECT
        date_str,
        up_mic_invite_ids,
        new_member_add_ids
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT date_str,up_mic_invite_ids,new_member_add_ids
            FROM t_room_exp_detail_${tableSuffix}
            WHERE room_id = #{room_id}
            AND <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        </foreach>
        ) AS stat
    </select>

    <select id="getAllByDay" resultMap="baseResultMap" parameterType="String">
        SELECT
        room_id,
        date_str,
        up_mic_people,
        up_mic_invite,
        up_mic_time,
        send_gift_person,
        send_gift_diamonds,
        new_member_add,
        today_exp,
        mtime
        FROM t_room_exp_detail_${dateMonth}
        WHERE date_str = #{dateStr}
    </select>


</mapper>
