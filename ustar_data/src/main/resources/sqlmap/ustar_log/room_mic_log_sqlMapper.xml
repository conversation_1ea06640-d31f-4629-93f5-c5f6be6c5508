<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar_log.RoomMicLogMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.RoomMicLogData">
        <result column="id" property="id"></result>
        <result column="room_id" property="roomId"></result>
        <result column="user_id" property="userId"></result>
        <result column="mic_position" property="micPosition"></result>
        <result column="mic_time" property="micTime"></result>
        <result column="os" property="os"></result>
        <result column="rookie_status" property="rookieStatus"></result>
        <result column="version_code" property="versionCode"></result>
        <result column="ctime" property="ctime"></result>
        <result column="mtime" property="mtime"></result>
    </resultMap>
    <sql id="baseSql">
        room_id,user_id,mic_position,mic_time,os,rookie_status,version_code,ctime,mtime
    </sql>
    <sql id="itemSql">
        #{item.roomId},#{item.userId},#{item.micPosition},#{item.micTime},#{item.os},#{item.rookieStatus},#{item.versionCode},#{item.ctime},#{item.mtime}
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="item.id" keyColumn="id">
        insert ignore into s_room_mic_${tableSuffix} (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>

    <select id = "selectUserMicTimeByHour"  resultType="com.quhong.mysql.data.CountData">
        SELECT user_id AS myKey ,IFNULL(sum(mic_time),0) AS `count` FROM  s_room_mic_${tableSuffix}
        WHERE  <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        GROUP BY user_id
    </select>

</mapper>
