<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar_log.UserExpDetailMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.UserExpDetailData">
        <result column="id" property="id"></result>
        <result column="uid" property="uid"></result>
        <result column="date_str" property="date_str"></result>
        <result column="login" property="login"></result>
        <result column="stay_room" property="stay_room"></result>
        <result column="up_mic" property="up_mic"></result>
        <result column="post_moment" property="post_moment"></result>
        <result column="send_msg" property="send_msg"></result>
        <result column="send_msg_ids" property="send_msg_ids"></result>
        <result column="send_room_msg" property="send_room_msg"></result>
        <result column="send_gift" property="send_gift"></result>
        <result column="like_moment" property="like_moment"></result>
        <result column="like_moment_ids" property="like_moment_ids"></result>
        <result column="followed" property="followed"></result>
        <result column="followed_ids" property="followed_ids"></result>
        <result column="become_friends" property="become_friends"></result>
        <result column="become_friends_ids" property="become_friends_ids"></result>
        <result column="homepage_viewed" property="homepage_viewed"></result>
        <result column="homepage_viewed_ids" property="homepage_viewed_ids"></result>
        <result column="moment_liked" property="moment_liked"></result>
        <result column="moment_liked_ids" property="moment_liked_ids"></result>
        <result column="moment_comment" property="moment_comment"></result>
        <result column="moment_comment_ids" property="moment_comment_ids"></result>
        <result column="check_in" property="check_in"></result>
        <result column="today_exp" property="today_exp"></result>
        <result column="ctime" property="ctime"></result>
        <result column="mtime" property="mtime"></result>
    </resultMap>
    <sql id="baseSql">
        uid
        ,date_str,login,stay_room,up_mic,post_moment,send_msg,send_msg_ids,send_room_msg,send_gift,like_moment,like_moment_ids,
        followed,followed_ids,become_friends,become_friends_ids,homepage_viewed,homepage_viewed_ids,moment_liked,
        moment_liked_ids,moment_comment,moment_comment_ids,check_in,today_exp,ctime,mtime
    </sql>
    <sql id="itemSql">
        #{item.uid}
        ,
        #{item.date_str},
        #{item.login},
        #{item.stay_room},
        #{item.up_mic},
        #{item.post_moment},
        #{item.send_msg},
        #{item.send_msg_ids},
        #{item.send_room_msg},
        #{item.send_gift},
        #{item.like_moment},
        #{item.like_moment_ids},
        #{item.followed},
        #{item.followed_ids},
        #{item.become_friends},
        #{item.become_friends_ids},
        #{item.homepage_viewed},
        #{item.homepage_viewed_ids},
        #{item.moment_liked},
        #{item.moment_liked_ids},
        #{item.moment_comment},
        #{item.moment_comment_ids},
        #{item.check_in},
        #{item.today_exp},
        #{item.ctime},
        #{item.mtime}
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="item.id" keyColumn="id">
        insert into t_user_exp_detail_${tableSuffix} (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>
    <update id="update">
        update t_user_exp_detail_${tableSuffix}
        set uid=#{item.uid},
            date_str=#{item.date_str},
            login=#{item.login},
            stay_room=#{item.stay_room},
            up_mic=#{item.up_mic},
            post_moment=#{item.post_moment},
            send_msg=#{item.send_msg},
            send_msg_ids=#{item.send_msg_ids},
            send_room_msg=#{item.send_room_msg},
            send_gift=#{item.send_gift},
            like_moment=#{item.like_moment},
            like_moment_ids=#{item.like_moment_ids},
            followed=#{item.followed},
            followed_ids=#{item.followed_ids},
            become_friends=#{item.become_friends},
            become_friends_ids=#{item.become_friends_ids},
            homepage_viewed=#{item.homepage_viewed},
            homepage_viewed_ids=#{item.homepage_viewed_ids},
            moment_liked=#{item.moment_liked},
            moment_liked_ids=#{item.moment_liked_ids},
            moment_comment=#{item.moment_comment},
            moment_comment_ids=#{item.moment_comment_ids},
            check_in=#{item.check_in},
            today_exp=#{item.today_exp},
            ctime=#{item.ctime},
            mtime=#{item.mtime}
        where id = #{item.id}
    </update>
    <select id="getByUid" resultMap="baseResultMap">
        select *
        from t_user_exp_detail_${tableSuffix}
        where uid = #{uid}
          and date_str = #{dateStr}
    </select>
</mapper>
