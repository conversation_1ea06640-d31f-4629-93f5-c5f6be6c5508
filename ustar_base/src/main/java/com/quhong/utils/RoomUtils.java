package com.quhong.utils;

import org.springframework.util.StringUtils;

public class RoomUtils {
    public static String formatRoomId(String uid){
        return "r:" + uid;
    }

    public static String getRoomHostId(String roomId){
        if (roomId == null || roomId.length() < 2) {
            return "";
        }
        return roomId.substring(2);
    }

    public static boolean isHomeowner(String uid, String roomId) {
        if(StringUtils.isEmpty(roomId) || StringUtils.isEmpty(uid)) {
            return false;
        }
        return roomId.contains(uid);
    }

    public static String formatGameRoomId(String uid) {
        return "g:" + uid;
    }

    public static Boolean isGameRoom(String roomId) {
        return StringUtils.hasLength(roomId) && roomId.startsWith("g:");
    }

    public static Boolean isVoiceRoom(String roomId) {
        return StringUtils.hasLength(roomId) && roomId.startsWith("r:");
    }

    public static String getRoomId(String roomId) {
        return StringUtils.hasLength(roomId) && roomId.length() >= 2 ? "r:" + roomId.substring(2) : "";
    }

    public static String getGameRoomId(String roomId) {
        return StringUtils.hasLength(roomId) && roomId.length() >= 2 ? "g:" + roomId.substring(2) : "";
    }
}
