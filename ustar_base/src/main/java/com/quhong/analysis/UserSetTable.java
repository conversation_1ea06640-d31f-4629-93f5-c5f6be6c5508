package com.quhong.analysis;


public class UserSetTable {
    private int beans;
    private int fb_gender;
    private int age;
    private int login_type;
    private String uid;
    private int accept_talk;
    private String ip;
    private String tn_id;
    private String channel;
    private int is_face;
    private String app_package_name;
    private int version_code;
    private int heart_got;
    private String idfa;
    private String os;
    private int rid;
    private int generation_time;
    private String ta_device_id;
    private int friends_number;
    private int following_number;
    private int followers_number;
    private String ip_country;
    private String account_country;
    private int vip_level;
    private int account_level;
    private int ban_state;
    private int ban_over_time;
    private String nickname;
    private int account_lang;
    private String device_lang;
    private int last_online_time;
    private String dynamic_channel;
    private int recharge;
    private int robot;
    private String email;
    private String shu_mei_id ;

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getFb_gender() {
        return fb_gender;
    }

    public void setFb_gender(int fb_gender) {
        this.fb_gender = fb_gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public int getLogin_type() {
        return login_type;
    }

    public void setLogin_type(int login_type) {
        this.login_type = login_type;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getAccept_talk() {
        return accept_talk;
    }

    public void setAccept_talk(int accept_talk) {
        this.accept_talk = accept_talk;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getTn_id() {
        return tn_id;
    }

    public void setTn_id(String tn_id) {
        this.tn_id = tn_id;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getIs_face() {
        return is_face;
    }

    public void setIs_face(int is_face) {
        this.is_face = is_face;
    }

    public String getApp_package_name() {
        return app_package_name;
    }

    public void setApp_package_name(String app_package_name) {
        this.app_package_name = app_package_name;
    }

    public int getVersion_code() {
        return version_code;
    }

    public void setVersion_code(int version_code) {
        this.version_code = version_code;
    }

    public int getHeart_got() {
        return heart_got;
    }

    public void setHeart_got(int heart_got) {
        this.heart_got = heart_got;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getGeneration_time() {
        return generation_time;
    }

    public void setGeneration_time(int generation_time) {
        this.generation_time = generation_time;
    }

    public String getTa_device_id() {
        return ta_device_id;
    }

    public void setTa_device_id(String ta_device_id) {
        this.ta_device_id = ta_device_id;
    }

    public int getFriends_number() {
        return friends_number;
    }

    public void setFriends_number(int friends_number) {
        this.friends_number = friends_number;
    }

    public int getFollowing_number() {
        return following_number;
    }

    public void setFollowing_number(int following_number) {
        this.following_number = following_number;
    }

    public int getFollowers_number() {
        return followers_number;
    }

    public void setFollowers_number(int followers_number) {
        this.followers_number = followers_number;
    }

    public String getIp_country() {
        return ip_country;
    }

    public void setIp_country(String ip_country) {
        this.ip_country = ip_country;
    }

    public String getAccount_country() {
        return account_country;
    }

    public void setAccount_country(String account_country) {
        this.account_country = account_country;
    }

    public int getVip_level() {
        return vip_level;
    }

    public void setVip_level(int vip_level) {
        this.vip_level = vip_level;
    }

    public int getAccount_level() {
        return account_level;
    }

    public void setAccount_level(int account_level) {
        this.account_level = account_level;
    }

    public int getBan_state() {
        return ban_state;
    }

    public void setBan_state(int ban_state) {
        this.ban_state = ban_state;
    }

    public int getBan_over_time() {
        return ban_over_time;
    }

    public void setBan_over_time(int ban_over_time) {
        this.ban_over_time = ban_over_time;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getAccount_lang() {
        return account_lang;
    }

    public void setAccount_lang(int account_lang) {
        this.account_lang = account_lang;
    }

    public String getDevice_lang() {
        return device_lang;
    }

    public void setDevice_lang(String device_lang) {
        this.device_lang = device_lang;
    }

    public int getLast_online_time() {
        return last_online_time;
    }

    public void setLast_online_time(int last_online_time) {
        this.last_online_time = last_online_time;
    }

    public String getDynamic_channel() {
        return dynamic_channel;
    }

    public void setDynamic_channel(String dynamic_channel) {
        this.dynamic_channel = dynamic_channel;
    }

    public int getRecharge() {
        return recharge;
    }

    public void setRecharge(int recharge) {
        this.recharge = recharge;
    }

    public int getRobot() {
        return robot;
    }

    public void setRobot(int robot) {
        this.robot = robot;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getShu_mei_id() {
        return shu_mei_id;
    }

    public void setShu_mei_id(String shu_mei_id) {
        this.shu_mei_id = shu_mei_id;
    }
}
