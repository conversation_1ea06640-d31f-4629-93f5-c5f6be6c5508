package com.quhong.analysis;

/**
 * 日活事件
 */
public class DAUEvent extends UserEvent {
    private int version_code;
    private String app_ver;
    private int is_new;
    private int dau_source_type;

    public DAUEvent() {
    }

    public DAUEvent(String uid, int version_code, int is_new, String app_ver, int dau_source_type) {
        this.uid = uid;
        this.version_code = version_code;
        this.is_new = is_new;
        this.app_ver = app_ver;
        this.dau_source_type = dau_source_type;
    }

    @Override
    public String getEventName() {
        return "day_active";
    }

    @Override
    public int getEventTime() {
        return 0;
    }

    public int getVersion_code() {
        return version_code;
    }

    public void setVersion_code(int version_code) {
        this.version_code = version_code;
    }

    public int getIs_new() {
        return is_new;
    }

    public void setIs_new(int is_new) {
        this.is_new = is_new;
    }

    public int getDau_source_type() {
        return dau_source_type;
    }

    public void setDau_source_type(int dau_source_type) {
        this.dau_source_type = dau_source_type;
    }
}
