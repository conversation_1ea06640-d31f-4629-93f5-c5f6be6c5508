package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.TakeMicDailyReward;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.USER_WEB_TASK_COMPLETION_MSG)
public class UserWebTaskCompletionMsg extends MarsServerMsg {

    private String taskKey;
    private int showGuide; // 是否展示引导提示
    private int showPopUp; // 是否展示顶部任务奖励弹窗 0: 不展示  1: 展示
    /**
     * 日常任务上麦奖励
     */
    private TakeMicDailyReward takeMicDailyReward;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.UserWebTaskCompletionMessage msg = YoustarProtoRoom.UserWebTaskCompletionMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.taskKey = msg.getTaskKey();
        this.showGuide = msg.getShowGuide();
        this.showPopUp = msg.getShowPopUp();
        this.takeMicDailyReward = new TakeMicDailyReward();
        this.takeMicDailyReward.doFromBody(msg.getTakeMicDailyReward());
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.UserWebTaskCompletionMessage.Builder builder = YoustarProtoRoom.UserWebTaskCompletionMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setTaskKey(taskKey);
        builder.setShowGuide(showGuide);
        builder.setShowPopUp(showPopUp);
        if (this.takeMicDailyReward != null){
            builder.setTakeMicDailyReward(this.takeMicDailyReward.doToBody());
        }
        return builder.build().toByteArray();
    }

    public String getTaskKey() {
        return taskKey;
    }

    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }

    public int getShowGuide() {
        return showGuide;
    }

    public void setShowGuide(int showGuide) {
        this.showGuide = showGuide;
    }

    public int getShowPopUp() {
        return showPopUp;
    }

    public void setShowPopUp(int showPopUp) {
        this.showPopUp = showPopUp;
    }

    public TakeMicDailyReward getTakeMicDailyReward() {
        return takeMicDailyReward;
    }

    public void setTakeMicDailyReward(TakeMicDailyReward takeMicDailyReward) {
        this.takeMicDailyReward = takeMicDailyReward;
    }
}
