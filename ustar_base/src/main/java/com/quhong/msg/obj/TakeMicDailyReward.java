package com.quhong.msg.obj;

import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoRoom;

/**
 * <AUTHOR>
 * @date 2025/9/9 10:24
 */
public class TakeMicDailyReward implements IProto<YoustarProtoRoom.TakeMicDailyReward> {
    /**
     * 奖励图标
     */
    private String icon;

    /**
     * 奖励数量，钻石数或礼物背包数
     */
    private Integer number;

    /**
     * 奖励时间，例如多少天
     */
    private Integer time;

    /**
     * 奖励类型，参见 {@link com.quhong.enums.BaseDataResourcesConstant}资源类型
     */
    private Integer type;

    @Override
    public void doFromBody(YoustarProtoRoom.TakeMicDailyReward proto) {
        this.icon = proto.getIcon();
        this.number = proto.getNumber();
        this.time = proto.getTime();
        this.type = proto.getType();
    }

    @Override
    public YoustarProtoRoom.TakeMicDailyReward.Builder doToBody() {
        YoustarProtoRoom.TakeMicDailyReward.Builder builder = YoustarProtoRoom.TakeMicDailyReward.newBuilder();
        builder.setIcon(this.icon == null ? "" : this.icon);
        builder.setNumber(this.number == null ? 0 : this.number);
        builder.setTime(this.time == null ? 0 : this.time);
        builder.setType(this.type == null ? 0 : this.type);
        return builder;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getTime() {
        return time;
    }

    public void setTime(Integer time) {
        this.time = time;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
