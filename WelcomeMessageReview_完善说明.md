# WelcomeMessageReviewService 完善说明

## 完成的功能

### 1. 创建审核状态常量类
- 文件：`ustar_data/src/main/java/com/quhong/constant/WelcomeMessageReviewConstant.java`
- 定义了审核状态常量：
  - `REVIEW_STATUS_PENDING = 1` (待审核)
  - `REVIEW_STATUS_APPROVED = 2` (通过)
  - `REVIEW_STATUS_REJECTED = 3` (拒绝)
- 定义了拒绝原因最大长度：`REJECT_REASON_MAX_LENGTH = 50`
- 定义了分表数量：`TABLE_COUNT = 16`

### 2. 完善WelcomeMessageReviewMapper
- 文件：`ustar_data/src/main/java/com/quhong/mysql/mapper/ustar/WelcomeMessageReviewMapper.java`
- 参考SendRecvFirstMapper实现，添加了以下方法：
  - `insert()` - 插入单条记录
  - `batchInsert()` - 批量插入记录
  - `updateReviewAction()` - 更新审核状态
  - `updateContent()` - 更新消息内容
  - `delete()` - 删除记录
  - `selectPageList()` - 分页查询，支持roomId、reviewAction、operatorUid过滤，按submitTime倒序
  - `selectCount()` - 查询总数
  - `selectById()` - 根据ID查询单条记录

### 3. 完善WelcomeMessageReviewDao
- 文件：`ustar_data/src/main/java/com/quhong/mysql/dao/WelcomeMessageReviewDao.java`
- 实现了所有业务方法：
  - `selectPageList()` - 分页查询，支持过滤条件
  - `insert()` - 插入记录，自动设置时间戳
  - `batchInsert()` - 批量插入，自动设置时间戳
  - `delete()` - 删除记录
  - `updateContent()` - 更新内容，同时重置审核状态为待审核
  - `updateReviewAction()` - 更新审核状态
  - `selectById()` - 根据ID查询记录
- 实现了分表逻辑：
  - 按roomId最后一位分16个表
  - 支持数字和字符的roomId
  - 表名格式：`t_welcome_message_review_0` 到 `t_welcome_message_review_15`

### 4. 完善WelcomeMessageReviewService
- 文件：`src/main/java/com/quhong/operation/server/WelcomeMessageReviewService.java`
- 完善了业务逻辑：
  - `selectPageList()` - 分页查询，支持roomId、reviewAction、operatorUid过滤，结果按submitTime倒序
  - `updateReviewAction()` - 修改审核状态，包含完整的业务校验
- 实现了业务规则校验：
  - 状态为拒绝时必须设置原因
  - 拒绝原因不能超过50个字符
  - 已通过的记录不能再修改为其他状态
  - 完整的参数校验

## 核心业务规则实现

### 1. 修改审核状态规则
```java
// 1. 状态为拒绝时必须设置原因，原因小于50个字符
if (REVIEW_STATUS_REJECTED.equals(reviewAction)) {
    if (!StringUtils.hasText(rejectReason)) {
        throw new CommonH5Exception("拒绝时必须设置拒绝原因");
    }
    if (rejectReason.length() > REJECT_REASON_MAX_LENGTH) {
        throw new CommonH5Exception("拒绝原因不能超过50个字符");
    }
}

// 2. 已通过的不能再修改为其他状态
if (REVIEW_STATUS_APPROVED.equals(currentData.getReviewAction())) {
    throw new CommonH5Exception("已通过的记录不能再修改状态");
}
```

### 2. 分页查询支持过滤
- 支持roomId过滤
- 支持reviewAction过滤
- 支持operatorUid过滤
- 结果按submitTime倒序排列

### 3. 分表策略
- 分16个表，按roomId最后一位分表
- 表名：`t_welcome_message_review_0` 到 `t_welcome_message_review_15`
- 支持数字和字符的roomId

## 使用示例

### 分页查询
```java
WelcomeMessageCondition condition = new WelcomeMessageCondition();
condition.setRoomId("12345");
condition.setReviewAction(1); // 待审核
condition.setPage(1);
condition.setPageSize(20);

PageResultVO<OpWelcomeMessageReviewVO> result = service.selectPageList(condition);
```

### 修改审核状态
```java
WelcomeMessageCondition condition = new WelcomeMessageCondition();
condition.setRoomId("12345");
condition.setId(1);
condition.setReviewAction(3); // 拒绝
condition.setRejectReason("内容不合规");

service.updateReviewAction("operatorUid", condition);
```

## 注意事项

1. 查询时建议指定roomId以提高查询效率
2. 拒绝原因最大长度为50个字符
3. 已通过的记录不能再修改状态
4. 所有时间字段使用秒级时间戳
5. 分表按roomId最后一位进行，支持0-15共16个表
