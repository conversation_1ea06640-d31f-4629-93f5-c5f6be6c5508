package com.quhong.intercepters;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.SpringUtils;
import com.quhong.enums.HttpCode;
import com.quhong.enums.LogType;
import com.quhong.enums.SLangType;
import com.quhong.handler.BaseController;
import com.quhong.handler.HttpEnvData;
import com.quhong.monitor.MonitorChecker;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * create by maoyule on 2019/3/30
 */
public class UserInterceptor extends BaseController implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(UserInterceptor.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);

    public static final String CLIENT_LANG = "client_lang";
    public static final String REQUEST_ATTR_START_TIME = "startTime";
    // 使用包含该字符串的接口可以不校验uid和token
    public static final String VISITOR = "/visitor/";
    public static final String REGISTER_OR_LOGIN = "register_or_login";
    public static final String START_PAGE = "start_page";
    public static final String BANNER = "user_info/banner";
    public static final String RESOURCE_VERSION = "resource_version";

    private static BasePlayerRedis playerRedis;
    private final MonitorChecker monitorChecker;
    private static final long DEFAULT_WARNING_TIME = 2000;
    protected Map<String, Long> requestWarnDurationMap;

    public UserInterceptor() {
        this(new HashMap<>());
    }

    public UserInterceptor(Map<String, Long> requestWarnDurationMap) {
        this.monitorChecker = new MonitorChecker("request_too_long");
        this.requestWarnDurationMap = requestWarnDurationMap;
    }

    public String genRequestId(String uid) {
        return uid + "-" + UUID.randomUUID().toString().substring(0, 8);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String remoteIp = RequestUtils.getIpAddress(request);
        JSONObject jsonObject = RequestUtils.parseSendData(request);
        request.setAttribute(REQUEST_ATTR_START_TIME, System.currentTimeMillis());
        if (jsonObject == null) {
            logger.info("jsonObject is null. path={} remoteAddress={}", request.getRequestURI(), remoteIp);
            sendResult(response, null, HttpCode.PARAM_ERROR);
            return false;
        }
        setLocale(request, jsonObject.getIntValue("slang"));
        request.setAttribute("responseEncode", 1);
        String uid = jsonObject.getString("uid");
        String token = jsonObject.getString("token");
        MDC.put(RequestUtils.REQUEST_ID, genRequestId(uid));
        msgLogger.info("request pre handler. ip={} path={}", remoteIp, request.getRequestURI());
        int debug = jsonObject.getIntValue("debug");
        if (ServerConfig.isNotProduct() && 1 == debug && StringUtils.isEmpty(token)) {
            return true;
        }
        if (!checkParam(uid, token)) {
            // 访问访客接口
            if (request.getRequestURI().contains(VISITOR) || request.getRequestURI().contains(REGISTER_OR_LOGIN)
                    || request.getRequestURI().contains(START_PAGE) || request.getRequestURI().contains(BANNER)
                    || request.getRequestURI().contains(RESOURCE_VERSION)) {
                return true;
            }
            logger.info("params has null param. path={} uid={} token={} remoteAddress={}", request.getRequestURI(), uid, token, remoteIp);
            sendResult(response, uid, HttpCode.PARAM_ERROR);
            return false;
        }
        if (playerRedis == null) {
            playerRedis = SpringUtils.getBean(BasePlayerRedis.class);
        }
        String redisToken = playerRedis.getToken(uid);
        if (!token.equals(redisToken)) {
            logger.info("token verify failed. path={} uid={} token={}, redisToken={}", request.getRequestURI(), uid, token, redisToken);
            sendResult(response, uid, HttpCode.SESSION_INVALID);
            return false;
        }
        return true;
    }

    private boolean checkParam(String uid, String token) {
        if (StringUtils.isEmpty(uid)) {
            return false;
        }
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        return true;
    }

    public void setLocale(HttpServletRequest request, int slang) {
        try {
            request.setAttribute(CLIENT_LANG, slang == SLangType.ENGLISH ? "en_US" : "ar");
        } catch (Exception e) {
            logger.error("set locale error, slang={} error message={}", slang, e.getMessage(), e);
        }
    }

    private void sendResult(HttpServletResponse response, String uid, HttpCode httpCode) {
        try {
            HttpEnvData envData = new HttpEnvData();
            envData.setUid(uid);
            String result = createError(envData, httpCode);
            response.addHeader("Content-Type", "text/html; charset=utf-8");
            response.getOutputStream().write(result.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            logger.error("response error.uid={} {}", uid, e.getMessage(), e);
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        checkRequestTime(request);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        MDC.clear();
    }

    private void checkRequestTime(HttpServletRequest request) {
        long startTime = (long) request.getAttribute(REQUEST_ATTR_START_TIME);
        long endTime = System.currentTimeMillis();
        long requestTime = endTime - startTime;
        String requestURI = request.getRequestURI();
        Long limitRequestTime = requestWarnDurationMap.get(requestURI);
        if (limitRequestTime == null) {
            limitRequestTime = DEFAULT_WARNING_TIME;
        }
        if (requestTime >= limitRequestTime) {
            String desc = "request: " + requestURI + ": use too long time = " + requestTime + ": min_time = " + limitRequestTime + " [requestId]:" + MDC.get(RequestUtils.REQUEST_ID);
            String detail = "{req_data:%s}";
            JSONObject jsonObject = RequestUtils.parseSendData(request);
            if (jsonObject != null) {
                String vname = jsonObject.getString("vname");
                if (null != vname && vname.contains("dev")) {
                    return;
                }
            }
            detail = String.format(detail, null == jsonObject ? "" : jsonObject.toString());
            monitorChecker.startWarning(desc, detail);
            logger.info("start warn request time too long. url={} time={}", requestURI, requestTime);
        }
    }
}
