package com.quhong.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.ClientOS;
import com.quhong.enums.PKGConstant;
import com.quhong.utils.RequestUtils;
import org.slf4j.MDC;

import java.io.Serializable;

public class HttpEnvData implements Serializable {
    protected String uid;
    protected int os; // 系统 1 为ios，其他为android
    protected int versioncode; //客户端版本号
    /**
     * @see com.quhong.enums.SLangType
     */
    protected int slang; // 设置语言
    protected int new_versioncode;
    protected int debug;
    protected String roomId;
    protected int encoding; // 0 AES 1 明文，2 Gzip
    protected String app_package_name;
    protected String device_model;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getOs() {
        return os;
    }

    public void setOs(int os) {
        this.os = os;
    }

    public int getVersioncode() {
        return versioncode;
    }

    public void setVersioncode(int versioncode) {
        this.versioncode = versioncode;
    }

    public int getSlang() {
        return slang;
    }

    public void setSlang(int slang) {
        this.slang = slang;
    }

    public int getNew_versioncode() {
        return new_versioncode;
    }

    public void setNew_versioncode(int new_versioncode) {
        this.new_versioncode = new_versioncode;
    }

    public int getDebug() {
        return debug;
    }

    public void setDebug(int debug) {
        this.debug = debug;
    }

    public String getRequestId() {
        return MDC.get(RequestUtils.REQUEST_ID);
    }

    public int getEncoding() {
        return encoding;
    }

    public void setEncoding(int encoding) {
        this.encoding = encoding;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getApp_package_name() {
        return app_package_name;
    }

    public void setApp_package_name(String app_package_name) {
        this.app_package_name = app_package_name;
    }

    public String getDevice_model() {
        return device_model;
    }

    public void setDevice_model(String device_model) {
        this.device_model = device_model;
    }

    public static HttpEnvData create(JSONObject paramObj) {
        HttpEnvData envData = new HttpEnvData();
        envData.uid = paramObj.getString("uid");
        String os = paramObj.getString("os");
        if ("1".equals(os)) {
            // 1 为 ios
            envData.os = ClientOS.IOS;
        } else {
            // 其他为android
            envData.os = ClientOS.ANDROID;
        }
        envData.versioncode = paramObj.getIntValue("versioncode");
        envData.new_versioncode = paramObj.getIntValue("new_versioncode");
        envData.slang = paramObj.getIntValue("slang");
        envData.debug = paramObj.getIntValue("debug");
        envData.app_package_name = paramObj.getString("app_package_name");
        envData.device_model = paramObj.getString("device_model");
        return envData;
    }

    public boolean invalidPkgName() {
        if (os == ClientOS.IOS) {
//            return !PKGConstant.IOS_MAIN.equals(app_package_name);
            return false;
        } else {
            return !PKGConstant.ANDROID_YOUSTAR.equals(app_package_name)
                    && !PKGConstant.ANDROID_YOUSTAR_LITE.equals(app_package_name)
                    && !PKGConstant.ANDROID_YOUSTAR_MEET.equals(app_package_name);
        }
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }


}
