package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
@Lazy
public class ImageDetectionApi {
    private static final Logger logger = LoggerFactory.getLogger(ImageDetectionApi.class);

    @Resource
    private WebClient webClient;

    private static final String TEST_URL = "http://internal-ustar-test-1454368070.ap-south-1.elb.amazonaws.com/detection/nsfw?img_url=";
    private static final String PROD_URL = "http://internal-ustar-prod-1465074082.ap-south-1.elb.amazonaws.com/detection/nsfw?img_url=";

    public boolean isSafeImage(String imageUrl) {
        try {
            String url = ServerConfig.isProduct() ? PROD_URL : TEST_URL;
            HttpResponseData<String> responseData = webClient.sendGet(url + imageUrl, null);
            String body = responseData.getBody();
            if (!StringUtils.isEmpty(body)) {
                JSONObject jsonObject = JSON.parseObject(body);
                logger.info("detection return imageUrl={} result={}", imageUrl, jsonObject);
                JSONObject data = jsonObject.getJSONObject("data");
                if (null != data) {
                    int nsfw_score = data.getIntValue("nsfw_score");
                    int scale = data.getIntValue("scale");
                    return nsfw_score < 0.4 * scale;
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("image detection error.", e);
            return true;
        }
    }
}
