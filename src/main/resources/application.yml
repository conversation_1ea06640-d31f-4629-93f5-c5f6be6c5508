baseUrl: /room_service/
server:
  port: 8080
spring:
  application:
    name: ustar-room-service
  messages:
    basename: i18n/roomService
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
ribbon:
  Readtimeout: 15000
  ConnectTimeout: 15000
feign:
  hystrix:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 15000
        readTimeout: 10000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 15000