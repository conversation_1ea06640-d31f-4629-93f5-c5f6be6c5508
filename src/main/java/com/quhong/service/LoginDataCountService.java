package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.TryLoginEvent;
import com.quhong.analysis.UserSetTable;
import com.quhong.constant.EventConstant;
import com.quhong.constant.LoginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.PyBlockAccountDTO;
import com.quhong.data.dto.ShuMeiDeviceDTO;
import com.quhong.enums.ClientOS;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.PKGConstant;
import com.quhong.enums.UserLevelConstant;
import com.quhong.mongo.dao.FollowDao;
import com.quhong.mongo.dao.LoginActorDao;
import com.quhong.mongo.dao.UserRegisterDao;
import com.quhong.mongo.data.MongoLoginActorData;
import com.quhong.mongo.data.UserRegisterInfoData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.BlockRedis;
import com.quhong.redis.FriendsListRedis;
import com.quhong.redis.ThirdApiCallRedis;
import com.quhong.redis.TnCheckRedis;
import com.quhong.service.impl.RobotLoginService;
import com.quhong.service.mysql.AccountStatusService;
import com.quhong.service.mysql.MySQLActorService;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.BitUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class LoginDataCountService {
    private static final Logger logger = LoggerFactory.getLogger(LoginDataCountService.class);
    private static final List<Integer> TN_LIMIT_REQUEST_COUNT = Arrays.asList(20000, 40000);
    private static final String PY_MAIL_QUEUE = "tn_add_account";
    private static Map<String, String> PKG_MAP_CHANNEL = new HashMap<>();
    private static final String HUA_WEI_CHANNEL = "huawei";
    private static Map<Integer, String> STATUS_MAP_DESC = new HashMap<>();
    private static final String USER_REGISTER = "user_register";
    private static final List<Integer> DEVICE_WARN_TYPE = Arrays.asList(216);

    @Autowired
    private MonitorSender monitorSender;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private TnCheckRedis tnCheckRedis;
    //    @Resource
//    private RedisTaskService redisTaskService;
    @Resource
    private UserRegisterDao userRegisterDao;
    @Resource
    private LoginActorDao loginActorDao;
    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private AccountStatusService accountStatusService;
    @Resource
    private LoginLogService loginLogService;
    @Resource
    private AppleLoginService appleLoginService;
    @Resource
    private DAUDao dAUDao;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private FollowDao followDao;
    @Resource
    private RechargeRedis rechargeRedis;
    @Resource
    private MySQLActorService mySQLActorService;
    @Resource
    private AdminApi adminApi;
    @Resource
    private RegisterOrLoginLogDao registerOrLoginLogDao;
    @Resource
    private TnDeviceAccountDao tnDeviceAccountDao;
    @Resource
    private RobotLoginService robotLoginService;
    @Resource
    private RiskUserDao riskUserDao;
    @Resource
    private SlaveRegisterOrLoginLogDao slaveRegisterOrLoginLogDao;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ThirdApiCallRedis thirdApiCallRedis;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private AccountService accountService;
    @Resource
    private AppsFlyerService appsFlyerService;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private GreetUserService greetUserService;
    @Resource
    private ActorCommonService actorCommonService;

    @PostConstruct
    public void init() {
        PKG_MAP_CHANNEL.put(PKGConstant.ANDROID_YOUSTAR, "Google Main");
        PKG_MAP_CHANNEL.put(PKGConstant.ANDROID_YOUSTAR_LITE, "Google Pro");
        PKG_MAP_CHANNEL.put(PKGConstant.ANDROID_YOUSTAR_MEET, "Ustar");
        PKG_MAP_CHANNEL.put(PKGConstant.IOS_MAIN, "App Store");

        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_SUCCESS, "success");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_ACCOUNT_INVALID, "account_invalid");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_DEVICE_INVALID, "device_invalid");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_DEVICE_EMULATOR, "device_emulator");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_DEVICE_LANGUAGE, "device_language");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_DEVICE_NUM, "device_num");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_G, "google_login_fail");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_FB, "facebook_login_fail");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_MAIL, "mail_login_fail");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_PHONE, "phone_login_fail");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_APPLE, "apple_login_fail");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_HUAWEI, "huawei_login_fail");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_VIP, "vip_login_fail");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_TN, "tn_login_fail");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR, "sever_parm_error");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_SEVER_TIME_OUT, "sever_lock_time_out_error");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_SEVER_GEN_RID_ERROR, "sever_gen_rid_error");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_SEVER_OTHER_ERROR, "sever_other_error");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_IP_LIMIT_ERROR, "user_ip_limit");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_DEVICE_LIMIT_USER, "device_limit_user");
        STATUS_MAP_DESC.put(LoginConstant.LOGIN_STATE_ACCOUNT_RISK_ZERO, "account_risk_zero");
    }

    public void recordLoginStatusToTga(RegisterOrLoginContext context, int loginStatus) {
        recordLoginStatusToTga(context, loginStatus, false);
    }

    public void recordLoginStatusToTga(RegisterOrLoginContext context, int loginStatus, boolean isBanAccount) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                try {
                    TryLoginEvent tryLoginEvent = new TryLoginEvent();
                    int isRegister = context.isRegister() == null ? 0 : context.isRegister() ? 1 : 2;
                    String pkgName = getPkgName(context);
                    String channel = getChannelByPkg(pkgName, context.getChannel());
                    Integer deviceRid = null;
                    String deviceUid = "";
                    String extraStr = "";
                    String thirdUid = context.getThirdUid();
                    String desc = STATUS_MAP_DESC.getOrDefault(loginStatus, "");
                    int os = context.getOs() == 1 ? context.getOs() : 0;
                    TnRespondsData tnRespondsData = context.getTnRespondsData();
                    if (tnRespondsData != null) {
                        Map<String, Object> extraMap = tnRespondsData.getExtraInfo();
                        if (extraMap != null) {
                            JSONObject ob = new JSONObject(extraMap);
                            extraStr = ob.toJSONString();
                        }
                    }
                    if (context.getType() == LoginConstant.FIRE_BASE_PHONE_TYPE && isRegister == 2 && context.getMongoLoginActorData() != null) {
                        thirdUid = context.getMongoLoginActorData().getUid();
                    }
                    String email = StringUtils.isEmpty(context.getRealEmail()) ? StringUtils.isEmpty(context.getEmail()) ?
                            "" : context.getEmail() : context.getRealEmail();
                    String reqId = robotLoginService.getReqId(context);
                    String riskStr = null == context.getTnRisk() ? "" : context.getTnRisk().toString();

                    tryLoginEvent.setUid(context.getUid());
                    tryLoginEvent.setIsRegister(isRegister);
                    tryLoginEvent.setLoginStatus(loginStatus);
                    tryLoginEvent.setLoginStatusDesc(desc);
                    tryLoginEvent.setRid(context.getRid());
                    if (null != context.getShowIdData()) {
                        ShowIdData showIdData = context.getShowIdData();
                        deviceRid = showIdData.getRid();
                        deviceUid = showIdData.getUid();
                        tryLoginEvent.setDeviceRid(deviceRid);
                        tryLoginEvent.setDeviceUid(deviceUid);
                    }
                    tryLoginEvent.setTnId(context.getTnId());
                    tryLoginEvent.setvName(context.getVname());
                    tryLoginEvent.setvCode(context.getVersioncode());
                    tryLoginEvent.setLoginType(context.getType());
                    tryLoginEvent.setPkgName(pkgName);
                    tryLoginEvent.setChannel(channel);
                    tryLoginEvent.setDynamicChannel(context.getPromotion_id());
                    tryLoginEvent.setOs(os);
                    tryLoginEvent.setTaDeviceId(context.getTa_device_id());
                    tryLoginEvent.setEmail(email);
                    if (context.getNowDeviceAccountNum() != null && context.getNowDeviceAccountNum() == 1) {
                        tryLoginEvent.setIs_new_device(1);
                    } else {
                        tryLoginEvent.setIs_new_device(0);
                    }
//                    if  (StringUtils.hasLength(context.getShuMeiId())) {
//                        tryLoginEvent.setShu_mei_risk(context.getShuMeiRisk().toString());
//                    }
//                    tryLoginEvent.setShu_mei_id(context.getShuMeiId());
                    tryLoginEvent.setTn_risk(riskStr);
                    tryLoginEvent.setReqId(robotLoginService.getReqId(context));

                    EventDTO eventDto = new EventDTO(context.getUid(), tryLoginEvent.getEventName(), tryLoginEvent);
                    eventDto.setDistinctId(context.getDistinct_id());
                    eventReport.track(eventDto);

                    RegisterOrLoginLogData logData = new RegisterOrLoginLogData();
                    logData.setReqId(robotLoginService.getReqId(context));
                    logData.setUid(context.getUid());
                    logData.setRid(context.getRid());
                    logData.setThirdUid(thirdUid);
                    logData.setTnId(context.getTnId());
                    logData.setTnRisk(riskStr);
                    logData.setTnExtraInfo(extraStr);
                    logData.setTnUseCache(context.isUseCacheTn() ? 1 : 0);
                    logData.setRegisterType(isRegister);
                    logData.setLoginType(context.getType());
                    logData.setLoginStatus(loginStatus);
                    logData.setLoginStatusDesc(desc);
                    logData.setDeviceRid(deviceRid);
                    logData.setDeviceUid(deviceUid);
                    logData.setAndroidId(context.getAndroidid());
                    logData.setIosKey(context.getP3());
                    logData.setIdfa(context.getIdfa());
                    logData.setDistinctId(context.getDistinct_id());
                    logData.setTaDeviceId(context.getTa_device_id());
                    logData.setCtime(DateHelper.getNowSeconds());
                    logData.setPkgName(pkgName);
                    logData.setvCode(context.getVersioncode());
                    logData.setvName(context.getVname());
                    logData.setDynamicChannel(context.getPromotion_id());
                    logData.setIp(context.getIp());
                    logData.setIpCountry(context.getIpCountry());
                    logData.setAccount(context.getAccount());
                    logData.setNowDeviceAccountNum(context.getNowDeviceAccountNum());
                    logData.setDeviceHonorUid(context.getDeviceHonorUid());
                    logData.setDeviceLevelUid(context.getDeviceLevelUid());
                    logData.setFbGender(context.getFbGender());
                    logData.setOs(os);
                    logData.setTokenExpired(context.getTokenExpired());
//                    logData.setShuMeiId(context.getShuMeiId());
//                    logData.setShuMeiRisk(null == context.getShuMeiRisk() ? "" : context.getShuMeiRisk().toString());
                    registerOrLoginLogDao.insert(logData);

                    if (isBanAccount && !StringUtils.isEmpty(context.getUid()) && !StringUtils.isEmpty(context.getTnId())) {
                        PyBlockAccountDTO pyBlockAccountDTO = new PyBlockAccountDTO();
                        pyBlockAccountDTO.setUid(AdminApi.SYSTEM_ROBOT);
                        pyBlockAccountDTO.setUser_uid(context.getUid());
                        pyBlockAccountDTO.setCmd(1);
                        String remark = "ban device login:" + context.getTnId();
                        pyBlockAccountDTO.setRemark(remark);

                        int blockCtime = (int) blockRedis.getBlockLoginCtime(context.getTnId());
                        int blockTime = blockCtime - DateHelper.getNowSeconds();
                        int block_term = 3;
                        if (blockTime > 0 && blockCtime < BlockRedis.FOREVER_BLOCK) {
                            blockTime /= TimeUnit.DAYS.toSeconds(1);
                            if (blockTime < 1) {
                                block_term = 1;
                            } else if (blockTime < 7) {
                                block_term = 2;
                            } else if (blockTime < 30) {
                                block_term = 5;
                            }
                        }
                        pyBlockAccountDTO.setRid(String.valueOf(context.getRid()));
                        pyBlockAccountDTO.setReason(remark);
                        pyBlockAccountDTO.setBlock_term(String.valueOf(block_term));
                        pyBlockAccountDTO.setM_type(3);


                        boolean ret = adminApi.blockAccount(pyBlockAccountDTO);
                        logger.info("blockAccount ret:{} pyBlockAccountDTO:{} blockCtime:{}", ret, pyBlockAccountDTO, blockCtime);
                    }
//                    creditRiskHandle(context, false);

//                    if (StringUtils.hasLength(context.getShuMeiMsg())) {
//                        ShuMeiDeviceDTO dto = new ShuMeiDeviceDTO();
//                        dto.setShuMeiMsg(context.getShuMeiMsg());
//                        dto.setUid(context.getUid());
//                        dto.setTnId(context.getTnId());
//                        dto.setTnRisk(context.getTnRisk());
//                        dto.setReqTn(!context.isUseCacheTn());
//
//                        dto.setOs(context.getOs());
//                        dto.setApp_package_name(context.getApp_package_name());
//                        dto.setVname(context.getVname());
//                        dto.setVersioncode(context.getVersioncode());
//                        dto.setFromInterface("registerOrLogin");
//
//                        dto.setSmDeviceRespondsData(context.getSmDeviceRespondsData());
//                        accountService.shuMeiDeviceData(dto);
//                    }
                    logger.info("track record success DistinctId:{} uid:{} rid:{}",
                            context.getDistinct_id(), context.getUid(), context.getRid());
                } catch (Exception e) {
                    logger.error("recordLoginStatusToTga error, context={} error msg={}", context, e.getMessage(), e);
                }

            }
        });
    }

    private void creditRiskHandle(RegisterOrLoginContext context, boolean isRecord) {
        String userId = !StringUtils.isEmpty(context.getUid()) ? context.getUid() : "";
        if (context.isLimitUserByTn() && !isRecord) {
            // recordLoginStatusToTga任务记录,可能没有登入成功
            RiskUserData riskUserData;
            if (!StringUtils.isEmpty(userId)) {
                riskUserData = riskUserDao.selectOne(userId);
            } else {
                riskUserData = riskUserDao.selectOneByThirdUid(context.getThirdUid());
            }
            int now = DateHelper.getNowSeconds();
            if (riskUserData != null) {
                riskUserData.setMtime(now);
                riskUserData.setStatus(CreditRiskService.UNAVAILABLE_STATUS);
                riskUserData.setScore(0);
                riskUserDao.update(riskUserData);
            } else {
                riskUserDao.insert(new RiskUserData(userId, context.getThirdUid(), CreditRiskService.UNAVAILABLE_STATUS, context.getType(), 0, now, now));
                if (!StringUtils.isEmpty(userId)) {
                }
            }
            logger.info("信誉体系,设备标签命中黑名单账号类型 uid={} t_uid={} login_type={}", context.getUid(), context.getThirdUid(), context.getType());
        } else if (!context.isLimitUserByTn() && isRecord) {
            // asynMainTask任务记录，都是登入成功的
            RiskUserData riskUserData;
            if (!StringUtils.isEmpty(userId)) {
                riskUserData = riskUserDao.selectOne(userId);
            } else {
                riskUserData = riskUserDao.selectOneByThirdUid(context.getThirdUid());
            }
            int now = DateHelper.getNowSeconds();
            int deductScore = 0;
            List<Integer> tnRisk = context.getTnRisk();
            if (!CollectionUtils.isEmpty(tnRisk)) {
                if (tnRisk.contains(LoginConstant.TN_ROOT_TYPE)) {
                    deductScore += 900;
                }
                if (tnRisk.contains(LoginConstant.TN_DUAL_APP_TYPE)) {
                    deductScore += 100;
                }
                if (tnRisk.contains(LoginConstant.TN_FAKE_GEO_TYPE)) {
                    deductScore += 100;

                }
                if (tnRisk.contains(LoginConstant.TN_DUAL_SYSTEM_TYPE)) {
                    deductScore += 100;
                }
            }
            int uidNum = slaveRegisterOrLoginLogDao.deviceCountByThirdUid(context.getThirdUid(), now - 30 * 86400, now);
            int ipNum = slaveRegisterOrLoginLogDao.deviceCountByIp(context.getIp(), now - 7 * 86400, now);

            if (uidNum >= 5 && uidNum < 10) {
                deductScore += 100;
            } else if (uidNum >= 10) {
                deductScore += 300;
            }

            if (ipNum >= 10 && ipNum < 100) {
                deductScore += 100;

            } else if (ipNum >= 100) {
                deductScore += 1000;
            }
            logger.info("信誉体系处理减分 uid={} t_uid={} login_type={} tnRisk={} uidNum={} ipNum={} deductScore={} oldRiskUserData={}",
                    context.getUid(), context.getThirdUid(), context.getType(), tnRisk, uidNum, ipNum, deductScore, riskUserData);
            if (riskUserData != null) {
                if (deductScore > 0) {
                    String desc = " 账号关联设备数:" + uidNum + " ip关联设备数:" + ipNum + " 设备风险标签值:" + tnRisk;
                    int score = deductScore >= riskUserData.getScore() ? 0 : riskUserData.getScore() - deductScore;
                    riskUserData.setScore(score);
                    riskUserData.setUid(context.getUid());
                    riskUserData.setMtime(now);
                    riskUserDao.update(riskUserData);
                } else {
                    if (StringUtils.isEmpty(riskUserData.getUid())) {
                        riskUserData.setUid(context.getUid());
                        riskUserData.setMtime(now);
                        riskUserDao.update(riskUserData);
                        // 补记录，因为之前注册时还没有uid生成
                        logger.info("补记录,注册时设备标签命中黑名单账号类型 uid={} t_uid={} login_type={}", context.getUid(), context.getThirdUid(), context.getType());
                    }
                }
            } else {
                if (deductScore > 0) {
                    // 只有减分才创建记录
                    int score = deductScore >= 1000 ? 0 : 1000 - deductScore;
                    riskUserDao.insert(new RiskUserData(context.getUid(), context.getThirdUid(), CreditRiskService.AVAILABLE_STATUS, context.getType(), score, now, now));
                }
            }
        }
    }


    // 业务相关
    public void asynMainTask(RegisterOrLoginContext context) {
        BaseTaskFactory.getFactory().add(new Task() {
            @Override
            protected void execute() {
                try {
                    int now = DateHelper.getNowSeconds();
                    updateTgaData(context);
                    if (context.isRegister()) {
                        loginActorDao.updateRegisterData(context.getUid(), context.getRid());
                        UserRegisterInfoData userRegisterInfoData = new UserRegisterInfoData();
                        if (null != context.getTnRespondsData()) {
                            fillTnData(userRegisterInfoData, context);
                            if (tnCheckRedis.isMailTicker(context.getTnId())) {
                                JSONObject data = new JSONObject();
                                data.put("uid", context.getUid());
                                data.put("tn_id", context.getTnId());
                                data.put("tn_risk", context.getTnRisk());
//                                redisTaskService.pushQueue(PY_MAIL_QUEUE, data);
                            }
                        }
                        userRegisterInfoData.setVersionCode(context.getVersioncode());
                        userRegisterInfoData.setVersionName(context.getVname());
                        userRegisterInfoData.setIp(context.getIp());
                        userRegisterInfoData.setAndroidId(context.getAndroidid());
                        userRegisterInfoData.setPackage_name(context.getToClientPkgName());
                        userRegisterDao.updateRegisterData(userRegisterInfoData);
                        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(context.getUid(), USER_REGISTER, context.getUid()));
                    } else {
                        MongoLoginActorData mongoLoginActorData = context.getMongoLoginActorData();
                        if (null != context.getTnRespondsData()) {
                            UserRegisterInfoData userRegisterInfoData = new UserRegisterInfoData();
                            fillTnData(userRegisterInfoData, context);
                            userRegisterDao.updateLoginData(userRegisterInfoData);
                        }

                        accountStatusService.deleteAccountStatus(context.getUid());

                        MySQLActorData mySQLActorData = new MySQLActorData();
                        fillMySQLActorData(mySQLActorData, context, mongoLoginActorData);
                        mySQLActorService.updateLoginData(mySQLActorData);
                        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(context.getUid(), UserLevelConstant.LOGIN, context.getUid()));
                    }

                    if (!StringUtils.isEmpty(context.getTnId())) {
                        LoginLogData loginLogData = new LoginLogData();
                        loginLogData.setUid(context.getUid());
                        loginLogData.setDeviceId(context.getTnId());
                        loginLogData.setCtime(now);
                        loginLogService.insertOne(loginLogData);
                    }

                    if (context.isCheckDeviceAccountNum() && null != context.getNowDeviceAccountNum()) {
                        int nowTime = DateHelper.getNowSeconds();
                        String tnId = context.getTnId();
                        int limitNum = LoginConstant.DEVICE_LIMIT_ACCOUNT_NUM.get(0);
                        int redisNum = 0;
                        if (tnCheckRedis.isWhite(tnId)) {
                            limitNum = ServerConfig.isNotProduct() ? LoginConstant.DEVICE_LIMIT_ACCOUNT_TEST_MAX_NUM :
                                    LoginConstant.DEVICE_LIMIT_ACCOUNT_NUM.get(3);
                        } else if ((redisNum = tnCheckRedis.getDeviceLimitNum(tnId)) > 0) {
                            limitNum = redisNum;
                        }
//                        if (context.getNowDeviceAccountNum() == 1 && context.isRegister()) {
//                            if (null == tnDeviceAccountDao.findRookieSignOne(context.getTnId(), false)) {
//                                loginActorDao.updateFirstTnId(context.getUid(), context.getTnId());
//                                logger.info("new device:{} new uid:{} add success", context.getTnId(), context.getUid());
//                            }
//                        }
                        TnDeviceAccountData deviceData = new TnDeviceAccountData();
                        deviceData.setTnId(context.getTnId());
                        deviceData.setNowNum(context.getNowDeviceAccountNum());
                        deviceData.setLimitNum(limitNum);
                        deviceData.setLevelUid(context.getDeviceLevelUid());
                        deviceData.setHonorUid(context.getDeviceHonorUid());
                        deviceData.setCtime(nowTime);
                        deviceData.setMtime(nowTime);
                        tnDeviceAccountDao.addOrUpdate(deviceData);
                    }

                    String authorizationCode = context.getAuthorization_code().trim();
                    if (!StringUtils.isEmpty(authorizationCode)) {
                        AppleLoginData data = new AppleLoginData();
                        data.setCtime(now);
                        data.setUid(context.getUid());
                        data.setAuthorizationCode(authorizationCode);
                        appleLoginService.updateAuthCode(data);
                    }
                    if (context.isRegister()) {
                        robotLoginService.addHumNumByRegister();
                        fillWhiteTestData(context);
                        // 打招呼推荐
                        handleGreetUserRecommend(context);
                    } else {
                        if (ServerConfig.isNotProduct() && context.getMongoLoginActorData().getRobot() == RobotLoginService.HUMAN_ROBOT) {
                            fillWhiteTestData(context);
                            // 打招呼推荐
                            handleGreetUserRecommend(context);
                        }
                    }

                    boolean isWarn = false;
                    List<Integer> tnRisk = context.getTnRisk();
                    if (!CollectionUtils.isEmpty(tnRisk)) {
                        for (Integer item : tnRisk) {
                            if (DEVICE_WARN_TYPE.contains(item)) {
                                isWarn = true;
                                break;
                            }
                        }
                        CommonMqTopicData topicData = new CommonMqTopicData(context.getUid(), "", "", "",
                                CommonMqTaskConstant.DEVICE_RISK_LOGIN, 1);
                        List<String> strRiskList = tnRisk.stream().map(String::valueOf).collect(Collectors.toList());
                        topicData.setDataList(strRiskList);
                        commonTaskService.sendCommonTaskMq(topicData);
                    }
                    if (isWarn && !context.isWhite()) {
                        String opt = context.isRegister() ? "注册" : "登入";
                        String content = "告警描述:非法" + opt + "youstar，疑似骚扰，请市场运营关注此用户\n"
                                + ">用户id: " + context.getRid() + "\n"
                                + ">IP地址: " + context.getIp() + "\n"
                                + ">IP所属国家区域: " + context.getIpCountryName() + "\n"
                                + ">图灵顿设备id: " + (null == context.getTnId() ? "" : context.getTnId()) + "\n"
                                + ">图灵顿风险标签: " + robotLoginService.getTnTagMsg(context.getTnRisk());
                        noticeWarn(content);
                    }
//                    creditRiskHandle(context, true);

                    FBInstallReferrerData fbInstallReferrerData = context.getFbInstallReferrerData();
                    if (fbInstallReferrerData != null) {
                        logger.info("fbInstallReferrerData:{}", JSON.toJSONString(fbInstallReferrerData));
                        appsFlyerService.saveFaceBookCampaign(fbDataToActorCampaign(context, fbInstallReferrerData));
                    }
                    logger.info("asynMainTask success uid:{}", context.getUid());
                } catch (Exception e) {
                    logger.error("asynMainTask error, context={} error msg={}", context, e.getMessage(), e);
                }
            }
        });

    }

    private ActorCampaignLogData fbDataToActorCampaign(RegisterOrLoginContext context, FBInstallReferrerData data) {
        ActorCampaignLogData logData = new ActorCampaignLogData();
        logData.setUid(context.getUid());
        logData.setGpAdId(data.getId() != null ? data.getId() : "");
        logData.setIdfa("facebook");
        logData.setCountryCode(ActorUtils.getUpperCaseCountryCode(context.getToClientCountry()));
        logData.setLanguage(context.getLang() != null ? context.getLang() : "");
        logData.setAppId(data.getAccountId() != null ? data.getAccountId() : "");
        logData.setAppVer(context.getVname() != null ? context.getVname() : "");
        logData.setAttributionTime(data.getInstallTime() != null ? String.valueOf((int) (data.getInstallTime() / 1000)) : "");
        logData.setCampaign(data.getCampaignGroupName() != null ? data.getCampaignGroupName() : "");
        logData.setTouchTime("");
        logData.setMedium(data.getPublisher_platform() != null ? data.getPublisher_platform() : "");
        logData.setSource("facebook");
        logData.setCtime(DateHelper.getNowSeconds());
        logData.setAdSet(data.getGroupName() != null ? data.getGroupName() : "");
        logData.setAdGroupName(data.getCampaignName() != null ? data.getCampaignName() : "");
        return logData;
    }

    private void fillWhiteTestData(RegisterOrLoginContext context) {
        if (!StringUtils.isEmpty(context.getTnId()) && !StringUtils.isEmpty(context.getUid())) {
            WhiteTestData whiteTestData = whiteTestDao.selectByTnId(context.getTnId());
            if (whiteTestData != null && whiteTestDao.selectByWhiteIdType(context.getUid(), WhiteTestDao.WHITE_TYPE_TN_ID) == null) {
                WhiteTestData newWhiteTestData = new WhiteTestData();
                newWhiteTestData.setWhiteId(context.getUid());
                newWhiteTestData.setTnId(whiteTestData.getTnId());
                newWhiteTestData.setBelong(whiteTestData.getBelong());
                newWhiteTestData.setType(WhiteTestDao.WHITE_TYPE_TN_ID);
                newWhiteTestData.setCtime(DateHelper.getNowSeconds());
                whiteTestDao.insertOne(newWhiteTestData);
                logger.info("insert WhiteTestData success tnId={} whiteId={} belong={}"
                        , context.getTnId(), context.getUid(), whiteTestData.getBelong());
            }
        }
    }

    public void updateTgaData(RegisterOrLoginContext context) {
        String uid = context.getUid();
        int now = DateHelper.getNowSeconds();
        ActorData actorData = new ActorData();
        actorData.setUid(uid);
        actorData.setFb_gender(context.getFbGender());
        actorData.setChannel(context.getChannel());
        actorData.setAppPackageName(context.getToClientPkgName());
        actorData.setOs(context.getOs() + "");
        actorData.setVersion_code(context.getVersioncode());

        UserSetTable userSetTable = new UserSetTable();
        userSetTable.setFb_gender(context.getFbGender());
        userSetTable.setLogin_type(context.getType());
        userSetTable.setUid(uid);
        userSetTable.setAccept_talk(context.getAcceptTalk());
        userSetTable.setIp(context.getIp());
        userSetTable.setTn_id(context.getTnId());
        userSetTable.setChannel(getChannelByPkg(context.getToClientPkgName(), context.getChannel()));
        userSetTable.setApp_package_name(context.getToClientPkgName());
        userSetTable.setVersion_code(context.getVersioncode());
        userSetTable.setIdfa(context.getIdfa());
        userSetTable.setOs(actorData.getIntOs() + "");
        userSetTable.setRid(context.getRid());
        userSetTable.setGeneration_time(new ObjectId(uid).getTimestamp());
        userSetTable.setTa_device_id(context.getTa_device_id());
        userSetTable.setIp_country(context.getIpCountry());
        userSetTable.setBan_state(1);
        userSetTable.setBan_over_time(0);
        userSetTable.setNickname(context.getToClientName());
        userSetTable.setAccount_lang(context.getSlang());
        userSetTable.setDevice_lang(context.getLang());
        userSetTable.setLast_online_time(now);
        userSetTable.setDynamic_channel(context.getPromotion_id());
        userSetTable.setShu_mei_id(context.getShuMeiId());
        userSetTable.setVersion_name(context.getVname());

        String email = StringUtils.isEmpty(context.getRealEmail()) ? StringUtils.isEmpty(context.getEmail()) ?
                "" : context.getEmail() : context.getRealEmail();
        userSetTable.setEmail("");

        if (context.isRegister()) {
            userSetTable.setBeans(0);
            userSetTable.setAge(18);
            userSetTable.setFriends_number(0);
            userSetTable.setFollowing_number(0);
            userSetTable.setFollowers_number(0);
            userSetTable.setRecharge(0);
            userSetTable.setVip_level(0);
            userSetTable.setAccount_country(context.getIpCountry());
            userSetTable.setRobot(context.getRegisterData().getRobot());
        } else {
            MongoLoginActorData loginData = context.getMongoLoginActorData();
            userSetTable.setBeans(loginData.getBeans());
            userSetTable.setAge(loginData.getAge());
            userSetTable.setFriends_number(friendsListRedis.getFriendCount(uid));
            userSetTable.setFollowing_number(followDao.getFollowingCount(uid));
            userSetTable.setFollowers_number(followDao.getFollowsCount(uid));
            userSetTable.setRecharge(rechargeRedis.isRechargeUser(uid) ? 1 : 0);
            userSetTable.setAccount_country(getCountryCode(loginData.getCountry()));
            userSetTable.setVip_level(context.getVipLevel());
            userSetTable.setAccount_level(context.getuLvl());
            userSetTable.setRobot(0);
        }
        EventDTO eventDto = new EventDTO(context.getUid(), "", userSetTable);
        eventDto.setDistinctId(context.getDistinct_id());
        eventReport.userSet(eventDto);

        dAUDao.updateDAU(actorData, context.isRegister() ? DAUDao.DAU_SOURCE_REGISTER : DAUDao.DAU_SOURCE_LOGIN);
        dAUDao.updateNewDAUEvent(actorData, context.isRegister() ? DAUDao.DAU_SOURCE_REGISTER : DAUDao.DAU_SOURCE_LOGIN);
        if (context.isRegister()) {
            EventDTO eventDTO = new EventDTO(uid, context.getDistinct_id(), EventConstant.NEW_USER_ADD);
            eventDTO.addProperties("form_type", "register");
            eventReport.track(eventDTO);
        }

    }

    private String getPkgName(RegisterOrLoginContext context) {
        String appPkgName = context.getApp_package_name() == null ? "" : context.getApp_package_name().trim();
        if (context.getOs() == ClientOS.IOS && StringUtils.isEmpty(appPkgName)) {
            appPkgName = PKGConstant.IOS_MAIN;
        }
        return appPkgName;
    }


    public String getChannelByPkg(String pkg, String channel) {
        if (PKGConstant.ANDROID_YOUSTAR.equals(pkg) && HUA_WEI_CHANNEL.equals(channel)) {
            return "HUAWEI";
        } else {
            return PKG_MAP_CHANNEL.getOrDefault(pkg, "");
        }
    }

    private void fillTnData(UserRegisterInfoData userRegisterInfoData, RegisterOrLoginContext context) {
        TnRespondsData tnData = context.getTnRespondsData();
        userRegisterInfoData.setUid(context.getUid());
        userRegisterInfoData.setTn_id(tnData.getOpenId());
        userRegisterInfoData.setTn_risk(null == tnData.getRiskObj() ? new ArrayList<>() : tnData.getRiskObj());
        Map<String, Object> extraMap = null == tnData.getExtraInfo() ? new HashMap<>() : tnData.getExtraInfo();
        userRegisterInfoData.setTn_extra(extraMap);
        userRegisterInfoData.setModel((String) extraMap.getOrDefault("model", ""));
    }

    private void fillMySQLActorData(MySQLActorData actorData, RegisterOrLoginContext context, MongoLoginActorData mongoLoginActorData) {
        actorData.setActorUid(context.getUid());
        actorData.setRid(mongoLoginActorData.getRid());
        actorData.setAppPackageName(context.getToClientPkgName());
        actorData.setVersionCode(context.getVersioncode());
        actorData.setCountry(mongoLoginActorData.getCountry());
        actorData.setLoginType(context.getType());
        actorData.setUid(mongoLoginActorData.getUid());
        actorData.setLang(context.getLang());
        actorData.setIsDelete(0);
        actorData.setAccountStatus(0);
        actorData.setChannel(mongoLoginActorData.getChannel());
        actorData.setIp(context.getIp());
        actorData.setTnId(!StringUtils.isEmpty(context.getTnId()) ? context.getTnId() : mongoLoginActorData.getTn_id());
        actorData.setName(context.getToClientName());
        actorData.setHead(context.getToClientHead());
        actorData.setFbGender(context.getFbGender());

    }

    private void fillLogData(RegisterOrLoginLogData logData, RegisterOrLoginContext context, int registerType, String pkgName
            , int deviceRid, String deviceUid) {


    }


    public void tnCheckTask(TnRespondsData tnData) {
        try {
            // EventDTO eventDTO = new EventDTO(context.getUid(), context.getDistinct_id(), EventConstant.SERV_TN_CHECK);
            // eventDTO.addProperties("status_code", StringUtils.isEmpty(context.getTnId()) ? 201 : 200);
            // eventReport.track(eventDTO);
            int curValue = (int) tnCheckRedis.incrementData(TnCheckRedis.DAY_MODE);
            tnCheckRedis.incrementData(TnCheckRedis.MONTH_MODE);
            thirdApiCallRedis.incApiCallNum(ThirdApiCallRedis.TN_LOGIN);
            if (TN_LIMIT_REQUEST_COUNT.contains(curValue)) {
                logger.info("达到当日图灵顿请求告警限制 curValue={} ", curValue);
                if (curValue == TN_LIMIT_REQUEST_COUNT.get(TN_LIMIT_REQUEST_COUNT.size() - 1)) {
                    monitorSender.infoWithPhone(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "达到当日图灵顿请求告警限制",
                            "curValue=" + curValue);
                } else {
                    monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "达到当日图灵顿请求告警限制",
                            "curValue=" + curValue);
                }

            }
            logger.info("tnCheckTask success  tn_id:{}", tnData.getOpenId());
        } catch (Exception e) {
            logger.error("tnCheckTask error, tnData={} error msg={}", JSONObject.toJSONString(tnData), e.getMessage(), e);
        }
    }

    public void noticeWarn(String content) {
        String warnName = ServerConfig.isProduct() ? LoginConstant.USTAR_RISK_LOGIN_WARN_NAME : ThirdApiLoginService.LOGIN_WARN_NAME;
//        monitorSender.info(warnName, desc, detail);
//        monitorSender.customMsg(warnName,content,null,null);
        monitorSender.customMarkdown(warnName, content);
    }

    public String getCountryCode(String country) {
        if (StringUtils.isEmpty(country) || country.trim().length() < 2) {
            return "";
        }
        try {
            return country.split("_")[0].toUpperCase();
        } catch (Exception e) {
            logger.error("country :{} split error", country, e);
            return "";
        }
    }

    /**
     * 处理打招呼推荐用户
     *
     * @param context 注册上下文
     */
    private void handleGreetUserRecommend(RegisterOrLoginContext context) {
        try {
            MongoLoginActorData registerData;
            if (context.isRegister()) {
                registerData = context.getRegisterData();
                if (ServerConfig.isProduct() && registerData.getRobot() > 0) {
                    return;
                }
            } else {
                if (actorCommonService.getRejectGreetStatus(context.getUid()) > 0) {
                    return;
                }
                registerData = context.getMongoLoginActorData();
            }

            String uid = context.getUid();
            int registerTime = new ObjectId(uid).getTimestamp();
            String countryCode = ActorUtils.getUpperCaseCountryCode(registerData.getCountry());
            int regionCode = ActorUtils.getRegionByCountryCode(countryCode);
            int currentTime = DateHelper.getNowSeconds();
            List<Integer> labelList = registerData.getLabel_list().stream().map(Integer::parseInt).collect(Collectors.toList());
            byte[] labelBit = BitUtils.tagsToBytes(labelList);
            double profileProcess = greetUserService.calculateProgress(registerData.getBanner(), labelList, registerData.getHead(), registerData.getName(), registerData.getDesc(), registerData.getBirthday(), registerData.getCountry());
            GreetUserData data = new GreetUserData(uid, registerData.getFb_gender(), registerTime, regionCode, countryCode, currentTime,
                    JSONObject.toJSONString(labelList), labelBit, 0.0, "", "",
                    profileProcess, 0, 0, currentTime, currentTime);
            greetUserService.insertGreetUser(data);
        } catch (Exception e) {
            logger.error("handleGreetUserRecommend: error processing campaign reward for uid={}, error={}", context.getUid(), e.getMessage(), e);
        }
    }

}
