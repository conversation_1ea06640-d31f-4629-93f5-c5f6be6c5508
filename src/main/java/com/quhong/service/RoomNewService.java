package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.quhong.analysis.*;
import com.quhong.config.AsyncConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.config.RoomConfig;
import com.quhong.config.UserRecommendConfig;
import com.quhong.constant.*;
import com.quhong.controllers.RoomMsgController;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.*;
import com.quhong.enums.RoomConstant;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IGameService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mq.CommonData;
import com.quhong.mq.MqItemConstant;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.*;
import com.quhong.msg.room.*;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.*;
import com.quhong.room.*;
import com.quhong.room.api.ThirdPartApiAdapter;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.EnterCartonData;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.mic.RoomMicService;
import com.quhong.room.redis.*;
import com.quhong.sdk.agora.AgoraService;
import com.quhong.sdk.zego.ZegoService;
import com.quhong.task.TaskFactory;
import com.quhong.utils.*;
import com.quhong.video.VideoCoreService;
import com.quhong.vo.NewDeviceReward;
import com.quhong.vo.RoomMicListVo;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Service
public class RoomNewService {
    private static final Logger logger = LoggerFactory.getLogger(RoomNewService.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);

    // 用户列表大小
    public static final int ACTOR_PAGE_SIZE = 30;
    private static final int MAX_UPLOAD_NUM = 10;
    public static final Set<String> STAFF_ROOM_SET = new HashSet<>(Arrays.asList("5b184dc691f8d0001856a48c",
            "5bbe240a66dc63002e3f1fe0", "5c2f0690644f8e004a8e9823", "5adeef348284771cbbb28b2c", "5c8b63f5644f8e002855cbab",
            "5c2c2dec644f8e002a6aaac4", "5be3f50002bd89001a2b9abc", "5c8c6b23644f8e00277c15f2", "594a3efb1bad4838a47d9a7c",
            "5aa2467e1bad48c4ead77821"));
    private static final Set<String> HIDE_KEY_SET = new HashSet<>(Arrays.asList("sex", "porn", "dick", "boob"));
    private static final Set<String> HIDE_COUNTRY_SET = new HashSet<>(Arrays.asList("af", "al", "by", "ba", "bi", "cf",
            "cd", "cu", "er", "gw", "ir", "iq", "kp", "xk", "lb", "ly", "ml", "ru", "rs", "so", "ss", "sd", "sy", "ua",
            "ve", "ye", "zw"));

    private static final List<String> CONQUER_ROOMID = new ArrayList<>();

    public static final String ACTIVITY_ROOM_POLICY_URL = "https://youstar.live/";

    // 幸运数字V2配置
    private static final List<Integer> LUCKY_NUM_COST_LIST = Arrays.asList(0, 2, 7);
    private static final List<Integer> LUCKY_NUM_RANGE_LIST = Arrays.asList(9, 99, 999);
    private static final List<Integer> LUCKY_NUM_SWITCH = Arrays.asList(0, 1);

    private static final List<String> BC_GAME_LIST = Arrays.asList("slots", "angel_or_devil", "horse_racing", "crash", "fishing", "fast3");

    // 关注房间消息文案
    private static final String FOLLOW_ROOM_MSG = "%s has followed this room.";
    private static final String FOLLOW_ROOM_MSG_AR = "قام %s بمتابعة هذه الغرفة";
    // 关注房间消息文案
    private static final String ROOM_NAME = "%s's room";
    private static final String ROOM_NAME_AR = "\u202b%s غرفة";
    private static final String LUCKY_NUM_ROOM = "r:6195cd1c8b57bfa01f82f939";
    // https://cdn3.qmovies.tv/youstar/op_1699260050_dian_zhan_1.png ------ https://cdn3.qmovies.tv/youstar/op_1699260050_dian_zhan_5.png
    private static final String LIKE_ICON_URL = "https://cdn3.qmovies.tv/youstar/op_1699260050_dian_zhan_2.png";

    private static final String BC_MARKET_URL = ServerConfig.isProduct() ? "https://static.youstar.live/bc_game/" : "https://test2.qmovies.tv/bc_game/";

    private static final String NEW_DEVICE_ACCOUNT_OTHER = "taskRookieStayInRoom1MinuteOther";
    private static final List<String> NEW_DEVICE_ACCOUNT_RES_KEY = Arrays.asList("Otherstay", "DZstay", "AEstay", "OMstay", "EGstay", "PLstay", "BHstay", "DEstay", "FRstay", "CAstay", "QAstay", "LBstay", "LYstay", "USstay", "MAstay", "SAstay", "SDstay", "TNstay", "TRstay", "ESstay", "SYstay", "YEstay", "IQstay", "UKstay", "JOstay");

    // 白名单用户ID列表 - 这些用户在任何房间都可以看到新用户的进房标签
    private static final List<String> WHITELIST_USERS = ServerConfig.isProduct() ? Arrays.asList("6840121c3f3eef79afff397a", "68401216dfd1490db1927a28", "6840120f3f3eef79afff3979") : Arrays.asList("643530db4fc4f9265e368cdd", "627fa3070a2a77444f0e4957");

    // 表情不可发送提示文案
    private static final String EMOJI_CAN_NOT_SEND_TIPS_EN = "You can use this sticker when you become %s";
    private static final String EMOJI_CAN_NOT_SEND_TIPS_AR = "يمكنك استخدام هذا الملصق عندما تصبح %s";

    // 每日上麦任务key和资源key   user_task_config_v2.yml配置文件里面的，如果配置文件更改，这里也需要修改
    private static final String DAILY_MIC_TASK_KEY = "web_daily_on_mic_time_10_minute";
    private static final String DAILY_MIC_TASK_RESOURCE_KEY = "taskDailyOnMicContinue10Minute";


    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private VideoCoreService videoService;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private LowModelsRedis lowModelsRedis;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private RecentlyRoomRedis recentlyRoomRedis;
    @Resource
    private RoomVisitorsRedis roomVisitorsRedis;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private FollowDao followDao;
    @Resource
    private CommonDao commonDao;
    @Resource
    private ZegoService zegoService;
    @Resource
    private AgoraService agoraService;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private RoomConfig roomConfig;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private GiftBagDao giftBagDao;
    @Resource
    private RoomMicService roomMicService;
    @Resource
    private RoomActorListService roomActorListService;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private CheatGiftRedis cheatGiftRedis;
    @Resource
    private RoomRedis roomRedis;
    @Resource
    private PartyGirlRedis partyGirlRedis;
    @Resource
    private EnterRoomContent enterRoomContent;
    @Resource
    private RoomThemeService roomThemeService;
    @Resource
    private BackstageConfigDao backstageConfigDao;
    @Resource
    private RoomRecommendRedis roomRecommendRedis;
    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private RoomTags roomTags;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private ThirdPartApiAdapter thirdPartApi;
    @Resource
    private RoomBannerDao roomBannerDao;
    @Resource
    private ConquerActivityDao conquerActivityDao;
    @Resource
    private RoomAdminRedis roomAdminRedis;
    @Resource
    private PkGameDao pkGameDao;
    @Resource
    private RoomPkGameDao roomPkGameDao;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private TruthOrDareRedis truthOrDareRedis;
    @Resource
    private FingerGuessRedis fingerGuessRedis;
    @Resource
    private EmojiResourceDao emojiResourceDao;
    @Resource
    private IDetectService detectService;
    @Resource
    private MqSenderService mqService;
    @Resource
    private ConquerRedis conquerRedis;
    @Resource
    private UploadBackgroundDao uploadBackgroundDao;
    @Resource
    private MongoThemeDao themeDao;
    @Resource
    private MineBackgroundDao mineBackgroundDao;
    @Resource
    private FollowRoomDao followRoomDao;
    @Resource
    private ActivityRoomRedis activityRoomRedis;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private LuckyNumDao luckyNumDao;
    @Resource
    private LuckyNumRedis luckyNumRedis;
    @Resource
    private DataCenterService dataCenterService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;
    @Resource
    private RoomRocketService roomRocketService;
    @Resource
    private RoomRocketV2Service roomRocketV2Service;
    @Resource
    private RoomEventService roomEventService;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private MicApplyRedis micApplyRedis;
    @Resource
    private UserRechargeRecordDao userRechargeRecordDao;
    @Resource
    private RoomHotDevoteRedis roomHotDevoteRedis;
    @Resource
    private SlaveUidAidDevoteLogDao slaveUidAidDevoteLogDao;
    @Resource
    private RoomSettingsService roomSettingsService;
    @Resource
    private RoomNewService roomNewService;
    @Resource
    private CreditRiskService creditRiskService;
    @Resource
    private RoomLikeRedis roomLikeRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private NewcomerTaskDao newcomerTaskDao;
    @Resource
    private UserTaskRedis userTaskRedis;
    @Resource
    private RoomLevelService roomLevelService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private VoiceMicThemeRedis voiceMicThemeRedis;
    @Resource
    private RecreationTagService recreationTagService;
    @Resource
    private RoomActionLogDao roomActionLogDao;
    @Resource
    private HotSearchHistoryDao hotSearchHistoryDao;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private ShuShuAnalyseService shuShuAnalyseService;
    @Resource
    private EmojiConfigDao emojiConfigDao;
    @Resource
    private SendGiftMonitorRedis sendGiftMonitorRedis;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private UserRegisterDao userRegisterDao;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private RoomMicThemeDao roomMicThemeDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource
    private DAUDao dAUDao;
    @Resource
    private TruthOrDareV2Redis truthOrDareV2Redis;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private IGameService iGameService;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private UserTaskDao userTaskDao;
    @Resource
    private RoomWelcomeMsgService roomWelcomeMsgService;


    @PostConstruct
    public void postInit() {
        if (ServerConfig.isProduct()) {
            CONQUER_ROOMID.addAll(Arrays.asList("r:5cc2797c66dc630025bf17c2", "r:5cb7df8366dc63002628e272", "r:5cb7f37366dc6300257b9511", "r:5cc81e4c66dc630025bf6520"));
        } else {
            CONQUER_ROOMID.add("r:6267eb2b3162c17359243a42");
        }
    }

    @Deprecated
    public PreCreateRoomVO preCreateOld(HttpEnvData req) {
        PreCreateRoomVO vo = new PreCreateRoomVO();
        MongoRoomData mongoRoomData = roomDao.findData(RoomUtils.formatRoomId(req.getUid()));
        if (null != mongoRoomData) {
            vo.setRoomType(RoomType.COMP == mongoRoomData.getComp() ? RoomType.COMP : RoomType.NORMAL);
            vo.setRoomName(mongoRoomData.getName());
            vo.setTag(mongoRoomData.getTag() == 0 ? -1 : mongoRoomData.getTag());
        }
        vo.setVoiceOnline(roomRedis.getRoomStatValue("voiceOnlineActor") * 2 + ThreadLocalRandom.current().nextInt(5));
        vo.setLiveOnline(roomRedis.getRoomStatValue("liveOnlineActor") * 2 + ThreadLocalRandom.current().nextInt(5));
        vo.setTagList(SLangType.ENGLISH == req.getSlang() ? roomTags.getEnTags() : roomTags.getArTags());
        return vo;
    }

    public boolean isASCII(String str) {
        return str.matches("\\A\\p{ASCII}*\\z");
    }

    private String formatRoomName(HttpEnvData req) {
        String name = actorDao.getActorDataFromCache(req.getUid()).getName();
        if (SLangType.ENGLISH == req.getSlang() || isASCII(name)) {
            return String.format(ROOM_NAME, name);
        } else {
            return String.format(ROOM_NAME_AR, name);
        }
    }

    public Object preCreate(HttpEnvData req) {
        if (!AppVersionUtils.versionCheck(844, req)) {
            return preCreateOld(req);
        }
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (null == actorData) {
            logger.error("cannot find actor, uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(RoomUtils.formatRoomId(req.getUid()));
        // if (AppVersionUtils.versionCheck(8592, req) && roomData != null) {
        //     // 已创建过房间的用户直接进入语音房。进直播房需在房间内切换
        //     RoomDTO dto = new RoomDTO();
        //     BeanUtils.copyProperties(req, dto);
        //     dto.setRoomId(roomData.getRid());
        //     dto.setRoomType(2);
        //     dto.setRequestTime(System.currentTimeMillis());
        //     // 调用加入房间接口
        //     return joinRoom(dto);
        // }
        PreCreateRoomVO vo = new PreCreateRoomVO();
        vo.setRoomMode(null == roomData || 0 == roomData.getRoomMode() ? RoomConstant.VOICE_ROOM_MODE : roomData.getRoomMode());
        vo.setRoomName(null == roomData ? formatRoomName(req) : roomData.getName());
        vo.setRoomHead(null == roomData ? actorData.getHead() : roomData.getHead());
        vo.setRoomAnnounce(null == roomData ? (req.getSlang() == SLangType.ENGLISH ? roomConfig.getDefaultRoomAnnounceEn() : roomConfig.getDefaultRoomAnnounceAr()) : roomData.getAnnounce());
        // 没创建过房间的用户默认使用“Random Talk”标签
        vo.setTag(null == roomData || roomData.getTag() == 0 ? 2 : roomData.getTag());
        vo.setTagName(roomTags.getTagNameById(vo.getTag(), req.getSlang()));
        vo.setLiveRoomRequirement(roomConfig.getLiveRoomRequirement());
        return vo;
    }

    public void createRoomCheck(String uid) {
        int liveRoomRequirement = roomConfig.getLiveRoomRequirement();
        if (1 == liveRoomRequirement) {
            if (vipInfoDao.getIntVipLevel(uid) < 2) {
                throw new CommonException(RoomHttpCode.LIVE_ROOM_LIMIT_NEW);
            }
        } else if (2 == liveRoomRequirement) {
            if (userLevelDao.getUserLevel(uid) < 5) {
                throw new CommonException(RoomHttpCode.LIVE_ROOM_LIMIT_LEVEL);
            }
        }
    }

    /**
     * 房主创建房间
     */
    public RoomVO create(RoomDTO req) {
        if (req.getRoomType() == RoomType.COMP) {
            // todo delete me
            if (ServerConfig.isNotProduct()) {
                throw new CommonException(HttpCode.UPDATE_APP);
            }
        }
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (null == actorData) {
            logger.error("cannot find actor, uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        // 脏词检测
        if (!ObjectUtils.isEmpty(req.getRoomName())) {
            if (checkDirtyWord(req.getRoomName().toLowerCase(), DetectOriginConstant.ROOM_NAME_RELATED, req.getUid())) {
                req.setRoomName(formatRoomName(req));
            }
        }
        if (!ObjectUtils.isEmpty(req.getRoomAnnounce())) {
            if (checkDirtyWord(req.getRoomAnnounce().toLowerCase(), DetectOriginConstant.ROOM_NAME_RELATED, req.getUid())) {
                req.setRoomAnnounce(req.getSlang() == SLangType.ENGLISH ? roomConfig.getDefaultRoomAnnounceEn() : roomConfig.getDefaultRoomAnnounceAr());
            }
        }
        if (!ObjectUtils.isEmpty(req.getRoomHead())) {
            String roomHead;
            if (req.getRoomHead().startsWith("http")) {
                roomHead = CDNUtils.getHttpCdnUrl(req.getRoomHead());
            } else {
                roomHead = CDNUtils.getCdnUrl(req.getRoomHead());
            }
            if (checkDirtyImage(roomHead, DetectOriginConstant.ROOM_HEAD, req.getUid())) {
                req.setRoomHead(actorData.getHead());
            } else {
                req.setRoomHead(roomHead);
            }
        }
        if (req.getRoomType() == RoomType.COMP || req.getRoomMode() == RoomConstant.LIVE_ROOM_MODE) {
            if (sendGiftMonitorRedis.isSendGiftFreezeUser(req.getUid())) {
                throw new CommonException(RoomHttpCode.SEND_GIFT_FREEZE);
            }
            createRoomCheck(req.getUid());
        }
        String roomId = RoomUtils.formatRoomId(req.getUid());
        req.setRoomId(roomId);
        MongoRoomData mongoRoomData = roomDao.findData(roomId);
        // 新版本live房间，强制设置为8麦，com.quhong.room.mic.RoomMicService.getRoomMicList(java.lang.String, int)
        if (RoomConstant.LIVE_ROOM_MODE == req.getRoomMode()) {
            req.setRoomType(4);
        }
        if (null == mongoRoomData) {
            createRoom(actorData, roomId, req);
        } else {
            updateRoom(mongoRoomData, req, actorData);
        }
        // 主动创建房间,关闭真心话大冒险
        createRoomTruthDareCloseGame(req, roomId);
        // 调用加入房间接口
        return joinRoom(req);
    }

    private void createRoomTruthDareCloseGame(RoomDTO req, String roomId) {
        try {
            // 真心话大冒险主题游戏 用户在麦位不能被踢出房间
            TruthOrDareV2Info truthOrDareV2Info = truthOrDareV2Redis.getGameInfoByRoomId(roomId);
            if (truthOrDareV2Info != null) {
                if (truthOrDareV2Info.getCreateUid().equals(req.getUid())) {
                    TruthDareV2DTO truthDareV2DTO = new TruthDareV2DTO();
                    BeanUtils.copyProperties(req, truthDareV2DTO);
                    truthDareV2DTO.setGameId(truthOrDareV2Info.getGameId());
                    truthDareV2DTO.setPushAll(true);
                    truthDareV2DTO.setExitCode(TruthDareV2Constant.EXIT_CODE_2);
                    truthDareV2DTO.setForceExit(true);
                    iGameService.truthDareCloseGame(truthDareV2DTO);
                }
            }
        } catch (Exception e) {
            logger.error("createRoomTruthDareCloseGame e={}", e.getMessage(), e);
        }
    }

    /**
     * 脏词检测
     *
     * @return 是否含有脏词
     */
    private boolean checkDirtyWord(String text, String origin, String uid) {
        if (detectService.detectText(new TextDTO(text, origin, uid)).getData().getIsSafe() == 0) {
            return true;
        }
        return false;
    }

    /**
     * 脏图检测
     *
     * @return 是否含有脏图
     */
    private boolean checkDirtyImage(String imgUrl, String origin, String uid) {
        if (detectService.detectImage(new ImageDTO(imgUrl, origin, uid)).getData().getIsSafe() == 0) {
            return true;
        }
        return false;
    }


    /**
     * 房主第一次创建房间
     */
    public void createRoom(ActorData actorData, String roomId, RoomDTO req) {
        if (req.getOs() != ClientOS.IOS) {
            int userLevel = userLevelDao.getUserLevel(req.getUid());
            int createRoomLimit = commonConfig.getConfigValue(CommonConfig.CREATE_ROOM_LIMIT, -1);
            if (createRoomLimit != -1 && userLevel < createRoomLimit) {
                throw new CommonException(RoomHttpCode.CREATE_ROOM_LIMIT, createRoomLimit);
            }
        }
        logger.info("first time create room uid={}", req.getUid());
        int nowSeconds = DateHelper.getNowSeconds();
        MongoRoomData mongoRoomData = new MongoRoomData();
        mongoRoomData.setName(StringUtils.isEmpty(req.getRoomName()) ? actorData.getName() : req.getRoomName());
        mongoRoomData.setHead(StringUtils.isEmpty(req.getRoomHead()) ? actorData.getHead() : req.getRoomHead());
        mongoRoomData.setAnnounce(StringUtils.isEmpty(req.getRoomAnnounce()) ? (req.getSlang() == SLangType.ENGLISH ? roomConfig.getDefaultRoomAnnounceEn() : roomConfig.getDefaultRoomAnnounceAr()) : req.getRoomAnnounce());
        mongoRoomData.setRid(roomId);
        mongoRoomData.setOwnerRid(actorData.getRid());
        mongoRoomData.setPrivi(1);
        mongoRoomData.setFee(0);
        mongoRoomData.setFeetype(2);
        mongoRoomData.setTextLimit(-1);
        mongoRoomData.setStream_id(genStreamId(roomId, null));
        mongoRoomData.setCountry(StringUtils.isEmpty(actorData.getCountry()) ? "AE_United Arab Emirates" : actorData.getCountry());
        mongoRoomData.setVideo_switch(2);
        mongoRoomData.setMtime(nowSeconds);
        mongoRoomData.setCtime(nowSeconds);
        mongoRoomData.setTag(roomTags.hasTag(req.getTagId()) ? req.getTagId() : 0);
        mongoRoomData.setRoomMode(req.getRoomMode() == 0 ? 1 : req.getRoomMode());
        mongoRoomData.setRoom_type(req.getRoomType());
        roomDao.save(mongoRoomData);
        setRoomTagEvent(roomId, mongoRoomData.getTag());
//        try {
//            // Py业务需要
//            createRoomRedis.opsForList().leftPush("new_create_room", nowSeconds + 604800 + "_" + roomId);
//        } catch (Exception e) {
//            logger.error("left push to new_create_room error roomId={}", roomId, e);
//        }
    }

    /**
     * 更新房间
     */
    public void updateRoom(MongoRoomData mongoRoomData, RoomDTO req, ActorData actorData) {
        String roomId = mongoRoomData.getRid();
        if (!AppVersionUtils.versionCheck(844, req)) {
            req.setRoomChange(mongoRoomData.getRoom_type() != req.getRoomType());
        } else {
            req.setRoomChange(0 != mongoRoomData.getRoomMode() && req.getRoomMode() != mongoRoomData.getRoomMode());
        }
        if (RoomType.COMP == req.getRoomType() || req.getRoomMode() == RoomConstant.LIVE_ROOM_MODE) {
            // delete me
            mongoRoomData.setComp(0);
            if (RoomType.COMP == req.getRoomType()) {
                mongoRoomData.setComp(1);
            }
            initLiveRoom(mongoRoomData, req.getUid(), req.getRoomMode());
        } else {
            mongoRoomData.setComp(0);
            if (req.getRoomMode() == RoomConstant.VOICE_ROOM_MODE) {
                checkUpMicPrivilege(mongoRoomData, req.getUid(), req, true);
            }
        }
        // 更新房间信息
        if (!ObjectUtils.isEmpty(req.getRoomName())) {
            if (!req.getRoomName().equals(mongoRoomData.getName())) {
                req.setChangeRoomName(true);
            }
            mongoRoomData.setName(req.getRoomName());
        }
        if (!ObjectUtils.isEmpty(req.getRoomHead())) {
            if (!req.getRoomHead().equals(mongoRoomData.getHead())) {
                req.setChangeRoomName(true);
            }
            mongoRoomData.setHead(req.getRoomHead());
        }
        if (!ObjectUtils.isEmpty(req.getRoomAnnounce())) {
            mongoRoomData.setAnnounce(req.getRoomAnnounce());
        }
        mongoRoomData.setOwnerRid(actorData.getRid());
        mongoRoomData.setRoom_type(req.getRoomType());
        mongoRoomData.setRoomMode(req.getRoomMode() == 0 ? 1 : req.getRoomMode());
        mongoRoomData.setTag(roomTags.hasTag(req.getTagId()) ? req.getTagId() : 0);
        mongoRoomData.setStream_id(genStreamId(roomId, mongoRoomData.getPwd()));
        roomDao.save(mongoRoomData);
        setRoomTagEvent(roomId, mongoRoomData.getTag());
        if (!AppVersionUtils.versionCheck(844, req)) {
            // 发送房间变更消息
            RoomChangeTypePushMsg msg = new RoomChangeTypePushMsg();
            msg.setRoomId(roomId);
            msg.setFromUid(req.getUid());
            msg.setComp(0);
            msg.setRoomType(req.getRoomType());
            roomWebSender.sendRoomWebMsg(roomId, req.getUid(), msg, true);
        }
    }

    /**
     * 直播房重置音乐房、话题房、视频房状态
     */
    public void initLiveRoom(MongoRoomData roomData, String uid, int reqMode) {
        int oldMicTheme = roomData.getMic_theme();
        if (oldMicTheme != RoomConstant.MIC_THEME_DEFAULT_ID && oldMicTheme != 0) {
            if (oldMicTheme == RoomConstant.MIC_THEME_VIDEO_ID) {
                // 视频房切换过来的
                int videoMicTheme = voiceMicThemeRedis.getRecentlyTheme(roomData.getRid(), VoiceMicThemeRedis.VIDEO_TYPE);
                if (videoMicTheme != RoomConstant.MIC_THEME_DEFAULT_ID && videoMicTheme != 0) {
                    voiceMicThemeRedis.setRecentlyTheme(roomData.getRid(), videoMicTheme, VoiceMicThemeRedis.LIVE_TYPE);
                }
//                voiceMicThemeRedis.deleteRecentlyTheme(roomData.getRid(),VoiceMicThemeRedis.VIDEO_TYPE);
            } else {
                voiceMicThemeRedis.setRecentlyTheme(roomData.getRid(), oldMicTheme, VoiceMicThemeRedis.LIVE_TYPE);
            }
        }
        int nowMicTheme = roomData.getMic_theme() == 0 ? RoomConstant.MIC_THEME_DEFAULT_ID : roomData.getMic_theme();
        if (nowMicTheme != RoomConstant.MIC_THEME_DEFAULT_ID) {
            videoService.doChangeMicThemeDefault(roomData, reqMode);
        }
        if (roomData.getVideo_switch() == VideoActionType.OPEN) {
            shuShuAnalyseService.closeYouToBeVideoEvent(roomData.getRid(), uid, 2);
        }
        // 清除视频房间列表
        videoService.closeVideo(roomData.getRid(), null);

        roomData.setMic_theme(RoomConstant.MIC_THEME_DEFAULT_ID);
        if (roomData.getOrigPrivi() != 3) {
            // live切换为申请上麦，并发送消息
            roomSettingsService.sendRoomOptMsg(roomData.getRid(), uid, 4);
        }

    }

    public int checkUpMicPrivilege(MongoRoomData roomData, String uid, HttpEnvData req, boolean isCreate) {
        int upMicPrivilege = roomData.getOrigPrivi();
        int oldMicTheme = voiceMicThemeRedis.getRecentlyTheme(roomData.getRid(), VoiceMicThemeRedis.LIVE_TYPE);
        int toMicThemeId = oldMicTheme == 0 ? RoomConstant.MIC_THEME_DEFAULT_ID : oldMicTheme;
//        logger.info("checkUpMicPrivilege toMicThemeId={} oldMicTheme={} roomId={} uid={}",
//                toMicThemeId, oldMicTheme, roomData.getRid(), uid);
        // 如果之前不是申请上麦，则发送一次上麦方式变更消息
        if (upMicPrivilege != 3) {
            roomSettingsService.sendRoomOptMsg(roomData.getRid(), uid, upMicPrivilege == 1 ? 2 : upMicPrivilege == 2 ? 3 : 2, true);
        }

        if (toMicThemeId != RoomConstant.MIC_THEME_DEFAULT_ID) {
            if (AppVersionUtils.versionCheck(849, req)) {
                logger.info("voice room recover micTheme={} roomId={} uid={}", toMicThemeId, roomData.getRid(), uid);
                roomThemeService.changeMicTheme(roomData, uid, toMicThemeId, true);
//                if (isCreate) {
//                    roomThemeService.changeMicTheme(roomData, "", oldMicTheme);
//                } else {
//                    TimerService.getService().addDelay(new DelayTask(1000) {
//                        @Override
//                        protected void execute() {
//                            roomThemeService.changeMicTheme(roomData, "", oldMicTheme);
//                        }
//                    });
//                }
                roomData.setMic_theme(toMicThemeId);
                roomData.setTheme_bg_type(RoomConstant.THEME_BG_TYPE_MIC);
            }
            voiceMicThemeRedis.deleteRecentlyTheme(roomData.getRid(), VoiceMicThemeRedis.LIVE_TYPE);
        }
        return toMicThemeId;
    }

    /**
     * 加入房间，返回房间信息、麦位信息
     * <p>
     * 房主进入房间 -> 开启直播房 OR 开启普通房
     * 其他用户进入房间 -> 进入直播房 OR 进入普通房
     */
    public RoomVO joinRoom(RoomDTO req) {
        if (needToUpgrade(req)) {
            logger.info("need to upgrade app uid={} versionCode={}", req.getUid(), req.getVersioncode());
            throw new CommonException(RoomHttpCode.NEED_UPGRADE);
        }
        // 版本强更
        if (!AppVersionUtils.versionCheck(861, req)) {
            throw new CommonException(RoomHttpCode.NEED_UPGRADE);
        }

        RoomActorDetailData myData = roomActorCache.getData(req.getRoomId(), req.getUid(), true);
        String ownerUid;
        try {
            ownerUid = RoomUtils.getRoomHostId(req.getRoomId());
        } catch (Exception e) {
            logger.error("roomId invalid, roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        RoomActorDetailData ownerData = roomActorCache.getData(req.getRoomId(), ownerUid, true);
        if (null == myData || null == ownerData) {
            logger.error("cannot find actor, uid={} ownerUid={}", req.getUid(), ownerUid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(req.getRoomId());
        if (null == roomData) {
            logger.error("cannot find room roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }

        // 指定主题要根据版本是否可以进入
        RoomMicThemeData roomMicThemeData = roomMicThemeDao.selectMicThemeById(roomData.getMic_theme());
        if (roomMicThemeData.getVersion() > 0 && !AppVersionUtils.versionCheck(roomMicThemeData.getVersion(), req)) {
            throw new CommonException(RoomHttpCode.ROOM_UPGRADED);
        }


        int vipLevel = vipInfoDao.getIntVipLevel(req.getUid());
        int reportLevel = roomKickRedis.getReportLevel(myData.getAid());
        // 进入房间权限校验
        checkJoinRoomPrivilege(myData, ownerData, req, reportLevel);
        return getJoinRoomResp(req, roomData, myData, ownerData, vipLevel, reportLevel);
    }

    private boolean needToUpgrade(RoomDTO req) {
        SudGameSimpleInfo sudGameInfo = sudGameRedis.getSudGameSimpleInfo(req.getRoomId());
        if (sudGameInfo != null && sudGameInfo.getGameType() == SudGameConstant.WOISSPY_GAME && !AppVersionUtils.versionCheck(865, req)) {
            return true;
        }

        if (ClientOS.ANDROID == req.getOs()) {
            return req.getVersioncode() < 385;
        }
        return RoomUtils.isGameRoom(req.getRoomId());
    }

    /**
     * 填充进房间返回数据
     */
    private RoomVO getJoinRoomResp(RoomDTO req, MongoRoomData roomData, RoomActorDetailData myData, RoomActorDetailData ownerData,
                                   int vipLevel, int reportLevel) {
        RoomVO resp = new RoomVO();
        // 基础信息填充
        fillRoomResp(ownerData, myData, roomData, resp, req);
        resp.getRoomVisitor().setRidData(myData.getRidData());
        resp.getRoomVisitor().setRid(myData.getRid());
        resp.getRoomVisitor().setVip(vipLevel);
        resp.getRoomVisitor().setVipMedal(myData.getVipMedal());
        resp.getRoomVisitor().setReportLevel(reportLevel);
        resp.getRoomVisitor().setMyName(myData.getName());
        if (myData.getAid().equals(ownerData.getAid())) {
            if (roomData.getOwnerRid() != ownerData.getRid()) {
                // 同步房主最新的rid
                roomDao.updateField(req.getRoomId(), "ownerRid", ownerData.getRid());
            }
            ownerJoinRoom(roomData, vipLevel, req, resp);
            resp.getRoomConfig().setActivityRoomPolicy(activityRoomRedis.isActivityRoom(myData.getAid()) ? ACTIVITY_ROOM_POLICY_URL : null);
        } else {
            otherJoinRoom(ownerData, req, resp);
        }
        resp.getRoomVisitor().setApplyCount(micApplyRedis.getMicApplyCount(req.getRoomId()));
        if (resp.getRoomVisitor().getAdmin() == 1) {
            resp.getRoomVisitor().setFirstHead(micApplyRedis.getFirstMicApplyHead(req.getRoomId()));
        }
        // 设置房间主题
        resp.getRoomConfig().setRoomTheme(getRoomTheme(resp.getRoomConfig().getMicTheme(), roomData));
        // 新房间背景上架时间
        resp.getRoomConfig().setNewBgShelfTime(themeDao.getNewBgShelfTime());
        // 房间火箭信息
        resp.getRoomConfig().setRocketInfo(roomRocketService.getRocketAndBoxInfo(req.getRoomId(), req.getUid()));
        // 房间火箭信息V2
        resp.getRoomConfig().setRocketInfoV2(roomRocketV2Service.getRocketInfo(req.getRoomId(), req.getUid()));
        // 房间活动信息
        resp.getRoomConfig().setRoomEvent(roomEventService.getRoomEventVO(req.getRoomId(), req.getUid(), req.getSlang()));
        // 普通房间设置相关属性
        if (RoomType.COMP != resp.getRoomConfig().getComp()) {
            resp.getRoomVisitor().setTalkForbidTime(micFrameRedis.getForbidTime(req.getRoomId(), req.getUid()));
            resp.getRoomVisitor().setMicForbidTime(roomMicRedis.getMicForbidTime(req.getRoomId(), req.getUid()));
        }
        if (pwdCheck(roomData, ownerData, myData, resp)) {
            // 进入房间后置处理
            afterJoinRoom(req, roomData, resp);
            // 最后才填充麦位信息
            fillMicData(roomData, req, resp);
        }
        // 填充进入房间的用户自己设置的配置
        fillActorRoomConfig(myData.getAid(), resp, req);
        // 重新进房间清除房间首次点赞记录
        roomLikeRedis.removeFirstLikeRecord(req.getUid(), req.getRoomId());

        if (!ObjectUtils.isEmpty(req.getEnterRoomSource())) {
            roomPlayerRedis.setEnterRoomSource(req.getRoomId(), req.getUid(), req.getEnterRoomSource());
        }

        // 语音房设置真心话大冒险主题信息
        String truthOrDareGameId = truthOrDareV2Redis.getGameIdByRoomId(req.getRoomId());
        if (!ObjectUtils.isEmpty(truthOrDareGameId)) {
            TruthOrDareV2Info gameInfo = truthOrDareV2Redis.getGameInfo(truthOrDareGameId);
            TruthOrDareV2VO truthOrDareV2VO = new TruthOrDareV2VO();
            BeanUtils.copyProperties(gameInfo, truthOrDareV2VO);
            TruthOrDareV2Info.SelectedUserInfo selectedUserInfo = gameInfo.getSelectedUserInfo();
            if (selectedUserInfo != null) {
                BeanUtils.copyProperties(selectedUserInfo, truthOrDareV2VO);
                truthOrDareV2VO.setSelectIndex(selectedUserInfo.getIndex());
                truthOrDareV2VO.setSelectedUid(selectedUserInfo.getUid());
                truthOrDareV2VO.setSelectedUserName(selectedUserInfo.getName());
                truthOrDareV2VO.setSelectedHead(selectedUserInfo.getHead());
            }
            resp.getRoomConfig().setTruthOrDareGameInfo(truthOrDareV2VO);
        }
        // 填充新设备用户奖励
        fillNewDeviceUserReward(myData, resp, req);
        // 设置sud游戏类型
        fillSudGameInfo(resp, req);
        // 填充上麦奖励
        fillMicReward(resp, req.getUid());
        return resp;
    }

    /**
     * 填充上麦奖励
     */
    private void fillMicReward(RoomVO resp, String reqUid) {
        if (StringUtils.isEmpty(reqUid)) {
            logger.error("fillMicReward reqUid is null");
            throw new CommonException(RoomHttpCode.USER_INVALID);
        }
        int taskDate = Integer.parseInt(DateHelper.ARABIAN.formatDateInDay2());
        // 判断有没有领取上麦奖励
        UserTaskData userTask = userTaskDao.getTaskByTaskKey(reqUid, taskDate, DAILY_MIC_TASK_KEY);
        if (null != userTask && userTask.getStatus() == 2) {
            // 已经领取
            return;
        }
        // 填充上麦奖励
        TakeMicDailyReward takeMicDailyReward = new TakeMicDailyReward();
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(DAILY_MIC_TASK_RESOURCE_KEY);
        if (resourceKeyConfigData != null) {
            ResourceKeyConfigData.ResourceMeta resourceMeta = resourceKeyConfigData.getResourceMetaList().get(0);
            takeMicDailyReward.setIcon(resourceMeta.getResourceIcon());
            takeMicDailyReward.setNumber(resourceMeta.getResourceNumber());
            takeMicDailyReward.setTime(resourceMeta.getResourceTime());
            takeMicDailyReward.setType(resourceMeta.getResourceType());
            resp.getRoomVisitor().setTakeMicDailyReward(takeMicDailyReward);
        }
    }

    /**
     * 填充sud游戏信息
     */
    private void fillSudGameInfo(RoomVO resp, RoomDTO req) {
        SudGameSimpleInfo sudGameInfo = sudGameRedis.getSudGameSimpleInfo(req.getRoomId());
        if (sudGameInfo != null && sudGameInfo.getGameType() == SudGameConstant.WOISSPY_GAME) {
            resp.getRoomConfig().setSudGameInfo(sudGameInfo);
        }
    }

    /**
     * 填充新设备用户奖励
     */
    private void fillNewDeviceUserReward(RoomActorDetailData myData, RoomVO resp, RoomDTO req) {
        String reqUid = req.getUid();
        if (!AppVersionUtils.versionCheck(8621, req)) {
            resp.getRoomVisitor().setIsShowMicGuide(ActorUtils.isNewRegisterActor(myData.getAid(), 15) ? 1 : 0);
            return;
        }

        boolean newUser = ActorUtils.isNewRegisterActor(reqUid, 7);
        int stayRoomStatus = userTaskRedis.getUserStayInRoomStatus(reqUid);
        if (!newUser || stayRoomStatus > 0) {
            resp.getRoomVisitor().setIsShowMicGuide(ActorUtils.isNewRegisterActor(myData.getAid(), 15) ? 1 : 0);
            return;
        }
        String resKey;
        if (ObjectUtils.isEmpty(myData.getFirstTnId())) {
            resKey = NEW_DEVICE_ACCOUNT_OTHER;
        } else {
            String countryCode = ActorUtils.getUpperCaseCountryCode(myData.getCountry());
            resKey = String.format("%sstay", countryCode);
            resKey = NEW_DEVICE_ACCOUNT_RES_KEY.contains(resKey) ? resKey : NEW_DEVICE_ACCOUNT_RES_KEY.get(0);
        }

        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(resKey);
        if (resourceKeyConfigData == null) {
            resp.getRoomVisitor().setIsShowMicGuide(ActorUtils.isNewRegisterActor(myData.getAid(), 15) ? 1 : 0);
            return;
        }
        List<ResourceMetaData> rewardConfigList = new ArrayList<>();
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = resourceKeyConfigData.getResourceMetaList();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            ResourceMetaData rewardConfig = new ResourceMetaData();
            BeanUtils.copyProperties(resourceMeta, rewardConfig);
            rewardConfigList.add(rewardConfig);
        }
        NewDeviceReward newDeviceReward = new NewDeviceReward();
        newDeviceReward.setCodeCountry(myData.getCountry());
        newDeviceReward.setTaskKey("stay_in_the_room");
        newDeviceReward.setTaskLimit(1);
        newDeviceReward.setRewardConfigList(rewardConfigList);
        resp.getRoomVisitor().setDeviceReward(newDeviceReward);
    }


    /**
     * 填充进入房间的用户自己设置的配置
     */
    private void fillActorRoomConfig(String uid, RoomVO resp, RoomDTO req) {
        ActorConfigData configData = actorConfigDao.findData(uid);
        if (null == configData) {
            configData = actorConfigDao.initActorConfigData(req.getUid());
        }
        Map<String, Object> config = configData.getRoom_config();
        if (config == null) {
            config = new HashMap<>(ActorConfigDao.DEF_ROOM_CONFIG);
            configData.setRoom_config(config);
        }
        boolean modify = false;
        if (!config.containsKey(ActorConfigDao.HIDE_ALL)) {
            config.putIfAbsent(ActorConfigDao.HIDE_ALL, 0);
            config.putIfAbsent(ActorConfigDao.HIDE_GIFT_RIDE_VOICE, 0);
            config.putIfAbsent(ActorConfigDao.HIDE_MIC, 0);
            config.putIfAbsent(ActorConfigDao.HIDE_RIPPLE, 0);
            config.putIfAbsent(ActorConfigDao.HIDE_GIF_AUTO, 0);
            modify = true;
        }
        JSONObject objectConfig = new JSONObject();
        objectConfig.putAll(config);

        if (lowModelsRedis.isLowModels(req.getUid())) {
            boolean modifyLow = false;
            for (String key : config.keySet()) {
                if (ActorConfigDao.HIDE_GIFT.equals(key)) {
                    if (objectConfig.getIntValue(key) != 1) {
                        config.put(key, 1);
                        objectConfig.put(key, 1);
                        modifyLow = true;
                    }
                } else if (ActorConfigDao.HIDE_RIDE.equals(key)) {
                    if (objectConfig.getIntValue(key) != 1) {
                        config.put(key, 1);
                        objectConfig.put(key, 1);
                        modifyLow = true;
                    }
                } else if (ActorConfigDao.HIDE_ALL.equals(key)) {
                    if (objectConfig.getIntValue(key) != 1) {
                        config.put(key, 1);
                        objectConfig.put(key, 1);
                        modifyLow = true;
                    }
                }
            }
            if (modifyLow) {
                modify = true;
                logger.info("low models auto close effect uid={}", req.getUid());
            }
            resp.getRoomConfig().setLowModels(1);
        }

        if (objectConfig.getIntValue(ActorConfigDao.HIDE_ALL) == 1) {
            if (objectConfig.getIntValue(ActorConfigDao.HIDE_GIFT_RIDE_VOICE) == 0
                    || objectConfig.getIntValue(ActorConfigDao.HIDE_MIC) == 0
                    || objectConfig.getIntValue(ActorConfigDao.HIDE_RIPPLE) == 0
                    || objectConfig.getIntValue(ActorConfigDao.HIDE_RIDE) == 0
                    || objectConfig.getIntValue(ActorConfigDao.HIDE_GIFT) == 0
            ) {
                config.put(ActorConfigDao.HIDE_GIFT_RIDE_VOICE, 1);
                config.put(ActorConfigDao.HIDE_MIC, 1);
                config.put(ActorConfigDao.HIDE_RIPPLE, 1);
                config.put(ActorConfigDao.HIDE_RIDE, 1);
                config.put(ActorConfigDao.HIDE_GIFT, 1);
                modify = true;
            }
        }

        if (modify) {
//            logger.info("reset effect uid={} config={}", req.getUid(), config);
            actorConfigDao.save(configData);
        }
        resp.getRoomConfig().setActorRoomConfig(config);
//        logger.info("after room config uid={} config={} lowModels={} objectConfig={}",
//                req.getUid(), config, resp.getRoomConfig().getLowModels(), objectConfig);
    }


    /**
     * 判断房间是否有密码，有密码时不返回流信息，app弹窗输入密码后再返回(pwd_check)
     */
    public boolean pwdCheck(MongoRoomData roomData, RoomActorDetailData ownerData, RoomActorDetailData myData, RoomVO resp) {
        // 房间密码处理
        if (!StringUtils.isEmpty(roomData.getPwd()) && !ownerData.getAid().equals(myData.getAid())) {
            // 删除旧的进房记录，py回重新写入带密码的值
            roomRedis.deleteRoomJoin(myData.getAid());
            return false;
        } else {
            // 这里增加进房记录
            roomRedis.addRoomJoin(myData.getAid(), roomData.getRid());
            // 第三方数据
            String streamId = genStreamId(roomData.getRid(), roomData.getPwd());
            resp.getRoomConfig().setStreamId(streamId);
            resp.getRoomConfig().setZegoToken(zegoService.getZegoToken(myData.getAid(), streamId));
            resp.getRoomConfig().setAgoraToken(agoraService.getAgoraToken(streamId, myData.getAid()));
            resp.getRoomConfig().setAgoraRtmToken(agoraService.getAgoraRtmToken(myData.getAid()));
            return true;
        }
    }

    /**
     * 获取上麦引导状态
     */
    public int getMicChatGuideStatus(String uid, int roomMode) {
        try {
            if (roomMode == RoomConstant.LIVE_ROOM_MODE) {
                return 0;
            }

            int currentTime = DateHelper.getNowSeconds();
            int registerTime = new ObjectId(uid).getTimestamp();
            // 大于7天不展示引导
            if (currentTime - registerTime >= 7 * 86400) {
                return 0;
            }
            String guideStatus = roomRecommendRedis.getMicChatGuideStatus(uid);
            if (StringUtils.isEmpty(guideStatus)) {
                roomRecommendRedis.setMicChatGuideStatus(uid, "1");
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            logger.error("getMicChatGuideStatus error uid:{}, roomMode:{}", uid, roomMode);
        }
        return 0;
    }


    /**
     * 获取房间主题配置
     */
    public RoomThemeVO getRoomTheme(int ownerMicTheme, MongoRoomData roomData) {
        RoomThemeVO themeVO = new RoomThemeVO();
        int theme = roomData.getTheme();
        int micThemeId = roomData.getMic_theme() == 0 ? 1 : roomData.getMic_theme();
        if (micThemeId == RoomConstant.MIC_THEME_VIDEO_ID && ownerMicTheme == RoomConstant.MIC_THEME_DEFAULT_ID) {
            micThemeId = ownerMicTheme;
        }
        RoomMicThemeData roomMicThemeData = roomMicThemeDao.selectMicThemeById(micThemeId);
        if (roomMicThemeData.getThemeType() > 0 && roomData.getTheme_bg_type() == RoomConstant.THEME_BG_TYPE_MIC) {
            theme = roomMicThemeData.getTid();
        }
        getRoomTheme(roomData.getRid(), theme, themeVO);
        return themeVO;
    }

    public void getRoomTheme(String roomId, int theme, RoomThemeVO themeVO) {
        int conquerLevel = conquerRedis.getRoomConquerLevel(roomId);
        if (conquerLevel != 0) {
            ConquerActivity conquerActivity = conquerActivityDao.findOne();
            if (conquerActivity != null && !CollectionUtils.isEmpty(conquerActivity.getConfigList())) {
                Map<Integer, String> conquerThemeMap = conquerActivity.getConfigList().stream().collect(Collectors.toMap(ConquerActivity.ConfigDetail::getLevel, ConquerActivity.ConfigDetail::getTheme));
                themeVO.setBgUrl(conquerThemeMap.get(conquerLevel));
                return;
            }
        }
        if (theme >= 1000) {
            UploadBackgroundData bgData = uploadBackgroundDao.selectOne(theme);
            if (null != bgData) {
                themeVO.setTid(theme);
                themeVO.setBgUrl(bgData.getBgUrl());
                themeVO.setmIcon(bgData.getmIcon());
                themeVO.setcIcon(bgData.getcIcon());
                themeVO.setPreview(bgData.getPreview());
            }
        } else {
            RoomTheme roomTheme = commonDao.findOne(new Query(Criteria.where("tid").is(theme)), RoomTheme.class);
            if (null != roomTheme) {
                themeVO.setTid(theme);
                String thumbnailUrl = ImageUrlGenerator.generateRoomTopicUrl(roomTheme.getBgurl());
                themeVO.setBgUrl(thumbnailUrl);
                themeVO.setmIcon(roomTheme.getMicon());
                themeVO.setcIcon(roomTheme.getCicon());
                themeVO.setPreview(thumbnailUrl);
            }
        }
    }

    /**
     * 进入房间后置处理
     */
    private void afterJoinRoom(RoomDTO dto, MongoRoomData roomData, RoomVO resp) {
        // 房间访客进房记录
        roomVisitorsRedis.addRoomVisitorRecord(dto.getRoomId(), dto.getUid());
        TaskFactory.getFactory().add(new Task() {
            @Override
            protected void execute() {
                roomWebSender.sendForceEnterRoom(dto.getRoomId(), dto.getUid(), roomData.getRtc_type());
                // 被引导进入房间的新用户进入房间
                RoomMicData roomMicData = roomRecommendRedis.selectData(dto.getUid());
                if (roomMicData != null && dto.getRoomId().equals(roomMicData.getRoomId())) {
                    sendOnboardingMsg(dto, roomMicData);
                    roomRecommendRedis.deleteData(dto.getUid());
                }
                // 资源按使用次数过期依赖这条消息
                sendEnterRoomMq(dto.getUid(), dto.getRoomId());
                // 非房主加入房间记录
                recentlyRoomRedis.addRecentlyActor(dto.getRoomId(), dto.getUid());
                // 记录进入房间的时间
                if (0 < dto.getRequestTime() && dto.getRequestTime() < System.currentTimeMillis() + 1000000L) {
                    recentlyRoomRedis.addEnterRoomClientTime(dto.getUid(), dto.getRequestTime());
                }
                if (dto.isChangeRoomName() && null != resp) {
                    // 房间名字变更
                    sendRoomInfoChangeMsg(resp.getRoomConfig().getRoomTheme().getBgUrl(), roomData);
                }
                micApplyRedis.removeMicApply(dto.getRoomId(), dto.getUid());
                if (dto.getFromScene() == FromSceneConstant.SEARCH_KEY) {
                    HotSearchHistoryData data = new HotSearchHistoryData();
                    data.setUid(dto.getUid());
                    data.setSearchKey(dto.getRoomId());
                    data.setSearchType(HotSearchHistoryConstant.ROOM_TYPE);
                    data.setSearchNum(1);
                    data.setMtime(DateHelper.getNowSeconds());
                    int row = hotSearchHistoryDao.incNum(data);
                    if (row <= 0) {
                        hotSearchHistoryDao.insertOrUpdate(data);
                    }
//                    logger.info("incNum hotSearchHistory success row:{} data:{}", row, data);
                }
                if (roomData.getVideo_switch() == VideoActionType.OPEN) {
                    shuShuAnalyseService.openYouToBeVideoEvent(dto.getRoomId(), dto.getUid(), 1);
                }
                ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
                dAUDao.updateNewDAUEvent(actorData, 4);

                // 检查是否有新用户进房，需要发送JoinRookieRoomMessage
                boolean isNew = ActorUtils.isNewDeviceAccount(dto.getUid(), actorData.getFirstTnId());
                if (isNew) {
                    Set<String> inRoomUserSet = roomPlayerRedis.getRoomActors(dto.getRoomId());
                    Set<String> recipientSet = new HashSet<>();

                    // 如果是迎新房间，添加迎新员工
                    if (newRookieRoomRedis.isNewRookieRoom(dto.getRoomId())) {
                        Set<String> allStaffUser = newRookieRoomRedis.getNewRookieUserByCache();
                        if (!CollectionUtils.isEmpty(inRoomUserSet) && !CollectionUtils.isEmpty(allStaffUser)) {
                            Set<String> inRoomStaffSet = inRoomUserSet.stream().filter(allStaffUser::contains).collect(Collectors.toSet());
                            recipientSet.addAll(inRoomStaffSet);
                        }
                    }

                    // 添加房间内的白名单用户
                    if (!CollectionUtils.isEmpty(inRoomUserSet)) {
                        Set<String> whitelistInRoom = inRoomUserSet.stream().filter(WHITELIST_USERS::contains).collect(Collectors.toSet());
                        recipientSet.addAll(whitelistInRoom);
                    }

                    // 如果有接收者，则发送消息
                    if (!CollectionUtils.isEmpty(recipientSet)) {
                        String configData = backstageConfigDao.getConfigData(BackStageConfigConstant.USER_RECOMMEND_CONFIG);
                        UserRecommendConfig config = JSONObject.parseObject(configData, UserRecommendConfig.class);
                        List<String> deviceNameList = new ArrayList<>(Arrays.asList(config.getDeviceNameList()));
                        String model = userRegisterDao.getModel(dto.getUid());
                        JoinRookieRoomMessage joinMessage = new JoinRookieRoomMessage();
                        joinMessage.setAid(dto.getUid());
                        joinMessage.setGender(actorData.getFb_gender());
                        joinMessage.setAge(actorData.getAge());
                        joinMessage.setHead(ImageUrlGenerator.generateMiniUrl(actorData.getHead()));
                        joinMessage.setIsNewUser(isNew ? 1 : 0);
                        joinMessage.setLocation(ActorUtils.getUpperCaseCountryCode(actorData.getCountry()));
                        joinMessage.setPlatform(actorData.getIntOs());
                        joinMessage.setSlang(actorData.getSlang());
                        joinMessage.setImportantUser(deviceNameList.contains(model) ? 1 : 0);
                        joinMessage.setName(actorData.getName());
                        recipientSet.forEach(recipient -> {
                            roomWebSender.sendPlayerWebMsg(dto.getRoomId(), "", recipient, joinMessage, false);
                            String recipientType = WHITELIST_USERS.contains(recipient) ? "whitelist" : "staff";
                            logger.info("user join room notice to {} uid:{} recipient:{} roomId:{} joinMessage:{}", recipientType, dto.getUid(), recipient, dto.getRoomId(), JSON.toJSONString(joinMessage));
                        });
                    }
                }
            }
        });
    }

    public void sendEnterRoomMq(String uid, String roomId) {
        try {
            EnterRoomMqData enterRoomMqData = new EnterRoomMqData();
            enterRoomMqData.setUid(uid);
            enterRoomMqData.setRoomId(roomId);
            enterRoomMqData.setItem(MqTopicItemConstant.ENTER_ROOM);
            logger.info("send novice task to mq. uid={}, roomId={}", enterRoomMqData.getUid(), enterRoomMqData.getRoomId());
            mqService.sendTopicMsgToMq(MqSenderService.ROUTE_KEY_USER_ENTER_ROOM, enterRoomMqData);

        } catch (AmqpException e) {
            logger.error("send novice task to mq error. {}", e.getMessage(), e);
        }
    }

    private void sendRoomInfoChangeMsg(String themeUrl, MongoRoomData roomData) {
        TaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 房间名字变更
                RoomInfoChangeMsg msg = new RoomInfoChangeMsg();
                msg.setRid(roomData.getRid());
                msg.setThemeUrl(themeUrl);
                msg.setRoomName(roomData.getName());
                msg.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                roomWebSender.sendRoomWebMsg(roomData.getRid(), "", msg, true);
                logger.info("room_back_url--sendRoomInfoChangeMsg 2168 roomData roomId:{} RoomInfoChangeMsg:{}", roomData.getRid(), msg);
            }
        });
    }

    private void sendOnboardingMsg(RoomDTO dto, RoomMicData roomMicData) {
        // 获取配置
        String configData = backstageConfigDao.getConfigData(BackStageConfigConstant.USER_RECOMMEND_CONFIG);
        UserRecommendConfig config = JSONObject.parseObject(configData, UserRecommendConfig.class);
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        NewUserOnboardingResponseMsg responseMsg = new NewUserOnboardingResponseMsg();
        responseMsg.setRoomId(dto.getRoomId());
        responseMsg.setUid(actorData.getUid());
        responseMsg.setName(actorData.getName());
        responseMsg.setHead(actorData.getHead());
        int randomNum = ThreadLocalRandom.current().nextInt(config.getGuideWordEn().length);
        responseMsg.setMsg_content(String.format(config.getGuideWordEn()[randomNum], actorData.getName()));
        responseMsg.setMsg_content_ar(String.format(config.getGuideWordAr()[randomNum], actorData.getName()));
        responseMsg.setAdmin(isAdmin(dto.getRoomId(), roomMicData.getUid()) ? 1 : 0);
        roomWebSender.sendPlayerWebMsg(dto.getRoomId(), dto.getUid(), roomMicData.getUid(), responseMsg, true);
    }

    /**
     * 判断是否为房间管理员 （包括管理员/房主/副房主 ）
     */
    private boolean isAdmin(String roomId, String uid) {
        RoomRoleData roomRoleData = memberDao.getRoleData(roomId, uid);
        return roomRoleData.getRole() == RoomRoleType.MANAGER || roomRoleData.isHostOrVice();
    }

    /**
     * 生成第三方流
     *
     * @param roomId 房间id
     * @param pwd    房间密码
     */
    public String genStreamId(String roomId, String pwd) {
        if (StringUtils.isEmpty(pwd)) {
            pwd = "you_star";
        }
        try {
            return DigestUtils.md5DigestAsHex((roomId + pwd).getBytes());
        } catch (Exception e) {
            logger.error("get streamId error roomId={} pwd={}", roomId, pwd);
            return null;
        }
    }

    /**
     * 填充返回数据
     */
    private void fillRoomResp(RoomActorDetailData ownerData, RoomActorDetailData myData, MongoRoomData roomData, RoomVO resp, RoomDTO req) {
        // 房主信息
        resp.copyFromActorData(ownerData);
        resp.getRoomOwner().setIsBeautifulRid(ownerData.getBeautifulRid());
        resp.getRoomOwner().setUserLevel(ownerData.getLevel());
        resp.getRoomOwner().setBadgeList(ownerData.getBadgeList());
        resp.getRoomOwner().setIdentify(ownerData.getIdentify());
        resp.getRoomOwner().setCountry(ownerData.getCountry());
        resp.getRoomOwner().setName(ownerData.getName());
        resp.getRoomOwner().setVip(ownerData.getVipLevel());
        resp.getRoomOwner().setVipMedal(ownerData.getVipMedal());
        resp.getRoomOwner().setHead(ImageUrlGenerator.generateRoomUserUrl(ownerData.getHead(), resp.getRoomOwner().getVip()));
        resp.getRoomOwner().setMicFrame(ownerData.getMicFrame());
        // 校验mic主题是否过期
        int micTheme = roomData.getMic_theme() == 0 ? 1 : roomData.getMic_theme();
        if (!roomThemeService.canUseMicTheme(ownerData.getVipLevel(), micTheme)) {
            logger.info("room host vip expired change theme. micTheme={} roomId={} vipLevel={} uid={}", micTheme, req.getRoomId(), ownerData.getVipLevel(), req.getUid());
            roomThemeService.changeMicTheme(roomData, req.getUid(), RoomConstant.MIC_THEME_DEFAULT_ID, true);
            roomData.setMic_theme(RoomConstant.MIC_THEME_DEFAULT_ID);
            roomData.setTheme_bg_type(RoomConstant.THEME_BG_TYPE_DEFAULT);
        }
        // 用户信息
        resp.getRoomVisitor().setUid(myData.getAid());
        resp.getRoomVisitor().setMyName(myData.getName());
        resp.getRoomVisitor().setMyHead(myData.getHead());
        resp.getRoomVisitor().setIdentify(myData.getIdentify());
        resp.getRoomVisitor().setBubbleId(myData.getBubbleId());
        resp.getRoomVisitor().setIsFollowRoom(commonDao.isFollowRoom(req.getRoomId(), myData.getAid()) ? 1 : 0);
        resp.getRoomVisitor().setUserLevel(myData.getLevel());
        resp.getRoomVisitor().setBadgeList(myData.getBadgeList());
        resp.getRoomVisitor().setStreamId(thirdPartApi.generateActorStreamId(roomData.getOwnerRid(),
                myData.getRid(), myData.getAid(), roomData.getRtc_type()));
//        resp.getRoomVisitor().setMicChatGuide(getMicChatGuideStatus(req.getUid(), roomData.getRoomMode()));
        resp.getRoomVisitor().setMicChatGuide(0);
        // 处理进场动画
        fillJoinCartoon(myData, myData, resp);
        // 房间配置信息
        resp.copyFromMongoRoomData(roomData, req);
        resp.getRoomConfig().setTag(roomData.getTag());
        resp.getRoomConfig().setTagName(roomTags.getTagNameById(roomData.getTag(), req.getSlang()));
        resp.getRoomConfig().setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead(), resp.getRoomOwner().getVip()));
        // 房间贡献度
        long devotes = roomNewService.getRoomTotalDevote(roomData.getRid());
        resp.getRoomConfig().setDevotes(devotes);
        resp.getRoomConfig().setDevotesStr(formatDevotes(devotes));
        List<String> rankList = roomHotDevoteRedis.getHotRankList();
        int rank = rankList.indexOf(roomData.getRid());
        int len = rankList.size();
        int myRank = rank == -1 ? len < RoomHotDevoteRedis.HOT_CHANGE_SIZE ? len + 1 : RoomHotDevoteRedis.HOT_CHANGE_MAX_RANK : rank + 1;
        resp.getRoomConfig().setHotRank(String.valueOf(myRank));
        List<String> devoteUserList = roomNewService.getRoomDevoteUserList(myData.getAid(), roomData.getRid());
        resp.getRoomConfig().setDevoteUserList(devoteUserList);
        resp.getRoomConfig().setLiveRoomRequirement(roomConfig.getLiveRoomRequirement());
        // 房间会员数
        resp.getRoomConfig().setMemberCount(roomMemberDao.getMemberCount(req.getRoomId()));
        // 房间点赞数
        String strDate = DateHelper.ARABIAN.formatDateInDay();
        resp.getRoomConfig().setRoomLikesNum(roomLikeRedis.getRoomLikesNum(req.getRoomId()));
        // 房间固定配置
        resp.getRoomConfig().setFollowTime(roomConfig.getFollowTime());
        resp.getRoomConfig().setDirtyVersion(getDirtyVersion());
        resp.getRoomConfig().setSendGiftExpire(roomConfig.getSendGiftExpireTime());
        resp.getRoomConfig().setVipRoomConfig(roomConfig.getVipRoomConfig());
        resp.getRoomConfig().setFollowWord(roomConfig.getRandomFollowStr(req.getSlang()));
        resp.getRoomConfig().setVideoVip(commonConfig.getConfigValue(CommonConfig.VIDEO_VIP_LEVEL, 3));
        resp.getRoomConfig().setHttpHeartSwitch(roomConfig.getHttpHeartSwitch());
        resp.getRoomConfig().setEmojiSwitch(roomConfig.getEmojiSwitch());
        resp.getRoomConfig().setUploadLog(assessUploadLog(req));
        int now = DateHelper.getNowSeconds();
//        resp.getRoomConfig().setLikeIconUrlList(roomConfig.getLikeIconUrlList());
        if (1726606800 <= now && now <= 1727211600) {
            resp.getRoomConfig().setLikeIconUrlList(roomConfig.getLikeIconUrlList());
        } else {
            resp.getRoomConfig().setLikeIconUrlList(Collections.emptyList());
        }
//        logger.info("likeIconUrlList:{}",resp.getRoomConfig().getLikeIconUrlList());
        // 房间插件状态
        resp.getRoomConfig().setVideoSwitch(roomData.getVideo_switch() == 0 ? VideoActionType.CLOSE : roomData.getVideo_switch());
        resp.getRoomConfig().setGuideList(roomConfig.getRandomGuideList(req.getSlang()));
        resp.getRoomConfig().setWelcomeWord(roomConfig.getWelcomeWordList(req.getSlang()));
        // 新用户特殊属性
        if (ActorUtils.isNewRegisterActor(req.getUid(), 7)) {
            resp.getRoomVisitor().setIsRookie(1);
            resp.getRoomVisitor().setGiftBag(giftBagDao.hadReceivedGift(req.getUid()) ? 1 : 0);
        }
        resp.getRoomVisitor().setFirstDayRookie(ActorUtils.isNewRegisterActor(req.getUid(), 1) ? 1 : 0);
        // 房间重复消息费用
        resp.getRoomConfig().setRepeatMsgCost(RoomMsgController.REPEAT_MSG_COST);
        // 查询用户是否为ptg
        boolean isPtg = partyGirlRedis.isPartyGirlByRedis(myData.getAid());
        resp.getRoomConfig().setIsPtg(isPtg ? 1 : 0);
        // 房间等级
        resp.getRoomConfig().setRoomLevel(roomLevelService.getRoomLevel(req.getRoomId()));
    }

    @Cacheable(value = "getRoomTotalDevote", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public long getRoomTotalDevote(String roomId) {
        return commonDao.getRoomTotalDevote(roomId);
    }

    @Cacheable(value = "getRoomDevoteUserList", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<String> getRoomDevoteUserList(String uid, String roomId) {
        List<String> devoteUserList = roomHotDevoteRedis.getRoomUserRankList(roomId);
        boolean isSave = false;
        if (devoteUserList == null) {
            devoteUserList = new ArrayList<>();
            int start = DateHelper.getNowSeconds() - 24 * 3600;
            List<CountData> dataList = slaveUidAidDevoteLogDao.getUserRankByRoom(roomId, start, 3);
            for (CountData item : dataList) {
                String aid = item.getMyKey();
                String aidHead = actorDao.getActorDataFromCache(aid).getHead();
                devoteUserList.add(vipInfoDao.generateVipUrl(aid, aidHead, ImageUrlGenerator.MODE_50));
            }
            roomHotDevoteRedis.saveRoomUserRankList(roomId, devoteUserList);
            isSave = true;
        }
//        logger.info("getRoomDevoteUserList success isSave={} uid={} roomId={} devoteUserList={}", isSave, uid, roomId, devoteUserList);
        return devoteUserList;
    }

    private int assessUploadLog(RoomDTO req) {
        return 0;
//        if (ServerConfig.isNotProduct()) {
//            return 1;
//        }
//         return Math.abs((req.getUid()).hashCode()) % 10 == 1 ? 1 : 0;
//        return 1;
    }

    /**
     * 处理进场对象
     */
    private void fillJoinCartoon(RoomActorDetailData myData, RoomActorDetailData detailData, RoomVO resp) {
        EnterCartonData cartonData = new EnterCartonData();
        enterRoomContent.fillJoinCartoon(myData.getRideOption(), cartonData, myData.getAid(), detailData);
        resp.getRoomVisitor().setSmallIcon(cartonData.getSmallIcon());
        resp.getRoomVisitor().setJoinCartoonId(cartonData.getJoinCartoonId());
        resp.getRoomVisitor().setJoinCartoonType(cartonData.getJoinCartoonType());
        resp.getRoomVisitor().setSourceType(cartonData.getSourceType());
        resp.getRoomVisitor().setSourceUrl(cartonData.getSourceUrl());
        resp.getRoomVisitor().setSourceMd5(cartonData.getSourceMd5());
        resp.getRoomVisitor().setCartoonTimes(cartonData.getCartoonTimes());
        // 处理进房通知
        resp.getRoomVisitor().setEntryEffectUrl(cartonData.getEntryEffectUrl());
        resp.getRoomVisitor().setEntryEffectUrlAr(cartonData.getEntryEffectUrlAr());
    }

    /**
     * 填充麦位信息
     */
    private void fillMicData(MongoRoomData roomData, RoomDTO req, RoomVO resp) {
        // 处理actor信息
        PageUtils.PageData<RoomActorDetailData> pageData = roomActorListService.findDetailList(req, 1, ACTOR_PAGE_SIZE, true, false, false);
        resp.getRoomActor().setNextPage(pageData.nextPage);
        resp.getRoomActor().setPageSize(pageData.pageSize);
        boolean addVisitor = roomVisitorsRedis.showRoomVisitor(req);
        resp.getRoomActor().setTotalActors(addVisitor ? roomVisitorsRedis.getRoomVisitorNum(req.getRoomId()) : pageData.totalSize);
        resp.getRoomActor().setTotalInRoomActors(pageData.totalSize);
        resp.getRoomActor().setRoomActorList(pageData.list);
        // 检查是否可以自动上麦
        RoomMicListData listData = roomMicService.autoUpRoomMic(roomData, req.getUid(), req);
        // 处理麦位信息
        if (listData == null) {
            // 缓存的最新麦位数据
            RoomMicListVo roomMicListVo = roomMicRedis.getRoomMicFromRedis(req.getRoomId());
            if (null != roomMicListVo) {
                resp.getRoomMic().setVersion(roomMicListVo.getVersion());
                resp.getRoomMic().setList(roomMicListVo.getList());
            } else {
                listData = roomMicService.getRoomMicList(req.getRoomId(), roomDao.getRoomType(roomData), roomData.getMicSize());
                doFillMicData(roomData, listData, resp);
            }
        } else {
            doFillMicData(roomData, listData, resp);
        }
    }

    private void doFillMicData(MongoRoomData roomData, RoomMicListData listData, RoomVO resp) {
        // 这里重新渲染数据
        resp.getRoomMic().setVersion(listData.getVersion());
        resp.getRoomMic().setList(convertRespMicList(roomData, listData.getRoomId(), listData.getList()));
    }

    protected List<RoomMicInfoObject> convertRespMicList(MongoRoomData roomData, String
            roomId, List<RoomMicData> micList) {
        List<RoomMicInfoObject> retList = new ArrayList<>();
        Map<String, CheatGiftRedis.CheatGiftData> map = cheatGiftRedis.fillCheatGift(micList);
        List<String> inGameList = null;
        boolean isGameRoom = RoomUtils.isGameRoom(roomId);
        if (isGameRoom) {
            inGameList = sudGameRedis.getInGameUserList(roomId);
        }
        for (RoomMicData micData : micList) {
            RoomMicInfoObject rspData = new RoomMicInfoObject();
            rspData.setIndex(micData.getPosition());
            rspData.setStatus(micData.getStatus());
            rspData.setMute(micData.getMute());
            String uid = micData.getUid();
            if (!StringUtils.isEmpty(uid)) {
                RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
                if (detailData == null) {
                    logger.error("can not find room actor data. roomId={} uid={}", roomId, uid);
                    continue;
                }
                RoomMicUserObject userData = new RoomMicUserObject();
                userData.setHead(detailData.getHead());
                userData.setName(detailData.getName());
                userData.setAid(detailData.getAid());
                userData.setRipple_url(detailData.getRippleUrl());
                userData.setMic_frame(detailData.getMicFrame());
                userData.setVip_level(detailData.getVipLevel());
                userData.setVipMedal(detailData.getVipMedal());
                userData.setRole(detailData.getRole());
                userData.setIdentify(detailData.getIdentify());
                userData.setViceHost(detailData.getViceHost());
                CheatGiftRedis.CheatGiftData cheatGiftData = map.get(uid);
                userData.setVoice_type(cheatGiftData.getVoiceType());
                if (!StringUtils.isEmpty(cheatGiftData.getPrankMicFrame())) {
                    userData.setMic_frame(cheatGiftData.getPrankMicFrame());
                }
                if (isGameRoom && inGameList.contains(uid)) {
                    userData.setGameRunning(1);
                }
                rspData.setUser(userData);
                userData.setStreamId(thirdPartApi.generateActorStreamId(roomData.getOwnerRid(), detailData.getRid(), detailData.getAid(), roomData.getRtc_type()));
            }
            retList.add(rspData);
        }
        return retList;
    }

    /**
     * 获取脏词版本
     */
    public int getDirtyVersion() {
        try {
            Object dirtyVersion = clusterRedis.opsForHash().get("hash:common_config_key", "dirty_version");
            return null == dirtyVersion ? 0 : Integer.parseInt(String.valueOf(dirtyVersion));
        } catch (Exception e) {
            logger.error("get dirty version msg={}", e.getMessage());
            return 0;
        }
    }

    /**
     * 判断是否调用了图灵盾接口，未调用接口的用户返回不为空
     */
    private boolean isCheckTnId(String uid) {
        try {
            Double score = clusterRedis.opsForZSet().score("zset:is:tn:check", uid);
            return score == null;
        } catch (Exception e) {
            logger.error("isCheckTnId error msg={}", e.getMessage(), e);
            return true;
        }
    }

    /**
     * 格式化房间贡献数值
     */
    private String formatDevotes(long devotes) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f).setScale(1, RoundingMode.HALF_UP) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f).setScale(1, RoundingMode.HALF_UP) + "K";
        } else {
            return devotes + "";
        }
    }

    /**
     * 房主进入房间
     */
    private void ownerJoinRoom(MongoRoomData roomData, int vipLevel, RoomDTO req, RoomVO resp) {
        // 房主通过其他途径进入自己房间，不指定房间类型，则沿用之前的房间类型
        int roomType = req.getRoomType() == 0 ? roomData.getRoom_type() : req.getRoomType();
        if (!STAFF_ROOM_SET.contains(req.getUid())) {
            // 校验背景图片是否过期
            checkThemeExpired(roomData, req.getUid(), vipLevel);
        }
        resp.getRoomVisitor().setAdmin(1);
        resp.getRoomConfig().setComp(roomType == RoomType.COMP ? RoomType.COMP : 0);
        resp.getRoomOwner().setName(resp.getRoomVisitor().getMyName());
        resp.getRoomOwner().setHead(resp.getRoomVisitor().getMyHead());
        // 每日任务房主进入自己的房间
        dailyTaskService.sendToMq(new DailyTaskMqData(req.getUid(), 3, DateHelper.ARABIAN.formatDateInDay2(), 1));
        if (req.isRoomChange()) {
            roomChange(roomData, req, resp);
        }
    }

    /**
     * 校验背景图片是否过期
     */
    private void checkThemeExpired(MongoRoomData roomData, String uid, int vipLevel) {
        if (roomData.getTheme() >= 1000) {
            // 使用自定义上传的主题，如果vip等级小于4，设置成默认主题
            if (vipLevel < 4) {
                logger.info("room owner vip4 expired, set default theme. roomId={} theme={}", roomData.getRid(), roomData.getTheme());
                setDefaultTheme(uid, roomData);
            }
        } else {
            // 非自定义上传的主题
            MongoThemeData themeData = themeDao.findThemeData(roomData.getTheme());
            if (themeData == null) {
                setDefaultTheme(uid, roomData);
                return;
            }
            // 使用vip主题，如果vip等级小于2且用户等级小于20，设置成默认主题
            int userLevel = userLevelDao.getUserLevel(RoomUtils.getRoomHostId(roomData.getRid()));
            if (themeData.getType() == 1 && vipLevel < 2 && userLevel < 20) {
                logger.info("room owner vip2 expired, set default theme. roomId={} theme={}", roomData.getRid(), roomData.getTheme());
                setDefaultTheme(uid, roomData);
            }
            // 购买和下发的主题过期后，设置成默认主题
            if (themeData.getType() != 0 && themeData.getType() != 1) {
                MineBackgroundData data = mineBackgroundDao.selectOne(roomData.getRid(), roomData.getTheme());
                if (data == null || DateHelper.getNowSeconds() >= data.getEndTime()) {
                    logger.info("Purchased background image expired, set default theme. roomId={} theme={}", roomData.getRid(), roomData.getTheme());
                    setDefaultTheme(uid, roomData);
                }
            }
        }
    }

    /**
     * 设置默认主题
     */
    private void setDefaultTheme(String uid, MongoRoomData roomData) {
        int defaultThemeId = 233;
        roomDao.updateField(roomData.getRid(), "theme", defaultThemeId);
        roomData.setTheme(defaultThemeId);
    }

    /**
     * 房间设置事件-更新标签
     */
    private void setRoomTagEvent(String roomId, int tagId) {
        eventReport.track(new EventDTO(new RoomSetupEvent(roomId, "room_tag", String.valueOf(tagId))));
    }

    private void roomChange(MongoRoomData roomData, RoomDTO req, RoomVO resp) {
        int toMicThemeId = roomData.getMic_theme();
        if (req.getRoomMode() == RoomConstant.VOICE_ROOM_MODE) {
            toMicThemeId = roomData.getMic_theme() == 0 ? RoomConstant.MIC_THEME_DEFAULT_ID : roomData.getMic_theme();
        } else if (RoomConstant.LIVE_ROOM_MODE == req.getRoomMode() && roomData.getMic_theme() == RoomConstant.MIC_THEME_DEFAULT_ID) {
            toMicThemeId = RoomConstant.MIC_THEME_LIVE_DEFAULT_ID;
        }
        roomSettingsService.sendRoomOptMsg(roomData.getRid(), req.getUid(), RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode() ? 8 : 7, false, toMicThemeId);
        // 防止切换房间造成缓存不一致，删除缓存重新发送麦位变更消息
        roomMicRedis.deleteRoomMic(roomData.getRid());
        micApplyRedis.clearMicApply(roomData.getRid());
        try {
            roomMicService.switchRoomMode(roomData, req.getUid(), req.getRoomMode());
        } catch (Exception ignored) {
        }
    }

    /**
     * 其他用户进入房间
     */
    private void otherJoinRoom(RoomActorDetailData ownerData, RoomDTO req, RoomVO resp) {
        // 设置房间角色
        RoomMemberData managerData = roomMemberDao.findData(req.getRoomId(), req.getUid());
        // 0 房主 1 管理员 2 观众 3 会员
        int roomRoleType = roomMemberDao.getRoleContainMember(managerData, req.getRoomId(), req.getUid());
        resp.getRoomVisitor().setRole(roomRoleType);
        resp.getRoomVisitor().setAdmin(roomRoleType == RoomRoleType.HOST || roomRoleType == RoomRoleType.MANAGER ? 1 : 2);
        resp.getRoomVisitor().setViceHost((null != managerData && managerData.getUtype() == 4) ? 1 : 0);
        resp.getRoomVisitor().setIsMember(roomRoleType != RoomRoleType.AUDIENCE ? 1 : 0);
        resp.getRoomVisitor().setIsFollowHost(followDao.isFollowed(req.getUid(), ownerData.getAid()) ? 1 : 0);
    }

    /**
     * 进入房间权限判断
     */
    private void checkJoinRoomPrivilege(RoomActorDetailData myData, RoomActorDetailData ownerData, RoomDTO req, int reportLevel) {
        if (myData.getValid() != 1) {
            throw new CommonException(RoomHttpCode.USER_INVALID);
        }
        if (ownerData.getValid() != 1) {
            throw new CommonException(RoomHttpCode.ROOM_BLOCK);
        }
        if (reportLevel > 2) {
            throw new CommonException(RoomHttpCode.REPORTED);
        }
        if (roomPlayerRedis.getRoomActorCount(req.getRoomId()) > 1500 && ServerConfig.isProduct()) {
            throw new CommonException(RoomHttpCode.ROOM_FULL);
        }
        if (myData.getAid().equals(ownerData.getAid())) {
            // 房主被禁，不能创建和进入房间
            String blockTime = blockRedis.checkBlock(actorDao.getActorDataFromCache(ownerData.getAid()).getTn_id(), BlockTnConstant.BLOCK_CREATE_ROOM);
            if (!StringUtils.isEmpty(blockTime)) {
                throw new CommonException(RoomHttpCode.BLOCK_CREATE_ROOM, blockTime);
            }
        } else {
            if (roomBlacklistDao.isBlock(req.getRoomId(), myData.getAid())) {
                throw new CommonException(RoomHttpCode.BLOCKED);
            }
        }
        int kickOutTime = roomKickRedis.getKickOutTime(req.getRoomId(), myData.getAid());
        if (kickOutTime > 0) {
            throw new CommonException(RoomHttpCode.KICKED, getKickOutDesc(req, kickOutTime));
        }
        ActorData actorData = actorDao.getActorDataFromCache(ownerData.getAid());
        int score = creditRiskService.getCreditRiskScoreByUid(ownerData.getAid(), actorData.getTn_id(), actorData.getIp());
        if (score < 100) {
            logger.info("actor is risk invalid score={} uid={} roomId={}", score, myData.getAid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.USER_RISK_INVALID_JOIN_ROOM);
        }
//        if (!isCheckTnId(myData.getAid())) {
//            logger.error("actor is not check tnId uid={}", myData.getAid());
//            monitorSender.info("ustar_java_exception", "用户未校验图灵盾接口", JSON.toJSONString(req));
//            basePlayerRedis.removeToken(myData.getAid());
//            throw new CommonException(RoomHttpCode.SESSION_INVALID);
//        }
        if (req.invalidPkgName()) {
            logger.error("app pkg name invalid uid={} pkgName={}", myData.getAid(), req.getApp_package_name());
            basePlayerRedis.removeToken(myData.getAid());
            throw new CommonException(RoomHttpCode.SESSION_INVALID);
        }
    }

    private String getKickOutDesc(RoomDTO req, int kickOutTime) {
        int hourTime = kickOutTime / 3600;
        if (hourTime > 0) {
            return SLangType.ARABIC == req.getSlang() ? String.format("%s ساعة", hourTime) : String.format("%s hours", hourTime);
        } else {
            int minuteTime = Math.max(1, kickOutTime / 60);
            return SLangType.ARABIC == req.getSlang() ? String.format("%s دقيقة", minuteTime) : String.format("%s minutes", minuteTime);
        }
    }

    /**
     * 分页获取房间内的用户
     */
    public RoomActorVO getRoomActors(PageDTO req) {
        RoomActorVO vo = new RoomActorVO();
        PageUtils.PageData<RoomActorDetailData> pageData = roomActorListService.findDetailList(req, req.getPage(), ACTOR_PAGE_SIZE, true, false, false);
        vo.setNextPage(pageData.nextPage);
        vo.setPageSize(pageData.pageSize);
        boolean addVisitor = roomVisitorsRedis.showRoomVisitor(req);
        vo.setTotalActors(addVisitor ? roomVisitorsRedis.getRoomVisitorNum(req.getRoomId()) : pageData.totalSize);
        vo.setTotalInRoomActors(pageData.totalSize);
        vo.setRoomActorList(pageData.list);
        return vo;
    }

    /**
     * 校验进房密码，成功后返回麦位列表和流信息
     */
    public PwdCheckVo pwdCheck(PwdCheckDTO req) {
        PwdCheckVo vo = new PwdCheckVo();
        String pwd = parseUserPwd(req.getPwd());
        if (ObjectUtils.isEmpty(pwd)) {
            logger.info("pwd check fail roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (null == actorData) {
            logger.error("cannot find actor, uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(req.getRoomId());
        if (null == roomData) {
            logger.error("cannot find room roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        doPwdCheck(roomData.getPwd(), pwd);
        // 第三方数据
        vo.setStreamId(genStreamId(roomData.getRid(), roomData.getPwd()));
        vo.setZegoToken(zegoService.getZegoToken(req.getUid(), vo.getStreamId()));
        vo.setAgoraToken(agoraService.getAgoraToken(vo.getStreamId(), req.getUid()));
        vo.setAgoraRtmToken(agoraService.getAgoraRtmToken(req.getUid()));
        // 麦位列表
        vo.setRoomMic(getNewestRoomMicList(roomData, roomDao.getRoomType(roomData)));
        roomRedis.addRoomJoin(req.getUid(), roomData.getRid() + "_" + DigestUtils.md5DigestAsHex(pwd.getBytes()));
        PageDTO pageDTO = new PageDTO();
        pageDTO.setPage(1);
        pageDTO.setUid(req.getUid());
        pageDTO.setRoomId(req.getRoomId());
        vo.setRoomActor(getRoomActors(pageDTO));
        vo.setRoomMode(roomData.getRoomMode() == 0 ? 1 : roomData.getRoomMode());
        afterJoinRoom(req, roomData, null);
        return vo;
    }

    /**
     * 获取最新的麦位列表
     */
    private RoomMicListVo getNewestRoomMicList(MongoRoomData roomData, int roomType) {
        RoomMicListVo roomMicListVo = roomMicRedis.getRoomMicFromRedis(roomData.getRid());
        if (null != roomMicListVo) {
            return roomMicListVo;
        }
        RoomMicListData roomMicList = roomMicService.getRoomMicList(roomData.getRid(), roomType, roomData.getMicSize());
        roomMicListVo = new RoomMicListVo();
        roomMicListVo.setList(convertRespMicList(roomData, roomData.getRid(), roomMicList.getList()));
        roomMicListVo.setVersion(roomMicList.getVersion());
        roomMicListVo.setRoomId(roomData.getRid());
        return roomMicListVo;
    }

    /**
     * 房间密码校验
     *
     * @param roomPwd 房主设置的密码
     * @param userPwd 用户输入的密码
     */
    private void doPwdCheck(String roomPwd, String userPwd) {
        if (null == roomPwd || !roomPwd.equals(userPwd)) {
            throw new CommonException(RoomHttpCode.PASSWORD_ERROR);
        }
    }

    private String parseUserPwd(String userPwd) {
        String userStrPwd = "";
        try {
            // 针对阿语数字进行特殊处理
            userStrPwd = Integer.parseInt(userPwd) + "";
        } catch (NumberFormatException e) {
            logger.info("parse userPwd error. userPwd={}", userPwd);
        }
        return userStrPwd;
    }

    /**
     * 高频接口：获取麦位列表
     */
    public RoomMicListVo getRoomMicList(RoomDTO req) {
        RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(req.getRoomId());
        if (null != roomMicVO) {
            return roomMicVO;
        }
        logger.info("skip getRoomMicList from redis. roomId={}", req.getRoomId());
        MongoRoomData roomData = roomDao.findData(req.getRoomId());
        RoomMicListData listData = roomMicService.getRoomMicList(req.getRoomId(), AppVersionUtils.versionCheck(844, req) ? roomData.getRoomMode() : req.getRoomType(), roomData.getMicSize());
        roomMicVO = new RoomMicListVo();
        roomMicVO.setVersion(listData.getVersion());

        roomMicVO.setList(convertRespMicList(roomData, req.getRoomId(), listData.getList()));
        roomMicVO.setRoomId(listData.getRoomId());
        return roomMicVO;
    }

    /**
     * 获取声网RTM授权
     */
    public String getAgoraRtmToken(String uid) {
        return agoraService.getAgoraRtmToken(uid);
    }

    /**
     * 获取即构或声网RTC授权
     */
    public String getToken(RoomDTO req) {
        String inRoomId = roomPlayerRedis.getActorRoomStatus(req.getUid());
        if (null == inRoomId || !inRoomId.equals(req.getRoomId())) {
            throw new CommonException(HttpCode.NOT_IN_ROOM);
        }
        MongoRoomData roomData = roomDao.findData(req.getRoomId());
        String streamId = genStreamId(req.getRoomId(), roomData.getPwd());
        if (1 == roomData.getRtc_type()) {
            return agoraService.getAgoraToken(streamId, req.getUid());
        } else {
            return zegoService.getZegoToken(req.getUid(), streamId);
        }
    }

    /**
     * 进入房间记录
     */
    public void enterRoom(RoomDTO req) {
        logger.info("enter room roomId={} uid={} reqTime={} requestId={}",
                req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId());
    }

    /**
     * 退出房间记录
     */
    public void quitRoom(RoomDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (AppVersionUtils.versionCheck(842, req)) {
            // 8.42起客户端不发送2003离开房间的mars消息
            long enterRoomTime = recentlyRoomRedis.getEnterRoomClientTime(uid);
            if (0 != req.getRequestTime() && req.getRequestTime() < enterRoomTime) {
                logger.info("quit request invalid, roomId={} uid={} enterRoomTime={} reqTime={}", roomId, uid, enterRoomTime, req.getRequestTime());
            } else {
                roomWebSender.sendLeaveRoom(roomId, uid);
            }
        }
    }

    /**
     * 获取房间密码
     */
    public RoomPwdVO getRoomPwd(RoomDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            logger.error("get room pwd param error. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (!Objects.equals(uid, RoomUtils.getRoomHostId(roomId))) {
            logger.error("not room owner cannot get room pwd. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(roomId);
        if (roomData == null) {
            logger.error("room data is null. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        RoomPwdVO vo = new RoomPwdVO();
        vo.setRoom_id(roomId);
        vo.setPwd(StringUtils.isEmpty(roomData.getPwd()) ? "" : roomData.getPwd());
        return vo;
    }

    public MongoRoomData getDataFromCache(String roomId) {
        MongoRoomData roomData = roomDao.getDataFromCache(roomId);
        if (null == roomData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return roomData;
    }

    public void actorValidCheck(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (null == actorData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (actorData.getValid() == 0) {
            throw new CommonException(RoomHttpCode.USER_INVALID);
        }
    }

    public Object addFollow(RoomNewDTO req) {
        String reqUid = req.getUid();
        actorValidCheck(reqUid);
        MongoRoomData roomData = getDataFromCache(req.getRoomId());
        followRoomDao.upsert(roomData.getRid(), req.getUid());
        if (ServerConfig.isNotProduct() || Boolean.TRUE.equals(clusterRedis.opsForValue().setIfAbsent("str:addRoomFollow:" + req.getRoomId() + req.getUid(), req.getUid(), 2, TimeUnit.HOURS))) {
            sendFollowRoomMsg(req.getUid(), req.getRoomId());
        }
        RoomRelateEvent relateEvent = new RoomRelateEvent();
        relateEvent.setUid(reqUid);
        relateEvent.setRoom_id(req.getRoomId());
        relateEvent.setRoom_relate_type(1);
        relateEvent.setRoom_relate_source(req.getRoomRelateSource());
        relateEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(relateEvent));
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(req.getUid(), req.getRoomId(), "", req.getRoomId(), CommonMqTaskConstant.FOLLOW_ROOM, 1));
        return null;
    }

    public void sendFollowRoomMsg(String uid, String roomId) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String inRoomId = roomPlayerRedis.getActorRoomStatus(uid);
                if (!roomId.equals(inRoomId)) {
                    logger.info("sendFollowRoomMsg actor not in room. uid={} roomId={}", uid, roomId);
                    return;
                }
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (null == actorData) {
                    return;
                }
                RoomNotificationMsg msg = new RoomNotificationMsg();
                msg.setUid(uid);
                msg.setUser_name(actorData.getName());
                msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
                msg.setText(String.format(FOLLOW_ROOM_MSG, actorData.getName()));
                msg.setText_ar(String.format(FOLLOW_ROOM_MSG_AR, actorData.getName()));
                List<HighlightTextObject> list = new ArrayList<>();
                HighlightTextObject object = new HighlightTextObject();
                object.setText(actorData.getName());
                object.setHighlightColor("#FFE200");
                list.add(object);
                msg.setHighlight_text(list);
                msg.setHighlight_text_ar(list);
                msg.setHide_head(1);
                List<RoomMemberData> adminList = memberDao.findAdminList(roomId);
                for (RoomMemberData roomMemberData : adminList) {
                    if (!roomId.equals(roomPlayerRedis.getActorRoomStatus(roomMemberData.getAid()))) {
                        logger.info("sendFollowRoomMsg actor not in room. roomId={} uid={} aid={}", roomId, uid, roomMemberData.getAid());
                        return;
                    }
                    roomWebSender.sendPlayerWebMsg(roomId, uid, roomMemberData.getAid(), msg, false);
                }
                roomWebSender.sendPlayerWebMsg(roomId, uid, RoomUtils.getRoomHostId(roomId), msg, false);
            }
        });
    }

    public Object delFollow(RoomNewDTO req) {
        followRoomDao.delete(req.getRoomId(), req.getUid());
        RoomRelateEvent relateEvent = new RoomRelateEvent();
        relateEvent.setUid(req.getUid());
        relateEvent.setRoom_id(req.getRoomId());
        relateEvent.setRoom_relate_type(2);
        relateEvent.setRoom_relate_source(req.getRoomRelateSource());
        relateEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(relateEvent));
        return null;
    }

    private SearchVO.ActorVO copyActorVO(ActorData actorData) {
        SearchVO.ActorVO actorVO = new SearchVO.ActorVO();
        actorVO.setAid(actorData.getUid());
        actorVO.setName(actorData.getName());
        actorVO.setGender(actorData.getFb_gender());
        actorVO.setAge(actorData.getAge());
        actorVO.setRid(actorData.getRid());
        actorVO.setRidData(actorData.getRidData());
        actorVO.setUlvl(userLevelDao.getUserLevel(actorData.getUid()));
        actorVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), actorVO.getViplevel()));
        actorVO.setMicFrame(micFrameRedis.getMicSourceFromCache(actorData.getUid()));
        return actorVO;
    }

    private SearchVO.RoomVO copyRoomVO(MongoRoomData roomData) {
        SearchVO.RoomVO roomVO = new SearchVO.RoomVO();
        roomVO.setRoomId(roomData.getRid());
        roomVO.setRoom_name(roomData.getName());
        roomVO.setRoom_head(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead(), vipInfoDao.getIntVipLevelFromCache(RoomUtils.getRoomHostId(roomData.getRid()))));
        roomVO.setAnnounce(roomData.getAnnounce());
        roomVO.setCountry(roomData.getCountry());
        roomVO.setComp(roomData.getComp());
        roomVO.setPwd(StringUtils.isEmpty(roomData.getPwd()) ? 0 : 1);
        roomVO.setRoom_type(roomData.getRoom_type() == 0 ? 4 : roomData.getRoom_type());
        roomVO.setRoomMode(roomData.getRoomMode() == 0 ? RoomConstant.VOICE_ROOM_MODE : roomData.getRoomMode());
        roomVO.setOnline(roomPlayerRedis.getRoomActorsCount(roomData.getRid()));
        roomVO.setIsOfficial(OfficialRoom.ifOfficialRoom(roomData.getRid()) ? 1 : 0);
        return roomVO;
    }

    //    @Cacheable(value = "roomSearch", key = "#p0.getKey()+'-'+#p0.getApp_package_name()",
//            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public SearchVO search(RoomNewDTO req) {
        SearchVO vo = new SearchVO();
        if (ObjectUtils.isEmpty(req.getKey())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String key = req.getKey().trim();
        if (key.length() >= 30 || HIDE_KEY_SET.contains(key.toLowerCase())) {
            return vo;
        }
        String reqUid = req.getUid();
        boolean isTestUser = false;
        if (!StringUtils.isEmpty(reqUid)) {
            isTestUser = whiteTestDao.isMemberByType(reqUid, WhiteTestDao.WHITE_TYPE_RID);
        }
        String aid = "";
        try {
            ActorData actorData = actorDao.getActorByStrRidFromDb(key);
            if (null != actorData && 1 == actorData.getValid() && AccountConstant.DELETED != actorData.getAccountStatus()) {
                String sRoomId = RoomUtils.formatRoomId(actorData.getUid());
                aid = actorData.getUid();
                if (!isTestUser && (whiteTestDao.isMemberByType(actorData.getUid(), WhiteTestDao.WHITE_TYPE_RID)
                        || whiteTestDao.isMemberByType(sRoomId, WhiteTestDao.WHITE_TYPE_ROOM_ID))) {
                    // 非测试用户不能搜索测试用户,房间
                    logger.info("normal user can not search test user reqUid:{} intKey:{} sUid:{}", reqUid, key, actorData.getUid());
                } else {
                    vo.getUsers().add(copyActorVO(actorData));
                    MongoRoomData roomData = roomDao.getDataFromCache(sRoomId);
                    if (null != roomData) {
                        SearchVO.RoomVO roomVO = copyRoomVO(roomData);
                        roomVO.setRidData(actorData.getRidData());
                        roomVO.setTag(roomData.getTag());
                        roomVO.setTagName(roomTags.getTagNameById(roomData.getTag(), req.getSlang()));
                        roomVO.setRecreationTag(getRecreationTag(roomData));
                        vo.getRooms().add(roomVO);
                    }
                }
            }

        } catch (NumberFormatException e) {
            // // 字符串搜索
            // searchByName(vo, key, isTestUser, req.getSlang());
        }
        if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(req.getApp_package_name())) {
            for (SearchVO.RoomVO room : vo.getRooms()) {
                if (!StringUtils.isEmpty(room.getCountry())) {
                    for (String cc : HIDE_COUNTRY_SET) {
                        if (room.getCountry().toLowerCase().startsWith(cc)) {
                            room.setCountry("us_United States");
                        }
                    }
                }
            }
        }
        return vo;
    }

    public void asyncWriteHistoryTask(String uid, String aid) {
        if (!StringUtils.isEmpty(aid)) {
            executor.execute(() -> {
                HotSearchHistoryData data = new HotSearchHistoryData();
                data.setUid(uid);
                data.setSearchKey(RoomUtils.formatRoomId(aid));
                data.setSearchType(HotSearchHistoryConstant.ROOM_TYPE);
                data.setSearchNum(1);
                data.setMtime(DateHelper.getNowSeconds());
                int row = hotSearchHistoryDao.incNum(data);
                if (row <= 0) {
                    hotSearchHistoryDao.insertOrUpdate(data);
                }
                data.setSearchKey(aid);
                data.setSearchType(HotSearchHistoryConstant.USER_TYPE);
                int row2 = hotSearchHistoryDao.incNum(data);
                if (row2 <= 0) {
                    hotSearchHistoryDao.insertOrUpdate(data);
                }
                logger.info("row:{} insert or update HotSearchHistoryData success:{}", row, data);
            });
        }
    }

    private int getRecreationTag(MongoRoomData roomData) {
        return getRecreationTag(roomData, null, null, null);
    }

    private int getRecreationTag(MongoRoomData roomData, Map<String, TurntableGameInfo> turntableRoomMap,
                                 Map<String, TruthOrDareInfo> truthDareRoomMap, Map<String, SudGameInfo> sudGameRoomMap) {
        if (turntableRoomMap == null) {
            List<TurntableGameInfo> allTurntableGame = turntableGameRedis.getAllTurntableGame();
            List<TruthOrDareInfo> allTruthDareGame = truthOrDareRedis.getAllTurntableGame();
            turntableRoomMap = CollectionUtil.listToKeyMap(allTurntableGame, TurntableGameInfo::getRoomId);
            truthDareRoomMap = CollectionUtil.listToKeyMap(allTruthDareGame, TruthOrDareInfo::getRoom_id);
            sudGameRoomMap = recreationTagService.getAllSudGameInfo();
        }
        return recreationTagService.getRecreationTag(roomData, turntableRoomMap, sudGameRoomMap, truthDareRoomMap, Collections.emptySet(), Collections.emptyMap());
    }

    private void searchByName(SearchVO vo, String key, boolean isTestUser, int slang) {
        try {
            List<MongoActorData> mongoActorDataList = actorDao.searchByRegularName(key);
            List<TurntableGameInfo> allTurntableGame = turntableGameRedis.getAllTurntableGame();
            List<TruthOrDareInfo> allTruthDareGame = truthOrDareRedis.getAllTurntableGame();
            Map<String, TurntableGameInfo> turntableRoomMap = CollectionUtil.listToKeyMap(allTurntableGame, TurntableGameInfo::getRoomId);
            Map<String, TruthOrDareInfo> truthDareRoomMap = CollectionUtil.listToKeyMap(allTruthDareGame, TruthOrDareInfo::getRoom_id);
            Map<String, SudGameInfo> sudGameRoomMap = recreationTagService.getAllSudGameInfo();

            for (MongoActorData mongoActorData : mongoActorDataList) {
                ActorData actorData = new ActorData();
                mongoActorData.copyTo(actorData);
                if (1 == actorData.getValid() && AccountConstant.DELETED != actorData.getAccountStatus()) {
                    String sRoomId = RoomUtils.formatRoomId(actorData.getUid());
                    if (!isTestUser && (whiteTestDao.isMemberByType(actorData.getUid(), WhiteTestDao.WHITE_TYPE_RID)
                            || whiteTestDao.isMemberByType(sRoomId, WhiteTestDao.WHITE_TYPE_ROOM_ID))) {
                        // 非测试用户不能搜索测试用户,房间
                        logger.info("normal user can not search test user  key:{} sUid:{}", key, actorData.getUid());
                    } else {
                        vo.getUsers().add(copyActorVO(actorData));
                        MongoRoomData roomData = roomDao.getDataFromCache(sRoomId);
                        if (null != roomData) {
                            SearchVO.RoomVO roomVO = copyRoomVO(roomData);
                            roomVO.setTag(roomData.getTag());
                            roomVO.setTagName(roomTags.getTagNameById(roomData.getTag(), slang));
                            roomVO.setRecreationTag(getRecreationTag(roomData, turntableRoomMap, truthDareRoomMap, sudGameRoomMap));
                            vo.getRooms().add(roomVO);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("searchByName  key:{} e:{}", key, e.getMessage(), e);
        }
    }


    public SearchVO searchByKey(RoomNewDTO req) {
        SearchVO vo = new SearchVO();
        if (StringUtils.isEmpty(req.getKey())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String key = req.getKey().trim();
        if (key.length() >= 30 || HIDE_KEY_SET.contains(key.toLowerCase())) {
            return vo;
        }
        try {

            int intKey = Integer.parseInt(key);
            ActorData actorData = actorDao.getActorByRid(intKey);
            if (null != actorData && 1 == actorData.getValid() && AccountConstant.DELETED != actorData.getAccountStatus()) {
                vo.getUsers().add(copyActorVO(actorData));
                MongoRoomData roomData = roomDao.getDataFromCache(RoomUtils.formatRoomId(actorData.getUid()));
                if (null != roomData) {
                    vo.getRooms().add(copyRoomVO(roomData));
                }
            }

            searchByName(vo, key, true, req.getSlang());

        } catch (NumberFormatException e) {
            searchByName(vo, key, true, req.getSlang());
            // 字符串搜索
        }
        if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(req.getApp_package_name())) {
            for (SearchVO.RoomVO room : vo.getRooms()) {
                if (!StringUtils.isEmpty(room.getCountry())) {
                    for (String cc : HIDE_COUNTRY_SET) {
                        if (room.getCountry().toLowerCase().startsWith(cc)) {
                            room.setCountry("us_United States");
                        }
                    }
                }
            }
        }
        return vo;
    }

    public UploadRoomThemeVO uploadTheme(RoomNewDTO req) {
        if (StringUtils.isEmpty(req.getRoom_them()) || StringUtils.isEmpty(req.getUid()) ||
                StringUtils.isEmpty(req.getRoomId()) || req.getRoomId().length() < 2) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = getDataFromCache(req.getRoomId());
        RoomRoleData roleData = memberDao.getRoleDataFromCache(req.getRoomId(), req.getUid());
        if (RoomRoleType.HOST != roleData.getRole() && 1 != roleData.getViceHost()) {
            logger.info("upload theme not room owner or vice owner. roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.NOT_HAVE_PERMISSION);
        }
        if (!STAFF_ROOM_SET.contains(req.getUid())) {
            if (!checkUploadBgVipLevel(req.getUid(), req.getRoomId())) {
                logger.info("Only VIP4 and above can upload room theme. roomId={} uid={}", req.getRoomId(), req.getUid());
                throw new CommonException(RoomHttpCode.UPLOAD_ROOM_THEME_LIMIT);
            }
        }
        int conquerLevel = conquerRedis.getRoomConquerLevel(req.getRoomId());
        if (conquerLevel != 0) {
            logger.error("The room has been conquered, unable to change the room background temporarily. uid={} roomId={} level={}", req.getUid(), req.getRoomId(), conquerLevel);
            throw new CommonException(RoomHttpCode.ROOM_HAS_BEEN_CONQUERED);
        }
        if (AppVersionUtils.versionCheck(836, req) && uploadBackgroundDao.selectCount(RoomUtils.getRoomHostId(req.getRoomId())) >= MAX_UPLOAD_NUM) {
            logger.info("Upload no more than 10 photos. uid={}", req.getUid());
            throw new CommonException(RoomHttpCode.UPLOAD_BG_NO_MORE_THAN_10_PHOTOS);
        }
        String roomTheme;
        if (req.getRoom_them().startsWith("http")) {
            roomTheme = CDNUtils.getHttpCdnUrl(req.getRoom_them());
        } else {
            roomTheme = CDNUtils.getCdnUrl(req.getRoom_them());
        }
        UploadBackgroundData bgData = new UploadBackgroundData(RoomUtils.getRoomHostId(req.getRoomId()), roomTheme, roomTheme);
        uploadBackgroundDao.insert(bgData);

        int micThemeId = roomData.getMic_theme();
        RoomMicThemeData micThemeData = roomMicThemeDao.selectMicThemeById(micThemeId);
        if (micThemeData.getChangeTheme() > 0) {
            roomDao.updateRoomTheme(req.getRoomId(), bgData.getId(), 0);
            // 房间背景变更
            sendRoomThemeChangeMsg(roomTheme, roomData);
        }
        return new UploadRoomThemeVO(bgData.getId(), bgData.getBgUrl(), bgData.getmIcon(), bgData.getcIcon(), bgData.getPreview());
    }

    private void sendRoomThemeChangeMsg(String themeUrl, MongoRoomData roomData) {
        RoomInfoChangeMsg msg = new RoomInfoChangeMsg();
        msg.setRid(roomData.getRid());
        msg.setThemeUrl(themeUrl);
        msg.setRoomName(roomData.getName());
        msg.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
        roomWebSender.sendRoomWebMsg(roomData.getRid(), "", msg, true);
        logger.info("room_back_url--sendRoomInfoChangeMsg 2168 roomData roomId:{} RoomInfoChangeMsg:{}", roomData.getRid(), msg);
    }

    /**
     * 上传背景需要vip4以上(如果房主是vip4、5、6、女王,无需判断副房主是否有VIP)
     */
    private boolean checkUploadBgVipLevel(String uid, String roomId) {
        if (vipInfoDao.getIntVipLevel(uid) >= 4) {
            return true;
        }
        if (!RoomUtils.isHomeowner(uid, roomId)) {
            // 如果房主是vip4、5、6、女王,无需判断副房主是否有VIP
            return vipInfoDao.getIntVipLevel(RoomUtils.getRoomHostId(roomId)) >= 4;
        }
        return false;
    }

    /**
     * 传主题时提示用户要vip4以上
     */
    public void checkUploadTheme(RoomNewDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId) || roomId.length() < 2) {
            logger.info("check upload theme param error. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (!isOwnerOrViceOwner(uid, roomId)) {
            logger.info("only room owner or vice owner can operate this. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.NOT_HAVE_PERMISSION);
        }
        if (STAFF_ROOM_SET.contains(uid)) {
            return;
        }
        if (!checkUploadBgVipLevel(uid, roomId)) {
            logger.info("Only VIP4 or above can upload room theme. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.TYCOON4_OR_ABOVE_CAN_UPLOAD);
        }
        if (AppVersionUtils.versionCheck(836, req) && uploadBackgroundDao.selectCount(RoomUtils.getRoomHostId(req.getRoomId())) >= MAX_UPLOAD_NUM) {
            logger.info("Upload no more than 10 photos. uid={}", req.getUid());
            throw new CommonException(RoomHttpCode.UPLOAD_BG_NO_MORE_THAN_10_PHOTOS);
        }
    }

    /**
     * 房间内banner图标按钮
     */
    public RoomBannerVO getRoomBanner(RoomDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        RoomBannerVO vo = new RoomBannerVO();
        String uid = req.getUid();
        String roomId = req.getRoomId();
        String ownerId = RoomUtils.getRoomHostId(roomId);
        JSONObject switchConfig = commonConfig.getSwitchConfig();

        // 填充banner
        fillRoomBannerConfig(req, vo, actorData);
        vo.setWeek_gift_url(getWeekGiftUrl(uid, ownerId, req.getSlang(), req.getOs(), req.getVersioncode(), req.getLang()));
        vo.setSuper_bag_url("");
        ConquerActivity conquerActivity = conquerActivityDao.findOne();
        int curTime = DateHelper.getNowSeconds();
        if (conquerActivity != null) {
            int startTime = conquerActivity.getStartTime() != null ? conquerActivity.getStartTime() : 0;
            int endTime = conquerActivity.getEndTime() != null ? conquerActivity.getEndTime() : 0;
            String acNameEn = conquerActivity.getAcNameEn() != null ? conquerActivity.getAcNameEn() : "";
            if (startTime <= curTime && curTime < endTime) {
                vo.setConquer(1);
                if (acNameEn.startsWith("test") && !CONQUER_ROOMID.contains(roomId)) {
                    vo.setConquer(0);
                }
            }
        }

        // 开关配置列表
        // ludo 游戏开关
        vo.setLudo(switchConfig.getIntValue(BannerSwitchConstant.LUDO_SWITCH));
        // UMO游戏开关
        vo.setUmo_switch(switchConfig.getIntValue(BannerSwitchConstant.UMO_SWITCH));
        // 消消乐开关
        vo.setMonster_crush_switch(switchConfig.getIntValue(BannerSwitchConstant.MONSTER_CRUSH_SWITCH));
        // 多米诺开关
        vo.setDomino_switch(switchConfig.getIntValue(BannerSwitchConstant.DOMINO_SWITCH));
        vo.setCarrom_pool_switch(switchConfig.getIntValue(BannerSwitchConstant.CARROM_POOL_SWITCH));
        // 幸运转盘开关
        vo.setLucky_wheel_switch(switchConfig.getIntValue(BannerSwitchConstant.LUCKY_WHEEL_SWITCH));
        // 真心话开关
        vo.setSmart_wheel(switchConfig.getIntValue(BannerSwitchConstant.SMART_WHEEL_SWITCH));
        // 房间投票开关
        vo.setRoom_vote(switchConfig.getIntValue(BannerSwitchConstant.ROOM_VOTE_SWITCH));
        // 消费奖励开关
        vo.setConsume_reward(switchConfig.getInteger("consume_reward"));
        vo.setHas_consume_reward(getHasConsumeReward(uid));
        // karaoke开关
        vo.setKaraoke(switchConfig.getIntValue("karaoke"));
        // 话题开关
        vo.setTopic_switch(switchConfig.getIntValue("topic_switch"));
        // 视频开关
        vo.setVideo_switch(switchConfig.getIntValue(BannerSwitchConstant.VIDEO_SWITCH));
        // 变声开关
        vo.setVoice_changer(switchConfig.getIntValue("voice_changer"));
        // 音效开关
        vo.setSound_effect(switchConfig.getIntValue("sound_effect"));
        vo.setRoom_theme_switch(getRoomThemeSwitch(uid, roomId, switchConfig));
        // 不是用户的vip等级， 是需要多少等级才能开启该video
        vo.setVideo_level(switchConfig.getIntValue("video_vip_level"));
        // 水果机游戏开关
        vo.setFruit_switch(switchConfig.getIntValue("fruit_switch"));
        // 碰碰游戏开关
        vo.setBumper_switch(switchConfig.getIntValue("bumper_switch"));
        // 骰子游戏开关
        vo.setDice2_option(switchConfig.getIntValue(BannerSwitchConstant.DICE_SWITCH));
        // 幸运数字开关
        vo.setLuckyNumFunction(switchConfig.getIntValue(BannerSwitchConstant.LUCKY_NUM_SWITCH) > 0 ? 2 : 0);
        fillLuckyNumConfig(vo, uid, roomId);
        // 重复消息开关
        vo.setRepeat_msg_switch(switchConfig.getIntValue(BannerSwitchConstant.REPEAT_MSG_SWITCH));
        // 幸运骰子开关
        vo.setLucky_dice_switch(switchConfig.getIntValue(BannerSwitchConstant.LUCKY_DICE_SWITCH));
        // 召集广播开关
        vo.setRoom_gathering_switch(switchConfig.getIntValue(BannerSwitchConstant.ROOM_GATHERING_SWITCH));
        // PK开关
        vo.setPk_switch(switchConfig.getIntValue(BannerSwitchConstant.PK_SWITCH));
        // 幸运红包开关
        vo.setLucky_box_switch(switchConfig.getIntValue(BannerSwitchConstant.LUCKY_BOX_SWITCH));
        // 猜拳开关： 空字符串为关
        vo.setGuess_icon(switchConfig.getIntValue(BannerSwitchConstant.GUESS_SWITCH) > 0 ? "https://cdn3.qmovies.tv/test/square_2020_03_10.png" : "");
        // 迁移状态
        vo.setPk(pkGameDao.findData(roomId) != null ? 1 : 0);
        vo.setRoom_pk(roomPkGameDao.findDataByRoomId(roomId) != null ? 1 : 0);
        vo.setTurntable(turntableGameRedis.getGameInfoByRoomId(roomId) != null ? 1 : 0);
        vo.setTruth_dare(truthOrDareRedis.getGameInfoByRoomId(roomId) != null ? 1 : 0);
        int gameStatus = getGameStatus(roomId);
        vo.setGame_status(gameStatus);
        vo.setLudo_status(isPlayingLudo(roomId) ? 1 : 0);
        vo.setBumper(gameStatus == 1 ? 1 : 0);
        vo.setFinger_guess(fingerGuessRedis.getAllGuessNum() > 0 ? 1 : 0);
        vo.setGetResType(goodsListHomeRedis.getResTypeFromRedis(uid));


        boolean isV2Start = sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_V2_KEY) == 1;
        vo.setRocket_switch_v2(isV2Start ? 1 : 0);
//        vo.setRocket_switch(isV2Start?0:1);
        vo.setRocket_switch(sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_KEY));
        vo.setLucky_box(checkLuckyBox(roomId));  // 进房间是否检查红包
        vo.setHasRoomEvent(roomEventDao.getHasOneNotEndRoomEvent(curTime) == null ? 0 : 1);
        vo.setRoomEventNum(roomEventDao.getNotEndRoomEventNum(roomId, curTime));
        vo.setHasTaskReward(getHasTaskReward(req));
        int chargeSwitch = commonConfig.getSwitchConfigValue(CommonConfig.FIRST_CHARGE_SWITCH, 0);
        if (chargeSwitch == 1 && AppVersionUtils.versionCheck(843, req)) {
            int showTime = commonConfig.getSwitchConfigValue(CommonConfig.FIRST_CHARGE_SHOW_TIME, 60);
            vo.setFirstChargeShowTime((null == userRechargeRecordDao.getFirstChargeData(uid, actorData.getTn_id())) ? showTime : 0);
        } else {
            vo.setFirstChargeShowTime(0);
        }
        vo.setRocket_switch_v2_light(sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG,
                SysConfigDao.ROOM_ROCKET_SWITCH_V2_LIGHT_KEY));
        return vo;
    }

    private void fillRoomBannerConfig(RoomDTO req, RoomBannerVO vo, ActorData actorData) {
        List<RoomBannerData> bannerList = roomBannerDao.getAllValidBannerList();
        if (CollectionUtils.isEmpty(bannerList)) {
            return;
        }

        String roomId = req.getRoomId();
        boolean gameRoomFlag = RoomUtils.isGameRoom(roomId);
        String ownerId = RoomUtils.getRoomHostId(roomId);
        List<RoomBannerVO.BannerConfig> list = new ArrayList<>();
        List<RoomBannerVO.BannerConfig> sidebarBannerList = new ArrayList<>(); // 房间侧边栏banner
        List<RoomBannerVO.BannerConfig> gameInteractList = new ArrayList<>(); // 游戏中心互动游戏区
        List<RoomBannerVO.BannerConfig> gameOtherList = new ArrayList<>(); // 游戏中心其他游戏区
        List<RoomBannerVO.BannerConfig> gameRoomWindowList = new ArrayList<>();  // 游戏房切换banner
        List<RoomBannerVO.BannerConfig> gameRoomSubList = new ArrayList<>();  // 游戏房底部h5切换banner

        int currentTime = DateHelper.getNowSeconds();
        int bcGameSwitch = homeBannerService.getBCGameSwitch(actorData);
        vo.setBcGameSwitch(bcGameSwitch);
        for (RoomBannerData data : bannerList) {
            int startTime = data.getStartTime();
            int endTime = data.getEndTime();
            if (startTime > 0 && endTime > 0 && (currentTime < startTime || currentTime > endTime)) {
                if (currentTime > endTime) {
                    Update update = new Update();
                    update.set("valid", 0);
                    roomBannerDao.updateData(data.get_id().toString(), update);
                }
                continue;
            }
            String url = data.getUrl() != null ? data.getUrl() : "";
            if (!isValidUrl(url, req)) {
                continue;
            }

            // 过滤条件判断
            if (!checkFilterValid(actorData, data, bcGameSwitch)) {
                continue;
            }

            if (url.startsWith("https://docs.google.com/forms")) {
                String rid = "";
                if (!StringUtils.isEmpty(req.getUid())) {
                    rid = String.valueOf(actorData.getRid());
                }
                url = url.replace("require_rid", rid);
            }

            RoomBannerVO.BannerConfig bannerConfig = new RoomBannerVO.BannerConfig();
            bannerConfig.setWebType(data.getWeb_type());
            bannerConfig.setType(data.getType());
            bannerConfig.setTitle(req.getSlang() == SLangType.ENGLISH ? data.getTitle() : data.getTitle_ar());
            bannerConfig.setEvent(data.getEvent());
            bannerConfig.setGame_type(data.getGame_type());
            bannerConfig.setIcon(req.getSlang() == SLangType.ENGLISH ? data.getIcon() : data.getIcon_ar());
            bannerConfig.setRoomIcon(req.getSlang() == SLangType.ENGLISH ? data.getRoom_icon() : data.getRoom_icon_ar());
            bannerConfig.setUrl(getBannerUrl(url, req.getUid(), ownerId, req.getSlang(), req.getOs(), req.getVersioncode(), req.getToken(), roomId, data.getGame_type()));
            bannerConfig.setBannerType(data.getBannerType());
            bannerConfig.setBannerId(data.get_id().toString());
            bannerConfig.setIsNew(DateHelper.getNowSeconds() - data.get_id().getTimestamp() < TimeUnit.DAYS.toSeconds(3) ? 1 : 0);

            switch (data.getShow_sidebar()) {
                case RoomBannerDao.ZONE_ROOM_WINDOWS:
                    list.add(bannerConfig);
                    break;
                case RoomBannerDao.ZONE_ROOM_SIDE:
                    sidebarBannerList.add(bannerConfig);
                    break;
                case RoomBannerDao.ZONE_ROOM_GAME_INTERACT:
                    gameInteractList.add(bannerConfig);
                    break;
                case RoomBannerDao.ZONE_ROOM_GAME_OTHER:
                    gameOtherList.add(bannerConfig);
                    break;
                case RoomBannerDao.ZONE_GAME_ROOM_SUB:
                    gameRoomSubList.add(bannerConfig);
                    break;
                case RoomBannerDao.ZONE_GAME_ROOM_WINDOWS:
                    gameRoomWindowList.add(bannerConfig);
                    break;
            }
        }
        if (gameRoomFlag) {
            vo.setList(gameRoomWindowList);
            RoomBannerVO.GameRoomInfo gameRoomInfo = new RoomBannerVO.GameRoomInfo();
            // int regDay = ActorUtils.getRegDays(req.getUid());
            // if (regDay > 7 && gameRoomRedis.getShowBcGameEntry(req.getUid())) {
            //     gameRoomInfo.setBcBannerList(gameRoomSubList);
            // }
            if (bcGameSwitch > 0) {
                gameRoomInfo.setBcBannerList(gameRoomSubList);
            }
            vo.setGameRoomInfo(gameRoomInfo);
        } else {
            vo.setList(list);
            vo.setSidebarBannerList(sidebarBannerList);
            vo.setGameInteractList(gameInteractList);
            vo.setGameOtherList(gameOtherList);
        }
    }

    private boolean checkFilterValid(ActorData actorData, RoomBannerData data, int bcGameSwitch) {

        try {
            switch (data.getFilterType()) {
                case HomeBannerService.FILTER_TYPE_SEX:
                    if (!StringUtils.isEmpty(data.getFilterItem()) && !data.getFilterItem().equals(String.valueOf(actorData.getFb_gender()))) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_PAY:
                    double userRecharge = actorPayExternalDao.getUserRechargeMoney(actorData.getUid()).doubleValue();
                    String[] split = data.getFilterItem().split("-");
                    double minMoney = Double.parseDouble(split[0]);
                    double maxMoney = Double.parseDouble(split[1]);
                    if (userRecharge < minMoney || userRecharge > maxMoney) {
                        return false;
                    }
                    break;

                case HomeBannerService.FILTER_TYPE_REGISTER:
                    String[] splitDay = data.getFilterItem().split("-");
                    int startDay = Integer.parseInt(splitDay[0]);
                    int endDay = Integer.parseInt(splitDay[1]);
                    int regDay = ActorUtils.getRegDays(actorData.getUid());
                    if (regDay < startDay || regDay > endDay) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_USER:
                    String[] uidArray = data.getFilterItem().split(",");
                    List<String> uidList = Arrays.asList(uidArray);
                    if (!uidList.contains(actorData.getUid())) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_LOSS:
                    if (backUserStateRedis.isReg(actorData.getUid()) ||
                            backUserStateRedis.isBackUser(actorData, true) == 0) {
                        //  不是回归用户
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_BC_GAME:
                    int filterStatus = Integer.parseInt(data.getFilterItem().trim());
                    if ((filterStatus == 0 && bcGameSwitch > 0) || (filterStatus == 1 && bcGameSwitch <= 0)) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_COUNTRY:
                    if (!StringUtils.isEmpty(data.getFilterItem()) && !StringUtils.isEmpty(actorData.getCountry())) {
                        String[] countryArray = data.getFilterItem().split(",");
                        List<String> countryList = Arrays.asList(countryArray);
                        String countryCode = ActorUtils.getUpperCaseCountryCode(actorData.getCountry());
                        if (!StringUtils.isEmpty(countryCode) && !countryList.contains(countryCode)) {
                            return false;
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            logger.error("checkBannerValid error:{}", e.getMessage(), e);
        }
        return true;
    }

    /**
     * 获取完成未领取任务数量
     */
    private int getHasTaskReward(RoomDTO dto) {
        String uid = dto.getUid();
        boolean ver8621 = AppVersionUtils.versionCheck(8621, dto);
        int hasRewardCount = ver8621 ? userTaskRedis.getWebUserHasRewardCount(uid) : userTaskRedis.getUserHasRewardCount(uid);
        if (hasRewardCount > 0) {
            return 1;
        }
        boolean isNewRegister = ActorUtils.isNewRegisterActor(uid, 7);
        hasRewardCount = isNewRegister ? newcomerTaskDao.selectHasRewardCount(uid, ver8621 ? 2 : 1) : 0;
        return hasRewardCount > 0 ? 1 : 0;
    }

    /**
     * 检查房间是否有红包
     */
    private int checkLuckyBox(String roomId) {
        try {
            Long luckyBoxNum = clusterRedis.opsForHash().size("hash:luckyBoxInRoom_" + roomId);
            if (luckyBoxNum != 0) {
                return 1;
            }
            Long giftBoxNum = clusterRedis.opsForHash().size("hash:giftBoxInfo_" + roomId);
            if (giftBoxNum != 0) {
                return 1;
            }
        } catch (Exception e) {
            logger.error("checkLuckyBox error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    private void fillLuckyNumConfig(RoomBannerVO vo, String uid, String roomId) {
        RoomConfigData roomConfig = roomConfigDao.findData(roomId);

        Map<String, Object> config = roomConfig.getRoom_config();
        int luckyNumAdmin = (int) config.getOrDefault("luckyNumAdmin", 0);

        RoomBannerVO.LuckyNumConfig luckyNumConfig = new RoomBannerVO.LuckyNumConfig();

        luckyNumConfig.setLuckyNumCost((int) config.getOrDefault("luckyNumCost", 0));
        luckyNumConfig.setLuckyNumRange((int) config.getOrDefault("luckyNumRange", 9));
        luckyNumConfig.setLuckyNumSwitch((int) config.getOrDefault("luckyNumSwitch", 0));
        luckyNumConfig.setLuckyNumData((int) config.getOrDefault("luckyNumData", 0));
        luckyNumConfig.setLuckyNumAdmin(luckyNumAdmin);

        int hostOrAdmin = hostOrAdminByChoice(uid, roomId);
        if ((hostOrAdmin == 1) || (luckyNumAdmin == 1 && hostOrAdminByChoice(uid, roomId) > 0)) {
            luckyNumConfig.setEnableSetting(1);
        } else {
            luckyNumConfig.setEnableSetting(0);
        }

        if (LUCKY_NUM_ROOM.equals(roomId)) {
            logger.info("roomId: {}, luckyNumCost:{}, luckyNumRange:{}, luckyNumSwitch:{}, luckyNumData:{}", roomId, luckyNumConfig.getLuckyNumCost(), luckyNumConfig.getLuckyNumRange(), luckyNumConfig.getLuckyNumSwitch(), luckyNumConfig.getLuckyNumData());
        }

        vo.setLuckyNumV2Config(luckyNumConfig);
    }


    /**
     * 0: 普通用户 1: 【房主】、 2: 管理员
     */
    private int hostOrAdminByChoice(String uid, String roomId) {
        int hostOrAdmin;
        hostOrAdmin = RoomUtils.isHomeowner(uid, roomId) ? 1 : 0;
        if (hostOrAdmin == 0) {
            hostOrAdmin = roomAdminRedis.isRoomAdmin(roomId, uid) ? 2 : 0;
        }
        return hostOrAdmin;
    }


    private String TruncateUrlPage(String url) {
        String strAllParam = null;
        url = url.trim();
        String[] arrSplit = url.split("[?]");
        if (url.length() > 1) {
            if (arrSplit.length > 1) {
                for (int i = 1; i < arrSplit.length; i++) {
                    strAllParam = arrSplit[i];
                }
            }
        }
        return strAllParam;
    }

    private Map<String, String> urlSplit(String url) {
        Map<String, String> mapRequest = new HashMap<>();
        String strUrlParam = TruncateUrlPage(url);
        if (strUrlParam == null) {
            return mapRequest;
        }
        String[] arrSplit = strUrlParam.split("[&]");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = strSplit.split("[=]");
            if (arrSplitEqual.length > 1) {
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);
            } else {
                if (!"".equals(arrSplitEqual[0])) {
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }

    /**
     * 旧版ludo
     */
    private boolean isPlayingLudo(String roomId) {
        String key = "room_ludo_" + roomId;
        return !StringUtils.isEmpty(clusterRedis.opsForValue().get(key));
    }

    public String getGameId(String roomId) {
        // 值为gameId-gameType
        String gameInfo = clusterRedis.opsForValue().get("str:room_sud_game_" + roomId);
        if (StringUtils.isEmpty(gameInfo)) {
            return null;
        }
        String[] split = gameInfo.split("-");
        if (split.length == 2) {
            return split[0];
        }
        return null;
    }

    private int getGameStatus(String roomId) {
        // 值为gameId-gameType
        String gameInfo = clusterRedis.opsForValue().get("str:room_sud_game_" + roomId);
        if (StringUtils.isEmpty(gameInfo)) {
            return 0;
        }
        String[] split = gameInfo.split("-");
        if (split.length == 2) {
            return Integer.parseInt(split[1]);
        }
        return 0;
    }

    private int getRoomThemeSwitch(String uid, String roomId, JSONObject switchConfig) {
        // 判断是否房主或房间管理员
        boolean isHostOrAdmin = RoomUtils.isHomeowner(uid, roomId);
        if (!isHostOrAdmin) {
            isHostOrAdmin = roomAdminRedis.isRoomAdmin(roomId, uid);
        }
        String strValue = clusterRedis.opsForValue().get("str:room_new_theme_key");
        int roomThemeSwitch = 1;
        if (!StringUtils.isEmpty(strValue)) {
            try {
                roomThemeSwitch = Integer.parseInt(strValue);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        if (roomThemeSwitch == 0) {
            roomThemeSwitch = 1;
        }
        return isHostOrAdmin && switchConfig.getIntValue("room_theme") != 0 ? roomThemeSwitch : 0;
    }

    private int getHasConsumeReward(String uid) {
        String key = "daily_consume_has:" + DateHelper.ARABIAN.formatDateInDay() + ":" + uid;
        String strValue = clusterRedis.opsForValue().get(key);
        if (!StringUtils.isEmpty(strValue)) {
            try {
                return Integer.parseInt(strValue);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return 0;
    }

    private Boolean isValidUrl(String url, RoomDTO req) {
        int os = req.getOs();
        int versioncode = req.getVersioncode();
        String packageName = req.getApp_package_name();
        int slang = req.getSlang();
        String roomId = req.getRoomId();
        Map<String, String> paramMap = urlSplit(url);
        // logger.info("isValidUrl url: {}, paramMap:{}, packageName:{}, versioncode:{}", url, JSONObject.toJSONString(paramMap), packageName, versioncode);

        if (!CollectionUtils.isEmpty(paramMap)) {
            if (paramMap.containsKey("android_hidden")) {
                int androidHidden = strToInt(paramMap.get("android_hidden"));
                if (PKGConstant.ANDROID_YOUSTAR.equals(packageName) && versioncode == androidHidden) {
                    return false;
                }
            }
            if (paramMap.containsKey("android_pro_hidden")) {
                int androidHidden = strToInt(paramMap.get("android_pro_hidden"));
                if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(packageName) && versioncode == androidHidden) {
                    return false;
                }
            }

            if (paramMap.containsKey("ios_hidden")) {
                int iosHidden = strToInt(paramMap.get("ios_hidden"));
                if (os == ClientOS.IOS && versioncode == iosHidden) {
                    return false;
                }
            }

            if (paramMap.containsKey("android_limit")) {
                int androidLimit = strToInt(paramMap.get("android_limit"));
                if (PKGConstant.ANDROID_YOUSTAR.equals(packageName) && versioncode < androidLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("ios_limit")) {
                int iosLimit = strToInt(paramMap.get("ios_limit"));
                if (os == ClientOS.IOS && versioncode < iosLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("pro_limit")) {
                int proLimit = strToInt(paramMap.get("pro_limit"));
                if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(packageName) && versioncode < proLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("android_up_limit")) {
                int androidLimit = strToInt(paramMap.get("android_up_limit"));
                if (PKGConstant.ANDROID_YOUSTAR.equals(packageName) && versioncode >= androidLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("ios_up_limit")) {
                int iosLimit = strToInt(paramMap.get("ios_up_limit"));
                if (os == ClientOS.IOS && versioncode >= iosLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("pro_up_limit")) {
                int proLimit = strToInt(paramMap.get("pro_up_limit"));
                if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(packageName) && versioncode >= proLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("roomLimit") && !Objects.equals(paramMap.get("roomLimit"), roomId)) {
                return false;
            }
            if (paramMap.containsKey("startTime") && paramMap.containsKey("endTime")) {
                int nowTime = DateHelper.getNowSeconds();
                int startTime = strToInt(paramMap.get("startTime"));
                int endTime = strToInt(paramMap.get("endTime"));
                if (nowTime < startTime || nowTime > endTime) {
                    return false;
                }
            }
            if (paramMap.containsKey("showEnv")) {
                int showEnv = strToInt(paramMap.get("showEnv"));
                if (slang != showEnv) {
                    return false;
                }
            }
        }
        return true;
    }

    private int strToInt(String strValue) {
        if (StringUtils.isEmpty(strValue)) {
            return 0;
        }
        try {
            return Integer.parseInt(strValue);
        } catch (Exception e) {
            logger.error("string to integer error. value={}", strValue);
        }
        return 0;
    }

    private String getBannerUrl(String url, String uid, String ownerId, int slang, int os, int versioncode, String
            token, String roomId, String gameType) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder;
        try {
            urlBuilder = UriComponentsBuilder.fromHttpUrl(url.trim());
        } catch (Exception e) {
            logger.error("getBannerUrl error. url={} {}", url, e.getMessage(), e);
            return "";
        }
        urlBuilder.queryParam("uid", uid);
        urlBuilder.queryParam("aid", ownerId);
        urlBuilder.queryParam("nlang", slang);
        urlBuilder.queryParam("os", os);
        urlBuilder.queryParam("versioncode", versioncode);
        urlBuilder.queryParam("token", token);
        urlBuilder.queryParam("room_id", roomId);
        if (BC_GAME_LIST.contains(gameType) && !url.contains("appId")) {
            urlBuilder.queryParam("appId", ServerConfig.isProduct() ? 1476635168L : 2135757682L);
        }
        return urlBuilder.build(false).encode().toUriString();
    }

    private String getWeekGiftUrl(String uid, String ownerId, int slang, int os, int versioncode, String lang) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl("https://cdn3.qmovies.tv/week_competition");
        urlBuilder.queryParam("uid", uid);
        urlBuilder.queryParam("aid", ownerId);
        urlBuilder.queryParam("nlang", slang);
        urlBuilder.queryParam("os", os);
        urlBuilder.queryParam("versioncode", versioncode);
        urlBuilder.queryParam("lang", lang);
        return urlBuilder.build(false).encode().toUriString();
    }

    /**
     * 表情资源列表
     */
    @Cacheable(value = "getEmojiList", key = "#p0.slang", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public EmojiListVO getEmojiList(RoomDTO req) {
        EmojiListVO vo = new EmojiListVO();
        List<EmojiListVO.EmojiVO> list = new ArrayList<>();
        req.setEmojiType(EmojiConfigDao.EMOJI_TYPE_VOICE_MIC);
        MsgEmojiListVO msgEmojiListVO = getMsgEmojiList(req);
        for (MsgEmojiListVO.EmojiMsgVO emojiMsgVO : msgEmojiListVO.getList()) {
            for (MsgEmojiListVO.EmojiMsgPanel msgPanel : emojiMsgVO.getEmojiPanel()) {
                EmojiListVO.EmojiVO emojiVO = new EmojiListVO.EmojiVO();
                BeanUtils.copyProperties(msgPanel, emojiVO);
                emojiVO.setIcon_file(msgPanel.getIconFile());
                list.add(emojiVO);
            }
        }
        vo.setList(list);
        return vo;
    }

    public MsgEmojiListVO getMsgEmojiList(RoomDTO req) {
        int emojiCategoryType = req.getEmojiType();
        int slang = req.getSlang();
        MsgEmojiListVO vo = new MsgEmojiListVO();
        List<EmojiConfigData> emojiConfigList = emojiConfigDao.findEmojiConfigList(emojiCategoryType);
        List<MsgEmojiListVO.EmojiMsgVO> list = new ArrayList<>();
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        int vipLevel = vipInfoDao.getIntVipLevel(req.getUid());
        int fb_gender = actorData.getFb_gender();
        for (EmojiConfigData config : emojiConfigList) {
            // vip 权限过滤
            int vipLimit = config.getVipLimit();
            if (vipLimit > 0 && !AppVersionUtils.versionCheck(867, req)) {
                continue;
            }

            // 性别过滤
            int emojiType = config.getEmojiType();
            if (emojiType > 0 && emojiType != fb_gender) {
                continue;
            }

            MsgEmojiListVO.EmojiMsgVO emojiMsgVO = new MsgEmojiListVO.EmojiMsgVO();
            emojiMsgVO.setCategoryIconUrl(config.getCategoryIconUrl());
            emojiMsgVO.setVipLimit(vipLimit);
            emojiMsgVO.setKey(config.get_id().toString());
            emojiMsgVO.setCanSent(1);
            if ((vipLevel <= VipFeatureConstant.VIP_LEVEL_6 && vipLevel < vipLimit) || (vipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN && vipLimit == VipFeatureConstant.VIP_LEVEL_6)) {
                emojiMsgVO.setCanSent(0);
                emojiMsgVO.setTips(slang == SLangType.ARABIC ? String.format(EMOJI_CAN_NOT_SEND_TIPS_AR, VipFeatureConstant.VIP_NAME_MAP_AR.get(vipLimit)) : String.format(EMOJI_CAN_NOT_SEND_TIPS_EN, VipFeatureConstant.VIP_NAME_MAP.get(vipLimit)));
            }

            // 各个表情Tab列表
            List<EmojiResourceData> emojiResourceDataList = emojiResourceDao.findEmojiList(config.get_id().toString());
            List<MsgEmojiListVO.EmojiMsgPanel> panelList = new ArrayList<>();
            List<MsgEmojiListVO.EmojiMsgPanel> textPanelList = new ArrayList<>();
            for (EmojiResourceData resourceData : emojiResourceDataList) {
                MsgEmojiListVO.EmojiMsgPanel emojiMsgPanel = new MsgEmojiListVO.EmojiMsgPanel();
                emojiMsgPanel.setEmojiId(resourceData.get_id().toString());
                emojiMsgPanel.setIcon(resourceData.getIcon());
                emojiMsgPanel.setName(slang == SLangType.ARABIC ? resourceData.getNameAr() : resourceData.getName());
                emojiMsgPanel.setIconFile(resourceData.getIcon_file());
                emojiMsgPanel.setEmojiType(resourceData.getEmojiSubType());
                emojiMsgPanel.setActionType(resourceData.getActionType());
                emojiMsgPanel.setOrder(resourceData.getOrder());
                emojiMsgPanel.setCycles(resourceData.getCycles());
                // 表情类型填充
                if (StringUtils.hasText(resourceData.getIcon_file())) {
                    String iconFile = resourceData.getIcon_file().toLowerCase();
                    if (iconFile.endsWith("webp")) {
                        emojiMsgPanel.setDynamicType(0);
                    } else if (iconFile.endsWith("svga")) {
                        emojiMsgPanel.setDynamicType(1);
                    } else if (iconFile.endsWith("mp4")) {
                        emojiMsgPanel.setDynamicType(2);
                    } else if (iconFile.endsWith("png")) {
                        emojiMsgPanel.setDynamicType(3);
                    }
                }
                // 这个表情点击调用骰子游戏
                if (emojiMsgPanel.getEmojiId().equals("66cbf7de0398e612e7d65f31") && !AppVersionUtils.versionCheck(859, req)) {
                    continue;
                }
                int emojiSubType = resourceData.getEmojiSubType();
                if (emojiSubType == 1) {
                    textPanelList.add(emojiMsgPanel);
                } else {
                    panelList.add(emojiMsgPanel);
                }
            }

            if (CollectionUtils.isEmpty(panelList) && CollectionUtils.isEmpty(textPanelList)) {
                continue;
            }
            emojiMsgVO.setEmojiPanel(panelList);
            emojiMsgVO.setEmojiTextPanel(textPanelList);
            list.add(emojiMsgVO);
        }
        vo.setList(list);

        // 如果是私信快捷互动表情，则返回单独的列表
        if (emojiCategoryType == EmojiConfigDao.EMOJI_TYPE_PRIVATE_ACTION) {
            vo.setMsgActionEmojiList(CollectionUtils.isEmpty(list) ? Collections.emptyList() : list.stream().flatMap(item -> item.getEmojiPanel().stream()).collect(Collectors.toList()));
            vo.setList(null);
        }
        return vo;
    }


    /**
     * 房间内发送图片
     */
    public RoomSendPicVO sendPicture(RoomSendPicDTO req) {
        RoomSendPicVO vo = new RoomSendPicVO();
        String uid = req.getUid();
        String roomId = req.getRoom_id();
        String url = ImageUrlGenerator.createCdnUrl(req.getImg());
        ImageUrlData data = new ImageUrlData();
        data.setUrl(url);
        data.setWidth(200);
        data.setHeight(300);
        data.setFit("lfit");
        data.setGifToImg(false);
        String thumbnail = ImageUrlGenerator.generateUrl(data);
        data.setGifToImg(true);
        String thumbnailStaticUrl = ImageUrlGenerator.generateUrl(data);
        asyncSendPic(url, uid, roomId, thumbnail, thumbnailStaticUrl, req.getWidth(), req.getHeight());
        vo.setUrl(url);
        vo.setThumbnailUrl(thumbnail);
        vo.setThumbnailStaticUrl(thumbnailStaticUrl);
//        logger.info("uid={} vo={}", uid, vo);
        return vo;
    }

    private void asyncSendPic(String url, String uid, String roomId, String thumbnail, String thumbnail2, int width,
                              int height) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                try {
                    boolean isSafe = detectService.detectImage(new ImageDTO(url, DetectOriginConstant.SCREEN_PICTURE, uid)).getData().getIsSafe() == 1;
                    if (isSafe) {
                        sendPictureInRoom(url, uid, roomId, thumbnail, thumbnail2, width, height);
                    } else {
                        creditRisk(uid);
                    }
                } catch (Exception e) {
                    sendPictureInRoom(url, uid, roomId, thumbnail, thumbnail2, width, height);
                }
            }
        });
    }

    private void creditRisk(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData != null) {
            creditRiskService.creditRisk(uid, actorData.getTn_id(), actorData.getIp(), CreditRiskService.TYPE_SCREEN_IMG, 200, 20, 10);
        }
    }

    /**
     * 在房间内发送图片
     */
    private void sendPictureInRoom(String url, String uid, String roomId, String thumbnail, String thumbnail2,
                                   int width, int height) {
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            return;
        }
        SendPicPushMsg msg = new SendPicPushMsg();
        UNameObject uNameObject = generateUNameObject(uid, roomId);
        msg.setName(uNameObject);
        msg.setUrl(url);
        msg.setHeight(height);
        msg.setWidth(width);
        msg.setThumbnailUrl(thumbnail);
        msg.setThumbnailStaticUrl(thumbnail2);
        msg.setImageType(0);
        String suffix = url.substring(url.lastIndexOf(".") + 1);
        if ("gif".equals(suffix) || "webp".equals(suffix)) {
            msg.setImageType(1);
        }
        roomWebSender.sendRoomWebMsg(roomId, uid, msg, false);
        mqService.sendFanoutUserLevelMq(new CommonData(uid, MqItemConstant.SEND_ROOM_MSG, null, 0));

        // 房间消息事件埋点
        RoomMsgNewEvent event = new RoomMsgNewEvent();
        int userCount = roomPlayerRedis.getRoomActorsCount(roomId);
        ActorData userData = actorDao.getActorDataFromCache(uid);
        event.setUid(uid);
        event.setRoom_id(roomId);
        event.setMsg_type(3);
        event.setSend_user_count(userCount);
        event.setRoom_user_count(userCount);
        event.setMsg_id(String.valueOf(msg.getMsgId()));
        event.setFrom_os(userData.getIntOs());
        event.setFrom_uid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 构建UNameObject
     */
    private UNameObject generateUNameObject(String uid, String roomId) {
        RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
        if (detailData == null) {
            logger.error("can not find room actor data. roomId={} uid={}", roomId, uid);
            return null;
        }
        UNameObject object = new UNameObject();
        object.setRid(detailData.getRid());
        object.setUid(uid);
        object.setName(detailData.getName());
        object.setHead(detailData.getHead());
        object.setRole(detailData.getNewRole());
        // 设置vip
        object.setVip(detailData.getVipLevel());
        object.setVipMedal(detailData.getVipMedal());
        // 设置徽章
        List<String> badgeSmallIconList = detailData.getBadgeList();
        object.setBadgeList(badgeSmallIconList);
        object.setLevel(detailData.getLevel());
        // 设置气泡
        object.setBid(detailData.getBubbleId());
        object.setIdentify(detailData.getIdentify());
        object.setIsNewUser(detailData.getIsNewUser());
        return object;
    }

    /**
     * 房主端欢迎语
     */
    public WelcomeWordVO sendWelcomeWord(RoomDTO req) {
        WelcomeWordVO vo = new WelcomeWordVO();
        int welcomeSwitch = actorConfigDao.getIntRoomConfig(RoomUtils.getRoomHostId(req.getRoomId()),
                ActorConfigDao.WELCOME_ROOM_MSG_SWITCH, 1);
        if (welcomeSwitch == 0) {
            vo.setWelcome_word("");
        } else {
            if (RoomUtils.isHomeowner(req.getUid(), req.getRoomId())) {
                vo.setWelcome_word("");
                return vo;
            }
            // 获取欢迎语
            List<String> hostWelcomeWords = roomWelcomeMsgService.getAllWelcomeMsgCache(req.getRoomId());
            Collections.shuffle(hostWelcomeWords);
            vo.setWelcome_word(hostWelcomeWords.get(0));
        }
        return vo;


        // List<String> hostWelcomeWords = req.getSlang() == 2 ? roomConfig.getHostWelcomeWordsAr() : roomConfig.getHostWelcomeWordsEn();
        // if (!CollectionUtils.isEmpty(hostWelcomeWords)) {
        //     Collections.shuffle(hostWelcomeWords);
        //     vo.setWelcome_word(hostWelcomeWords.get(0));
        // } else {
        //     vo.setWelcome_word("");
        // }
        // return vo;
    }

    /**
     * 房间内禁言
     */
    public void banMsg(BanMsgDTO req) {
        if (req.getComp() != 0) {
            // show room 直接返回
            return;
        }
        String uid = req.getUid();
        String aid = req.getAid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(aid) || StringUtils.isEmpty(roomId)) {
            logger.info("cancel ban msg param error. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        int uType = memberDao.findMemberUtype(roomId, uid);
        int aType = memberDao.findMemberUtype(roomId, aid);
        if (!RoomUtils.isHomeowner(uid, roomId) && aType >= uType) {
            logger.info("You can not forbid this user talk. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.NOT_FORBID_USER_TALK);
        }
        if (micFrameRedis.getForbidTime(roomId, aid) > 0) {
            logger.info("Already forbid this user to talk. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.ALREADY_FORBID_USER_TALK);
        }

        int vipLevel = vipInfoDao.getIntVipLevel(aid);
        if (vipLevel >= 4) {
            logger.info("You can not forbid VIP4 and above user talk. uid={} aid={} roomId={} vipLevel={}", uid, aid, roomId, vipLevel);
            throw new CommonException(RoomHttpCode.VIP_CAN_NOT_BAN_MSG);
        }

        micFrameRedis.setForbidTime(roomId, aid);
        sendOptBanTxtPushMsg(uid, aid, roomId, true);
    }

    /**
     * 房间内取消禁言
     */
    public void cancelBanMsg(BanMsgDTO req) {
        if (req.getComp() != 0) {
            // show room 直接返回
            return;
        }
        String uid = req.getUid();
        String aid = req.getAid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(aid) || StringUtils.isEmpty(roomId)) {
            logger.error("cancel ban msg param error. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        int uType = memberDao.findMemberUtype(roomId, uid);
        int aType = memberDao.findMemberUtype(roomId, aid);
        boolean isOwner = RoomUtils.isHomeowner(uid, roomId);
        if (!isOwner) {
            if (uType == 0) {
                logger.info("Only Owner or Admin  can cancel ban on text. uid={} aid={} roomId={}", uid, aid, roomId);
                throw new CommonException(RoomHttpCode.NO_PERMISSION_CANCEL_BAN_TEXT);
            }
            if (aType >= uType) {
                logger.info("You can not cancel forbid this user talk. uid={} aid={} roomId={}", uid, aid, roomId);
                throw new CommonException(RoomHttpCode.NOT_CANCEL_FORBID_USER_TALK);
            }
        }
        micFrameRedis.removeForbidTime(roomId, aid);
        sendOptBanTxtPushMsg(uid, aid, roomId, false);
    }

    /**
     * 发送禁言操作消息
     */
    private void sendOptBanTxtPushMsg(String uid, String aid, String roomId, boolean ban) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                OptBanTxtPushMsg msg = new OptBanTxtPushMsg();
                UserInfoObject userInfo = buildUserInfoObject(uid, roomId);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("from_name", userInfo.getName());
                jsonObject.put("from_img", userInfo.getHead());
                jsonObject.put("opt_user", userInfo);
                msg.fillFrom(jsonObject, ban);
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
                // 房间操作记录
                roomActionLogDao.saveRoomActionLog(roomId, uid, aid, ban ? 5 : 6);
            }
        });
    }

    /**
     * 麦位静音，弃用，新版使用mute_mic接口
     */
    @Deprecated
    public void roomMicOpt(BanMsgDTO req) {
        if (req.getComp() != 0) {
            // show room 直接返回
            return;
        }
        String uid = req.getUid();
        String aid = req.getAid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(aid) || StringUtils.isEmpty(roomId)) {
            logger.error("cancel ban msg param error. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        int uType = memberDao.findMemberUtype(roomId, uid);
        int aType = memberDao.findMemberUtype(roomId, aid);
        if (!RoomUtils.isHomeowner(uid, roomId) && aType >= uType) {
            logger.info("You can not forbid this user talk. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.NOT_FORBID_USER_TALK);
        }
        RoomMicListData roomMicList = roomMicService.getRoomMicList(roomId);
        for (RoomMicData roomMicData : roomMicList.getList()) {
            if (aid.equals(roomMicData.getUid())) {
                roomMicService.muteRoomMic(roomMicData, req.getOpt() == 1);
            }
        }
        sendMuteMicPushMsg(uid, aid, roomId, req.getOpt());
    }

    /**
     * 发送麦位操作消息
     */
    private void sendMuteMicPushMsg(String uid, String aid, String roomId, int opt) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                MuteMicPushMsg msg = new MuteMicPushMsg();
                UserInfoObject userInfo = buildUserInfoObject(uid, roomId);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("from_name", userInfo.getName());
                jsonObject.put("from_img", userInfo.getHead());
                jsonObject.put("opt_user", userInfo);
                jsonObject.put("opt", opt);
                jsonObject.put("to_uid", aid);
                msg.fillFrom(jsonObject);
                logger.info("MuteMicPushMsg={}", JSONObject.toJSONString(msg));
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
            }
        });
    }

    /**
     * 构建UserInfoObject
     */
    private UserInfoObject buildUserInfoObject(String uid, String roomId) {
        UserInfoObject userInfo = new UserInfoObject();
        RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
        userInfo.setUid(uid);
        if (detailData != null) {
            userInfo.setName(detailData.getName());
            userInfo.setHead(detailData.getHead());
            userInfo.setViceHost(detailData.getViceHost());
        }
        return userInfo;
    }

    /**
     * 所有麦位静音时房主调用
     */
    public void allMicDown(BanMsgDTO req) {
        if (!isOwnerOrViceOwner(req.getUid(), req.getRoomId())) {
            logger.info("only room owner or vice owner can forbid all mic. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.NO_PERMISSION_FORBID_ALL_MIC);
        }
        if (req.getOpt() == 1) {
            roomMicRedis.setAllMicDown(req.getRoomId());
        } else {
            roomMicRedis.deleteAllMicDown(req.getRoomId());
        }
    }

    /**
     * 判断是否是房主或副房主
     */
    private Boolean isOwnerOrViceOwner(String uid, String roomId) {
        if (RoomUtils.isHomeowner(uid, roomId)) {
            return true;
        }
        if (!roomAdminRedis.isRoomAdmin(roomId, uid)) {
            return false;
        }
        return memberDao.isViceHostManager(uid, roomId);
    }

    /**
     * 管理员或者房主操作是否能开启pk竞赛
     */
    public void operatePk(RoomPkDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(req)) {
            logger.error("operate room pk param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (!RoomUtils.isHomeowner(uid, roomId) && !roomAdminRedis.isRoomAdmin(roomId, uid)) {
            logger.info("only room owner or admin can opeate this. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.NO_PERMISSION_OPERATE_PK);
        }
        MongoRoomData roomData = roomDao.findData(roomId);
        if (roomData == null) {
            logger.error("room data not exist. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        roomData.setRoom_pk(req.getRoom_pk());
        roomDao.save(roomData);
        sendOptPkPushMsg(uid, roomId, req.getRoom_pk());
    }

    /**
     * 发送操作创建pk游戏权限消息
     */
    private void sendOptPkPushMsg(String uid, String roomId, int roomPk) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                OptPkPushMsg msg = new OptPkPushMsg();
                msg.setOpt(roomPk);
                msg.setName(generateUNameObject(uid, roomId));
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
            }
        });
    }

    /**
     * 幸运数字启动
     */
    public DiceGameVO startLuckyNumber(RoomDTO req) {
        DiceGameVO vo = new DiceGameVO();
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            logger.error("start lucky number param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("actor data not find. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        JSONObject switchConfig = commonConfig.getSwitchConfig();
        int function = switchConfig.getIntValue("lucky_num_v1_function");
        if (function == 2 && !AppVersionUtils.versionCheck(838, req)) {
            logger.info("Please upgrade to the latest version to use it. uid={} versioncode={}", uid, req.getVersioncode());
            throw new CommonException(RoomHttpCode.PLEASE_UPGRADE_VERSION);
        }
        Random random = new Random();
        int randomInt = random.nextInt(100);
        EnterRoomCheckData data = roomRedis.getEnterRoomValue(uid);
        if (RoomUtils.isHomeowner(uid, roomId) || (data != null && roomId.equals(data.getRoomId()))) {
            sendLuckyNumberPushMsg(uid, roomId, randomInt, false, String.valueOf(randomInt));
        } else {
            logger.info("is_really_enter_room false not broadcast uid={} roomId={}", uid, roomId);
        }
        vo.setRand_int(randomInt);
        vo.setBeans(actorData.getBeans());
        return vo;
    }

    /**
     * 骰子游戏
     */
    public DiceGameVO startDiceGame(RoomDTO req) {
        DiceGameVO vo = new DiceGameVO();
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            logger.error("start dice game param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("actor data not find. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        Random random = new Random();
        int randomInt = random.nextInt(6) + 1;
        EnterRoomCheckData data = roomRedis.getEnterRoomValue(uid);
        if (RoomUtils.isHomeowner(uid, roomId) || (data != null && roomId.equals(data.getRoomId()))) {
            sendDicePushMsg(uid, roomId, randomInt);
        } else {
            logger.info("is_really_enter_room false not broadcast uid={} roomId={}", uid, roomId);
        }
        vo.setRand_int(randomInt);
        vo.setBeans(actorData.getBeans());
        return vo;
    }

    private void sendDicePushMsg(String uid, String roomId, int num) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                DicePushMsg msg = new DicePushMsg();
                msg.setUname(generateUNameObject(uid, roomId));
                msg.setNum(num);
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
            }
        });
    }

    private void sendLuckyNumberPushMsg(String uid, String roomId, int num, boolean match, String formatLuckyNum) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                LuckyNumberPushMsg msg = new LuckyNumberPushMsg();
                msg.setUname(generateUNameObject(uid, roomId));
                msg.setNum(num);
                msg.setRet(match ? 1 : 0);
                msg.setLuckyNum(formatLuckyNum);
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
            }
        });
    }

    /**
     * 幸运数字V2版本
     */

    public LuckyNumConfigVO luckyNumConfig(RoomDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            logger.error("luckyNumSetting param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        MongoRoomData mongoRoomData = roomDao.findData(roomId);
        if (mongoRoomData == null) {
            logger.error("mongoRoomData data not find. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        LuckyNumConfigVO vo = new LuckyNumConfigVO();
        vo.setCostNumList(LUCKY_NUM_COST_LIST);
        vo.setRangeNumList(LUCKY_NUM_RANGE_LIST);

        RoomConfigData roomConfig = roomConfigDao.findData(roomId);

        if (null == roomConfig) {
            roomConfig = roomConfigDao.initRoomConfigData(roomId);
        }

        Map<String, Object> config = roomConfig.getRoom_config();
        vo.setLuckyNumCost((int) config.getOrDefault("luckyNumCost", 0));
        vo.setLuckyNumRange((int) config.getOrDefault("luckyNumRange", 9));
        vo.setLuckyNumSwitch((int) config.getOrDefault("luckyNumSwitch", 0));
        vo.setLuckyNumData((int) config.getOrDefault("luckyNumData", 0));
        vo.setLuckyNumAdmin((int) config.getOrDefault("luckyNumAdmin", 0));

        return vo;
    }

    private void userSetLuckyNumberEvent(String uid, String roomId, int luckyNumCost, int luckyNumRange,
                                         int luckyNumSwitch, int luckyNumData, int luckyNumAdmin) {
        LuckyNumberEvent event = new LuckyNumberEvent();
        event.setUid(uid);
        event.setRoom_id(roomId);
        event.setLucky_number_action(3);
        event.setLucky_number_price(luckyNumCost);
        event.setLucky_number_range(luckyNumRange);
        event.setIs_allow_admin(luckyNumAdmin);
        event.setCtime(DateHelper.getNowSeconds());
        if (luckyNumSwitch == 1) {
            event.setLucky_number_set(luckyNumData);
        }

        eventReport.track(new EventDTO(event));
    }


    public void luckyNumSetting(LuckyNumSettingDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        int luckyNumCostReq = req.getLuckyNumCost();
        int luckyNumRangeReq = req.getLuckyNumRange();
        int luckyNumSwitchReq = req.getLuckyNumSwitch();
        int luckyNumDataReq = req.getLuckyNumData();
        int luckyNumAdminReq = req.getLuckyNumAdmin();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            logger.error("luckyNumSetting param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        if (!LUCKY_NUM_COST_LIST.contains(luckyNumCostReq)) {
            logger.error("costNum param error. uid={} roomId={} costNum={}", req.getUid(), req.getRoomId(), luckyNumCostReq);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        if (!LUCKY_NUM_RANGE_LIST.contains(luckyNumRangeReq)) {
            logger.error("rangeNum param error. uid={} roomId={} rangeNum={}", req.getUid(), req.getRoomId(), luckyNumRangeReq);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        if (!LUCKY_NUM_SWITCH.contains(luckyNumSwitchReq) || !LUCKY_NUM_SWITCH.contains(luckyNumAdminReq)) {
            logger.error("luckyNumSwitchReq or luckyNumAdminReq param error. uid={} roomId={} luckyNumSwitchReq={}, " +
                    "luckyNumAdminReq={}", req.getUid(), req.getRoomId(), luckyNumSwitchReq, luckyNumAdminReq);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        if (luckyNumSwitchReq == 1 && (luckyNumDataReq < 0 || luckyNumDataReq > luckyNumRangeReq)) {
            logger.error("luckyNumData param error. uid={} roomId={} luckyNumData={}, rangeNum={}", req.getUid(), req.getRoomId(), luckyNumDataReq, luckyNumRangeReq);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }


        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("actor data not find. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        RoomConfigData roomConfig = roomConfigDao.findData(roomId);
        int hostOrAdmin = hostOrAdminByChoice(uid, roomId);
        if (hostOrAdmin <= 0) {
            logger.info("hostOrAdmin1 limit uid={} roomId={}  hostOrAdmin={}", req.getUid(), req.getRoomId(), hostOrAdmin);
            throw new CommonException(RoomHttpCode.AUTH_ERROR);
        }

        Map<String, Object> config = roomConfig.getRoom_config();
        int luckyNumAdmin = (int) config.getOrDefault("luckyNumAdmin", 0);
        if (hostOrAdmin == 2 && luckyNumAdmin == 0) {
            logger.info("hostOrAdmin2 limit uid={} roomId={}  hostOrAdmin={}", req.getUid(), req.getRoomId(), hostOrAdmin);
            throw new CommonException(RoomHttpCode.AUTH_ERROR);
        }

        boolean pushStatus = false;
        int luckyNumCost = (int) config.getOrDefault("luckyNumCost", 0);
        int luckyNumRange = (int) config.getOrDefault("luckyNumRange", 9);
        int luckyNumSwitch = (int) config.getOrDefault("luckyNumSwitch", 0);
        int luckyNumData = (int) config.getOrDefault("luckyNumData", 0);

        if (luckyNumCost != luckyNumCostReq || luckyNumRangeReq != luckyNumRange ||
                luckyNumSwitch != luckyNumSwitchReq || luckyNumData != luckyNumDataReq ||
                luckyNumAdmin != luckyNumAdminReq) {
            pushStatus = true;
        }


        luckyNumDataReq = luckyNumSwitchReq == 1 ? luckyNumDataReq : 0;
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("luckyNumCost", luckyNumCostReq);
        updateMap.put("luckyNumRange", luckyNumRangeReq);
        updateMap.put("luckyNumSwitch", luckyNumSwitchReq);
        updateMap.put("luckyNumData", luckyNumDataReq);
        updateMap.put("luckyNumAdmin", luckyNumAdminReq);
        roomConfigDao.updateRoomConfigByMap(roomId, updateMap);

        luckyNumRedis.deleteLuckyNumSize(roomId);
        userSetLuckyNumberEvent(uid, roomId, luckyNumCostReq, luckyNumRangeReq, luckyNumSwitchReq, luckyNumDataReq, luckyNumAdminReq);

        if (pushStatus) {
            // 推送设置消息
            LuckyNumRoomChangeMsg changeMsg = new LuckyNumRoomChangeMsg();
            changeMsg.setNum(luckyNumDataReq);
            changeMsg.setLuckyNumCost(luckyNumCostReq);
            changeMsg.setLuckyNumSwitch(luckyNumSwitchReq);
            changeMsg.setLuckyNumAdmin(luckyNumAdminReq);
            changeMsg.setLuckyNumRange(luckyNumRangeReq);
            UserInfoObject userInfoObject = new UserInfoObject();
            userInfoObject.setUid(uid);
            userInfoObject.setName(actorData.getName());
            userInfoObject.setHead(actorData.getHead());
            userInfoObject.setViceHost(hostOrAdmin);
            changeMsg.setOpt_user(userInfoObject);
            roomWebSender.sendRoomWebMsg(roomId, uid, changeMsg, true);
        }

    }

    private void deductLuckyNumDiamonds(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(210);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle("lucky number");
        moneyDetailReq.setDesc("send lucky number");
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);

        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(RoomHttpCode.UNION_NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    private void initLuckyNumSize(String roomId, int luckyNumRange, int luckyNumData) {
        List<Integer> initList = IntStream.rangeClosed(0, luckyNumRange).boxed().collect(Collectors.toList());
        initList.remove(Integer.valueOf(luckyNumData));
        Collections.shuffle(initList);
        List<Integer> pushList;
        if (luckyNumRange == 99) {
            pushList = initList.subList(0, 49);
        } else {
            pushList = initList.subList(0, 79);
        }
        pushList.add(luckyNumData);
        Collections.shuffle(pushList);

        List<String> indexStrList = pushList.stream().map(String::valueOf).collect(Collectors.toList());
        luckyNumRedis.initLuckyNumSize(roomId, indexStrList);
    }

    private int getLuckyNumByRule(String roomId, int luckyNumRange, int luckyNumSwitch, int luckyNumData) {

        int randomInt;
        Random random = new Random();
        if (luckyNumRange <= LUCKY_NUM_RANGE_LIST.get(0) || luckyNumSwitch == 0) {
            randomInt = random.nextInt(luckyNumRange + 1);
        } else {
            if (luckyNumRedis.getLuckyNumSize(roomId) <= 5) {
                initLuckyNumSize(roomId, luckyNumRange, luckyNumData);
            }

            String luckyNum = luckyNumRedis.drawLuckyNumKey(roomId);
            randomInt = Integer.parseInt(luckyNum);
        }

        return randomInt;
    }

    private String getFormatLuckyNum(int num, int luckyNumRange) {
        String STR_FORMAT = "0";
        if (luckyNumRange == LUCKY_NUM_RANGE_LIST.get(1)) {
            STR_FORMAT = "00";
        } else if (luckyNumRange == LUCKY_NUM_RANGE_LIST.get(2)) {
            STR_FORMAT = "000";
        }
        DecimalFormat df = new DecimalFormat(STR_FORMAT);
        return df.format(num);
    }

    private void saveLuckyNumberRecord(String uid, String roomId, int num) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                LuckyNumData luckyNumObj = new LuckyNumData();
                luckyNumObj.setLuckyNum(num);
                luckyNumObj.setRoomId(roomId);
                luckyNumObj.setUid(uid);
                luckyNumObj.setCtime(DateHelper.getNowSeconds());
                luckyNumDao.insert(luckyNumObj);
            }
        });
    }


    public LuckyNumVO sendLuckyNum(RoomDTO req) {
        LuckyNumVO vo = new LuckyNumVO();
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            logger.error("sendLuckyNum param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }


        RoomConfigData roomConfig = roomConfigDao.findData(roomId);
        Map<String, Object> config = roomConfig.getRoom_config();
        int luckyNumCost = (int) config.getOrDefault("luckyNumCost", 0);
        if (luckyNumCost > 0) {
            deductLuckyNumDiamonds(uid, luckyNumCost);
        }

        int luckyNumRange = (int) config.getOrDefault("luckyNumRange", 9);
        int luckyNumSwitch = (int) config.getOrDefault("luckyNumSwitch", 0);
        int luckyNumData = (int) config.getOrDefault("luckyNumData", 0);
        int randomInt = getLuckyNumByRule(roomId, luckyNumRange, luckyNumSwitch, luckyNumData);
        String formatLuckyNum = getFormatLuckyNum(randomInt, luckyNumRange);

        boolean match = luckyNumSwitch == 1 && randomInt == luckyNumData;

        if (LUCKY_NUM_ROOM.equals(roomId)) {
            logger.info("roomId: {}, luckyNumCost:{}, luckyNumRange:{}, luckyNumSwitch:{}, luckyNumData:{}, randomInt:{}", roomId, luckyNumCost, luckyNumRange, luckyNumSwitch, luckyNumData, randomInt);
        }

        EnterRoomCheckData data = roomRedis.getEnterRoomValue(uid);
        if (RoomUtils.isHomeowner(uid, roomId) || (data != null && roomId.equals(data.getRoomId()))) {

            sendLuckyNumberPushMsg(uid, roomId, randomInt, match, formatLuckyNum);
        } else {
            logger.info("is_really_enter_room false not broadcast uid={} roomId={}", uid, roomId);
        }

        if (match) {
            saveLuckyNumberRecord(uid, roomId, randomInt);
        }
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("actor data not find. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        // 发布任务消息
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(req.getUid(), roomId, "", "", CommonMqTaskConstant.SEND_LUCKY_NUMBER, 1));

        vo.setLuckyNum(formatLuckyNum);
        vo.setBeans(actorData.getBeans());
        vo.setMatch(match);
        return vo;
    }


    public LuckyNumRecordVO luckyNumRecord(String roomId, int page) {
        LuckyNumRecordVO recordVO = new LuckyNumRecordVO();
        int page_size = 20;
        if (page < 1) {
            page = 1;
        }
        int currentTime = DateHelper.getNowSeconds();
        int startTime = currentTime - 3 * 24 * 3600;
        List<LuckyNumData> dataList = luckyNumDao.getRecords(roomId, page, page_size, startTime, currentTime);

        List<LuckyNumRecordVO.LuckyNumRecord> recordList = new ArrayList<>();
        for (LuckyNumData data : dataList) {
            LuckyNumRecordVO.LuckyNumRecord luckyNumRecord = new LuckyNumRecordVO.LuckyNumRecord();
            luckyNumRecord.setLuckyNum(data.getLuckyNum());
            luckyNumRecord.setUid(data.getUid());
            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
            luckyNumRecord.setName(actorData.getName());
            luckyNumRecord.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            luckyNumRecord.setCtime(data.getCtime());
            recordList.add(luckyNumRecord);
        }

        recordVO.setList(recordList);
        if (dataList.size() < page_size) {
            recordVO.setNextUrl("");
        } else {
            recordVO.setNextUrl(String.valueOf(page + 1));
        }
        return recordVO;
    }

    /**
     * 设置房间配置开关开关
     */
    public void setActorRoomConfigSwitch(RoomActorConfigDTO req) {
        String uid = req.getUid();
        String configName = req.getConfigName();
        if (ObjectUtils.isEmpty(configName)) {
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        int configValue = req.getConfigValue();
        if (configName.equals(ActorConfigDao.WELCOME_ROOM_MSG_SWITCH)) {
            RoomRoleData roleData = roomMemberDao.getRoleData(req.getRoomId(), req.getUid());
            if (!roleData.isHostOrVice()) {
                logger.info(" uid is not room owner or vice owner");
                throw new CommonException(RoomHttpCode.NOT_ROOM_OWNER_OR_VICE_OWNER);
            }
            actorConfigDao.updateRoomConfig(RoomUtils.getRoomHostId(req.getRoomId()), configName, configValue);
        } else {
            actorConfigDao.updateRoomConfig(uid, configName, configValue);
        }
    }
}
