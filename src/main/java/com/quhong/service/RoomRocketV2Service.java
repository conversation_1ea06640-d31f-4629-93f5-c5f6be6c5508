package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.MatchConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.RocketLevelV2Data;
import com.quhong.data.RocketLevelV2PoolStateData;
import com.quhong.data.dto.RoomRocketDTO;
import com.quhong.data.vo.RocketV2VO;
import com.quhong.data.vo.RoomRocketV2VO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.FloatScreenSourceData;
import com.quhong.mongo.data.JoinSourceData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mongo.data.RocketRewardConfigData;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.redis.RoomRocketV2Redis;
import com.quhong.utils.ArithmeticUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/17
 */
@Service
public class RoomRocketV2Service {

    private static final Logger logger = LoggerFactory.getLogger(RoomRocketV2Service.class);

    private static final List<Integer> BASE_LIST = Arrays.asList(1, 2, 3, 4);
    private static final List<Integer> PRO_LIST = Arrays.asList(5, 6, 7, 8);
    private static final int A_TYPE = 930;
    private static final String TITLE = "Room Rocket Reward";
    private static final String DESC = "Room Rocket Reward";
    private static final int BOM_TIME = 60;

    @Resource
    private ActorDao actorDao;
    @Resource
    private RocketRewardConfigDao rocketRewardConfigDao;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private RoomRocketV2Redis roomRocketV2Redis;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private FloatScreenSourceDao floatScreenSourceDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorCommonService actorCommonService;

    /**
     * 获取房间火箭分页详情信息
     */
    public RoomRocketV2VO getRocketDetailInfo(RoomRocketDTO req) {
        RoomRocketV2VO vo = new RoomRocketV2VO();
        List<RocketV2VO> rocketVOList = new ArrayList<>();
        String roomId = req.getRoomId();
        String nowDay = DateHelper.ARABIAN.formatDateInDay();
        fillRocketLevel(req.getUid(), roomId, vo);
        int rocketLevel = vo.getNowRocketLevel();
        int enterIndex = rocketLevel > 4 ? rocketLevel - 5 : rocketLevel - 1;
        vo.setEnterIndex(enterIndex);

        List<RocketRewardConfigData> rocketRewardConfigDataList = rocketRewardConfigDao.findV2AllCache();
        Map<Integer, RocketRewardConfigData> rocketLevelMap = rocketRewardConfigDataList.stream().collect(Collectors.toMap(RocketRewardConfigData::getRocketLevel, Function.identity()));
        List<Integer> nowList = rocketLevel > 4 ? PRO_LIST : BASE_LIST;
        for (Integer i : nowList) {
            RocketV2VO itemVO = getRocketLvInfo(nowDay, roomId, i, rocketLevel,
                    rocketLevelMap.get(i), vo.getNowEnergyRate(),
                    vo.getNowRocketLevelAdd());
            rocketVOList.add(itemVO);
        }
        vo.setRocketVOList(rocketVOList);
        return vo;
    }

    /**
     * 获取房间火箭奖励,弹窗展示,返回空不要展示
     */
    public RoomRocketV2VO getRocketReward(RoomRocketDTO req) {
        String roomId = req.getRoomId();
        String uid = req.getUid();
        int rocketLevel = req.getRocketLevel();
        String day = DateHelper.ARABIAN.formatDateInDay();
        List<RocketRewardConfigData.ResourceMeta> srcList = roomRocketV2Redis.getRewardList(day, roomId, uid, rocketLevel);
        List<RocketV2VO.ResourceData> rewardList = new ArrayList<>();
//        srcList = drawPool(uid, day, roomId, rocketLevel, srcList);
        if (!CollectionUtils.isEmpty(srcList)) {
            for (RocketRewardConfigData.ResourceMeta item : srcList) {
                RocketV2VO.ResourceData itemVO = new RocketV2VO.ResourceData();
                BeanUtils.copyProperties(item, itemVO);
                if (itemVO.getResourceType() == -3) {
                    itemVO.setResourcePriceType(2);
                } else {
                    itemVO.setResourcePriceType(1);
                }
                rewardList.add(itemVO);
            }
        }
        RoomRocketV2VO vo = new RoomRocketV2VO();
        vo.setRewardList(rewardList);
        return vo;
    }

    private void fillSourceUrl(RocketV2VO.ResourceData itemVO) {
        if (itemVO.getResourceType() == 3) {
            JoinSourceData joinSourceData = joinSourceDao.getSourceData(itemVO.getResourceId());
            if (null != joinSourceData) {
                itemVO.setResourceUrl(joinSourceData.getSource_url());
                itemVO.setResourceMd5(joinSourceData.getSource_md5());
                itemVO.setJoinSourceType(joinSourceData.getSource_type());
            }

        } else if (itemVO.getResourceType() == 8) {
            FloatScreenSourceData floatScreenSourceData = floatScreenSourceDao.getFloatScreenSourceFromCache(itemVO.getResourceId());
            if (null != floatScreenSourceData) {
                itemVO.setResourceUrl(floatScreenSourceData.getScreen_source());
                itemVO.setScreenSourceVap(floatScreenSourceData.getScreenSourceVap());
            }
        }
        if (itemVO.getResourceType() == -3) {
            itemVO.setResourcePriceType(2);
        } else {
            itemVO.setResourcePriceType(1);
        }

    }

    /**
     * 获取房间火箭信息
     */
    public RoomRocketV2VO getRocketInfo(String roomId, String uid) {
        int rocketSwitch = sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_V2_KEY);
        if (rocketSwitch == 0) {
            return null;
        }
//        if (DateHelper.getNowSeconds() < MatchConstant.ROOM_ALLOWANCE_START) {
//            return null;
//        }
        RoomRocketV2VO vo = new RoomRocketV2VO();
        fillRocketLevel(uid, roomId, vo);
        return vo;
    }

    private RoomRocketV2VO fillRocketLevel(String uid, String roomId, RoomRocketV2VO vo) {
        List<RocketRewardConfigData> rocketRewardConfigDataList = rocketRewardConfigDao.findV2AllCache();
        Map<Integer, RocketRewardConfigData> rocketLevelMap = rocketRewardConfigDataList.stream().
                collect(Collectors.toMap(RocketRewardConfigData::getRocketLevel, Function.identity()));
        int maxIndex = 8; // 目前取值为8
        String nowDay = DateHelper.ARABIAN.formatDateInDay();
        RocketLevelV2Data oldRocketLevelV2Data = roomRocketV2Redis.getRocketLevelV2Data(nowDay, roomId);
        int index = oldRocketLevelV2Data.getLevel();
        int level;
        int levelAdd;
        BigDecimal rate;
        if (index < maxIndex) {
            level = index + 1;
            RocketRewardConfigData rewardConfigData = rocketLevelMap.get(level);
            int levelTotal = oldRocketLevelV2Data.getNextLevelTotal() == 0 ? rewardConfigData.getRocketLaunchLimit() : oldRocketLevelV2Data.getNextLevelTotal();
            levelAdd = oldRocketLevelV2Data.getNextLevelAdd();
            rate = ArithmeticUtils.div(levelAdd, levelTotal);
//            logger.info(" roomId:{} level:{} levelAdd:{} levelTotal:{} rate:{} index:{}",
//                    roomId, level, levelAdd, levelTotal, rate, index);

        } else {
            level = maxIndex;
            RocketRewardConfigData rewardConfigData = rocketLevelMap.get(level);
            levelAdd = rewardConfigData.getRocketLaunchLimit();
            rate = new BigDecimal("1.00");
        }
        vo.setNowRocketLevel(level);
        vo.setNowEnergyRate(rate);
        vo.setNowRocketLevelAdd(levelAdd);

        int rocketLevel = 0;
//        if (level >= 2) {
//            rocketLevel = level < 8 ? level - 1 : 8;
//            RocketLevelV2PoolStateData poolStateData = roomRocketV2Redis.getPoolState(nowDay, roomId, rocketLevel);
//            if (poolStateData == null) {
//                // 不可抽奖
//                rocketLevel = 0;
//            } else {
//                int superMaxCount = poolStateData.getSuperMaxCount();
//                int nowCount = poolStateData.getNowCount();
//                Set<String> excludeSet = poolStateData.getExcludeSet();
//                int startTime = poolStateData.getStartTime();
//                if (nowCount >= superMaxCount || excludeSet.contains(uid) || startTime + BOM_TIME < DateHelper.getNowSeconds()) {
//                    // 不可抽奖
//                    rocketLevel = 0;
//                }
//            }
//
//        }
        vo.setRocketLevel(rocketLevel);
        return vo;
    }


    private RocketV2VO getRocketLvInfo(String day, String roomId, int level, int currentLevel,
                                       RocketRewardConfigData configData, BigDecimal nowEnergyRate,
                                       int nowAdd) {
        RocketV2VO vo = new RocketV2VO();
        List<RocketV2VO.RankUser> rankList = new ArrayList<>();
        List<RocketV2VO.ResourceData> superRewardsList = new ArrayList<>();
        List<RocketV2VO.ResourceData> ownerRewardsList = new ArrayList<>();
        int levelTotal = configData.getRocketLaunchLimit();
        if (level < currentLevel) {
            fillRankList(rankList, day, roomId, level);
            vo.setEnergyRate(new BigDecimal("1.00"));
            vo.setRocketLevelNow(levelTotal);
            vo.setRocketLevelLimit(levelTotal);
        } else if (level == currentLevel) {
            fillRankList(rankList, day, roomId, level);
            vo.setEnergyRate(nowEnergyRate);
            vo.setRocketLevelNow(nowAdd);
            vo.setRocketLevelLimit(levelTotal);
        } else {
            vo.setEnergyRate(new BigDecimal("0"));
            vo.setRocketLevelNow(0);
            vo.setRocketLevelLimit(levelTotal);
        }

        int count = 0;
        if (!CollectionUtils.isEmpty(configData.getRoomTop3MetaList())) {
            List<RocketRewardConfigData.ResourceMeta> metaList = new ArrayList<>(configData.getRoomTop3MetaList());
            metaList.sort(Comparator.comparing(RocketRewardConfigData.ResourceMeta::getResourcePrice).reversed());
            for (RocketRewardConfigData.ResourceMeta detail : metaList) {
                if (count > 20) {
                    break;
                }
                RocketV2VO.ResourceData reward = new RocketV2VO.ResourceData();
                BeanUtils.copyProperties(detail, reward);
                fillSourceUrl(reward);
                superRewardsList.add(reward);
                count++;
            }
        }

        if (!CollectionUtils.isEmpty(configData.getSuperMetaList())) {
            List<RocketRewardConfigData.ResourceMeta> superMetaList = new ArrayList<>(configData.getSuperMetaList());
            superMetaList.sort(Comparator.comparing(RocketRewardConfigData.ResourceMeta::getResourcePrice).reversed());
            for (RocketRewardConfigData.ResourceMeta detail : superMetaList) {
                if (count > 20) {
                    break;
                }
                RocketV2VO.ResourceData reward = new RocketV2VO.ResourceData();
                BeanUtils.copyProperties(detail, reward);
                fillSourceUrl(reward);
                superRewardsList.add(reward);
                count++;
            }
        }

        if (!CollectionUtils.isEmpty(configData.getRoomOwnerMetaList())) {
            List<RocketRewardConfigData.ResourceMeta> metaList = new ArrayList<>(configData.getRoomOwnerMetaList());
            metaList.sort(Comparator.comparing(RocketRewardConfigData.ResourceMeta::getResourcePrice).reversed());
            for (RocketRewardConfigData.ResourceMeta detail : metaList) {
                RocketV2VO.ResourceData reward = new RocketV2VO.ResourceData();
                BeanUtils.copyProperties(detail, reward);
                fillSourceUrl(reward);
                ownerRewardsList.add(reward);
            }
        }

        vo.setRocketLevel(level);
        vo.setRankList(rankList);
        vo.setSuperRewardsList(superRewardsList);
        vo.setOwnerRewardsList(ownerRewardsList);
        return vo;
    }

    /**
     * 获取房间火箭榜单
     */
    public RoomRocketV2VO getRocketRoomRank(RoomRocketDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        RoomRocketV2VO vo = new RoomRocketV2VO();
        List<RocketV2VO.RankUser> totalRankList = new ArrayList<>();
        RocketV2VO.RankUser myRank = new RocketV2VO.RankUser();
        String nowDay = DateHelper.ARABIAN.formatDateInDay();
        String key = roomRocketV2Redis.getZSetTotalKey(nowDay, roomId);
        Map<String, Integer> linkedRankMap = roomRocketV2Redis.getCommonRankingMap
                (key, 30);
        if (!CollectionUtils.isEmpty(linkedRankMap)) {
            int rank = 1;
            for (Map.Entry<String, Integer> entry : linkedRankMap.entrySet()) {
                RocketV2VO.RankUser rankingVO = new RocketV2VO.RankUser();
                String aid = entry.getKey();
                ActorData rankActor = actorDao.getActorDataFromCache(aid);
                rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                rankingVO.setName(rankActor.getName());
                rankingVO.setVipLevel(vipInfoDao.getIntVipLevelFromCache(aid));
                rankingVO.setVipMedal(actorCommonService.getCommonVipMedal(aid, rankingVO.getVipLevel()));
                rankingVO.setUid(aid);
                rankingVO.setPoints(entry.getValue());
                rankingVO.setRank(rank);
                if (aid.equals(uid)) {
                    BeanUtils.copyProperties(rankingVO, myRank);
                }
                totalRankList.add(rankingVO);
                rank += 1;
            }
        }

        if (myRank.getRank() == null || myRank.getRank() == 0) {
            ActorData host = actorDao.getActorDataFromCache(uid);
            myRank.setUid(uid);
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(host.getHead()));
            myRank.setVipLevel(vipInfoDao.getIntVipLevelFromCache(uid));
            myRank.setVipMedal(actorCommonService.getCommonVipMedal(uid, myRank.getVipLevel()));
            myRank.setName(host.getName());
            myRank.setPoints(roomRocketV2Redis.getCommonZSetRankingScore(key, uid));
            myRank.setRank(-1);
        }
        vo.setTotalRankList(totalRankList);
        vo.setMyRank(myRank);
        return vo;
    }


    private void fillRankList(List<RocketV2VO.RankUser> rankList, String day, String roomId, int level) {
        Map<String, Integer> linkedRankMap = roomRocketV2Redis.getRocketRankingMap(day, roomId, level, 3);
        if (!CollectionUtils.isEmpty(linkedRankMap)) {
            int rank = 1;
            for (Map.Entry<String, Integer> entry : linkedRankMap.entrySet()) {
                RocketV2VO.RankUser rankingVO = new RocketV2VO.RankUser();
                String aid = entry.getKey();
                ActorData rankActor = actorDao.getActorDataFromCache(aid);
                rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                rankingVO.setName(rankActor.getName());
                rankingVO.setVipLevel(vipInfoDao.getIntVipLevelFromCache(aid));
                rankingVO.setVipMedal(actorCommonService.getCommonVipMedal(aid, rankingVO.getVipLevel()));
                rankingVO.setUid(aid);
                rankingVO.setPoints(entry.getValue());
                rankingVO.setRank(rank);
                rankList.add(rankingVO);
                rank += 1;
            }
        }
    }

    private List<RocketRewardConfigData.ResourceMeta> drawPool(String uid, String day, String roomId,
                                                               int level, List<RocketRewardConfigData.ResourceMeta> templateList) {
//        String lockKey = day + roomId + uid;
        // 普通用户
        RocketLevelV2PoolStateData data = roomRocketV2Redis.getPoolState(day, roomId, level);
        if (data == null) {
            // 不可抽奖
            return null;
        }
        int superMaxCount = data.getSuperMaxCount();
        int nowCount = data.getNowCount();
        int startTime = data.getStartTime();
        Set<String> drawSet = data.getDrawSet();
        Set<String> excludeSet = data.getExcludeSet();
        if (nowCount >= superMaxCount || excludeSet.contains(uid) || startTime + BOM_TIME < DateHelper.getNowSeconds()) {
            // 不可抽奖
            return null;
        }
        // 中了其他奖池的奖
        if (drawSet.contains(uid)) {
            excludeSet.add(uid);
            data.setExcludeSet(excludeSet);
            roomRocketV2Redis.savePoolState(day, roomId, level, data);
            return templateList;
        }

        List<RocketRewardConfigData> rocketRewardConfigDataList = rocketRewardConfigDao.findV2AllCache();
        if (rocketRewardConfigDataList == null) {
            logger.error("not rocket v2 config");
            return null;
        }
        Map<Integer, RocketRewardConfigData> rocketLevelMap = rocketRewardConfigDataList.stream().collect(Collectors.toMap(RocketRewardConfigData::getRocketLevel, Function.identity()));
        RocketRewardConfigData configData = rocketLevelMap.get(level);
        List<RocketRewardConfigData.ResourceMeta> superMetaList = configData.getSuperMetaList();
        Map<String, RocketRewardConfigData.ResourceMeta> superMetaMap = superMetaList.stream().collect(Collectors.toMap(RocketRewardConfigData.ResourceMeta::getMetaId, Function.identity()));

        // 抽奖
        Map<String, Integer> calcMap = this.getCalcMap(superMetaList);
        String drawMetaId = this.getMetaIdByProbability(calcMap);
        if (drawMetaId == null) {
            return null;
        }
        RocketRewardConfigData.ResourceMeta item = superMetaMap.get(drawMetaId);
        data.setNowCount(nowCount + 1);
        addToTemplateList(uid, item, templateList);

//        int poolSize = superMetaList.size();
//        int toIndex = ThreadLocalRandom.current().nextInt(0, poolSize);//0-(total-1)
//        RocketRewardConfigData.ResourceMeta item = superMetaList.get(toIndex);
//        if (item != null) {
//            int rate = (int) (Float.parseFloat(item.getRateNumber()) * 1000);
//            logger.info("item metaId:{} name:{} rateNumber:{} rate:{}", item.getMetaId(), item.getResourceNameEn(),
//                    item.getRateNumber(), rate);
//            if (rate <= 1000) {
//                long random = ThreadLocalRandom.current().nextLong(0, 1000);
//                if (random <= rate) {
//                    data.setNowCount(nowCount + 1);
//                    addToTemplateList(uid, item, templateList);
//                }
//            }
//        }

        excludeSet.add(uid);
        data.setExcludeSet(excludeSet);
        roomRocketV2Redis.savePoolState(day, roomId, level, data);
        logger.info("day:{} roomId:{} level:{}  savePoolState success data:{} ", day, roomId, level, JSONObject.toJSONString(data));
        return templateList;

    }

    private void addToTemplateList(String uid, RocketRewardConfigData.ResourceMeta srcItem,
                                   List<RocketRewardConfigData.ResourceMeta> templateList) {
        RocketRewardConfigData.ResourceMeta toItem = new RocketRewardConfigData.ResourceMeta();
        BeanUtils.copyProperties(srcItem, toItem);
        templateList.add(toItem);
        // 奖励写入redis
//        roomRocketV2Redis.saveRewardList(templateList, day, roomId, aid, level);
        // 发奖励
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        ResourceKeyConfigData.ResourceMeta resourceMeta = new ResourceKeyConfigData.ResourceMeta();
        BeanUtils.copyProperties(toItem, resourceMeta);
        resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, A_TYPE, TITLE,
                DESC, 6);
    }

    private Map<String, Integer> getCalcMap(List<RocketRewardConfigData.ResourceMeta> resourceMetaList) {
        Map<String, Integer> calcMap = new HashMap<>();
        for (RocketRewardConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            try {
                int rate = (int) (Float.parseFloat(resourceMeta.getRateNumber()) * 1000);
                if (rate >= 0) {
                    calcMap.put(resourceMeta.getMetaId(), rate);
                }
            } catch (Exception e) {
                logger.info("resourceMeta:{} error:{}", JSONObject.toJSONString(resourceMeta), e.getMessage(), e);
                return null;
            }
        }
        return calcMap;
    }

    public String getMetaIdByProbability(Map<String, Integer> sourceMap) {
        try {
            if (CollectionUtils.isEmpty(sourceMap)) {
                logger.info("getAwardIdByProbability sourceMap is empty");
                return null;
            }
            int total = 0;
            Map<Integer, String> mapRatio = new HashMap<>();
            List<Integer> ratioList = new ArrayList<>(); // 这样大于等于0，小于1000取第一个（key为1000） 、大于等于1000，小于2000取第二个 （key为2000）
            for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
                String awardId = entry.getKey();
                int value = entry.getValue();
                if (value <= 0) {
                    continue;
                }
                total += value;
                mapRatio.put(total, awardId);
                ratioList.add(total);
            }
            if (total == 0) {
                logger.info("getAwardIdByProbability total is zero sourceMap={} ", sourceMap);
                return null;
            }
            int ratio = ThreadLocalRandom.current().nextInt(0, total);//0-(total-1)
            if (!ratioList.contains(ratio)) {
                ratioList.add(ratio);
                Collections.sort(ratioList);
            }

            int index = ratioList.indexOf(ratio);
            int destNum = ratioList.get(index + 1);
            return mapRatio.get(destNum);
        } catch (Exception e) {
            logger.error("getAwardIdByProbability error sourceMap:{} msg:{}", sourceMap, e.getMessage(), e);
            return null;
        }
    }
}
