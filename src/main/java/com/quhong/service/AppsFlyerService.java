package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.ActorCampaignLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.LoginConstant;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.AdjustSourceCallbackDTO;
import com.quhong.data.dto.AppsflyerSourceCallbackDTO;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.dao.ActorCampaignDao;
import com.quhong.mysql.dao.AppsFlyerCallbackLogDao;
import com.quhong.mysql.data.ActorCampaignData;
import com.quhong.mysql.data.ActorCampaignLogData;
import com.quhong.mysql.data.AppsFlyerCallbackLogData;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/17
 */
@Service
public class AppsFlyerService {

    private static final Logger logger = LoggerFactory.getLogger(AppsFlyerService.class);
    private static final String YOUSTAR_APP_SECRET = "1a989224a726c484a9ff0a862e90c354"; //
    private static final String YOUSTAR_PRO_APP_SECRET = "1f73f6f15e0b583546afbfc4c8e3d437"; //
    public static final String FACEBOOK_DELETE_URL = "https://static.youstar.live/posters/?version=68aedae12399ee55639249f4&posters=0"; //

    @Resource
    private AppsFlyerCallbackLogDao appsFlyerCallbackLogDao;
    @Resource
    private ActorCampaignDao actorCampaignDao;
    @Autowired(required = false)
    private EventReport eventReport;
    @Autowired
    private ActorDao actorDao;
    @Resource
    private AccountService accountService;

    public void saveLog(String dto) {
        AppsflyerSourceCallbackDTO sourceCallbackDTO = JSONObject.parseObject(dto, AppsflyerSourceCallbackDTO.class);
        if (sourceCallbackDTO == null) {
            return;
        }
        AppsFlyerCallbackLogData data = new AppsFlyerCallbackLogData();
        String customData = sourceCallbackDTO.getCustom_data();
        JSONObject jsonObject = JSONObject.parseObject(customData);
        data.setUid(jsonObject != null ? jsonObject.getString("ta_account_id") : "");
        data.setCity(sourceCallbackDTO.getCity());
        data.setCountryCode(sourceCallbackDTO.getCountry_code());
        data.setIdfa(sourceCallbackDTO.getIdfa());
        data.setIdfv(sourceCallbackDTO.getIdfv());
        data.setLanguage(sourceCallbackDTO.getLanguage());
        data.setPlatform(sourceCallbackDTO.getPlatform());
        data.setMediaSource(sourceCallbackDTO.getMedia_source());
        data.setIp(sourceCallbackDTO.getIp());
        data.setAdvertisingId(sourceCallbackDTO.getAdvertising_id());
        data.setOaId(sourceCallbackDTO.getOa_id());
        data.setRegion(sourceCallbackDTO.getRegion());
        data.setGpReferrer(sourceCallbackDTO.getGp_referrer());
        data.setCampaignType(sourceCallbackDTO.getCampaign_type());
        data.setCampaign(sourceCallbackDTO.getCampaign());
        data.setRespBody(sourceCallbackDTO.getResp_body());
        data.setCtime(DateHelper.getNowSeconds());
        data.setInstallTime(convertTimeFormat(sourceCallbackDTO.getInstall_time()));
        data.setAppVersion(sourceCallbackDTO.getApp_version());
        data.setAttributedTouchTime(sourceCallbackDTO.getAttributed_touch_time());
        data.setAppId(sourceCallbackDTO.getApp_id());
        data.setAdSet(sourceCallbackDTO.getAf_adset());
        appsFlyerCallbackLogDao.insert(data);
        saveToActorCampaignLog(data);
    }

    private String convertTimeFormat(String strTime) {
        if (StringUtils.isEmpty(strTime)) {
            return "0";
        }
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
            return String.valueOf(dateFormat.parse(strTime).getTime() / 1000);
        } catch (Exception e) {
            logger.error("convert time format error. strTime = {}", strTime);
            return "0";
        }
    }

    private void saveToActorCampaignLog(AppsFlyerCallbackLogData data) {
        ActorCampaignLogData logData = new ActorCampaignLogData();
        logData.setUid(data.getUid() != null ? data.getUid() : data.getUid());
        logData.setGpAdId(data.getAdvertisingId() != null ? data.getAdvertisingId() : "");
        logData.setIdfa(data.getIdfa() != null ? data.getIdfa() : "");
        logData.setCountryCode(data.getCountryCode() != null ? data.getCountryCode() : "");
        logData.setLanguage(data.getLanguage() != null ? data.getLanguage() : "");
        logData.setAppId(data.getAppId() != null ? data.getAppId() : "");
        logData.setAppVer(data.getAppVersion() != null ? data.getAppVersion() : "");
        logData.setAttributionTime(data.getInstallTime() != null ? data.getInstallTime() : "");
        logData.setCampaign(data.getCampaign() != null ? data.getCampaign() : "");
        logData.setTouchTime(data.getAttributedTouchTime() != null ? data.getAttributedTouchTime() : "");
        logData.setMedium(data.getMediaSource() != null ? data.getMediaSource() : "");
        logData.setSource("");
        logData.setCtime(data.getCtime());
        logData.setAdSet(data.getAdSet() != null ? data.getAdSet() : "");
        if (!StringUtils.isEmpty(logData.getUid())) {
            // 数数上报
            sendEventReport(logData);
        }
        saveToActorCampaign(logData);
    }

    private void sendEventReport(ActorCampaignLogData logData) {
        ActorCampaignLogEvent event = new ActorCampaignLogEvent();
        event.setUid(logData.getUid());
        event.setGp_ad_id(logData.getGpAdId());
        event.setIdfa(logData.getIdfa());
        event.setCountry_code(logData.getCountryCode());
        event.setLanguage(logData.getLanguage());
        event.setApp_id(logData.getAppId());
        event.setApp_ver(logData.getAppVer());
        event.setAttribution_time(logData.getAttributionTime());
        event.setCampaign(logData.getCampaign());
        event.setAd_group_name(logData.getAdGroupName());
        event.setTouch_time(logData.getTouchTime());
        event.setMedium(logData.getMedium());
        event.setSource(logData.getSource());
        event.setCtime(logData.getCtime());
        event.setAdSet(logData.getAdSet());
        event.setData_time(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getLocalDate(logData.getCtime() * 1000L)));
        eventReport.track(new EventDTO(event));
    }

    private void saveToActorCampaign(ActorCampaignLogData logData) {
        ActorCampaignData data = new ActorCampaignData();
        BeanUtils.copyProperties(logData, data);
        ActorCampaignData actorCampaignData = actorCampaignDao.selectOneByUid(logData.getUid());
        if (actorCampaignData == null) {
            data.setId(null);
            actorCampaignDao.insert(data);
        }
//        else {
//            data.setCtime(null);
//            data.setId(actorCampaignData.getId());
//            actorCampaignDao.updateById(data);
//        }
    }

    public void saveFaceBookCampaign(ActorCampaignLogData logData) {
        sendEventReport(logData);
        saveToActorCampaign(logData);
    }

    public void saveAdjustLog(AdjustSourceCallbackDTO dto) {
        // 数数上报
        sendEventReport(dto);
        // 写入数据库
        ActorCampaignLogData logData = new ActorCampaignLogData();
        logData.setUid(dto.getUid());
        logData.setGpAdId(dto.getGps_adid());
        logData.setIdfa(dto.getIdfa());
        logData.setCountryCode(dto.getCountry());
        logData.setLanguage(dto.getLanguage());
        logData.setAppId(dto.getApp_id());
        logData.setAppVer(dto.getApp_version());
        logData.setAttributionTime(dto.getInstalled_at());
        logData.setCampaign(dto.getCampaign_name());
        logData.setTouchTime(dto.getImpression_time());
        logData.setMedium(dto.getNetwork_name());
        logData.setSource(dto.getStore());
        logData.setAdGroupName(dto.getAdgroup_name());
        logData.setCtime(DateHelper.getNowSeconds());
        logData.setAdSet("");
        logData.setFbInstallReferrerAdgroupName(dto.getFb_install_referrer_adgroup_name());
        logData.setMetaInstallReferrerAdgroupName(dto.getMeta_install_referrer_adgroup_name());
        saveToActorCampaign(logData);

    }

    private void sendEventReport(AdjustSourceCallbackDTO dto) {
        ActorCampaignLogEvent event = new ActorCampaignLogEvent();
        event.setUid(dto.getUid());
        event.setCtime(DateHelper.getNowSeconds());
        if (StringUtils.hasLength(dto.getUid())) {
            ActorData actorData = actorDao.getActorData(dto.getUid());
            event.setServer_name(actorData != null ? actorData.getVersion_name() : "");
            event.setServer_version(actorData != null ? actorData.getVersion_name() : "");
            event.setCountry_code(actorData != null ? ActorUtils.getCountryCode(actorData.getCountry()) : "");
        } else {
            event.setUid("install");
        }
        event.setCountry(dto.getCountry());
        event.setCampaign(dto.getCampaign_name());
        event.setAd_group_name(dto.getAdgroup_name());
        event.setGp_ad_id(dto.getGps_adid());
        event.setCity(dto.getCity());
        event.setIdfa(dto.getIdfa());
        event.setLanguage(dto.getLanguage());
        event.setMedium(dto.getNetwork_name());
        event.setApp_ver(dto.getApp_version());
        event.setEventname(dto.getEvent_name());
        event.setApp_id(dto.getApp_id());
        event.setAttribution_time(dto.getInstalled_at());
        event.setTouch_time(dto.getImpression_time());
        event.setSource(dto.getStore());
        event.setData_time(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getToday()));
        event.setFb_install_referrer_adgroup_name(dto.getFb_install_referrer_adgroup_name());
        event.setMeta_install_referrer_adgroup_name(dto.getMeta_install_referrer_adgroup_name());
        eventReport.track(new EventDTO(event));
    }

    public String fbDelete(String signedRequest, boolean isPro) {
        logger.info("fbDelete signedRequest:{} isPro:{}", signedRequest,isPro);
        String[] parts = signedRequest.split("\\.", 2);
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid signed_request format");
        }

        String encodedSig = parts[0];
        String payload = parts[1];

        // 1. 解码 payload
        String dataJson = new String(Base64.getUrlDecoder().decode(payload));
        logger.info("fbDelete dataJson:{}", dataJson);
        JSONObject data = JSONObject.parseObject(dataJson);

        // 2. 校验签名
        if (!verifySignature(encodedSig, payload,isPro)) {
            logger.error("fbDelete verifySignature fail");
            throw new SecurityException("Invalid signed_request signature");
        }

        // 3. 获取用户 ID
        String userId = data.getString("user_id");

        // 4. 删除用户数据（这里示例仅打印，实际要操作数据库）
        logger.info("Deleting user data for userId: " + userId);

        // TODO: 数据库删除/标记删除用户相关数据
        // 5. 生成确认码
        String confirmationCode = String.valueOf(System.currentTimeMillis());
        MongoActorData actorData = actorDao.findActorDataUidFromDB(userId);
        if (actorData != null && actorData.getLoginType() == LoginConstant.FACE_BOOK_TYPE) {
            String uid = actorData.get_id().toString();
            confirmationCode = uid;
            logger.info("fbDelete deleteAccount uid:{}", uid);
            try {
                accountService.deleteAccount(uid);
            } catch (Exception e) {
                logger.error("fbDelete deleteAccount error:{}", e.getMessage());
            }
        }

        // 6. 返回 Facebook 要求的响应
        Map<String, String> response = new HashMap<>();
        response.put("url", FACEBOOK_DELETE_URL + "&id=" + confirmationCode);
        response.put("confirmation_code", confirmationCode);
        return JSON.toJSONString(response);
    }

    private boolean verifySignature(String encodedSig, String payload, boolean isPro) {
        try {
            String APP_SECRET = isPro ? YOUSTAR_PRO_APP_SECRET : YOUSTAR_APP_SECRET;
            byte[] sig = Base64.getUrlDecoder().decode(encodedSig);

            Mac hmac = Mac.getInstance("HmacSHA256");
            hmac.init(new SecretKeySpec(APP_SECRET.getBytes(), "HmacSHA256"));
            byte[] expectedSig = hmac.doFinal(payload.getBytes());

            return Arrays.equals(sig, expectedSig);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}

