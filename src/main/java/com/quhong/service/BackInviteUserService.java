package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.InviteRecordEvent;
import com.quhong.analysis.StarBeatGameLogEvent;
import com.quhong.config.AsyncConfig;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.constant.UserHttpCode;
import com.quhong.constant.UserInfoConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.dto.BackUserDTO;
import com.quhong.data.dto.InviteFissionDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.vo.BackUserInfoVO;
import com.quhong.data.vo.InviteFissionVO;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.InviteFissionDao;
import com.quhong.mysql.data.InviteFissionData;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.redis.DauUserRedis;
import com.quhong.redis.InviteCodeRedis;
import com.quhong.utils.ActorUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 回归用户,邀请裂变用户
 */
@Service
public class BackInviteUserService {

    private static final Logger logger = LoggerFactory.getLogger(BackInviteUserService.class);
    private static final String CHARGE_499 = "499";
    private static final String CHARGE_1999 = "1999";
    private static final String CHARGE_4999 = "4999";
    private static final String DAU_2 = "dau2";
    private static final String DAU_3 = "dau3";
    private static final String DAU_5 = "dau5";
    private static final String DAU_7 = "dau7";

    // 回归活动
    public static List<CoreRewardConfigData> TYPE_BACK_DAU_2_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 20, 1),
            new CoreRewardConfigData(3, "Rose gift", "هدية الورد",
                    "https://cdn3.qmovies.tv/youstar/sign_rose_v826_1.png", 110, 110,
                    BaseDataResourcesConstant.TYPE_BAG_GIFT, 7, 15, 3));

    public static List<CoreRewardConfigData> TYPE_BACK_DAU_3_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 30, 1),
            new CoreRewardConfigData(2, "Rose gift", "هدية الورد",
                    "https://cdn3.qmovies.tv/youstar/sign_rose_v826_1.png", 110, 110,
                    BaseDataResourcesConstant.TYPE_BAG_GIFT, 7, 15, 2));

    public static List<CoreRewardConfigData> TYPE_BACK_DAU_7_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 50, 1),
            new CoreRewardConfigData(2, "Rose gift", "هدية الورد",
                    "https://cdn3.qmovies.tv/youstar/sign_rose_v826_1.png", 110, 110,
                    BaseDataResourcesConstant.TYPE_BAG_GIFT, 7, 15, 2));


    // 裂变活动
    // 邀请者（被邀请者完成填写验证码且有效）
    public static List<CoreRewardConfigData> TYPE_FISSION_INVITER_REG_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 10, 1),
            new CoreRewardConfigData(2, "badge", "badge",
                    "", 2635, 1732,
                    BaseDataResourcesConstant.TYPE_BADGE, 7, 0, 2),
            new CoreRewardConfigData(3, "Mic frame", "اطار مايك",
                    "", 616, 593,
                    BaseDataResourcesConstant.TYPE_MIC, 3, 0, 3));

    // 被邀请者完成填写验证码且有效
    public static List<CoreRewardConfigData> TYPE_FISSION_INVITEE_REG_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 10, 1));

    // 邀请者（被邀请者2日日活）
    public static List<CoreRewardConfigData> TYPE_FISSION_INVITER_DAU_2_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 20, 1));
    // 邀请者（被邀请者3日日活）
    public static List<CoreRewardConfigData> TYPE_FISSION_INVITER_DAU_3_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 30, 1));
    // 邀请者（被邀请者7日日活）
    public static List<CoreRewardConfigData> TYPE_FISSION_INVITER_DAU_7_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 50, 1));
    // 被邀请者2日日活
    public static List<CoreRewardConfigData> TYPE_FISSION_INVITEE_DAU_2_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 20, 1));
    // 被邀请者3日日活
    public static List<CoreRewardConfigData> TYPE_FISSION_INVITEE_DAU_3_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 30, 1),
            new CoreRewardConfigData(3, "Mic frame", "اطار مايك",
                    "", 615, 592,
                    BaseDataResourcesConstant.TYPE_MIC, 7, 0, 3));

    // 被邀请者7日日活
    public static List<CoreRewardConfigData> TYPE_FISSION_INVITEE_DAU_7_LIST = Arrays.asList(
            new CoreRewardConfigData(1, "extra diamond", "extra diamond",
                    "https://cdn3.qmovies.tv/youstar/0316rookie_day2_dm.png", -1, -1,
                    CoreRewardInitData.DIAMOND_TYPE, 0, 50, 1),
            new CoreRewardConfigData(2, "badge", "badge",
                    "", 2636, 1731,
                    BaseDataResourcesConstant.TYPE_BADGE, 15, 0, 2));


    public static final Map<String, List<CoreRewardConfigData>> TYPE_CONFIG_MAP = new HashMap<String, List<CoreRewardConfigData>>() {
        {
            put(CHARGE_499, CoreRewardInitData.TYPE_BACK_REWARD_499_LIST);
            put(CHARGE_1999, CoreRewardInitData.TYPE_BACK_REWARD_1999_LIST);
            put(CHARGE_4999, CoreRewardInitData.TYPE_BACK_REWARD_4999_LIST);
            put(DAU_2, TYPE_BACK_DAU_2_LIST);
            put(DAU_3, TYPE_BACK_DAU_3_LIST);
            put(DAU_7, TYPE_BACK_DAU_7_LIST);
        }
    };

    /**
     * 邀请者奖励
     */
    public static final Map<String, List<CoreRewardConfigData>> TYPE_INVITER_MAP = new HashMap<String, List<CoreRewardConfigData>>() {
        {
            put(DAU_2, TYPE_FISSION_INVITER_DAU_2_LIST);
            put(DAU_3, TYPE_FISSION_INVITER_DAU_3_LIST);
            put(DAU_7, TYPE_FISSION_INVITER_DAU_7_LIST);
        }
    };

    /**
     * 被邀请者奖励
     */
    public static final Map<String, List<CoreRewardConfigData>> TYPE_INVITEE_MAP = new HashMap<String, List<CoreRewardConfigData>>() {
        {
            put(DAU_2, TYPE_FISSION_INVITEE_DAU_2_LIST);
            put(DAU_3, TYPE_FISSION_INVITEE_DAU_3_LIST);
            put(DAU_7, TYPE_FISSION_INVITEE_DAU_7_LIST);
        }
    };


    /**
     * 被邀请者奖励
     */
    public static final Map<String, String> TYPE_INVITE_DIAMOND_TITLE_MAP = new HashMap<String, String>() {
        {
            put("reg-inviter", "Invite-Registration rewards Inviter");
            put("reg-invitee", "Invite-Registration rewards Invitee");
            put("dau2-inviter", "Invite-Inviter Active day2");
            put("dau3-inviter", "Invite-Inviter Active day3");
            put("dau7-inviter", "Invite-Inviter Active day7");
            put("dau2-invitee", "Invite-Invitee Active day2");
            put("dau3-invitee", "Invite-Invitee Active day3");
            put("dau7-invitee", "Invite-Invitee Active day7");
            put("recharge-inviter", "Invite-Recharge Rebate month Inviter");
        }
    };


    private String URL_BACK_URL;

    private Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private ActorDao actorDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private CoreRewardInitData coreRewardInitData;
    @Resource
    private InviteFissionDao inviteFissionDao;
    @Resource
    private DauUserRedis dauUserRedis;
    @Resource
    private InviteCodeRedis inviteCodeRedis;
    @Resource
    private EventReport eventReport;

    @PostConstruct
    public void postInit() {
        URL_BACK_URL = ServerConfig.isProduct() ? "https://static.youstar.live/back_user/" : "https://test2.qmovies.tv/back_user/";
    }

    @Deprecated
    public BackUserInfoVO getBackUserInfo(BackUserDTO req) {
        String uid = req.getUid();
        BackUserInfoVO vo = new BackUserInfoVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);

        }
        BackUserStateData backUserStateData = backUserStateRedis.checkBackUser(actorData, true);
        if (backUserStateData == null) {
            logger.error("not find back user uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_BACK);
        }
        List<BackUserInfoVO.BackUserItemBag> chargeBagList = new ArrayList<>();
        fillItemBag(CHARGE_499, chargeBagList, backUserStateData);
        fillItemBag(CHARGE_1999, chargeBagList, backUserStateData);
        fillItemBag(CHARGE_4999, chargeBagList, backUserStateData);
        List<BackUserInfoVO.BackUserItemBag> dauBagList = new ArrayList<>();
        fillItemBag(DAU_2, dauBagList, backUserStateData);
        fillItemBag(DAU_3, dauBagList, backUserStateData);
        fillItemBag(DAU_7, dauBagList, backUserStateData);

        int regTime = new ObjectId(uid).getTimestamp();
        int lastLoginOutTime = backUserStateData.getLastLoginOutTime();
        int backTime = backUserStateData.getBackTime();
        int isDoneShowPop = backUserStateData.getIsDoneShowPop();
        int now = DateHelper.getNowSeconds();

        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setLossDays((backTime - lastLoginOutTime) / 86400);
        vo.setRegTime(regTime);
        vo.setAliveTime(lastLoginOutTime - regTime);
        vo.setFriendCount(friendsDao.getFriendCount(uid));
        vo.setEndTime(backTime + ResourceConstant.BACK_AC_DAY_TIME);
        vo.setChargeBagList(chargeBagList);
        vo.setDauBagList(dauBagList);
        vo.setIsShowPop(isDoneShowPop == 1 ? 0 : 1);
        if (isDoneShowPop == 0) {
            backUserStateData.setIsDoneShowPop(1);
            backUserStateRedis.saveBackUserState(backUserStateData, backUserStateData.getUid());
        }
        return vo;
    }

    @Deprecated
    public BackUserInfoVO signBackUserDau(BackUserDTO req) {
        String uid = req.getUid();
        String itemTag = req.getItemName();
        if (!StringUtils.hasLength(itemTag) || !StringUtils.hasLength(uid)) {
            logger.error("itemTag:{} or uid:{} is empty", itemTag, uid);
            throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
        }
        BackUserInfoVO vo = new BackUserInfoVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        BackUserStateData backUserStateData = backUserStateRedis.checkBackUser(actorData, true);
        if (backUserStateData == null) {
            logger.error("Not returning to users uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_BACK);
        }
        int now = DateHelper.getNowSeconds();
        if (backUserStateData.getBackTime() + ResourceConstant.BACK_AC_DAY_TIME < now) {
            logger.error("Invalid back activity uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.INVALID_BACK);
        }
        int state = getState(itemTag, backUserStateData);
        if (state == 1) {
            List<CoreRewardConfigData> configDataList = TYPE_CONFIG_MAP.get(itemTag);

            if (!CollectionUtils.isEmpty(configDataList)) {
                executor.execute(() -> {
                    for (CoreRewardConfigData item : configDataList) {
                        handleResources(uid, item, ResourceConstant.BACK_AC_DESC,
                                MoneyTypeConstant.BACK_AC_DIAMOND_TYPE, MoneyTypeConstant.BACK_AC_DIAMOND_TITLE);
                    }
                });
                setState(itemTag, backUserStateData, 2);
                backUserStateRedis.saveBackUserState(backUserStateData, uid);
            }

        } else {
            logger.error("dau reward fail uid:{} itemTag:{} state:{}", uid, itemTag, state);
            throw new CommonH5Exception(UserHttpCode.DAU_REWARD_FAIL);
        }
        return vo;
    }
    @Deprecated
    public InviteFissionVO getInviterInfo(InviteFissionDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        InviteFissionVO vo = new InviteFissionVO();
        HotSearchListData totalData = inviteFissionDao.getMyTotalData(uid);
        logger.info("totalData:{}", totalData);
        if (totalData != null) {
            vo.setTotalPerson(totalData.getTotalP());
            long totalDiamond = totalData.getSearchNumSum() != null ? totalData.getSearchNumSum() : 0;
            vo.setTotalDiamond((int) totalDiamond);
        }
        vo.setInviteCode(inviteCodeRedis.getHostCodeByUid(uid));
        List<InviteFissionVO.RankInviteUserVO> rollList = new ArrayList<>();
        List<InviteFissionData> sourceRollList = inviteFissionDao.getRollList();
        if (!CollectionUtils.isEmpty(sourceRollList)) {
            sourceRollList.forEach(item -> {
                InviteFissionVO.RankInviteUserVO sub = new InviteFissionVO.RankInviteUserVO();
                String subUid = item.getUid();
                ActorData actorData1 = actorDao.getActorDataFromCache(subUid);
                sub.setName(actorData1.getName());
                sub.setNum(item.getTotalReward());
                rollList.add(sub);
            });
        }
        vo.setRollList(rollList);
        return vo;
    }

    @Deprecated
    public InviteFissionVO getInviterDetail(InviteFissionDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        InviteFissionVO vo = new InviteFissionVO();
        List<InviteFissionVO.DetailItemVO> detailList = new ArrayList<>();
        List<InviteFissionData> sourceDetailList = inviteFissionDao.getDetailByUid(uid);

        if (!CollectionUtils.isEmpty(sourceDetailList)) {
            sourceDetailList.forEach(item -> {
                InviteFissionVO.DetailItemVO sub = new InviteFissionVO.DetailItemVO();
                BeanUtils.copyProperties(item, sub);
                String subAid = item.getAid();
                ActorData actorData1 = actorDao.getActorDataFromCache(subAid);
                sub.setInviteeUid(subAid);
                sub.setInviteeName(actorData1.getName());
                sub.setInviteeHead(ImageUrlGenerator.generateRoomUserUrl(actorData1.getHead()));
                detailList.add(sub);
            });
        }
        vo.setDetailList(detailList);
        return vo;
    }

    @Deprecated
    public InviteFissionVO getInviterRank(InviteFissionDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        int startTime = DateHelper.getNowSeconds() - 30 * 86400;
        InviteFissionVO vo = new InviteFissionVO();
        List<InviteFissionVO.RankInviteUserVO> rankList = new ArrayList<>();
        List<HotSearchListData> sourceDetailList = inviteFissionDao.getRankListData(startTime);
        if (!CollectionUtils.isEmpty(sourceDetailList)) {
            int rank = 0;
            for (HotSearchListData item : sourceDetailList) {
                InviteFissionVO.RankInviteUserVO sub = new InviteFissionVO.RankInviteUserVO();
                String subUid = item.getSearchKey();
                ActorData actorData1 = actorDao.getActorDataFromCache(subUid);
                if (actorData1.getValid() == 0) {
                    continue;
                }
                rank += 1;
                sub.setUid(subUid);
                sub.setName(actorData1.getName());
                sub.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData1.getHead()));
                sub.setNum(item.getTotalP());
                sub.setRank(rank);
                rankList.add(sub);
                if (rank >= 30) {
                    break;
                }
            }

        }
        vo.setRankList(rankList);
        return vo;
    }

    @Deprecated
    public InviteFissionVO getInviteeInfo(InviteFissionDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        InviteFissionVO vo = new InviteFissionVO();
        List<InviteFissionVO.InviteeUserItemVO> dauList = new ArrayList<>();
        BackUserStateData inviteUserStateData = checkInviteUser(actorData, false);
        if (inviteUserStateData != null) {
            fillInviteeUserItem(DAU_2, dauList, inviteUserStateData);
            fillInviteeUserItem(DAU_3, dauList, inviteUserStateData);
            fillInviteeUserItem(DAU_7, dauList, inviteUserStateData);
            InviteFissionData inviteFissionData = inviteFissionDao.getOneByAid(uid);
            ActorData actorData2 = actorDao.getActorDataFromCache(inviteFissionData.getUid());
            if (actorData2 != null) {
                vo.setInviterUid(inviteFissionData.getUid());
                vo.setInviterName(actorData2.getName());
                vo.setInviterHead(ImageUrlGenerator.generateRoomUserUrl(actorData2.getHead()));
            }
        }
        vo.setInviteeUserList(dauList);
        vo.setInviteeReward(TYPE_FISSION_INVITEE_REG_LIST.get(0).getResourceNum());


        return vo;
    }

    @Deprecated
    public InviteFissionVO signInviteeUserDau(InviteFissionDTO req) {
        String uid = req.getUid();
        String itemTag = req.getItemName();
        if (!StringUtils.hasLength(itemTag) || !StringUtils.hasLength(uid)) {
            logger.error("itemTag:{} or uid:{} is empty", itemTag, uid);
            throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
        }
        InviteFissionVO vo = new InviteFissionVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        BackUserStateData inviteUserStateData = checkInviteUser(actorData, false);
        if (inviteUserStateData == null) {
            logger.error("Not invitee to users uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
        }
        int now = DateHelper.getNowSeconds();
        int state = getState(itemTag, inviteUserStateData);
        if (state == 1) {
            List<CoreRewardConfigData> configDataList = TYPE_INVITEE_MAP.get(itemTag);
            if (!CollectionUtils.isEmpty(configDataList)) {
                executor.execute(() -> {
                    String keyTitle = TYPE_INVITE_DIAMOND_TITLE_MAP.getOrDefault(itemTag + "-invitee", MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TITLE);
                    for (CoreRewardConfigData item : configDataList) {
                        handleResources(uid, item, ResourceConstant.INVITE_FISSION_AC_DESC,
                                MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE,
                                keyTitle);
                    }
                });
                setState(itemTag, inviteUserStateData, 2);
                backUserStateRedis.saveInvitedUserState(inviteUserStateData, uid);
            }

        } else {
            logger.error("dau reward fail uid:{} itemTag:{} state:{}", uid, itemTag, state);
            throw new CommonH5Exception(UserHttpCode.DAU_REWARD_FAIL);
        }
        return vo;
    }

    @Deprecated
    public InviteFissionVO bindUidByCode(InviteFissionDTO req) {
        String aid = req.getUid();
        String inviteCode = req.getInviteCode();
        if (!StringUtils.hasLength(inviteCode) || !StringUtils.hasLength(aid)) {
            logger.error("inviteCode:{} or aid:{} is empty", inviteCode, aid);
            throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
        }
        boolean isReg = ActorUtils.isNewRegisterActor(aid, UserInfoConstant.INVITE_ALIVE_DAY);
        if (!isReg) {
            logger.error("aid is not reg new user aid:{} ", aid);
            throw new CommonH5Exception(UserHttpCode.REG_NOT_ALLOW);
        }
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData == null || !StringUtils.hasLength(actorData.getTn_id())) {
            logger.error("not find aid:{} or tn_id is empty", aid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        String uid = inviteCodeRedis.getUidByHostCode(inviteCode);
        if (!StringUtils.hasLength(uid)) {
            logger.error("uid not find inviteCode:{} ", inviteCode);
            throw new CommonH5Exception(UserHttpCode.INVITE_CODE_NOT_ALLOW);
        }
        if (uid.equals(aid)) {
            logger.error("uid equals aid uid:{} inviteCode:{} ", uid, inviteCode);
            throw new CommonH5Exception(UserHttpCode.MYSELF_NOT_ALLOW);
        }
        int startTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        int todayCount = inviteFissionDao.getCountByUid(uid, startTime);
        int totalCount = inviteFissionDao.getCountByUid(uid, null);
        if (todayCount >= 5) {
            logger.error("above limit uid:{} aid:{} totalCount:{} todayCount:{}", uid, aid, totalCount, todayCount);
            throw new CommonH5Exception(UserHttpCode.MAX_CODE_DAY_NOT_ALLOW);
        }
        if (totalCount >= 50) {
            logger.error("above limit uid:{} aid:{} totalCount:{} todayCount:{}", uid, aid, totalCount, todayCount);
            throw new CommonH5Exception(UserHttpCode.MAX_CODE_TOTAL_NOT_ALLOW);
        }
        int regReward = TYPE_FISSION_INVITER_REG_LIST.get(0).getResourceNum(); // 奖励邀请者钻石
        InviteFissionVO vo = new InviteFissionVO();
        synchronized (stringPool.intern(getInviteLockKey(aid))) {
            InviteFissionData inviteFissionData = inviteFissionDao.getInviteByAidOrDev(aid, actorData.getTn_id());
//            InviteFissionData inviteFissionData = inviteFissionDao.getOneByAid(aid);
            if (inviteFissionData != null) {
                logger.error("aid or device is use already uid:{} aid:{} tn_id:{}", uid, aid, actorData.getTn_id());
                throw new CommonH5Exception(UserHttpCode.AID_DEV_NOT_ALLOW);
            }
            int now = DateHelper.getNowSeconds();
            InviteFissionData data = new InviteFissionData();
            data.setUid(uid);
            data.setAid(aid);
            data.setDeviceId(actorData.getTn_id());
            data.setRegReward(regReward);
            data.setTotalReward(regReward);
            data.setCtime(now);
            data.setMtime(now);
            try {
                inviteFissionDao.insert(data);
            } catch (Exception e) {
                logger.error("insert error msg:{}", e.getMessage(), e);
                throw new CommonH5Exception(UserHttpCode.AID_DEV_NOT_ALLOW);
            }
            BackUserStateData inviteUserData = checkInviteUser(actorData, true);
            if (inviteUserData != null) {
                executor.execute(() -> {
                    String inviterTitle = TYPE_INVITE_DIAMOND_TITLE_MAP.getOrDefault("reg-inviter", MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TITLE);
                    for (CoreRewardConfigData item : TYPE_FISSION_INVITER_REG_LIST) {
                        handleResources(uid, item, ResourceConstant.INVITE_FISSION_AC_DESC,
                                MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE,
                                inviterTitle);
                    }

                    String inviteeTitle = TYPE_INVITE_DIAMOND_TITLE_MAP.getOrDefault("reg-invitee", MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TITLE);
                    for (CoreRewardConfigData item : TYPE_FISSION_INVITEE_REG_LIST) {
                        handleResources(aid, item, ResourceConstant.INVITE_FISSION_AC_DESC,
                                MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE,
                                inviteeTitle);
                    }
                    doReportEvent(uid, aid);
                });
            }
            logger.info("bind success uid:{} aid:{} inviteUserData:{}", uid, aid, JSON.toJSONString(inviteUserData));
        }
        return vo;
    }


    private void fillInviteeUserItem(String itemName, List<InviteFissionVO.InviteeUserItemVO> dauList, BackUserStateData backUserStateData) {
        InviteFissionVO.InviteeUserItemVO itemBag = new InviteFissionVO.InviteeUserItemVO();
        itemBag.setItemName(itemName);
        itemBag.setItemState(getState(itemName, backUserStateData));
        itemBag.setItemReward(TYPE_INVITEE_MAP.get(itemName).get(0).getResourceNum());
        dauList.add(itemBag);
    }

    private void fillItemBag(String itemName, List<BackUserInfoVO.BackUserItemBag> chargeBagList, BackUserStateData backUserStateData) {
        BackUserInfoVO.BackUserItemBag itemBag = new BackUserInfoVO.BackUserItemBag();
        itemBag.setItemName(itemName);
        itemBag.setItemState(getState(itemName, backUserStateData));
        itemBag.setConfigDataList(TYPE_CONFIG_MAP.get(itemName));
        chargeBagList.add(itemBag);
    }

    private int getState(String itemName, BackUserStateData backUserStateData) {
        if (DAU_2.equals(itemName)) {
            return backUserStateData.getDau2State();
        } else if (DAU_3.equals(itemName)) {
            return backUserStateData.getDau3State();
        } else if (DAU_7.equals(itemName)) {
            return backUserStateData.getDau7State();
        } else {
            return 0;
        }
    }

    private void setState(String itemName, BackUserStateData backUserStateData, int state) {
        if (DAU_2.equals(itemName)) {
            backUserStateData.setDau2State(state);
        } else if (DAU_3.equals(itemName)) {
            backUserStateData.setDau3State(state);
        } else if (DAU_7.equals(itemName)) {
            backUserStateData.setDau7State(state);
        }
    }

//    public boolean isReg(String uid) {
//        return ActorUtils.isNewRegisterActor(uid, UserInfoConstant.BACK_DAY);
//    }
//
//    public int isBackUser(ActorData actorData, boolean isCheckRedis) {
//        int lastLogoutTime = 0;
//        if (actorData.getLastLogin() != null) {
//            if (actorData.getLastLogin().getLogoutTime() != null) {
//                lastLogoutTime = Math.toIntExact(actorData.getLastLogin().getLogoutTime());
//            }
//        }
//        if (lastLogoutTime == 0) {
//            logger.info("uid:{} not find lastLogoutTime", actorData.getUid());
//            return 0;
//        }
//        int now = DateHelper.getNowSeconds();
//        if (lastLogoutTime + UserInfoConstant.BACK_DAY_TIME < now) {
//            return lastLogoutTime;
//        } else {
//            if (isCheckRedis) {
//                BackUserStateData backUserStateData = backUserStateRedis.getBackUserState(actorData.getUid());
//                if (backUserStateData != null && backUserStateData.getBackTime() + ResourceConstant.BACK_AC_DAY_TIME >= now) {
//                    // 回流用户在7天活动期内,展示banner入口
//                    return backUserStateData.getLastLoginOutTime();
//                }
//            }
//            return 0;
//        }
//    }
//
//
//    public BackUserStateData checkBackUser(ActorData actorData) {
//        boolean isReg = isReg(actorData.getUid());
//        if (!isReg) {
//            int lastLogoutTime = isBackUser(actorData, false);
//            BackUserStateData data = backUserStateRedis.getBackUserState(actorData.getUid());
//            if (data == null) {
//                if (lastLogoutTime > 0) {
//                    data = new BackUserStateData();
//                    data.setUid(actorData.getUid());
//                    data.setBackTime(DateHelper.getNowSeconds());
//                    data.setDau2State(0);
//                    data.setDau3State(0);
//                    data.setDau5State(0);
//                    data.setDau7State(0);
//                    Set<String> daySet = new HashSet<>();
//                    String dayStr = DateHelper.ARABIAN.formatDateInDay();
//                    daySet.add(dayStr);
//                    data.setDayActiveSet(daySet);
//                    data.setLastLoginOutTime(lastLogoutTime);
//                    backUserStateRedis.saveBackUserState(data, data.getUid());
//                    // 第一次记录让客户端弹窗
//                    return data;
//                }
//
//            } else {
//                int backTime = data.getBackTime();
//                if (backTime + ResourceConstant.BACK_AC_DAY_TIME >= DateHelper.getNowSeconds()) {
//                    // 在活动期内
//                    String dayStr = DateHelper.ARABIAN.formatDateInDay();
//                    Set<String> daySet = data.getDayActiveSet();
//                    daySet.add(dayStr);
//                    int nowDau = daySet.size();
//                    data.setDau2State(changeBackUserState(data.getDau2State(), nowDau, 2));
//                    data.setDau3State(changeBackUserState(data.getDau3State(), nowDau, 3));
//                    data.setDau5State(changeBackUserState(data.getDau5State(), nowDau, 5));
//                    data.setDau7State(changeBackUserState(data.getDau7State(), nowDau, 7));
//                    data.setDayActiveSet(daySet);
//                    backUserStateRedis.saveBackUserState(data, data.getUid());
//                }
//                return data;
//            }
//        }
//        return null;
//    }
//
//    private int changeBackUserState(int oldState, int nowDau, int destDau) {
//        if (oldState == 0) {
//            if (nowDau >= destDau) {
////                List<DayTimeData> dataList = DateHelper.ARABIAN.getContinuesDays(-2, 0);
//                return 1;
//            }
//            return 0;
//        } else {
//            return oldState;
//        }
//    }

    @Deprecated
    public BackUserStateData checkInviteUser(ActorData actorData, boolean isInsert) {
        boolean isReg = ActorUtils.isNewRegisterActor(actorData.getUid(), UserInfoConstant.INVITE_ALIVE_DAY);
        if (isReg) {
            BackUserStateData data = backUserStateRedis.getInvitedUserState(actorData.getUid());
            if (data == null) {
                if (isInsert) {
                    String uid = actorData.getUid();
                    data = new BackUserStateData();
                    data.setUid(uid);
                    Set<String> daySet = new HashSet<>();
                    String dayStr = DateHelper.ARABIAN.formatDateInDay();
                    daySet.add(dayStr);
                    int regDays = ActorUtils.getRegDays(actorData.getUid()); // 1 为当天注册
                    if (regDays > 1) {
                        List<DayTimeData> dataList = DateHelper.ARABIAN.getContinuesDays(-(regDays - 1), 0);
                        for (DayTimeData item : dataList) {
                            if (dauUserRedis.hasLogByDate(actorData.getUid(), item.getDate())) {
                                daySet.add(item.getDate());
                            }
                        }
                    }
                    int nowDau = daySet.size();
                    data.setDau2State(changeInviteUserState(uid, 0, nowDau, 2, DAU_2));
                    data.setDau3State(changeInviteUserState(uid, 0, nowDau, 3, DAU_3));
                    data.setDau7State(changeInviteUserState(uid, 0, nowDau, 7, DAU_7));
                    data.setDayActiveSet(daySet);
                    backUserStateRedis.saveInvitedUserState(data, data.getUid());
                }
            } else {
                String dayStr = DateHelper.ARABIAN.formatDateInDay();
                Set<String> daySet = data.getDayActiveSet();
                daySet.add(dayStr);
                int nowDau = daySet.size();
                data.setDau2State(changeInviteUserState(data.getUid(), data.getDau2State(), nowDau, 2, DAU_2));
                data.setDau3State(changeInviteUserState(data.getUid(), data.getDau3State(), nowDau, 3, DAU_3));
                data.setDau7State(changeInviteUserState(data.getUid(), data.getDau7State(), nowDau, 7, DAU_7));
                data.setDayActiveSet(daySet);
                backUserStateRedis.saveInvitedUserState(data, data.getUid());
            }
            return data;
        }
        return null;
    }

    private int changeInviteUserState(String aid, int oldState, int nowDau, int destDau, String dauStr) {
//        logger.info("changeInviteUserState aid:{} oldState:{} nowDau:{} destDau:{} dauStr:{}", aid, oldState, nowDau, destDau, dauStr);
        if (oldState == 0) {
            if (nowDau >= destDau) {
                executor.execute(() -> {
                    synchronized (stringPool.intern(getInviteLockKey(aid))) {
                        InviteFissionData inviteFissionData = inviteFissionDao.getOneByAid(aid);
//                        logger.info("json inviteFissionData:{}", JSON.toJSONString(inviteFissionData));
                        if (inviteFissionData != null) {
                            String uid = inviteFissionData.getUid();
                            List<CoreRewardConfigData> configDataList = TYPE_INVITER_MAP.get(dauStr);
                            String keyTitle = TYPE_INVITE_DIAMOND_TITLE_MAP.getOrDefault(dauStr + "-inviter", MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TITLE);
                            for (CoreRewardConfigData item : configDataList) {
                                handleResources(uid, item, ResourceConstant.INVITE_FISSION_AC_DESC,
                                        MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE, keyTitle);
                                if (CoreRewardInitData.DIAMOND_TYPE == item.getResourceType()) {
//                                    logger.info("json changeInviteUserState CoreRewardConfigData:{}", JSON.toJSONString(item));
                                    int beansNum = item.getResourceNum();
                                    if (beansNum > 0) {
                                        inviteFissionData.setAliveDay(destDau);
                                        int aliveDayReward = inviteFissionData.getAliveDayReward() == null ? 0 : inviteFissionData.getAliveDayReward();
                                        inviteFissionData.setAliveDayReward(aliveDayReward + beansNum);
                                        int totalReward = inviteFissionData.getTotalReward() == null ? 0 : inviteFissionData.getTotalReward();
                                        inviteFissionData.setTotalReward(totalReward + beansNum);
                                        inviteFissionData.setMtime(DateHelper.getNowSeconds());
                                        inviteFissionDao.updateById(inviteFissionData);
                                    }
                                }
                            }
                        }
                    }
                });
                return 1;
            }
            return 0;
        } else {
            return oldState;
        }
    }


    public void handleResources(String uid, CoreRewardConfigData vo, String resDesc, int diamondType, String diamondDesc) {
        ResourcesDTO dto = coreRewardInitData.configDataToResDTO(uid, vo, resDesc);
        if (dto != null) {
            mqSenderService.asyncHandleResources(dto);
        } else {
            if (CoreRewardInitData.DIAMOND_TYPE == vo.getResourceType()) {
                int beansNum = vo.getResourceNum();
                if (beansNum > 0) {
                    MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                    moneyDetailReq.setRandomId();
                    moneyDetailReq.setUid(uid);
                    moneyDetailReq.setAtype(diamondType);
                    moneyDetailReq.setChanged(beansNum);
                    moneyDetailReq.setTitle(diamondDesc);
                    moneyDetailReq.setDesc(diamondDesc);
                    ApiResult<String> addBeansResult = dataCenterService.chargeBeans(moneyDetailReq);
                    if (!addBeansResult.isOk()) {
                        logger.info("change beans fail add={} uid={} ", beansNum, uid);
                    }
                }
            } else {
                logger.info("ResourcesDTO is null vo:{}", vo);
            }
        }
    }


    private void doReportEvent(String uid, String aid) {
        InviteRecordEvent event = new InviteRecordEvent();
        event.setUid(uid);
        event.setInvited_uid(aid);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));

        Map<String, Object> properties = new HashMap<>();
        properties.put("invite_uid", uid);
        eventReport.userSet(new EventDTO(aid, "", properties));
    }

    private String getInviteLockKey(String uid) {
        return "invite:lock:key:" + uid;
    }
}
