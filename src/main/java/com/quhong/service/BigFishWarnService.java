package com.quhong.service;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.monitor.MonitorSender;
import com.quhong.monitor.data.CardBO;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.RoomMicRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.RoomMicListVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class BigFishWarnService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(BigFishWarnService.class);
    public static final String ACTIVITY_ID = "6763c7da02c227fa3f5594b1";
    public static final Integer WARN_ON_MIC_USER_COUNT = 3;
    private static final String SHARE_ROOM_LINK = ServerConfig.isProduct()? "https://share.youstar.live/share/shareRoom/" : "https://testv2.qmovies.tv/share/shareRoom/";
    private static final List<String> WARN_ROOM_LIST =ServerConfig.isProduct()? Arrays.asList("r:5c40423966dc630030b5faa3", "r:5ee27069484060c13abb07e8", "r:680b4906af0c056719bdab06", "r:65a198ffdcec4673473acd26",
            "r:68002cd5ddcaf36b7aa9347d", "r:67ad2fd9f9bf341846f4d8ed", "r:620b43b7160f6c2b3214fa4e", "r:62dadbe189c25196ab1b4103", "r:62474fbe872f5d952d52692e", "r:675972b432c25649cf3b919b") :
            Arrays.asList("r:655c4c24b661b86b85455f3b");

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;

    private String getOnMicUserPushZSetKey(String activityId) {
        return String.format("onMicUserPush:%s:%s", activityId, DateHelper.ARABIAN.formatDateInHour());
    }

    public void taskMsgProcess(CommonMqTopicData data) {
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!CommonMqTaskConstant.USER_UP_MIC.equals(item)){
            return;
        }

        if (ObjectUtils.isEmpty(roomId) || !WARN_ROOM_LIST.contains(roomId)){
            return;
        }

        String onMicUserPushZSetKey = getOnMicUserPushZSetKey(ACTIVITY_ID);
        int currentScore = activityCommonRedis.getCommonZSetRankingScore(onMicUserPushZSetKey, roomId);
        if (currentScore >= 1){
            return;
        }

        // 判断麦位是否有创建人
        RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(roomId);
        if (roomMicVO == null || CollectionUtils.isEmpty(roomMicVO.getList())){
            return;
        }
        long roomMicCount = roomMicVO.getList().stream().filter(roomMic -> roomMic.getStatus() == 1).count();
        if (roomMicCount < WARN_ON_MIC_USER_COUNT){
            return;
        }
        activityCommonRedis.incrCommonZSetRankingScoreSimple(onMicUserPushZSetKey, roomId, 1);
        ActorData actorRoomData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId));

        CardBO cardBO = new CardBO();
        // 点击消息跳转
        cardBO.getTemplateCard().getCardAction().setUrl(SHARE_ROOM_LINK + actorRoomData.getStrRid());
        // 大图
        cardBO.getTemplateCard().getMainTitle().setTitle(" ");
        cardBO.getTemplateCard().getCardImage().setUrl(actorRoomData.getHead());
        // 图片后置描述
        List<CardBO.MainTitleBO> verticalContentList = new ArrayList<>();

        // 房主rid信息
        CardBO.MainTitleBO ridInfoBO = new CardBO.MainTitleBO();
        ridInfoBO.setTitle(String.format("房主rid: %s", actorRoomData.getStrRid()));
        verticalContentList.add(ridInfoBO);

        // 房主账号国家信息
        CardBO.MainTitleBO accountCountryBO = new CardBO.MainTitleBO();
        accountCountryBO.setTitle(String.format("房主账号国家: %s\n房主IP国家: %s", ActorUtils.getCountryCode(actorRoomData.getCountry()), ActorUtils.getCountryCode(actorRoomData.getIpCodeCountry())));
        verticalContentList.add(accountCountryBO);

        // 当前房间在房人数信息
        CardBO.MainTitleBO roomPlayerCountBO = new CardBO.MainTitleBO();
        roomPlayerCountBO.setTitle(String.format("在房人数: %s\n在麦上人数: %s", roomPlayerRedis.getRoomActorsCount(roomId), roomMicCount));
        verticalContentList.add(roomPlayerCountBO);

        // 跳转用户所在房间
        cardBO.getTemplateCard().setVerticalContentList(verticalContentList);
        List<CardBO.QuoteAreaBO> jumpList = new ArrayList<>();
        CardBO.QuoteAreaBO jumpRoomData = new CardBO.QuoteAreaBO();
        jumpRoomData.setType(1);
        jumpRoomData.setUrl(SHARE_ROOM_LINK + actorRoomData.getStrRid());
        jumpRoomData.setTitle("跳转当前所在房间");
        jumpList.add(jumpRoomData);
        cardBO.getTemplateCard().setJumpList(jumpList);
        monitorSender.customCard("ustar_big_fish", cardBO);
    }
}
