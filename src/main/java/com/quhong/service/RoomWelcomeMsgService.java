package com.quhong.service;

import com.quhong.analysis.EventReport;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.config.RoomConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.WelcomeMessageReviewConstant;
import com.quhong.enums.RoomHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.RoomRoleData;
import com.quhong.data.dto.WelcomeMsgDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.feign.IDetectService;
import com.quhong.mongo.dao.*;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.*;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.mic.RoomMicService;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Component
public class RoomWelcomeMsgService {

    private static final Logger logger = LoggerFactory.getLogger(RoomWelcomeMsgService.class);
    public static final List<Integer> UN_CHANGE_BACKGROUND_MIC_THEME = Arrays.asList(16, 17, 18, 19);

    public static final String AT_XXX = "@xxx";

    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomMicThemeRedis micThemeRedis;
    @Resource
    private CommonDao commonDao;
    @Resource
    private RoomMicThemeLogDao roomMicThemeLogDao;
    @Resource
    private ConquerRedis conquerRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomThemeSwitchRecordDao roomThemeSwitchRecordDao;
    @Resource
    private UploadBackgroundDao uploadBackgroundDao;
    @Resource
    private ConquerActivityDao conquerActivityDao;
    @Resource
    private RoomLevelService roomLevelService;
    @Resource
    private RoomMicService roomMicService;
    @Autowired
    private RoomActorCache actorCache;
    @Resource
    private WelcomeMessageReviewDao welcomeMessageReviewDao;
    @Resource
    private IDetectService idetectService;
    @Resource
    private RoomConfig roomConfig;

    public PageVO<WelcomeMessageReviewData> selectPageList(WelcomeMsgDTO dto) {
        // 房主或者副房主
        RoomRoleData roleData = roomMemberDao.getRoleData(dto.getRoomId(), dto.getUid());
        if (!roleData.isHostOrVice()) {
            logger.info("commonAddCheck uid is not room owner or vice owner");
            throw new CommonException(RoomHttpCode.NOT_ROOM_OWNER_OR_VICE_OWNER);
        }
        WelcomeMessageReviewData condition = new WelcomeMessageReviewData();
        condition.setRoomId(dto.getRoomId());
        condition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
        long count = welcomeMessageReviewDao.selectCount(condition);
        // 正常最少会有一条记录
        if (count == 0) {
            // 插入默认20条阿语打招呼语言
            List<String> newHostWelcomeWordsAr = roomConfig.getNewHostWelcomeWordsAr();
            List<WelcomeMessageReviewData> insertList = newHostWelcomeWordsAr.stream().map(item -> {
                WelcomeMessageReviewData data = new WelcomeMessageReviewData();
                data.setSubmitterUid(dto.getUid());
                data.setRoomId(dto.getRoomId());
                data.setMessageContent(item);
                data.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
                data.setSubmitTime(DateHelper.getNowSeconds());
                data.setUpdateTime(DateHelper.getNowSeconds());
                return data;
            }).collect(Collectors.toList());
            welcomeMessageReviewDao.batchInsert(dto.getRoomId(), insertList);
        }
        int page = dto.getPage() == null ? 1 : dto.getPage();
        List<Integer> reviewActionList;
        if (dto.getStatusType() == 0) {
            // 通过
            reviewActionList = Arrays.asList(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
        } else {
            // 待审核+拒绝
            reviewActionList = Arrays.asList(WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING, WelcomeMessageReviewConstant.REVIEW_STATUS_REJECTED);
        }
        List<WelcomeMessageReviewData> list = welcomeMessageReviewDao.selectPageListByStatus(dto.getRoomId(),
                reviewActionList, page, 40);
        PageVO<WelcomeMessageReviewData> pageVO = new PageVO<>();
        pageVO.setList(list);
        pageVO.setNextUrl("");

        return pageVO;
    }

    public void addNewMsg(WelcomeMsgDTO dto) {
        commonAddCheck(dto);
        WelcomeMessageReviewData condition = new WelcomeMessageReviewData();
        condition.setRoomId(dto.getRoomId());
        condition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
        long count = welcomeMessageReviewDao.selectActionListCount(dto.getRoomId(),
                Arrays.asList(WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING, WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED));
        if (count >= 20) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "最多只能有20条待审核与审核通过的记录");
        }
        if (idetectService.detectText(new TextDTO(dto.getMessageContent(), DetectOriginConstant.ROOM_WELCOME_MSG_RELATED, dto.getUid())).getData().getIsSafe() == 0) {
            logger.info("updateContent messageContent is not safe");
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "打招呼内容包含敏感词");
        }
        WelcomeMessageReviewData data = new WelcomeMessageReviewData();
        data.setSubmitterUid(dto.getUid());
        data.setRoomId(dto.getRoomId());
        data.setMessageContent(dto.getMessageContent());
        data.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING);
        data.setSubmitTime(DateHelper.getNowSeconds());
        data.setUpdateTime(DateHelper.getNowSeconds());
        welcomeMessageReviewDao.insert(data);
        logger.info("addNewMsg success. roomId={} messageContent={}", dto.getRoomId(), dto.getMessageContent());
    }

    public void updateContent(WelcomeMsgDTO dto) {
        commonAddCheck(dto);
        if (dto.getId() == null) {
            logger.info("updateContent id is null");
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "id不能为空");
        }
        WelcomeMessageReviewData data = welcomeMessageReviewDao.selectById(dto.getRoomId(), dto.getId());
        if (data == null || data.getReviewAction() == WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED) {
            logger.info("updateContent data is null or reviewAction is approved");
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "数据不存在或者已通过");
        }
        if (data.getReviewAction() == WelcomeMessageReviewConstant.REVIEW_STATUS_REJECTED) {
            // 拒绝的可以修改，但是要判断数量
            WelcomeMessageReviewData condition = new WelcomeMessageReviewData();
            condition.setRoomId(dto.getRoomId());
            condition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
            long count = welcomeMessageReviewDao.selectActionListCount(dto.getRoomId(),
                    Arrays.asList(WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING, WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED));
            if (count >= 20) {
                throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "最多只能有20条待审核与审核通过的记录");
            }
        }

        if (idetectService.detectText(new TextDTO(dto.getMessageContent(), DetectOriginConstant.ROOM_WELCOME_MSG_RELATED, dto.getUid())).getData().getIsSafe() == 0) {
            logger.info("updateContent messageContent is not safe");
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "打招呼内容包含敏感词");
        }

        welcomeMessageReviewDao.updateContent(dto.getRoomId(), dto.getId(), dto.getMessageContent());
    }


    private void commonAddCheck(WelcomeMsgDTO dto) {
        if (StringUtils.isEmpty(dto.getMessageContent())) {
            logger.info("commonAddCheck messageContent is empty");
            throw new CommonException(RoomHttpCode.MESSAGE_CONTENT_EMPTY);
        }
        if (StringUtils.isEmpty(dto.getRoomId())) {
            logger.info("commonAddCheck roomId is empty");
            throw new CommonException(RoomHttpCode.ROOM_ID_EMPTY);
        }
        if (dto.getMessageContent().length() > 200) {
            logger.info("commonAddCheck messageContent length is greater than 200");
            throw new CommonException(RoomHttpCode.MESSAGE_TOO_LONG);
        }
        // 房主或者副房主
        RoomRoleData roleData = roomMemberDao.getRoleData(dto.getRoomId(), dto.getUid());
        if (!roleData.isHostOrVice()) {
            logger.info("commonAddCheck uid is not room owner or vice owner");
            throw new CommonException(RoomHttpCode.NOT_ROOM_OWNER_OR_VICE_OWNER);
        }

    }

    public void delete(WelcomeMsgDTO dto) {
        // 房主或者副房主
        RoomRoleData roleData = roomMemberDao.getRoleData(dto.getRoomId(), dto.getUid());
        if (!roleData.isHostOrVice()) {
            logger.info("commonAddCheck uid is not room owner or vice owner");
            throw new CommonException(RoomHttpCode.NOT_ROOM_OWNER_OR_VICE_OWNER);
        }
        WelcomeMessageReviewData condition = new WelcomeMessageReviewData();
        condition.setRoomId(dto.getRoomId());
        condition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
        long count = welcomeMessageReviewDao.selectCount(condition);
        if (count <= 1) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "最后一条记录不能删除");
        }
        welcomeMessageReviewDao.delete(dto.getRoomId(), dto.getId());
        logger.info("delete welcome msg success. roomId={} id={}", dto.getRoomId(), dto.getId());
    }

    @Cacheable(value = "getAllWelcomeMsgCache", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<String> getAllWelcomeMsgCache(String roomId) {
        WelcomeMessageReviewData condition = new WelcomeMessageReviewData();
        condition.setRoomId(roomId);
        condition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
        List<WelcomeMessageReviewData> allList = welcomeMessageReviewDao.selectPageList(1, 20, condition);
        if (CollectionUtils.isEmpty(allList)) {
            List<String> newHostWelcomeWordsAr = roomConfig.getNewHostWelcomeWordsAr();
            return newHostWelcomeWordsAr.stream().map(this::buildAt).collect(Collectors.toList());
        }
        return allList.stream().map(item -> buildAt(item.getMessageContent()))
                .collect(Collectors.toList());
    }

    public String buildAt(String text) {
        // 拼接 @xxx
        return String.format("%s %s", AT_XXX, text);
    }


}
