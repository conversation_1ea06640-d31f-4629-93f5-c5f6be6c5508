package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.LuckyDiceDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.PageDTO;
import com.quhong.data.vo.LuckyDiceVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.room.LuckyDiceMsg;
import com.quhong.mysql.dao.LuckyDiceRecordDao;
import com.quhong.mysql.data.LuckyDiceRecordData;
import com.quhong.redis.LuckyDiceRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;


@Service
public class LuckyDiceService {

    private static final Logger logger = LoggerFactory.getLogger(LuckyDiceService.class);
    private static final String MONEY_TITLE = "Lucky Dice";
    private static final Integer FEE_MONEY_TYPE = 963;
    private static final String FEE_DESC = "Lucky dice fee";
    private static final Integer REWARD_MONEY_TYPE = 964;
    private static final String REWARD_DESC = "Lucky dice reward";
    private final List<Integer> FEE_LIST = Arrays.asList(3, 10, 100, 500); // 费用列表

    private static final int TRIPLE_TIMES = 7; // 三个相同倍数
    private static final int STRAIGHT_TIMES = 3; // 顺子倍数
    private static final int DOUBLE_TIMES = 2; // 两个相同倍数

    private static final String RECORD_RUL = ServerConfig.isProduct() ? "https://static.youstar.live/lucky_dice_record/" : "https://test2.qmovies.tv/lucky_dice_record/";
    private static String RULE_RUL = "https://static.youstar.live/lucky_dice_rule/";


    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private LuckyDiceRedis luckyDiceRedis;
    @Resource
    private LuckyDiceRecordDao luckyDiceRecordDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomActorCache roomActorCache;


    @PostConstruct
    private void init() {
        List<Integer> luckyNumber = luckyDiceRedis.getLuckyNumber(false);
        RULE_RUL = RULE_RUL + "?bonus_list=" + (null == luckyNumber ? 50 : luckyNumber.get(3)) + "," + TRIPLE_TIMES + "," + STRAIGHT_TIMES + "," + DOUBLE_TIMES;
    }

    public LuckyDiceVO.CheckVO checkLuckyDice(String uid) {
        LuckyDiceVO.CheckVO checkVO = new LuckyDiceVO.CheckVO();
        checkVO.setBalance(actorDao.findActorDataFromDB(uid).getBeans());
        checkVO.setTodayWin(luckyDiceRedis.getWinToday(uid));
        checkVO.setFeeList(FEE_LIST);
        checkVO.setLuckyNumberList(luckyDiceRedis.getLuckyNumber(false));
        checkVO.setWinningList(luckyDiceRecordDao.getWinningList());
        checkVO.setRuleUrl(RULE_RUL);
        checkVO.setRecordUrl(RECORD_RUL);
        return checkVO;
    }

    public LuckyDiceVO.ResultVO throwLuckyDice(LuckyDiceDTO dto) {
        if (!FEE_LIST.contains(dto.getFee())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int prizePool = luckyDiceRedis.getPrizePool();
        int balance = deductDiamonds(dto.getUid(), dto.getFee());
        List<Integer> luckyNumber = luckyDiceRedis.getLuckyNumber(false);
        LuckyDiceVO.ResultVO vo = doLuckyDice(dto.getFee(), balance, luckyNumber, prizePool);
        if (vo.getReward() == 0 && prizePool > 200000) {
            // todo 提高中奖概率
            vo = doLuckyDice(dto.getFee(), balance, luckyNumber, prizePool);
        }
        if (vo.getReward() > 0) {
            sendReward(dto.getUid(), vo.getReward());
        }
        luckyDiceRecordDao.saveOne(new LuckyDiceRecordData(dto.getUid(), JSON.toJSONString(vo.getDiceList()), dto.getFee(), vo.getTimes(), vo.getReward()));
//        sendLuckyDiceMsg(dto.getRoomId(), dto.getUid(), vo.getTimes(), vo.getDiceList());
        vo.setTodayWin(luckyDiceRedis.getWinToday(dto.getUid()));
        logger.info("send lucky dice reward uid={} fee={} reward={} prizePool={}", dto.getUid(), dto.getFee(), vo.getReward(), prizePool);
        return vo;
    }

    private void sendLuckyDiceMsg(String roomId, String uid, int times, List<Integer> diceList) {
        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
        if (actorData == null) {
            return;
        }
        LuckyDiceMsg msg = new LuckyDiceMsg();
        UNameObject uNameObject = new UNameObject();
        uNameObject.setUid(uid);
        uNameObject.setName(actorData.getName());
        uNameObject.setBid(actorData.getBubbleId());
        uNameObject.setHead(actorData.getHead());
        uNameObject.setLevel(actorData.getLevel());
        uNameObject.setVip(actorData.getVipLevel());
        uNameObject.setVipMedal(actorData.getVipMedal());
        uNameObject.setBadgeList(actorData.getBadgeList());
        uNameObject.setRole(actorData.getNewRole());
        uNameObject.setIdentify(actorData.getIdentify());
        msg.setUname(uNameObject);
        msg.setDice1(diceList.get(0));
        msg.setDice2(diceList.get(1));
        msg.setDice3(diceList.get(2));
        msg.setTimes(times);
        roomWebSender.sendRoomWebMsg(roomId, null, msg, false);
    }

    private LuckyDiceVO.ResultVO doLuckyDice(int fee, int balance, List<Integer> luckyNumber, int prizePool) {
        int times = 0;
        int dice1 = ThreadLocalRandom.current().nextInt(1, 7);
        int dice2 = ThreadLocalRandom.current().nextInt(1, 7);
        int dice3 = ThreadLocalRandom.current().nextInt(1, 7);
        // 幸运数
        if (null != luckyNumber
                && Objects.equals(luckyNumber.get(0), dice1)
                && Objects.equals(luckyNumber.get(1), dice2)
                && Objects.equals(luckyNumber.get(2), dice3)) {
            times = luckyNumber.get(3);
        } else if (dice1 == dice2 && dice1 == dice3) {
            // 三个相同
            times = TRIPLE_TIMES;
        } else if ((dice2 - dice1 == 1 && dice3 - dice2 == 1) || (dice1 - dice2 == 1 && dice2 - dice3 == 1)) {
            // 顺子
            times = STRAIGHT_TIMES;
        } else if (dice1 == dice2 || dice2 == dice3) {
            // 两个相同
            times = DOUBLE_TIMES;
        }
        int reward = fee * times;
        if (reward >= prizePool * 0.9f) {
            return doLuckyDice(fee, balance, luckyNumber, prizePool);
        }
        LuckyDiceVO.ResultVO resultVO = new LuckyDiceVO.ResultVO();
        resultVO.setReward(reward);
        resultVO.setBalance(balance + reward);
        resultVO.setDiceList(Arrays.asList(dice1, dice2, dice3));
        resultVO.setTimes(times);
        return resultVO;
    }

    private int deductDiamonds(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(LuckyDiceService.FEE_MONEY_TYPE);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(MONEY_TITLE);
        moneyDetailReq.setDesc(LuckyDiceService.FEE_DESC);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(RoomHttpCode.UNION_NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
        // 增加到奖池
        luckyDiceRedis.addPrizePool(changed * 0.95d);
        return Integer.parseInt(result.getData());
    }

    private void sendReward(String uid, int reward) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setChanged(reward);
        moneyDetailReq.setTitle(MONEY_TITLE);
        moneyDetailReq.setDesc(REWARD_DESC);
        moneyDetailReq.setAtype(REWARD_MONEY_TYPE);
        moneyDetailReq.setMtime(DateHelper.getNowSeconds());
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        luckyDiceRedis.addWinToday(uid, reward);
        luckyDiceRedis.reducePrizePool(reward);
    }

    public PageVO<LuckyDiceVO.RecordVO> luckyDiceRecord(PageDTO dto) {
        List<LuckyDiceRecordData> pageList = luckyDiceRecordDao.findByPage(dto.getUid(), dto.getPage());
        PageVO<LuckyDiceVO.RecordVO> vo = new PageVO<>(new ArrayList<>());
        for (LuckyDiceRecordData data : pageList) {
            LuckyDiceVO.RecordVO recordVO = new LuckyDiceVO.RecordVO();
            recordVO.setResult(JSON.parseObject(data.getResult(), new TypeReference<List<Integer>>() {
            }));
            recordVO.setFee(data.getFee());
            recordVO.setTimes(data.getTimes());
            recordVO.setReward(data.getReward());
            recordVO.setCtime(data.getCtime());
            vo.getList().add(recordVO);
        }
        vo.setNextUrl(dto.getPage(), LuckyDiceRecordDao.PAGE_SIZE);
        return vo;
    }
}
