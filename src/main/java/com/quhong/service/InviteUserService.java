package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.InviteRecordEvent;
import com.quhong.config.AsyncConfig;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.constant.UserHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.InviteFissionDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.vo.BackUserInfoVO;
import com.quhong.data.vo.InviteFissionVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.InviteFissionDao;
import com.quhong.mysql.data.InviteFissionData;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.redis.DauUserRedis;
import com.quhong.redis.InviteCodeRedis;
import com.quhong.utils.ActorUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * 邀请裂变用户
 */
@Service
public class InviteUserService {

    private static final Logger logger = LoggerFactory.getLogger(InviteUserService.class);
    private static final String CHARGE_499 = "499";
    private static final String CHARGE_1999 = "1999";
    private static final String CHARGE_4999 = "4999";
    private static final String DAU_2 = "dau2";
    private static final String DAU_3 = "dau3";
    private static final String DAU_5 = "dau5";
    private static final String DAU_7 = "dau7";

    /**
     * 埋点 邀请者奖励title
     */
    public static final Map<String, String> TYPE_INVITE_DIAMOND_TITLE_MAP = new HashMap<String, String>() {
        {
            // 邀请者获得奖励埋点
            put("reg-inviter", "Invite-Registration rewards Inviter");
            put("dau2-inviter", "Invite-Inviter Active day2");
            put("dau3-inviter", "Invite-Inviter Active day3");
            put("dau7-inviter", "Invite-Inviter Active day7");
            put("gift1-inviter", "Invite-Inviter gift500");
            put("gift2-inviter", "Invite-Inviter gift1000");
            put("gift3-inviter", "Invite-Inviter gift2000");
            put("charge1-inviter", "Invite-Inviter week1");
            put("charge2-inviter", "Invite-Inviter week2");
            put("charge3-inviter", "Invite-Inviter week3");

            // 被邀请者获得奖励埋点
            put("reg-invitee", "Invite-Registration rewards Invitee");
            put("dau2-invitee", "Invite-Invitee Active day2");
            put("dau3-invitee", "Invite-Invitee Active day3");
            put("dau7-invitee", "Invite-Invitee Active day7");
            put("gift1-invitee", "Invite-Invitee gift500");
            put("gift2-invitee", "Invite-Invitee gift1000");
            put("gift3-invitee", "Invite-Invitee gift2000");
            put("charge1-invitee", "Invite-Invitee charge099");
            put("charge2-invitee", "Invite-Invitee charge1999");
            put("charge3-invitee", "Invite-Invitee charge4999");
        }
    };


    private String URL_BACK_URL;

    private Interner<String> stringPool = Interners.newWeakInterner();

    private static final int PAGE_SIZE = 20;
    private static final int REG_MAX_DAY = 35;
    private static final int INVITEE_REG_VALID_DAY = 30;

    private static final String REG_KEY = "reg";

    private static final List<String> DAU_KEY_LIST = Arrays.asList("dau2", "dau3", "dau7");

    private static final List<String> GIFT_KEY_LIST = Arrays.asList("gift1", "gift2", "gift3");

    private static final List<String> MONEY_KEY_LIST = Arrays.asList("charge1", "charge2", "charge3");


    private static final Integer REG_BEAN = 10;
    private static final List<Integer> DAU_BEAN_LIST = Arrays.asList(20, 30, 50);
    private static final List<Integer> GIFT_BEAN_LIST = Arrays.asList(30, 50, 100);
    private static final List<Integer> MONEY_BEAN_LIST = Arrays.asList(2000, 2000, 2000);

    private static int maxInviterDauBeans; // 邀请者可领取的最大日活钻石
    private static int maxInviterGiftBeans;
    private static int maxInviterMoneyBeans;

    /**
     *
     */
    public static final Map<String, Integer> TYPE_DAU_KEY_DAY_MAP = new HashMap<String, Integer>() {
        {
            put("dau2", 2);
            put("dau3", 3);
            put("dau7", 7);
        }
    };

    /**
     * 邀请者奖励key,被邀请者奖励key
     */
    public static final Map<String, String> TYPE_INVITE_KEY_MAP = new HashMap<String, String>() {
        {
            put("reg-1", "reg-inviter-key");  // 邀请码验证成功	-邀请者奖励
//            put("reg-2", "reg-invitee-key");  // 邀请码验证成功	-被邀请邀请者奖励
//            put("dau2-1", "dau2-inviter");
//            put("dau3-1", "dau3-inviter");
//            put("dau7-1", "dau7-inviter");
//            put("dau2-2", "dau2-invitee");
//            put("dau3-2", "dau3-invitee");
//            put("dau7-2", "dau7-invitee");
//            put("gift1-1", "gift1-inviter");
//            put("gift2-1", "gift2-inviter");
//            put("gift3-1", "gift3-inviter");
//            put("gift1-2", "gift1-invitee");
//            put("gift2-2", "gift2-invitee");
//            put("gift3-2", "gift3-invitee");
            put("charge1-2", "charge1-invitee-key");
            put("charge2-2", "charge2-invitee-key");
            put("charge3-2", "charge3-invitee-key");
        }
    };
    @Resource
    private ActorDao actorDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private CoreRewardInitData coreRewardInitData;
    @Resource
    private InviteFissionDao inviteFissionDao;
    @Resource
    private DauUserRedis dauUserRedis;
    @Resource
    private InviteCodeRedis inviteCodeRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    protected CommonTaskService commonTaskService;

    @PostConstruct
    public void postInit() {
        maxInviterDauBeans = DAU_BEAN_LIST.stream().mapToInt(Integer::intValue).sum() * 2; // 这里可能有老版本的数据
        maxInviterGiftBeans = GIFT_BEAN_LIST.stream().mapToInt(Integer::intValue).sum();
        maxInviterMoneyBeans = MONEY_BEAN_LIST.stream().mapToInt(Integer::intValue).sum();
    }

    public InviteFissionVO getInviterInfo(InviteFissionDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        InviteFissionVO vo = new InviteFissionVO();
        vo.setInviterHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

        HotSearchListData totalData = inviteFissionDao.getMyTotalData(uid);
        logger.info("totalData:{}", totalData);
        if (totalData != null) {
            vo.setTotalPerson(totalData.getTotalP());
            long totalDiamond = totalData.getSearchNumSum() != null ? totalData.getSearchNumSum() : 0;
            vo.setTotalDiamond((int) totalDiamond);
        }
        vo.setInviteCode(inviteCodeRedis.getHostCodeByUid(uid));
        vo.setToday(DateHelper.ARABIAN.formatDateInDay());

        List<InviteFissionVO.RankInviteUserVO> rollList = new ArrayList<>();
        int todayStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        List<InviteFissionData> sourceDetailList = inviteFissionDao.getDetailByUidToday(uid, todayStartTime);
        if (!CollectionUtils.isEmpty(sourceDetailList)) {
            sourceDetailList.forEach(item -> {
                InviteFissionVO.RankInviteUserVO sub = new InviteFissionVO.RankInviteUserVO();
                String subAid = item.getAid();
                ActorData actorData1 = actorDao.getActorDataFromCache(subAid);
                sub.setName(actorData1.getName());
                sub.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData1.getHead()));
                sub.setUid(subAid);
                rollList.add(sub);
            });
        }
        vo.setRollList(rollList);

        return vo;
    }

    public InviteFissionVO getInviterDetail(InviteFissionDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        InviteFissionVO vo = new InviteFissionVO();
        List<InviteFissionVO.DetailItemVO> detailList = new ArrayList<>();
        List<InviteFissionData> sourceDetailList = inviteFissionDao.getDetailByUidPage(uid, req.getPage(), PAGE_SIZE);
        if (!CollectionUtils.isEmpty(sourceDetailList)) {
            sourceDetailList.forEach(item -> {
                InviteFissionVO.DetailItemVO sub = new InviteFissionVO.DetailItemVO();
                BeanUtils.copyProperties(item, sub);
                String subAid = item.getAid();
                ActorData actorData1 = actorDao.getActorDataFromCache(subAid);
                sub.setInviteeUid(subAid);
                sub.setInviteeName(actorData1.getName());
                sub.setInviteeHead(ImageUrlGenerator.generateRoomUserUrl(actorData1.getHead()));
                sub.setInviteeRegTime(new ObjectId(subAid).getTimestamp());
                if (ActorUtils.isNewRegisterActor(subAid, REG_MAX_DAY)) {
                    sub.setHasReward(1);
                    fillStateDetail(sub, item);
                } else {
                    sub.setHasReward(0);
                }
                detailList.add(sub);
            });
        }
        vo.setDetailList(detailList);
        return vo;
    }

    private void fillStateDetail(InviteFissionVO.DetailItemVO detailItemVO, InviteFissionData inviteData) {
        InviteFissionVO.InviteeUserStateItemVO detail = new InviteFissionVO.InviteeUserStateItemVO();

        InviteFissionVO.InviteeUserItemVO inviteeUserRegState = new InviteFissionVO.InviteeUserItemVO(); // 被邀请者注册邀请者领取状态
        List<InviteFissionVO.InviteeUserItemVO> inviteeUserDauStateList = new ArrayList<>(); //被邀请者日活邀请者领取状态
        List<InviteFissionVO.InviteeUserItemVO> inviteeUserGiftStateList = new ArrayList<>(); //被邀请者送礼邀请者领取状态
        List<InviteFissionVO.InviteeUserItemVO> inviteeUserChargeStateList = new ArrayList<>(); //被邀请者充值邀请者领取状态


        InviterUserStateData yaoQingUserdata = backUserStateRedis.getInvitedUserData(inviteData.getUid(), inviteData.getAid()); // 邀请者领取数据
        BackUserStateData inviteeStateData = checkInviteUser(inviteData.getAid());// 被邀请者领取数据
        if (yaoQingUserdata != null) {
            inviteeUserRegState.setItemName(REG_KEY);
            inviteeUserRegState.setItemReward(REG_BEAN);
            inviteeUserRegState.setItemState(inviteData.getRegReward() != null && inviteData.getRegReward() > 0 ? 2 : 1); // 按mysql表的状态来

            int index = 0;
            Map<String, Integer> dayMapStatus = yaoQingUserdata.getDayMapStatus();

            boolean isDone = false;
            if (inviteeStateData != null) {
                Set<String> daySet = inviteeStateData.getDayActiveSet();
                int oldDauCount = daySet.size(); // 获取老的日活天数
                isDone = oldDauCount >= 7;
            }
            for (String item : DAU_KEY_LIST) {
                int status = isDone ? 2 : dayMapStatus.getOrDefault(item, 0);
                inviteeUserDauStateList.add(new InviteFissionVO.InviteeUserItemVO(item, status, DAU_BEAN_LIST.get(index)));
                index++;
            }

            index = 0;
            Map<String, Integer> giftMapStatus = yaoQingUserdata.getGiftMapStatus();
            for (String item : GIFT_KEY_LIST) {
                inviteeUserGiftStateList.add(new InviteFissionVO.InviteeUserItemVO(item, giftMapStatus.getOrDefault(item, 0), GIFT_BEAN_LIST.get(index)));
                index++;
            }

            List<Integer> weekTimeList = yaoQingUserdata.getWeekTimeList();
            if (CollectionUtils.isEmpty(weekTimeList)) {
//                int start = inviteData.getCtime();
//                start + (int) TimeUnit.DAYS.toSeconds(7);
                inviteeUserChargeStateList.add(new InviteFissionVO.InviteeUserItemVO(MONEY_KEY_LIST.get(0), 0, 0));
                inviteeUserChargeStateList.add(new InviteFissionVO.InviteeUserItemVO(MONEY_KEY_LIST.get(1), 0, 0));
                inviteeUserChargeStateList.add(new InviteFissionVO.InviteeUserItemVO(MONEY_KEY_LIST.get(2), 0, 0));
            } else {
                index = 0;
                List<Integer> weekBeansList = yaoQingUserdata.getWeekBeansList();
                List<Integer> weekStatusList = yaoQingUserdata.getWeekStatusList();


                for (Integer bean : weekBeansList) {
                    int wStatus = getWeekStatus(index, weekStatusList.get(index), inviteData.getCtime(), bean);
                    inviteeUserChargeStateList.add(new InviteFissionVO.InviteeUserItemVO(MONEY_KEY_LIST.get(index), wStatus, bean));
                    index++;
                }
            }
        }

        detail.setInviteeUserRegState(inviteeUserRegState);
        detail.setInviteeUserDauStateList(inviteeUserDauStateList);
        detail.setInviteeUserGiftStateList(inviteeUserGiftStateList);
        detail.setInviteeUserChargeStateList(inviteeUserChargeStateList);

        detailItemVO.setInviteeUserStateDetail(detail);
    }

    private int getWeekStatus(int index, int oldStatus, int ctime, int bean) {
        int startTime = 0;
        int endTime = 0;
        if (ServerConfig.isNotProduct()) {
            if (index == 0) {
                startTime = ctime + (int) TimeUnit.HOURS.toSeconds(1);
                endTime = ctime + (int) TimeUnit.HOURS.toSeconds(2);
            } else if (index == 1) {
                startTime = ctime + (int) TimeUnit.HOURS.toSeconds(2);
                endTime = ctime + (int) TimeUnit.HOURS.toSeconds(3);
            } else if (index == 2) {
                startTime = ctime + (int) TimeUnit.HOURS.toSeconds(3);
                endTime = ctime + (int) TimeUnit.DAYS.toSeconds(35);
            }
        } else {
            if (index == 0) {
                startTime = ctime + (int) TimeUnit.DAYS.toSeconds(14);
                endTime = ctime + (int) TimeUnit.DAYS.toSeconds(21);
            } else if (index == 1) {
                startTime = ctime + (int) TimeUnit.DAYS.toSeconds(21);
                endTime = ctime + (int) TimeUnit.DAYS.toSeconds(28);
            } else if (index == 2) {
                startTime = ctime + (int) TimeUnit.DAYS.toSeconds(28);
                endTime = ctime + (int) TimeUnit.DAYS.toSeconds(35);
            }
        }


        if (oldStatus != 2) {
            // 待领取或者未完成的状态
            if (bean == 0) {
                return 0;
            }
            int now = DateHelper.getNowSeconds();
            if (now >= startTime && now < endTime) {
                return 1;
            }
            return 0;
        } else {
            return 2;
        }
    }


    public InviteFissionVO getInviterRank(InviteFissionDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        int startTime = DateHelper.getNowSeconds() - 30 * 86400;
        InviteFissionVO vo = new InviteFissionVO();
        List<InviteFissionVO.RankInviteUserVO> rankList = new ArrayList<>();
        List<HotSearchListData> sourceDetailList = inviteFissionDao.getRankListData(startTime);
        if (!CollectionUtils.isEmpty(sourceDetailList)) {
            int rank = 0;
            for (HotSearchListData item : sourceDetailList) {
                InviteFissionVO.RankInviteUserVO sub = new InviteFissionVO.RankInviteUserVO();
                String subUid = item.getSearchKey();
                ActorData actorData1 = actorDao.getActorDataFromCache(subUid);
                if (actorData1.getValid() == 0) {
                    continue;
                }
                rank += 1;
                sub.setUid(subUid);
                sub.setName(actorData1.getName());
                sub.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData1.getHead()));
                sub.setNum(item.getTotalP());
                sub.setRank(rank);
                rankList.add(sub);
                if (rank >= 30) {
                    break;
                }
            }

        }
        vo.setRankList(rankList);
        return vo;
    }


    public InviteFissionVO getInviteeInfo(InviteFissionDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        InviteFissionVO vo = new InviteFissionVO();
        vo.setToday(DateHelper.ARABIAN.formatDateInDay());
        vo.setInviteeHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setInviteeValidTime(new ObjectId(uid).getTimestamp() + (int) TimeUnit.DAYS.toSeconds(INVITEE_REG_VALID_DAY));
        vo.setInviteeMyStateDetail(inviteeMyStateDetail(uid));

        InviteFissionData inviteFissionData = inviteFissionDao.getOneByAid(uid);
        if (inviteFissionData != null) {
            ActorData actorData2 = actorDao.getActorDataFromCache(inviteFissionData.getUid());
            if (actorData2 != null) {
                vo.setInviterUid(inviteFissionData.getUid());
                vo.setInviterName(actorData2.getName());
                vo.setInviterHead(ImageUrlGenerator.generateRoomUserUrl(actorData2.getHead()));
            }
        }
        return vo;
    }


    private InviteFissionVO.InviteeUserStateItemVO inviteeMyStateDetail(String uid) {
        InviteFissionVO.InviteeUserStateItemVO detail = new InviteFissionVO.InviteeUserStateItemVO();

        InviteFissionVO.InviteeUserItemVO inviteeUserRegState = new InviteFissionVO.InviteeUserItemVO(); // 被邀请者注册领取状态
        List<InviteFissionVO.InviteeUserItemVO> inviteeUserDauStateList = new ArrayList<>(); //被邀请者日活领取状态
        List<InviteFissionVO.InviteeUserItemVO> inviteeUserGiftStateList = new ArrayList<>(); //被邀请者送礼领取状态
        List<InviteFissionVO.InviteeUserItemVO> inviteeUserChargeStateList = new ArrayList<>(); //被邀请者充值领取状态

        boolean noBind = false;
        BackUserStateData inviteMyStateData = checkInviteUser(uid);// 被邀请者领取数据
        if (inviteMyStateData == null && ActorUtils.isNewRegisterActor(uid, INVITEE_REG_VALID_DAY)) {
            // 还没有绑定过
            inviteMyStateData = backUserStateRedis.getInvitedUserData(uid);
            noBind = true;
        }
        if (inviteMyStateData != null) {
            inviteeUserRegState.setItemName(REG_KEY);
            inviteeUserRegState.setItemReward(REG_BEAN);
            inviteeUserRegState.setItemState(noBind ? 0 :
                    inviteMyStateData.getRegStatus() == 0 ? 2 : inviteMyStateData.getRegStatus());

            int index = 0;
            for (String item : DAU_KEY_LIST) {
                fillInviteeUserItem(item, inviteeUserDauStateList, inviteMyStateData, index);
                index++;
            }

            index = 0;
            Map<String, Integer> giftMapStatus = inviteMyStateData.getGiftMapStatus();
            for (String item : GIFT_KEY_LIST) {
                inviteeUserGiftStateList.add(new InviteFissionVO.InviteeUserItemVO(item, giftMapStatus.getOrDefault(item, 0), GIFT_BEAN_LIST.get(index)));
                index++;
            }

            index = 0;
            Map<String, Integer> dollarsMapStatus = inviteMyStateData.getDollarsMapStatus();
            for (String item : MONEY_KEY_LIST) {
                inviteeUserChargeStateList.add(new InviteFissionVO.InviteeUserItemVO(item, dollarsMapStatus.getOrDefault(item, 0), MONEY_BEAN_LIST.get(index)));
                index++;
            }
        }

        detail.setInviteeUserRegState(inviteeUserRegState);
        detail.setInviteeUserDauStateList(inviteeUserDauStateList);
        detail.setInviteeUserGiftStateList(inviteeUserGiftStateList);
        detail.setInviteeUserChargeStateList(inviteeUserChargeStateList);
        return detail;
    }


    public InviteFissionVO collectReward(InviteFissionDTO req) {
        String uid = req.getUid();
        String itemTag = req.getItemName();
        Integer userType = req.getUserType();
        String aid = req.getAid();

        if (!StringUtils.hasLength(itemTag) || !StringUtils.hasLength(uid) || userType == null) {
            logger.error("itemTag:{} or uid:{} is empty", itemTag, uid);
            throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
        }
        if (!DAU_KEY_LIST.contains(itemTag) && !GIFT_KEY_LIST.contains(itemTag) && !MONEY_KEY_LIST.contains(itemTag)
                && !REG_KEY.equals(itemTag)) {
            logger.error("itemTag:{} is invalid", itemTag);
            throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        InviteFissionVO vo = new InviteFissionVO();
        int rewardDiamond = 0;
        String keyTitle = "";
        boolean isChanged = false;
        if (userType == 1) {
            InviterUserStateData yaoQingData = backUserStateRedis.getInviterUserState(uid, aid);
            InviteFissionData inviteFissionData = inviteFissionDao.getOneByAid(aid);

            if (yaoQingData == null) {
                logger.error("Not inviter to users uid:{} aid:{}", uid, aid);
                throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
            }
            int totalReward = inviteFissionData.getTotalReward() == null ? 0 : inviteFissionData.getTotalReward();
            keyTitle = TYPE_INVITE_DIAMOND_TITLE_MAP.getOrDefault(itemTag + "-inviter", MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TITLE);
            if (REG_KEY.equals(itemTag)) {
                int status = inviteFissionData.getRegReward() != null && inviteFissionData.getRegReward() > 0 ? 2 : 1;
                if (status == 1) {
                    rewardDiamond = REG_BEAN;
                    inviteFissionData.setRegReward(REG_BEAN);
                    inviteFissionData.setTotalReward(totalReward + REG_BEAN);
                    inviteFissionData.setMtime(DateHelper.getNowSeconds());
                    inviteFissionDao.updateById(inviteFissionData);
                    backUserStateRedis.saveInviterUserState(yaoQingData, uid, aid);

                    //  通过资源key下发奖励
                    String resKey = TYPE_INVITE_KEY_MAP.getOrDefault(itemTag + "-" + userType, "");
                    resourceKeyHandlerService.sendResourceData(uid, resKey, MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE,
                            keyTitle, keyTitle, keyTitle, "", "", 0);
                    isChanged = true;
                }
            } else if (DAU_KEY_LIST.contains(itemTag)) {
                int index = DAU_KEY_LIST.indexOf(itemTag);
                rewardDiamond = DAU_BEAN_LIST.get(index);
                Map<String, Integer> dayMapStatus = yaoQingData.getDayMapStatus();
                int status = dayMapStatus.getOrDefault(itemTag, 0);
                int aliveDayReward = inviteFissionData.getAliveDayReward() == null ? 0 : inviteFissionData.getAliveDayReward();
                if (status == 1 && aliveDayReward < maxInviterDauBeans) {
                    dayMapStatus.put(itemTag, 2);
                    backUserStateRedis.saveInviterUserState(yaoQingData, uid, aid);
                    handleDiamondResourcesOnly(uid, rewardDiamond, MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE, keyTitle, keyTitle);

                    inviteFissionData.setAliveDay(TYPE_DAU_KEY_DAY_MAP.getOrDefault(itemTag, 2));
                    inviteFissionData.setAliveDayReward(aliveDayReward + rewardDiamond);
                    inviteFissionData.setTotalReward(totalReward + rewardDiamond);
                    inviteFissionData.setMtime(DateHelper.getNowSeconds());
                    inviteFissionDao.updateById(inviteFissionData);
                    isChanged = true;
                }
            } else if (GIFT_KEY_LIST.contains(itemTag)) {
                Map<String, Integer> giftMapStatus = yaoQingData.getGiftMapStatus();
                int status = giftMapStatus.getOrDefault(itemTag, 0);
                int giftReward = inviteFissionData.getGiftReward() == null ? 0 : inviteFissionData.getGiftReward();
                if (status == 1 && giftReward < maxInviterGiftBeans) {
                    giftMapStatus.put(itemTag, 2);
                    backUserStateRedis.saveInviterUserState(yaoQingData, uid, aid);
                    int index = GIFT_KEY_LIST.indexOf(itemTag);
                    rewardDiamond = GIFT_BEAN_LIST.get(index);
                    handleDiamondResourcesOnly(uid, rewardDiamond, MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE, keyTitle, keyTitle);


                    inviteFissionData.setGiftReward(giftReward + rewardDiamond);
                    inviteFissionData.setTotalReward(totalReward + rewardDiamond);
                    inviteFissionData.setMtime(DateHelper.getNowSeconds());
                    inviteFissionDao.updateById(inviteFissionData);
                    isChanged = true;
                }
            } else if (MONEY_KEY_LIST.contains(itemTag)) {
                int index = MONEY_KEY_LIST.indexOf(itemTag);
                List<Integer> weekTimeList = yaoQingData.getWeekTimeList();
                List<Integer> weekBeansList = yaoQingData.getWeekBeansList();
                List<Integer> weekStatusList = yaoQingData.getWeekStatusList();
                int bean = weekBeansList.get(index);

                int wStatus = getWeekStatus(index, weekStatusList.get(index), inviteFissionData.getCtime(), bean);
                int rechargeReward = inviteFissionData.getRechargeReward() == null ? 0 : inviteFissionData.getRechargeReward();
                if (wStatus == 1 && bean > 0 && rechargeReward < maxInviterMoneyBeans) {
                    weekStatusList.set(index, 2);
                    backUserStateRedis.saveInviterUserState(yaoQingData, uid, aid);
                    rewardDiamond = bean;
                    handleDiamondResourcesOnly(uid, rewardDiamond, MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE, keyTitle, keyTitle);

                    inviteFissionData.setRechargeReward(rechargeReward + rewardDiamond);
                    inviteFissionData.setTotalReward(totalReward + rewardDiamond);
                    inviteFissionData.setMtime(DateHelper.getNowSeconds());
                    inviteFissionDao.updateById(inviteFissionData);
                    isChanged = true;
                }

            }

        } else if (userType == 2) {
            BackUserStateData inviteUserStateData = checkInviteUser(uid);
            InviteFissionData inviteFissionData = inviteFissionDao.getOneByAid(uid);
            String yaoQingUid = inviteFissionData != null ? inviteFissionData.getUid() : "";
            ActorData yaoQingActorData = actorDao.getActorDataFromCache(yaoQingUid);
            int yaoQingRid = yaoQingActorData.getRid();
            keyTitle = TYPE_INVITE_DIAMOND_TITLE_MAP.getOrDefault(itemTag + "-invitee", MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TITLE);
            if (inviteUserStateData == null) {
                logger.error("Not invitee to users uid:{}", uid);
                throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
            }
            if (REG_KEY.equals(itemTag)) {
                int regStatus = inviteUserStateData.getRegStatus() == 0 ? 2 : inviteUserStateData.getRegStatus();
                if (regStatus == 1) {
                    rewardDiamond = REG_BEAN;
                    inviteUserStateData.setRegStatus(2);
                    isChanged = true;
                }
            } else if (DAU_KEY_LIST.contains(itemTag)) {
                int state = getState(itemTag, inviteUserStateData);
                if (state == 1) {
                    int index = DAU_KEY_LIST.indexOf(itemTag);
                    rewardDiamond = DAU_BEAN_LIST.get(index);
                    setState(itemTag, inviteUserStateData, 2);
                    isChanged = true;
                }
            } else if (GIFT_KEY_LIST.contains(itemTag)) {
                Map<String, Integer> giftMapStatus = inviteUserStateData.getGiftMapStatus();
                int status = giftMapStatus.getOrDefault(itemTag, 0);
                if (status == 1) {
                    giftMapStatus.put(itemTag, 2);
                    int index = GIFT_KEY_LIST.indexOf(itemTag);
                    rewardDiamond = GIFT_BEAN_LIST.get(index);
                    isChanged = true;
                }

            } else if (MONEY_KEY_LIST.contains(itemTag)) {
                //  通过资源key下发奖励
                Map<String, Integer> dollarsMapStatus = inviteUserStateData.getDollarsMapStatus();
                int status = dollarsMapStatus.getOrDefault(itemTag, 0);
                if (status == 1) {
                    dollarsMapStatus.put(itemTag, 2);
//                    backUserStateRedis.saveInvitedUserState(inviteUserStateData, uid);
                    String resKey = TYPE_INVITE_KEY_MAP.getOrDefault(itemTag + "-" + userType, "");
                    resourceKeyHandlerService.sendResourceData(uid, resKey, MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE,
                            keyTitle, keyTitle, String.valueOf(yaoQingRid), "", "", 0);
                    isChanged = true;
                }

            }
            if (isChanged) {
                backUserStateRedis.saveInvitedUserState(inviteUserStateData, uid);
                if (rewardDiamond > 0) {
                    handleDiamondResourcesOnly(uid, rewardDiamond, MoneyTypeConstant.INVITE_FISSION_AC_DIAMOND_TYPE, keyTitle, String.valueOf(yaoQingRid));
                }
            }
        }
        if (!isChanged && rewardDiamond == 0) {
            logger.error("collect reward fail uid:{} itemTag:{}", uid, itemTag);
            throw new CommonH5Exception(UserHttpCode.COLLECT_REWARD_FAIL);
        }
        vo.setRewardDiamond(rewardDiamond);
        return vo;
    }

    public InviteFissionVO bindUidByCode(InviteFissionDTO req) {
        String aid = req.getUid(); // 被邀请者
        String inviteCode = req.getInviteCode();
        if (!StringUtils.hasLength(inviteCode) || !StringUtils.hasLength(aid)) {
            logger.error("inviteCode:{} or aid:{} is empty", inviteCode, aid);
            throw new CommonH5Exception(UserHttpCode.PARAM_ERROR);
        }
        boolean isReg = ActorUtils.isNewRegisterActor(aid, INVITEE_REG_VALID_DAY);
        if (!isReg) {
            logger.error("aid is not reg new user aid:{} ", aid);
            throw new CommonH5Exception(UserHttpCode.REG_NOT_ALLOW);
        }
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData == null || !StringUtils.hasLength(actorData.getTn_id())) {
            logger.error("not find aid:{} or tn_id is empty", aid);
            throw new CommonH5Exception(UserHttpCode.USER_NOT_EXIST);
        }
        String uid = inviteCodeRedis.getUidByHostCode(inviteCode); // 邀请者
        if (!StringUtils.hasLength(uid)) {
            logger.error("uid not find inviteCode:{} ", inviteCode);
            throw new CommonH5Exception(UserHttpCode.INVITE_CODE_NOT_ALLOW);
        }
        if (uid.equals(aid)) {
            logger.error("uid equals aid uid:{} inviteCode:{} ", uid, inviteCode);
            throw new CommonH5Exception(UserHttpCode.MYSELF_NOT_ALLOW);
        }
        int startTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        int todayCount = inviteFissionDao.getCountByUid(uid, startTime);
        int mStartTime = (int) (DateHelper.ARABIAN.getMonthFirstDayTimestamp() / 1000);
        int totalCount = inviteFissionDao.getCountByUid(uid, mStartTime);
        if (todayCount >= 5) {
            logger.error("above limit uid:{} aid:{} totalCount:{} todayCount:{}", uid, aid, totalCount, todayCount);
            throw new CommonH5Exception(UserHttpCode.MAX_CODE_DAY_NOT_ALLOW);
        }
        if (totalCount >= 50) {
            logger.error("above limit uid:{} aid:{} totalCount:{} todayCount:{}", uid, aid, totalCount, todayCount);
            throw new CommonH5Exception(UserHttpCode.MAX_CODE_MONTH_NOT_ALLOW);
        }
        InviteFissionVO vo = new InviteFissionVO();
        synchronized (stringPool.intern(getInviteLockKey(aid))) {
            InviteFissionData inviteFissionData = null;
            if (ServerConfig.isProduct()) {
                inviteFissionData = inviteFissionDao.getInviteByAidOrDev(aid, actorData.getTn_id());
            } else {
                inviteFissionData = inviteFissionDao.getOneByAid(aid);
            }
            if (inviteFissionData != null) {
                logger.error("aid or device is use already uid:{} aid:{} tn_id:{}", uid, aid, actorData.getTn_id());
                throw new CommonH5Exception(UserHttpCode.AID_DEV_NOT_ALLOW);
            }
            int now = DateHelper.getNowSeconds();
            InviteFissionData data = new InviteFissionData();
            data.setUid(uid);
            data.setAid(aid);
            if (ServerConfig.isProduct()) {
                data.setDeviceId(actorData.getTn_id());
            }
            data.setRegReward(0);
            data.setTotalReward(0);
            data.setCtime(now);
            data.setMtime(now);
            try {
                inviteFissionDao.insert(data);
            } catch (Exception e) {
                logger.error("insert error msg:{}", e.getMessage(), e);
                throw new CommonH5Exception(UserHttpCode.AID_DEV_NOT_ALLOW);
            }
            BackUserStateData inviteUserData = new BackUserStateData(); // 被邀请者领取数据
            inviteUserData.setUid(aid);
            inviteUserData.setRegStatus(1);
            inviteUserData.setDau2State(0);
            inviteUserData.setDau3State(0);
            inviteUserData.setDau7State(0);
            inviteUserData.setDayNewActiveSet(new HashSet<>());
            inviteUserData.setDayActiveSet(new HashSet<>());
            inviteUserData.setGiftMapStatus(new HashMap<>());
            inviteUserData.setDollarsMapStatus(new HashMap<>());
            backUserStateRedis.saveInvitedUserState(inviteUserData, aid);
            InviterUserStateData yaoQingUserdata = backUserStateRedis.getInvitedUserData(uid, aid); // 邀请者领取数据
            doReportEvent(uid, aid);
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", aid, aid, CommonMqTaskConstant.INVITE_BIND_USER, 1));
            logger.info("bind success uid:{} aid:{} inviteUserData:{}", uid, aid, JSON.toJSONString(inviteUserData));
        }
        return vo;
    }


    private void fillInviteeUserItem(String itemName, List<InviteFissionVO.InviteeUserItemVO> dauList, BackUserStateData backUserStateData, int index) {
        InviteFissionVO.InviteeUserItemVO itemBag = new InviteFissionVO.InviteeUserItemVO();
        itemBag.setItemName(itemName);
        itemBag.setItemState(getState(itemName, backUserStateData));
        itemBag.setItemReward(DAU_BEAN_LIST.get(index));
        dauList.add(itemBag);
    }


    private int getState(String itemName, BackUserStateData backUserStateData) {
        if (DAU_2.equals(itemName)) {
            return backUserStateData.getDau2State();
        } else if (DAU_3.equals(itemName)) {
            return backUserStateData.getDau3State();
        } else if (DAU_7.equals(itemName)) {
            return backUserStateData.getDau7State();
        } else {
            return 0;
        }
    }

    private void setState(String itemName, BackUserStateData backUserStateData, int state) {
        if (DAU_2.equals(itemName)) {
            backUserStateData.setDau2State(state);
        } else if (DAU_3.equals(itemName)) {
            backUserStateData.setDau3State(state);
        } else if (DAU_7.equals(itemName)) {
            backUserStateData.setDau7State(state);
        }
    }

    public BackUserStateData checkInviteUser(String uid) {
        boolean isReg = ActorUtils.isNewRegisterActor(uid, INVITEE_REG_VALID_DAY);
        if (isReg) {
            BackUserStateData invitedUserData = backUserStateRedis.getInvitedUserState(uid);
            if (invitedUserData != null) {
                if (invitedUserData.getDayActiveSet() == null) {
                    invitedUserData.setDayActiveSet(new HashSet<>());
                }
                if (invitedUserData.getGiftMapStatus() == null) {
                    invitedUserData.setGiftMapStatus(new HashMap<>());
                }
                if (invitedUserData.getDollarsMapStatus() == null) {
                    invitedUserData.setDollarsMapStatus(new HashMap<>());
                }
                if (invitedUserData.getDayNewActiveSet() == null) {
                    invitedUserData.setDayNewActiveSet(new HashSet<>());
                }
            }
            return invitedUserData;
        }
        return null;
    }


    public void handleDiamondResourcesOnly(String uid, int beansNum, int diamondType, String title, String diamondDesc) {
        if (beansNum > 0) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(diamondType);
            moneyDetailReq.setChanged(beansNum);
            moneyDetailReq.setTitle(title);
            moneyDetailReq.setDesc(diamondDesc);
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        }
    }


    private void doReportEvent(String uid, String aid) {
        InviteRecordEvent event = new InviteRecordEvent();
        event.setUid(uid);
        event.setInvited_uid(aid);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));

        Map<String, Object> properties = new HashMap<>();
        properties.put("invite_uid", uid);
        eventReport.userSet(new EventDTO(aid, "", properties));
    }

    private String getInviteLockKey(String uid) {
        return "invite:lock:key:" + uid;
    }
}
