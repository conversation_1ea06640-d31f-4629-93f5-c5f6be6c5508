package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.cloud.vision.v1.*;
import com.quhong.analysis.*;
import com.quhong.config.AsyncConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.UserHttpCode;
import com.quhong.constant.UserInfoConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.date.DateSupport;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.InviteFissionDTO;
import com.quhong.data.dto.TempToAidDTO;
import com.quhong.data.dto.UpdateUserInfoDTO;
import com.quhong.data.dto.UserInfoDTO;
import com.quhong.data.vo.*;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.TextDTO;
import com.quhong.dto.VipV2BuyDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IMsgService;
import com.quhong.feign.IVIPService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.room.FollowChangeMsg;
import com.quhong.mysql.dao.UserLabelDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.GreetUserData;
import com.quhong.mysql.data.LabelConfigData;
import com.quhong.redis.BlockRedis;
import com.quhong.redis.UserInfoShowRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.CDNUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.LabelConfigVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


@Component
public class UserOptService {

    private static final Logger logger = LoggerFactory.getLogger(UserOptService.class);
    private static final String LIMIT_REPORT_HEAD = "https://cdn3.qmovies.tv/youstar/reporthead.png";
    private static final String LIMIT_REPORT_NAME = "user";
    private static final int LIMIT_NAME_LEN = 24;
    private static final int LIMIT_DESC_LEN = 100;
    private static final String USER_HEAD_TYPE_NAME = "用户头像";
    private static final String BANNER_TYPE_NAME = "用户banner";
    private static final String ROOM_HEAD_TYPE_NAME = "房间头像";
    private static final String USER_COVER_TYPE_NAME = "用户封面";
    private static final String UPLOAD_PICTURE_ROOKIE = "upload_picture_rookie";
    private static final String FILL_HOBBIES_ROOKIE = "fill_hobbies_rookie";
    private static final String UPDATE_USER_BANNER = "update_user_banner";
    private static final String NOTICE_BODY = "Sorry,your avatar didn't pass the review, please upload your new avatar.";
    private static final String NOTICE_BODY_AR = "المعتذر،لم تنجح صورتك الرمزية في المراجعة، يرجى تحميل الصورة الرمزية الجديد";
    private static final String PICTURE_MSG = "Information has been saved, will be automatically updated after approval.";
    private static final String PICTURE_MSG_AR = "تم حفظ المعلومات ، وسيتم تحديثها تلقائيًا بعد الموافقة.";
    private static final int homeTab = 2;// # 1为进入Crush，2为进入Party-Chat列表，3为Crush和Party-Global交替进入，默认配置为3

    @Resource
    private ActorDao actorDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private FollowDao followDao;
    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private FriendService friendService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private LimitFuncUidDao limitFuncUidDao;
    @Resource
    private CommonDao commonDao;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private IDetectService detectService;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private UserLabelDao userLabelDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private BaseInitData baseInitData;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private MonitorNsfwDao monitorNsfwDao;
    @Resource
    private ImageAnnotatorClient imageAnnotatorClient;
    @Resource
    private UserInfoShowRedis userInfoShowRedis;
    @Resource
    private RoomBlacklistService roomBlacklistService;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private DetectManageApi detectManageApi;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private CreditRiskService creditRiskService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private BackInviteUserService backInviteUserService;
    @Resource
    private InviteUserService inviteUserService;
    @Resource
    private IVIPService iVIPService;
    @Resource
    private GreetUserService greetUserService;
    @Resource
    private ActorCommonService actorCommonService;

    public void addBlackList(String uid, String aid, boolean addRoomBlackList) {
        if (uid.equals(aid)) {
            logger.info("can't black myself uid={} aid={}", uid, aid);
            throw new CommonException(UserHttpCode.BLACKLIST_UNAVAILABLE);
        }
        if (addRoomBlackList) {
            String roomId = RoomUtils.formatRoomId(uid);
            roomBlacklistService.addRoomBlackList(roomId, aid, uid);
            String inRoomId = roomPlayerRedis.getActorRoomStatus(aid);
            if (roomId.equals(inRoomId)) {
                sendKickFromRoom(roomId, uid, aid);
            }
        }
        if (blackListDao.isBlockFromDb(uid, aid)) {
            logger.info("already black uid={} aid={}", uid, aid);
            throw new CommonException(UserHttpCode.BLACKLIST_UNAVAILABLE);
        }
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                TempToAidDTO dto = new TempToAidDTO();
                dto.setUid(uid);
                dto.setAid(aid);
                if (iMsgService.deleteMsg(dto).isError()) {
                    logger.info("delete msg record fail uid={} aid={}", uid, aid);
                }
                blackListDao.addBlock(uid, aid);
                // 8.43自动删除好友关系，及取消关注被拉黑者
                friendService.deleteFriend(uid, aid);
                friendService.cancelFollowNew(uid, aid);
                blockedListLogEventReport(aid, uid, 1);
            }
        });
//        JSONObject msgBody = new JSONObject();
//        msgBody.put("uid", uid);
//        msgBody.put("aid", aid);
//        msgBody.put("item", "block_user");
//        redisTaskService.broadcastMessage("burying_point_follow_friend", msgBody);
    }

    private void blockedListLogEventReport(String aid, String uid, int opType) {
        BlockedListLogEvent event = new BlockedListLogEvent();
        event.setRoom_id(null);
        event.setBlocked_list_action(opType);
        event.setUid(uid);
        event.setTo_uid(aid);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void sendKickFromRoom(String roomId, String fromUid, String toUid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                roomKickRedis.setKickRecord(roomId, fromUid, toUid);
                String path = "kick_actor";
                Map<String, String> params = new HashMap<>();
                params.put("room_id", roomId);
                params.put("from_uid", fromUid);
                params.put("to_uid", toUid);
                roomWebSender.sendPost(path, roomId, params);
            }
        });
    }

    public void delBlackList(String uid, String aid, UserInfoDTO userInfoDTO) {
        if (!blackListDao.delBlock(uid, aid)) {
            logger.info("delBlackList fail uid={} aid={}", uid, aid);
            throw new CommonException(UserHttpCode.NOT_BLOCK_USER);
        }
        if (!AppVersionUtils.versionCheck(843, userInfoDTO)) {
            roomBlacklistService.removeRoomBlackList(RoomUtils.formatRoomId(uid), aid, uid);
        }
//        JSONObject msgBody = new JSONObject();
//        msgBody.put("uid", uid);
//        msgBody.put("aid", aid);
//        msgBody.put("item", "unblock_user");
//        redisTaskService.broadcastMessage("burying_point_follow_friend", msgBody);
        ActorData aidActor = actorDao.getActorDataFromCache(aid);
        if (aidActor != null) {
            FriendClickEvent event = new FriendClickEvent();
            event.setUid(uid);
            event.setFriend_click_uid(aid);
            event.setFriend_click_rid(aidActor.getRid());
            event.setFriend_click_type(6);
            event.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new EventDTO(event));
            blockedListLogEventReport(aid, uid, 2);
        }
    }

    public BlackListVO getBlackList(String uid, int page, int os) {
        int pageSize = os == ClientOS.ANDROID ? 50 : 10;
        List<BlackListData> dataList = blackListDao.findAllDataByPage(uid, page, pageSize);
        BlackListVO listVO = new BlackListVO();
        List<UserBasicInfo> mList = new ArrayList<>();
        for (BlackListData item : dataList) {
            String aid = item.getAid();
            ActorData userInfo = actorDao.getActorDataFromCache(aid);
            if (userInfo == null) {
                logger.error("aid={} not find user info continue", aid);
                continue;
            }
            UserBasicInfo user = new UserBasicInfo();
            user.setName(userInfo.getName());
            user.setViplevel(vipInfoDao.getIntVipLevelFromCache(aid));
            user.setVipMedal(actorCommonService.getCommonVipMedal(aid, user.getViplevel()));
            user.setHead(ImageUrlGenerator.generateRoomUserUrl(userInfo.getHead(), user.getViplevel()));
            user.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
            user.setDesc("");
            user.setGender(userInfo.getFb_gender() == 2 ? 2 : 1);
            user.setCountry(userInfo.getCountry());
            user.setAge(userInfo.getAge());
            user.setRid(userInfo.getRid());
            user.setRidData(userInfo.getRidData());
            user.setValid(userInfo.getValid());
            user.setAid(aid);
            user.setUlvl(userLevelDao.getUserLevel(aid));
            mList.add(user);
        }
        listVO.setList(mList);
        if (dataList.size() < pageSize) {
            listVO.setNextUrl("");
        } else {
            listVO.setNextUrl(String.valueOf(page + 1));
        }
        return listVO;

    }

    public BlackListVO searchBlackList(String uid, String key) {
        BlackListVO listVO = new BlackListVO();
//        List<ActorData> actorDataList = actorDao.searchByKey(key);
        List<ActorData> actorDataList = actorDao.searchActorByRid(key, 1, 50);
        List<UserBasicInfo> mList = new ArrayList<>();
        for (ActorData actorData : actorDataList) {
            BlackListData data = blackListDao.findData(uid, actorData.getUid());
            if (null == data) {
                continue;
            }
            UserBasicInfo user = new UserBasicInfo();
            user.setName(actorData.getName());
            user.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
            user.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), user.getViplevel()));
            user.setGender(actorData.getFb_gender() == 2 ? 2 : 1);
            user.setCountry(actorData.getCountry());
            user.setAge(actorData.getAge());
            user.setRid(actorData.getRid());
            user.setRidData(actorData.getRidData());
            user.setValid(actorData.getValid());
            user.setAid(actorData.getUid());
            user.setUlvl(userLevelDao.getUserLevel(actorData.getUid()));
            mList.add(user);
        }
        listVO.setList(mList);
        listVO.setNextUrl("");
        return listVO;
    }

    public AddFollowVO addFollow(String uid, String aid) {
        if (uid.equals(aid)) {
            logger.info("can't add follow myself uid={} aid={}", uid, aid);
            throw new CommonException(UserHttpCode.USER_NOT_ALLOW);
        }
        UserActorData uidData = actorDao.getActorFromRedis(uid, UserActorData.class);
        UserActorData aidData = actorDao.getActorFromRedis(aid, UserActorData.class);
        if (uidData == null || aidData == null || uidData.getValid() == 0 || aidData.getValid() == 0) {
            logger.info("uid={} or aid={} is not valid", uid, aid);
            throw new CommonException(UserHttpCode.USER_NOT_VALID);
        }

        if (blackListDao.isBlock(aid, uid)) {
            logger.info("aid={} reject uid={}", aid, uid);
            throw new CommonException(UserHttpCode.BLOCKED_BY_ACTOR);
        }
        int maxFollow = 0;
        if (uidData.getFollow_limit() > 0) {
            maxFollow = uidData.getFollow_limit();
        } else {
            maxFollow = followDao.getMaxFollowNum(uid);
        }
        int nowCount = followDao.getFollowingCount(uid);
        if (nowCount >= maxFollow) {
            logger.info("MAX_ACCOUNT_LIMIT uid={} nowCount={} maxFollow={}", uid, nowCount, maxFollow);
            throw new CommonException(UserHttpCode.MAX_ACCOUNT_LIMIT);
        }
        if (followDao.isFollowed(uid, aid)) {
            logger.info("already followed uid={} aid={}", uid, aid);
            throw new CommonException(UserHttpCode.ALREADY_FOLLOWED);
        }
        followDao.addFollowed(uid, aid);
        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(aid, UserLevelConstant.FOLLOWED, uid));
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", aid, aid, CommonMqTaskConstant.FOLLOW_USER, 1));
        AddFollowVO addFollowVO = new AddFollowVO();
        addFollowVO.setFriended(friendsDao.isFriend(uid, aid) ? 1 : 0);
        addFollowVO.setStatus(1);
        addFollowVO.setFollowStatus(friendService.doGetFollowStatus(true, followDao.isFollowed(aid, uid)));
        sendFollowChangeMsg(aid);
        return addFollowVO;
    }

    private void sendFollowChangeMsg(String aid) {
        executor.execute(() -> {
            FollowChangeMsg msg = new FollowChangeMsg();
            msg.setNewFans(followDao.getNewFollowsCount(aid));
            msg.setFollowerStr(String.valueOf(followDao.getFollowsCountByMonGo(aid)));
            roomWebSender.sendPlayerWebMsg(null, aid, aid, msg, true);
        });
    }

    public AddFollowVO cancelFollow(String uid, String aid) {
        followDao.removeFollowed(uid, aid);
        AddFollowVO addFollowVO = new AddFollowVO();
        addFollowVO.setFriended(friendsDao.isFriend(uid, aid) ? 1 : 0);
        addFollowVO.setStatus(0);
        addFollowVO.setFollowStatus(friendService.doGetFollowStatus(false, followDao.isFollowed(uid, aid)));
        return addFollowVO;
    }

    public VisitorConfVO visitorSwitch(String uid, int status) {
        int vip = vipInfoDao.getIntVipLevel(uid);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        int fbGender = actorData.getFb_gender();
        if (vip < UserInfoConstant.VISITOR_NEED_VIP_LEVEL) {
            logger.info("visitorSwitch limit uid={} status={}", uid, status);
            if (fbGender == 2) {
                throw new CommonException(UserHttpCode.FEMALE_VISITOR_VIP_LIMIT);
            } else {
                throw new CommonException(UserHttpCode.MALE_VISITOR_VIP_LIMIT);
            }
        }
        actorDao.updateGeneralConf(uid, "invisible", status);
        VisitorConfVO visitorConfVO = new VisitorConfVO();
        visitorConfVO.setInvisible(status);
        return visitorConfVO;
    }

    public void needUpdateAppInfo(HttpEnvData dto) {
        logger.info("notice update app dto:{}", dto);
        throw new CommonException(UserInfoHttpCode.PARAMETER_UPDATE_APP);
    }

    private LocalDate getLocalDate(String birthday) {
        LocalDate birthDate = null;
        try {
            //去除不可打印字符 比如 \u202C \u202D
            birthday = birthday.replaceAll("[\\p{C}]", "");
            birthDate = DateSupport.parseYyyymd(birthday);
        } catch (DateTimeParseException e) {
            birthDate = DateSupport.parse(birthday);
        }
        return birthDate;
    }


    public UpdateUserInfoVO updateUserInfo(UpdateUserInfoDTO dto) {
        MongoUserActorData userActorData = checkUser(dto);
        int vipLevel = vipInfoDao.getIntVipLevel(dto.getUid());

        String uid = dto.getUid();
        String toName = userActorData.getName();
        String toHead = userActorData.getHead();
        int toAge = userActorData.getAge();
        int toGender = userActorData.getFb_gender();
        String toCountry = userActorData.getCountry();
        String toBirthday = userActorData.getBirthday();
        String toDesc = userActorData.getDesc();
        List<String> toBannerList = userActorData.getBannerList();
        List<Integer> toLabelList = userActorData.getLabelList();
        String toVideoUrl = userActorData.getVideoUrl();

        int fillDone = userActorData.getFillStatus("fill_done", 0);
        int fillGender = userActorData.getFillStatus("fill_gender", 0);
        int fillAlbum = userActorData.getFillStatus("fill_album", 0);
        int fillInterests = userActorData.getFillStatus("fill_interests", 0);
        int fillAboutMe = userActorData.getFillStatus("fill_about_me", 0);

        UpdateUserInfoData updateInfo = new UpdateUserInfoData();
        updateInfo.setUid(uid);
        updateInfo.setOs(dto.getOs());
        updateInfo.setVersioncode(dto.getVersioncode());
        updateInfo.setSlang(dto.getSlang());
        updateInfo.setGeneral_conf(userActorData.getGeneral_conf());
        updateInfo.setAppPackageName(userActorData.getAppPackageName());

        int score = creditRiskService.getCreditRiskScoreByUid(dto.getUid(), userActorData.getTn_id(), userActorData.getIp());
        if (score < 300) {
            logger.info("actor is risk invalid score={} uid={} rid={}", score, dto.getUid(), userActorData.getRid());
            throw new CommonException(UserHttpCode.USER_RISK_INVALID_UPDATE_INFO);
        }

        if (!StringUtils.isEmpty(dto.getName())) {
            String name = dto.getName().trim();
            if (!name.equals(userActorData.getName())) {
                boolean detectFlag = false;
                if (StringUtils.isEmpty(name) || name.indexOf("\n") > 0 || name.toLowerCase().contains("vip")
                        || name.length() > LIMIT_NAME_LEN || !(detectFlag = detectText(name, DetectOriginConstant.USER_INFO_NAME, uid))) {
                    creditRiskService.asyncCreditRisk(uid, userActorData.getTn_id(), userActorData.getIp(), CreditRiskService.TYPE_USER_PROFILE, 200, 10, 5);
                    logger.info("name is invalid detectFlag={} dto={}", detectFlag, dto);
                    throw new CommonException(UserHttpCode.CODE_NAME_NOT_ALLOW);
                }
                LimitFuncUidData limitFuncUidData = limitFuncUidDao.findData(uid);
                if (limitFuncUidData != null && limitFuncUidData.getName_type() > 0 && !LIMIT_REPORT_NAME.equals(name)) {
                    logger.info("limit function name dto={}", dto);
                    throw new CommonException(UserHttpCode.CODE_NOT_ALLOW_CHANGE);
                }
                toName = name;
                updateInfo.setName(name);
                if (fillDone != 1) {
                    updateInfo.setFill_done(1);
                    updateInfo.setUpdateGeneralConf(true);
                }
            }
        }

        if (!StringUtils.isEmpty(dto.getDesc())) {
            String desc = dto.getDesc().trim();
            if (!desc.equals(userActorData.getDesc())) {
                boolean detectFlag = false;
                if (desc.length() > LIMIT_DESC_LEN || !(detectFlag = detectText(desc, DetectOriginConstant.USER_INFO_DEAS, uid))) {
                    creditRiskService.asyncCreditRisk(uid, userActorData.getTn_id(), userActorData.getIp(), CreditRiskService.TYPE_USER_PROFILE, 200, 10, 5);
                    logger.info("desc is invalid detectFlag={} dto={}", detectFlag, dto);
                    throw new CommonException(UserHttpCode.CODE_DESC_NOT_ALLOW);
                }
                toDesc = desc;
                updateInfo.setDesc(desc);
            }
            if (fillAboutMe != 1) {
                updateInfo.setFill_about_me(1);
                updateInfo.setUpdateGeneralConf(true);
            }

        }

        List<String> bannerList = dto.getBanner();

        if (!StringUtils.isEmpty(dto.getHead())) {
            String myHeadUrl = dto.getHead().startsWith("http") ? CDNUtils.getHttpCdnUrl(dto.getHead()) :
                    ImageUrlGenerator.createCdnUrl(dto.getHead());
            dto.setHeadIndex(0);
            toHead = setHead(myHeadUrl, dto, userActorData, updateInfo);

            if (AppVersionUtils.versionCheck(844, dto)) {
                String oldCover = userActorData.getCover();
                String oldHead = userActorData.getHead();
                int len = 0;
                // 刚刚升级844，第一次仅更改头像执行
                if (bannerList == null && !StringUtils.isEmpty(oldCover) && !oldHead.equals(oldCover)
                        && !CollectionUtils.isEmpty(toBannerList) && (len = toBannerList.size()) <= UserInfoConstant.MAX_BANNER_SIZE
                        && !toBannerList.contains(oldCover)) {
                    bannerList = new ArrayList<>(toBannerList);
                    if (len < UserInfoConstant.MAX_BANNER_SIZE) {
                        bannerList.add(0, oldCover);
                        logger.info("844 update info add cover success uid:{} oldHead:{} oldCover:{} ", dto.getUid(), oldHead, oldCover);
                    } else {
                        // 可能me接口已经转换过满8张移除head，但是用户844版本更改只改了头像
                        boolean isRemoveHeadSuccess = bannerList.remove(oldHead);
                        if (isRemoveHeadSuccess) {
                            bannerList.add(0, oldCover);
                            logger.info("844 update info add cover success uid:{} oldHead:{} oldCover:{} isRemoveHead:{}", dto.getUid(), oldHead, oldCover, isRemoveHeadSuccess);
                        } else {
                            bannerList = null;
                            logger.info("844 update info add cover fail uid:{} oldHead:{} oldCover:{}  isRemoveHead:{}", dto.getUid(), oldHead, oldCover, isRemoveHeadSuccess);
                        }
                    }
                }
                if (!toHead.equals(oldCover)) {
                    updateInfo.setCover(toHead);
                    logger.info("updateHead change_cover_to_head uid:{} old_cover:{} to_head:{}", uid, oldCover, toHead);
                }
            }
        }


        List<String> updateBannerList = new ArrayList<>();
        List<String> allBannerList = new ArrayList<>();
        if (bannerList != null) {
            if (AppVersionUtils.versionCheck(844, dto)) {
                if (!toHead.equals(userActorData.getCover())) {
                    //刚刚升级844，第一次更改相册执行  844兼容以前版本banner图片拼接cover，844没有cover了，这里设置为head,
                    logger.info("updateBanner change_cover_to_head uid:{} old_cover:{} to_head:{}", uid, userActorData.getCover(), toHead);
                    updateInfo.setCover(toHead);
                }
            }

            if (bannerList.isEmpty()) {
                if (AppVersionUtils.versionCheck(844, dto)) {
                    // 删除所有banner
                    updateInfo.setBanner(allBannerList);
                    toBannerList = allBannerList;
                }
            } else {
                // 去掉？后面的参数
                List<String> simpleBannerList = bannerList.stream().map(url -> {
                    if (!url.startsWith("http")) {
                        return url;
                    }
                    return CDNUtils.getHttpCdnUrl(url);
                }).collect(Collectors.toList());

                if (AppVersionUtils.versionCheck(825, dto) && !AppVersionUtils.versionCheck(844, dto)) {
                    String cover = simpleBannerList.get(0);
                    if (!cover.equals(userActorData.getCover())) {
                        if (!cover.startsWith("http")) {
                            cover = ImageUrlGenerator.createCdnUrl(cover);
                        }
                        updateInfo.setCover(cover);
                    }
                    String head = dto.getHeadIndex() == 0 ? cover : simpleBannerList.get(dto.getHeadIndex());
                    toHead = setHead(head, dto, userActorData, updateInfo);
                    simpleBannerList.remove(0);
                }

                for (String item : simpleBannerList) {
                    String urlItem;
                    if (!item.startsWith("http")) {
                        urlItem = ImageUrlGenerator.createCdnUrl(item);
                        updateBannerList.add(urlItem);
                    } else {
                        urlItem = item;
                    }
                    allBannerList.add(urlItem);
                }

                // 兼容设置10, 实际只能设置8
                if (allBannerList.size() > 10) {
                    throw new CommonException(UserHttpCode.CODE_DESC_NOT_ALLOW.getCode(), "عدد الصور التي تقوم بتحميلها يتجاوز الحد المسموح به");
                }

                updateInfo.setUpdateBanners(updateBannerList);
                updateInfo.setBanner(allBannerList);
                toBannerList = allBannerList;
                if (fillAlbum != 1) {
                    updateInfo.setFill_album(1);
                    updateInfo.setUpdateGeneralConf(true);
                }
            }
        }

        toLabelList = updateLabelList(dto, updateInfo, toLabelList);
        if (!CollectionUtils.isEmpty(toLabelList) && fillInterests != 1) {
            updateInfo.setFill_interests(1);
            updateInfo.setUpdateGeneralConf(true);
        }

        String birthday = dto.getBirthday();
        if (!StringUtils.isEmpty(birthday) && !birthday.equals(userActorData.getBirthday())) {
            birthday = handleArbNum(birthday);
            LocalDate nowDate = DateSupport.ARABIAN.getToday();
            LocalDate birthDate = getLocalDate(birthday);
            int age = birthDate.until(nowDate).getYears();
            if (age >= 18) {
                updateInfo.setBirthday(birthday);
                updateInfo.setAge(age);
                toBirthday = birthday;
                toAge = age;
            } else {
                logger.info("Age should be over 18 years old  age={} dto={}", age, dto);
                CommonException exception = new CommonException(UserHttpCode.CODE_OVER_18_YEARS_OLD);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("birthday", birthday);
                exception.setData(jsonObject);
                throw exception;
            }
        }

        if (dto.getGender() == 1 || dto.getGender() == 2) {
            if (fillGender == 1) {
                logger.info("You Cannot Modify Gender dto={}", dto);
                throw new CommonException(UserHttpCode.CODE_CANNOT_MODIFY_GENDER);
            }

            // if (AppVersionUtils.versionCheck(864, dto) && toGender != dto.getGender()) {
            //     VipV2BuyDTO vipV2BuyDTO = new VipV2BuyDTO();
            //     vipV2BuyDTO.setUid(uid);
            //     vipV2BuyDTO.setNewGender(dto.getGender());
            //     iVIPService.vipChange(vipV2BuyDTO);
            // }
            toGender = dto.getGender();
            updateInfo.setFb_gender(dto.getGender());
            updateInfo.setFill_gender(1);
            updateInfo.setUpdateGeneralConf(true);
        }

        if (!StringUtils.isEmpty(dto.getCity()) && !dto.getCity().equals(userActorData.getCity())) {
            updateInfo.setCity(dto.getCity().trim());
        }

        // 修改国家
        Long modifyCountry = null;
        if (!StringUtils.isEmpty(dto.getCountry()) && !dto.getCountry().equals(userActorData.getCountry())) {
            // boolean isVer864 = AppVersionUtils.versionCheck(864, dto);
            // if (isVer864) {
            //     modifyCountry = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.MODIFY_COUNTRY, 0);
            //     if (modifyCountry <= 0){
            //         logger.info("You Cannot Modify Country dto={}", JSONObject.toJSONString(dto));
            //         throw new CommonException(UserHttpCode.VIP_LEVEL_ENABLE_MODIFY_COUNTRY);
            //     }
            // }
            updateInfo.setCountry(dto.getCountry().trim());
            mongoRoomDao.updateField(RoomUtils.formatRoomId(uid), "country", updateInfo.getCountry());
            toCountry = updateInfo.getCountry();
            String changeCountry = String.format("%s-%s",
                    ActorUtils.getCountryCode(userActorData.getCountry())
                    , ActorUtils.getCountryCode(toCountry));
            // 资料卡更改国家
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(updateInfo.getUid(), "", changeCountry, "",
                    CommonMqTaskConstant.PERSONAL_UPDATE_COUNTRY, 1));

            // if (isVer864){
            //     modifyCountry = modifyCountry - 1;
            //     actorConfigDao.updateUserConfig(uid, ActorConfigDao.MODIFY_COUNTRY, modifyCountry);
            //     doPermissionReport(uid, vipLevel);
            // }
        }

        if (!StringUtils.isEmpty(dto.getPhone()) && !dto.getPhone().equals(userActorData.getPhone())) {
            updateInfo.setPhone(dto.getPhone().trim());
        }

        if ((dto.getNlang() == 1 || dto.getNlang() == 2) && dto.getNlang() != userActorData.getNlang()) {
            updateInfo.setNlang(dto.getNlang());
            updateInfo.setArea(dto.getNlang());
        }
        int vipOnlineSwitch = commonConfig.getSwitchConfigValue(CommonConfig.VIP_ONLINE_SWITCH_KEY, 0);
        long toShowVideoSwitch = 1;

        if (vipOnlineSwitch == 1 && AppVersionUtils.versionCheck(844, dto)) {
            Long showVideoSwitch = dto.getShowVideoSwitch();
            toShowVideoSwitch = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.SHOW_VIDEO_IN_DETAIL, 0);
            if (showVideoSwitch != null && showVideoSwitch != toShowVideoSwitch) {
                if (showVideoSwitch == 1) {
                    if (vipLevel < UserInfoConstant.VISITOR_NEED_VIP_LEVEL) {
                        logger.info("video switch limit level 5 uid={} vipLevel={}", dto.getUid(), vipLevel);
                        if (toGender == 2) {
                            throw new CommonException(UserHttpCode.VIP5_LIMIT_VIDEO, UserInfoConstant.VISITOR_NEED_VIP_LEVEL);
                        } else {
                            throw new CommonException(UserHttpCode.VIP5_LIMIT_VIDEO, UserInfoConstant.VISITOR_NEED_VIP_LEVEL);
                        }
                    }
                }
                actorConfigDao.updateUserConfig(uid, ActorConfigDao.SHOW_VIDEO_IN_DETAIL, showVideoSwitch);
                toShowVideoSwitch = showVideoSwitch;
            }
            if (!StringUtils.isEmpty(dto.getVideoUrl())) {
                String videoUrl = dto.getVideoUrl().startsWith("http") ? CDNUtils.getHttpCdnUrl(dto.getVideoUrl()) :
                        ImageUrlGenerator.createCdnUrl(dto.getVideoUrl());
                if (!videoUrl.equals(toVideoUrl)) {
                    if (vipLevel < UserInfoConstant.VISITOR_NEED_VIP_LEVEL) {
                        logger.info("video limit level 5 uid={} vipLevel={}", dto.getUid(), vipLevel);
                        if (toGender == 2) {
                            throw new CommonException(UserHttpCode.FEMALE_VISITOR_VIP_LIMIT);
                        } else {
                            throw new CommonException(UserHttpCode.MALE_VISITOR_VIP_LIMIT);
                        }
                    }
                    updateInfo.setVideoUrl(videoUrl);
                    toVideoUrl = videoUrl;
                }
            } else {
                if (dto.getVideoUrl() != null && !StringUtils.isEmpty(toVideoUrl)) {
                    //删除视频
                    updateInfo.setVideoUrl("");
                    toVideoUrl = "";
                }
            }
        }

        actorDao.updateActorUserInfo(uid, updateInfo);
        doEditReport(updateInfo);

        UpdateUserInfoVO vo = new UpdateUserInfoVO();
        vo.setName(toName);
        vo.setHead(toHead);
        vo.setAge(toAge);
        vo.setGender(toGender);
        vo.setCountry(toCountry);
        vo.setBirthday(toBirthday);
        vo.setDesc(toDesc);
        vo.setStarSign(userInfoService.getConstellation(toBirthday));
        vo.setShowVideoSwitch(toShowVideoSwitch);
        vo.setImgVideoUrl(StringUtils.isEmpty(toVideoUrl) ? "" : ImageUrlGenerator.generateSnapshotUrl(toVideoUrl, 0, 0));
        vo.setVideoUrl(StringUtils.isEmpty(toVideoUrl) ? "" : toVideoUrl);
        vo.setModifyCountry(modifyCountry);

        MongoUserActorData completedUserData = new MongoUserActorData();
        completedUserData.setBannerList(toBannerList);
        completedUserData.setLabelList(toLabelList);
        completedUserData.setHead(toHead);
        completedUserData.setDesc(toDesc);
        completedUserData.setName(toName);
        completedUserData.setBirthday(toBirthday);
        completedUserData.setCountry(toCountry);
        int progress = userInfoService.completedProgress(completedUserData);
        vo.setProgress(progress);
        if (!StringUtils.isEmpty(updateInfo.getHead()) || !CollectionUtils.isEmpty(updateInfo.getUpdateBanners())) {
            vo.setPicture_msg(dto.getSlang() == SLangType.ENGLISH ? PICTURE_MSG : PICTURE_MSG_AR);
            updateInfoTask(updateInfo, toGender, toHead, userActorData, progress);
        } else if (!StringUtils.isEmpty(updateInfo.getCover()) || !CollectionUtils.isEmpty(updateInfo.getLabel_list())
                || !StringUtils.isEmpty(updateInfo.getName())) {
            updateInfoTask(updateInfo, toGender, toHead, userActorData, progress);
        }
        if (!StringUtils.isEmpty(updateInfo.getVideoUrl())) {
            long start = System.currentTimeMillis();
            detectManageApi.asyncSafeVideo(uid, updateInfo.getVideoUrl(), (isSafeText, newVideoUrl) -> {
                boolean isChange = !updateInfo.getVideoUrl().equals(newVideoUrl);
                if (isChange) {
                    actorDao.updateVideoUrl(uid, newVideoUrl);
                }
                logger.info("updateVideoUrl done uid:{} oldUrl:{} newVideoUrl:{} isChange:{} cost:{}", uid, updateInfo.getVideoUrl(), newVideoUrl, isChange, System.currentTimeMillis() - start);
            });
        }
//        updateInfoTask(updateInfo, toGender, toHead, toName);
        updateGreetUser(updateInfo, progress);

        logger.info("updateInfo success uid:{} updateInfo:{} vo:{}", uid, updateInfo, vo);
        return vo;
    }

    private void doEditReport(UpdateUserInfoData userInfoData) {
        EditInformationLogEvent logEvent = new EditInformationLogEvent();
        logEvent.setUid(userInfoData.getUid());
        logEvent.setCtime(DateHelper.getNowSeconds());
        if (!StringUtils.isEmpty(userInfoData.getHead())) {
            logEvent.setEdit_information_type(1);
            eventReport.track(new EventDTO(logEvent));
        }
        if (!StringUtils.isEmpty(userInfoData.getName())) {
            logEvent.setEdit_information_type(2);
            eventReport.track(new EventDTO(logEvent));
        }
        if (userInfoData.getFb_gender() != null) {
            logEvent.setEdit_information_type(3);
            eventReport.track(new EventDTO(logEvent));
        }
        if (!StringUtils.isEmpty(userInfoData.getBirthday())) {
            logEvent.setEdit_information_type(4);
            eventReport.track(new EventDTO(logEvent));
        }
        if (!StringUtils.isEmpty(userInfoData.getCountry())) {
            logEvent.setEdit_information_type(5);
            eventReport.track(new EventDTO(logEvent));
        }
        if (userInfoData.getBanner() != null) {
            logEvent.setEdit_information_type(6);
            eventReport.track(new EventDTO(logEvent));
        }
        if (!StringUtils.isEmpty(userInfoData.getVideoUrl())) {
            logEvent.setEdit_information_type(7);
            eventReport.track(new EventDTO(logEvent));
        }
        if (userInfoData.isChangeLabelList()) {
            logEvent.setEdit_information_type(8);
            eventReport.track(new EventDTO(logEvent));
        }
        if (!StringUtils.isEmpty(userInfoData.getDesc())) {
            logEvent.setEdit_information_type(9);
            eventReport.track(new EventDTO(logEvent));
        }
    }

    // 使用vip特权修改资料时上报
    private void doPermissionReport(String uid, int vipLevel) {
        PermissionUsageEvent logEvent = new PermissionUsageEvent();
        logEvent.setUid(uid);
        logEvent.setPermission_name("Edit Profile");
        logEvent.setPermission_name("vip permission");
        logEvent.setUse_permission_status(vipLevel > 0 ? "vip" + vipLevel : "");
        logEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(logEvent));
    }


    public UpdateUserInfoVO improveUserInfo(UpdateUserInfoDTO dto) {
        MongoUserActorData userActorData = checkUser(dto);

        String uid = dto.getUid();
        String toName = userActorData.getName();
        String toHead = userActorData.getHead();
        int toAge = userActorData.getAge();
        int toGender = userActorData.getFb_gender();
        String toCountry = userActorData.getCountry();
        String toBirthday = userActorData.getBirthday();

        int toFillDone = userActorData.getFillStatus("fill_done", 0);
        int fillGender = userActorData.getFillStatus("fill_gender", 0);

        UpdateUserInfoData updateInfo = new UpdateUserInfoData();
        updateInfo.setUid(uid);
        updateInfo.setOs(dto.getOs());
        updateInfo.setVersioncode(dto.getVersioncode());
        updateInfo.setSlang(dto.getSlang());
        updateInfo.setGeneral_conf(userActorData.getGeneral_conf());
        updateInfo.setAppPackageName(userActorData.getAppPackageName());

        if (!StringUtils.isEmpty(dto.getName())) {
            String name = dto.getName().trim();
            if (StringUtils.isEmpty(name)) {
                logger.info("name is empty invalid dto={}", dto);
                throw new CommonException(UserHttpCode.CODE_NAME_NOT_ALLOW);
            }
            if (!name.equals(userActorData.getName())) {
                boolean detectFlag = false;
                if (name.indexOf("\n") > 0 || name.toLowerCase().contains("vip")
                        || name.length() > LIMIT_NAME_LEN || !(detectFlag = detectText(name, DetectOriginConstant.USER_INFO_NAME, uid))) {
                    logger.info("name is invalid detectFlag={} dto={}", detectFlag, dto);
                    throw new CommonException(UserHttpCode.CODE_NAME_NOT_ALLOW);
                }
                LimitFuncUidData limitFuncUidData = limitFuncUidDao.findData(uid);
                if (limitFuncUidData != null && limitFuncUidData.getName_type() > 0 && !LIMIT_REPORT_NAME.equals(name)) {
                    logger.info("limit function name dto={}", dto);
                    throw new CommonException(UserHttpCode.CODE_NOT_ALLOW_CHANGE);
                }
                toName = name;
                updateInfo.setName(name);
                if (toFillDone != 1) {
                    toFillDone = 1;
                    updateInfo.setFill_done(1);
                    updateInfo.setUpdateGeneralConf(true);
                }
            }

        }

        if (!StringUtils.isEmpty(dto.getHead())) {
            toHead = setHead(dto.getHead().trim(), dto, userActorData, updateInfo);
        }

        String birthday = dto.getBirthday();
        if (!StringUtils.isEmpty(birthday) && !birthday.equals(userActorData.getBirthday())) {
            birthday = handleArbNum(birthday);
            if (birthday.startsWith("19") || birthday.startsWith("20")) {
                LocalDate nowDate = DateSupport.ARABIAN.getToday();
                LocalDate birthDate = getLocalDate(birthday);
                int age = birthDate.until(nowDate).getYears();
                if (age >= 18) {
                    updateInfo.setBirthday(birthday);
                    updateInfo.setAge(age);
                    toBirthday = birthday;
                    toAge = age;
                } else {
                    logger.info("Age should be over 18 years old  age={} dto={}", age, dto);
                    CommonException exception = new CommonException(UserHttpCode.CODE_OVER_18_YEARS_OLD);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("birthday", birthday);
                    exception.setData(jsonObject);
                    throw exception;
                }
            } else {
                logger.info("birthday={} is invalid dto={}", birthday, dto);
                throw new CommonException(UserHttpCode.PARAM_ERROR);
            }
        }
        if (fillGender == 0) {
            if (dto.getGender() == 1 || dto.getGender() == 2) {
                toGender = dto.getGender();
                updateInfo.setFb_gender(dto.getGender());
                updateInfo.setFill_gender(1);
                updateInfo.setUpdateGeneralConf(true);
            }
        }

        if (!StringUtils.isEmpty(dto.getCountry()) && !dto.getCountry().equals(userActorData.getCountry())) {
            updateInfo.setCountry(dto.getCountry().trim());
            toCountry = dto.getCountry();
        } else {
            if (StringUtils.isEmpty(userActorData.getCountry())) {
                toCountry = "SA_Saudi Arabia";
                updateInfo.setCountry(toCountry);
            }
        }

        updateInfo.setNlang(2);
        updateInfo.setArea(2);
        updateInfo.setImprove_data(1);

        actorDao.updateActorUserInfo(uid, updateInfo);

        int inviteCodeState = 1;
        String inviteCode = dto.getInviteCode();
        if (StringUtils.hasLength(inviteCode)) {
            InviteFissionDTO req = new InviteFissionDTO();
            req.setUid(uid);
            req.setInviteCode(inviteCode);
            try {
                inviteUserService.bindUidByCode(req);
            } catch (CommonH5Exception e) {
                inviteCodeState = 0;
            }
        }

        UpdateUserInfoVO vo = new UpdateUserInfoVO();
        vo.setName(toName);
        vo.setHead(toHead);
        vo.setAge(toAge);
        vo.setGender(toGender);
        vo.setNlang(2);
        vo.setUlvl(userLevelDao.getUserLevel(uid));
        vo.setViplevel(vipInfoDao.getIntVipLevel(uid));
        vo.setCountry(toCountry);
        vo.setHome_tab(homeTab);
        vo.setFillDone(toFillDone);
        vo.setInviteCodeState(inviteCodeState);
        int progress = userInfoService.completedProgress(userActorData);
        if (!StringUtils.isEmpty(updateInfo.getHead()) || !StringUtils.isEmpty(updateInfo.getName())) {
            updateInfoTask(updateInfo, toGender, toHead, userActorData, progress);
        }
//        updateInfoTask(updateInfo, toGender, toHead, toName);
        updateGreetUser(updateInfo, progress);
        logger.info("improveInfo success uid:{} updateInfo:{} vo:{}", uid, updateInfo, vo);
        return vo;
    }


    public void labelUpdate(UpdateUserInfoDTO dto) {
        MongoUserActorData userActorData = checkUser(dto);

        String uid = dto.getUid();
        String toHead = userActorData.getHead();
        String toName = userActorData.getName();
        int toGender = userActorData.getFb_gender();
        List<Integer> toLabelList = userActorData.getLabelList();

        UpdateUserInfoData updateInfo = new UpdateUserInfoData();
        updateInfo.setUid(uid);
        updateInfo.setOs(dto.getOs());
        updateInfo.setVersioncode(dto.getVersioncode());
        updateInfo.setSlang(dto.getSlang());
        updateInfo.setGeneral_conf(userActorData.getGeneral_conf());
        updateInfo.setAppPackageName(userActorData.getAppPackageName());
        toLabelList = updateLabelList(dto, updateInfo, toLabelList);

        actorDao.updateActorUserInfo(uid, updateInfo);

        int progress = userInfoService.completedProgress(userActorData);
        updateInfoTask(updateInfo, toGender, toHead, userActorData, progress);
        updateGreetUser(updateInfo, progress);
        logger.info("labelUpdate success uid:{} updateInfo:{} ", uid, updateInfo);
    }


    private String setHead(String head, UpdateUserInfoDTO dto, MongoUserActorData userActorData, UpdateUserInfoData updateInfo) {
        String uid = dto.getUid();
        String toHead = userActorData.getHead();
        if (!head.equals(userActorData.getHead())) {
            LimitFuncUidData limitFuncUidData = limitFuncUidDao.findData(uid);
            if (limitFuncUidData != null && limitFuncUidData.getHead_type() > 0 && !LIMIT_REPORT_HEAD.equals(head)) {
                logger.info("limit function head dto={}", dto);
                throw new CommonException(UserHttpCode.CODE_NOT_ALLOW_CHANGE);
            }
            if (roomKickRedis.getReportLevel(uid) > 0) {
                logger.info("admin report head can not change dto={}", dto);
                throw new CommonException(UserHttpCode.CODE_HEAD_NOT_ALLOW);
            }

            if (!head.startsWith("http")) {
                head = ImageUrlGenerator.createCdnUrl(head);
            } else {
                head = CDNUtils.getHttpCdnUrl(head);
            }
            toHead = head;
            updateInfo.setHead(head);
            updateInfo.setIs_face(2);
        }
        updateInfo.setHeadIndex(dto.getHeadIndex());
        updateInfo.setUpdateGeneralConf(true);
        return toHead;
    }

    private void updateInfoTask(UpdateUserInfoData updateInfo, int gender, String toHead, MongoUserActorData userActorData, int progress) {
        BaseTaskFactory.getFactory().add(new Task() {
            @Override
            protected void execute() {
                try {
                    String uid = updateInfo.getUid();
                    int os = updateInfo.getOs();
                    int versioncode = updateInfo.getVersioncode();
                    boolean isUploadPicture = false;
                    boolean isHeadSystemUrl = false;
                    boolean isHeadSendGoogle = false;
                    List<String> allReachedList = null;
                    UpdateUserInfoData newUserInfo = new UpdateUserInfoData();
                    newUserInfo.setGeneral_conf(updateInfo.getGeneral_conf());
                    Set<String> unSafeHeadSet = new HashSet<>();

                    if (!StringUtils.isEmpty(updateInfo.getHead())) {
                        isHeadSystemUrl = baseInitData.isSystemUrl(updateInfo.getHead());
                        if (!isHeadSystemUrl) {
                            isHeadSendGoogle = true;
                            if (detectImage(updateInfo.getHead(), DetectOriginConstant.USER_HEAD, uid)) {
                                isUploadPicture = true;
                                userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UPLOAD_PICTURE_ROOKIE, os, versioncode));
                            } else {
                                String randomHead = baseInitData.generateSysRandomHead(gender);
                                newUserInfo.setHead(randomHead);
                                newUserInfo.setIs_face(0);
                                newUserInfo.setUpdateGeneralConf(true);
                                newUserInfo.setHeadIndex(0);
                                newUserInfo.setCover(randomHead);
                                newUserInfo.setSetCover(true);
                                sendNotice(uid, updateInfo.getHead(), updateInfo.getSlang());
                                monitorNsfwDao.save(new MonitorNsfwData(9, uid, updateInfo.getHead(), 1, 0, DateHelper.getNowSeconds()));
                                creditRiskService.creditRisk(uid, userActorData.getTn_id(), userActorData.getIp(), CreditRiskService.TYPE_USER_IMG, 300, 20, 10);
                                unSafeHeadSet.add(updateInfo.getHead());
                                logger.info("detectImage is not safe change to default uid:{} type_name:{} url:{}", uid, USER_HEAD_TYPE_NAME, updateInfo.getHead());
                            }
                        }
                    }

                    if (!StringUtils.isEmpty(updateInfo.getCover()) && !updateInfo.getCover().equals(updateInfo.getHead())) {
                        if (!detectImage(updateInfo.getCover(), DetectOriginConstant.USER_BACKGROUND, uid)) {
                            newUserInfo.setCover("");
                            newUserInfo.setSetCover(true);
                            unSafeHeadSet.add(updateInfo.getCover());
                            logger.info("detectImage is not safe change to default uid:{} type_name:{} url:{}", uid, USER_COVER_TYPE_NAME, updateInfo.getCover());
                        }
                    }
                    if (!CollectionUtils.isEmpty(updateInfo.getBanner())) {
                        dailyTaskService.sendToMq(new DailyTaskMqData(uid, 18, DateHelper.ARABIAN.formatDateInDay2(), updateInfo.getBanner().size()));
                    }
                    if (!CollectionUtils.isEmpty(updateInfo.getUpdateBanners())) {
                        List<String> oldBannerList = updateInfo.getBanner();
                        List<String> deleteBannerList = new ArrayList<>();
                        for (String item : updateInfo.getUpdateBanners()) {
                            if (!baseInitData.isSystemUrl(item)) {
                                if (item.equals(updateInfo.getHead()) || item.equals(updateInfo.getCover())) {
                                    if (unSafeHeadSet.contains(item)) {
                                        deleteBannerList.add(item);
                                        monitorNsfwDao.save(new MonitorNsfwData(9, uid, item, 2, 0, DateHelper.getNowSeconds()));
                                        logger.info("detectImage banner head is not safe change to default uid:{} type_name:{} url:{}", uid, BANNER_TYPE_NAME, item);
                                    }
                                } else if (!detectImage(item, DetectOriginConstant.USER_BACKGROUND, uid)) {
                                    deleteBannerList.add(item);
                                    monitorNsfwDao.save(new MonitorNsfwData(9, uid, item, 2, 0, DateHelper.getNowSeconds()));
                                    logger.info("detectImage banner is not safe change to default uid:{} type_name:{} url:{}", uid, BANNER_TYPE_NAME, item);
                                }
                            }
                        }
                        if (!CollectionUtils.isEmpty(deleteBannerList)) {
                            List<String> nowBannerList = oldBannerList.stream().filter(a -> !deleteBannerList.contains(a)).collect(Collectors.toList());
                            if (updateInfo.getUpdateBanners().size() > deleteBannerList.size()) {
                                isUploadPicture = true;
                                userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UPDATE_USER_BANNER, os, versioncode));
                            }
                            newUserInfo.setBanner(nowBannerList);
                            if (newUserInfo.getHeadIndex() == null && !StringUtils.isEmpty(toHead)) {
                                int myHeadIndex = nowBannerList.indexOf(toHead);
                                newUserInfo.setUpdateGeneralConf(true);
                                newUserInfo.setHeadIndex(myHeadIndex >= 0 ? myHeadIndex + 1 : 0);
                            }
                            creditRiskService.creditRisk(uid, userActorData.getTn_id(), userActorData.getIp(), CreditRiskService.TYPE_USER_IMG, 300, 20, 10);
                            logger.info("detectImage is not safe change to default uid:{} type_name:{} deleteBannerList:{} oldSize:{} nowSize:{}",
                                    uid, BANNER_TYPE_NAME, deleteBannerList, oldBannerList.size(), nowBannerList.size());
                        } else {
                            isUploadPicture = true;
                            userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UPDATE_USER_BANNER, os, versioncode));
                        }
                    }
                    if (newUserInfo.isSetCover() || !StringUtils.isEmpty(newUserInfo.getHead()) ||
                            newUserInfo.getBanner() != null) {
                        actorDao.updateUnSafePic(uid, newUserInfo);
                    }

                    if (isUploadPicture) {
                        int lenBannerSize = CollectionUtils.isEmpty(updateInfo.getBanner()) ? 0 : updateInfo.getBanner().size();
                        if (!isHeadSystemUrl && lenBannerSize >= 8) {
                            allReachedList = getAllReached(uid, allReachedList);
                            if (!allReachedList.contains(UserLevelConstant.UPLOAD_PICTURE)) {
                                userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.UPLOAD_PICTURE));
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(updateInfo.getLabel_list())) {
                        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, FILL_HOBBIES_ROOKIE, os, versioncode));
                        dailyTaskService.sendToMq(new DailyTaskMqData(uid, 19, DateHelper.ARABIAN.formatDateInDay2(), updateInfo.getLabel_list().size()));
                        allReachedList = getAllReached(uid, allReachedList);
                        if (!allReachedList.contains(UserLevelConstant.FILL_HOBBIES)) {
                            userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.FILL_HOBBIES));
                        }
                    }
                    if (isHeadSendGoogle) {
                        int index = ThreadLocalRandom.current().nextInt(10); // 0-9
                        if (index < 5 && userInfoShowRedis.incrementCountMont() < 70000) {
                            detectGoogleSafeSearchUrl(updateInfo.getHead());
                        }
                    }
//                    MySQLActorData data = new MySQLActorData();
//                    data.setActorUid(uid);
//                    data.setFbGender(gender);
//                    data.setHead(toHead);
//                    data.setName(toName);
//                    data.setVersionCode(updateInfo.getVersioncode());
//                    data.setAppPackageName(updateInfo.getAppPackageName());
//                    mySQLActorService.updateBaseInfo(data);
                    if (progress >= 100) {
                        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(updateInfo.getUid(), "", "", "", CommonMqTaskConstant.UPDATE_PERSONAL_INFO, 1));
                    }
                    logger.info("updateInfoTask success uid:{} newUserInfo:{}", updateInfo.getUid(), newUserInfo);
                } catch (Exception e) {
                    logger.error("updateInfoTask error, updateInfo={} error msg={}", updateInfo, e.getMessage(), e);
                }

            }
        });

    }

    private void updateGreetUser(UpdateUserInfoData updateInfo, int progress) {
        GreetUserData updateGreetUser = new GreetUserData();
        updateGreetUser.setUid(updateInfo.getUid());

        if (updateInfo.getFb_gender() != null){
            updateGreetUser.setGender(updateInfo.getFb_gender());
        }

        if (!CollectionUtils.isEmpty(updateInfo.getLabel_list())) {
            updateGreetUser.setLabelInfo(JSONObject.toJSONString(updateInfo.getLabel_list()));
        }

        if (!ObjectUtils.isEmpty(updateInfo.getCountry())) {
            updateGreetUser.setCountryCode(ActorUtils.getUpperCaseCountryCode(updateInfo.getCountry()));
        }
        updateGreetUser.setProfileProcess(progress * 0.01);
        greetUserService.updateGreetUser(updateGreetUser);

    }

    private List<String> getAllReached(String uid, List<String> oldList) {
        if (oldList == null) {
            UserLevelData userLevelData = userLevelDao.findUserLevel(uid);
            if (userLevelData != null && !CollectionUtils.isEmpty(userLevelData.getReached())) {
                return userLevelData.getReached();
            } else {
                return Collections.emptyList();
            }
        } else {
            return oldList;
        }
    }

    private MongoUserActorData checkUser(UpdateUserInfoDTO dto) {
        String uid = dto.getUid();
        if (dto.invalidPkgName()) {
            logger.info("pkg name is invalid dto={}", dto);
            basePlayerRedis.removeToken(uid);
            throw new CommonException(HttpCode.SESSION_INVALID);
        }
        if (!userMonitorDao.notFreezeOrBan(uid)) {
            logger.info("user is freeze or ban dto={}", dto);
            throw new CommonException(HttpCode.USER_MONITOR_FREEZE);
        }

        MongoUserActorData userActorData = commonDao.findOne(new Query(Criteria.where("_id").is(new ObjectId(uid))), MongoUserActorData.class);
        if (userActorData == null || userActorData.getValid() == 0) {
            logger.info("user is not valid dto={}", dto);
            throw new CommonException(UserHttpCode.USER_NOT_VALID);
        }

        String tnId = userActorData.getTn_id();
        String blockTime = blockRedis.checkBlock(tnId, BlockTnConstant.BLOCK_PROFILE);
        if (!StringUtils.isEmpty(blockTime)) {
            logger.info("user device limit profile tnId={} blockTime={} dto={}", tnId, blockTime, dto);
            throw new CommonException(UserHttpCode.DEVICE_PROFILE_LIMIT_BAN, blockTime);
        }
        return userActorData;
    }


    private List<Integer> updateLabelList(UpdateUserInfoDTO dto, UpdateUserInfoData updateInfo, List<Integer> toLabelList) {
        List<Integer> labelList = !CollectionUtils.isEmpty(dto.getLabel_list()) ? dto.getLabel_list() : dto.getLabel();
        if (!CollectionUtils.isEmpty(labelList)) {
            Map<Integer, LabelConfigVO> labelMap = userLabelDao.getLabelConfigFromCache();
            List<Integer> newLabelList = labelList.stream().filter(labelMap::containsKey).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(newLabelList)) {
                newLabelList = newLabelList.stream().distinct().collect(Collectors.toList());
                int lenLabel = newLabelList.size();
                if (lenLabel < 5) {
                    logger.info("lenLabel invalid len={} < 5 newLabelList={} dto={}", lenLabel, newLabelList, dto);
                    throw new CommonException(UserHttpCode.LEN_LABEL_MIN);
                } else if (lenLabel > 20) {
                    logger.info("lenLabel invalid len={} > 20 newLabelList={} dto={}", lenLabel, newLabelList, dto);
                    throw new CommonException(UserHttpCode.LEN_LABEL_MAX);
                }
                updateInfo.setLabel_list(newLabelList);
                updateInfo.setChangeLabelList(isChangeList(toLabelList, newLabelList));
                toLabelList = newLabelList;
            }
        }
        return toLabelList;
    }

    private boolean isChangeList(List<Integer> oldList, List<Integer> newList) {
        if (CollectionUtils.isEmpty(oldList) || CollectionUtils.isEmpty(newList)) {
            return true;
        }
        boolean isChange = false;
        int oldSize = oldList.size();
        int newSize = newList.size();
        if (oldSize != newSize) {
            isChange = true;
        } else {
            for (Integer item : newList) {
                if (!oldList.contains(item)) {
                    isChange = true;
                    break;
                }
            }
        }
        return isChange;
    }

    private boolean detectImage(String url, String origin, String uid) {
        try {
            if (detectService.detectImage(new ImageDTO(url, origin, uid)).getData().getIsSafe() == 1) {
                return true;
            }
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", "", "", CommonMqTaskConstant.USER_PROFILE_VIOLATION, 1));
        } catch (Exception e) {
            logger.error("detect image Exception url={} uid={} origin={}", url, uid, origin, e);
        }
        return false;
    }

    private boolean detectText(String name, String origin, String fromUid) {
        try {
            if (detectService.detectText(new TextDTO(name, origin, fromUid)).getData().getIsSafe() == 1) {
                return true;
            }
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(fromUid, "", "", "", CommonMqTaskConstant.USER_PROFILE_VIOLATION, 1));
        } catch (Exception e) {
            logger.error("detect text Exception name={}", name, e);
        }
        return false;
    }


    private void sendNotice(String uid, String head, int slang) {
        OfficialData officialData;
        if (slang == SLangType.ENGLISH) {
            officialData = new OfficialData("", NOTICE_BODY, uid);
        } else {
            officialData = new OfficialData("", NOTICE_BODY_AR, uid);
        }
        officialData.setPicture(head);
        officialDao.save(officialData);
        noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
    }

    public LabelInfoConfigVO labelInfoConfig(HttpEnvData dto) {
        String uid = dto.getUid();
        int slang = dto.getSlang();

        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.info("user is not valid dto={}", dto);
            throw new CommonException(UserHttpCode.USER_NOT_VALID);
        }
        List<Integer> myLabelList = actorData.getLabelList();

        LabelInfoConfigVO vo = new LabelInfoConfigVO();
        List<LabelInfoConfigVO.LabelItemInfo> userLabel = new ArrayList<>();
        Map<Integer, LabelInfoConfigVO.LabelCategoryInfo> categoryDict = new HashMap<>();


        List<LabelConfigData> configDataList = userLabelDao.getAllLabelConfigFromCache();
        for (LabelConfigData item : configDataList) {
            LabelInfoConfigVO.LabelItemInfo info = new LabelInfoConfigVO.LabelItemInfo();
            info.setLabel_id(item.getLabelId());
            info.setLabel_name(slang == SLangType.ENGLISH ? item.getLabelName() : item.getLabelArname());
            info.setLabel_color(item.getTypeColor());
            info.setLabelOrder(item.getLabelOrder());
            if (myLabelList.contains(item.getLabelId())) {
                info.setChoise(1);
                LabelInfoConfigVO.LabelItemInfo myInfo = new LabelInfoConfigVO.LabelItemInfo();
                BeanUtils.copyProperties(info, myInfo);
                userLabel.add(myInfo);
            } else {
                info.setChoise(0);
            }
            LabelInfoConfigVO.LabelCategoryInfo categoryInfo = categoryDict.getOrDefault(item.getLabelType(), null);
            if (categoryInfo == null) {
                categoryInfo = new LabelInfoConfigVO.LabelCategoryInfo();
                categoryInfo.setCategory(item.getLabelType());
                categoryInfo.setCategory_icon(item.getTypeIcon());
                categoryInfo.setCategory_name(slang == SLangType.ENGLISH ? item.getTypeName() : item.getTypeArname());
                List<LabelInfoConfigVO.LabelItemInfo> categoryLabel = new ArrayList<>();
                categoryLabel.add(info);
                categoryInfo.setLabel_list(categoryLabel);
                categoryInfo.setTypeOrder(item.getTypeOrder());
                categoryDict.put(item.getLabelType(), categoryInfo);
            } else {
                List<LabelInfoConfigVO.LabelItemInfo> categoryLabel = categoryInfo.getLabel_list();
                categoryLabel.add(info);
            }

        }
        List<LabelInfoConfigVO.LabelCategoryInfo> categoryList = new ArrayList<>(categoryDict.values());
        categoryList.sort(Comparator.comparing(LabelInfoConfigVO.LabelCategoryInfo::getTypeOrder));
//        for (LabelInfoConfigVO.LabelCategoryInfo xtem : categoryList) {
//            xtem.getLabel_list().sort(Comparator.comparing(LabelInfoConfigVO.LabelItemInfo::getLabelOrder));
//        }
        vo.setUser_label(userLabel);
        vo.setCategory_list(categoryList);
        return vo;
    }


    public List<LabelInfoConfigVO.LabelItemInfo> oldUserLabel(HttpEnvData dto) {
        String uid = dto.getUid();
        int slang = dto.getSlang();

        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.info("user is not valid dto={}", dto);
            throw new CommonException(UserHttpCode.USER_NOT_VALID);
        }
        List<Integer> myLabelList = actorData.getLabelList();
        List<LabelInfoConfigVO.LabelItemInfo> labelList = new ArrayList<>();

        List<LabelConfigData> configDataList = userLabelDao.getOldAllLabelConfig();
        for (LabelConfigData item : configDataList) {
            LabelInfoConfigVO.LabelItemInfo info = new LabelInfoConfigVO.LabelItemInfo();
            info.setLabel_id(item.getLabelId());
            info.setLabel_name(slang == SLangType.ENGLISH ? item.getLabelName() : item.getLabelArname());
            info.setLabelOrder(item.getLabelOrder());
            if (myLabelList.contains(item.getLabelId())) {
                info.setChoise(1);
            } else {
                info.setChoise(0);
            }
            labelList.add(info);
        }
        return labelList;
    }

    public ImproveInfoVO improveShowUserInfo(UpdateUserInfoDTO dto) {
        ImproveInfoVO vo = new ImproveInfoVO();
        String uid = dto.getUid();
        ActorData actorData = actorDao.getActorData(uid);
        vo.setName(actorData.getName());
        vo.setHead(actorData.getHead());
        vo.setGender(actorData.getFb_gender());
        vo.setBirthDay(actorData.getBirthday());
        vo.setMaleHeadList(BaseInitData.SYS_NEW_MALE_HEAD_LIST);
        vo.setFemaleHeadList(BaseInitData.SYS_NEW_FEMALE_HEAD_LIST);
        vo.setMaleNameList(baseInitData.getSysNewRandomNameBySlang(1, dto.getSlang()));
        vo.setFemaleNameList(baseInitData.getSysNewRandomNameBySlang(2, dto.getSlang()));
        return vo;
    }

    public String handleArbNum(String oldAccount) {
        char[] aa = oldAccount.toCharArray();
        StringBuilder newAccount = new StringBuilder();
        for (Character ch : aa) {
            newAccount.append(parseArb(ch.toString()));
        }
        return newAccount.toString();
    }

    private String parseArb(String userPwd) {
        String userStrPwd = userPwd;
        try {
            // 针对阿语数字进行特殊处理
            // ARABIC_INDIC = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٩',','۸']
            userStrPwd = Integer.parseInt(userPwd) + "";
        } catch (NumberFormatException e) {
//            logger.info("userPwd={} error msg={}", userPwd, e.getMessage());
        }
        return userStrPwd;
    }

    public void detectGoogleSafeSearchUrl(String url) {
        /**
         * https://cloud.google.com/vision/docs/detecting-safe-search?hl=zh-cn#vision_safe_search_detection-java
         */
        List<AnnotateImageRequest> requests = new ArrayList<>();

        ImageSource imgSource = ImageSource.newBuilder().setImageUri(url).build();
        Image img = Image.newBuilder().setSource(imgSource).build();
        Feature feat = Feature.newBuilder().setType(Feature.Type.SAFE_SEARCH_DETECTION).build();
        AnnotateImageRequest request = AnnotateImageRequest.newBuilder().addFeatures(feat).setImage(img).build();
        requests.add(request);

//        imageAnnotatorClient.asyncBatchAnnotateImagesAsync();
//        imageAnnotatorClient.asyncBatchAnnotateImagesCallable();
//        imageAnnotatorClient.asyncBatchAnnotateImagesOperationCallable();

        BatchAnnotateImagesResponse response = imageAnnotatorClient.batchAnnotateImages(requests);
        List<AnnotateImageResponse> responses = response.getResponsesList();

        for (AnnotateImageResponse res : responses) {
            if (res.hasError()) {
                logger.info("google_image url:{} Error: {}", url, res.getError().getMessage());
                return;
            }
            SafeSearchAnnotation annotation = res.getSafeSearchAnnotation();
            logger.info("google_image url:{} adult={} medical={} spoofed={} violence={} racy={}",
                    url,
                    annotation.getAdult(),
                    annotation.getMedical(),
                    annotation.getSpoof(),
                    annotation.getViolence(),
                    annotation.getRacy());
        }

    }
}
