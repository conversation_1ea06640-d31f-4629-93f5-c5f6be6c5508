package com.quhong.service;

import com.quhong.data.dto.UpdateActorConfigDTO;
import com.quhong.data.vo.ActorConfigVO;
import com.quhong.data.vo.AvatarLimitationVO;
import com.quhong.data.vo.ResourceVersionVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.ActorConfigData;
import com.quhong.mongo.data.CommonConfigData;
import com.quhong.mongo.data.UploadHeadWhiteUidData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 用户自定义设置
 *
 * <AUTHOR>
 * @date 2022/7/18
 */
@Service
public class ActorConfigService {

    private static final Logger logger = LoggerFactory.getLogger(ActorConfigService.class);

    private static final String BUDDY_KEY = "buddy";

    private static final List<String> SYS_AVATAR_LIST = Arrays.asList("https://cdn3.qmovies.tv/youstar/s1.png", "https://cdn3.qmovies.tv/youstar/s2.png",
            "https://cdn3.qmovies.tv/youstar/s3.png", "https://cdn3.qmovies.tv/youstar/s4.png", "https://cdn3.qmovies.tv/youstar/s5.png",
            "https://cdn3.qmovies.tv/youstar/s6.png", "https://cdn3.qmovies.tv/youstar/s7.png", "https://cdn3.qmovies.tv/youstar/s8.png",
            "https://cdn3.qmovies.tv/youstar/s9.png", "https://cdn3.qmovies.tv/youstar/s10.png", "https://cdn3.qmovies.tv/youstar/s11.png",
            "https://cdn3.qmovies.tv/youstar/s12.png", "https://cdn3.qmovies.tv/youstar/s13.png", "https://cdn3.qmovies.tv/youstar/s14.png",
            "https://cdn3.qmovies.tv/youstar/s15.png", "https://cdn3.qmovies.tv/youstar/s16.png", "https://cdn3.qmovies.tv/youstar/s17.png",
            "https://cdn3.qmovies.tv/youstar/s18.png", "https://cdn3.qmovies.tv/youstar/s19.png", "https://cdn3.qmovies.tv/youstar/s20.png",
            "https://cdn3.qmovies.tv/youstar/s21.png", "https://cdn3.qmovies.tv/youstar/s22.png", "https://cdn3.qmovies.tv/youstar/s23.png",
            "https://cdn3.qmovies.tv/youstar/s24.png", "https://cdn3.qmovies.tv/youstar/s25.png", "https://cdn3.qmovies.tv/youstar/s26.png",
            "https://cdn3.qmovies.tv/youstar/s27.png");

    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private UploadHeadWhiteUidDao uploadHeadWhiteUidDao;

    /**
     * 更新actor_config
     */
    public void updateActorConfig(UpdateActorConfigDTO req) {
        String uid = req.getUid();
        Map<String, Object> configMap = req.getConfigMap();
        if (CollectionUtils.isEmpty(configMap)) {
            return;
        }
        if (StringUtils.isEmpty(uid) || actorDao.getActorDataFromCache(uid) == null) {
            logger.error("cannot find actor.uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        Map<String, Object> config;
        ActorConfigData configData = actorConfigDao.findData(uid);
        if (configData == null) {
            configData = new ActorConfigData();
            configData.setUid(uid);
            config = new HashMap<>(16);
        } else {
            config = req.getType() == 0 ? configData.getCommon_config() :
                    req.getType() == 1 ? configData.getRoom_config() : configData.getGame_room_config();
            if (CollectionUtils.isEmpty(config)) {
                if (req.getType() == 2) {
                    config = new HashMap<>(ActorConfigDao.DEF_GAME_ROOM_CONFIG);
                } else {
                    config = new HashMap<>(16);
                }
            }

        }

        for (Map.Entry<String, Object> entry : configMap.entrySet()) {
            if (entry.getValue() instanceof Integer) {
                config.put(entry.getKey(), entry.getValue());
            } else {
                logger.info("value not integer uid={} key={} value={}", req.getUid(), entry.getKey(), entry.getValue());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
        }

        if (req.getType() == 0) {
            configData.setCommon_config(config);
        } else if (req.getType() == 1) {
            configData.setRoom_config(config);
        } else if (req.getType() == 2) {
            configData.setGame_room_config(config);
        }
        logger.info("client set uid={} type={} configMap={}", req.getUid(), req.getType(), configMap);
        actorConfigDao.save(configData);
    }

    /**
     * 设置语音首选项
     */
    public ActorConfigVO settingVideoOption(UpdateActorConfigDTO req) {
        if (StringUtils.isEmpty(req.getUid())) {
            logger.error("setting actor video option param error. uid={} video={}", req.getUid(), req.getVideo());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        ActorConfigVO vo = new ActorConfigVO();
        String video;
        if (Boolean.FALSE.toString().equals(req.getVideo())) {
            video = "0";
        } else if (Boolean.TRUE.toString().equals(req.getVideo())) {
            video = "1";
        } else {
            video = req.getVideo() != null ? req.getVideo() : "0";
        }
        actorDao.updateVideoOption(req.getUid(), video);
        vo.setVideo(video);
        return vo;
    }

    /**
     * 设置坐骑首选项
     */
    public ActorConfigVO settingRideOption(UpdateActorConfigDTO req) {
        if (StringUtils.isEmpty(req.getUid())) {
            logger.error("setting actor ride option param error. uid={} ride={}", req.getUid(), req.getRide());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        ActorConfigVO vo = new ActorConfigVO();
        actorDao.updateRideOption(req.getUid(), req.getRide());
        vo.setRide(req.getRide());
        return vo;
    }

    /**
     * 设置是否拒绝加好友
     */
    public ActorConfigVO settingBuddy(UpdateActorConfigDTO req) {
        boolean flag = StringUtils.isEmpty(req.getUid()) || (req.getBuddy() != 0 && req.getBuddy() != 1);
        if (flag) {
            logger.error("setting actor buddy param error. uid={} buddy={}", req.getUid(), req.getBuddy());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        ActorConfigVO vo = new ActorConfigVO();
        actorDao.updateGeneralConf(req.getUid(), BUDDY_KEY, req.getBuddy());
        vo.setBuddy(req.getBuddy());
        return vo;
    }

    /**
     * 设置用户所属区域
     */
    public void setNlang(UpdateActorConfigDTO req) {
        if (StringUtils.isEmpty(req.getUid()) || (req.getNlang() != 1 && req.getNlang() != 2)) {
            logger.error("update actor nlang param error. uid={} nlang={}", req.getUid(), req.getNlang());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        actorDao.updateActorArea(req.getUid(), req.getNlang(), req.getNlang());
    }

    /**
     * 头像限制api
     */
    public AvatarLimitationVO avatarLimitation(UpdateActorConfigDTO req) {
        AvatarLimitationVO vo = new AvatarLimitationVO();
        // 值为-1是关闭状态，其他大于等于0的值为对应等级
        CommonConfigData registerLimitData = commonConfig.findData("register_limit", req.getOs());
        CommonConfigData profileLimitData = commonConfig.findData("profile_limit", req.getOs());
        CommonConfigData roomLimitData = commonConfig.findData("room_limit", req.getOs());
        int registerLimit = registerLimitData != null ? (int) registerLimitData.getValue() : -1;
        int profileLimit = profileLimitData != null ? (int) profileLimitData.getValue() : -1;
        int roomLimit = roomLimitData != null ? (int) roomLimitData.getValue() : -1;
        int userLevel = -1;
        // 白名单
        if (!StringUtils.isEmpty(req.getUid())) {
            userLevel = userLevelDao.getUserLevel(req.getUid());
            UploadHeadWhiteUidData data = uploadHeadWhiteUidDao.findData(req.getUid());
            if (data != null) {
                registerLimit = -1;
                profileLimit = -1;
                roomLimit = -1;
            }
        }
        List<String> list = new ArrayList<>(SYS_AVATAR_LIST);
        int index = (int) (Math.random() * SYS_AVATAR_LIST.size());
        List<String> newList = new ArrayList<>();
        newList.add(list.get(index));
        list.remove(index);
        newList.addAll(list);
        vo.setRegister(registerLimit);
        vo.setProfile(profileLimit);
        vo.setRoom(roomLimit);
        vo.setSysAvatars(newList);
        vo.setUserLevel(userLevel);
        return vo;
    }

    public ResourceVersionVO resourceVersion() {
        ResourceVersionVO vo = new ResourceVersionVO();
        Object sVipNameColorObject = commonConfig.getConfigValue("svip_name_color");
        vo.setVipResVersion(commonConfig.getConfigIntValue("vip_res_version"));
        vo.setBubbleResVersion(commonConfig.getConfigIntValue("bubble_res_version"));
        vo.setSvipNameColor(sVipNameColorObject != null ? (String) sVipNameColorObject : "");
        return vo;
    }

    /**
     * 设置消息顶部通知开关
     */
    public void setMessageTopSwitch(UpdateActorConfigDTO req) {
        String uid = req.getUid();
        int messageTopSwitch = req.getMessageTopSwitch();

        if (StringUtils.isEmpty(uid) || (messageTopSwitch != 0 && messageTopSwitch != 1)) {
            logger.error("update actor messageTopSwitch param error. uid={} messageTopSwitch={}", uid, messageTopSwitch);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        actorConfigDao.updateRoomConfig(uid, ActorConfigDao.MESSAGE_TOP_SWITCH, messageTopSwitch);
    }
}
