package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.config.FirstRechargeConfig;
import com.quhong.constant.MatchConstant;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.vo.FirstRechargeInfoVO;
import com.quhong.data.vo.FirstRechargeRewardVO;
import com.quhong.data.vo.PrizeVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.RewardTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.feign.DataCenterService;
import com.quhong.handler.RechargeHandler;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.UserRechargeRecordDao;
import com.quhong.mysql.dao.VersionRechargeRankDao;
import com.quhong.mysql.data.RechargeRankData;
import com.quhong.mysql.data.UserRechargeRecordData;
import com.quhong.mysql.data.VersionRechargeRankData;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.room.RoomWebSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 首充礼包
 *
 * <AUTHOR>
 * @date 2022/11/14
 */
@Service
public class FirstRechargeService implements RechargeHandler {

    private static final Logger logger = LoggerFactory.getLogger(FirstRechargeService.class);

    private static final int RANK_NUM = 50;

    private static final int SHOW_NUM = 0;
    private static final int SHOW_DAYS = 1;
    private static final String DIAMOND = "diamond";
    private static final String GIFT = "gift";
    private static final String COIN = "coin";
    private static final String DISCOUNT = "discount";

    private static final int A_TYPE = 922;
    private static final String TITLE = "First recharge bonus";
    private static final String DESC = "First recharge bonus";

    private static final List<String> FORMAL_RECHARGE_CHANNEL = Arrays.asList("google", "apple", "huawei");

    public static final String ACTION_EN = "Go to wear>";
    public static final String ACTION_AR = "الذهاب للإرتداء >";

//    public static final String NOTICE_EN = "Congratulations,you get a recharge package!";
//    public static final String NOTICE_AR = "تهانينا ، لقد حصلت على  إعادة الشحن حزمة!";
//    public static final String NOTICE_DESC_EN = "Ride、Micframe、Recharge badge was already sent in your account, please goto Me - My Decorations and Me - Badge to wear it. gift was already sent to you gift bag, please go room check it.";
//    public static final String NOTICE_DESC_AR = "المركبات ، إطارات المايك، وسام إعادة الشحن  تم إرسال  بالفعل في حسابك ، يرجى الانتقال إلى انا  - الديكور الخاص بي و انا - الوسام  لارتدائها.  تم إرسال الهدية بالفعل إلى حقيبة الهدايا ، يرجى الذهاب والتحقق من ذلك.";

    public static final String NOTICE_EN = "Congratulations on getting your first recharge reward";
    public static final String NOTICE_AR = "تهانينا على حصولك على مكافأة الشحنة الأولى";
    public static final String NOTICE_DESC_EN = "The first recharge reward has arrived, please use it on theMe-Decorations page.";
    public static final String NOTICE_DESC_AR = "لقد وصلت مكافأة الشحنة الأولى، يرجى استخدامها على صفحة الزينة الخاصة بي.";


    public static final String SUB_TITLE_EN = "Recharge to get an extra %s of diamonds. The more you recharge, the more you will get. There is only one chance.";
    public static final String SUB_TITLE_AR = "كلما شحنت أكثر، كلما حصلت على المزيد. لديك فرصة واحدة فقط. قم بإعادة الشحن لتحصل على %s ألماس إضافي";

    public static final String FIRST_RECHARGE = "first_recharge";

    private final static String VIEW_EN = "Recharge >";
    private final static String VIEW_AR = "إعادة الشحن >";
    private final static String TEXT_EN = "Congratulations to %s for getting the first recharge gift package. Recharge >";
    private final static String TEXT_AR = "تهانينا لـ %s للحصول على حزمة هدايا الشحنة الأولى. إعادة الشحن >";

    public static final Map<String, List<CoreRewardConfigData>> TYPE_BACK_CONFIG_MAP = new HashMap<String, List<CoreRewardConfigData>>() {
        {
            put("product_coins_diamonds2", CoreRewardInitData.TYPE_BACK_REWARD_499_LIST);
            put("product_coins_diamonds3", CoreRewardInitData.TYPE_BACK_REWARD_1999_LIST);
            put("product_coins_diamonds4", CoreRewardInitData.TYPE_BACK_REWARD_4999_LIST);
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_STR_MAP = new HashMap<String, String>() {
        {
            put("product_coins_diamonds1", "back_ac_reward_099_key");
            put("product_coins_diamonds3", "back_ac_reward_1999_key");
            put("product_coins_diamonds4", "back_ac_reward_4999_key");
        }
    };

    @Resource
    private ActorDao actorDao;
    @Resource
    private FirstRechargeConfig firstRechargeConfig;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private UserRechargeRecordDao userRechargeRecordDao;
    @Resource
    private VersionRechargeRankDao rechargeRankDao;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private GiftBagDao giftBagDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    protected OfficialDao officialDao;
    @Resource
    protected NoticeNewDao noticeNewDao;
    @Autowired
    protected RoomWebSender roomWebSender;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource
    private CoreRewardInitData coreRewardInitData;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;


    /**
     * 获取首充信息
     */
    @Cacheable(value = "getFirstRechargeInfo", key = "#p0+#p1", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public FirstRechargeInfoVO getFirstRechargeInfo(String uid, int slang) {
        FirstRechargeInfoVO vo = new FirstRechargeInfoVO();
        int checkSwitch = commonConfig.getSwitchConfigValue(CommonConfig.FIRST_CHARGE_SWITCH, 0);
        if (checkSwitch != 1) {
            vo.setFirstRechargeRewardList(Collections.emptyList());
            logger.info("uid={} checkSwitch={} is close", uid, checkSwitch);
            return vo;
        }
        if (!StringUtils.isEmpty(uid)) {
            // app 上来的请求要查数据库
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            UserRechargeRecordData firstChargeData = userRechargeRecordDao.getFirstChargeData(uid, actorData.getTn_id());
            if (firstChargeData != null) {
                vo.setFirstRechargeRewardList(Collections.emptyList());
                logger.info("uid={} is recharged", uid);
                return vo;
            }
            int now = DateHelper.getNowSeconds();
            int showDays = commonConfig.getSwitchConfigValue(CommonConfig.FIRST_CHARGE_SHOW_DAYS, 7);
            int start = actorConfigDao.getIntRoomConfig(uid, ActorConfigDao.FIRST_RECHARGE_SHOW_TIME, 0);
            int end = start + showDays * 24 * 3600;
//            logger.info("start={} end={} now={} showDays={} uid={} ", start, end, now, showDays, uid);
            if (now <= end) {
                vo.setShowPop(1);
            } else if (start == 0) {
                vo.setShowPop(1);
                actorConfigDao.updateRoomConfig(uid, ActorConfigDao.FIRST_RECHARGE_SHOW_TIME, now);
            } else {
                vo.setShowPop(0);
            }
            int showTime = commonConfig.getSwitchConfigValue(CommonConfig.FIRST_CHARGE_SHOW_TIME, 60);
            vo.setFirstChargeShowTime(showTime);
        }
        List<PrizeVO> firstRechargeRewards = firstRechargeConfig.getFirstRechargeRewards();
        List<FirstRechargeRewardVO> rewardVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(firstRechargeRewards)) {
            for (PrizeVO prizeVO : firstRechargeRewards) {
                FirstRechargeRewardVO rewardVO = new FirstRechargeRewardVO();
                rewardVO.setName(prizeVO.getName());
                rewardVO.setNameAr(prizeVO.getNameAr());
                rewardVO.setIcon(prizeVO.getLink());
                rewardVO.setType(prizeVO.getType());
                if (GIFT.equals(prizeVO.getType()) || COIN.equals(prizeVO.getType()) || DIAMOND.equals(prizeVO.getType())) {
                    rewardVO.setShowNumOrDays(SHOW_NUM);
                    rewardVO.setDays(0);
                    rewardVO.setNum(prizeVO.getNums());
                } else {
                    rewardVO.setShowNumOrDays(SHOW_DAYS);
                    rewardVO.setNum(1);
                    rewardVO.setDays(prizeVO.getNums());
                }
                rewardVOList.add(rewardVO);
            }
        }
        vo.setFirstRechargeRewardList(rewardVOList);
        String highText = (int) (MatchConstant.FIRST_CHARGE_DISCOUNT * 100) + "%";
        vo.setHighText(highText);
        String subTitle = slang == SLangType.ENGLISH ? String.format(SUB_TITLE_EN, highText) : String.format(SUB_TITLE_AR, highText);
        vo.setSubTitle(subTitle);
        return vo;
    }

    @Override
    public void process(RechargeInfo rechargeInfo) {
        if (rechargeInfo == null || !FORMAL_RECHARGE_CHANNEL.contains(rechargeInfo.getRechargeItem())) {
            return;
        }
        if (StringUtils.isEmpty(rechargeInfo.getUid())) {
            logger.error("rechargeInfo uid is empty");
            return;
        }
        ActorData actorData = actorDao.getActorData(rechargeInfo.getUid());
        if (actorData == null) {
            logger.error("can not find actor. uid={}", rechargeInfo.getUid());
            return;
        }
        int firstRecharge = 0;

        int checkSwitch = commonConfig.getSwitchConfigValue(CommonConfig.FIRST_CHARGE_SWITCH, 0);
        if (checkSwitch == 1) {
            UserRechargeRecordData firstChargeData = userRechargeRecordDao.getFirstChargeData(rechargeInfo.getUid(), actorData.getTn_id());
            if (firstChargeData == null) {
                int add = rechargeInfo.getFirstChargedAdd() == null ? 0 : rechargeInfo.getFirstChargedAdd();
                sendFirstRechargeGiftBag(rechargeInfo.getUid(), add);
                firstRecharge = 1;
                sendOfficialMsg(rechargeInfo.getUid(), actorData.getSlang());
                if (ServerConfig.isNotProduct()) {
                    sendNotice(actorData);
                }
            }
//            int count = userRechargeRecordDao.selectCount(rechargeInfo.getUid());
//            if (count == 0) {
//                // 没有充值记录则为首次充值
//                sendFirstRechargeGiftBag(rechargeInfo.getUid());
//                firstRecharge = 1;
//            } else {
//                UserRechargeRecordData firstChargeData = userRechargeRecordDao.getFirstChargeData(rechargeInfo.getUid());
//                if (firstChargeData == null) {
//                    sendFirstRechargeGiftBag(rechargeInfo.getUid());
//                    firstRecharge = 1;
//                }
//            }
            saveUserRechargeRecordData(rechargeInfo, firstRecharge, actorData.getTn_id());
        }
//        backUserReward(rechargeInfo.getUid(), rechargeInfo.getProductId(), actorData);
    }

    private void backUserReward(String uid, String productId, ActorData actorData) {

        if (StringUtils.hasLength(productId)) {
            List<CoreRewardConfigData> configDataList = TYPE_BACK_CONFIG_MAP.get(productId);
            if (!CollectionUtils.isEmpty(configDataList) && ServerConfig.isProduct()) {
                BackUserStateData backUserStateData = backUserStateRedis.checkBackUser(actorData, false);
                if (backUserStateData != null && backUserStateData.getBackTime() + ResourceConstant.BACK_AC_DAY_TIME >= DateHelper.getNowSeconds()) {
                    Set<String> rechargeProductIdSet = backUserStateData.getRechargeProductIdSet();
                    if (rechargeProductIdSet == null) {
                        rechargeProductIdSet = new HashSet<>();
                    }
                    if (rechargeProductIdSet.contains(productId)) {
                        logger.info("backUserReward fail uid:{} productId:{} is consumer", uid, productId);
                        return;
                    }
                    for (CoreRewardConfigData item : configDataList) {
                        handleResources(uid, item, ResourceConstant.BACK_AC_DESC);
                    }
                    rechargeProductIdSet.add(productId);
                    backUserStateData.setRechargeProductIdSet(rechargeProductIdSet);
                    backUserStateRedis.saveBackUserState(backUserStateData, uid);
                    logger.info("backUserReward success uid:{} productId:{}", uid, productId);
                }
            }
        }
    }

    public void handleResources(String uid, CoreRewardConfigData vo, String resDesc) {
        ResourcesDTO dto = coreRewardInitData.configDataToResDTO(uid, vo, resDesc);
        if (dto != null) {
            mqSenderService.asyncHandleResources(dto);
        } else {
            if (CoreRewardInitData.DIAMOND_TYPE == vo.getResourceType()) {
                int beansNum = vo.getResourceNum();
                if (beansNum > 0) {
                    MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                    moneyDetailReq.setRandomId();
                    moneyDetailReq.setUid(uid);
                    moneyDetailReq.setAtype(MoneyTypeConstant.BACK_AC_DIAMOND_TYPE);
                    moneyDetailReq.setChanged(beansNum);
                    moneyDetailReq.setTitle(MoneyTypeConstant.BACK_AC_DIAMOND_TITLE);
                    moneyDetailReq.setDesc(MoneyTypeConstant.BACK_AC_DIAMOND_TITLE);
                    ApiResult<String> addBeansResult = dataCenterService.chargeBeans(moneyDetailReq);
                    if (!addBeansResult.isOk()) {
                        logger.info("change beans fail add={} uid={} ", beansNum, uid);
                    }
                }
            } else {
                logger.info("ResourcesDTO is null vo:{}", vo);
            }
        }
    }

    /**
     * 退款
     */
    public void processRefund(RechargeInfo rechargeInfo) {
        ActorData actorData = actorDao.getActorData(rechargeInfo.getUid());
        if (actorData == null) {
            logger.error("can not find actor. uid={}", rechargeInfo.getUid());
            return;
        }

        UserRechargeRecordData firstChargeData = userRechargeRecordDao.getFirstChargeData(rechargeInfo.getUid());
        if (firstChargeData == null) {
            logger.info("return can not get recharge bag data. uid={} orderId={}", rechargeInfo.getUid(), rechargeInfo.getOrderId());
            return;
        }

        UserRechargeRecordData data = userRechargeRecordDao.selectOne(rechargeInfo.getUid(), rechargeInfo.getOrderId());
        if (data == null || data.getHasRefunded() != 0) {
            logger.info("return can not find recharge data. uid={} orderId={}", rechargeInfo.getUid(), rechargeInfo.getOrderId());
            return;
        }
        data.setHasRefunded(1);
        userRechargeRecordDao.update(data);
        int count = userRechargeRecordDao.selectNoRefundCount(rechargeInfo.getUid());
        if (count == 0) {
            // 全部订单都退款
            if (userRechargeRecordDao.selectHasRecycledRewardCount(rechargeInfo.getUid()) != 0) {
                // 已经回收过首充奖励
                logger.info("return has recycled reward uid={} orderId={}", rechargeInfo.getUid(), rechargeInfo.getOrderId());
                return;
            }
            // 回收资源
            recycleFirstRechargeGiftBag(actorData, data);
            data.setHasRefunded(2);
            userRechargeRecordDao.update(data);
        } else {
            // 还有其他充值订单，不回收奖励
            logger.info("return have other order not refund uid={} orderId={}", rechargeInfo.getUid(), rechargeInfo.getOrderId());
        }
    }

    private void saveUserRechargeRecordData(RechargeInfo rechargeInfo, int firstRecharge, String tnId) {
        UserRechargeRecordData data = new UserRechargeRecordData();
        data.setUid(rechargeInfo.getUid());
        data.setOrderId(rechargeInfo.getOrderId());
        data.setRechargeMoney(rechargeInfo.getRechargeMoney());
        data.setRechargeDiamond(rechargeInfo.getRechargeDiamond());
        data.setRechargeTime(rechargeInfo.getRechargeTime());
        data.setFirstCharge(firstRecharge);
        data.setHasRefunded(0);
        data.setTnId(tnId);
        data.setFirstChargedAdd(rechargeInfo.getFirstChargedAdd());
        userRechargeRecordDao.insert(data);
    }

    /**
     * 下发首充礼包
     */
    private void sendFirstRechargeGiftBag(String uid, int addDiamonds) {
        List<PrizeVO> firstRechargeRewards = firstRechargeConfig.getFirstRechargeRewards();
        if (CollectionUtils.isEmpty(firstRechargeRewards)) {
            logger.error("first recharge reward list is empty.");
            return;
        }
        for (PrizeVO prizeVO : firstRechargeRewards) {
            if (DIAMOND.equals(prizeVO.getType())) {
                distributionService.sendDiamondsReward(uid, A_TYPE, prizeVO.getNums(), TITLE, DESC);
            } else if (COIN.equals(prizeVO.getType())) {
                distributionService.sendCoinRecord(uid, prizeVO.getNums(), TITLE, DESC);
            } else if (DISCOUNT.equals(prizeVO.getType())) {
                if (addDiamonds > 0) {
                    distributionService.sendDiamondsReward(uid, A_TYPE, addDiamonds, TITLE, DESC);
                } else {
                    logger.info("uid:{} not add diamond addDiamonds:{}", uid, addDiamonds);
                }
            } else {
                distributionService.sendResourceReward(uid, prizeVO.getSourceId(), RewardTypeEnum.getEnumByName(prizeVO.getType()), BaseDataResourcesConstant.ACTION_GET_WEAR, prizeVO.getDay(), prizeVO.getNums(), DESC, 0);
            }
        }
        logger.info("uid:{} send first recharge gift bag success.", uid);
    }

    /**
     * 回收首充礼包资源
     */
    private void recycleFirstRechargeGiftBag(ActorData actorData, UserRechargeRecordData data) {
        List<PrizeVO> firstRechargeRewards = firstRechargeConfig.getFirstRechargeRewards();
        if (CollectionUtils.isEmpty(firstRechargeRewards)) {
            logger.error("first recharge reward list is empty.");
            return;
        }
        for (PrizeVO prizeVO : firstRechargeRewards) {
            if (DIAMOND.equals(prizeVO.getType())) {
                if (actorData.getBeans() >= prizeVO.getNums()) {
                    distributionService.deductDiamondsReward(actorData.getUid(), A_TYPE, prizeVO.getNums(), TITLE, DESC);
                } else if (actorData.getBeans() > 0 && actorData.getBeans() < prizeVO.getNums()) {
                    // 钻石不足则全扣
                    distributionService.deductDiamondsReward(actorData.getUid(), A_TYPE, actorData.getBeans(), TITLE, DESC);
                }
            } else if (COIN.equals(prizeVO.getType())) {
                if (actorData.getHeartGot() >= prizeVO.getNums()) {
                    distributionService.sendCoinRecord(actorData.getUid(), -prizeVO.getNums(), TITLE, DESC);
                } else if (actorData.getHeartGot() > 0 && actorData.getHeartGot() < prizeVO.getNums()) {
                    // 金币不足则全扣
                    distributionService.sendCoinRecord(actorData.getUid(), -actorData.getHeartGot(), TITLE, DESC);
                }
            } else if (GIFT.equals(prizeVO.getType())) {
                String uid = actorData.getUid();
                int giftId = prizeVO.getSourceId();
                int num = prizeVO.getNums();
                GiftBagData giftBagData = giftBagDao.getGiftBagData(actorData.getUid(), giftId);
                if (null == giftBagData) {
                    logger.info("user didn't get any number of the gift, uid={} giftId={}", uid, giftId);
                    continue;
                }
                if (giftBagData.getNum() < num) {
                    logger.info("user doesn't have enough gift number, uid={} giftId={}", uid, giftId);
                    num = giftBagData.getNum();
                }
                giftBagData.setNum(giftBagData.getNum() - num);
                giftBagDao.saveGiftBagData(giftBagData);
            } else if (DISCOUNT.equals(prizeVO.getType())) {
                int add = data.getFirstChargedAdd() != null ? data.getFirstChargedAdd() : 0;
                if (add != 0) {
                    if (actorData.getBeans() >= add) {
                        distributionService.deductDiamondsReward(actorData.getUid(), A_TYPE, add, TITLE, DESC);
                    } else if (actorData.getBeans() > 0 && actorData.getBeans() < add) {
                        // 钻石不足则全扣
                        distributionService.deductDiamondsReward(actorData.getUid(), A_TYPE, actorData.getBeans(), TITLE, DESC);
                    }
                } else {
                    // 礼包没有获取钻石则不扣
                    logger.info("user doesn't get discount diamonds, uid={} firstChargedAdd={}", actorData.getUid(), add);
                }
            } else {
                distributionService.sendResourceReward(actorData.getUid(), prizeVO.getSourceId(), RewardTypeEnum.getEnumByName(prizeVO.getType()), BaseDataResourcesConstant.ACTION_DELETE, prizeVO.getDay(), prizeVO.getNums(), DESC, 0);
            }
        }
        Integer rechargeTime = data.getRechargeTime();
        Integer rechargeDiamond = data.getRechargeDiamond();
        String orderId = data.getOrderId();
        if (rechargeTime != null && rechargeDiamond != null && rechargeTime != 0) {
            int nowDate = Integer.parseInt(DateHelper.ARABIAN.formatDateInDay2());
            int rechargeDate = Integer.parseInt(DateHelper.ARABIAN.formatDateInDay2(new Date(rechargeTime * 1000L)));
            if (rechargeDate == nowDate) {
                VersionRechargeRankData rankData = new VersionRechargeRankData();
                rankData.setUid(actorData.getUid());
                rankData.setMtime(rechargeTime);
                rankData.setRechargeDate(rechargeDate);
                rankData.setDiamond(rechargeDiamond);
                int result = rechargeRankDao.removeOne(rankData);
                logger.info("delete uid:{} mTime:{} result:{} success.", actorData.getUid(), rechargeTime, result);
            }
            logger.info("delete uid:{} mTime:{} nowDate:{} rechargeDate:{}.", actorData.getUid(), rechargeTime, nowDate, rechargeDate);
        }
        logger.info("uid:{} orderId:{} recycle first recharge gift bag success.", actorData.getUid(), orderId);
    }

    public int getMySelfRank(String uid) {
        String yesterdayStr = DateHelper.ARABIAN.formatDateInDay();
        int rechargeDate = Integer.parseInt(yesterdayStr.replace("-", ""));
        List<RechargeRankData> rechargeRankDataList = rechargeRankDao.selectRechargeRank(rechargeDate);
        logger.info("rechargeDate={}, rechargeRankDataList.size={}", rechargeDate, CollectionUtils.isEmpty(rechargeRankDataList) ? 0 : rechargeRankDataList.size());
        if (CollectionUtils.isEmpty(rechargeRankDataList)) {
            logger.info("recharge rank data list is empty. rechargeDate={}", rechargeDate);
            return 0;
        }
        List<String> hideUidList = getHideUidList();
        int rank = 0;
        for (RechargeRankData rankData : rechargeRankDataList) {
            String rankDataUid = rankData.getUid();
            // 过滤掉隐藏用户
            if (hideUidList.contains(uid)) {
                continue;
            }
            rank++;
            if (uid.equals(rankDataUid)) {
                return rank;
            }
            if (rank >= RANK_NUM) {
                break;
            }
        }
        return 0;
    }

    @Cacheable(value = "getHideUidList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<String> getHideUidList() {
        return sysConfigDao.getList(SysConfigDao.HIDE_UID_LIST, SysConfigDao.HIDE_UID_LIST);
    }

    public void sendOfficialMsg(String uid, int slang) {
        String actText = slang == SLangType.ARABIC ? ACTION_AR : ACTION_EN;
        String title = slang == SLangType.ARABIC ? NOTICE_AR : NOTICE_EN;
        String body = slang == SLangType.ARABIC ? NOTICE_DESC_AR : NOTICE_DESC_EN;
//        commonOfficialMsg(uid, "", 14, actText, title, body);
        officialMsg(uid, slang, actText, title, body);
    }


    public void commonOfficialMsg(String uid, String picture, int actionType, String actText, String title, String body) {
        OfficialData officialData = new OfficialData();
        officialData.setSubTitle("");
        officialData.setValid(1);
        officialData.setPicture(picture);
        officialData.setTo_uid(uid);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setNews_type(0);
        officialData.setAtype(actionType);
        officialData.setAct(actText);
        officialData.setTitle(title);
        officialData.setBody(body);
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        }
    }


    private void sendNotice(ActorData actorData) {
        RoomNotificationMsg msg = new RoomNotificationMsg();
        String name = actorData.getName();
        msg.setUid(actorData.getUid());
        msg.setUser_name(actorData.getName());
        msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        msg.setText(String.format(TEXT_EN, name));
        msg.setText_ar(String.format(TEXT_AR, name));
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(name);
        object.setHighlightColor("#FFE200");//
        list.add(object);
        HighlightTextObject viewObject = new HighlightTextObject();
        viewObject.setText(VIEW_EN);
        viewObject.setHighlightColor("#37B5FF");//
        list.add(viewObject);
        HighlightTextObject viewArObject = new HighlightTextObject();
        viewArObject.setText(VIEW_AR);
        viewArObject.setHighlightColor("#37B5FF");//
        list.add(viewArObject);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
        msg.setGame_type(FIRST_RECHARGE);
        msg.setHide_head(1);
        roomWebSender.sendRoomWebMsg(RoomWebSender.ALL_ROOM, null, msg, false);
        logger.info("uid:{} send screen msg success", actorData.getUid());
    }

    private void officialMsg(String uid, int slang, String actText, String title, String body) {
//        ActorData actorData = actorDao.getActorDataFromCache(uid);
//        if (actorData == null) {
//            logger.error("can not find actor data. uid={}", uid);
//            return;
//        }
        OfficialData officialData = new OfficialData();
        officialData.setValid(1);
        officialData.setTo_uid(uid);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setNews_type(6);
        officialData.setAtype(14);
        officialData.setAct(actText);
        officialData.setTitle(title);
        officialData.setBody(body);
        officialData.setNtype(0);
        List<OfficialData.AwardInfo> awardList = new ArrayList<>();
        List<PrizeVO> firstRechargeRewards = firstRechargeConfig.getFirstRechargeRewards();
        for (PrizeVO prizeVO : firstRechargeRewards) {
            String name = slang == SLangType.ENGLISH ? prizeVO.getName() : prizeVO.getNameAr();
//            if (DISCOUNT.equals(prizeVO.getType())) {
//                awardList.add(new OfficialData.AwardInfo(
//                        name,
//                        prizeVO.getLink(),
//                        ""));
//            }
            awardList.add(new OfficialData.AwardInfo(
                    name,
                    prizeVO.getLink(),
                    ""));
//            RewardTypeEnum type = RewardTypeEnum.getEnumByName(prizeVO.getType());
//            ResTypeEnum typeEnum = ResTypeEnum.getByType(type.getCode());
//            if (typeEnum == null) {
//                continue;
//            }
//            awardList.add(new OfficialData.AwardInfo(
//                    typeEnum.getNameBySlang(slang),
//                    prizeVO.getLink(),
//                    typeEnum.formatTag(slang, packData.getNum())));
        }
        officialData.setAward_list(awardList);
        officialMsgPush(officialData);
    }

    public void officialMsgPush(OfficialData officialData) {
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            NoticeNewData noticeNewData = new NoticeNewData(officialData.getTo_uid(), officialData.get_id().toString());
            noticeNewData.setNtype(0);
            noticeNewDao.save(noticeNewData);
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg(null, null, officialData.getTo_uid(), msg, false);
        }
    }

}
