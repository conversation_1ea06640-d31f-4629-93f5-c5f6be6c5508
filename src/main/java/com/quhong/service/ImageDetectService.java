package com.quhong.service;

import com.quhong.config.AsyncConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.dto.ImageDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.feign.IDetectService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MomentDao;
import com.quhong.mongo.dao.MonitorNsfwDao;
import com.quhong.mongo.data.MomentData;
import com.quhong.mongo.data.MonitorNsfwData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class ImageDetectService {
    private static final Logger logger = LoggerFactory.getLogger(ImageDetectService.class);
    private static final String PIC_VIOLENT = "https://cdn3.qmovies.tv/test/pic_violation.png";

    @Resource
    private MomentDao momentDao;
    @Resource
    private IDetectService detectService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CreditRiskService creditRiskService;
    @Resource
    private MonitorNsfwDao monitorNsfwDao;
    @Resource
    private CommonTaskService commonTaskService;

    @Async(AsyncConfig.ASYNC_TASK)
    public void momentImageCheck(List<MomentData.Image> imageList, String mid, String uid) {
        boolean modify = false;
        for (int index = 0; index < imageList.size(); index++) {
            MomentData.Image image = imageList.get(index);
            if (detectService.detectImage(new ImageDTO(image.getOrigin(), DetectOriginConstant.MOMENT_PICTURE, uid)).getData().getIsSafe() == 0) {
                logger.info("unsafe image mid={} uid={} originUrl={} index={}", mid, uid, image.getOrigin(), index);
                String momentIndex = mid + "_" + index;
                MonitorNsfwData nsfwData = new MonitorNsfwData(9, uid, image.getOrigin(), 4, 0, DateHelper.getNowSeconds());
                nsfwData.setMoment_index(momentIndex);
                image.setOrigin(PIC_VIOLENT);
                image.setThumbnail(PIC_VIOLENT);
                image.setSafe(0);
                monitorNsfwDao.save(nsfwData);
                modify = true;
            }
        }
        if (modify) {
            momentDao.updateImage(imageList, mid);
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                creditRiskService.creditRisk(uid, actorData.getTn_id(), actorData.getIp(), CreditRiskService.TYPE_MOMENT_IMG, 300, 20, 10);
            }
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", "", "", CommonMqTaskConstant.USER_MOMENT_VIOLATION, 1));
        }
    }
}
