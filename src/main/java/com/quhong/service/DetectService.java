package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.DetectConstant;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.data.AestronData;
import com.quhong.data.DetectFromData;
import com.quhong.data.DetectToData;
import com.quhong.data.dto.FeginVideoDTO;
import com.quhong.data.dto.ImageDTO;
import com.quhong.data.dto.TextDTO;
import com.quhong.data.vo.DetectVO;
import com.quhong.enums.ApiResult;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.CommonConfig;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.DetectUserRecordDao;
import com.quhong.mysql.dao.DiscernImageDao;
import com.quhong.mysql.dao.DiscernTextDao;
import com.quhong.mysql.data.DetectUserRecordData;
import com.quhong.mysql.data.DiscernImageData;
import com.quhong.mysql.data.DiscernTextData;
import com.quhong.redis.DetectRedis;
import com.quhong.redis.ThirdApiCallRedis;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

@Service
public class DetectService {
    private static final Logger logger = LoggerFactory.getLogger(DetectService.class);
    private static final String ACCESS_TOKEN_URL = "https://api.aestron.net/abs-web/audit/token";
    private static final String APP_ID = "a670266574311c8auzv04ul6x3he9lhm";
    private static final String CERTIFICATE = "uxnqp0qaavqneok6to054nlrfuhpgy3glgwgnsh3edgy7quq";  // certificate 证书

    private static final String DETECT_TEXT_URL = "https://api.aestron.net/abs-web/audit/text";
    private static final String DETECT_TEXT_ID = "f8cd664cb93441d6bb620c6b34d6ff9e";  //  文本检测功能服务id
    private static final Integer DETECT_TEXT_LENGTH = 400;  // 文本检测长度
    private static final String DETECT_TEXT_SCREEN = "screenMessage";  // 公屏消息类型
    private static final Integer DETECT_TEXT_TIME = 5000;

    private static final String DETECT_IMAGE_URL = "https://api.aestron.net/abs-web/audit/image";
    private static final String DETECT_IMAGE_ID = "e829e132f7f9465aa78f0a1797eeeeb0";  //  图片检测功能服务id

    private static final List<String> REQUEST_MODULE = Collections.singletonList("all");
    private static final Map<String, Object> paramsMap = new HashMap<>();

    static {
        paramsMap.put("country", "SA");
        paramsMap.put("search_with_business", true);
    }

    private static final String rootDir = "/temp/"; // 视频转码的临时目录

    @Resource
    DetectRedis detectRedis;

    @Resource
    private WebClient webClient;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private DiscernTextDao discernTextDao;
    @Resource
    private DiscernImageDao discernImageDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ImageDetectionApi imageDetectionApi;
    @Resource
    private OSSUploadService ossUploadService;
    @Resource
    private DetectUserRecordDao detectUserRecordDao;
    @Resource
    private DetectServiceManager detectServiceManager;
    @Resource
    private ThirdApiCallRedis thirdApiCallRedis;

    /**
     * 获取访问token
     */

    private String getAccessToken(boolean isCache) {

        if (isCache) {
            String accessToken = detectRedis.getAccessToken();
            if (!StringUtils.isEmpty(accessToken)) {
                return accessToken;
            }
        }

        try {
            int expireTime = DateHelper.getNowSeconds() + 7 * 86400;

            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(ACCESS_TOKEN_URL);
            urlBuilder.queryParam("appidStr", APP_ID);
            urlBuilder.queryParam("cert", CERTIFICATE);
            urlBuilder.queryParam("expires", expireTime);
            String url = urlBuilder.build(false).encode().toUriString();
            HttpResponseData<String> responseData = webClient.sendGet(url, null);

            String body = responseData.getBody();
            if (!StringUtils.isEmpty(body)) {
                JSONObject jsonObject = JSON.parseObject(body);
                logger.info("getAccessToken return result={}", jsonObject);
                if (jsonObject.getInteger("code") != 0) {
                    String warnDesc = ServerConfig.isProduct() ? "bigo检测" : "测试服bigo检测";
                    monitorSender.info("ustar_java_exception", warnDesc, "获取bigo accessToken失败, 失败信息:" + jsonObject.toJSONString());
                    return "";
                }

                String accessToken = jsonObject.getString("data");
                detectRedis.setAccessToken(accessToken);
                return accessToken;
            }

        } catch (Exception e) {
            logger.error("getAccessToken detection error={}", e.getMessage(), e);

        }
        return "";
    }

    /**
     * 用户维度记录文本及图片检测
     */
    private void saveDetectUserRecord(String uid, String sourceType, int detectType, String detectInfo, String score, String resultData) {
        if (StringUtils.isEmpty(uid)) {
            return;
        }
        DetectUserRecordData detectUserRecordData = new DetectUserRecordData();
        detectUserRecordData.setUid(uid);
        detectUserRecordData.setSourceType(sourceType);
        detectUserRecordData.setDetectType(detectType);
        detectUserRecordData.setDetectInfo(detectInfo);
        detectUserRecordData.setScore(score);
        detectUserRecordData.setResultData(resultData);
        detectUserRecordData.setCtime(DateHelper.getNowSeconds());
        detectUserRecordDao.insert(detectUserRecordData);
    }


    /**
     * bigo 脏词记录, 作为缓存
     */

    private void recordDetectText(String fromUid, String origin, String detectWord, String textMd5, Integer isSafe,
                                  String score, String resultData) {
        try {
            int nowSeconds = DateHelper.getNowSeconds();
            DiscernTextData discernTextData = new DiscernTextData();
            discernTextData.setText(detectWord);
            discernTextData.setTextMd5(textMd5);
            discernTextData.setIsSafe(isSafe);
            discernTextData.setCtime(nowSeconds);
            discernTextData.setScore(score);
            discernTextData.setResultData(resultData);
            discernTextDao.addDiscernText(discernTextData);
            if (isSafe == 0) {
                saveDetectUserRecord(fromUid, origin, DetectUserRecordDao.TYPE_TEXT, detectWord, discernTextData.getScore(), discernTextData.getResultData());
            }

        } catch (Exception e) {
            logger.error("recordDetectText error={}", e.getMessage(), e);
        }
    }

    private DiscernTextData discernTextFromDB(String detectWord, String textMd5) {
        try {
            DiscernTextData discernTextData = discernTextDao.getDiscernTextData(textMd5);
            if (discernTextData != null) {
                logger.info("detectText from cache detectWord={}, textMd5={}, discernTextData={}", detectWord, textMd5, discernTextData);
                return discernTextData;
            }
        } catch (Exception e) {
            logger.error("discernTextFromDB error={}", e.getMessage(), e);
        }
        return null;
    }

    private int detectTextFromApi(String fromUid, String origin, String detectWord, String textMd5, String requestId) {
        int isSafe = 1;
        String accessToken = getAccessToken(true);
        if (StringUtils.isEmpty(accessToken)) {
            return isSafe;
        }

        try {
            // 构造请求
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("token", accessToken);

            JSONObject jsonObject = new JSONObject();
            JSONObject batchObject = new JSONObject();
            List<JSONObject> batchList = new ArrayList<>();

            batchObject.put("text_base64", Base64.getEncoder().encodeToString(detectWord.getBytes(StandardCharsets.UTF_8)));
            batchObject.put("request_id", requestId);
            batchObject.put("params", paramsMap);
            batchList.add(batchObject);

            jsonObject.put("service_id", DETECT_TEXT_ID);
            jsonObject.put("request_module", REQUEST_MODULE);
            jsonObject.put("batch", batchList);


            //  发起请求
            long timeMillis = System.currentTimeMillis();
            HttpResponseData<String> responseData = webClient.sendRestfulPost(DETECT_TEXT_URL, jsonObject.toJSONString(), headerMap);
            long costTime = System.currentTimeMillis() - timeMillis;
            logger.info("detectText from Api textMd5={}  detectWord={} timeout={}", textMd5, detectWord, costTime);

            if (costTime >= DETECT_TEXT_TIME) {
                String warnDetail = String.format("调用bigo文本检测接口大于%s ms, 耗时: %s ms, 检测文本: %s", DETECT_TEXT_TIME, costTime, detectWord);
                monitorSender.info("ustar_java_exception", "bigo检测", warnDetail);
            }

            // 解析结果
            if (responseData.getStatus() == 200) {
                String body = responseData.getBody();
                AestronData aestronData = JSON.parseObject(body, AestronData.class);
                logger.info("detectText: {}", aestronData);
                if (aestronData.getStatusCode() != 200) {
                    return isSafe;
                }

                AestronData.BatchConfig batchConfig = aestronData.getBatchResult().get(0);

                switch (batchConfig.getSuggest()) {
                    case "PASS":
                        break;
                    case "REVIEW":
                    case "REJECT":
                        isSafe = 0;
                        break;
                }

                // 异步写记录
                int finalIsSafe = isSafe;
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        recordDetectText(fromUid, origin, detectWord, textMd5, finalIsSafe,
                                String.valueOf(aestronData.getBatchResult().get(0).getRate()), JSON.toJSONString(aestronData));
                    }
                });

            }
            return isSafe;


        } catch (Exception e) {
            logger.error("text detection error={}", e.getMessage());
            return isSafe;
        }
    }


//    public ApiResult<DetectVO> detectTextManage(TextDTO dto) {
//
//        DetectVO vo = new DetectVO();
//        vo.setIsSafe(1);
//        JSONObject detectConfig = commonConfig.getDetectConfig();
//        int switchValue = detectConfig.getIntValue("value");
//        int dailyTextNum = detectConfig.getIntValue("daily_text_num");
//        int startOne = detectConfig.getIntValue("start_one");
//        int endOne = detectConfig.getIntValue("end_one");
//        int startTwo = detectConfig.getIntValue("start_two");
//        int endTwo = detectConfig.getIntValue("end_two");
//        int shumeiTextSw = detectConfig.getIntValue("shumei_text_sw");
//
//        String text = dto.getText().trim();
//        String origin = dto.getOrigin();
//        String fromUid = dto.getFromUid();
//        String requestId = new ObjectId().toString();
//
//
//        if (switchValue == 0 || StringUtils.isEmpty(text)) {
//            return ApiResult.getOk(vo);
//        }
//
//        try {
//            JSONObject jsonObject = JSON.parseObject(text);
//            String content = jsonObject.getString("content");
//            if (!ObjectUtils.isEmpty(content)) {
//                text = content;
//                logger.info("recover test to content:{}", content);
//            }
//        } catch (Exception e) {
//
//        }
//
//        if (text.length() > DETECT_TEXT_LENGTH) {
//            text = text.substring(0, 120);
//        }
//
//        // 先走cache缓存, 再查sql数据库, 没有结果再调用第三方api
//        String textMd5 = DigestUtils.md5DigestAsHex(text.getBytes(StandardCharsets.UTF_8));
//        DiscernTextData discernTextData = discernTextFromDB(text, textMd5);
//        if (discernTextData != null) {
//            Integer safeStatus = discernTextData.getIsSafe();
//            if (safeStatus == 0) {
//                saveDetectUserRecord(fromUid, origin, DetectUserRecordDao.TYPE_TEXT, text, discernTextData.getScore(), discernTextData.getResultData());
//            }
//            vo.setIsSafe(safeStatus);
//            return ApiResult.getOk(vo);
//        }
//
//        if (shumeiTextSw > 0) {
//            String fromText = text;
//            BaseTaskFactory.getFactory().addSlow(new Task() {
//                @Override
//                protected void execute() {
//                    DetectFromData detectFromData = new DetectFromData();
//                    detectFromData.setFromUid(fromUid);
//                    detectFromData.setOrigin(origin);
//                    detectFromData.setText(fromText);
//                    detectFromData.setTextMD5(textMd5);
//                    detectFromData.setRequestId(requestId);
//                    detectFromData.setSelectType(DetectConstant.SHU_MEI);
//                    detectServiceHandle(detectFromData);
//                }
//            });
//        }
//        // 当日使用量
//        int curNum = detectRedis.getTextDailyNum(DetectConstant.BI_GO);
//        int nowHour = LocalDateTime.now(ZoneOffset.ofHours(8)).getHour();
//        if (curNum >= dailyTextNum) {
//            return ApiResult.getOk(vo);
//        }
//
//        // 公屏消息高峰期检测
//        if (DETECT_TEXT_SCREEN.equals(origin)) {
//            if ((nowHour >= startOne && nowHour <= endOne) || (nowHour >= startTwo && nowHour <= endTwo)) {
//                vo.setIsSafe(detectTextFromApi(fromUid, origin, text, textMd5, requestId));
//            } else {
//                return ApiResult.getOk(vo);
//            }
//
//        } else {
//            vo.setIsSafe(detectTextFromApi(fromUid, origin, text, textMd5, requestId));
//        }
//
//
//        int afterNum = detectRedis.incTextDailyNum(DetectConstant.BI_GO, curNum);
//        if (afterNum >= dailyTextNum) {
//            String warnDesc = ServerConfig.isProduct() ? "bigo文本检测" : "测试服bigo文本检测";
//            monitorSender.info("ustar_java_exception", warnDesc, "bigo文本检测数量已用完, 当前设置数量：" + dailyTextNum);
//        }
//
//        detectRedis.incTextMonthNum(DetectConstant.BI_GO);
//        return ApiResult.getOk(vo);
//
//    }


    /**
     * bigo 图像鉴别
     */
    private void recordDetectImage(String fromUid, String origin, String imageUrl, String urlMD5, int isSafe
            , String score, String resultData) {
        try {
            int nowSeconds = DateHelper.getNowSeconds();
            DiscernImageData discernImageData = new DiscernImageData();
            discernImageData.setImageUrl(imageUrl);
            discernImageData.setImageMd5(urlMD5);
            discernImageData.setScore(score);
            discernImageData.setIsSafe(isSafe);
            discernImageData.setResultData(resultData);
            discernImageData.setCtime(nowSeconds);
            discernImageDao.addDiscernImage(discernImageData);
            if (discernImageData.getIsSafe() == 0) {
                saveDetectUserRecord(fromUid, origin, DetectUserRecordDao.TYPE_IMAGE, imageUrl, discernImageData.getScore(), discernImageData.getResultData());
            }
        } catch (Exception e) {
            logger.error("recordDetectImage error={}", e.getMessage(), e);
        }
    }

    private void recordTensorFlowImage(String fromUid, String origin, String imageUrl, String urlMD5) {
        try {
            int nowSeconds = DateHelper.getNowSeconds();
            DiscernImageData discernImageData = new DiscernImageData();
            discernImageData.setImageUrl(imageUrl);
            discernImageData.setImageMd5(urlMD5);
            discernImageData.setScore("0");
            discernImageData.setIsSafe(0);
            discernImageData.setResultData("Detect By TensorFlow Unsafe Image");
            discernImageData.setCtime(nowSeconds);
            discernImageDao.addDiscernImage(discernImageData);

            if (discernImageData.getIsSafe() == 0) {
                saveDetectUserRecord(fromUid, origin, DetectUserRecordDao.TYPE_IMAGE, imageUrl, discernImageData.getScore(), discernImageData.getResultData());
            }

        } catch (Exception e) {
            logger.error("recordTensorFlowImage error={}", e.getMessage(), e);
        }
    }


    private DiscernImageData discernImageFromDB(String imageUrl, String urlMD5) {
        try {
            DiscernImageData discernImageData = discernImageDao.getDiscernImageData(urlMD5);
            if (discernImageData != null) {
                logger.info("detectImage from cache imageUrl={}, urlMD5={}, discernTextData={}", imageUrl, urlMD5, discernImageData);
                return discernImageData;
            }
        } catch (Exception e) {
            logger.error("detectImageFromDB error={}", e.getMessage(), e);
        }
        return null;
    }


    private int detectImageFromApi(String fromUid, String origin, String imageUrl, String urlMD5, String requestId) {

        int isSafe = 1;
        String accessToken = getAccessToken(true);
        if (StringUtils.isEmpty(accessToken)) {
            return isSafe;
        }

        try {
            // 构造请求
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("token", accessToken);

            JSONObject jsonObject = new JSONObject();
            JSONObject batchObject = new JSONObject();
            List<JSONObject> batchList = new ArrayList<>();

            String simpleUrl = ImageUrlGenerator.generateUrlByMode(imageUrl,
                    true, ImageUrlGenerator.MODE_500);

            batchObject.put("image_url", simpleUrl);
            batchObject.put("request_id", requestId);
            batchList.add(batchObject);

            jsonObject.put("service_id", DETECT_IMAGE_ID);
            jsonObject.put("request_module", REQUEST_MODULE);
            jsonObject.put("batch", batchList);

            // 发起请求
            long timeMillis = System.currentTimeMillis();
            HttpResponseData<String> responseData = webClient.sendRestfulPost(DETECT_IMAGE_URL, jsonObject.toJSONString(), headerMap);
            long costTime = System.currentTimeMillis() - timeMillis;
            logger.info("detectImage from API simpleUrl={} timeout={}", simpleUrl, costTime);

            // 解析结果
            if (responseData.getStatus() == 200) {
                AestronData aestronData = JSON.parseObject(responseData.getBody(), AestronData.class);
                logger.info("aestronData: {}", aestronData);
                if (aestronData.getStatusCode() != 200) {
                    return isSafe;
                }

                AestronData.BatchConfig batchConfig = aestronData.getBatchResult().get(0);

                switch (batchConfig.getSuggest()) {
                    case "PASS":
                        break;
                    case "REVIEW":
                    case "REJECT":
                        isSafe = 0;
                        break;
                }

                // 异步写记录
                int finalIsSafe = isSafe;
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        recordDetectImage(fromUid, origin, imageUrl, urlMD5, finalIsSafe,
                                String.valueOf(aestronData.getBatchResult().get(0).getRate()), JSON.toJSONString(aestronData));
                    }
                });

            }
            return isSafe;

        } catch (Exception e) {
            logger.error("image detection error={}", e.getMessage());
            return isSafe;
        }
    }


    private boolean detectByTensorFlow(String fromUid, String origin, String imageUrl, String urlMD5) {
        boolean safeImage = imageDetectionApi.isSafeImage(imageUrl);
        if (!safeImage) {
            logger.info("detectImage from tensorflow imageUrl={}, urlMD5={}", imageUrl, urlMD5);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    recordTensorFlowImage(fromUid, origin, imageUrl, urlMD5);
                }
            });
        }
        return safeImage;
    }


//    public ApiResult<DetectVO> detectImageManage(ImageDTO dto) {
//
//        DetectVO vo = new DetectVO();
//        vo.setIsSafe(1);
//
//        JSONObject detectConfig = commonConfig.getDetectConfig();
//        int switchValue = detectConfig.getIntValue("value");
//        int dailyImageNum = detectConfig.getIntValue("daily_image_num");
//        int shumeiImageSw = detectConfig.getIntValue("shumei_image_sw");
//
//        String imageUrl = dto.getImage_url();
//        String fromUid = dto.getFromUid();
//        String origin = dto.getOrigin();
//        String urlMD5 = DigestUtils.md5DigestAsHex(imageUrl.getBytes(StandardCharsets.UTF_8));
//        String requestId = new ObjectId().toString();
//
//        if (StringUtils.isEmpty(imageUrl)) {
//            return ApiResult.getOk(vo);
//        }
//
//        // 先走缓存, 再走sql, 然后到TensorFlow, 最后第三方api
//        DiscernImageData discernImageData = discernImageFromDB(imageUrl, urlMD5);
//        if (discernImageData != null) {
//            Integer safeStatus = discernImageData.getIsSafe();
//            if (safeStatus == 0) {
//                saveDetectUserRecord(fromUid, origin, DetectUserRecordDao.TYPE_IMAGE, imageUrl, discernImageData.getScore(), discernImageData.getResultData());
//            }
//
//            vo.setIsSafe(safeStatus);
//            return ApiResult.getOk(vo);
//        }
//
//        if (shumeiImageSw > 0) {
//            BaseTaskFactory.getFactory().addSlow(new Task() {
//                @Override
//                protected void execute() {
//                    DetectFromData detectFromData = new DetectFromData();
//                    detectFromData.setFromUid(fromUid);
//                    detectFromData.setOrigin(origin);
//                    detectFromData.setImageUrl(imageUrl);
//                    detectFromData.setUrlMD5(urlMD5);
//                    detectFromData.setRequestId(requestId);
//                    detectFromData.setSelectType(DetectConstant.SHU_MEI);
//                    detectServiceHandle(detectFromData);
//                }
//            });
//        }
//
//        boolean safeImage = detectByTensorFlow(fromUid, origin, imageUrl, urlMD5);
//        if (!safeImage) {
//            vo.setIsSafe(0);
//            return ApiResult.getOk(vo);
//        }
//
//        // 走第三方api
//        int curNum = detectRedis.getImageDailyNum(DetectConstant.BI_GO);
//        if (curNum >= dailyImageNum) {
//            return ApiResult.getOk(vo);
//        }
//
//        vo.setIsSafe(detectImageFromApi(fromUid, origin, imageUrl, urlMD5, requestId));
//        int afterNum = detectRedis.incImageDailyNum(DetectConstant.BI_GO, curNum);
//        if (afterNum >= dailyImageNum) {
//            String warnDesc = ServerConfig.isProduct() ? "bigo图像检测" : "测试服bigo图像检测";
//            monitorSender.info("ustar_java_exception", warnDesc, "bigo图像检测数量已用完, 当前设置数量：" + dailyImageNum);
//        }
//        detectRedis.incImageMonthNum(DetectConstant.BI_GO);
//        return ApiResult.getOk(vo);
//    }

    public ApiResult<DetectVO> handleVideoUrl(FeginVideoDTO dto) {
        DetectVO vo = new DetectVO();
        String oldUrl = dto.getVideo_url();
        String uid = dto.getUid();
        uid = StringUtils.isEmpty(uid) ? "empty" : uid.substring(uid.length() - 10); // 取uid最后的10位
        vo.setNewVideoUrl(oldUrl);
        vo.setIsSafe(1);
        if (!StringUtils.isEmpty(oldUrl)) {
            String fileName = getFileName(oldUrl);
            if (!StringUtils.isEmpty(fileName)) {
                String destFileName = "fast_" + uid + "_" + fileName;
                String newUrl = ossUploadService.doesObjectExist(destFileName);
                if (!StringUtils.isEmpty(newUrl)) {
                    vo.setNewVideoUrl(newUrl);
                    logger.info("upload already done oldUrl={} newUrl={}", oldUrl, newUrl);
                } else {
                    String srcPath = loadFileFromUrl(oldUrl, fileName);
                    if (!StringUtils.isEmpty(srcPath)) {
                        String destPath = rootDir + destFileName;
                        if (fastVideo(srcPath, destPath)) {
                            long millis = System.currentTimeMillis();
                            newUrl = ossUploadService.upload(destFileName, destPath);
                            logger.info("upload done oldUrl={}  newUrl={} cost={}", oldUrl, newUrl, System.currentTimeMillis() - millis);
                            if (!StringUtils.isEmpty(newUrl)) {
                                vo.setNewVideoUrl(newUrl);
                            }
                        }
//                    deleteFiles(srcPath, destPath);
                    } else {
                        logger.info("handleVideoUrl,srcPath is empty  oldUrl={}", oldUrl);
                    }
                }
            } else {
                logger.info("handleVideoUrl,fileName is empty  oldUrl={}", oldUrl);
            }
        } else {
            logger.info("handleVideoUrl,oldUrl is empty");
        }

        return ApiResult.getOk(vo);
    }


    private void deleteFiles(String srcPath, String destPath) {
        File srcFile = new File(srcPath);
        File destFile = new File(destPath);
        srcFile.delete();
        destFile.delete();
    }

    private String getFileName(String url) {
        String fileName = "";
        try {
            URL urlD = new URL(url);
            String uPath = urlD.getPath();  // ex: /user/0E5612B34AF4AFAC024B0E892B68F9C6.png
            String[] spUrl = uPath.split("/");
            fileName = spUrl[spUrl.length - 1];
        } catch (Exception e) {
            logger.error("handleVideoUrl error ,url={} msg={}", url, e.getMessage(), e);
        }
        return fileName;
    }

    private String loadFileFromUrl(String url, String fileName) {
        OutputStream outputStream = null;
        try {
            Path path = Paths.get(rootDir);
            if (!Files.exists(path)) {
                Files.createDirectory(path);
            }
            String filePath = rootDir + fileName;
            long millis = System.currentTimeMillis();
            byte[] resp = webClient.getBytes(url);
            logger.info("down load success url={} cost={}", url, System.currentTimeMillis() - millis);
            outputStream = new BufferedOutputStream(new FileOutputStream(filePath));
            outputStream.write(resp);
            outputStream.close();
            return filePath;
        } catch (Exception e) {
            logger.error("loadFileFromUrl, msg={}", e.getMessage(), e);
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (Exception e) {
                logger.error("loadFileFromUrl, msg={}", e.getMessage(), e);
            }
        }
        return "";
    }

    private boolean fastVideo(String srcPath, String destPath) {
        long millis = System.currentTimeMillis();
        boolean isSuccess = false;
        try {
            BufferedReader br = null;
            try {
                ProcessBuilder builder = new ProcessBuilder();
                // ffmpeg -i input.mp4 -c copy -f mp4 -movflags faststart output.mp4
                // ffmpeg -i input.mp4 -c copy -f mp4 -b:v 1M  -r 20  -fs 10MB -movflags faststart output.mp4
                ArrayList<String> command = new ArrayList<>();
                command.add("ffmpeg");
                command.add("-i");
                command.add(srcPath);
                command.add("-c");
                command.add("copy");
                command.add("-f");
                command.add("mp4");
                command.add("-movflags");
                command.add("faststart");
                command.add(destPath);
                builder.command(command);
                builder.redirectErrorStream(true);
                Process process = builder.start();
                br = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line = null;
                while ((line = br.readLine()) != null) {
//                    logger.info("fast video 。。。 srcPath={}, line={}", srcPath, line);
                }
                isSuccess = true;
            } catch (Exception e) {
                logger.error("1 fastVideo srcPath={}, msg={}", srcPath, e.getMessage(), e);
            } finally {
                if (br != null) {
                    try {
                        br.close();
                    } catch (Exception e) {
                        logger.error("2 fastVideo srcPath={}, msg={}", srcPath, e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("3 fastVideo srcPath={}, msg={}", srcPath, e.getMessage(), e);
        }
        logger.info("fast video done srcPath={} destPath={} isSuccess={} cost={}", srcPath, destPath, isSuccess, System.currentTimeMillis() - millis);
        return isSuccess;
    }


    public DetectToData detectServiceHandle(DetectFromData detectFromData) {
        AbstractDetectService abstractDetectService = detectServiceManager.getDetectService(detectFromData.getSelectType());
        DetectToData detectToData = abstractDetectService.detect(detectFromData);
        return detectToData;
    }


    public ApiResult<DetectVO> detectText(TextDTO dto) {
        DetectVO vo = new DetectVO();
        vo.setIsSafe(1);
        JSONObject detectConfig = commonConfig.getDetectConfig();
        int switchValue = detectConfig.getIntValue("value");
        int dailyTextNum = detectConfig.getIntValue("daily_text_num");
        int startOne = detectConfig.getIntValue("start_one");
        int endOne = detectConfig.getIntValue("end_one");
        int startTwo = detectConfig.getIntValue("start_two");
        int endTwo = detectConfig.getIntValue("end_two");

        String text = dto.getText().trim();
        String origin = dto.getOrigin();
        String fromUid = dto.getFromUid();
        String requestId = new ObjectId().toString();


        if (switchValue == 0 || StringUtils.isEmpty(text)) {
            logger.info("switchValue is close");
            return ApiResult.getOk(vo);
        }

        if (text.length() > DETECT_TEXT_LENGTH) {
            text = text.substring(0, 300);
        }

        String thirdType = DetectConstant.SHU_MEI;
        // 先走cache缓存, 再查sql数据库, 没有结果再调用第三方api
        String textMd5 = DigestUtils.md5DigestAsHex(text.getBytes(StandardCharsets.UTF_8));
        DiscernTextData discernTextData = discernTextFromDB(text, textMd5);

        if (discernTextData != null) {
            Integer safeStatus = discernTextData.getIsSafe();
            if (safeStatus == 0) {
                String finalText1 = text;
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        saveDetectUserRecord(fromUid, origin, DetectUserRecordDao.TYPE_TEXT, finalText1, discernTextData.getScore(), discernTextData.getResultData());
                    }
                });
            }
            vo.setIsSafe(safeStatus);
            return ApiResult.getOk(vo);
        }

        // 当日使用量
        int curNum = detectRedis.getTextDailyNum(thirdType);
        int nowHour = LocalDateTime.now(ZoneOffset.ofHours(8)).getHour();
        if (curNum >= dailyTextNum) {
            return ApiResult.getOk(vo);
        }

        DetectFromData detectFromData = new DetectFromData();
        detectFromData.setFromUid(fromUid);
        detectFromData.setOrigin(origin);
        detectFromData.setText(text);
        detectFromData.setTextMD5(textMd5);
        detectFromData.setRequestId(requestId);
        detectFromData.setSelectType(thirdType);

        DetectToData detectToData;
        // 公屏消息高峰期检测
        if (DETECT_TEXT_SCREEN.equals(origin)) {
            if ((nowHour >= startOne && nowHour <= endOne) || (nowHour >= startTwo && nowHour <= endTwo)) {
                detectToData = detectServiceHandle(detectFromData);
                vo.setIsSafe(detectToData.getIsSafe());
            } else {
                logger.info("公屏消息不在配置时间，不检测");
                return ApiResult.getOk(vo);
            }

        } else {
            detectToData = detectServiceHandle(detectFromData);
            vo.setIsSafe(detectToData.getIsSafe());
        }

        DetectToData finalDetectToData = detectToData;
        String finalText = text;
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                int afterNum = detectRedis.incTextDailyNum(thirdType, curNum);
                thirdApiCallRedis.incApiCallNum(ThirdApiCallRedis.SHU_MEI_TEXT_DETECT);
                if (afterNum >= dailyTextNum) {
                    String warnDesc = ServerConfig.isProduct() ? thirdType + "文本检测" : "测试服" + thirdType + "文本检测";
                    monitorSender.info("ustar_java_exception", warnDesc, thirdType + "文本检测数量已用完, 当前设置数量：" + dailyTextNum);
                }
                detectRedis.incTextMonthNum(thirdType);
                if (finalDetectToData.getResultData() != null) {
                    recordDetectText(fromUid, origin, finalText, textMd5,
                            finalDetectToData.getIsSafe(), String.valueOf(finalDetectToData.getScore()), finalDetectToData.getResultData());
                }
            }
        });
        return ApiResult.getOk(vo);
    }


    public ApiResult<DetectVO> detectImage(ImageDTO dto) {

        DetectVO vo = new DetectVO();
        vo.setIsSafe(1);

        JSONObject detectConfig = commonConfig.getDetectConfig();
        int switchValue = detectConfig.getIntValue("value");
        int dailyImageNum = detectConfig.getIntValue("daily_image_num");

        String imageUrl = dto.getImage_url();
        String fromUid = dto.getFromUid();
        String origin = dto.getOrigin();
//        String imageUrl2 = imageUrl + "new";
        String urlMD5 = DigestUtils.md5DigestAsHex(imageUrl.getBytes(StandardCharsets.UTF_8));

        Optional<UrlUtils.UrlPathInfo> urlPathInfo = UrlUtils.getUrlPathInfo(imageUrl);

        if(urlPathInfo.isPresent()){
            UrlUtils.UrlPathInfo info = urlPathInfo.get();
            String pathSegment = info.getPathSegment();
            String fileName = info.getFileName();
            if (DetectOriginConstant.DETECT_ORIGIN_IMG_LIST.contains(origin)
                    && !StringUtils.isEmpty(fileName)) {
                urlMD5 =  DigestUtils.md5DigestAsHex(fileName.getBytes(StandardCharsets.UTF_8));
                logger.info("urlPathInfo present detectImage fromUid={} urlMD5={} fileName={} origin={} pathSegment={} "
                        ,fromUid, urlMD5, fileName, origin,pathSegment);
            }
        }
        String requestId = new ObjectId().toString();


        // 先走缓存, 再走sql, 然后到TensorFlow, 最后第三方api
        DiscernImageData discernImageData = discernImageFromDB(imageUrl, urlMD5);
        if (discernImageData != null) {
            Integer safeStatus = discernImageData.getIsSafe();
            if (safeStatus == 0) {
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        saveDetectUserRecord(fromUid, origin,
                                DetectUserRecordDao.TYPE_IMAGE, imageUrl, discernImageData.getScore(), discernImageData.getResultData());

                    }
                });
            }
            vo.setIsSafe(safeStatus);
            return ApiResult.getOk(vo);
        }

//        boolean safeImage = detectByTensorFlow(fromUid, origin, imageUrl, urlMD5);
//        if (!safeImage) {
//            vo.setIsSafe(0);
//            return ApiResult.getOk(vo);
//        }

        String type = DetectConstant.SHU_MEI;
        // 走第三方api
        int curNum = detectRedis.getImageDailyNum(type);
        if (curNum >= dailyImageNum) {
            return ApiResult.getOk(vo);
        }

        DetectFromData detectFromData = new DetectFromData();
        detectFromData.setFromUid(fromUid);
        detectFromData.setOrigin(origin);
        detectFromData.setImageUrl(imageUrl);
        detectFromData.setUrlMD5(urlMD5);
        detectFromData.setRequestId(requestId);
        detectFromData.setSelectType(type);

        DetectToData detectToData = detectServiceHandle(detectFromData);
        vo.setIsSafe(detectToData.getIsSafe());
        vo.setHitText(detectToData.getHitText());
        String finalUrlMD = urlMD5;
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                int afterNum = detectRedis.incImageDailyNum(type, curNum);
                thirdApiCallRedis.incApiCallNum(ThirdApiCallRedis.SHU_MEI_IMAGE_DETECT);
                if (afterNum >= dailyImageNum) {
                    String warnDesc = ServerConfig.isProduct() ? type + "图像检测" : "测试服" + type + "图像检测";
                    monitorSender.info("ustar_java_exception", warnDesc, type + "图像检测数量已用完, 当前设置数量：" + dailyImageNum);
                }
                detectRedis.incImageMonthNum(type);
                recordDetectImage(fromUid, origin, imageUrl, finalUrlMD, detectToData.getIsSafe(),
                        String.valueOf(detectToData.getScore()), detectToData.getResultData());

            }
        });
        return ApiResult.getOk(vo);
    }

}
