package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.*;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.config.MomentConfig;
import com.quhong.config.RewardGiftConfig;
import com.quhong.constant.*;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.data.MomentTopicSetTopData;
import com.quhong.dto.TextDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IFriendService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.Comment;
import com.quhong.mongo.data.MomentData;
import com.quhong.mongo.data.MomentNotice;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.MomentBackgroundData;
import com.quhong.mysql.data.MomentRewardData;
import com.quhong.mysql.data.MomentTopicData;
import com.quhong.mysql.data.MomentTopicMemberData;
import com.quhong.redis.*;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.user.CustomerServiceUser;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.CDNUtils;
import com.quhong.utils.PageUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class MomentService {
    private static final Logger logger = LoggerFactory.getLogger(MomentService.class);
    private static final List<Integer> SELF_SHOW = Collections.emptyList();
    private static final List<Integer> FRIEND_SHOW = Arrays.asList(1, 2);
    private static final List<Integer> STRANGER_SHOW = Collections.singletonList(1);

    private static final int MOMENT_SIZE = 10;
    private static final int FRIEND_LIST_SIZE = 10;
    private static final int LIKE_LIST_SIZE = 20;
    private static final int PERSONAL_LIST_SIZE = 10;
    private static final int COMMENTS_LIST_SIZE = 10;
    private static final int NOTICE_LIST_SIZE = 10;
    private static final int COMMENT_DETAIL_LIST_SIZE = 10;
    private static final int SEND_GIFT_REWARD_ATYPE = 211;
    private static final String SEND_GIFT_REWARD_TITLE = "Moment Send Gift";
    private static final String SEND_GIFT_REWARD_DESC = "send gift to %s in moment.";
    private static final int RECEIVE_GIFT_REWARD_ATYPE = 212;
    private static final String RECEIVE_GIFT_REWARD_TITLE = "Moment Receive Gift";
    private static final String RECEIVE_GIFT_REWARD_DESC = "receive gift from %s in moment.";
    private static final int BUY_MOMENT_BACKGROUND_ATYPE = 213;
    private static final String BUY_MOMENT_BACKGROUND_TITLE = "Buy Moment Background";
    private static final String BUY_MOMENT_BACKGROUND_DESC = "buy background in moment.";
    private static final int FIRST_COMMENT_COUNT_TIME = 1745921770;
    private static final int HOT_COMMENT_COUNT_NUM = 2;

    @Resource
    private MomentDao momentDao;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private BlockMomentRedis blockMomentRedis;
    @Resource
    private IDetectService detectService;
    @Resource
    private TimelineService timelineService;
    @Resource
    private MomentNoticeService momentNoticeService;
    @Resource
    private UserLevelTaskService levelTaskService;
    @Resource
    private MomentConfig momentConfig;
    @Resource
    private MomentShowRedis momentShowRedis;
    @Resource
    private FeaturedRedis featuredRedis;
    @Resource
    private CommentDao commentDao;
    @Resource
    private TopMomentRedis topMomentRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private FollowDao followDao;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private LocalCacheService localCacheService;
    @Resource
    private IFriendService friendService;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private MsgListService msgListService;
    @Resource
    private ImageDetectService imageDetectService;
    @Resource
    private EventReport eventReport;
    @Resource
    private RecommendService recommendService;
    @Resource
    private RewardGiftConfig rewardGiftConfig;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    protected MqSenderService mqService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private MomentRewardService momentRewardService;
    @Resource
    private MomentThemeService momentThemeService;
    @Resource
    private UserMonitorService userMonitorService;
    @Resource
    private IndexBannerDao indexBannerDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private BadgeService badgeService;
    @Resource
    private VipPrivilegeService vipPrivilegeService;
    @Resource
    private CreditRiskService creditRiskService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private MomentTopicDao momentTopicDao;
    @Resource
    private MomentTopicMemberDao momentTopicMemberDao;
    @Resource
    private MomentTopicService momentTopicService;
    @Resource
    private MomentTopicFeaturedRedis momentTopicFeaturedRedis;
    @Resource
    private CommentSuggestDao commentSuggestDao;
    @Resource
    private MomentTopicSetTopRedis momentTopicSetTopRedis;
    @Resource
    private NearbyService nearbyService;
    @Resource
    private ActorCommonService actorCommonService;
    @Resource
    private FeaturedSaRedis featuredSaRedis;
    @Resource
    private NewSaRedis newSaRedis;

    private void checkPrivilege(PublishDTO req) {
        if (StringUtils.isEmpty(req.getText())) {
            // 转发朋友圈、分享链接、发布只有图片的朋友圈支持不输入内容
            if (null != req.getQuote() || null != req.getImgs()) {
                req.setText("");
            } else {
                logger.info("publish moment text is empty uid={}", req.getUid());
                throw new CommonException(MomentHttpCode.PARAM_ERROR);
            }
        }
        if (req.getText().length() > 1024) {
            throw new CommonException(MomentHttpCode.LENGTH_NOT_ALLOW);
        }
        if (req.getImgs() != null && req.getImgs().size() > 9) {
            throw new CommonException(MomentHttpCode.IMG_LIMIT, 9);
        }
        if (req.getAt_list() != null && req.getAt_list().size() > 10) {
            throw new CommonException(MomentHttpCode.AT_LIMIT, 10);
        }
        if (req.getQuote() != null) {
            if (StringUtils.isEmpty(req.getQuote().getAction()) || StringUtils.isEmpty(req.getQuote().getContent())) {
                logger.info("publish moment quote invalid uid={} quote={}", req.getUid(), JSONObject.toJSONString(req.getQuote()));
                throw new CommonException(MomentHttpCode.PARAM_ERROR);
            }
            if (req.getQuote().getType() == MomentConstant.QUOTE_REPOST && req.getImgs() != null && req.getImgs().size() > 1) {
                throw new CommonException(MomentHttpCode.IMG_LIMIT, 1);
            }
        }
        if (req.getTopicRid() != null && req.getTopicRid() > 0) {
            if (req.getShow() != 1) {
                logger.info("publish moment must public show={} topic={}. uid={}", req.getShow(), req.getTopicRid(), req.getUid());
                throw new CommonException(MomentHttpCode.TOPIC_MUST_PUBLIC);
            }
            MomentTopicData momentTopicData = momentTopicDao.selectValidByTopicRid(req.getTopicRid());
            if (momentTopicData == null) {
                logger.info("publish moment topic invalid show={} topicRid={}. uid={}", req.getShow(), req.getTopicRid(), req.getUid());
                throw new CommonException(MomentHttpCode.PARAM_ERROR);
            }
            if (momentTopicMemberDao.selectBlack(momentTopicData.getId(), req.getUid()) != null) {
                logger.info("publish moment, actor account has block by topic. uid={}", req.getUid());
                throw new CommonException(MomentHttpCode.TOPIC_MEMBER_BLACK);
            }
            req.setTopicId(momentTopicData.getId());
        }

        checkBlock(req);
        checkFroze(req);
//        if (userLevelDao.getUserLevel(req.getUid()) < 2) {
//            throw new CommonException(MomentHttpCode.LEVEL_LIMIT, 2);
//        }
        checkDirtyWord(req.getUid(), req.getText(), DetectOriginConstant.MOMENT_POST);
        checkPublishLimit(req.getUid());
    }

    /**
     * 发布朋友圈频率
     */
    private void checkPublishLimit(String uid) {
        int currentTime = DateHelper.getNowSeconds();
        int pastHourTime = currentTime - 3600;
        int pastFiveTime = currentTime - 300;
        List<MomentData> recentlyMomentList = momentDao.findRecentlyMomentListByTime(uid, pastHourTime, currentTime);
        int pastHourCount = CollectionUtils.isEmpty(recentlyMomentList) ? 0 : recentlyMomentList.size();
        long pastFiveTimeCount = CollectionUtils.isEmpty(recentlyMomentList) ? 0 : recentlyMomentList.stream().filter(item -> item.getC_time() >= pastFiveTime).count();
        if (pastHourCount >= 10) {
            throw new CommonException(MomentHttpCode.MOMENT_POST_HOUR_LIMIT);
        }
        if (pastFiveTimeCount >= 2) {
            throw new CommonException(MomentHttpCode.MOMENT_POST_MINUTE_LIMIT);
        }
    }


    private void checkDirtyWord(String uid, String text, String origin) {
        if (detectService.detectText(new TextDTO(text, origin, uid)).getData().getIsSafe() == 0) {
            logger.info("Dirty word in message is not allowed!. uid={} msg={}", uid, text);
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                creditRiskService.asyncCreditRisk(uid, actorData.getTn_id(), actorData.getIp(), CreditRiskService.TYPE_MOMENT_MSG, 200, 10, 5);
            }
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", "", "", CommonMqTaskConstant.USER_MOMENT_VIOLATION, 1));
            throw new CommonException(MomentHttpCode.DIRTY_WORD);
        }
    }

    private void checkBlock(HttpEnvData req) {
        if (blockMomentRedis.isBlock(req.getUid())) {
            logger.info("publish moment, actor account has block. uid={}", req.getUid());
            throw new CommonException(MomentHttpCode.MOMENT_BLOCK);
        }
    }

    private void checkFroze(HttpEnvData req) {
        if (!userMonitorDao.notFreezeOrBan(req.getUid())) {
            logger.info("publish moment, actor account has frozen. uid={}", req.getUid());
            throw new CommonException(MomentHttpCode.MOMENT_FROZEN);
        }
    }

    private List<MomentData.Image> formatImageList(List<MomentImageDTO> imgList) {
        if (CollectionUtils.isEmpty(imgList)) {
            return Collections.emptyList();
        }
        List<MomentData.Image> resultList = new ArrayList<>();
        for (MomentImageDTO imageDTO : imgList) {
            MomentData.Image image = new MomentData.Image();
            image.setOrigin(CDNUtils.getCdnUrl(imageDTO.getUrl()));
            image.setThumbnail(ImageUrlGenerator.generateUrl(image.getOrigin(), 400, 400, false));
            image.setSafe(1);
            image.setWidth(imageDTO.getWidth());
            image.setHeight(imageDTO.getHeight());
            resultList.add(image);
        }
        return resultList;
    }

    private MomentBackgroundData handleMomentTheme(PublishDTO req, MomentData momentData) {
        MomentData.Theme theme = req.getTheme();
        if (null == theme) {
            return null;
        }
        MomentBackgroundData backgroundData = momentThemeService.getById(theme.getBgId());
        if (null == backgroundData) {
            logger.error("get moment background data by id error. id={}", theme.getBgId());
            throw new CommonException(MomentHttpCode.PARAM_ERROR);
        }
        req.getTheme().setBg(backgroundData.getImageUrl());
        req.getTheme().setFontColor(backgroundData.getFontColor());
        momentData.setTheme(req.getTheme());
        if (1 == backgroundData.getCostType()) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(req.getUid());
            moneyDetailReq.setChanged(-backgroundData.getPrice());
            moneyDetailReq.setAtype(BUY_MOMENT_BACKGROUND_ATYPE);
            moneyDetailReq.setTitle(BUY_MOMENT_BACKGROUND_TITLE);
            moneyDetailReq.setDesc(BUY_MOMENT_BACKGROUND_DESC);
            ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
            if (result.isError()) {
                if (1 == result.getCode().getCode()) {
                    throw new CommonException(MomentHttpCode.INSUFFICIENT_DIAMONDS);
                }
                logger.error("reduce beans error, uid={} msg={}", req.getUid(), result.getCode().getMsg());
                throw new CommonException(HttpCode.SERVER_ERROR);
            }
        } else if (2 == backgroundData.getCostType()) {
            boolean isSuccess = heartRecordDao.changeHeart(req.getUid(), -backgroundData.getPrice(),
                    BUY_MOMENT_BACKGROUND_TITLE, BUY_MOMENT_BACKGROUND_DESC);
            if (!isSuccess) {
                throw new CommonException(MomentHttpCode.INSUFFICIENT_GOLDS);
            }
        } else if (0 == backgroundData.getCostType()) {
            logger.info("use free moment background. uid={} bgId={}", req.getUid(), theme.getBgId());
        } else {
            logger.error("cost type not support. costType={}", backgroundData.getCostType());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
        return backgroundData;
    }

    public String publish(PublishDTO req) {
        checkPrivilege(req);
        List<MomentData.Image> images = formatImageList(req.getImgs());
        int nowSeconds = DateHelper.getNowSeconds();
        MomentData momentData = new MomentData(req.getUid(), req.getText(), images, req.getLocation(), req.getShow(), req.getAt_list(), nowSeconds);
        MomentBackgroundData momentBackgroundData = handleMomentTheme(req, momentData);
        if (null != req.getQuote()) {
            if (req.getQuote().getType() == MomentConstant.QUOTE_REPOST) {
                logger.info("repost moment uid={} mid={}", req.getUid(), req.getQuote().getAction());
                // 增加转发次数
                momentDao.incrMomentRepost(req.getQuote().getAction());
            }
            momentData.setQuote(req.getQuote());
        }
        if (null != req.getTopicId()) {
            momentData.setTopicId(req.getTopicId());
            asyncPublishMoment(req.getUid(), req.getTopicId());
        }
        ActorData actorData = actorDao.getActorData(req.getUid());
        if (actorData != null) {
            momentData.setCountry(actorData.getCountry());
        }
        momentDao.save(momentData);
        if (null == momentData.get_id()) {
            logger.error("save moment error. uid={}", req.getUid());
            throw new CommonException();
        }
        String mid = momentData.get_id().toString();
        if (req.getShow() == MomentConstant.MOMENT_PUBLIC || req.getShow() == MomentConstant.MOMENT_FRIENDS) {
            timelineService.makeTimeline(req.getUid(), momentData, req.getShow() == MomentConstant.MOMENT_PUBLIC);
            if (!CollectionUtils.isEmpty(req.getAt_list()) && !StringUtils.isEmpty(req.getText())) {
                Set<String> atSet = new HashSet<>();
                for (MomentData.AtUser atUser : req.getAt_list()) {
                    if (null != atUser && atUser.getAid().length() == 24) {
                        if (atSet.contains(atUser.getAid())) {
                            continue;
                        }
                        atSet.add(atUser.getAid());
                        MomentNotice momentNotice = new MomentNotice(atUser.getAid(), req.getUid(),
                                MomentConstant.NOTICE_PUBLISH_AT, mid, momentData.getC_time(),
                                req.getText(), req.getAt_list(), null);
                        fillMomentNotice(momentData, momentNotice);
                        momentNoticeService.addNotice(atUser.getAid(), momentNotice, null);
                    }
                }
            }
        }
        if (images.size() > 0) {
            if(req.getLocation()!=null && req.getLocation().startsWith(MomentConstant.NOT_CHECK_IMAGE_PREFIX)){
                logger.info("not check image. mid={}", mid);
            }else {
                imageDetectService.momentImageCheck(images, mid, req.getUid());
            }
        }
        // 用户等级任务
        levelTaskService.sendTaskDataToMq(new UserLevelTaskData(req.getUid(), UserLevelConstant.POST_MOMENT));
        // 任务：发布动态
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(req.getUid(), "", "", mid, CommonMqTaskConstant.POST_MOMENT, 1));
        // 埋点
        MomentSendLogEvent event = new MomentSendLogEvent();
        event.setUid(momentData.getUid());
        event.setMoment_id(mid);
        event.setMoment_visible_range(momentData.getShow());
        event.setIs_at(CollectionUtils.isEmpty(momentData.getAt_list()) ? 0 : 1);
        event.setIs_picture(CollectionUtils.isEmpty(momentData.getImgs()) ? 0 : 1);
        event.setQuote_type(null == momentData.getQuote() ? 0 : momentData.getQuote().getType());
        event.setQuote_data(momentData.getQuote() == null ? "" : JSONObject.toJSONString(momentData.getQuote()));
        if (null != momentBackgroundData) {
            event.setMoment_background_id(momentBackgroundData.getId());
            event.setMoment_background_type(momentBackgroundData.getCostType());
        }
        event.setActive_id(req.getActiveId());
        event.setCtime(momentData.getC_time());

        if (momentData.getTopicId() > 0) {
            MomentTopicData momentTopicData = momentTopicDao.selectByIdFromCache(momentData.getTopicId());
            if (momentTopicData != null) {
                event.setIs_topic(1);
                event.setTopic_id(momentTopicData.getRid());
                event.setTopic_name(momentTopicData.getName());
                event.setTopic_create_uid(momentTopicData.getOwnerUid());
            }
        }
        eventReport.track(new com.quhong.analysis.EventDTO(event));
        return mid;
    }

    public void publishPermission(HttpEnvData req) {
        checkBlock(req);
//        if (userLevelDao.getUserLevel(req.getUid()) < 2) {
//            throw new CommonException(MomentHttpCode.LEVEL_LIMIT, 2);
//        }
    }

    public PrePublishVO prePublish(MomentTopicDTO req) {
        publishPermission(req);
        PrePublishVO vo = new PrePublishVO();
        vo.setBgList(momentThemeService.getBgList());
        List<MomentTopicMemberData> momentTopicMemberDataList = momentTopicMemberDao.selectRecentlyUsePage(req.getUid(), 1, 5);
        List<MomentTopicVO> topicList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(momentTopicMemberDataList)) {
            momentTopicMemberDataList.forEach((item -> {
                MomentTopicVO itemVO = new MomentTopicVO();
                MomentTopicData momentTopicData = momentTopicDao.selectByIdFromCache(item.getTopicId());
                itemVO.setTopicRid(momentTopicData.getRid());
                itemVO.setName(momentTopicData.getName());
                itemVO.setHead(ImageUrlGenerator.generateNormalUrl(momentTopicData.getHead()));
                topicList.add(itemVO);
            }));
        }
        vo.setMomentTopicList(topicList);
        vo.setIsShowTopic(1);
        if (req.getTopicRid() != null && req.getTopicRid() > 0) {
            momentTopicService.checkTopicRid(req);
            if (momentTopicMemberDao.selectBlack(req.getTopicId(), req.getUid()) != null) {
                vo.setIsShowTopic(0);
            }
        }
        return vo;
    }

    public MomentData getMoment(String mid) {
        if (StringUtils.isEmpty(mid)) {
            throw new CommonException(MomentHttpCode.PARAM_ERROR);
        }
        MomentData moment = momentDao.getMoment(mid);
        if (null == moment) {
            logger.info("cannot find moment mid={}", mid);
            throw new CommonException(MomentHttpCode.MOMENT_NOT_EXIST);
        }
        return moment;
    }

    private CommentVO commentToVO(HttpEnvData req, MomentData moment, Comment comment, boolean showReplayTo, Comment hotComment) {
        CommentVO commentVO = new CommentVO();
        String commentId = comment.get_id().toString();
        commentVO.setComment_id(commentId);
        commentVO.setComment(comment.getContent());
        commentVO.setUid(comment.getCommentator());
        commentVO.setReplyCount(comment.getReply_count());
        commentVO.setIsLike(null == comment.getLikes() ? 0 : comment.getLikes().contains(req.getUid()) ? 1 : 0);
        commentVO.setLikeCount(null == comment.getLikes() ? 0 : comment.getLikes().size());
        commentVO.setC_time(comment.getC_time());
        ActorData actorData = actorDao.getActorDataFromCache(comment.getCommentator());
        if (null != actorData) {
            commentVO.setName(actorData.getName());
            commentVO.setGender(actorData.getFb_gender());
            commentVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
        }
        if (showReplayTo) {
            commentVO.setReply_to_uid(comment.getReply_to());
            if (!StringUtils.isEmpty(comment.getReply_to())) {
                actorData = actorDao.getActorDataFromCache(comment.getReply_to());
                if (null != actorData) {
                    commentVO.setReply_to_name(actorData.getName());
                }
            }
        }
        commentVO.setAt_list(comment.getAt_list());
        if (!StringUtils.isEmpty(req.getUid())) {
            commentVO.setDel((req.getUid().equals(moment.getUid()) || req.getUid().equals(comment.getCommentator())
                    || momentConfig.getPOWER_USER().contains(req.getUid())) ? 1 : 0);
        }

        if (!ObjectUtils.isEmpty(moment.getFirstCommentId()) && moment.getFirstCommentId().equals(commentId)){
            commentVO.setFirstComment(1);
        }

        // 区分一级评论、二级评论
        // logger.info("hotComment handle commentId:{}, hotComment:{}", commentId, hotComment != null ? JSONObject.toJSONString(hotComment) : "");
        if (!ObjectUtils.isEmpty(hotComment) && commentId.equals(hotComment.get_id().toString())) {
            if ((hotComment.getOriginal_id() == null && moment.getComments() > 1) ||
                    (hotComment.getOriginal_id() != null && hotComment.getReply_count() > 1)) {
                commentVO.setHotComment(1);
            }
        }
        return commentVO;
    }

    @Cacheable(value = "detailFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public MomentInfoVO detailFromCache(MomentDTO req) {
        return detail(req);
    }

    public MomentInfoVO detail(MomentDTO req) {
        MomentData moment = getMoment(req.getMid());
        MomentInfoVO momentInfoVO;
        if (1 == req.getPage() || (ClientOS.IOS != req.getOs() && req.getVersioncode() < 440)) {
            momentInfoVO = getMomentInfoVO(moment, req, true, true, true);
            momentInfoVO.setIs_top(topMomentRedis.getTopMomentFromCache().contains(momentInfoVO.getMid()) ? 1 : 0);
        } else {
            momentInfoVO = new MomentInfoVO();
        }
        int start = (req.getPage() - 1) * COMMENTS_LIST_SIZE;
        // 兼容老版本
        if (!AppVersionUtils.versionCheck(835, req)) {
            List<Comment> comments = commentDao.getPageComments(req.getMid(), start, COMMENTS_LIST_SIZE);
            List<CommentVO> commentVOList = new ArrayList<>();
            for (Comment comment : comments) {
                commentVOList.add(commentToVO(req, moment, comment, true, null));
            }
            momentInfoVO.setComment_list(commentVOList);
            momentInfoVO.setNextUrl(commentVOList.size() < COMMENTS_LIST_SIZE ? "" : req.getPage() + 1 + "");
        }
        return momentInfoVO;
    }

    public void delete(MomentDTO req) {
        MomentData moment = getMoment(req.getMid());
        if (req.getUid().equals(moment.getUid())
                || momentConfig.getPOWER_USER().contains(req.getUid())
                || momentConfig.getOFFICIAL_USER().contains(req.getUid())) {
            // 删除朋友圈
            momentDao.delete(moment, moment.get_id().toString());
            // 删除广场数据
            momentShowRedis.deleteMoment(moment, req.getUid());
            featuredRedis.deleteMoment(moment, req.getUid());
            nearbyService.deleteMoment(moment.get_id().toString(), req.getUid());
            newSaRedis.deleteMoment(moment, req.getUid());
            featuredSaRedis.deleteMoment(moment, req.getUid());
            if (moment.getTopicId() > 0) {
                momentTopicFeaturedRedis.deleteMoment(moment, req.getUid(), moment.getTopicId());
            }
            // 删除通知消息
            momentNoticeService.deleteNotice(moment);
            // 删除评论
            commentDao.removeAll(req.getMid());
            // 删除礼物打赏
            momentRewardService.deleteMomentReward(req.getMid());
            // 删除话题相关
            if (moment.getTopicId() > 0) {
                momentTopicDao.incMomentNum(moment.getTopicId(), -1);
            }

            MomentDeleteEvent event = new MomentDeleteEvent();
            event.setUid(req.getUid());
            event.setCtime(DateHelper.getNowSeconds());
            event.setOperator_role(moment.getUid().equals(req.getUid()) ? "moment_creator" : "administrator");
            event.setMoment_id(moment.get_id().toString());
            event.setMoment_create_uid(moment.getUid());
            event.setTopic_create_uid(moment.getUid());
            if (moment.getTopicId() > 0) {
                event.setTopic_id(String.valueOf(moment.getTopicId()));
                MomentTopicData momentTopicData = momentTopicDao.selectByIdFromCache(moment.getTopicId());
                if (momentTopicData != null) {
                    event.setTopic_create_uid(momentTopicData.getOwnerUid());
                }
            }
            eventReport.track(new EventDTO(event));

        }
    }

    public MomentVO showPublish(MomentDTO req) {
        return 2 == req.getOpt() ? getPrivateMoment(req) : getPublicMoment(req);
    }

    @Cacheable(value = "getPublicMomentFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public MomentVO getPublicMomentFromCache(MomentDTO req) {
        return getPublicMoment(req);
    }

    private MomentVO getPrivateMoment(MomentDTO req) {
        if (StringUtils.isEmpty(req.getUid())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int start = (req.getPage() - 1) * MOMENT_SIZE;
        MomentVO vo = new MomentVO();
        int resultLength = 0;
        List<String> timeline = timelineService.getTimeline(req.getUid(), start, start + MOMENT_SIZE - 1);
        for (String mid : timeline) {
            resultLength++;
            MomentData moment = momentDao.getMoment(mid);
            if (null == moment || blackListDao.isBlock(req.getUid(), moment.getUid())) {
                // 清除已经删除的moment
                timelineService.deleteTimeline(req.getUid(), mid);
                continue;
            }
            MomentInfoVO momentInfoVO = getMomentInfoVO(moment, req, false, true);
            if (null != momentInfoVO) {
                // 不是好友且未关注，删除记录
                if (1 != momentInfoVO.getIs_friend() && !req.getUid().equals(momentInfoVO.getAid())) {
                    if (moment.getShow() == MomentConstant.MOMENT_FRIENDS || 1 != momentInfoVO.getIs_followed()) {
                        timelineService.deleteTimeline(req.getUid(), mid);
                        continue;
                    }
                }
                vo.getList().add(momentInfoVO);
            }
        }
        if (req.getPage() == 1) {
            if (!localCacheService.isCleanToday(req.getUid()) && timelineService.getTimelineSize(req.getUid()) > 500) {
                // 每超过500条好友圈消息时，清除最先的300条记录
                timelineService.cleanTimeline(req.getUid(), 200);
                localCacheService.addCleanToday(req.getUid());
            }
            actorDao.clearMomentUnread(req.getUid());
        }
        vo.setNextUrl(resultLength < MOMENT_SIZE ? "" : req.getPage() + 1 + "");
        vo.setRicky(momentNoticeService.getNoticeCount(req.getUid()));
        vo.setIs_official(momentConfig.getOFFICIAL_USER().contains(req.getUid()) ? 1 : 0);
        return vo;
    }

    private MomentVO getPublicMoment(MomentDTO req) {
        MomentVO vo = new MomentVO();
        ActorData actorData = actorDao.getActorData(req.getUid());
        boolean saFlag = RecommendService.SA_RECOMMEND_KEY.equals(ActorUtils.getUpperCaseCountryCode(actorData.getCountry()));
        if (1 == req.getOpt() || 0 == req.getOpt()) {
            // 官方置顶
            // && req.getHideTop() == 0
            Set<String> topMidSet = new HashSet<>();
            if (req.getPage() == 1) {
                for (String topMid : topMomentRedis.getTopMomentFromCache()) {
                    MomentInfoVO momentInfoVO = getMomentInfoVO(momentDao.getMoment(topMid), req, false, true);
                    if (null == momentInfoVO) {
                        // 清理脏数据
                        logger.error("cannot find top moment, remove top moment. mid={}", topMid);
                        topMomentRedis.deleteTopMoment("[system]", topMid);
                    } else {
                        topMidSet.add(momentInfoVO.getMid());
                        momentInfoVO.setIs_top(1);
                        vo.getList().add(momentInfoVO);
                    }
                }
                // 获取前8个热门话题：先按置顶，再按最近7天内发布帖子数量排序
                List<MomentTopicVO> trendTopicList = getTrendTopicList(req.getUid(), 8);
                vo.setTopicList(trendTopicList);
            }
            // AbstractFeaturedRedis abstractRedis = saFlag ? featuredSaRedis : featuredRedis;
            AbstractFeaturedRedis abstractRedis = featuredRedis;
            fillPublicMoment(req, vo, topMidSet, abstractRedis);
        } else if (3 == req.getOpt()) {
            // AbstractFeaturedRedis abstractRedis = saFlag ? newSaRedis : momentShowRedis;
            AbstractFeaturedRedis abstractRedis = momentShowRedis;
            fillPublicMoment(req, vo, Collections.emptySet(), abstractRedis);
        }else if (4 == req.getOpt()) {
            fillNearbyMoment(req, vo);
        }
        vo.setNextUrl(CollectionUtils.isEmpty(vo.getList()) ? "" : req.getPage() + 1 + "");
        vo.setRicky(momentNoticeService.getNoticeCount(req.getUid()));
        vo.setIs_official(momentConfig.getOFFICIAL_USER().contains(req.getUid()) ? 1 : 0);

        return vo;
    }

    private void fillRecommendMoment(MomentDTO req, MomentVO vo, Set<String> topMidSet) {
        List<String> recommendList = recommendService.getRecommendList(req.getUid(), topMidSet);
        for (String mid : recommendList) {
            MomentInfoVO momentInfoVO = getMomentInfoVO(momentDao.getMoment(mid), req, false, false);
            if (null != momentInfoVO) {
                vo.getList().add(momentInfoVO);
            }
        }
    }

    private void fillPublicMoment(MomentDTO req, MomentVO vo, Set<String> topMidSet, AbstractFeaturedRedis redis) {
        int start = (req.getPage() - 1) * MOMENT_SIZE;
        List<String> momentShow = redis.getMoment(start, start + MOMENT_SIZE - 1);
        for (String mixMid : momentShow) {
            String[] arrays = mixMid.split("_");
            if (arrays.length != 2) {
                continue;
            }
            if (Integer.parseInt(arrays[1]) < DateHelper.getNowSeconds() - TimeUnit.DAYS.toSeconds(7)) {
                // 清除过期的moment
                redis.trimMoment(start + MOMENT_SIZE);
                return;
            }
            MomentData moment = momentDao.getMoment(arrays[0]);
            if (null == moment) {
                // 清除已经删除的moment
                redis.deleteMoment(mixMid);
                continue;
            }
            if (topMidSet.contains(moment.get_id().toString()) || userMonitorService.isBan(moment.getUid())) {
                continue;
            }
            MomentInfoVO momentInfoVO = getMomentInfoVO(moment, req, false, true);
            if (null != momentInfoVO) {
                vo.getList().add(momentInfoVO);
            }
        }
    }

    private void fillNearbyMoment(MomentDTO req, MomentVO vo) {
        List<String> momentShow = nearbyService.getNearbyMomentList(req.getUid(), req.getPage());
        for (String momentId : momentShow) {
            MomentData moment = momentDao.getMoment(momentId);
            if (null == moment) {
                // 清除已经删除的moment
                continue;
            }
            MomentInfoVO momentInfoVO = getMomentInfoVO(moment, req, false, true);
            if (null != momentInfoVO) {
                vo.getList().add(momentInfoVO);
            }
        }
    }

    /**
     * 格式化礼物打赏数值
     */
    private String formatGifted(int gifted) {
        if (gifted >= 1000) {
            return new BigDecimal(gifted / 1000f).setScale(1, RoundingMode.HALF_UP) + "K";
        } else {
            return gifted + "";
        }
    }

    public MomentInfoVO getMomentInfoVO(MomentData moment, HttpEnvData req, boolean likeDetail, boolean fillFollow) {
        return getMomentInfoVO(moment, req, likeDetail, fillFollow, false);
    }

    /**
     * 获取朋友圈详情对象
     *
     * @param moment            朋友圈对象
     * @param req               请求
     * @param likeDetail        是否填充like详情
     * @param fillFollow        是否填充follow关系
     * @param isShowTopicDetail 是否填充话题详情
     * @return 朋友圈详情对象
     */
    public MomentInfoVO getMomentInfoVO(MomentData moment, HttpEnvData req, boolean likeDetail, boolean fillFollow, boolean isShowTopicDetail) {
        if (moment == null) {
            return null;
        }
        String uid = req.getUid();
        boolean isMyMoment = uid.equals(moment.getUid());
        MomentInfoVO momentInfoVO = new MomentInfoVO();
        momentInfoVO.setMid(moment.get_id().toString());
        momentInfoVO.setAid(moment.getUid());
        momentInfoVO.setText(moment.getText());
        momentInfoVO.setImgs_list(null == moment.getImgs() ? Collections.emptyList() : moment.getImgs());
        momentInfoVO.setAt_list(null == moment.getAt_list() ? Collections.emptyList() : moment.getAt_list());
        momentInfoVO.setQuote(moment.getQuote());
        momentInfoVO.setTheme(moment.getTheme());
        momentInfoVO.setLocation(null == moment.getLocation() ? "" : moment.getLocation());
        momentInfoVO.setDel((isMyMoment || momentConfig.getPOWER_USER().contains(uid)) ? 1 : 0);
        momentInfoVO.setAid(moment.getUid());
        momentInfoVO.setLikes_count(null == moment.getLikes() ? 0 : moment.getLikes().size());
        momentInfoVO.setIs_like(null == moment.getLikes() ? 0 : moment.getLikes().contains(uid) ? 1 : 0);
        momentInfoVO.setOfficial(momentConfig.getOFFICIAL_USER().contains(moment.getUid()) ? 1 : 0);
        momentInfoVO.setIs_official(momentConfig.getOFFICIAL_USER().contains(uid) ? 1 : 0);
        momentInfoVO.setComments(moment.getComments());
        momentInfoVO.setRepost(moment.getRepost());
        momentInfoVO.setTime(moment.getC_time());
        momentInfoVO.setShow(moment.getShow());
        List<MomentInfoVO.MomentLikeVO> likeVOList = new ArrayList<>();
        // 最多8个点赞用户头像
        if (null != moment.getLikes() && likeDetail) {
            List<String> likes = new ArrayList<>(moment.getLikes());
            Collections.reverse(likes);
            for (int i = 0; i < likes.size(); i++) {
                if (i == 8) {
                    break;
                }
                ActorData actorData = actorDao.getActorDataFromCache(likes.get(i));
                if (null == actorData) {
                    continue;
                }
                likeVOList.add(new MomentInfoVO.MomentLikeVO(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead())));
            }
        }
        momentInfoVO.setGifted(formatGifted(moment.getGifted()));
        momentInfoVO.setGiftCount(moment.getGifted());
        momentInfoVO.setGiftTotalPrice(moment.getGiftTotalPrice());
        if (AppVersionUtils.versionCheck(8573, req)) {
            if (moment.getGiftTotalPrice() > 0) {
                List<MomentRewardData> lastSendList = momentRewardService.getLastSendList(momentInfoVO.getMid());
                List<String> giftedList = new ArrayList<>();
                List<MomentInfoVO.GiftedInfoVO> giftedInfoList = new ArrayList<>();
                for (MomentRewardData momentRewardData : lastSendList) {
                    ActorData actorData = actorDao.getActorDataFromCache(momentRewardData.getUid());
                    if (null == actorData) {
                        continue;
                    }
                    if (momentRewardData.getRewardPrice() == null) {
                        continue;
                    }
                    String giftedHead = ImageUrlGenerator.generateRoomUserUrl(actorData.getHead());
                    giftedList.add(giftedHead);
                    giftedInfoList.add(new MomentInfoVO.GiftedInfoVO(actorData.getUid(), giftedHead, momentRewardData.getGiftIcon()));
                }
                momentInfoVO.setGiftedList(giftedList);
                momentInfoVO.setGiftedInfoList(giftedInfoList);
            }
        } else {
            if (moment.getGifted() > 0) {
                List<MomentRewardData> lastSendList = momentRewardService.getLastSendList(momentInfoVO.getMid());
                List<String> giftedList = new ArrayList<>();
                List<MomentInfoVO.GiftedInfoVO> giftedInfoList = new ArrayList<>();
                for (MomentRewardData momentRewardData : lastSendList) {
                    ActorData actorData = actorDao.getActorDataFromCache(momentRewardData.getUid());
                    if (null == actorData) {
                        continue;
                    }
                    String giftedHead = ImageUrlGenerator.generateRoomUserUrl(actorData.getHead());
                    giftedList.add(giftedHead);
                    giftedInfoList.add(new MomentInfoVO.GiftedInfoVO(actorData.getUid(), giftedHead, momentRewardData.getGiftIcon()));
                }
                momentInfoVO.setGiftedList(giftedList);
                momentInfoVO.setGiftedInfoList(giftedInfoList);
            }
        }
        momentInfoVO.setLikes_list(likeVOList);
        ActorData actorData = actorDao.getActorDataFromCache(moment.getUid());
        MomentInfoVO.UserInfoVO userInfoVO = new MomentInfoVO.UserInfoVO();
        if (null != actorData) {
            userInfoVO.setVip_level(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
            userInfoVO.setVipMedal(actorCommonService.getCommonVipMedal(actorData.getUid(), userInfoVO.getVip_level()));
            userInfoVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), userInfoVO.getVip_level()));
            if (ServerConfig.isNotProduct()) {
                userInfoVO.setHeadFrame(vipPrivilegeService.getHeadFrameByVip(userInfoVO.getVip_level()));
                momentInfoVO.setBgUrl(vipPrivilegeService.getBgUrlByVip(userInfoVO.getVip_level()));
                momentInfoVO.setBgUrlAr(vipPrivilegeService.getBgUrlArByVip(userInfoVO.getVip_level()));
            }
            userInfoVO.setName(actorData.getName());
            userInfoVO.setGender(actorData.getFb_gender());
            userInfoVO.setIdentify(userInfoVO.getVip_level() > 0 ? 1 : 0);
            userInfoVO.setCountry(actorData.getCountry());
            if (1 == actorData.getAccept_talk()) {
                String roomId = roomPlayerRedis.getActorRoomStatus(moment.getUid());
                userInfoVO.setSstatus(StringUtils.isEmpty(roomId) ? 0 : 1);
                userInfoVO.setInRoomId(StringUtils.isEmpty(roomId) ? "" : roomId);
            }
        }
        momentInfoVO.setUser_info(userInfoVO);
        if (!StringUtils.isEmpty(uid)) {
            if (fillFollow) {
                momentInfoVO.setIs_followed(uid.equals(moment.getUid()) ? 1 : followDao.isFollowed(uid, moment.getUid()) ? 1 : 0);
            }
            momentInfoVO.setIs_friend(uid.equals(moment.getUid()) ? 0 : friendsListRedis.isFriend(moment.getUid(), uid) ? 1 : 0);
        }
        if (moment.getComments() > 0 && AppVersionUtils.versionCheck(8471, req)) {
            fillLastCommentList(momentInfoVO, moment, req);
        }
        int topicId = moment.getTopicId();
        if (topicId > 0) {
            MomentTopicVO.MomentTopicShowVO topicShowVO = new MomentTopicVO.MomentTopicShowVO();
            MomentTopicData momentTopicData = momentTopicDao.selectByIdFromCache(topicId);
            topicShowVO.setTopicName(momentTopicData.getName());
            topicShowVO.setTopicRid(momentTopicData.getRid());
            if (isShowTopicDetail) {
                topicShowVO.setHead(ImageUrlGenerator.generateRoomUserUrl(momentTopicData.getHead()));
                topicShowVO.setAnnounce(momentTopicData.getAnnounce());
                topicShowVO.setFollowers(momentTopicData.getFollowers());
                topicShowVO.setMomentNum(momentTopicData.getMomentNum());
                topicShowVO.setGiftBeans(momentTopicData.getGiftBeans());
            }
            topicShowVO.setIsHotByTopic(moment.getTopicHotTime() > 0 ? 1 : 0);
            Set<String> allAdminList = momentTopicMemberDao.selectAllAdminListCache(topicId);
            int topicRole = uid.equals(momentTopicData.getOwnerUid()) ? 1 : allAdminList.contains(uid) ? 2 : 0;
            int momentUidTopicRole = moment.getUid().equals(momentTopicData.getOwnerUid()) ? 1
                    : allAdminList.contains(moment.getUid()) ? 2 : 0;
            topicShowVO.setTopicRole(momentUidTopicRole);
            int isShowSetAsHot = 0;
            int isShowUnBindMoment = 0;
            int isShowBlockUser = 0;
            if (topicRole == 1) {
                if (topicShowVO.getIsHotByTopic() == 1) {
                    isShowSetAsHot = 2;
                } else {
                    isShowSetAsHot = 1;
                }
                isShowUnBindMoment = 1;
                isShowBlockUser = isMyMoment ? 0 : 1;
            }
            if (topicRole == 2) {
                if (momentUidTopicRole >= 1) {
                    if (isMyMoment) {
                        isShowUnBindMoment = 1;
                    }
                } else {
                    isShowUnBindMoment = 1;
                    isShowBlockUser = isMyMoment ? 0 : 1;
                }
            } else {
                if (isMyMoment) {
                    isShowUnBindMoment = 1;
                }
            }
            topicShowVO.setIsShowSetAsHot(isShowSetAsHot);
            topicShowVO.setIsShowUnBindMoment(isShowUnBindMoment);
            topicShowVO.setIsShowBlockUser(isShowBlockUser);
            momentInfoVO.setMomentTopicInfo(topicShowVO);
        }
        return momentInfoVO;
    }

    private void fillLastCommentList(MomentInfoVO momentInfoVO, MomentData moment, HttpEnvData req) {
        List<Comment> commentDetails = commentDao.getMergedPageComments(moment.get_id().toString(), 0, 4);
        List<CommentVO> commentVOList = new ArrayList<>();
        for (Comment comment : commentDetails) {
            if (commentVOList.size() == 3) {
                momentInfoVO.setMoreComment(1);
                break;
            }
            commentVOList.add(commentToVO(req, moment, comment, true, null));
        }
        momentInfoVO.setLastCommentList(commentVOList);
    }

    @Cacheable(value = "getFriendsList", key = "#p0.uid+'-'+#p0.page+'-'+#p0.excludeRecently", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public AtListVO getFriendsList(MomentDTO req) {
        AtListVO vo = new AtListVO();
        int start = (req.getPage() - 1) * FRIEND_LIST_SIZE;
        ApiResult<List<String>> pageFriendList = friendService.getPageFriendList(req.getUid(), start, FRIEND_LIST_SIZE);
        if (pageFriendList.isOk()) {
            List<String> friendListData = pageFriendList.getData();
            if (req.getPage() == 1) {
                int total = 0;
                if (req.getExcludeRecently() != 1) {
                    List<String> friendsList = msgListService.getRecentlyChat(req.getUid(), 20);
                    friendListData.addAll(0, friendsList);
                    if (!req.getUid().equals(CustomerServiceUser.getUid())) {
//                    friendsList.add(0, CustomerServiceUser.getUid());
                        friendListData.add(0, CustomerServiceUser.getUid());
                        total += 1;
                    }
                }
                total += friendService.getFriendCount(req.getUid()).getData();
                vo.setNums(total);
            }
            vo.setList(getActorVOList(friendListData));
        }
        vo.setNextUrl(vo.getList().isEmpty() ? "" : req.getPage() + 1 + "");
        return vo;
    }

    private List<Object> getActorVOList(Collection<String> aidList) {
        List<Object> friendVOList = new ArrayList<>();
        Set<String> distinctSet = new HashSet<>();
        for (String aid : aidList) {
            if (distinctSet.contains(aid)) {
                continue;
            }
            distinctSet.add(aid);
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (null == actorData || AccountConstant.DELETED == actorData.getAccountStatus()) {
                logger.info("getActorVOList cannot find actor uid={} accountStatus={}", aid, actorData != null ? actorData.getAccountStatus() : 0);
                continue;
            }
            friendVOList.add(copyActorVO(actorData));
        }
        return friendVOList;
    }

    private ActorVO copyActorVO(ActorData actorData) {
        ActorVO actorVO = new ActorVO();
        actorVO.setAid(actorData.getUid());
        actorVO.setRid(actorData.getRid());
        actorVO.setRidData(actorData.getRidData());
        actorVO.setName(actorData.getName());
        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        actorVO.setGender(actorData.getFb_gender());
        actorVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
        actorVO.setVipMedal(actorCommonService.getCommonVipMedal(actorData.getUid(), actorVO.getViplevel()));
        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), actorVO.getViplevel()));
        actorVO.setMicFrame(micFrameRedis.getMicSourceFromCache(actorData.getUid()));
        return actorVO;
    }

    @Cacheable(value = "search", key = "#p0.uid+'-'+#p0.key", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public FriendSearchVO search(MomentDTO req) {
        FriendSearchVO vo = new FriendSearchVO();
        if (null == req.getKey() || StringUtils.isEmpty(req.getKey().trim())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String key = req.getKey().trim();
        try {
            List<ActorData> actorDataList = actorDao.searchActorByRid(key, 1, 50);
            for (ActorData actorData : actorDataList) {
                if (friendsListRedis.isFriend(req.getUid(), actorData.getUid())) {
                    vo.getUsers().add(copyActorVO(actorData));
                }
            }
//            ActorData actorData = actorDao.getActorByStrRid(key);
//            if (null != actorData && friendsListRedis.isFriend(req.getUid(), actorData.getUid())) {
//                vo.getUsers().add(copyActorVO(actorData));
//            }
        } catch (NumberFormatException e) {
            ApiResult<Set<String>> friendSet = friendService.getFriendSet(req.getUid());
            if (friendSet.isOk()) {
                Set<String> friends = friendSet.getData();
                if (!CollectionUtils.isEmpty(friends)) {
                    List<MongoActorData> actors = actorDao.getActorsByName(key, friends);
                    for (MongoActorData actor : actors) {
                        ActorVO actorVO = new ActorVO();
                        actorVO.setAid(actor.get_id().toString());
                        actorVO.setRid(actor.getRid());
                        actorVO.setRidData(actor.getRidData());
                        actorVO.setName(actor.getName());
                        actorVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorVO.getAid()));
                        actorVO.setVipMedal(actorCommonService.getCommonVipMedal(actorVO.getAid(), actorVO.getViplevel()));
                        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead(), actorVO.getViplevel()));
                        actorVO.setGender(actor.getFb_gender());
                        vo.getUsers().add(actorVO);
                    }
                }
            }
        }
        return vo;
    }

    public boolean checkSameComment(String uid, String commentContent) {
        try {
            if (StringUtils.isEmpty(commentContent)) {
                return false;
            }

            Set<String> sameUidList = new HashSet<>();
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (!StringUtils.isEmpty(actorData.getTn_id())) {
                List<MongoActorData> sameTnUser = actorDao.findListByTnId(actorData.getTn_id());
                sameUidList.addAll(sameTnUser.stream().map(MongoActorData::get_id).map(ObjectId::toString).collect(Collectors.toList()));
            }

//            if (!StringUtils.isEmpty(actorData.getIp())) {
//                List<MongoActorData> sameIpUser = actorDao.findListByIp(actorData.getIp());
//                sameUidList.addAll(sameIpUser.stream().map(MongoActorData::get_id).map(ObjectId::toString).collect(Collectors.toList()));
//            }

            if (CollectionUtils.isEmpty(sameUidList)) {
                return false;
            }

            int lastTime = DateHelper.getNowSeconds() - 60;
            List<Comment> lastCommentList = commentDao.getLastCommentByUidList(sameUidList, lastTime);
            int sameCount = 0;
            for (Comment comment : lastCommentList) {
                if (comment.getContent().equals(commentContent)) {
                    sameCount += 1;
                }

                if (sameCount >= 2) {
                    return true;
                }
            }
        } catch (Exception e) {
            logger.error("checkSameComment error:{}", e.getMessage(), e);
        }
        return false;

    }

    public CommentResultVO comment(CommentDTO req) {
        if (StringUtils.isEmpty(req.getContent())) {
            throw new CommonException(MomentHttpCode.PARAM_ERROR);
        }
        if (req.getContent().length() > 300) {
            throw new CommonException(MomentHttpCode.COMMENT_TOO_LENGTH);
        }
        if (req.getAt_list() != null && req.getAt_list().size() > 10) {
            throw new CommonException(MomentHttpCode.AT_LIMIT, 10);
        }
        checkFroze(req);
        checkDirtyWord(req.getUid(), req.getContent(), DetectOriginConstant.MOMENT_COMMENT);
        MomentData moment = getMoment(req.getMid());
        if (blackListDao.isBlock(moment.getUid(), req.getUid())) {
            throw new CommonException(MomentHttpCode.BLOCKED_BY_ACTOR);
        }
        // 一分钟评论内容同ip同设备不能超过两条
        if (checkSameComment(req.getUid(), req.getContent())) {
            throw new CommonException(MomentHttpCode.SAME_COMMENT);
        }

        // 获取原始评论id
        String originalId = null;
        String sourceText = null;
        if (!StringUtils.isEmpty(req.getComment_id())) {
            Comment comment = commentDao.getComment(req.getComment_id());
            if (null == comment) {
                logger.info("cannot find comment uid={} commentId={}", req.getUid(), req.getComment_id());
                throw new CommonException(MomentHttpCode.COMMENT_NOT_EXIST);
            }
            sourceText = comment.getContent();
            commentDao.incrCommentReplyCount(req.getComment_id(), 1);
            if (!StringUtils.isEmpty(comment.getOriginal_id())) {
                originalId = comment.getOriginal_id();
                commentDao.incrCommentReplyCount(originalId, 1);
            } else {
                originalId = req.getComment_id();
            }
        }
        Comment comment = new Comment(req.getMid(), req.getUid(), req.getContent(), req.getReply_to(), req.getComment_id(), req.getAt_list(), originalId);
        commentDao.save(comment);
        if (null == comment.get_id()) {
            logger.error("save comment error, uid={} mid={}", req.getUid(), req.getMid());
            throw new CommonException();
        }
        // 用户等级任务
        levelTaskService.sendTaskDataToMq(new UserLevelTaskData(req.getUid(), UserLevelConstant.MOMENT_COMMENT, req.getMid()));
        // 任务：评论动态
        CommonMqTopicData commentMqTopicData = new CommonMqTopicData(req.getUid(), "", moment.getUid(), req.getMid(), CommonMqTaskConstant.COMMENT_MOMENT, 1);
        commentMqTopicData.setJsonData(DigestUtils.md5DigestAsHex(req.getContent().getBytes(StandardCharsets.UTF_8)));
        commonTaskService.sendCommonTaskMq(commentMqTopicData);
        // 评论通知
        sendCommentNotice(moment, comment, req, sourceText);
        momentDao.incrMomentComments(req.getMid(), 1);
        setMomentFirstCommentId(moment, comment);
        // 附近推荐
        nearbyService.handleCommentMomentScore(moment.get_id().toString(), req.getUid(), NearbyService.COMMENT_INC_SCORE);
        return new CommentResultVO(req.getComment_id(), commentToVO(req, moment, comment, true, null));
    }

    private void setMomentFirstCommentId(MomentData moment, Comment comment){
        int momentTime = moment.getC_time();
        if (momentTime < FIRST_COMMENT_COUNT_TIME){
            return;
        }
        String firstCommentId = moment.getFirstCommentId();
        if (!ObjectUtils.isEmpty(firstCommentId)){
            return;
        }

        String momentId = moment.get_id().toString();
        firstCommentId = comment.get_id().toString();
        moment.setFirstCommentId(firstCommentId);
        momentDao.setMomentFirstCommentId(momentId, firstCommentId);
    }

    private void sendCommentNotice(MomentData moment, Comment comment, CommentDTO req, String sourceText) {
        if (StringUtils.isEmpty(req.getReply_to())) {
            // 通知博主
            if (!req.getUid().equals(moment.getUid())) {
                MomentNotice momentNotice = new MomentNotice(moment.getUid(), req.getUid(),
                        MomentConstant.NOTICE_COMMENT, req.getMid(), comment.getC_time(), req.getContent(),
                        req.getAt_list(), comment.get_id().toString());
                fillMomentNotice(moment, momentNotice);
                momentNoticeService.addNotice(moment.getUid(), momentNotice, moment.getUid());
                reportMomentInteractLogEvent(req.getUid(), moment, 2, null);
            }
        } else {
            // 通知回复用户
            MomentNotice momentNotice = new MomentNotice(req.getReply_to(), req.getUid(),
                    MomentConstant.NOTICE_COMMENT_REPLAY, req.getMid(), comment.getC_time(), req.getContent(),
                    req.getAt_list(), comment.get_id().toString());
            momentNotice.setSourceText(sourceText);
            momentNoticeService.addNotice(req.getReply_to(), momentNotice, moment.getUid());
            reportMomentInteractLogEvent(req.getUid(), moment, 4, null);
        }
        // 通知@用户
        if (moment.getShow() != MomentConstant.MOMENT_PRIVATE && !CollectionUtils.isEmpty(req.getAt_list())) {
            Set<String> ownerFriends = Collections.emptySet();
            if (moment.getShow() == MomentConstant.MOMENT_FRIENDS) {
                ApiResult<Set<String>> friendSet = friendService.getFriendSet(moment.getUid());
                if (friendSet.isOk()) {
                    ownerFriends = friendSet.getData();
                }
            }
            Set<String> atSet = new HashSet<>();
            for (MomentData.AtUser atUser : req.getAt_list()) {
                if (null != atUser && atUser.getAid().length() == 24) {
                    if (atSet.contains(atUser.getAid())) {
                        continue;
                    }
                    atSet.add(atUser.getAid());
                    if (moment.getShow() == MomentConstant.MOMENT_FRIENDS) {
                        if (!ownerFriends.contains(atUser.getAid())) {
                            logger.info("send comment notice uid={} is not aid={} friend.", atUser.getAid(), moment.getUid());
                            continue;
                        }
                    }
                    MomentNotice momentNotice = new MomentNotice(atUser.getAid(), req.getUid(),
                            MomentConstant.NOTICE_COMMENT_AT, req.getMid(), comment.getC_time(), req.getContent(),
                            req.getAt_list(), comment.get_id().toString());
                    momentNotice.setSourceText(req.getContent());
                    momentNoticeService.addNotice(atUser.getAid(), momentNotice, moment.getUid());
                }
            }
        }
    }

    public CommentListVO commentDetail(CommentDTO req) {
        MomentData moment = getMoment(req.getMid());
        CommentListVO vo = new CommentListVO();
        int start = (req.getPage() - 1) * COMMENT_DETAIL_LIST_SIZE;
        Comment originalComment = commentDao.getComment(req.getComment_id());
        if (null == originalComment) {
            logger.info("cannot find comment uid={} commentId={}", req.getUid(), req.getComment_id());
            throw new CommonException(MomentHttpCode.COMMENT_NOT_EXIST);
        }
        List<Comment> commentDetails = commentDao.getPageCommentDetails(req.getComment_id(), start, COMMENT_DETAIL_LIST_SIZE);
        List<CommentVO> commentVOList = new ArrayList<>();

        // 二级评论热门评论
        Comment originalHotComment = commentDao.getHotCommentWithMinLikes(req.getMid(), req.getComment_id(), HOT_COMMENT_COUNT_NUM);
        if (originalHotComment != null){
            originalHotComment.setReply_count(originalComment.getReply_count());
        }
        for (Comment comment : commentDetails) {
            CommentVO commentVO = commentToVO(req, moment, comment, !req.getComment_id().equals(comment.getComment_id()), originalHotComment);
            commentVOList.add(commentVO);
        }
        if (1 == req.getPage()) {
            // 一级评论热门评论
            Comment hotComment = commentDao.getHotCommentWithMinLikes(req.getMid(), null, HOT_COMMENT_COUNT_NUM);
            vo.setComment(commentToVO(req, moment, originalComment, true, hotComment));
        }
        vo.setList(commentVOList);
        vo.setNextUrl(commentVOList.size() < COMMENT_DETAIL_LIST_SIZE ? "" : req.getPage() + 1 + "");
        return vo;
    }

    public MomentVO commentList(CommentDTO req) {
        MomentData moment = getMoment(req.getMid());
        MomentVO vo = new MomentVO();
        int start = (req.getPage() - 1) * COMMENT_DETAIL_LIST_SIZE;
        List<Comment> commentDetails;
        if (req.getSortBy() == 0) {
            commentDetails = commentDao.getCommentsByPopular(req.getMid(), start, COMMENT_DETAIL_LIST_SIZE);
        } else {
            commentDetails = commentDao.getMergedPageComments(req.getMid(), start, COMMENT_DETAIL_LIST_SIZE);
        }
        Comment hotComment = commentDao.getHotCommentWithMinLikes(req.getMid(), null, HOT_COMMENT_COUNT_NUM);
        List<Object> commentVOList = new ArrayList<>();
        for (Comment comment : commentDetails) {
            CommentVO commentVO = commentToVO(req, moment, comment, true, hotComment);
            if (comment.getReply_count() > 0) {
                // 获取最新的一条回复
                Comment latestComment = commentDao.getLatestComment(comment.get_id().toString());
                if (null != latestComment) {
                    commentVO.setSubList(Collections.singletonList(commentToVO(req, moment, latestComment, true, null)));
                }
            }
            commentVOList.add(commentVO);
        }
        vo.setList(commentVOList);
        vo.setNextUrl(commentVOList.size() < COMMENT_DETAIL_LIST_SIZE ? "" : req.getPage() + 1 + "");
        return vo;
    }

    public void commentLike(CommentDTO req) {
        MomentData moment = getMoment(req.getMid());
        Comment comment = commentDao.getComment(req.getComment_id());
        if (null == comment) {
            logger.info("cannot find comment uid={} commentId={}", req.getUid(), req.getComment_id());
            throw new CommonException(MomentHttpCode.COMMENT_NOT_EXIST);
        }
        if (1 == req.getOpt()) {
            // 点赞评论
            commentDao.addLikes(req.getComment_id(), req.getUid());
            if (!req.getUid().equals(comment.getCommentator())) {
                momentNoticeService.addNotice(comment.getCommentator(), new MomentNotice(comment.getCommentator(), req.getUid(),
                        MomentConstant.NOTICE_COMMENT_LIKE, req.getMid(), null, comment.getContent(), req.getComment_id()), moment.getUid());
                reportMomentInteractLogEvent(req.getUid(), moment, 3, null);
            }
        } else if (0 == req.getOpt()) {
            // 取消点赞评论
            commentDao.removeLikes(req.getComment_id(), req.getUid());
            momentNoticeService.deleteNotice(comment.getCommentator(), req.getMid(), MomentConstant.NOTICE_COMMENT_LIKE, req.getComment_id());
            reportMomentInteractLogEvent(req.getUid(), moment, 7, null);
        }
    }

    public void delComment(CommentDTO req) {
        MomentData moment = getMoment(req.getMid());
        Comment comment = commentDao.findAndRemove(req.getComment_id());
        if (null == comment) {
            throw new CommonException(MomentHttpCode.COMMENT_NOT_EXIST);
        }
        if (!req.getUid().equals(moment.getUid()) && !req.getUid().equals(comment.getCommentator())
                && !momentConfig.getPOWER_USER().contains(req.getUid())) {
            throw new CommonException(MomentHttpCode.COMMENT_AUTH_ERROR);
        }
        momentDao.incrMomentComments(req.getMid(), -1);
        if (null != comment.getOriginal_id()) {
            commentDao.incrCommentReplyCount(comment.getOriginal_id(), -1);
        }else {
            // 附近推荐
            nearbyService.handleCommentMomentScore(moment.get_id().toString(), req.getUid(), NearbyService.COMMENT_DEC_SCORE);
        }
        momentNoticeService.deleteNoticeByCommentId(moment.getUid(), req.getMid(), req.getComment_id());
    }

    public void like(LikeDTO req) {
        try (DistributeLock lock = new DistributeLock("momentLike_" + req.getMid())) {
            lock.lock();
            MomentData moment = getMoment(req.getMid());
            if (blackListDao.isBlock(moment.getUid(), req.getUid())) {
                throw new CommonException(MomentHttpCode.BLOCKED_BY_ACTOR);
            }
            if (1 == req.getOpt()) {
                if (null != moment.getLikes() && moment.getLikes().contains(req.getUid())) {
                    return;
                }
                // 用户等级任务
                levelTaskService.sendTaskDataToMq(new UserLevelTaskData(req.getUid(), UserLevelConstant.LIKE_MOMENT, req.getMid()));
                // 任务：点赞动态
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(req.getUid(), "", moment.getUid(), req.getMid(), CommonMqTaskConstant.LIKE_MOMENT, 1));
                momentDao.addLikes(req.getMid(), req.getUid());
                if (!req.getUid().equals(moment.getUid())) {
                    MomentNotice momentNotice = new MomentNotice(moment.getUid(), req.getUid(),
                            MomentConstant.NOTICE_LIKE, req.getMid(), null, moment.getText(), null);
                    fillMomentNotice(moment, momentNotice);
                    momentNoticeService.addNotice(moment.getUid(), momentNotice, moment.getUid());
                    reportMomentInteractLogEvent(req.getUid(), moment, 1, null);
                    // 用户等级任务
                    levelTaskService.sendTaskDataToMq(new UserLevelTaskData(moment.getUid(), UserLevelConstant.MOMENT_LIKED, req.getUid()));
                }
                // 附近推荐
                nearbyService.handleLikeMomentScore(moment.get_id().toString(), NearbyService.LIKE_INC_SCORE);
            } else if (0 == req.getOpt()) {
                // 取消点赞
                momentDao.removeLikes(req.getMid(), req.getUid());
                momentNoticeService.deleteNotice(moment.getUid(), req.getMid(), MomentConstant.NOTICE_LIKE);
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(req.getUid(), "", moment.getUid(), req.getMid(), CommonMqTaskConstant.CANCEL_LIKE_MOMENT, 1));
                reportMomentInteractLogEvent(req.getUid(), moment, 6, null);
                // 附近推荐
                nearbyService.handleLikeMomentScore(moment.get_id().toString(), NearbyService.LIKE_DEC_SCORE);
            }
        }
    }

    public GiftRewordVO giftReward(GiftRewardDTO req) {
        try (DistributeLock lock = new DistributeLock("giftReward_" + req.getUid())) {
            lock.lock();
            ActorData sendActor = actorDao.getActorDataFromCache(req.getUid());
            MomentData moment = getMoment(req.getMid());
            ActorData momentOwner = actorDao.getActorDataFromCache(moment.getUid());
            GiftData giftData = rewardGiftConfig.getRewardGIftMap().get(req.getGiftId());
            if (null == giftData) {
                logger.info("moment gift reward error, cannot not find giftId. uid={} giftId={}", req.getUid(), req.getGiftId());
                throw new CommonException(MomentHttpCode.PARAM_ERROR);
            }
            int balance = doDeduct(sendActor, giftData, momentOwner);
            momentDao.incrMomentGifted(req.getMid(), giftData.getRewardNum(), req.getRewardPrice());
            momentRewardService.saveMomentReward(new MomentRewardData(req.getMid(), req.getUid(), giftData.getGiftId(),
                    rewardGiftConfig.getRewardGiftIcon(0), giftData.getRewardNum(), req.getRewardPrice()));
            // 通知博主
            MomentNotice momentNotice = new MomentNotice(moment.getUid(), req.getUid(),
                    MomentConstant.NOTICE_MOMENT_REWARD, req.getMid(), null, moment.getText(), null);
            fillMomentNotice(moment, momentNotice);
            momentNotice.setGiftReward(new MomentNotice.GiftReward(giftData.getRewardNum(), giftData.getGiftIcon()));
            momentNoticeService.addNotice(moment.getUid(), momentNotice, moment.getUid());
            // 埋点
            MomentGiftEvent event = new MomentGiftEvent();
            event.setUid(req.getUid());
            event.setMoment_id(req.getMid());
            event.setFrom_uid(req.getUid());
            event.setTo_uid(moment.getUid());
            event.setGift_id(giftData.getGiftId());
            event.setGift_number(giftData.getRewardNum());
            event.setGift_price(giftData.getPrice());
            event.setGift_price_type(giftData.getPriceType());
            event.setEarn_beans(giftData.getPrice() * 3 / 10);
            event.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new com.quhong.analysis.EventDTO(event));
            return new GiftRewordVO(ImageUrlGenerator.generateMiniUrl(sendActor.getHead()),
                    formatGifted(moment.getGifted() + giftData.getRewardNum()), balance);
        }
    }

    private int doDeduct(ActorData sendActor, GiftData giftData, ActorData momentOwner) {
        String uid = sendActor.getUid();
        if (1 == giftData.getPriceType()) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setChanged(-giftData.getPrice());
            moneyDetailReq.setAtype(SEND_GIFT_REWARD_ATYPE);
            moneyDetailReq.setTitle(SEND_GIFT_REWARD_TITLE);
            moneyDetailReq.setDesc(String.format(SEND_GIFT_REWARD_DESC, momentOwner.getRid()));
            ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
            if (result.isError()) {
                if (1 == result.getCode().getCode()) {
                    throw new CommonException(MomentHttpCode.INSUFFICIENT_DIAMONDS);
                }
                logger.error("moment gift reward, reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
                throw new CommonException(HttpCode.SERVER_ERROR);
            }
            MoneyDetailReq receiveDiamonds = new MoneyDetailReq();
            int receiveBean = giftData.getPrice() * 3 / 10;
            receiveDiamonds.setRandomId();
            receiveDiamonds.setUid(momentOwner.getUid());
            receiveDiamonds.setAtype(RECEIVE_GIFT_REWARD_ATYPE);
            receiveDiamonds.setChanged(receiveBean);
            receiveDiamonds.setTitle(RECEIVE_GIFT_REWARD_TITLE);
            receiveDiamonds.setDesc(String.format(RECEIVE_GIFT_REWARD_DESC, sendActor.getRid()));
            mqService.asyncChargeDiamonds(receiveDiamonds);

            // doMomentAchieveBadge(momentOwner.getUid(), receiveBean);
            return Integer.parseInt(result.getData());
        } else if (2 == giftData.getPriceType()) {
            boolean isSuccess = heartRecordDao.changeHeart(uid, -giftData.getPrice(),
                    SEND_GIFT_REWARD_TITLE, String.format(SEND_GIFT_REWARD_DESC, momentOwner.getRid()));
            if (!isSuccess) {
                throw new CommonException(MomentHttpCode.INSUFFICIENT_GOLDS);
            }
            heartRecordDao.changeHeart(uid, giftData.getPrice() * 3 / 10,
                    RECEIVE_GIFT_REWARD_TITLE, String.format(RECEIVE_GIFT_REWARD_DESC, momentOwner.getRid()));
            return sendActor.getHeartGot() - giftData.getPrice();
        } else {
            logger.error("price type not support. priceType={}", giftData.getPriceType());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    private void doMomentAchieveBadge(String uid, int receiveBean) {
        long beanCount = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.MOMENT_COUNT, -1L);

        if (beanCount > -1) {
            beanCount += receiveBean;
            logger.info("doMomentAchieveBadge uid: {}  giftCount:{}", uid, beanCount);
            actorConfigDao.updateUserConfig(uid, ActorConfigDao.MOMENT_COUNT, beanCount);
            badgeService.doAchieveBadge(uid, AchieveBadgeConstant.TYPE_SEND_GIFT, beanCount, receiveBean);
        } else {

            MoneyTypeDTO dto = new MoneyTypeDTO();
            dto.setUid(uid);
            dto.setMoneyType(MoneyTypeConstant.MOMENT_RECEIVE_GIFT);
            ApiResult<Long> result = dataCenterService.esMoneyTypeTotal(dto);
            if (result.isError()) {
                logger.error("esMoneyTypeTotal Moment error uid:{}, code: {} msg:{}", uid, result.getCode().getCode(), result.getCode().getMsg());
            } else {
                long totalReceiveBean = result.getData();
                logger.info("esMoneyTypeTotal Moment totalReceiveBean: {}, giftCount:{}", totalReceiveBean, beanCount);
                if (totalReceiveBean > -1) {
                    badgeService.supplyAchieveBadge(uid, AchieveBadgeConstant.TYPE_SEND_GIFT, totalReceiveBean);
                    actorConfigDao.updateUserConfig(uid, ActorConfigDao.MOMENT_COUNT, totalReceiveBean);
                }
            }
        }
    }

    public RewardPanelVO rewardPanel(HttpEnvData req) {
        RewardPanelVO vo = new RewardPanelVO();
        MongoActorData actorData = actorDao.findActorDataFromDB(req.getUid());
        if (null == actorData) {
            logger.info("get reward panel error, cannot not find actor data. uid={}", req.getUid());
            throw new CommonException(MomentHttpCode.PARAM_ERROR);
        }
        vo.setDiamonds(actorData.getBeans());
        vo.setGolds(actorData.getHeartGot());
        vo.setGiftList(rewardGiftConfig.getRewardGiftList());
        return vo;
    }

    public RewardListVO rewardList(RewardListDTO req) {
        RewardListVO vo = new RewardListVO();
        boolean isV8573 = AppVersionUtils.versionCheck(8573, req);
        List<RewardListVO.GiftRewardVO> rewardList = momentRewardService.getRewardList(req.getMid(), req.getPage());
        List<RewardListVO.GiftRewardVO> toRewardList = new ArrayList<>();
        for (RewardListVO.GiftRewardVO giftRewardVO : rewardList) {
            ActorData actorData = actorDao.getActorDataFromCache(giftRewardVO.getAid());
            if (null == actorData) {
                continue;
            }
            if (isV8573 && giftRewardVO.getGiftPrice() == null) {
                continue;
            }
            RewardListVO.GiftRewardVO toGiftRewardVO = new RewardListVO.GiftRewardVO();
            BeanUtils.copyProperties(giftRewardVO, toGiftRewardVO);
            toGiftRewardVO.setRid(actorData.getRid());
            toGiftRewardVO.setName(actorData.getName());
            toGiftRewardVO.setGender(actorData.getFb_gender());
            toGiftRewardVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
            toGiftRewardVO.setVipMedal(actorCommonService.getCommonVipMedal(actorData.getUid(), toGiftRewardVO.getViplevel()));
            toGiftRewardVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), toGiftRewardVO.getViplevel()));
            // 兼容旧版
            if (ObjectUtils.isEmpty(toGiftRewardVO.getGiftIcon())) {
                toGiftRewardVO.setGiftIcon(rewardGiftConfig.getRewardGiftIcon(0));
            }
            toGiftRewardVO.setGiftPrice(toGiftRewardVO.getGiftPrice() == null ? 0 : toGiftRewardVO.getGiftPrice());
            toRewardList.add(toGiftRewardVO);
        }
        vo.setList(toRewardList);
        vo.setNextUrl(vo.getList().isEmpty() ? "" : String.valueOf(req.getPage() + 1));
        return vo;
    }

    /**
     * 右边动态内容显示规则：有图片动态显示第一张图、链接动态显示链接图标、视频动态显示视频封面、纯文本动态显示文本内容，最多显示3行。
     * 优先级：img -> link -> sourceText
     */
    public void fillMomentNotice(MomentData moment, MomentNotice momentNotice) {
        momentNotice.setSourceText(moment.getText());
        if (!CollectionUtils.isEmpty(moment.getImgs())) {
            momentNotice.setImg(moment.getImgs().get(0).getThumbnail());
            return;
        }
        if (null != moment.getQuote()) {
            if (MomentConstant.QUOTE_YOUTUBE_LINK == moment.getQuote().getType()) {
                momentNotice.setImg(moment.getQuote().getIcon());
            } else if (MomentConstant.QUOTE_LINK == moment.getQuote().getType()) {
                momentNotice.setLink(1);
            }
        }
    }

    public MomentVO likeList(MomentDTO req) {
        MomentData moment = getMoment(req.getMid());
        MomentVO vo = new MomentVO();
        vo.setCode(0);
        if (null == moment.getLikes()) {
            vo.setNums(0);
            vo.setNextUrl("");
            vo.setList(Collections.emptyList());
        } else {
            List<String> likes = new ArrayList<>(moment.getLikes());
            Collections.reverse(likes);
            PageUtils.PageData<String> pageData = PageUtils.getPageData(likes, req.getPage(), LIKE_LIST_SIZE);
            vo.setNextUrl(pageData.nextPage == 0 ? "" : String.valueOf(pageData.nextPage));
            vo.setList(getActorVOList(pageData.list));
            vo.setNums(vo.getList().size());
        }
        return vo;
    }

    public void report(MomentDTO req) {
        momentDao.incrReports(req.getMid());
    }

    /**
     * 管理员直接拉黑该用户，该用户无法再发动态；普通用户直接拉黑该用户
     */
    public void block(MomentDTO req) {
        MomentData moment = getMoment(req.getMid());
        if (req.getUid().equals(moment.getUid())) {
            return;
        }
        if (momentConfig.getOFFICIAL_USER().contains(req.getUid()) || momentConfig.getPOWER_USER().contains(req.getUid())) {
            momentShowRedis.deleteMoment(moment, req.getUid());
            featuredRedis.deleteMoment(moment, req.getUid());
            nearbyService.deleteMoment(moment.get_id().toString(), req.getUid());
            newSaRedis.deleteMoment(moment, req.getUid());
            featuredSaRedis.deleteMoment(moment, req.getUid());
            if (moment.getTopicId() > 0) {
                momentTopicFeaturedRedis.deleteMoment(moment, req.getUid(), moment.getTopicId());
            }
            momentDao.delete(moment, moment.get_id().toString());
        } else {
            blackListDao.addBlock(req.getUid(), moment.getUid());
        }
    }

    public UnreadCheckVO unreadCheck(MomentDTO req) {
        return new UnreadCheckVO(actorDao.getMomentUnread(req.getUid()), momentNoticeService.getNoticeCount(req.getUid()));
    }

    public MomentVO showPersonalMoment(String uid, String aid, Integer page, HttpEnvData envData) {
        MomentVO vo = new MomentVO();
        int start = (page - 1) * PERSONAL_LIST_SIZE;
        List<MomentData> momentDataList;
        if (Objects.equals(uid, aid)) {
            momentDataList = momentDao.getMomentList(aid, SELF_SHOW, start, PERSONAL_LIST_SIZE);
        } else if (friendsListRedis.isFriend(uid, aid)) {
            momentDataList = momentDao.getMomentList(aid, FRIEND_SHOW, start, PERSONAL_LIST_SIZE);
        } else {
            momentDataList = momentDao.getMomentList(aid, STRANGER_SHOW, start, PERSONAL_LIST_SIZE);
        }
        for (MomentData moment : momentDataList) {
            MomentInfoVO momentInfoVO = getMomentInfoVO(moment, envData, false, true);
            if (null != momentInfoVO) {
                vo.getList().add(momentInfoVO);
            }
        }
        vo.setNextUrl(momentDataList.size() < PERSONAL_LIST_SIZE ? "" : page + 1 + "");
        vo.setIs_official(momentConfig.getOFFICIAL_USER().contains(uid) ? 1 : 0);

        List<UserFollowTopicJoinData> followTopicJoinDataList = momentTopicMemberDao.getUserTopicJoinData(aid, 0, 4);
        List<MomentTopicVO> topicList = new ArrayList<>();
        int count = 0;
        for (UserFollowTopicJoinData item : followTopicJoinDataList) {
            count++;
            // 只给3个
            if (count >= 4) {
                break;
            }
            MomentTopicVO itemVO = new MomentTopicVO();
            itemVO.setTopicRid(item.getTopicRid());
            itemVO.setName(item.getName());
            itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(item.getHead()));
            itemVO.setFollowers(item.getFollowers());
            itemVO.setRole(item.getRole());
            topicList.add(itemVO);

        }
        int showMore = followTopicJoinDataList.size() > 3 ? 1 : 0;
        vo.setTopicList(topicList);
        vo.setShowMore(showMore);
        return vo;
    }

    private List<Object> noticeToVO(List<MomentNotice> notices, MomentDTO req) {
        List<Object> noticeVOList = new ArrayList<>();
        for (MomentNotice momentNotice : notices) {
            ActorData actorData = actorDao.getActorDataFromCache(momentNotice.getAid());
            if (null == actorData) {
                continue;
            }
            String sourceText = momentNotice.getSourceText();
            if (req.getSlang() == SLangType.ARABIC && StringUtils.hasLength(momentNotice.getSourceTextAr())) {
                sourceText = momentNotice.getSourceTextAr();
            }
            NoticeVO noticeVO = new NoticeVO();
            noticeVO.setMid(momentNotice.getMoment_id());
            noticeVO.setAid(momentNotice.getAid());
            noticeVO.setName(actorData.getName());
            noticeVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
            noticeVO.setText(momentNotice.getText());
            noticeVO.setSourceText(sourceText);
            noticeVO.setLink(momentNotice.getLink());
            noticeVO.setType(momentNotice.getAction_atype());
            noticeVO.setTime(momentNotice.getC_time());
            noticeVO.setComment_id(momentNotice.getComment_id());
            noticeVO.setAt_list(momentNotice.getAt_list());
            noticeVO.setGender(actorData.getFb_gender());
            noticeVO.setImg(momentNotice.getImg());
            noticeVO.setGiftReward(momentNotice.getGiftReward());
            noticeVOList.add(noticeVO);
        }
        return noticeVOList;
    }

    public MomentVO noticeList(MomentDTO req) {
        if (req.getPage() == 1) {
            momentNoticeService.clearUnread(req.getUid());
        }
        MomentVO vo = new MomentVO();
        vo.setList(noticeToVO(momentNoticeService.gePageNotice(req.getUid(), req.getOpt(), req.getPage(), NOTICE_LIST_SIZE), req));
        vo.setNextUrl(vo.getList().size() < NOTICE_LIST_SIZE ? "" : req.getPage() + 1 + "");
        return vo;
    }

    public void clearNotice(MomentDTO req) {
        momentNoticeService.clearNotice(req.getUid(), req.getOpt());
    }

    public SetTopMomentVO setTop(MomentDTO req) {
        if (StringUtils.isEmpty(req.getMid())) {
            throw new CommonException(MomentHttpCode.PARAM_ERROR);
        }
        if (!momentConfig.getOFFICIAL_USER().contains(req.getUid())) {
            logger.error("set top moment error, actor is not official user uid={}", req.getUid());
            throw new CommonException(MomentHttpCode.AUTH_ERROR);
        }
        SetTopMomentVO vo = new SetTopMomentVO();
        if (1 == req.getOpt()) {
            // 置顶
            if (!topMomentRedis.getTopMoment().contains(req.getMid())) {
                topMomentRedis.addTopMoment(req.getUid(), req.getMid());
            }
            if (req.getTopMomentEndTime() > 0) {
                topMomentRedis.addTopMomentEndTime(req.getMid(), req.getTopMomentEndTime());
            }
            vo.setTopMomentEndTime(req.getTopMomentEndTime());
        } else if (2 == req.getOpt()) {
            // 取消置顶
            topMomentRedis.deleteTopMoment(req.getUid(), req.getMid());
            topMomentRedis.removeTopMomentEndTime(req.getMid());
        } else if (3 == req.getOpt()) {
            // 查询当前帖子的置顶截止时间
            int endTime = 0; // 0帖子没有被置顶过 -1 时间永久有效  >0为实际的置顶截止时间戳
            if (topMomentRedis.getTopMoment().contains(req.getMid())) {
                int end = topMomentRedis.getTopMomentEndTime(req.getMid());
                endTime = end == 0 ? -1 : end;
            }
            vo.setTopMomentEndTime(endTime);
        }
        return vo;
    }

    public void expiredTopMoment() {
        int now = DateHelper.getNowSeconds();
        Set<String> midSet = topMomentRedis.getTopMomentIdByTime(now);
        if (!CollectionUtils.isEmpty(midSet)) {
            midSet.forEach(item -> {
                topMomentRedis.removeTopMomentEndTime(item);
                topMomentRedis.deleteTopMoment("", item);
                logger.info("delete moment id success mid:{}", item);
            });
        }
    }

    public BannerVO getBanner(HttpEnvData req) {
        BannerVO vo = new BannerVO();
        vo.setList(homeBannerService.getHomeBannerList(req.getUid(), IndexBannerDao.TYPE_MOMENT, req.getSlang(), req.getVersioncode(), req.getApp_package_name(), req.getOs(), false));
        return vo;
    }


    private void asyncPublishMoment(String uid, int topicId) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                momentTopicService.publishMoment(uid, topicId);
            }
        });
    }

    public void reportMomentInteractLogEvent(String uid, MomentData momentData, int moment_interact_type,
                                             Integer share_nums) {
        // 埋点
        MomentInteractLogEvent event = new MomentInteractLogEvent();
        event.setUid(uid);
        event.setMoment_id(momentData.get_id().toString());
        event.setMoment_uid(momentData.getUid());
        event.setMoment_interact_type(moment_interact_type);
        if (share_nums != null) {
            event.setShare_nums(share_nums);
        }
        event.setCtime(DateHelper.getNowSeconds());
        if (momentData.getTopicId() > 0) {
            MomentTopicData momentTopicData = momentTopicDao.selectByIdFromCache(momentData.getTopicId());
            if (momentTopicData != null) {
                event.setIs_topic(1);
                event.setTopic_id(momentTopicData.getRid());
                event.setTopic_name(momentTopicData.getName());
                event.setTopic_create_uid(momentTopicData.getOwnerUid());
            }
        }
        eventReport.track(new com.quhong.analysis.EventDTO(event));
    }


    public CommentSuggestVO getCommentSuggest(HttpEnvData req) {
        CommentSuggestVO vo = new CommentSuggestVO();
        vo.setList(commentSuggestDao.getCommentSuggestListCache());
        return vo;
    }


    public void pinMoment(String mid, Integer opType, Integer endTime) {
        MomentData moment = momentDao.getMoment(mid);
        if (null == moment) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (1 == opType) {
            topMomentRedis.addTopMoment("[system]", mid);
            if (null != endTime) {
                topMomentRedis.addTopMomentEndTime(mid, endTime);
            }
        } else {
            topMomentRedis.deleteTopMoment("[system]", mid);
            topMomentRedis.removeTopMomentEndTime(mid);
        }
    }

    /**
     * 获取热门话题列表：先按置顶话题，再按最近7天内发布帖子数量排序
     * @param limit 返回数量限制
     * @return 热门话题列表
     */
    private List<MomentTopicVO> getTrendTopicList(String uid, int limit) {
        List<MomentTopicVO> trendTopicList = new ArrayList<>();
        Set<Integer> addedTopicIds = new HashSet<>();
        
        // 1. 先添加置顶话题
        List<MomentTopicSetTopData> validTopicHotList = momentTopicSetTopRedis.getValidTopicHotList(uid);
        for (MomentTopicSetTopData topData : validTopicHotList) {
            if (trendTopicList.size() >= limit) {
                break;
            }
            Integer topicId = topData.getTopicId();
            MomentTopicData topicData = momentTopicDao.selectByIdFromCache(topicId);
            if (topicData != null) {
                MomentTopicVO itemVO = new MomentTopicVO();
                itemVO.setTopicRid(topicData.getRid());
                itemVO.setName(topicData.getName());
                itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(topicData.getHead()));
                trendTopicList.add(itemVO);
                addedTopicIds.add(topicId);
            }
        }
        
        // 2. 如果置顶话题不足，再添加最近7天热门话题
        if (trendTopicList.size() < limit) {
            List<Integer> hotTopicIdList = momentDao.getTopicIdCountMomentsCache(7, 10);
            for (Integer topicId : hotTopicIdList) {
                if (trendTopicList.size() >= limit) {
                    break;
                }
                // 跳过已经添加的置顶话题
                if (addedTopicIds.contains(topicId)) {
                    continue;
                }
                MomentTopicData topicData = momentTopicDao.selectByIdFromCache(topicId);
                if (topicData != null) {
                    MomentTopicVO itemVO = new MomentTopicVO();
                    itemVO.setTopicRid(topicData.getRid());
                    itemVO.setName(topicData.getName());
                    itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(topicData.getHead()));
                    trendTopicList.add(itemVO);
                }
            }
        }
        
        return trendTopicList;
    }

    public void genFeaturedAndNewSa(int startTime, int endTime) {
        List<MomentData> momentList = momentDao.findPublicMomentListByTime(startTime, endTime);
        for (MomentData momentData : momentList) {
            String countryCode = ActorUtils.getUpperCaseCountryCode(momentData.getCountry());
            if (!RecommendService.SA_RECOMMEND_KEY.equals(countryCode)) {
                continue;
            }
            newSaRedis.addMoment(momentData);
            int score = recommendService.calculateScore(momentData);
            if (score > 20) {
                featuredSaRedis.addMoment(momentData);
            }
        }
    }

}
