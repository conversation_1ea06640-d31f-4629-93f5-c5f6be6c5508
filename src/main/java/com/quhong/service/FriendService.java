package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.MakeFriendRecordEvent;
import com.quhong.config.AsyncConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.AchieveBadgeConstant;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.FriendConstant;
import com.quhong.constant.UserHttpCode;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.UserBasicInfo;
import com.quhong.data.dto.FriendApplyDTO;
import com.quhong.data.dto.FriendListDTO;
import com.quhong.data.dto.TempToAidDTO;
import com.quhong.data.vo.*;
import com.quhong.dto.TextDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.UserInfoHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.CommonData;
import com.quhong.mq.MqItemConstant;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.chat.FriendApplyMsg;
import com.quhong.msg.room.BecomeFriendMessage;
import com.quhong.msg.room.RoomAddFriendsMsg;
import com.quhong.mysql.dao.FriendshipDao;
import com.quhong.mysql.dao.UserCommonMessageDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.FriendsListRedis;
import com.quhong.redis.InRoomFriendRedis;
import com.quhong.redis.UserInterceptionRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.NumberUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 好友Service
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@Component
public class FriendService {

    private static final Logger logger = LoggerFactory.getLogger(FriendService.class);

    private static final Integer PAGE_SIZE = 12; // 列表分页
    private static final Integer FRIEND_NUM = 500; // 好友默认数量上限
    private static final String MSG_EN = "Due to your recent violations, you have been banned from adding friends.Release time: %s";
    private static final String MSG_AR = " بسبب انتهاكاتك الأخيرة ، تم منعك من إضافة أصدقاءوقت فك الحظر: %s";
    private static final String SEND_FRIEND_REQUEST_MSG_EN = "send a friend request to you.";
    private static final String SEND_FRIEND_REQUEST_MSG_AR = "أرسل لك طلب الصداقة";

    @Resource
    private FriendsDao friendsDao;
    @Resource
    private FriendsNumDao friendsNumDao;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private FriendApplyDao friendApplyDao;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private LimitFuncUidDao limitFuncUidDao;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private LikeDao likeDao;
    @Resource
    private FollowDao followDao;
    @Resource
    private UserCommonMessageDao userCommonMessageDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private IDetectService detectService;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private HostListDao hostListDao;
    @Resource
    private BadgeListDao badgeListDao;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private InRoomFriendRedis inRoomFriendRedis;
    @Resource
    private AllOnlineFriendService allOnlineFriendService;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private FriendshipDao friendshipDao;
    @Resource
    private HVElasticsearchService hvElasticsearchService;
    @Resource
    private BadgeService badgeService;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private RecommendUserService recommendUserService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private UserInterceptionRedis userInterceptionRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    private ActorCommonService actorCommonService;

    /**
     * 获取好友列表
     */
    public FriendsVO getFriendsList(FriendListDTO req) {
        String uid = req.getUid();
        int page = req.getPage();
        if (StringUtils.isEmpty(uid) || page <= 0) {
            logger.error("params error.uid={} page={}", uid, page);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        FriendsVO vo = new FriendsVO();
        int start = (page - 1) * PAGE_SIZE;
        List<FriendsData> dataList = friendsDao.findDataList(uid, start, PAGE_SIZE);
        vo.setNextUrl(dataList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        FriendsNumData friendsNumData = friendsNumDao.findData(uid);
        if (CollectionUtils.isEmpty(dataList)) {
            if (friendsNumData != null) {
                // 拉取列表之后清除表数据
                friendsNumDao.cleanData(uid, null);
            }
            vo.setList(new ArrayList<>());
            return vo;
        }
        int count = friendsListRedis.getFriendCount(uid);
        if (friendsNumData == null) {
            // 有好友数据，没有friend_num数据,因为follow在导入数据
            saveNewFriendsNumData(uid, count);
        }
        List<FriendsVO.UserInfo> list = new ArrayList<>();
        for (FriendsData friendsData : dataList) {
            FriendsVO.UserInfo friendInfo = getFriendsUserInfo(uid, friendsData, friendsNumData);
            if (friendInfo == null) {
                continue;
            }
            list.add(friendInfo);
        }
        vo.setList(list);
        // 拉取列表之后清除表数据
        friendsNumDao.cleanData(uid, count);
        return vo;
    }

    /**
     * 获取好友列表 （v8.30版本）
     */
    public FriendListVO getFriendListNew(FriendListDTO req) {
        String uid = req.getUid();
        String key = req.getKey();
        int page = req.getPage();
        if (StringUtils.isEmpty(uid) || page <= 0) {
            logger.error("params error.uid={} page={}", uid, page);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        FriendListVO vo = new FriendListVO();
        vo.setNextUrl("");
        vo.setList(Collections.emptyList());
        List<FriendsData> dataList;
        if (StringUtils.isEmpty(key)) {
            dataList = friendsDao.findAllFriend(uid, true);
        } else {
            dataList = new ArrayList<>();
            FriendsData friendByRid = findFriendByRid(uid, key);
            if (friendByRid != null) {
                dataList.add(friendByRid);
            }
        }
        FriendsNumData friendsNumData = friendsNumDao.findData(uid);
        if (CollectionUtils.isEmpty(dataList)) {
            if (friendsNumData != null) {
                // 拉取列表之后清除表数据
                friendsNumDao.cleanData(uid, null);
            }
            return vo;
        }
        int count = friendsListRedis.getFriendCount(uid);
        if (friendsNumData == null) {
            // 有好友数据，没有friend_num数据,因为follow在导入数据
            saveNewFriendsNumData(uid, count);
        }
        // 优先展示在线的好友，其次按照成为好友时间倒序排列
        Set<String> allOnlineSet = allOnlineFriendService.getAllOnlineSet();
        Comparator<FriendsData> onlineAsc = Comparator.comparing(o -> allOnlineSet.contains(uid.equals(o.getUidFirst()) ? o.getUidSecond() : o.getUidFirst()) ? 1 : 0);
        Comparator<FriendsData> onlineDesc = onlineAsc.reversed();
        Comparator<FriendsData> ctimeDesc = Comparator.comparing(FriendsData::getCtime).reversed();
        dataList.sort(onlineDesc.thenComparing(ctimeDesc));
        // 分页
        int start = (page - 1) * PAGE_SIZE;
        int end = Math.min(page * PAGE_SIZE, dataList.size());
        if (start >= end) {
            return vo;
        }
        int nextPage = 0;
        if (dataList.size() > end) {
            nextPage = page + 1;
        }
        vo.setNextUrl(nextPage > 0 ? String.valueOf(nextPage) : "");
        List<FriendListVO.UserInfo> list = new ArrayList<>();
        List<FriendsData> friendsList = dataList.subList(start, end);
        if (CollectionUtils.isEmpty(friendsList)) {
            return vo;
        }
        for (FriendsData data : friendsList) {
            FriendListVO.UserInfo userInfo = setFriendInfo(uid, data, allOnlineSet);
            if (userInfo == null) {
                continue;
            }
            list.add(userInfo);
        }
        vo.setList(list);
        return vo;
    }

    /**
     * 通过rid查询好友
     */
    private FriendsData findFriendByRid(String uid, String strRid) {
        ActorData actor;
        if (NumberUtils.isNumber(strRid)) {
            int rid;
            try {
                rid = Integer.parseInt(strRid);
            } catch (Exception e) {
                logger.error("An exception occurred when converting String to integer. uid={} strRid={}", uid, strRid);
                return null;
            }
            actor = actorDao.getActorByRid(rid);
        } else {
            actor = actorDao.getActorByStrRid(strRid);
        }
        if (actor == null || actor.getValid() != 1) {
            return null;
        }
        return friendsDao.findData(uid, actor.getUid());
    }

    /**
     * 填充好友信息
     */
    private FriendListVO.UserInfo setFriendInfo(String uid, FriendsData data, Set<String> allOnlineSet) {
        String aid = Objects.equals(uid, data.getUidFirst()) ? data.getUidSecond() : data.getUidFirst();
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData == null) {
            return null;
        }
        FriendListVO.UserInfo userInfo = new FriendListVO.UserInfo();
        setUserBasicInfo(aid, actorData, userInfo);
        userInfo.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
        userInfo.setCtime(data.getCtime());
        userInfo.setOnline(allOnlineSet.contains(aid) ? 1 : 0);
        userInfo.setInRoom(0);
        if (userInfo.getOnline() == 1 && actorData.getAccept_talk() != 0) {
            String roomId = roomPlayerRedis.getActorRoomStatus(aid);
            if (!StringUtils.isEmpty(roomId) && RoomUtils.isVoiceRoom(roomId) && !whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                userInfo.setInRoom(1);
                userInfo.setInRoomId(roomId);
            } else {
                userInfo.setInRoomId("");
            }
        }
        userInfo.setBadgeList(getBadgeList(aid));
        return userInfo;
    }

    /**
     * 获取用户佩戴的勋章
     */
    private List<String> getBadgeList(String uid) {
        List<BadgeData> wearBadgeList = badgeDao.getWearBadgeList(uid);
        if (CollectionUtils.isEmpty(wearBadgeList)) {
            return Collections.emptyList();
        }
        return badgeListDao.getSmallIconsFromCache(wearBadgeList);
    }

    /**
     * 获取好友申请列表
     */
    public FriendApplyVO getFriendApplyList(FriendListDTO req) {
        String uid = req.getUid();
        int page = req.getPage();
        FriendApplyVO vo = new FriendApplyVO();
        int start = (page - 1) * PAGE_SIZE;
        List<FriendApplyData> dataList = friendApplyDao.findDataList(uid, start, PAGE_SIZE);
        vo.setNextUrl(dataList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        friendApplyDao.cleanUnread(uid);
        List<FriendApplyVO.UserInfo> list = new ArrayList<>();
        for (FriendApplyData data : dataList) {
            FriendApplyVO.UserInfo friendApplyUserInfo = getFriendApplyUserInfo(uid, data);
            if (friendApplyUserInfo == null) {
                continue;
            }
            list.add(friendApplyUserInfo);
        }
        vo.setList(list);
        return vo;
    }

    /**
     * 添加好友申请
     */
    public void addFriendApply(FriendApplyDTO req) {
        String uid = req.getUid();
        String aid = req.getAid();
        if (!isCheckTnId(uid)) {
            monitorSender.info("ustar_java_exception", "用户未校验图灵盾接口", JSON.toJSONString(req));
            basePlayerRedis.removeToken(uid);
            logger.error("actor is not check tnId uid={}", uid);
            throw new CommonException(UserInfoHttpCode.UNLOGIN);
        }
        // v7.8 账号冻结或封禁权限控制
        Boolean privilege = checkPrivilege(uid);
        if (!privilege) {
            logger.error("The account has been frozen. uid={}", uid);
            throw new CommonException(HttpCode.USER_MONITOR_FREEZE);
        }
        LimitFuncUidData limitUidData = limitFuncUidDao.findData(uid);
        if (limitUidData != null && limitUidData.getFriend_type() != 0) {
            logger.error("The account has been frozen. uid={}", uid);
            throw new CommonException(UserInfoHttpCode.USER_LIMIT_SEND_FRIEND_APPLY);
        }
        // 黑名单校验
        if (blackListDao.isBlock(uid, aid)) {
            return;
        }
        if (blackListDao.isBlock(aid, uid)) {
            throw new CommonException(UserHttpCode.BLOCKED_BY_ACTOR);
        }
        ActorData uidInfo = basePlayerRedis.getActorFromRedis(uid);
        String tnId = uidInfo.getTn_id();
        if (!StringUtils.isEmpty(tnId)) {
            int ctime = getAddFriendsBlockTnid(tnId);
            if (ctime != 0) {
                LocalDateTime time = LocalDateTime.ofEpochSecond(ctime, 0, ZoneOffset.ofHours(3));
                String sTime = DayTimeSupport.format(time);
                logger.info("Due to your recent violations, you have been banned from adding friends. uid={}", uid);
                throw new CommonException(new HttpCode(72, String.format(MSG_EN, sTime), String.format(MSG_AR, sTime)));
            }
        }
        // 检测好友数量是否到达上限
        if (checkFriendNumOverLimit(uid)) {
            logger.info("Your friends number is over the limitation, please clean up in time. uid={}", uid);
            throw new CommonException(UserInfoHttpCode.FRIENDS_NUMBER_EXCEEDS_LIMIT);
        }
        ActorData aidInfo = basePlayerRedis.getActorFromRedis(aid);
        if (aidInfo != null && aidInfo.getGeneralConfActorData() != null && aidInfo.getGeneralConfActorData().getBuddy() != 0) {
            logger.info("He/She has set refuse to add friends. uid={} aid={}", uid, aid);
            throw new CommonException(UserInfoHttpCode.SET_REFUSE_TO_ADD_FRIENDS);
        }
        // 已经是好友了无法申请
        if (friendsDao.findData(uid, aid) != null) {
            logger.info("You are already friends. uid={}", uid);
            throw new CommonException(UserInfoHttpCode.CODE_ALREADY_FRIEND);
        }
        // 已经申请过了(10分钟只能申请2次)
        if (friendApplyDao.getFriendApplyCount(uid, aid) >= 2) {
            logger.info("Too many friend requests sent. Please try again later. uid={}", uid);
            throw new CommonException(UserInfoHttpCode.CODE_ALREADY_SEND);
        }
        if (!StringUtils.isEmpty(req.getMsg())) {
            // 20200817 bigo取代self文本检测
            if (detectService.detectText(new TextDTO(req.getMsg(), DetectOriginConstant.USER_APPLY_FRIEND, uid)).getData().getIsSafe() == 0) {
                logger.info("Dirty word in message is not allowed!. uid={} msg={}", uid, req.getMsg());
                throw new CommonException(UserInfoHttpCode.CODE_NOT_ALLOW);
            }
        }
        FriendApplyData data = friendApplyDao.findData(uid, aid);
        if (data != null && data.getOptType() == FriendConstant.OPERATE_DEFAULT) {
            return;
        }
        // 保存好友申请
        saveFriendApplyData(uid, aid, req.getMsg());
        // fcm推送
        sendFriendApplyMsg(uid, aid, uidInfo, req.getSlang());
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", aid, aid, CommonMqTaskConstant.APPLY_FRIEND, 1));
        // 新用户收到加好友请求时，公屏展示同意添加好友消息
        if (ActorUtils.isNewRegisterActor(aid, 7)) {
            String uidRoomId = roomPlayerRedis.getActorRoomStatus(uid);
            String aidRoomId = roomPlayerRedis.getActorRoomStatus(aid);
            if (!StringUtils.isEmpty(uidRoomId) && !StringUtils.isEmpty(aidRoomId) && Objects.equals(uidRoomId, aidRoomId)) {
                // 发送im
                sendRoomAddFriendsMsg(uid, aid, aidRoomId, uidInfo);
            }
        }
    }

    /**
     * 删除好友申请消息
     */
    public void deleteFriendApply(FriendApplyDTO req) {
        FriendApplyData data = friendApplyDao.findData(req.getAid(), req.getUid());
        if (data == null) {
            logger.error("delete error. uid={} aid={}", req.getUid(), req.getAid());
            throw new CommonException(UserInfoHttpCode.DELETE_ERROR);
        }
        friendApplyDao.removeFriendApplyFromDb(data);
    }

    /**
     * 处理好友申请, 同意或者拒绝
     */
    public void handleFriendApply(FriendApplyDTO req) {
        String uid = req.getUid();
        String aid = req.getAid();
        int opt = req.getOpt();
        FriendApplyData data = friendApplyDao.findData(aid, uid, FriendConstant.OPERATE_DEFAULT);
        if (data == null) {
            logger.info("You are already friends. uid={} aid={}", req.getUid(), req.getAid());
            throw new CommonException(UserInfoHttpCode.CODE_ALREADY_FRIEND);
        }
        if (opt == FriendConstant.OPERATE_AGREE) {
            // 检测好友数量是否到达上限
            if (checkFriendNumOverLimit(uid)) {
                logger.info("Your friends number is over the limitation, please clean up in time. uid={} aid={}", req.getUid(), req.getAid());
                throw new CommonException(UserInfoHttpCode.FRIENDS_NUMBER_EXCEEDS_LIMIT);
            }
            FriendsData friendsData = friendsDao.findData(uid, aid);
            if (friendsData != null) {
                if (data.getOptType() != FriendConstant.OPERATE_AGREE) {
                    data.setOptType(FriendConstant.OPERATE_AGREE);
                    friendApplyDao.updateOptType(data);
                }
                logger.info("You are already friends. uid={} aid={}", req.getUid(), req.getAid());
                throw new CommonException(UserInfoHttpCode.CODE_ALREADY_FRIEND);
            }
            friendsData = new FriendsData();
            friendsData.setUidFirst(uid);
            friendsData.setUidSecond(aid);
            friendsData.setSource(FriendConstant.SOURCE_FRIEND_ADD);
            friendsData.setCtime(DateHelper.getNowSeconds());
            friendsDao.save(friendsData);
            // 好友列表写进redis
            friendsListRedis.saveFriendToRedis(uid, aid);
            friendsListRedis.saveFriendToRedis(aid, uid);
            // 操作好友数量表
            optFriendNum(uid, aid, FriendConstant.FRIEND_NUM_ADD);
            // 保存到新的好友关系表里
            try {
                optFriendBadge(uid, aid, FriendConstant.FRIEND_NUM_ADD);
                friendshipDao.addFriend(uid, aid, FriendConstant.SOURCE_FRIEND_ADD);
            } catch (Exception e) {
                logger.error("insert friendship data error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
            }
            data.setOptType(FriendConstant.OPERATE_AGREE);
            // 任务：加好友
            CommonMqTopicData mqUidData = new CommonMqTopicData(uid, "", aid, aid, CommonMqTaskConstant.ADD_FRIEND, 1);
            mqUidData.setJsonData(JSONObject.toJSONString(new CommonMqTopicData.FriendInfo(FriendConstant.SOURCE_FRIEND_ADD)));
            commonTaskService.sendCommonTaskMq(mqUidData);

            CommonMqTopicData mqAidData = new CommonMqTopicData(aid, "", uid, uid, CommonMqTaskConstant.ADD_FRIEND, 1);
            mqAidData.setJsonData(JSONObject.toJSONString(new CommonMqTopicData.FriendInfo(FriendConstant.SOURCE_FRIEND_ADD)));
            commonTaskService.sendCommonTaskMq(mqAidData);
            iMsgService.updateNormalChat(new TempToAidDTO(uid, aid, ""));
            doMakeFriendRecordEvent(aid, aid, uid, data.getCtime(), 1, FriendConstant.SOURCE_FRIEND_ADD);
            pushBecomeFriend(uid, aid);
        } else {
            data.setOptType(FriendConstant.OPERATE_REJECT);
        }
        friendApplyDao.updateOptType(data);
    }

    public void becomeFriend(FriendApplyDTO req) {
        String uid = req.getUid();
        String aid = req.getAid();
        // 检测好友数量是否到达上限
        if (checkFriendNumOverLimit(uid)) {
            logger.info("Your friends number is over the limitation, please clean up in time. uid={} aid={}", req.getUid(), req.getAid());
            throw new CommonException(UserInfoHttpCode.FRIENDS_NUMBER_EXCEEDS_LIMIT);
        }
        FriendsData friendsData = friendsDao.findData(uid, aid);
        if (friendsData != null) {
            logger.info("You are already friends. uid={} aid={}", req.getUid(), req.getAid());
            throw new CommonException(UserInfoHttpCode.CODE_ALREADY_FRIEND);
        }
        friendsData = new FriendsData();
        friendsData.setUidFirst(uid);
        friendsData.setUidSecond(aid);
        friendsData.setSource(req.getFriendSource());
        friendsData.setCtime(DateHelper.getNowSeconds());
        friendsDao.save(friendsData);
        // 好友列表写进redis
        friendsListRedis.saveFriendToRedis(uid, aid);
        friendsListRedis.saveFriendToRedis(aid, uid);
        // 操作好友数量表
        optFriendNum(uid, aid, FriendConstant.FRIEND_NUM_ADD);
        // 保存到新的好友关系表里
        try {
            optFriendBadge(uid, aid, FriendConstant.FRIEND_NUM_ADD);
            friendshipDao.addFriend(uid, aid, FriendConstant.SOURCE_FRIEND_ADD);
            // 如果有申请记录改变状态
            FriendApplyData uidApplyData = friendApplyDao.findData(uid, aid, FriendConstant.OPERATE_DEFAULT);
            if (uidApplyData != null){
                uidApplyData.setOptType(FriendConstant.OPERATE_AGREE);
                friendApplyDao.updateOptType(uidApplyData);
            }
            FriendApplyData aidApplyData = friendApplyDao.findData(aid, uid, FriendConstant.OPERATE_DEFAULT);
            if (aidApplyData != null){
                aidApplyData.setOptType(FriendConstant.OPERATE_AGREE);
                friendApplyDao.updateOptType(aidApplyData);
            }
        } catch (Exception e) {
            logger.error("insert friendship data error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
        // 任务：加好友
        CommonMqTopicData mqUidData = new CommonMqTopicData(uid, "", aid, aid, CommonMqTaskConstant.ADD_FRIEND, 1);
        mqUidData.setJsonData(JSONObject.toJSONString(new CommonMqTopicData.FriendInfo(req.getFriendSource())));
        commonTaskService.sendCommonTaskMq(mqUidData);

        CommonMqTopicData mqAidData = new CommonMqTopicData(aid, "", uid, uid, CommonMqTaskConstant.ADD_FRIEND, 1);
        mqAidData.setJsonData(JSONObject.toJSONString(new CommonMqTopicData.FriendInfo(req.getFriendSource())));
        commonTaskService.sendCommonTaskMq(mqAidData);
        doMakeFriendRecordEvent(aid, aid, uid, DateHelper.getNowSeconds(), 1, FriendConstant.SOURCE_SAY_HELLO);
        pushBecomeFriend(uid, aid);
    }

    private void pushBecomeFriend(String uid, String aid) {
        roomWebSender.sendPlayerWebMsg("", uid, uid, new BecomeFriendMessage(uid, aid), false);
        roomWebSender.sendPlayerWebMsg("", aid, aid, new BecomeFriendMessage(aid, uid), false);
    }


    @Async(AsyncConfig.ASYNC_TASK)
    public void deleteFriend(FriendApplyDTO req) {
        deleteFriend(req.getUid(), req.getAid());
    }

    /**
     * 删除好友
     */
    public void deleteFriend(String uid, String aid) {
        FriendsData friendsData = friendsDao.findData(uid, aid);
        int ctime;
        if (friendsData == null) {
            // 从好友列表移除
            removeFriendListFromRedis(uid, aid);
            logger.info("you are not friends. uid={} aid={}", uid, aid);
            return;
        } else {
            // 广播消息处理朋友圈
//            broadcastMessage(DELETE_FRIEND_CHANNEL, buildFriendMsgBody(uid, aid, friendsData));
            if (friendsData.getSource() == FriendConstant.SOURCE_FRIEND_FOLLOW) {
                cancelFollowNew(uid, aid);
            }
            ctime = friendsData.getCtime();
            friendsDao.removeFromDB(friendsData);
        }
        // 从好友列表移除
        removeFriendListFromRedis(uid, aid);
        // 好友数量修改
        optFriendNum(uid, aid, FriendConstant.FRIEND_NUM_DELETE);
        // 申请表记录删除
        friendApplyDao.removeFriendApplyFromDb(uid, aid);
        // 喜欢表记录删除
        likeDao.removeLikeFromDb(uid, aid);
        // 删除新的好友关系表
        friendshipDao.deleteFriend(uid, aid);
        // 删除好友间的聊天消息
        deleteBothMsg(uid, aid);
        if (friendsData.getSource() == FriendConstant.SOURCE_FRIEND_MATCH && (DateHelper.getNowSeconds() - ctime) < 86400) {
            // 发送mq消息用于删除匹配
            mqSenderService.sendPartyGirlTaskToMq(new CommonData(uid, MqItemConstant.MATCH_DELETE, aid, 0));
        }

        // 好友勋章递减
        optFriendBadge(uid, aid, FriendConstant.FRIEND_NUM_DELETE);
        // 清除拦截标识
        deleteBothInterceptionMsg(uid, aid);

        doMakeFriendRecordEvent(uid, uid, aid, -1, -1, 0);
    }

    // 异步删除好友间的聊天消息
    public void deleteBothMsg(String uid, String aid) {
        TempToAidDTO dto = new TempToAidDTO();
        dto.setUid(uid);
        dto.setAid(aid);
        if (iMsgService.deleteBothMsg(dto).isError()) {
            logger.error("delete msg record fail uid={} aid={}", uid, aid);
        }
    }

    // 好友拦截消息
    public void deleteBothInterceptionMsg(String uid, String aid) {
        userInterceptionRedis.removeUserInterception(uid, aid);
        userInterceptionRedis.removeUserInterception(aid, uid);
    }

    /**
     * pk邀请好友时的搜索接口
     */
    public FriendSearchVO getFriendSearchList(FriendListDTO req) {
        FriendSearchVO vo = new FriendSearchVO();
        vo.setList(Collections.emptyList());
        String uid = req.getUid();
        String key = req.getKey();
//        ActorData actor = actorDao.getActorByStrRidFromDb(key);
        List<ActorData> actorList = actorDao.searchActorByRid(key, 1, 50);
        List<FriendSearchVO.FriendInfo> list = new ArrayList<>();
        for (ActorData actor : actorList) {
            if (actor == null || actor.getValid() != 1) {
                logger.error("user not exist. uid={} ", uid);
                throw new CommonException(UserInfoHttpCode.USER_NOT_EXIST);
            }
            String aid = actor.getUid();
            FriendsData friendsData = friendsDao.findData(uid, aid);
            if (friendsData == null) {
                return vo;
            }
            FriendSearchVO.FriendInfo friendInfo = getSearchUserInfo(aid);
            list.add(friendInfo);
        }
        vo.setList(list);
        return vo;
    }

    /**
     * 推荐好友列表
     */
    public FriendRecommendVO getFriendRecommendList(FriendListDTO req) {
        FriendRecommendVO vo = new FriendRecommendVO();
        List<HostListData> hostList = hostListDao.findAllData();
        if (CollectionUtils.isEmpty(hostList)) {
            vo.setList(Collections.emptyList());
            return vo;
        }
        List<String> hostUids = hostList.stream().map(HostListData::getUid).filter(aid -> !friendsDao.isFriend(req.getUid(), aid)).collect(Collectors.toList());
        Collections.shuffle(hostUids);
        List<String> hostUidlist = hostUids.subList(0, 5);
        List<FriendRecommendVO.UserInfo> list = new ArrayList<>();
        for (String hostUid : hostUidlist) {
            ActorData actorData = actorDao.getActorDataFromCache(hostUid);
            if (actorData == null) {
                continue;
            }
            FriendRecommendVO.UserInfo userInfo = new FriendRecommendVO.UserInfo();
            setUserBasicInfo(hostUid, actorData, userInfo);
            userInfo.setUid(hostUid);
            userInfo.setOs(actorData.getIntOs());
            list.add(userInfo);
        }
        vo.setList(list);
        return vo;
    }

    /**
     * 获取在线好友列表（最多展示10个）
     */
    public OnlineFriendVO getOnlineFriendList(FriendListDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        List<FriendsData> dataList = getInRoomFriendList(req.getUid());
        OnlineFriendVO vo = new OnlineFriendVO();
        List<OnlineFriendVO.FriendInfo> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (FriendsData data : dataList) {
                String aid = Objects.equals(req.getUid(), data.getUidFirst()) ? data.getUidSecond() : data.getUidFirst();
                ActorData userData = actorDao.getActorDataFromCache(aid);
                if (userData == null) {
                    continue;
                }
                OnlineFriendVO.FriendInfo friendInfo = new OnlineFriendVO.FriendInfo();
                friendInfo.setName(userData.getName());
                friendInfo.setHead(vipInfoDao.generateVipUrl(userData.getUid(), userData.getHead(), ImageUrlGenerator.MODE_300));
                friendInfo.setAid(userData.getUid());
                friendInfo.setRid(userData.getRid());
                friendInfo.setGender(userData.getFb_gender());
                friendInfo.setRidData(userData.getRidData());
                String roomId = roomPlayerRedis.getActorRoomStatus(aid);
                friendInfo.setInRoomId(!StringUtils.isEmpty(roomId) ? !whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID) ? roomId : "" : "");
                list.add(friendInfo);
            }
        }
        vo.setFriendNum(inRoomFriendRedis.getInRoomFriendCountFromRedis(req.getUid()));
        vo.setList(list);
        if (vo.getList().size() == 0 && AppVersionUtils.versionCheck(847, req)) {
            // 847版本 好友在房间栏如果没有用户，显示为你推荐
            recommendUserService.getRecommendUserList(actorData, vo);
        }
        return vo;
    }

    /**
     * 获取11个在房间的好友
     */
    private List<FriendsData> getInRoomFriendList(String uid) {
        int onlineFriendNum = inRoomFriendRedis.getInRoomFriendCountFromRedis(uid);
        if (onlineFriendNum == 0) {
            inRoomFriendRedis.saveInRoomFriendToRedis(uid);
            onlineFriendNum = inRoomFriendRedis.getInRoomFriendCountFromRedis(uid);
        }
        List<FriendsData> list = new ArrayList<>();
        int listSize = 10;
        if (onlineFriendNum > listSize) {
            for (int i = 1; i <= listSize; i++) {
                FriendsData friendsData = inRoomFriendRedis.getOneInRoomFriend(uid);
                if (friendsData == null) {
                    continue;
                }
                list.add(friendsData);
            }
        } else {
            List<String> strObjectList = inRoomFriendRedis.getAllInRoomFriends(uid);
            for (String strObject : strObjectList) {
                if (StringUtils.isEmpty(strObject)) {
                    continue;
                }
                FriendsData friendsData = JSONObject.parseObject(strObject, FriendsData.class);
                if (friendsData == null) {
                    continue;
                }
                list.add(friendsData);
            }
        }
        return list;
    }

    /**
     * 发送im
     */
    private void sendRoomAddFriendsMsg(String uid, String aid, String aidRoomId, ActorData userInfo) {
        RoomAddFriendsMsg msg = new RoomAddFriendsMsg();
        msg.setName(userInfo.getName());
        msg.setAid(uid);
        msg.setHead(ImageUrlGenerator.generateRoomUserUrl(userInfo.getHead()));
        roomWebSender.sendPlayerWebMsg(aidRoomId, uid, aid, msg, true);
    }

    /**
     * 发送好友申请消息
     */
    private void sendFriendApplyMsg(String uid, String aid, ActorData userInfo, Integer slang) {
        FriendApplyMsg msg = new FriendApplyMsg();
        msg.setFromUid(uid);
        msg.setName(userInfo.getName());
        msg.setHead(ImageUrlGenerator.generateRoomUserUrl(userInfo.getHead()));
        msg.setBody(slang == 1 ? SEND_FRIEND_REQUEST_MSG_EN : SEND_FRIEND_REQUEST_MSG_AR);
        roomWebSender.sendPlayerWebMsg("", uid, aid, msg, true);
    }

    /**
     * 保存好友申请
     */
    private void saveFriendApplyData(String uid, String aid, String msg) {
        FriendApplyData friendApplyData = friendApplyDao.findData(uid, aid);
        if (friendApplyData == null) {
            friendApplyData = new FriendApplyData();
            friendApplyData.setUid(uid);
            friendApplyData.setAid(aid);
        }
        friendApplyData.setOptType(FriendConstant.OPERATE_DEFAULT);
        friendApplyData.setIsNew(1);
        friendApplyData.setMsg(msg);
        friendApplyData.setCtime(DateHelper.getNowSeconds());
        friendApplyDao.save(friendApplyData);
        friendApplyDao.saveFriendApplyCount(uid, aid);
    }

    /**
     * 检测好友数量是否到达上限 ture超过好友上限  false没有超过好友上限
     */
    private Boolean checkFriendNumOverLimit(String uid) {
        int currentFriendNum = friendsListRedis.getFriendCount(uid);
        int limitNum = getFriendNumLimit();
        logger.info("currentFriendNum={}, limitNum={}", currentFriendNum, limitNum);
        return currentFriendNum > limitNum;
    }

    /**
     * 从好友列表移除
     */
    private void removeFriendListFromRedis(String uid, String aid) {
        friendsListRedis.removeFromRedis(uid, aid);
        friendsListRedis.removeFromRedis(aid, uid);
        inRoomFriendRedis.remove(uid);
        inRoomFriendRedis.remove(aid);
    }

    /**
     * uid取消关注aid, 由于该方法需要提供给其他进程调用，所以从api方法内抽象出来
     */
    public void cancelFollowNew(String uid, String aid) {
        followDao.removeFollowed(uid, aid);
        userCommonMessageDao.updateMessageStatus(uid, aid, 1, 0);
        // 发布消息， 用于埋点数据统计： 取消关注的
//        broadcastMessage(FOLLOW_FRIEND_CHANNEL, buildFollowMsgBody(uid, aid, UNFOLLOW_ITEM));
    }

    /**
     * 构建关注消息
     */
    private Object buildFollowMsgBody(String uid, String aid, String item) {
        JSONObject msgBody = new JSONObject();
        msgBody.put("uid", uid);
        msgBody.put("aid", aid);
        msgBody.put("item", item);
        return msgBody;
    }

    /**
     * 构建朋友圈消息body
     */
    private Object buildFriendMsgBody(String uid, String aid, FriendsData friendsData) {
        JSONObject msgBody = new JSONObject();
        msgBody.put("uid", uid);
        msgBody.put("aid", aid);
        msgBody.put("ctime", friendsData.getCtime());
        msgBody.put("item", "delfriend");
        return msgBody;
    }


    /**
     * 操作好友数量表
     *
     * @param optType 1新增好友 2删除好友
     */
    private void optFriendNum(String uid, String aid, int optType) {
        FriendsNumData uidData = friendsNumDao.findData(uid);
        FriendsNumData aidData = friendsNumDao.findData(aid);
        if (optType == FriendConstant.FRIEND_NUM_ADD) {
            // 新增好友
            updateFriendNum(uid, aid, uidData);
            updateFriendNum(aid, uid, aidData);

        } else if (optType == FriendConstant.FRIEND_NUM_DELETE) {
            // 删除好友
            if (uidData == null || aidData == null) {
                logger.error("user_first and user_second can not be None. uid={} aid={}", uid, aid);
                return;
            }
            uidData.setFriends(uidData.getFriends() - 1);
            aidData.setFriends(aidData.getFriends() - 1);
            friendsNumDao.saveData(uidData);
            friendsNumDao.saveData(aidData);
        }
    }

    /**
     * 更新好友数量表
     */
    private void updateFriendNum(String uid, String aid, FriendsNumData uidData) {
        if (uidData == null) {
            uidData = new FriendsNumData();
            uidData.setUid(uid);
            uidData.setFriends(friendsDao.getFriendCount(uid) + 1);
            uidData.setNewFriends(1);
            uidData.setNewFriendsList(Collections.singletonList(aid));
            uidData.setLike(likeDao.findCountByAid(uid));
            uidData.setNewLike(0);
        } else {
            uidData.setFriends(uidData.getFriends() != null ? uidData.getFriends() + 1 : 1);
            uidData.setNewFriends(uidData.getNewFriends() != null ? uidData.getNewFriends() + 1 : 1);
            List<String> newFriendsList = uidData.getNewFriendsList();
            if (!CollectionUtils.isEmpty(newFriendsList)) {
                newFriendsList.add(aid);
            }
            uidData.setNewFriendsList(newFriendsList);
        }
        friendsNumDao.saveData(uidData);
    }

    /**
     * 好友勋章
     */
    private void optFriendBadge(String uid, String aid, int optType) {
        int uidFriendsNum = friendsListRedis.getFriendCount(uid);
        int aidFriendsNum = friendsListRedis.getFriendCount(aid);
        int friendBadge = commonConfig.getConfigIntValue(CommonConfig.FRIEND_BADGE_SWITCH);

        if (optType == FriendConstant.FRIEND_NUM_ADD) {
            if (friendBadge == 1) {
                badgeService.doAchieveBadge(uid, AchieveBadgeConstant.TYPE_FRIEND, uidFriendsNum, 1);
                badgeService.doAchieveBadge(aid, AchieveBadgeConstant.TYPE_FRIEND, aidFriendsNum, 1);
            }
        } else if (optType == FriendConstant.FRIEND_NUM_DELETE) {
            if (friendBadge == 1) {
                badgeService.deleteAchieveBadge(uid, AchieveBadgeConstant.TYPE_FRIEND, uidFriendsNum, 1);
                badgeService.deleteAchieveBadge(aid, AchieveBadgeConstant.TYPE_FRIEND, aidFriendsNum, 1);
            }
        }
    }

    private Boolean checkPrivilege(String uid) {
        int configValue = commonConfig.getConfigIntValue(CommonConfig.MONITOR_ADD_FRIEND);
        // 未开启更新个人信息权限控制（v7.8）
        if (configValue == 0) {
            return true;
        }
        // 是否被冻结或封禁
        return userMonitorDao.friendApplyPrivilege(uid);
    }

    /**
     * 获取申请好友用户详细信息
     */
    private FriendApplyVO.UserInfo getFriendApplyUserInfo(String uid, FriendApplyData data) {
        String aid = Objects.equals(uid, data.getUid()) ? data.getAid() : data.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData == null) {
            return null;
        }
        FriendApplyVO.UserInfo userinfo = new FriendApplyVO.UserInfo();
        setUserBasicInfo(aid, actorData, userinfo);
        userinfo.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
        userinfo.setOs(actorData.getOs());
        // opt_type 0未处理 1已经同意 2已经拒绝
        userinfo.setOpt_type(data.getOptType());
        userinfo.setCtime(data.getCtime());
        userinfo.setMsg(!StringUtils.isEmpty(data.getMsg()) ? data.getMsg() : String.format("Hi, I am %s", userinfo.getName()));
        return userinfo;
    }

    /**
     * 获取好友用户详细信息
     */
    private FriendsVO.UserInfo getFriendsUserInfo(String uid, FriendsData friendsData, FriendsNumData friendsNumData) {
        String friendAid = Objects.equals(uid, friendsData.getUidFirst()) ? friendsData.getUidSecond() : friendsData.getUidFirst();
        ActorData actorData = actorDao.getActorDataFromCache(friendAid);
        if (actorData == null) {
            return null;
        }
        FriendsVO.UserInfo friendInfo = new FriendsVO.UserInfo();
        setUserBasicInfo(friendAid, actorData, friendInfo);
        friendInfo.setOs(actorData.getIntOs());
        boolean isNew = friendsNumData != null && !CollectionUtils.isEmpty(friendsNumData.getNewFriendsList()) && friendsNumData.getNewFriendsList().contains(friendAid);
        friendInfo.setIs_new(isNew ? 1 : 0);
        return friendInfo;
    }

    /**
     * 获取搜索的好友用户详细信息
     */
    private FriendSearchVO.FriendInfo getSearchUserInfo(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            return null;
        }
        FriendSearchVO.FriendInfo friendInfo = new FriendSearchVO.FriendInfo();
        friendInfo.setOs(actorData.getIntOs());
        friendInfo.setLogin_status(actorData.getLastLogin() != null ? actorData.getLastLogin().getStatus() : FriendConstant.TALK_STATUS_OFFLINE);
        friendInfo.setAccept_talk(actorData.getAccept_talk());
        friendInfo.setVchat_status(actorData.getVchat_status());
        String roomId = roomPlayerRedis.getActorRoomStatus(uid);
        friendInfo.setRoomId(!StringUtils.isEmpty(roomId) ? !whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID) ? roomId : "" : "");
        friendInfo.setUlvl(userLevelDao.getUserLevel(uid));
        setUserBasicInfo(uid, actorData, friendInfo);
        friendInfo.setSstatus(0);
        return friendInfo;
    }


    /**
     * 设置用户基础信息
     */
    private void setUserBasicInfo(String aid, ActorData actorData, UserBasicInfo userInfo) {
        userInfo.setName(actorData.getName());
        userInfo.setDesc(actorData.getDesc());
        userInfo.setGender(actorData.getFb_gender() != 0 ? actorData.getFb_gender() : 1);
        userInfo.setAge(actorData.getAge());
        userInfo.setCountry(actorData.getCountry());
        userInfo.setRid(actorData.getRid());
        userInfo.setValid(actorData.getValid());
        userInfo.setAid(aid);
        userInfo.setViplevel(vipInfoDao.getIntVipLevelFromCache(aid));
        userInfo.setVipMedal(actorCommonService.getCommonVipMedal(aid, userInfo.getViplevel()));
        userInfo.setUlvl(userLevelDao.getUserLevel(aid));
        userInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), userInfo.getViplevel()));
        userInfo.setRidData(actorData.getRidData());
    }

    /**
     * 新增friend_num数据
     */
    private void saveNewFriendsNumData(String uid, Integer count) {
        FriendsNumData newData = new FriendsNumData();
        newData.setUid(uid);
        newData.setFriends(count);
        newData.setNewFriends(0);
        newData.setNewFriendsList(null);
        newData.setLike(0);
        newData.setNewLike(0);
        newData.setNewFriendsList(null);
        friendsNumDao.saveData(newData);
    }

    /**
     * 判断是否调用了图灵盾接口，未调用接口的用户返回不为空
     */
    private boolean isCheckTnId(String uid) {
        try {
            Double score = clusterRedis.opsForZSet().score("zset:is:tn:check", uid);
            return score == null;
        } catch (Exception e) {
            logger.error("isCheckTnId error msg={}", e.getMessage(), e);
            return true;
        }
    }

    /**
     * 获取被禁止添加好友的用户设备id
     */
    private Integer getAddFriendsBlockTnid(String tnId) {
        try {
            Double score = clusterRedis.opsForZSet().score("block:add:friends:tnid", tnId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("isCheckTnId error msg={}", e.getMessage(), e);
            return 0;
        }
    }

    private Integer getFriendNumLimit() {
        try {
            Object friendNum = clusterRedis.opsForHash().get("hash:common_config_key", "friend_num");
            return null == friendNum ? FRIEND_NUM : Integer.parseInt(String.valueOf(friendNum));
        } catch (Exception e) {
            logger.error("get friend num limit. {}", e.getMessage(), e);
            return 0;
        }
    }

    public MergeListVO getMergeList(FriendListDTO req) {
        String uid = req.getUid();
        int listType = req.getListType();
        int page = req.getPage() > 0 ? req.getPage() : 1;
        if (StringUtils.isEmpty(uid) || page <= 0) {
            logger.error("params error.uid={} page={}", uid, page);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MergeListVO vo = new MergeListVO();
        vo.setListType(listType);
        List<String> aidList = new ArrayList<>();
        int start = (page - 1) * PAGE_SIZE;
        int num = 0;
        switch (listType) {
            case FriendConstant.LIST_TYPE_FRIENDS:
                num = friendsDao.getFriendCount(uid);
                if (num != 0) {
                    aidList = getFriendUserIdList(uid, start);
                }
                break;
            case FriendConstant.LIST_TYPE_FOLLOWING:
                num = followDao.getFollowingCount(uid);
                if (num != 0) {
                    aidList = getFollowingUserIdList(uid, start);
                }
                break;
            case FriendConstant.LIST_TYPE_FOLLOWERS:
                num = followDao.getFollowsCountByMonGo(uid);
                if (num != 0) {
                    aidList = getFollowersUserIdList(uid, start);
                    followDao.cleanFollowedUnread(uid);
                }
                break;
            default:
                break;
        }
        if (CollectionUtils.isEmpty(aidList)) {
            vo.setNum(num);
            vo.setNextUrl("");
            vo.setList(Collections.emptyList());
            return vo;
        }
        List<MergeListVO.UserInfo> list = new ArrayList<>();
        for (String aid : aidList) {
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (actorData == null) {
                continue;
            }
            MergeListVO.UserInfo userInfo = new MergeListVO.UserInfo();
            setUserBasicInfo(aid, actorData, userInfo);
            userInfo.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
            userInfo.setOs(actorData.getIntOs());
            userInfo.setInRoom(0);
            userInfo.setFollowStatus(getFollowStatus(uid, aid));
            if (actorData.getAccept_talk() != 0 && !StringUtils.isEmpty(roomPlayerRedis.getActorRoomStatus(aid))) {
                userInfo.setInRoom(1);
            }
            userInfo.setBadgeList(getBadgeList(aid));
            list.add(userInfo);
        }
        vo.setNextUrl(aidList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        vo.setNum(num);
        vo.setList(list);
        return vo;
    }

    /**
     * 获取关注状态
     */
    public int getFollowStatus(String uid, String aid) {
        // uid 关注了 aid
        boolean following = followDao.isFollowed(uid, aid);
        // aid 关注了 uid
        boolean followed = followDao.isFollowed(aid, uid);
        return doGetFollowStatus(following, followed);
    }

    public int doGetFollowStatus(boolean following, boolean followed) {
        if (following && !followed) {
            // 已关注，对方未关注
            return 1;
        } else if (!following && followed) {
            // 未关注，对方已关注
            return 2;
        } else if (following && followed) {
            // 相互都关注了
            return 3;
        } else {
            // 相互未关注
            return 0;
        }
    }

    /**
     * friends模块的好友列表，相比于me页面的信息更加详细
     */
    public FriendDetailVO getFriendDetailList(FriendListDTO req) {
        FriendDetailVO vo = new FriendDetailVO();
        int pageSize = 50;
        String uid = req.getUid();
        int page = req.getPage();
        if (StringUtils.isEmpty(uid) || page <= 0) {
            logger.error("params error.uid={} page={}", uid, page);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int start = (page - 1) * pageSize;
        List<FriendsData> dataList = friendsDao.findDataList(uid, start, pageSize);
        vo.setNextUrl(dataList.size() < pageSize ? "" : String.valueOf(page + 1));
        FriendsNumData friendsNumData = friendsNumDao.findData(uid);
        if (CollectionUtils.isEmpty(dataList)) {
            if (friendsNumData != null) {
                // 拉取列表之后清除表数据
                friendsNumDao.cleanData(uid, null);
            }
            vo.setList(new ArrayList<>());
            return vo;
        }
        int count = friendsListRedis.getFriendCount(uid);
        if (friendsNumData == null) {
            // 有好友数据，没有friend_num数据,因为follow在导入数据
            saveNewFriendsNumData(uid, count);
        }
        List<FriendDetailVO.FriendInfo> list = new ArrayList<>();
        for (FriendsData data : dataList) {
            String aid = Objects.equals(uid, data.getUidFirst()) ? data.getUidSecond() : data.getUidFirst();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (actorData == null) {
                continue;
            }
            FriendDetailVO.FriendInfo friendInfo = new FriendDetailVO.FriendInfo();
            setUserBasicInfo(aid, actorData, friendInfo);
            friendInfo.setOs(actorData.getIntOs());
            friendInfo.setLogin_status(getLoginStatus(actorData));
            friendInfo.setAccept_talk(actorData.getAccept_talk());
            friendInfo.setVchat_status(actorData.getVchat_status());
            friendInfo.setUlvl(userLevelDao.getUserLevel(aid));
            String roomId = roomPlayerRedis.getActorRoomStatus(uid);
            friendInfo.setRoomId(!StringUtils.isEmpty(roomId) ? !whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID) ? roomId : "" : "");
            friendInfo.setIdentify(friendInfo.getViplevel() > 0 ? 1 : 0);
            friendInfo.setSstatus(0);
            list.add(friendInfo);
        }
        vo.setList(list);
        // 拉取列表之后清除表数据
        friendsNumDao.cleanData(uid, count);
        vo.setNew_friends(friendApplyDao.getNewFriendsNum(uid));
        return vo;
    }

    private int getLoginStatus(ActorData actorData) {
        if (actorData.getLastLogin() == null || actorData.getLastLogin().getStatus() == null) {
            return FriendConstant.TALK_STATUS_OFFLINE;
        }
        return actorData.getLastLogin().getStatus();
    }

    /**
     * 获取好友的用户uid
     */
    private List<String> getFriendUserIdList(String uid, Integer start, Integer end) {
        List<String> aidList = new ArrayList<>();
        List<FriendsData> dataList = friendsDao.findDataList(uid, start, end);
        if (CollectionUtils.isEmpty(dataList)) {
            return aidList;
        }
        for (FriendsData data : dataList) {
            String aid = Objects.equals(uid, data.getUidFirst()) ? data.getUidSecond() : data.getUidFirst();
            aidList.add(aid);
        }
        return aidList;
    }

    private List<String> getFriendUserIdList(String uid, Integer start) {
        return getFriendUserIdList(uid, start, PAGE_SIZE);
    }

    /**
     * 获取关注的用户uid
     */
    private List<String> getFollowingUserIdList(String uid, Integer start) {
        List<FollowData> followingList = followDao.getFollowingList(uid, start, PAGE_SIZE);
        if (CollectionUtils.isEmpty(followingList)) {
            return Collections.emptyList();
        }
        return followingList.stream().map(FollowData::getAid).collect(Collectors.toList());
    }

    /**
     * 获取粉丝的用户uid
     */
    private List<String> getFollowersUserIdList(String uid, Integer start) {
        List<FollowData> followsList = followDao.getFollowsList(uid, start, PAGE_SIZE);
        if (CollectionUtils.isEmpty(followsList)) {
            return Collections.emptyList();
        }
        return followsList.stream().map(FollowData::getUid).collect(Collectors.toList());
    }

    @Cacheable(value = "getFriendSet", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public Set<String> getFriendSet(String uid) {
        return friendsListRedis.getFriendList(uid);
    }

    public List<String> getPageFriendList(String uid, int start, int end) {
        return getFriendUserIdList(uid, start, end);
    }

    public int getNewVisitorNum(String uid) {
        return (int) hvElasticsearchService.getVisitorCount(uid, true);
    }

    public int getFriendCount(String uid) {
        return friendsDao.getFriendCount(uid);
    }

    private void doMakeFriendRecordEvent(String uid, String fromUid, String toUid, int fromTime, int makeFriendType, int make_friend_scene) {
        MakeFriendRecordEvent event = new MakeFriendRecordEvent();
        event.setUid(uid);
        event.setFrom_uid(fromUid);
        event.setTo_uid(toUid);
        event.setFrom_time(fromTime);
        event.setMake_friend_type(makeFriendType);
        event.setMake_friend_scene(make_friend_scene);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }
}
