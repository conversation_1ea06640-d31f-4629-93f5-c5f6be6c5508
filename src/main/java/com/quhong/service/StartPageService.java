package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.LoginUserRecordEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.config.StartPageConfig;
import com.quhong.constant.BackStageConfigConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.BeforeLoginDTO;
import com.quhong.data.dto.PyBroadcastDTO;
import com.quhong.data.dto.QueryCountryByIpDTO;
import com.quhong.data.dto.StartPageDTO;
import com.quhong.data.vo.CommonLimitVO;
import com.quhong.data.vo.QueryCountryVO;
import com.quhong.data.vo.StartPageVO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.ISundryService;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.BackstageConfigDao;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.mysql.data.GreetUserData;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.PartyGirlRedis;
import com.quhong.redis.UserTaskRedis;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2022/7/21
 */
@Service
public class StartPageService {

    private static final Logger logger = LoggerFactory.getLogger(StartPageService.class);

    private static final StartPageVO.Mars mars = new StartPageVO.Mars();
    private static final StartPageVO.Theme theme = new StartPageVO.Theme();
    private static final StartPageVO.MarsV2 marsV2 = new StartPageVO.MarsV2();

    private static final String OSS_END_POINT = "https://oss-me-east-1.aliyuncs.com";
    private static final String OSS_REGION_ID = "me-east-1";
    private static final String POOL_ID = "ap-south-1:3db8f38a-165e-455e-bd14-bb38fe9d222b";
    private static final String AGORA_VPN_ID = "310f3037a1cf4b0d9b3fca76a1c8e117";
    private static final String AGORA_VPN_TOKEN = "310f3037a1cf4b0d9b3fca76a1c8e117";
    private static final String BIG_BOSS_URL = "https://static.youstar.live/top_up/";
    private static final String BIG_BOSS_URL_DEBUG = "https://test2.qmovies.tv/top_up/";
    private static final String SIGN_URL = "https://static.youstar.live/sign_up/";
    private static final String SIGN_URL_DEBUG = "https://test2.qmovies.tv/sign_up/";
    private static final String HEART_TASK_URL = "https://static.youstar.live/love_task/";
    private static final String HEART_TASK_URL_DEBUG = "https://test2.qmovies.tv/love_task/";
    private static String FEED_BACK_URL = ServerConfig.isProduct() ? "https://static.youstar.live/user_feedback/?origin=rateUs" : "https://test2.qmovies.tv/user_feedback/?origin=rateUs"; // ?&origin=settings
    // private static String FEED_BACK_URL = "https://docs.google.com/forms/d/e/1FAIpQLSe9dK66N4YwZtAnANYHsRDlrR1oCXvm0w3AApK5KEyq1kFvEA/viewform?usp=pp_url&entry.1344037265=%s";

    @Resource
    private PartyGirlRedis partyGirlRedis;
    @Resource
    private CommonConfig commonConfig;
    //    @Resource(name = DataRedisBean.MESSAGE_REDIS)
//    protected StringRedisTemplate messageRedis;
    @Resource
    private BackstageConfigDao backstageConfigDao;
    @Resource
    private UserRegisterDao userRegisterDao;
    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private ActorDao actorDao;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private RechargeRedis rechargeRedis;
    @Resource
    private FollowDao followDao;
    @Resource
    private FriendsNumDao friendsNumDao;
    @Resource
    private PhoneAccountDao phoneAccountDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private JoinCartonDao joinCartonDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private FriendApi friendApi;
    @Resource
    private AppThemeV2Dao appThemeV2Dao;
    @Resource
    private AppPageDao appPageDao;
    @Resource
    private StartPageService startPageService;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private LoginDataCountService loginDataCountService;
    @Resource
    private ISundryService iSundryService;
    @Resource
    private UserTaskRedis userTaskRedis;
    @Resource
    private DAUDao dAUDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private GreetUserService greetUserService;

    @PostConstruct
    private void init() {
        mars.setHost(new String[]{"*************"});
        mars.setPort(new Integer[]{8108});
        mars.setHeartbeatTime(3600);

        theme.setName("");
        theme.setUrl("");
        theme.setMd5("");
        theme.setShow(0);

        marsV2.setPort(new Integer[]{9001});
        marsV2.setHeartbeatTime(30);
        marsV2.setMarsSwitch(1);

        if (ServerConfig.isProduct()) {
            marsV2.setHost(new String[]{"api.qmovies.tv"});
            marsV2.setHostV2(new String[]{"im.qmovies.tv"});
        } else {
            marsV2.setHost(new String[]{"**************"});
            marsV2.setHostV2(new String[]{"**************", "*************"});
        }
    }

    public StartPageVO getStartPage(StartPageDTO startPageDTO) {
        StartPageVO vo = new StartPageVO();
        String uid = startPageDTO.getUid();
        int slang = startPageDTO.getSlang() == 1 ? 1 : 2;
        // 安卓不是合法包名清除token
        if (startPageDTO.invalidPkgName()) {
            logger.error("app pkg name invalid uid={} pkgName={}", uid, startPageDTO.getApp_package_name());
            if (!StringUtils.isEmpty(uid)) {
                basePlayerRedis.removeToken(uid);
            }
            throw new CommonException(HttpCode.SESSION_INVALID);
        }
        // 获取后台配置
        StartPageConfig config = getStartPageConfig();
        JSONObject switchConfig = commonConfig.getSwitchConfig();
        boolean isDebug = !ServerConfig.isProduct();
        vo.setTheme(theme);
        setStartPageInfo(vo, slang, uid);
        vo.setUrl(slang == 1 ? vo.getUrl() : vo.getUrlAr());
        vo.setMars(mars);
        vo.setIsPtg(partyGirlRedis.isPartyGirlByRedis(uid) ? 1 : 0);
        vo.setMarsV2(marsV2);
        vo.setPtgUrl("");
        setSwitchConfig(uid, vo, switchConfig);
        vo.setOssEndPoint(OSS_END_POINT);
        vo.setOssRegionId(OSS_REGION_ID);
        // 大小5m
        vo.setPicSize(5);
        // s3上传资源身份池id
        vo.setPoolId(POOL_ID);
        vo.setBossUrl(getBossUrl(uid, slang, isDebug));
        vo.setNewUser(!StringUtils.isEmpty(uid) ? (ActorUtils.isNewRegisterActor(uid) ? 1 : 0) : 0);
        vo.setRamadan(0);
        vo.setRamadanV2(config.getRamadan());
        setGuideInfo(vo, config);
        vo.setPtgNoticeTime(300);
        vo.setEmojiSwitch(1);
        vo.setHttpSendRoomMsg(1);
        // 进房间调接口 1:py  2: java
        vo.setRoomApiVer(2);
        vo.setAgoraVpnId(AGORA_VPN_ID);
        vo.setAgoraVpnToken(AGORA_VPN_TOKEN);
        vo.setAgoraVpnChainId(isDebug ? 288 : 289);
        vo.setHomeTab(config.getHomeTab());
        vo.setResLoadType(config.getResLoadType());
        vo.setGuideTriggerTime(config.getGuideTriggerTime());
        vo.setGuideTriggerCount(config.getGuideTriggerCount());
        vo.setCalibrateTime(System.currentTimeMillis());
        vo.setSeniorPlayer(isSeniorPlayer(uid));
        vo.setAnniversaryV5(config.getAnniversaryV5());
        vo.setWorldCup(config.getWorldCup());
        vo.setAppTheme(switchConfig.getIntValue("app_theme"));
        fillThemeZip(vo, startPageDTO.getOs());
        vo.setNewVisitorOrFans(getNewVisitorOrFans(uid));
        vo.setVideoSize(10);
        setWearRide(vo, uid, startPageDTO.getOs());
        if (startPageDTO.getOs() == ClientOS.ANDROID && startPageDTO.getVersioncode() >= 545) {
            QueryCountryByIpDTO qDTO = new QueryCountryByIpDTO();
            qDTO.setIp(startPageDTO.getIp());
            qDTO.setOutId(startPageDTO.getRequestId());
            QueryCountryVO queryCountryVO = iSundryService.queryCountryByIp(qDTO).getData();
            if (null != queryCountryVO && "DZ".equalsIgnoreCase(queryCountryVO.getCode())) {
                vo.setAutoWrite(1);
                logger.info("set client auto write. uid={} ip={} countryCode={}", uid, startPageDTO.getIp(), queryCountryVO.getCode());
            }
        }
        // ios 审核被拒
        if (startPageDTO.getOs() == 0) {
            setSignInfo(vo, slang, isDebug);
        }
        int showGlobalOutRoom = 1;
        int showGlobalInRoom = 1;
        if (!StringUtils.isEmpty(uid)) {
            // 同步上报数数据
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    flushTgaApiData(uid, startPageDTO.getTa_device_id());
                    updateVersionCodeSlang(startPageDTO);
                    commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", "", "", CommonMqTaskConstant.DAILY_LOGIN, 1));
                }
            });

            showGlobalOutRoom = (int) actorConfigDao.getLongUserConfig(uid, ActorConfigDao.SHOW_GLOBAL_OUT_ROOM, 1);
            showGlobalInRoom = (int) actorConfigDao.getLongUserConfig(uid, ActorConfigDao.SHOW_GLOBAL_IN_ROOM, 1);
            String lastChar = uid.substring(uid.length() - 1);
            JSONArray jsonArray = switchConfig.getJSONArray("ta_log_char");
//            logger.info("uid:{} jsonArray:{} lastChar:{}",uid,jsonArray,lastChar);
            if (jsonArray.contains(lastChar)) {
                vo.setTaLogSwitch(1);
            } else {
                vo.setTaLogSwitch(0);
            }

            // vo.setTaLogAppPageSwitch(1);
            int logPageDay = switchConfig.getIntValue("ta_log_page_sw");
            if (logPageDay > 0) {
                vo.setTaLogAppPageSwitch(ActorUtils.isNewRegisterActor
                        (uid, logPageDay) ? 1 : 0);
            } else {
                vo.setTaLogAppPageSwitch(0);
            }
            vo.setNewTaskReward(fillNewTaskReward(startPageDTO));
            vo.setFeedBackUrl(FEED_BACK_URL);
            // vo.setFeedBackUrl(String.format(FEED_BACK_URL, actorData.getRid()));
            if (ActorUtils.isNewRegisterActor(uid, 1)) {
                vo.setIsNewOrBackUser(1);
            } else if (isBackUser(uid)) {
                vo.setIsNewOrBackUser(2);
            } else {
                vo.setIsNewOrBackUser(0);
            }

        } else {
            vo.setTaLogSwitch(1);
        }
        vo.setShowGlobalOutRoom(showGlobalOutRoom);
        vo.setShowGlobalInRoom(showGlobalInRoom);
        vo.setNewTaskSwitch(switchConfig.getIntValue("new_task_switch"));
        vo.setCollectShuMeiMsg(1);

        // 临时代码
//        if (DateHelper.getNowSeconds() < 1691596674 && "5c55326a66dc63003ebd7e85".equals(uid)) {
//            vo.setAutoWrite(1);
//        }
        return vo;
    }

    private int fillNewTaskReward(StartPageDTO dto) {
        String uid = dto.getUid();
        if (StringUtils.isEmpty(uid)) {
            return 0;
        }
        int newTaskReward = AppVersionUtils.versionCheck(8621, dto) ? userTaskRedis.getWebUserHasRewardCount(uid) : userTaskRedis.getUserHasRewardCount(uid);
        return newTaskReward > 0 ? 1 : 2;
    }


    private Integer getNewVisitorOrFans(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return 0;
        }
        ApiResult<Integer> result = friendApi.getNewVisitorNum(uid);
        int newVisitorNum = result.getData() != null ? result.getData() : 0;
        return followDao.getNewFollowsCount(uid) + newVisitorNum;
    }


    private void fillThemeZip(StartPageVO vo, int os) {
        AppThemeV2Data appThemeV2Data = appThemeV2Dao.findValidData();
        int currentTime = DateHelper.getNowSeconds();
        if (appThemeV2Data != null && currentTime >= appThemeV2Data.getStartTime() && currentTime <= appThemeV2Data.getEndTime()) {
            vo.setThemeZipUrlV3(os == 1 ? appThemeV2Data.getIosZipUrl() : appThemeV2Data.getAndroidZipUrl());
            vo.setThemeZipMd5V3(os == 1 ? appThemeV2Data.getIosZipMd5() : appThemeV2Data.getAndroidZipMd5());
            vo.setThemeIosXZipUrlV3(appThemeV2Data.getIosXZipUrl());
            vo.setThemeIosXZipMd5V3(appThemeV2Data.getIosXZipMd5());
        }
    }

    private void setWearRide(StartPageVO vo, String uid, int os) {
        if (!StringUtils.isEmpty(uid)) {
            JoinCartonData joinCartonData = joinCartonDao.findData(uid, 1);
            if (joinCartonData != null) {
                JoinSourceData joinSourceData = joinSourceDao.getSourceData(joinCartonData.getJoin_carton_id());
                StartPageVO.WearRide wearRide = new StartPageVO.WearRide();
                wearRide.setId(joinCartonData.getJoin_carton_id());
                wearRide.setUrl(os == 1 ? joinSourceData.getIos_url() : joinSourceData.getSource_url());
                wearRide.setMd5(os == 1 ? joinSourceData.getIos_md5() : joinSourceData.getSource_md5());
                wearRide.setSource_type(joinSourceData.getSource_type());
                vo.setWearRide(wearRide);
            }
        }
    }

    private String getBossUrl(String uid, Integer slang, Boolean isDebug) {
        String bigBossUrl = isDebug ? BIG_BOSS_URL_DEBUG : BIG_BOSS_URL;
        return getUrlByUidAndSlang(bigBossUrl, uid, slang);
    }

    private String getUrlByUidAndSlang(String url, String uid, Integer slang) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        if (!StringUtils.isEmpty(uid)) {
            urlBuilder.queryParam("uid", uid);
        }
        urlBuilder.queryParam("slang", slang);
        return urlBuilder.build(false).encode().toUriString();
    }

    private void flushTgaApiData(String uid, String ta_device_id) {
        // 发送mq消息
        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.LOGIN, uid, System.currentTimeMillis()));
        // 记录数数事件
        ActorData actor = actorDao.getActorDataFromCache(uid);
        if (actor == null) {
            logger.error("user data is null. uid={}", uid);
            return;
        }
        String channel = loginDataCountService.getChannelByPkg(actor.getAppPackageName(), actor.getChannel());
        LoginUserRecordEvent event = new LoginUserRecordEvent();
        event.setUid(uid);
        event.setBeans(actor.getBeans());
        event.setFb_gender(actor.getFb_gender());
        event.setAge(actor.getAge());
        event.setLogin_type(actor.getLoginType());
        event.setAccept_talk(actor.getAccept_talk());
        event.setIp(actor.getIp());
        event.setTn_id(actor.getTn_id());
        event.setChannel(channel);
        event.setIs_face(actor.getIsFace());
        event.setApp_package_name(actor.getAppPackageName());
        event.setVersion_code(actor.getVersion_code());
        event.setVersion_name(actor.getVersion_name());
        event.setHeart_got(actor.getHeartGot());
        // event.setIdfa(actor.getIdfa());
        event.setIdfa("");
        event.setOs(String.valueOf(actor.getIntOs()));
        event.setRid(actor.getRid());
        event.setRecharge(rechargeRedis.isRechargeUser(uid) ? 1 : 0);
        event.setGeneration_time(new ObjectId(uid).getTimestamp());
        event.setTa_device_id(ta_device_id);
        event.setFriends_number(friendsNumDao.getUserFriendsNum(uid));
        event.setFollowing_number(followDao.getFollowingCount(uid));
        event.setFollowers_number(followDao.getFollowsCountByMonGo(uid));
        event.setPhone(actor.getLoginType() == 4 ? phoneAccountDao.getUserPhone(uid) : "");
        event.setPhone("");
        // event.setGaid(actor.getAndroid_id());
        event.setGaid("");
        event.setRecharge_total(actorPayExternalDao.getUserRechargeMoney(uid));
        event.setBirthday(actor.getBirthday());
        event.setDynamic_channel(actor.getPromotion_id());
        if (StringUtils.hasLength(actor.getShuMeiId())) {
            event.setShu_mei_id(actor.getShuMeiId());
        }
        event.setLucky_game_switch_status(homeBannerService.getBCGameSwitch(actor));
        event.setGolden_rid(actor.getAlphaRid());
        event.setUse_system_avatar_id(BaseInitData.SYS_NEW_MALE_HEAD_LIST.contains(actor.getHead()) || BaseInitData.SYS_NEW_FEMALE_HEAD_LIST.contains(actor.getHead()) ? actor.getHead() : "");
        eventReport.userSet(new EventDTO(event));
        dAUDao.updateDAU(actor, DAUDao.DAU_SOURCE_START);
    }

/*    public void broadcastMessage(String name, Object data) {
        try {
            messageRedis.convertAndSend(getChannelKey(name), JSON.toJSONString(new PyBroadcastDTO(data)));
        } catch (Exception e) {
            logger.error("push task to channel error, name={} data={} {}", name, JSON.toJSONString(data), e.getMessage());
        }
    }*/

    private void setSignInfo(StartPageVO vo, Integer slang, Boolean isDebug) {
        StartPageVO.SignInfo signInfo = new StartPageVO.SignInfo();
        String signUrl = isDebug ? SIGN_URL_DEBUG : SIGN_URL;
        String heartTaskUrl = isDebug ? HEART_TASK_URL_DEBUG : HEART_TASK_URL;
        signInfo.setSignStatus(0);
        signInfo.setSignUrl(getUrlByUidAndSlang(signUrl, "", slang));
        vo.setSignInfo(signInfo);
        vo.setHeartTaskUrl(getUrlByUidAndSlang(heartTaskUrl, "", slang));
    }

    private void setGuideInfo(StartPageVO vo, StartPageConfig config) {
        StartPageVO.GuideInfo guideInfo = new StartPageVO.GuideInfo();
        guideInfo.setGuideTimes(config.getGuideTimes());
        guideInfo.setGuideSeconds(config.getGuideSeconds());
        vo.setGuideInfo(guideInfo);
    }

    private void setSwitchConfig(String uid, StartPageVO vo, JSONObject switchConfig) {
        vo.setLive(switchConfig != null && switchConfig.getIntValue("live") == 1 ? 1 : 0);
        vo.setWatchSwitch(switchConfig != null ? switchConfig.getIntValue("watch_switch") : 0);
        vo.setMusicSwitch(switchConfig != null ? switchConfig.getIntValue("music_switch") : 0);
        vo.setTopicSwitch(switchConfig != null ? switchConfig.getIntValue("topic_switch") : 0);
        vo.setReportApi(switchConfig != null ? switchConfig.getIntValue("report_api") : 0);
        vo.setBigBoss(switchConfig != null ? switchConfig.getIntValue("big_boss") : 0);
        vo.setAgoraVpnSwitch(switchConfig != null ? switchConfig.getIntValue("agora_vpn_switch") : 0);
        // 控制部分用户可以使用
        if (switchConfig != null && switchConfig.getIntValue("agora_vpn_control") != 0) {
            if (switchConfig.getJSONArray("agora_vpn_user").contains(uid)) {
                vo.setAgoraVpnSwitch(1);
            } else {
                vo.setAgoraVpnSwitch(0);
            }
        }
        vo.setVisitorSwitch(switchConfig != null ? switchConfig.getIntValue("visitor_switch") : 0);
    }

    @Cacheable(value = "getAppPageListData", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<AppPageData> getAppPageListData() {
        return appPageDao.findAppPageListData();
    }

    private void setStartPageInfo(StartPageVO vo, int slang, String uid) {
        List<AppPageData> appPageData = startPageService.getAppPageListData();
        List<StartPageVO.AppPageInfo> appPageInfoList = new ArrayList<>();
        int maxTime = 0;
        StringBuilder versionTime = new StringBuilder();
        int currentTime = DateHelper.getNowSeconds();
        boolean hasUid = !StringUtils.isEmpty(uid);
        ActorData actorData = null;
        int bcGameSwitch = 0;
        if (hasUid) {
            actorData = actorDao.getActorDataFromCache(uid);
            bcGameSwitch = homeBannerService.getBCGameSwitch(actorData);
        }
        for (AppPageData data : appPageData) {
            StartPageVO.AppPageInfo appPageInfo = new StartPageVO.AppPageInfo();

            int startTime = data.getStartTime();
            int endTime = data.getEndTime();

            if (startTime > 0 && endTime > 0 && (currentTime < startTime || currentTime > endTime)) {
                if (currentTime > endTime) {
                    Update update = new Update();
                    update.set("status", 0);
                    appPageDao.updateData(data, update);
                }
                continue;
            }

            if (hasUid) {
                // 过滤条件判断
                if (!checkFilterValid(actorData, data, bcGameSwitch)) {
                    continue;
                }
            }
            appPageInfo.setUrl(slang == SLangType.ARABIC ? data.getUrlAr() : data.getUrl());
            appPageInfo.setIphoneXUrl(slang == SLangType.ARABIC ? data.getIphoneXUrlAr() : data.getIphoneXUrlEn());
            appPageInfo.setLink(StringUtils.isEmpty(data.getLink()) ? "" : getUrlByUidAndSlang(data.getLink(), uid, slang));
            appPageInfo.setActionType(data.getActionType());
            appPageInfo.setTargetId(StringUtils.isEmpty(data.getTargetId()) ? "" : data.getTargetId());
            appPageInfo.setStartTime(startTime);
            appPageInfo.setEndTime(endTime);
            appPageInfoList.add(appPageInfo);
            maxTime = Math.max(data.getMtime(), maxTime);
            versionTime.append(data.getMtime());
        }
        vo.setStartList(appPageInfoList);
        vo.setStartVersion(versionTime.toString());
        vo.setValidTime(maxTime > 0 ? maxTime : null);
    }

    private StartPageConfig getStartPageConfig() {
        String strConfigData = backstageConfigDao.getConfigData(BackStageConfigConstant.START_PAGE_CONFIG);
        return JSONObject.parseObject(strConfigData, StartPageConfig.class);
    }

    private Integer isSeniorPlayer(String uid) {
        int isSeniorPlayer = 0;
        if (!StringUtils.isEmpty(uid)) {
            UserRegisterInfoData register = userRegisterDao.getRegister(uid);
            if (register != null) {
                String strModels = backstageConfigDao.getConfigData(BackStageConfigConstant.HIGN_END_MODELS);
                JSONObject jsonObject = JSONObject.parseObject(strModels);
                Map<String, Object> tnExtraMap = register.getTn_extra();
                String model = !CollectionUtils.isEmpty(tnExtraMap) ? (String) tnExtraMap.get("model") : "";
                if (jsonObject.getJSONArray("HighEndModels").contains(model)) {
                    isSeniorPlayer = 1;
                }
            }
        }
        return isSeniorPlayer;
    }

    private String getChannelKey(String name) {
        return String.format("event:%s", name);
    }


    private void updateVersionCodeSlang(StartPageDTO dto) {
        UpdateUserInfoData update = new UpdateUserInfoData();
        if (!StringUtils.isEmpty(dto.getApp_package_name())) {
            update.setAppPackageName(dto.getApp_package_name());
        }
        if (dto.getVersioncode() > 0) {
            update.setVersioncode(dto.getVersioncode());
        }
        if (dto.getSlang() > 0) {
            update.setSlang(dto.getSlang());
        }
        if (!ObjectUtils.isEmpty(dto.getVname())) {
            update.setVname(dto.getVname());
        }

        if (!ObjectUtils.isEmpty(dto.getIp())) {
            update.setIp(dto.getIp());
            // 获取ipCountry
            QueryCountryByIpDTO qDTO = new QueryCountryByIpDTO();
            qDTO.setIp(dto.getIp());
            qDTO.setOutId(dto.getRequestId());
            QueryCountryVO queryCountryVO = iSundryService.queryCountryByIp(qDTO).getData();
            if (!ObjectUtils.isEmpty(queryCountryVO)) {
                String ipCodeCountry = String.format("%s_%s", queryCountryVO.getCode(), queryCountryVO.getCountry());
                update.setIpCodeCountry(ipCodeCountry);
            }
        }
        actorDao.updateVersionCodeSlang(dto.getUid(), update);

        GreetUserData updateGreetData = new GreetUserData();
        updateGreetData.setUid(dto.getUid());
        updateGreetData.setOnlineTime(DateHelper.getNowSeconds());
        greetUserService.updateGreetUser(updateGreetData);
    }

    public CommonLimitVO checkDeviceLogin(BeforeLoginDTO dto) {
        CommonLimitVO vo = new CommonLimitVO();
        vo.setUseDeviceId(1);
//        vo.setUseDeviceId(0);
        return vo;
    }

    public boolean isBackUser(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        int lastLogoutTime = 0;
        if (actorData.getLastLogin() != null) {
            if (actorData.getLastLogin().getLogoutTime() != null) {
                lastLogoutTime = Math.toIntExact(actorData.getLastLogin().getLogoutTime());
            }
        }
        if (lastLogoutTime == 0) {
            logger.info("uid:{} not find lastLogoutTime", actorData.getUid());
            return false;
        }
        int now = DateHelper.getNowSeconds();
        if (lastLogoutTime + TimeUnit.DAYS.toSeconds(30) < now) {
            return true;
        } else {
            return false;
        }
    }

    public void dayActiveReport(StartPageDTO startPageDTO) {
        String uid = startPageDTO.getUid();
        if (ServerConfig.isNotProduct() && StringUtils.isEmpty(uid)) {
            return;
        }
        if (!startPageService.dayActiveReportCache(uid)) {
            Integer dauSourceType = startPageDTO.getDauSourceType();
            if (dauSourceType != null) {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                dAUDao.updateNewDAUEvent(actorData, dauSourceType);
                startPageService.putDayActiveReportCache(uid, true);
            }
        }
    }

    @Cacheable(value = "dayActiveReportCache", key = "#p0",
            cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public boolean dayActiveReportCache(String uid) {
        return false;
    }

    @CachePut(value = "dayActiveReportCache", key = "#p0 ",
            cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public boolean putDayActiveReportCache(String uid, boolean value) {
        return value;
    }


    private boolean checkFilterValid(ActorData actorData, AppPageData data, int bcGameSwitch) {

        try {
            switch (data.getFilterType()) {
                case HomeBannerService.FILTER_TYPE_SEX:
                    if (!StringUtils.isEmpty(data.getFilterItem()) && !data.getFilterItem().equals(String.valueOf(actorData.getFb_gender()))) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_PAY:
                    double userRecharge = actorPayExternalDao.getUserRechargeMoney(actorData.getUid()).doubleValue();
                    String[] split = data.getFilterItem().split("-");
                    double minMoney = Double.parseDouble(split[0]);
                    double maxMoney = Double.parseDouble(split[1]);
                    if (userRecharge < minMoney || userRecharge > maxMoney) {
                        return false;
                    }
                    break;

                case HomeBannerService.FILTER_TYPE_REGISTER:
                    String[] splitDay = data.getFilterItem().split("-");
                    int startDay = Integer.parseInt(splitDay[0]);
                    int endDay = Integer.parseInt(splitDay[1]);
                    int regDay = ActorUtils.getRegDays(actorData.getUid());
                    if (regDay < startDay || regDay > endDay) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_USER:
                    String[] uidArray = data.getFilterItem().split(",");
                    List<String> uidList = Arrays.asList(uidArray);
                    if (!uidList.contains(actorData.getUid())) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_LOSS:
                    if (backUserStateRedis.isReg(actorData.getUid()) ||
                            backUserStateRedis.isBackUser(actorData, true) == 0) {
                        //  不是回归用户
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_BC_GAME:
                    int filterStatus = Integer.parseInt(data.getFilterItem().trim());
                    if ((filterStatus == 0 && bcGameSwitch > 0) || (filterStatus == 1 && bcGameSwitch <= 0)) {
                        return false;
                    }
                    break;
            }
        } catch (Exception e) {
            logger.error("checkBannerValid error:{}", e.getMessage(), e);
        }
        return true;
    }
}
