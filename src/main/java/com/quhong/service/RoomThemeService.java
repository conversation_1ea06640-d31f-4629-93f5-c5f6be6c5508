package com.quhong.service;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.SwitchRoomThemeEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RoomRoleData;
import com.quhong.data.SudGameSimpleInfo;
import com.quhong.data.bo.RoomMicThemeChangeData;
import com.quhong.data.dto.RoomThemeDTO;
import com.quhong.data.vo.MicThemeVO;
import com.quhong.data.vo.OptMicThemeVO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.ConquerActivity;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RoomTheme;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.RoomMicUserObject;
import com.quhong.msg.room.RoomMicThemeChangeMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.*;
import com.quhong.room.RoomWebSender;
import com.quhong.room.api.ThirdPartApiAdapter;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.cache.RoomListCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.mic.RoomMicService;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.PageVO;
import com.quhong.vo.RoomMicListVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.quhong.video.VideoService.SWITCH_OPEN;


@Component
public class RoomThemeService {

    private static final Logger logger = LoggerFactory.getLogger(RoomThemeService.class);
    public static final List<Integer> UN_CHANGE_BACKGROUND_MIC_THEME = Arrays.asList(16, 17, 18, 19);

    public static final String ABOVE_QUEEN_EN = "Above Queen";
    public static final String ABOVE_QUEEN_AR = "فوق الملكة";

    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomMicThemeRedis micThemeRedis;
    @Resource
    private CommonDao commonDao;
    @Resource
    private RoomMicThemeLogDao roomMicThemeLogDao;
    @Resource
    private ConquerRedis conquerRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomThemeSwitchRecordDao roomThemeSwitchRecordDao;
    @Resource
    private UploadBackgroundDao uploadBackgroundDao;
    @Resource
    private ConquerActivityDao conquerActivityDao;
    @Resource
    private RoomLevelService roomLevelService;
    @Resource
    private RoomMicService roomMicService;
    @Autowired
    private RoomActorCache actorCache;
    @Resource
    private ThirdPartApiAdapter thirdPartApi;
    @Resource
    private CheatGiftRedis cheatGiftRedis;
    @Resource
    private RoomMicThemeDao roomMicThemeDao;
    @Resource
    private RoomListCache roomListCache;
    @Resource
    private RoomKickService roomKickService;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private TruthOrDareV2Redis truthOrDareV2Redis;
    @Resource
    private SudGameRedis sudGameRedis;

    public PageVO<MicThemeVO> getMicThemeList(RoomThemeDTO dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(dto.getRoomId());
        if (roomData == null) {
            logger.error("can not find room data. roomId={}", dto.getRoomId());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        // 直播房不支持房间主题
        if (RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode()) {
            logger.info("live room no right opt to mic theme list. roomId={} uid={}", dto.getRoomId(), dto.getUid());
            return new PageVO<>();
        }
        List<MicThemeVO> list = new ArrayList<>();
        // 获取当前房间的麦位主题
        int micTheme = roomData.getMic_theme() == 0 ? RoomConstant.MIC_THEME_DEFAULT_ID : roomData.getMic_theme();
        // 房间等级
        int stepRoomLevel = roomLevelService.getStepLevel(dto.getRoomId());
        List<RoomMicThemeData> roomMicThemeDataList = roomMicThemeDao.selectAllMicTheme();
        for (RoomMicThemeData themeData : roomMicThemeDataList) {
            if (themeData.getStatus() <= 0){
                continue;
            }

            // 校验用户版本是否支持房间主题
            if (themeData.getVersion() != 0 && !AppVersionUtils.versionCheck(themeData.getVersion(), dto)) {
                continue;
            }

            // 只展示当前房间等级的麦位主题
            if (themeData.getRoomStepLevel() > 0 && stepRoomLevel != themeData.getRoomStepLevel()) {
                continue;
            }

            MicThemeVO micThemeVO = new MicThemeVO();
            BeanUtils.copyProperties(themeData, micThemeVO);
            micThemeVO.setThemeId(themeData.getId());
            micThemeVO.setNewVersion(getNewVersion(dto.getUid(), themeData.getId(), themeData.getCtime()));
            micThemeVO.setIsCurTheme(themeData.getId() == micTheme ? 1 : 0);
            micThemeVO.setShowTxtEn(themeData.getVip() > 4 && actorData.getFb_gender() == 2 ? ABOVE_QUEEN_EN : themeData.getShowTxtEn());
            micThemeVO.setShowTxtAr(themeData.getVip() > 4 && actorData.getFb_gender() == 2 ? ABOVE_QUEEN_AR : themeData.getShowTxtAr());
            micThemeVO.setVip(themeData.getVip());
            micThemeVO.setMicSize(themeData.getMicSize());
            list.add(micThemeVO);
        }
        return new PageVO<>(list);
    }

    public OptMicThemeVO optMicTheme(RoomThemeDTO dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        String roomId = dto.getRoomId();
        String uid = dto.getUid();
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(dto.getRoomId());
        if (roomData == null) {
            logger.error("can not find room data. roomId={}", dto.getRoomId());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        RoomMicThemeData themeData = roomMicThemeDao.selectMicThemeById(dto.getThemeId());
        // ThemeData themeData = themeConfig.getMap().get(dto.getThemeId());
        if (themeData == null) {
            logger.error("can not find theme data. roomId={} uid={} micTheme={}", dto.getRoomId(), dto.getUid(), dto.getThemeId());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        // 校验用户版本是否支持房间主题
        if (themeData.getVersion() != 0 && !AppVersionUtils.versionCheck(themeData.getVersion(), dto)) {
            logger.info("actor need up app version. roomId={} uid={} micTheme={}", dto.getRoomId(), dto.getUid(), dto.getThemeId());
            throw new CommonException(RoomHttpCode.PLEASE_UPGRADE_VERSION);
        }
        // 直播房不支持房间主题
        if (RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode()) {
            logger.info("comp room no right opt mic theme. roomId={} uid={}", dto.getRoomId(), dto.getUid());
            throw new CommonException(RoomHttpCode.MIC_THEME_OPT_NOT_COMP);
        }
        // 校验视屏房切换主题需要关掉视频开关
        if (roomData.getVideo_switch() == SWITCH_OPEN) {
            logger.info("video opt mic theme need close video first. roomId={} uid={}", dto.getRoomId(), dto.getUid());
            throw new CommonException(RoomHttpCode.PLEASE_CLOSE_VIDEO);
        }
        RoomRoleData roomRoleData = roomMemberDao.getRoleData(dto.getRoomId(), dto.getUid());
        // 校验权限：只有房主和管理员才能进行mic主题切换操作
        if (!dto.isNoCheckRole() && !roomRoleData.isAdmin()) {
            logger.info("actor is not host or vice host. can not opt mic theme. roomId={} uid={} micTheme={} role={}", dto.getRoomId(), dto.getUid(), dto.getThemeId(), roomRoleData.getRole());
            throw new CommonException(RoomHttpCode.AUTH_ERROR);
        }
        // 被征服的房间不能替换房间主题
        int level = getSnowmanRoomLevel(dto.getRoomId());
        if (level != 0) {
            logger.error("The room has been conquered, unable to change the room background temporarily. uid={} roomId={} level={}", dto.getUid(), dto.getRoomId(), level);
            throw new CommonException(RoomHttpCode.ROOM_HAS_BEEN_CONQUERED);
        }
        // 校验房主vip权限是否足够
        String ownerUid = RoomUtils.getRoomHostId(dto.getRoomId());
        if (vipInfoDao.getIntVipLevel(ownerUid) < themeData.getVip()) {
            logger.info("opt mic theme no right to opt. roomId={} uid={} micTheme={}", dto.getRoomId(), dto.getUid(), dto.getThemeId());
            if (RoomUtils.isHomeowner(dto.getUid(), dto.getRoomId())) {
                // 房主
                if (themeData.getVip() <= 4){
                    throw new CommonException(RoomHttpCode.MIC_THEME_OPT_AUTH_3, themeData.getVip());
                }
                throw new CommonException(actorData.getFb_gender() == 2 ? RoomHttpCode.MIC_THEME_OPT_AUTH_QUEEN : RoomHttpCode.MIC_THEME_OPT_AUTH_ERROR);
            } else {
                // 副房主
                throw new CommonException(RoomHttpCode.ROOM_OWNER_AUTH_ERROR);
            }
        }
        // 校验在玩真心话大冒险, 不能切换主题
        String truthOrDareGameId = truthOrDareV2Redis.getGameIdByRoomId(roomId);
        if (!StringUtils.isEmpty(truthOrDareGameId)){
            logger.info("truthOrDareGame play. roomId={} uid={} truthOrDareGameId={}", dto.getRoomId(), dto.getUid(), truthOrDareGameId);
            throw new CommonException(RoomHttpCode.PLEASE_CLOSE_TRUTH_DARE);
        }

        SudGameSimpleInfo sudGameInfo = sudGameRedis.getSudGameSimpleInfo(roomId);
        if (sudGameInfo != null && sudGameInfo.getGameType() == SudGameConstant.WOISSPY_GAME) {
            logger.info("who is spy game play. roomId={} uid={} gameId={}", dto.getRoomId(), dto.getUid(), sudGameInfo.getGameId());
            throw new CommonException(RoomHttpCode.PLAY_WHO_IS_SPY_NOT_CHANGE_THEME);
        }

        OptMicThemeVO vo = new OptMicThemeVO();
        vo.setMicTheme(dto.getThemeId());
        int roomMicTheme = roomData.getMic_theme() == 0 ? 1 : roomData.getMic_theme();
        if (dto.getThemeId() != roomMicTheme || themeData.getTid() != roomData.getTheme()) {
            RoomMicThemeChangeData micThemeChangeData = changeMicTheme(roomData, dto.getUid(), dto.getThemeId(), dto.isPushAll());
            vo.setTopic(micThemeChangeData.getBackUrl());
            // 设置麦位变化
            vo.setRoomMicListData(micThemeChangeData.getRoomMicListData());
            // 切换房间主题数数埋点
            setRoomThemeEvent(dto.getUid(), roomData, dto.getThemeId());

            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    // 踢人出房间
                    sendKickRoomOnMicThemeChange(roomId, themeData, uid);
                }
            });
        }
        return vo;
    }

    /**
     * 更新指定版本麦位主题，增加踢人消息
     */
    private void sendKickRoomOnMicThemeChange(String roomId, RoomMicThemeData themeData, String fromUid){
        try {

            int themeVersion = themeData.getVersion();
            if(themeVersion <= 0){
                return;
            }

            Set<String> inRoomUserSet = roomListCache.getData(roomId);
            if(CollectionUtils.isEmpty(inRoomUserSet)){
                return;
            }

            for (String aid: inRoomUserSet) {
                ActorData actorData = actorDao.getActorData(aid);
                if (!AppVersionUtils.versionCheck(themeVersion, actorData.getVersion_code())){
                    logger.info("sendKickRoomOnMicThemeChange aid:{} themeVersion:{}, Version_code:{}", aid, themeVersion, actorData.getVersion_code());
                    roomKickRedis.setKickRecord(roomId, fromUid, aid);
                    roomKickService.sendKickFromRoom(roomId, fromUid, aid);
                }
            }
        }catch (Exception e){
            logger.error("sendKickRoomOnMicThemeChange error={}", e.getMessage(), e);
        }



    }


    /**
     * 上新标志
     */
    private int getNewVersion(String uid, int micTheme, int ctime) {
        if (micTheme == RoomConstant.MIC_THEME_DEFAULT_ID) {
            return 0;
        }
        if (DateHelper.getNowSeconds() - ctime > (int) TimeUnit.DAYS.toSeconds(7)) {
            return 0;
        }
        boolean hasSeenNewTheme = micThemeRedis.hasSeenNewTheme(micTheme, uid);
        if (!hasSeenNewTheme) {
            micThemeRedis.addHasSeenNewThemeUid(micTheme, uid);
        }
        return hasSeenNewTheme ? 0 : 1;
    }

    private void setRoomThemeEvent(String uid, MongoRoomData roomData, int micTheme) {
        int roomMicTheme = roomData.getMic_theme() == 0 ? 1 : roomData.getMic_theme();
        String roomId = roomData.getRid();
        RoomThemeSwitchRecordData lastData = roomThemeSwitchRecordDao.findLastData(roomId);
        SwitchRoomThemeEvent event = new SwitchRoomThemeEvent();
        // 开启主题
        event.setUid(uid);
        event.setRoom_id(roomId);
        event.setRoom_theme(micTheme);
        event.setRoom_theme_use_duration(0);
        event.setRoom_theme_use_type(1);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
        // 关闭主题
        event.setRoom_theme(roomMicTheme);
        event.setRoom_theme_use_duration(lastData != null ? DateHelper.getNowSeconds() - lastData.getOnTime() : 0);
        event.setRoom_theme_use_type(2);
        eventReport.track(new EventDTO(event));
        // 保存房间主题切换记录
        saveRoomThemeSwitchRecord(uid, roomId, micTheme);
    }

    private void saveRoomThemeSwitchRecord(String uid, String roomId, int micTheme) {
        RoomThemeSwitchRecordData data = new RoomThemeSwitchRecordData();
        data.setUid(uid);
        data.setRoomId(roomId);
        data.setRoomTheme(micTheme);
        data.setOnTime(DateHelper.getNowSeconds());
        roomThemeSwitchRecordDao.insert(data);
    }

    public RoomMicThemeChangeData changeMicTheme(MongoRoomData roomData, String uid, int micTheme, boolean pushUidFlag) {
        //更新房间主题图、麦位主题
        String roomId = roomData.getRid();
        RoomMicThemeData themeData = roomMicThemeDao.selectMicThemeById(micTheme);
        int themeBackType = RoomConstant.THEME_BG_TYPE_MIC;
        if (micTheme == RoomConstant.MIC_THEME_DEFAULT_ID) {
            themeBackType = RoomConstant.THEME_BG_TYPE_DEFAULT;
        }
        String backUrl = getThemeBgUrl(roomData, themeData);
        roomDao.updateMicTheme(roomId, micTheme, themeBackType);
        //发送mars消息
        if(roomData.getRoomMode() == RoomConstant.LIVE_ROOM_MODE && micTheme == RoomConstant.MIC_THEME_DEFAULT_ID){
            micTheme = RoomConstant.MIC_THEME_LIVE_DEFAULT_ID;
        }
        RoomMicThemeChangeMsg changeMsg = new RoomMicThemeChangeMsg();
        changeMsg.setThemeId(micTheme);
        changeMsg.setBackUrl(backUrl == null ? "" : backUrl);
        int showUserOpt = 0;
        if (!StringUtils.isEmpty(uid)) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            changeMsg.setUid(uid);
            changeMsg.setUsername(actorData.getName());
            changeMsg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
            showUserOpt = 1;
        } else {
            changeMsg.setUid("");
            changeMsg.setUsername("");
            changeMsg.setHead("");
        }
        changeMsg.setUserOpt(showUserOpt);
        RoomMicListData roomMicList = roomMicService.setMicMode(roomId, uid, themeData.getMicSize(), 0);
        Map<String, CheatGiftRedis.CheatGiftData> map = cheatGiftRedis.fillCheatGift(roomMicList.getList());
        List<RoomMicInfoObject> infoList = new ArrayList<>();
        for (RoomMicData micData : roomMicList.getList()) {
            RoomMicInfoObject micObj = new RoomMicInfoObject();
            micObj.setIndex(micData.getPosition());
            micObj.setMute(micData.getMute());
            micObj.setStatus(micData.getStatus());
            String micUid = micData.getUid();
            if (!StringUtils.isEmpty(micUid)) {
                RoomMicUserObject userObject = createUser(roomId, micUid, true, roomData);
                // 以下仅作业务处理
                CheatGiftRedis.CheatGiftData cheatGiftData = map.get(micUid);
                userObject.setVoice_type(cheatGiftData.getVoiceType());
                if (!StringUtils.isEmpty(cheatGiftData.getPrankMicFrame())) {
                    userObject.setMic_frame(cheatGiftData.getPrankMicFrame());
                }
                userObject.setUpMicTime(micData.getUpMicTime());
                userObject.setThirdPartId(micData.getThirdPartId());
                userObject.setGameRunning(0);
                micObj.setUser(userObject);
            }
            infoList.add(micObj);
        }
        changeMsg.setList(infoList);
        changeMsg.setVersion(roomMicList.getVersion());
        roomWebSender.sendRoomWebMsg(roomId, pushUidFlag ? "" : uid, changeMsg, true);
        reportMicThemeChange(roomData, uid, micTheme);
        logger.info("room mic theme chang ok. roomId={} uid={} micTheme={} backUrl={}", roomId, uid, micTheme, backUrl);

        RoomMicListVo roomMicListData = new RoomMicListVo();
        roomMicListData.setList(infoList);
        roomMicListData.setRoomId(roomId);
        roomMicListData.setVersion(roomMicList.getVersion());
        roomMicListData.setAllMute(roomMicList.isAllMute() ? 1 : 0);

        RoomMicThemeChangeData micThemeChangeData = new RoomMicThemeChangeData();
        micThemeChangeData.setBackUrl(backUrl);
        micThemeChangeData.setRoomMicListData(roomMicListData);
        return micThemeChangeData;
    }

    private RoomMicUserObject createUser(String roomId, String uid, boolean forceUpdate, MongoRoomData roomData) {
        RoomActorDetailData detailData = actorCache.getData(roomId, uid, forceUpdate);
        RoomMicUserObject userObject = new RoomMicUserObject();
        userObject.setAid(detailData.getAid());
        userObject.setName(detailData.getName());
        userObject.setHead(detailData.getHead());
        userObject.setMic_frame(detailData.getMicFrame());
        userObject.setRipple_url(detailData.getRippleUrl());
        userObject.setVip_level(detailData.getVipLevel());
        userObject.setVipMedal(detailData.getVipMedal());
        userObject.setRole(detailData.getRole());
        userObject.setIdentify(detailData.getIdentify());
        userObject.setViceHost(detailData.getViceHost());
        userObject.setStreamId(thirdPartApi.generateActorStreamId(roomData.getOwnerRid(), detailData.getRid(), detailData.getAid(), roomData.getRtc_type()));
        return userObject;
    }

    @Cacheable(value = "getSnowmanRoomLevel", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public int getSnowmanRoomLevel(String roomId) {
        return conquerRedis.getRoomConquerLevel(roomId);
    }

    private void reportMicThemeChange(MongoRoomData roomData, String uid, int micTheme) {
        RoomMicThemeLogData themeLogData = new RoomMicThemeLogData();
        themeLogData.setRoomId(roomData.getRid());
        if (StringUtils.isEmpty(uid)) {
            themeLogData.setOpUid("");
        } else {
            themeLogData.setOpUid(uid);
        }
        int beforeTheme = roomData.getMic_theme() == 0 ? RoomConstant.MIC_THEME_DEFAULT_ID : roomData.getMic_theme();
        themeLogData.setBeforeTheme(beforeTheme);
        themeLogData.setAfterTheme(micTheme);
        themeLogData.setCtime(DateHelper.getNowSeconds());
        roomMicThemeLogDao.saveOne(themeLogData);
    }

    public String getThemeBgUrl(MongoRoomData roomData, RoomMicThemeData themeData) {
        // 征服活动背景
        int tid = themeData.getTid();
        int level = getSnowmanRoomLevel(roomData.getRid());
        if (level > 0) {
            logger.info("change mic theme, room has conquered. roomId={} level={}", roomData.getRid(), level);
            ConquerActivity conquerActivity = conquerActivityDao.findOne();
            if (conquerActivity != null && !CollectionUtils.isEmpty(conquerActivity.getConfigList())) {
                Map<Integer, String> conquerThemeMap = conquerActivity.getConfigList().stream().collect(Collectors.toMap(ConquerActivity.ConfigDetail::getLevel, ConquerActivity.ConfigDetail::getTheme));
                String themeBgUrl = conquerThemeMap.get(level);
                if (!StringUtils.isEmpty(themeBgUrl)) {
                    return themeBgUrl;
                }
            }
        }
        if (themeData.getThemeType() > 0) {
            RoomTheme roomTheme = commonDao.findOne(new Query(Criteria.where("tid").is(tid)), RoomTheme.class);
            return roomTheme != null ? roomTheme.getBgurl() : "";
        }
        return getRoomLastTheme(roomData);
    }

    private String getRoomLastTheme(MongoRoomData roomData) {
        int theme = roomData.getTheme();
        if (theme >= 1000) {
            UploadBackgroundData bgData = uploadBackgroundDao.selectOne(theme);
            if (null != bgData) {
                return bgData.getBgUrl();
            }
        } else if (theme > 0) {
            RoomTheme roomTheme = commonDao.findOne(new Query(Criteria.where("tid").is(theme)), RoomTheme.class);
            if (null != roomTheme) {
                return ImageUrlGenerator.generateRoomTopicUrl(roomTheme.getBgurl());
            }
        }
        return "";
    }

    public boolean canUseMicTheme(int vipLevel, int micTheme) {
        if (micTheme == RoomConstant.MIC_THEME_DEFAULT_ID) {
            return true;
        }
        Map<Integer, RoomMicThemeData> roomMicThemeDataMap = roomMicThemeDao.selectAllMicTheme().stream().collect(Collectors.toMap(RoomMicThemeData::getId, Function.identity()));
        RoomMicThemeData themeData = roomMicThemeDataMap.get(micTheme);
        if (themeData == null) {
            return false;
        }
        return vipLevel >= themeData.getVip();
    }
}
