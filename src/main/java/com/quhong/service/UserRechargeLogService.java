package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.ServUserRechargeEvent;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.UserLevelTaskData;
import com.quhong.enums.UserLevelConstant;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.data.ActorPayExternalData;
import com.quhong.mysql.data.RechargeDailyInfoData;
import com.quhong.redis.SandboxUserRedis;
import com.quhong.service.redis.RechargeRedis;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class UserRechargeLogService {
    private static final Logger logger = LoggerFactory.getLogger(UserRechargeLogService.class);
    public static Map<String, Integer> USER_RECHARGE_MAP = new HashMap<>();
    public static Map<Integer, String> USER_RECHARGE_TYPE_TITLE_MAP = new HashMap<>();
    public static List<Integer> REPORT_ANALYTICS_TYPE = Arrays.asList(0, 1, 4, 6, 8); // 0: 其他正常支付   非0: admin支付 [4: 'Money', 1: "Credit", 6: 'Deposit']
    static {
        // 3、5、7没有数据产生
        USER_RECHARGE_MAP.put("google", 1);
        USER_RECHARGE_MAP.put("apple", 2);
        USER_RECHARGE_MAP.put("tap", 3);
        USER_RECHARGE_MAP.put("admin", 4);
        USER_RECHARGE_MAP.put("jolly", 5);
        USER_RECHARGE_MAP.put("huawei", 6);
        USER_RECHARGE_MAP.put("OPayCharge", 7);
        USER_RECHARGE_MAP.put("payCenter", 8);

        USER_RECHARGE_TYPE_TITLE_MAP.put(1, "googlePayCharge");
        USER_RECHARGE_TYPE_TITLE_MAP.put(2, "ApplePayCharge");
        USER_RECHARGE_TYPE_TITLE_MAP.put(3, "TapPayCharge");
        USER_RECHARGE_TYPE_TITLE_MAP.put(4, "admin charge");
        USER_RECHARGE_TYPE_TITLE_MAP.put(5, "JollychicCharge");
        USER_RECHARGE_TYPE_TITLE_MAP.put(6, "HuaWeiPayCharge");
        USER_RECHARGE_TYPE_TITLE_MAP.put(7, "OPayCharge");
        USER_RECHARGE_TYPE_TITLE_MAP.put(8, "中台支付");
    }

    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private RechargeRedis rechargeRedis;
    @Resource
    private SandboxUserRedis sandboxUserRedis;
    @Autowired(required = false)
    private EventReport eventReport;

    private int getUserIdentify(double rechargeMoney){
        if(rechargeMoney >= 1000){
            return 3;
        }else if (rechargeMoney >= 100){
            return 2;
        }else {
            return 1;
        }
    }

    private Date getRechargeDate(int rechargeTime){

        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date(rechargeTime * 1000L);
            Calendar cld = Calendar.getInstance(TimeZone.getTimeZone("GMT+3:00"));
            cld.setTime(date);
            cld.set(Calendar.HOUR_OF_DAY, 24);
            cld.set(Calendar.MINUTE, 0);
            cld.set(Calendar.SECOND, 0);
            return simpleDateFormat.parse(simpleDateFormat.format(cld.getTime()));
        }catch (Exception e){
            logger.error("getRechargeDate error: {}", e.getMessage(), e);
        }
        return new Date();
    }

    public void handleUserRecharge(RechargeInfo rechargeInfo){

        try{

            String uid  = rechargeInfo.getUid();
            int rechargeType  = rechargeInfo.getRechargeType();
            int rechargeDiamond  = rechargeInfo.getRechargeDiamond();
            int rechargeTime  = rechargeInfo.getRechargeTime();
            Double rechargeMoney  = rechargeInfo.getRechargeMoney();
            String rechargeItem  = rechargeInfo.getRechargeItem();
            String subType  = rechargeInfo.getSubType();
            int currentTime = DateHelper.getNowSeconds();
            int adminSubType = 0;
            if(rechargeType != 1){
                logger.info("not honor recharge return rechargeInfo:{}", rechargeInfo);
                return;
            }

            // 用户等级充值相关业务
            userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.RECHARGE, rechargeDiamond));

            // 充值金额统计
            Integer payType = USER_RECHARGE_MAP.get(rechargeItem);  // 支付类型
            if(payType == null){
                logger.error("not find payType, rechargeInfo:{}", rechargeInfo);
                return;
            }

            if(sandboxUserRedis.androidSandbox(uid)){
                logger.info("androidSandbox User recharge:{}", rechargeInfo);
                return;
            }

            if(sandboxUserRedis.iosSandbox(uid)){
                logger.info("iosSandbox User recharge:{}", rechargeInfo);
                return;
            }

            if(payType == 4){
                adminSubType = Integer.parseInt(subType);
            }

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            ObjectId objectId = new ObjectId(uid);
            RechargeDailyInfoData rechargeDailyInfoData = new RechargeDailyInfoData();
            rechargeDailyInfoData.setRechargeDate(getRechargeDate(rechargeTime));
            rechargeDailyInfoData.setUid(uid);
            rechargeDailyInfoData.setPayType(payType);
            rechargeDailyInfoData.setRechargeMoney(BigDecimal.valueOf(rechargeMoney));
            rechargeDailyInfoData.setRechargeDiamond(rechargeDiamond);
            rechargeDailyInfoData.setRechargeTime(rechargeTime);
            rechargeDailyInfoData.setRegisterTime(objectId.getTimestamp());
            rechargeDailyInfoData.setsType(adminSubType);
            rechargeDailyInfoData.setOs(actorData.getIntOs());
            rechargeDailyInfoData.setSubType(subType);
            rechargeDailyInfoDao.insert(rechargeDailyInfoData);

            if (!rechargeRedis.isRechargeUser(uid)) {
                Map<String, Object> properties = new HashMap<>();
                properties.put("recharge", 1);
                eventReport.userSet(new EventDTO(uid, "", properties));
            }
            rechargeRedis.addRechargeUser(uid);

            if(REPORT_ANALYTICS_TYPE.contains(adminSubType)){

                ActorPayExternalData actorPayExternalData = actorPayExternalDao.selectOne(uid);

                if(actorPayExternalData == null){
                    actorPayExternalData = new ActorPayExternalData();
                    actorPayExternalData.setUid(uid);
                    actorPayExternalData.setRechargeMoney(BigDecimal.valueOf(rechargeMoney));
                    actorPayExternalData.setRechargeChannel(payType);
                    actorPayExternalData.setUserIdentify(getUserIdentify(rechargeMoney));
                    actorPayExternalData.setLastTime(rechargeTime);
                    actorPayExternalData.setMtime(currentTime);
                    actorPayExternalData.setCtime(currentTime);
                    actorPayExternalDao.insertData(actorPayExternalData);
                }else {
                    double afterRechargeMoney = actorPayExternalData.getRechargeMoney().doubleValue() + rechargeMoney;
                    actorPayExternalData.setRechargeMoney(BigDecimal.valueOf(afterRechargeMoney));
                    actorPayExternalData.setUserIdentify(getUserIdentify(afterRechargeMoney));
                    actorPayExternalData.setRechargeChannel(payType);
                    actorPayExternalData.setLastTime(rechargeTime);
                    actorPayExternalData.setMtime(currentTime);
                    actorPayExternalDao.updateData(actorPayExternalData);
                }

                String payTitle = USER_RECHARGE_TYPE_TITLE_MAP.getOrDefault(payType, "");
                ServUserRechargeEvent servUserRechargeEvent = new ServUserRechargeEvent();
                servUserRechargeEvent.setUid(uid);
                servUserRechargeEvent.setPay_type(payType);
                servUserRechargeEvent.setPay_title(payTitle);
                servUserRechargeEvent.setRecharge_money(rechargeMoney);
                servUserRechargeEvent.setRecharge_diamond(rechargeDiamond);
                servUserRechargeEvent.setRecharge_time(rechargeTime);
                servUserRechargeEvent.setS_type(adminSubType);
                servUserRechargeEvent.setSub_type(subType);
                servUserRechargeEvent.setCtime(currentTime);
                logger.info("servUserRechargeEvent data: {}", JSON.toJSONString(servUserRechargeEvent));
                eventReport.track(new EventDTO(servUserRechargeEvent));

            }


        }catch (Exception e){
            logger.error("handleUserRecharge error rechargeInfo:{}  e:{}", rechargeInfo, e.getMessage(), e);
            monitorSender.info("ustar_java_exception", "支付数据统计异常", "rechargeInfo: " + rechargeInfo.toString() + "msg：" + e.getMessage());
        }





    }
}
