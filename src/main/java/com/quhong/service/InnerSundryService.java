package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.maxmind.geoip2.model.CityResponse;
import com.quhong.country.CountryQuery;
import com.quhong.data.CountryData;
import com.quhong.data.dto.IsLimitUserDTO;
import com.quhong.data.dto.QueryCountryByIpDTO;
import com.quhong.data.vo.IsNotLimitUseVO;
import com.quhong.data.vo.QueryCountryVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SundryHttpCode;
import com.quhong.mongo.dao.CommonConfig;
import com.quhong.mysql.dao.TnWhiteConfigDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
public class InnerSundryService {

    private static final Logger logger = LoggerFactory.getLogger(InnerSundryService.class);

    private static final Set<String> HIDE_COUNTRY_SET = new HashSet<>(Arrays.asList("CN", "HK", "TW", "MO"));

    private static final Set<String> HIDE_LANGUAGE_SET = new HashSet<>(Arrays.asList("ZH"));

    private static final Set<Integer> STEP_LOGIN_TYPE = new HashSet<>(Arrays.asList(2, 6));

    private static final String HUA_WEI_CHANNEL = "huawei";

    @Autowired
    private CountryQuery countryQuery;
    @Autowired
    private TnWhiteConfigDao tnWhiteConfigDao;
    @Resource
    private CommonConfig commonConfig;

    public IsNotLimitUseVO isLimit(IsLimitUserDTO dto) {
        String uid = dto.getUid();
        String tnId = dto.getTnId();
        String lang = dto.getLang();
        String ipCountry = dto.getIpCountry();
        String ip = dto.getIp();
        String channel = dto.getChannel();

        int os = dto.getOs();
        int verCode = dto.getVersioncode();
        Integer loginType = dto.getLoginType();

        if (verCode > 0 && os == 0 && HUA_WEI_CHANNEL.equals(channel)) {
            /**
             * db.common_config.update({"name" : "switch_config"}, {$set:{"sconfig.huawei_check_switch":1,"sconfig.huawei_check_version":0}})
             */
            int checkSwitch = commonConfig.getSwitchConfigValue("huawei_check_switch", 0);
            int checkVersion = commonConfig.getSwitchConfigValue("huawei_check_version", 0);
            if (checkSwitch > 0 && verCode >= checkVersion) {
                // 华为渠道
                logger.info("huawei check pass dto={}", dto);
                return new IsNotLimitUseVO(true,true);
            }
        }
        // 图灵盾id白名单
        if (!StringUtils.isEmpty(tnId)) {
            if (tnWhiteConfigDao.getAllTnId().contains(tnId)) {
                logger.info("tnId={} in white list dto={}", tnId, dto);
                return new IsNotLimitUseVO(true,true);
            }
        }
        // uid白名单
        if (!StringUtils.isEmpty(uid)) {
            if (tnWhiteConfigDao.getAllTnId().contains(uid)) {
                logger.info("uid={} in white list dto={}", uid, dto);
                return new IsNotLimitUseVO(true,true);
            }
        }
        List<String> targetCountryList = new ArrayList<>();
        List<String> targetLanguageList = new ArrayList<>();
        if (!StringUtils.isEmpty(lang)) {
            targetCountryList.add(getCountryByLang(lang));
            targetLanguageList.add(getLanguageByLang(lang));
        }
        if (!StringUtils.isEmpty(ipCountry)) {
            targetCountryList.add(ipCountry);
        } else {
            // 获取ip所在国家
            if (!StringUtils.isEmpty(ip)) {
                CountryData countryData = countryQuery.find(ip);
                if (null != countryData && null != countryData.getCode()) {
                    targetCountryList.add(countryData.getCode());
                }
            }

        }

        for (String item : targetCountryList) {
            if (HIDE_COUNTRY_SET.contains(item.toUpperCase())) {
                logger.info("uid={} item={} limit", uid, item);
                // 限制国家包含
                return new IsNotLimitUseVO(false,false);
            }
        }

        for (String item : targetLanguageList) {
            if (HIDE_LANGUAGE_SET.contains(item.toUpperCase())) {
                logger.info("uid={} item={} limit", uid, item);
                // 限制语言包含
                return new IsNotLimitUseVO(false,false);
            }
        }
        return new IsNotLimitUseVO(false,true);
    }

    private String getCountryByLang(String lang) {
        String[] split = lang.split("_"); // android 格式
        if (split.length >= 2) {
            return split[split.length - 1];
        }
        split = lang.split("-"); // ios 格式
        if (split.length >= 2) {
            return split[split.length - 1];
        }
        return lang;
    }

    private String getLanguageByLang(String lang) {
        String[] split = lang.split("_"); // android 格式
        if (split.length >= 2) {
            return split[0];
        }
        split = lang.split("-"); // ios 格式
        if (split.length >= 2) {
            return split[0];
        }
        return lang;
    }

    public ApiResult<QueryCountryVO> queryDataByIp(QueryCountryByIpDTO dto) {
        String ip = dto.getIp();
        CountryData countryData = countryQuery.find(ip);
        if (null != countryData) {
            QueryCountryVO vo = new QueryCountryVO();
            BeanUtils.copyProperties(countryData, vo);

            logger.info("QueryCountryVO={}", JSONObject.toJSONString(vo));
            return ApiResult.getOk(vo);
        }
        logger.info("dto={} not find in db", dto);
        return ApiResult.getError(SundryHttpCode.IP_NOT_FIND);
    }

    public ApiResult<QueryCountryVO> queryCityDataByIp(QueryCountryByIpDTO dto) {
        String ip = dto.getIp();
        CityResponse cityResponse = countryQuery.findCityResponse(ip);
        if (null != cityResponse) {
            QueryCountryVO vo = new QueryCountryVO();
            vo.setIp(ip);
            vo.setCode(cityResponse.getCountry().getIsoCode());
            vo.setTimeZone(cityResponse.getLocation().getTimeZone());
            return ApiResult.getOk(vo);
        }
        logger.info("dto={} not find in db", dto);
        return ApiResult.getError(SundryHttpCode.IP_NOT_FIND);
    }

}
