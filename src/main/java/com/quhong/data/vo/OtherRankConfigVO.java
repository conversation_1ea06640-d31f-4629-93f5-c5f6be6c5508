package com.quhong.data.vo;


import java.util.List;

public class OtherRankConfigVO {
    // 配置类活动信息
    private Integer isFirstEnterActivity; // 是否是第一次进入活动
    protected Integer startTime;
    protected Integer endTime;
    protected Integer score;
    protected String currentDate;
    protected List<DailyData> dailyDataList; // 日榜数据

    public static class DailyData {
        private String key;
        private String strDate;
        private int status; // 0未开始 1正在进行 2已结束
        private String top1UserHead; // top1用户头像
        private List<OtherRankingListVO> travelRankingList; // 当天日榜
        private OtherMyRankVO myTravelRank; // 我的排名
        private Integer prizePoolBeans; // 奖池钻石数 (奖池瓜分活动)

        public DailyData() {
        }

        public DailyData(String key, String strDate, int status, String top1UserHead, List<OtherRankingListVO> travelRankingList, OtherMyRankVO myTravelRank) {
            this.key = key;
            this.strDate = strDate;
            this.status = status;
            this.top1UserHead = top1UserHead;
            this.travelRankingList = travelRankingList;
            this.myTravelRank = myTravelRank;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getStrDate() {
            return strDate;
        }

        public void setStrDate(String strDate) {
            this.strDate = strDate;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getTop1UserHead() {
            return top1UserHead;
        }

        public void setTop1UserHead(String top1UserHead) {
            this.top1UserHead = top1UserHead;
        }

        public List<OtherRankingListVO> getTravelRankingList() {
            return travelRankingList;
        }

        public void setTravelRankingList(List<OtherRankingListVO> travelRankingList) {
            this.travelRankingList = travelRankingList;
        }

        public OtherMyRankVO getMyTravelRank() {
            return myTravelRank;
        }

        public void setMyTravelRank(OtherMyRankVO myTravelRank) {
            this.myTravelRank = myTravelRank;
        }

        public Integer getPrizePoolBeans() {
            return prizePoolBeans;
        }

        public void setPrizePoolBeans(Integer prizePoolBeans) {
            this.prizePoolBeans = prizePoolBeans;
        }
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getCurrentDate() {
        return currentDate;
    }

    public void setCurrentDate(String currentDate) {
        this.currentDate = currentDate;
    }

    public List<DailyData> getDailyDataList() {
        return dailyDataList;
    }

    public void setDailyDataList(List<DailyData> dailyDataList) {
        this.dailyDataList = dailyDataList;
    }

    public Integer getIsFirstEnterActivity() {
        return isFirstEnterActivity;
    }

    public void setIsFirstEnterActivity(Integer isFirstEnterActivity) {
        this.isFirstEnterActivity = isFirstEnterActivity;
    }
}
