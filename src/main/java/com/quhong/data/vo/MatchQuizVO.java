package com.quhong.data.vo;


import java.util.List;

public class MatchQuizVO extends OtherRankConfigVO {
    private String uid;
    private String name;
    private String head;

    private Integer totalNum; // 总答题人数
    private Integer newNum; // 新用户答题人数
    private Integer matchNum; // 匹配心有灵犀人数
    private Integer isGeneratedQuiz; // 是否已生成问卷 1已生成 0未生成


    private QuestionResultVO myQuestionResult; // 我的答题结果
    private List<QuestionResultVO> questionMatchList; // 心有灵犀等级用户列表
    private List<QuestionResultVO> questionRecordList; // 答题用户记录


    public static class QuestionResultVO {
        private String aid; // 答题者 ,配置接口为出题者
        private String aidName; // 答题者昵称
        private String aidHead; // 答题者头像
        private int score; // 正确的题目数量
        private int level; // 分数评级
        private int isNew; // 是否是新用户 1是 0否

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getAidName() {
            return aidName;
        }

        public void setAidName(String aidName) {
            this.aidName = aidName;
        }

        public String getAidHead() {
            return aidHead;
        }

        public void setAidHead(String aidHead) {
            this.aidHead = aidHead;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public int getIsNew() {
            return isNew;
        }

        public void setIsNew(int isNew) {
            this.isNew = isNew;
        }
    }

    public static class HistoryRedisData {
        private int ctime;
        private String aid; // 答题者
        private int score; // 答对题目数量
        private int level; // 分数评级

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public QuestionResultVO getMyQuestionResult() {
        return myQuestionResult;
    }

    public void setMyQuestionResult(QuestionResultVO myQuestionResult) {
        this.myQuestionResult = myQuestionResult;
    }

    public List<QuestionResultVO> getQuestionMatchList() {
        return questionMatchList;
    }

    public void setQuestionMatchList(List<QuestionResultVO> questionMatchList) {
        this.questionMatchList = questionMatchList;
    }

    public List<QuestionResultVO> getQuestionRecordList() {
        return questionRecordList;
    }

    public void setQuestionRecordList(List<QuestionResultVO> questionRecordList) {
        this.questionRecordList = questionRecordList;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getNewNum() {
        return newNum;
    }

    public void setNewNum(Integer newNum) {
        this.newNum = newNum;
    }

    public Integer getMatchNum() {
        return matchNum;
    }

    public void setMatchNum(Integer matchNum) {
        this.matchNum = matchNum;
    }

    public Integer getIsGeneratedQuiz() {
        return isGeneratedQuiz;
    }

    public void setIsGeneratedQuiz(Integer isGeneratedQuiz) {
        this.isGeneratedQuiz = isGeneratedQuiz;
    }
}
