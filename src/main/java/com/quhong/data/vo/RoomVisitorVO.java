package com.quhong.data.vo;

import com.quhong.data.RidData;
import com.quhong.msg.obj.TakeMicDailyReward;
import com.quhong.vo.NewDeviceReward;

import java.util.List;

/**
 * 房间用户信息
 */
public class RoomVisitorVO {
    private RidData ridData; // 新版本rid对象
    private String uid; // 用户uid
    private String myName; // 用户名字
    private String myHead; // 用户头像
    private int rid;
    private int vip; // vip等级
    private String vipMedal; // vip勋章
    private int isFollowRoom; // 是否关注房间
    private int isFollowHost; // 是否关注房主
    private int viceHost; // 是否是副房主
    private int isMember; // 是否房间会员
    private int admin; // 是否是房间管理员 1是 2否
    private int role; // 房间角色 0 房主 1 管理员 2 观众 3 会员
    private int bubbleId; // 气泡id
    private int isRookie; // 是否新用户
    private int firstDayRookie; // 是否当天新用户
    private int identify; // vip信息
    private int talkForbidTime; // 公屏聊天禁止时间
    private int micForbidTime; // 禁止上麦时间
    private int giftBag; // 是否领取了背包礼物
    private int taskReceive; // 新手任务是否有红点
    private int reportLevel; // 举报等级
    private int userLevel; // 用户等级
    private int isHost; // 是否主持人
    private List<String> badgeList; // 用户佩戴的徽章小图标列表
    private int hasNoviceTask; // 是否有房间新手任务
    // 进场动画
    private String smallIcon; // 进场动画预览图
    private int joinCartoonId = 0; // 进入房间动画id
    private int sourceType = 0; // 资源类型
    private String sourceUrl; // 资源zipUrl
    private String sourceMd5; // 资源sourceMd5
    private int joinCartoonType = 0; // 进场动画类型 1vip 2荣誉 3商店购买 4svip 佩戴
    private long cartoonTimes = 0; // 进场动画持续时间 ms
    private String streamId; // 流id
    // 进场通知
    private String entryEffectUrl; // 资源地址
    private String entryEffectUrlAr; // 资源地址
    // 麦位申请信息
    private int applyCount; // 申请数量
    private String firstHead; // 最新一个申请用户头像，房主、副房主和管理员才有值
    // 是否展示上麦及文字引导
    private int micChatGuide;  // 申请数量
    private int coinBalance; // 金币余额
    private int isShowMicGuide; // 是否展示上麦引导 1展示 0不展示
    private NewDeviceReward deviceReward;  // 7天新用户奖励deviceReward
    private int takeMicDailyCount = 1; // 每日上麦引导次数
    private TakeMicDailyReward takeMicDailyReward;  // 日常任务的上麦奖励

    public int getTakeMicDailyCount() {
        return takeMicDailyCount;
    }

    public void setTakeMicDailyCount(int takeMicDailyCount) {
        this.takeMicDailyCount = takeMicDailyCount;
    }

    public TakeMicDailyReward getTakeMicDailyReward() {
        return takeMicDailyReward;
    }

    public void setTakeMicDailyReward(TakeMicDailyReward takeMicDailyReward) {
        this.takeMicDailyReward = takeMicDailyReward;
    }

    public RidData getRidData() {
        return ridData;
    }

    public void setRidData(RidData ridData) {
        this.ridData = ridData;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getMyName() {
        return myName;
    }

    public void setMyName(String myName) {
        this.myName = myName;
    }

    public String getMyHead() {
        return myHead;
    }

    public void setMyHead(String myHead) {
        this.myHead = myHead;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public String getVipMedal() {
        return vipMedal;
    }

    public void setVipMedal(String vipMedal) {
        this.vipMedal = vipMedal;
    }

    public int getIsFollowRoom() {
        return isFollowRoom;
    }

    public void setIsFollowRoom(int isFollowRoom) {
        this.isFollowRoom = isFollowRoom;
    }

    public int getIsFollowHost() {
        return isFollowHost;
    }

    public void setIsFollowHost(int isFollowHost) {
        this.isFollowHost = isFollowHost;
    }

    public int getViceHost() {
        return viceHost;
    }

    public void setViceHost(int viceHost) {
        this.viceHost = viceHost;
    }

    public int getIsMember() {
        return isMember;
    }

    public void setIsMember(int isMember) {
        this.isMember = isMember;
    }

    public int getAdmin() {
        return admin;
    }

    public void setAdmin(int admin) {
        this.admin = admin;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getBubbleId() {
        return bubbleId;
    }

    public void setBubbleId(int bubbleId) {
        this.bubbleId = bubbleId;
    }

    public int getIsRookie() {
        return isRookie;
    }

    public void setIsRookie(int isRookie) {
        this.isRookie = isRookie;
    }

    public int getFirstDayRookie() {
        return firstDayRookie;
    }

    public void setFirstDayRookie(int firstDayRookie) {
        this.firstDayRookie = firstDayRookie;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public int getTalkForbidTime() {
        return talkForbidTime;
    }

    public void setTalkForbidTime(int talkForbidTime) {
        this.talkForbidTime = talkForbidTime;
    }

    public int getMicForbidTime() {
        return micForbidTime;
    }

    public void setMicForbidTime(int micForbidTime) {
        this.micForbidTime = micForbidTime;
    }

    public int getGiftBag() {
        return giftBag;
    }

    public void setGiftBag(int giftBag) {
        this.giftBag = giftBag;
    }

    public int getTaskReceive() {
        return taskReceive;
    }

    public void setTaskReceive(int taskReceive) {
        this.taskReceive = taskReceive;
    }

    public int getReportLevel() {
        return reportLevel;
    }

    public void setReportLevel(int reportLevel) {
        this.reportLevel = reportLevel;
    }

    public int getUserLevel() {
        return userLevel;
    }

    public void setUserLevel(int userLevel) {
        this.userLevel = userLevel;
    }

    public int getIsHost() {
        return isHost;
    }

    public void setIsHost(int isHost) {
        this.isHost = isHost;
    }

    public List<String> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<String> badgeList) {
        this.badgeList = badgeList;
    }

    public void setHasNoviceTask(int hasNoviceTask) {
        this.hasNoviceTask = hasNoviceTask;
    }

    public int getHasNoviceTask() {
        return hasNoviceTask;
    }

    public String getSmallIcon() {
        return smallIcon;
    }

    public void setSmallIcon(String smallIcon) {
        this.smallIcon = smallIcon;
    }

    public int getJoinCartoonId() {
        return joinCartoonId;
    }

    public void setJoinCartoonId(int joinCartoonId) {
        this.joinCartoonId = joinCartoonId;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public String getSourceMd5() {
        return sourceMd5;
    }

    public void setSourceMd5(String sourceMd5) {
        this.sourceMd5 = sourceMd5;
    }

    public int getJoinCartoonType() {
        return joinCartoonType;
    }

    public void setJoinCartoonType(int joinCartoonType) {
        this.joinCartoonType = joinCartoonType;
    }

    public long getCartoonTimes() {
        return cartoonTimes;
    }

    public void setCartoonTimes(long cartoonTimes) {
        this.cartoonTimes = cartoonTimes;
    }

    public String getStreamId() {
        return streamId;
    }

    public void setStreamId(String streamId) {
        this.streamId = streamId;
    }

    public int getApplyCount() {
        return applyCount;
    }

    public void setApplyCount(int applyCount) {
        this.applyCount = applyCount;
    }

    public String getFirstHead() {
        return firstHead;
    }

    public void setFirstHead(String firstHead) {
        this.firstHead = firstHead;
    }

    public String getEntryEffectUrl() {
        return entryEffectUrl;
    }

    public void setEntryEffectUrl(String entryEffectUrl) {
        this.entryEffectUrl = entryEffectUrl;
    }

    public String getEntryEffectUrlAr() {
        return entryEffectUrlAr;
    }

    public void setEntryEffectUrlAr(String entryEffectUrlAr) {
        this.entryEffectUrlAr = entryEffectUrlAr;
    }

    public int getMicChatGuide() {
        return micChatGuide;
    }

    public void setMicChatGuide(int micChatGuide) {
        this.micChatGuide = micChatGuide;
    }

    public int getCoinBalance() {
        return coinBalance;
    }

    public void setCoinBalance(int coinBalance) {
        this.coinBalance = coinBalance;
    }

    public int getIsShowMicGuide() {
        return isShowMicGuide;
    }

    public void setIsShowMicGuide(int isShowMicGuide) {
        this.isShowMicGuide = isShowMicGuide;
    }

    public NewDeviceReward getDeviceReward() {
        return deviceReward;
    }

    public void setDeviceReward(NewDeviceReward deviceReward) {
        this.deviceReward = deviceReward;
    }
}
