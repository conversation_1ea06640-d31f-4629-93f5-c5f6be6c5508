package com.quhong.data.vo;

public class PrizeConfigVO {
    private String key;
    private String drawType;
    private String uid;
    private String userRid;
    private String userName;
    private String iconEn;
    private String nameEn;
    private String nameAr;
    private String nameShortEn;
    private String nameShortAr;
    private String rewardType;
    private Integer sourceId;
    private Integer rewardTime;
    private Integer rewardNum;
    private Integer rewardPrice;
    private Integer rateNum;
    private Integer inRoomScreen;           // 本房间公屏
    private Integer allRoomScreen;          // 全房间公屏
    private Integer broadcast;              // 房间横幅广播
    private Integer status;                 // 状态
    private Integer ctime;

    public PrizeConfigVO() {
    }


    public PrizeConfigVO(String nameEn, String nameAr, String rewardType, Integer sourceId, Integer rewardTime, Integer rewardNum, Integer inRoomScreen, Integer allRoomScreen, Integer broadcast) {
        this.nameEn = nameEn;
        this.nameAr = nameAr;
        this.rewardType = rewardType;
        this.sourceId = sourceId;
        this.rewardTime = rewardTime;
        this.rewardNum = rewardNum;
        this.inRoomScreen = inRoomScreen;
        this.allRoomScreen = allRoomScreen;
        this.broadcast = broadcast;
    }

    public PrizeConfigVO(String key, String iconEn, String nameEn, String nameAr, String rewardType, Integer sourceId, Integer rewardTime, Integer rewardNum) {
        this.key = key;
        this.iconEn = iconEn;
        this.nameEn = nameEn;
        this.nameAr = nameAr;
        this.rewardType = rewardType;
        this.sourceId = sourceId;
        this.rewardTime = rewardTime;
        this.rewardNum = rewardNum;
    }

    public PrizeConfigVO(String rewardType, Integer sourceId, Integer rewardTime, Integer rewardNum) {
        this.rewardType = rewardType;
        this.sourceId = sourceId;
        this.rewardTime = rewardTime;
        this.rewardNum = rewardNum;
    }

    public String getDrawType() {
        return drawType;
    }

    public void setDrawType(String drawType) {
        this.drawType = drawType;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserRid() {
        return userRid;
    }

    public void setUserRid(String userRid) {
        this.userRid = userRid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIconEn() {
        return iconEn;
    }

    public void setIconEn(String iconEn) {
        this.iconEn = iconEn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getNameShortEn() {
        return nameShortEn;
    }

    public void setNameShortEn(String nameShortEn) {
        this.nameShortEn = nameShortEn;
    }

    public String getNameShortAr() {
        return nameShortAr;
    }

    public void setNameShortAr(String nameShortAr) {
        this.nameShortAr = nameShortAr;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(Integer rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Integer getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(Integer rewardNum) {
        this.rewardNum = rewardNum;
    }

    public Integer getRewardPrice() {
        return rewardPrice;
    }

    public void setRewardPrice(Integer rewardPrice) {
        this.rewardPrice = rewardPrice;
    }

    public Integer getRateNum() {
        return rateNum;
    }

    public void setRateNum(Integer rateNum) {
        this.rateNum = rateNum;
    }

    public Integer getInRoomScreen() {
        return inRoomScreen;
    }

    public void setInRoomScreen(Integer inRoomScreen) {
        this.inRoomScreen = inRoomScreen;
    }

    public Integer getAllRoomScreen() {
        return allRoomScreen;
    }

    public void setAllRoomScreen(Integer allRoomScreen) {
        this.allRoomScreen = allRoomScreen;
    }

    public Integer getBroadcast() {
        return broadcast;
    }

    public void setBroadcast(Integer broadcast) {
        this.broadcast = broadcast;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
