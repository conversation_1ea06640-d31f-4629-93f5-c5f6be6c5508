package com.quhong.data.vo;

import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class FootballCarnivalVO extends OtherRankConfigVO {

    private Integer kickBallChanceNum;            // 我的踢球机会数
    private Integer kickBallTotalNum;         // 当前阶段我的踢球总次数
    private Integer kickBallSuccessNum;            // 当前阶段我的踢球成功次数
    private List<RollRecordData> rollRecordList;   // 滚屏列表
    private FootballCarnivalTeamConfig teamInfo;   // 团队信息
    private List<FootballCarnivalTeamConfig> teamRankList;   // 团队榜单-晋级赛阶段
    private List<FootballCarnivalTeamCompetitionRoundVO> roundRankList;   // 团队榜单-晋级赛阶段

    private List<RollRecordData> recordList;   // 抽奖记录列表
    private Integer nextUrl;

    private Integer stage; // 活动阶段 1 选拔赛 2 8->4 3 4->2 4 决赛
    private Integer stageStartTime; // 活动阶段开始时间
    private Integer stageEndTime; // 活动阶段结束时间
    private Integer isJoinStage; // 用户是否进入当前阶段 0:未进入 1:已进入


    public static class FootballCarnivalTeamCompetitionRoundVO {
        private Integer stage;
        private List<FootballCarnivalTeamCompetitionVO> pkRankList; // pk对阵信息

        public Integer getStage() {
            return stage;
        }

        public void setStage(Integer stage) {
            this.stage = stage;
        }

        public List<FootballCarnivalTeamCompetitionVO> getPkRankList() {
            return pkRankList;
        }

        public void setPkRankList(List<FootballCarnivalTeamCompetitionVO> pkRankList) {
            this.pkRankList = pkRankList;
        }
    }

    public static class FootballCarnivalTeamCompetitionVO {
        private FootballCarnivalTeamConfig team1;
        private FootballCarnivalTeamConfig team2;

        public FootballCarnivalTeamConfig getTeam1() {
            return team1;
        }

        public void setTeam1(FootballCarnivalTeamConfig team1) {
            this.team1 = team1;
        }

        public FootballCarnivalTeamConfig getTeam2() {
            return team2;
        }

        public void setTeam2(FootballCarnivalTeamConfig team2) {
            this.team2 = team2;
        }
    }


    public static class FootballCarnivalTeamConfig {
        private String teamId;            // 团队id
        private String teamLeadUid;       // 团队队长id
        private String teamName;          // 团队名称
        private Integer teamScore;        // 团队总分数
        private Integer rank;             // 团队排名
        private Integer muchScore;        // 分数差
        private List<TeamInfo> teamMemberList;  // 团队成员信息

        private Integer muchPkScore;     // pk阶段分数差,仅看自己队伍进入pk赛时自己队伍有值


        public String getTeamId() {
            return teamId;
        }

        public void setTeamId(String teamId) {
            this.teamId = teamId;
        }

        public String getTeamLeadUid() {
            return teamLeadUid;
        }

        public void setTeamLeadUid(String teamLeadUid) {
            this.teamLeadUid = teamLeadUid;
        }

        public String getTeamName() {
            return teamName;
        }

        public void setTeamName(String teamName) {
            this.teamName = teamName;
        }

        public Integer getTeamScore() {
            return teamScore;
        }

        public void setTeamScore(Integer teamScore) {
            this.teamScore = teamScore;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Integer getMuchScore() {
            return muchScore;
        }

        public void setMuchScore(Integer muchScore) {
            this.muchScore = muchScore;
        }

        public List<TeamInfo> getTeamMemberList() {
            return teamMemberList;
        }

        public void setTeamMemberList(List<TeamInfo> teamMemberList) {
            this.teamMemberList = teamMemberList;
        }

        public Integer getMuchPkScore() {
            return muchPkScore;
        }

        public void setMuchPkScore(Integer muchPkScore) {
            this.muchPkScore = muchPkScore;
        }
    }

    public static class TeamInfo {
        private String uid;              // 用户uid
        private String name;             // 用户名
        private String head;             // 用户头像
        private Integer score;           // 驯龙次数

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getScore() {
            return score;
        }

        public void setScore(Integer score) {
            this.score = score;
        }
    }

    public static class RollRecordData extends ResourceKeyConfigData.ResourceMeta {
        private String uid;                 // 用户uid
        private String name;                // 用户名
        private String head;                // 用户头像
        private Integer ctime;              // 抽奖时间

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }

    public Integer getKickBallChanceNum() {
        return kickBallChanceNum;
    }

    public void setKickBallChanceNum(Integer kickBallChanceNum) {
        this.kickBallChanceNum = kickBallChanceNum;
    }

    public Integer getKickBallSuccessNum() {
        return kickBallSuccessNum;
    }

    public void setKickBallSuccessNum(Integer kickBallSuccessNum) {
        this.kickBallSuccessNum = kickBallSuccessNum;
    }

    public Integer getKickBallTotalNum() {
        return kickBallTotalNum;
    }

    public void setKickBallTotalNum(Integer kickBallTotalNum) {
        this.kickBallTotalNum = kickBallTotalNum;
    }

    public FootballCarnivalTeamConfig getTeamInfo() {
        return teamInfo;
    }

    public void setTeamInfo(FootballCarnivalTeamConfig teamInfo) {
        this.teamInfo = teamInfo;
    }

    public List<FootballCarnivalTeamConfig> getTeamRankList() {
        return teamRankList;
    }

    public void setTeamRankList(List<FootballCarnivalTeamConfig> teamRankList) {
        this.teamRankList = teamRankList;
    }

    public List<RollRecordData> getRollRecordList() {
        return rollRecordList;
    }

    public void setRollRecordList(List<RollRecordData> rollRecordList) {
        this.rollRecordList = rollRecordList;
    }

    public List<RollRecordData> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<RollRecordData> recordList) {
        this.recordList = recordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public Integer getStageStartTime() {
        return stageStartTime;
    }

    public void setStageStartTime(Integer stageStartTime) {
        this.stageStartTime = stageStartTime;
    }

    public Integer getStageEndTime() {
        return stageEndTime;
    }

    public void setStageEndTime(Integer stageEndTime) {
        this.stageEndTime = stageEndTime;
    }

    public List<FootballCarnivalTeamCompetitionRoundVO> getRoundRankList() {
        return roundRankList;
    }

    public void setRoundRankList(List<FootballCarnivalTeamCompetitionRoundVO> roundRankList) {
        this.roundRankList = roundRankList;
    }

    public Integer getIsJoinStage() {
        return isJoinStage;
    }

    public void setIsJoinStage(Integer isJoinStage) {
        this.isJoinStage = isJoinStage;
    }
}
