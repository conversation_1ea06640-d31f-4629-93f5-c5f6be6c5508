package com.quhong.data.bo;

import java.util.List;
import java.util.Map;

public class TaskRankTemplateBO {

    private Map<String, List<TaskRankConfig>> dailyTaskMap;      // 以taskKey为key的map
    private Map<String, List<TaskRankConfig>> periodTaskMap;     // 以taskKey为key的map
    private Map<String, List<TaskRankConfig>> rankTaskMap;      // 以rankType为key的map

    public static class TaskRankConfig {
        private String activityId;
        private int testStatus;                            // 灰度测试： 0: 否  1: 是
        private int startTime;                             // 活动开始时间
        private int endTime;                               // 活动结束时间
        private String countryFilter;                      // 区域限制: 仅选中区域用户可完成活动任务与上榜
        private int genderFilter;                          // 性别限制: 仅选中性别可完成活动任务与上榜 0:全部 1:男 2: 女
        private List<Integer> giftIdFilterList;            // 活动礼物
        private String taskRankKey;                        // 任务key
        private Integer totalProcess;                      // 完成值
        private String extraParam;                         // 某些任务额外配置 如指定游戏key、指定设备数

        private boolean isCollectData = true;            // 是否对此任务，单独建立key收集数据

        // 仅对任务-【送出指定礼物流水】 有效
        private Integer userType;           // 用户类型: 0: 全部用户  1: 注册0-7天新设备用户
        private Integer giftOrigin;         // 礼物来源: 0: 全部来源  1: 非背包礼物

        public String getActivityId() {
            return activityId;
        }

        public void setActivityId(String activityId) {
            this.activityId = activityId;
        }

        public int getTestStatus() {
            return testStatus;
        }

        public void setTestStatus(int testStatus) {
            this.testStatus = testStatus;
        }

        public int getStartTime() {
            return startTime;
        }

        public void setStartTime(int startTime) {
            this.startTime = startTime;
        }

        public int getEndTime() {
            return endTime;
        }

        public void setEndTime(int endTime) {
            this.endTime = endTime;
        }

        public String getCountryFilter() {
            return countryFilter;
        }

        public void setCountryFilter(String countryFilter) {
            this.countryFilter = countryFilter;
        }

        public int getGenderFilter() {
            return genderFilter;
        }

        public void setGenderFilter(int genderFilter) {
            this.genderFilter = genderFilter;
        }

        public List<Integer> getGiftIdFilterList() {
            return giftIdFilterList;
        }

        public void setGiftIdFilterList(List<Integer> giftIdFilterList) {
            this.giftIdFilterList = giftIdFilterList;
        }

        public String getTaskRankKey() {
            return taskRankKey;
        }

        public void setTaskRankKey(String taskRankKey) {
            this.taskRankKey = taskRankKey;
        }

        public Integer getTotalProcess() {
            return totalProcess;
        }

        public void setTotalProcess(Integer totalProcess) {
            this.totalProcess = totalProcess;
        }

        public String getExtraParam() {
            return extraParam;
        }

        public void setExtraParam(String extraParam) {
            this.extraParam = extraParam;
        }

        public boolean isCollectData() {
            return isCollectData;
        }

        public void setCollectData(boolean collectData) {
            isCollectData = collectData;
        }

        public Integer getUserType() {
            return userType;
        }

        public void setUserType(Integer userType) {
            this.userType = userType;
        }

        public Integer getGiftOrigin() {
            return giftOrigin;
        }

        public void setGiftOrigin(Integer giftOrigin) {
            this.giftOrigin = giftOrigin;
        }
    }

    public Map<String, List<TaskRankConfig>> getDailyTaskMap() {
        return dailyTaskMap;
    }

    public void setDailyTaskMap(Map<String, List<TaskRankConfig>> dailyTaskMap) {
        this.dailyTaskMap = dailyTaskMap;
    }

    public Map<String, List<TaskRankConfig>> getPeriodTaskMap() {
        return periodTaskMap;
    }

    public void setPeriodTaskMap(Map<String, List<TaskRankConfig>> periodTaskMap) {
        this.periodTaskMap = periodTaskMap;
    }

    public Map<String, List<TaskRankConfig>> getRankTaskMap() {
        return rankTaskMap;
    }

    public void setRankTaskMap(Map<String, List<TaskRankConfig>> rankTaskMap) {
        this.rankTaskMap = rankTaskMap;
    }
}
