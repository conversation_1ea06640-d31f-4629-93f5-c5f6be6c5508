package com.quhong.data.bo;

import java.util.List;

/**
 * 完成任务BO
 */
public class TaskRankMsgBO {

    // 根据消息设置
    private String taskRankUid;                       // 任务uid
    private String taskAid;                           // 任务Aid
    private String taskRankKey;                       // 榜单任务key
    private String taskRankExtraParam;                // 某些任务额外配置 如指定游戏key、指定礼物
    private int giftId;                               // 发送或接收的礼物id
    private int giving_type;                          // 礼物赠送来源类型
    private int extraParamType;                       // 处理extraParam方式 0: 不处理  1: 礼物榜单  2: 游戏过滤
    private boolean setFlag;                          // 是否tn去重
    private int score;                                // 增加分数值

    // 根据用户属性设置
    private String taskUserCountry;                   // 用户国家
    private int taskUserGender;                       // 用户性别

    // 根据活动配置设置
    private String activityId;                        // 活动id
    private int testStatus;                           // 灰度测试状态
    private String countryFilter;                     // 国家过滤
    private int genderFilter;                         // 性别过滤
    private Integer totalProcess;                         // 完成值
    private String extraParam;                        // 某些任务额外配置 如指定游戏key、指定设备数过滤
    private List<Integer> giftIdFilterList;           // 礼物过滤
    private boolean isCollectData = true;            // 是否对此任务，单独建立key收集数据

    // 仅对任务-【送出指定礼物流水】 有效
    private Integer userType;           // 用户类型: 0: 全部用户  1: 注册0-7天新设备用户
    private Integer giftOrigin;         // 礼物来源: 0: 全部来源  1: 非背包礼物

    public TaskRankMsgBO(String taskRankUid, String taskAid, String taskRankKey, String taskRankExtraParam, int extraParamType, boolean setFlag, int score) {
        this.taskRankUid = taskRankUid;
        this.taskAid = taskAid;
        this.taskRankKey = taskRankKey;
        this.score = score;
        this.taskRankExtraParam = taskRankExtraParam;
        this.extraParamType = extraParamType;
        this.setFlag = setFlag;
    }

    public String getTaskRankUid() {
        return taskRankUid;
    }

    public void setTaskRankUid(String taskRankUid) {
        this.taskRankUid = taskRankUid;
    }

    public String getTaskAid() {
        return taskAid;
    }

    public void setTaskAid(String taskAid) {
        this.taskAid = taskAid;
    }

    public String getTaskRankKey() {
        return taskRankKey;
    }

    public void setTaskRankKey(String taskRankKey) {
        this.taskRankKey = taskRankKey;
    }

    public String getTaskRankExtraParam() {
        return taskRankExtraParam;
    }

    public void setTaskRankExtraParam(String taskRankExtraParam) {
        this.taskRankExtraParam = taskRankExtraParam;
    }

    public int getGiftId() {
        return giftId;
    }

    public void setGiftId(int giftId) {
        this.giftId = giftId;
    }

    public int getGiving_type() {
        return giving_type;
    }

    public void setGiving_type(int giving_type) {
        this.giving_type = giving_type;
    }

    public int getExtraParamType() {
        return extraParamType;
    }

    public void setExtraParamType(int extraParamType) {
        this.extraParamType = extraParamType;
    }

    public boolean isSetFlag() {
        return setFlag;
    }

    public void setSetFlag(boolean setFlag) {
        this.setFlag = setFlag;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public int getTestStatus() {
        return testStatus;
    }

    public void setTestStatus(int testStatus) {
        this.testStatus = testStatus;
    }

    public String getCountryFilter() {
        return countryFilter;
    }

    public void setCountryFilter(String countryFilter) {
        this.countryFilter = countryFilter;
    }

    public int getGenderFilter() {
        return genderFilter;
    }

    public void setGenderFilter(int genderFilter) {
        this.genderFilter = genderFilter;
    }

    public String getTaskUserCountry() {
        return taskUserCountry;
    }

    public void setTaskUserCountry(String taskUserCountry) {
        this.taskUserCountry = taskUserCountry;
    }

    public int getTaskUserGender() {
        return taskUserGender;
    }

    public void setTaskUserGender(int taskUserGender) {
        this.taskUserGender = taskUserGender;
    }

    public Integer getTotalProcess() {
        return totalProcess;
    }

    public void setTotalProcess(Integer totalProcess) {
        this.totalProcess = totalProcess;
    }

    public String getExtraParam() {
        return extraParam;
    }

    public void setExtraParam(String extraParam) {
        this.extraParam = extraParam;
    }

    public List<Integer> getGiftIdFilterList() {
        return giftIdFilterList;
    }

    public void setGiftIdFilterList(List<Integer> giftIdFilterList) {
        this.giftIdFilterList = giftIdFilterList;
    }

    public boolean isCollectData() {
        return isCollectData;
    }

    public void setCollectData(boolean collectData) {
        isCollectData = collectData;
    }


    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getGiftOrigin() {
        return giftOrigin;
    }

    public void setGiftOrigin(Integer giftOrigin) {
        this.giftOrigin = giftOrigin;
    }
}
