package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
public class UpdateUserInfoDTO extends HttpEnvData {

    private String name;

    private String head;

    private int headIndex;

    private int gender;

    private List<String> banner;

    private List<Integer> label_list;

    private String birthday;

    private String city;

    private String country;

    private String phone;

    private String desc;

    private int nlang;

    private List<Integer> label;

    private String videoUrl;

    private Long showVideoSwitch;

    private String inviteCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getHeadIndex() {
        return headIndex;
    }

    public void setHeadIndex(int headIndex) {
        this.headIndex = headIndex;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public List<String> getBanner() {
        return banner;
    }

    public void setBanner(List<String> banner) {
        this.banner = banner;
    }

    public List<Integer> getLabel_list() {
        return label_list;
    }

    public void setLabel_list(List<Integer> label_list) {
        this.label_list = label_list;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getNlang() {
        return nlang;
    }

    public void setNlang(int nlang) {
        this.nlang = nlang;
    }

    public List<Integer> getLabel() {
        return label;
    }

    public void setLabel(List<Integer> label) {
        this.label = label;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public Long getShowVideoSwitch() {
        return showVideoSwitch;
    }

    public void setShowVideoSwitch(Long showVideoSwitch) {
        this.showVideoSwitch = showVideoSwitch;
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }
}
