package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class MomentDTO extends HttpEnvData {
    private String mid; // 朋友圈id
    private String key;
    private int opt; // 1featured、 2following、 3new 4:Nearby
    private int page = 1;
    private int hideTop; // 是否隐藏置顶内容
    private int topMomentEndTime;//设置置顶帖子的结束时间
    private int excludeRecently;//0 不排除 1 排除

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getOpt() {
        return opt;
    }

    public void setOpt(int opt) {
        this.opt = opt;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getHideTop() {
        return hideTop;
    }

    public void setHideTop(int hideTop) {
        this.hideTop = hideTop;
    }

    public int getTopMomentEndTime() {
        return topMomentEndTime;
    }

    public void setTopMomentEndTime(int topMomentEndTime) {
        this.topMomentEndTime = topMomentEndTime;
    }

    public int getExcludeRecently() {
        return excludeRecently;
    }

    public void setExcludeRecently(int excludeRecently) {
        this.excludeRecently = excludeRecently;
    }
}
