package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class ShareMomentDTO extends HttpEnvData {
    private String picture;
    private String momentText;
    private String location; // 活动组名，不传则为活动id

    private String quoteAction;// 引用的活动地址 要跳转的活动连接
    private String quoteIcon; // 引用的活动图标 url连接
    private String quoteContent;  // 引用的活动内容（一般为活动的阿语名称）

    private Integer topicRid; // 话题rid

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getMomentText() {
        return momentText;
    }

    public void setMomentText(String momentText) {
        this.momentText = momentText;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getQuoteIcon() {
        return quoteIcon;
    }

    public void setQuoteIcon(String quoteIcon) {
        this.quoteIcon = quoteIcon;
    }

    public String getQuoteContent() {
        return quoteContent;
    }

    public void setQuoteContent(String quoteContent) {
        this.quoteContent = quoteContent;
    }

    public String getQuoteAction() {
        return quoteAction;
    }

    public void setQuoteAction(String quoteAction) {
        this.quoteAction = quoteAction;
    }

    public Integer getTopicRid() {
        return topicRid;
    }

    public void setTopicRid(Integer topicRid) {
        this.topicRid = topicRid;
    }
}
