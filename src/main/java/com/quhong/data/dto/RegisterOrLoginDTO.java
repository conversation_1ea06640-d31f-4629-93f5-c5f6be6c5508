package com.quhong.data.dto;

import com.quhong.data.AdjustCampaignInfo;
import com.quhong.handler.HttpEnvData;

import java.util.List;

public class RegisterOrLoginDTO extends HttpEnvData {
    protected String androidid = "";
    protected String lang = "en_US";
    protected String oid = "";
    protected int type;
    protected String account = "";
    protected String password = "";
    protected String name = "";
    protected String head = "";
    protected int gender = 1;
    protected String channel = "";
    protected String tn_ticket = "";
    protected String tn_msg = "";
    protected String idfa = ""; // 苹果的广告id
    protected String distinct_id = ""; // 数数访客id
    protected String ta_device_id = ""; // 数数设备id
    protected String login_token = "";
    protected String token = ""; //  # 与login_token一致 ios 8.29没传token
    protected String promotion_id = ""; // 推广渠道
    protected String authorization_code = "";
    protected String open_id = "";
    protected String ip = "";
    protected String p3 = ""; // ios_key
    protected String vname = "";
    protected String pCountryCode;
    protected String pNumber;
    protected String tokenExpired;
    protected String email;
    protected String huaweiPhoneCode; //华为手机验证码
    protected String installReferrer;// 广告投放归因，目前接了facebook 广告
    protected String shuMeiMsg; // 数美凭据
    protected DeviceInfo deviceInfo; // 设备信息，用于国内环境判断
    protected String adjustCampaignInfo; // Adjust广告信息对象

    public static class DeviceInfo {
        private String simOperation; // 是中国移动、中国联通、中国电信、中国广电 传值
        private List<String> installApps;  // 安装微信、国内抖音 传值，值为包名
        private String timeZone; // 时区 为国内+8时区，传值
        private String chinaMobile; // 国行手机

        public String getSimOperation() {
            return simOperation;
        }

        public void setSimOperation(String simOperation) {
            this.simOperation = simOperation;
        }

        public List<String> getInstallApps() {
            return installApps;
        }

        public void setInstallApps(List<String> installApps) {
            this.installApps = installApps;
        }

        public String getTimeZone() {
            return timeZone;
        }

        public void setTimeZone(String timeZone) {
            this.timeZone = timeZone;
        }

        public String getChinaMobile() {
            return chinaMobile;
        }

        public void setChinaMobile(String chinaMobile) {
            this.chinaMobile = chinaMobile;
        }
    }

    public String getAndroidid() {
        return androidid;
    }

    public void setAndroidid(String androidid) {
        this.androidid = androidid;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getTn_ticket() {
        return tn_ticket;
    }

    public void setTn_ticket(String tn_ticket) {
        this.tn_ticket = tn_ticket;
    }

    public String getTn_msg() {
        return tn_msg;
    }

    public void setTn_msg(String tn_msg) {
        this.tn_msg = tn_msg;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getDistinct_id() {
        return distinct_id;
    }

    public void setDistinct_id(String distinct_id) {
        this.distinct_id = distinct_id;
    }

    public String getTa_device_id() {
        return ta_device_id;
    }

    public void setTa_device_id(String ta_device_id) {
        this.ta_device_id = ta_device_id;
    }

    public String getLogin_token() {
        return login_token;
    }

    public void setLogin_token(String login_token) {
        this.login_token = login_token;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPromotion_id() {
        return promotion_id;
    }

    public void setPromotion_id(String promotion_id) {
        this.promotion_id = promotion_id;
    }

    public String getAuthorization_code() {
        return authorization_code;
    }

    public void setAuthorization_code(String authorization_code) {
        this.authorization_code = authorization_code;
    }

    public String getOpen_id() {
        return open_id;
    }

    public void setOpen_id(String open_id) {
        this.open_id = open_id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getP3() {
        return p3;
    }

    public void setP3(String p3) {
        this.p3 = p3;
    }

    public String getVname() {
        return vname;
    }

    public void setVname(String vname) {
        this.vname = vname;
    }

    public String getpCountryCode() {
        return pCountryCode;
    }

    public void setpCountryCode(String pCountryCode) {
        this.pCountryCode = pCountryCode;
    }

    public String getpNumber() {
        return pNumber;
    }

    public void setpNumber(String pNumber) {
        this.pNumber = pNumber;
    }

    public String getTokenExpired() {
        return tokenExpired;
    }

    public void setTokenExpired(String tokenExpired) {
        this.tokenExpired = tokenExpired;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getHuaweiPhoneCode() {
        return huaweiPhoneCode;
    }

    public void setHuaweiPhoneCode(String huaweiPhoneCode) {
        this.huaweiPhoneCode = huaweiPhoneCode;
    }

    public String getInstallReferrer() {
        return installReferrer;
    }

    public void setInstallReferrer(String installReferrer) {
        this.installReferrer = installReferrer;
    }

    public String getShuMeiMsg() {
        return shuMeiMsg;
    }

    public void setShuMeiMsg(String shuMeiMsg) {
        this.shuMeiMsg = shuMeiMsg;
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getAdjustCampaignInfo() {
        return adjustCampaignInfo;
    }

    public void setAdjustCampaignInfo(String adjustCampaignInfo) {
        this.adjustCampaignInfo = adjustCampaignInfo;
    }
}
