package com.quhong.config;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/12
 */
public class RoomEventConfig {

    /**
     * 创建房间活动需要花费的钻石数
     */
    private int costBeans;

    /**
     * 活动封面
     */
    private List<String> eventCoverUrl;

    /**
     * 活动类型
     */
    private Map<String, EventType> eventType;

    public static class EventType {
        private String icon;
        private String nameEn;
        private String nameAr;

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }
    }

    public int getCostBeans() {
        return costBeans;
    }

    public void setCostBeans(int costBeans) {
        this.costBeans = costBeans;
    }

    public List<String> getEventCoverUrl() {
        return eventCoverUrl;
    }

    public void setEventCoverUrl(List<String> eventCoverUrl) {
        this.eventCoverUrl = eventCoverUrl;
    }

    public Map<String, EventType> getEventType() {
        return eventType;
    }

    public void setEventType(Map<String, EventType> eventType) {
        this.eventType = eventType;
    }
}
