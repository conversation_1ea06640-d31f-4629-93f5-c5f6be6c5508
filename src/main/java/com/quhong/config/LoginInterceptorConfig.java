package com.quhong.config;

import com.quhong.intercepters.InterceptConfig;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拦截内容
 */
@Configuration
public class LoginInterceptorConfig extends InterceptConfig {

    protected List<String> getExcludePaths() {
        return Arrays.asList(baseUrl + "visitor/source/appsflyer",
                baseUrl + "visitor/source/adjust", baseUrl + "visitor/fb/delete", baseUrl + "external/**",
                baseUrl + "check_phone", baseUrl + "sendSms", baseUrl + "appleLoginTokenUpgrade"
                , baseUrl + "checkSendNum", baseUrl + "incSendNum", baseUrl + "get/starPage/checkDeviceLogin"
                ,baseUrl + "visitor/fb/proDelete");

    }


    @Override
    protected Map<String, Long> getRequestWarnDurationMap() {
        Map<String, Long> map = new HashMap<>();
        map.put("/login_service/register_or_login", 5000L);
        return map;
    }
}
