package com.quhong.controllers;


import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.DiTreasureDrawDTO;
import com.quhong.data.dto.GuardEarthDTO;
import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.data.dto.ShareMomentDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 杂七杂八的接口
 */


@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class GeneralController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(GeneralController.class);

    @Resource
    private GiftRecommendService giftRecommendService;
    @Resource
    private LuckyGiftService luckyGiftService;
    @Resource
    private BlindBoxWeeklyService blindBoxWeeklyService;
    @Resource
    private FootballActivityService footballActivityService;
    @Resource
    private MiniTaskService miniTaskService;
    @Resource
    private PyramidService pyramidService;
    @Resource
    private ThanksgivingService thanksgivingService;
    @Resource
    private MissContestService missContestService;
    @Resource
    private ChristmasService christmasService;
    @Resource
    private MeteorService meteorService;
    @Resource
    private PlantsWarService plantsWarService;
    @Resource
    private CupidService cupidService;
    @Resource
    private EidV2Service eidV2Service;
    @Resource
    private ClownsMagicService clownsMagicService;
    @Resource
    private GuardEarthService guardEarthService;
    @Resource
    private FalconService falconService;
    @Resource
    private SuperPlayerService superPlayerService;
    @Resource
    private Eid2024Service eid2024Service;
    @Resource
    private RechargeCanivalService rechargeCanivalService;
    @Resource
    private RechargeDigTreasureService rechargeDigTreasureService;
    @Resource
    private MomentHotPostService momentHotPostService;
    @Resource
    private EventCenterService eventCenterService;
    @Resource
    private CarromGameService carromGameService;
    @Resource
    private RoomReturnBonusService roomReturnBonusService;

    @RequestMapping("giftRecommend")
    private String giftRecommend(@RequestParam String uid, @RequestParam String recommendId) {
        return createResult(HttpCode.SUCCESS, giftRecommendService.giftConfig(uid, recommendId));
    }


    @RequestMapping("luckyGiftInfo")
    private String luckyGiftInfo(@RequestParam String uid, @RequestParam int slang) {
        return createResult(HttpCode.SUCCESS, luckyGiftService.luckyGiftInfo(uid, slang));
    }

    // 官网接口
    @RequestMapping("officialSite")
    private String officialSite() {
        return createResult(HttpCode.SUCCESS, giftRecommendService.officialSite());
    }

    // 海报接口
    @RequestMapping("poster")
    private String officialPoster(@RequestParam String version) {
        return createResult(HttpCode.SUCCESS, giftRecommendService.officialPoster(version));
    }

    /**
     * 盲盒周活动
     */
    @RequestMapping("blindBoxWeeklyConfig")
    private String blindBoxWeeklyConfig(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, blindBoxWeeklyService.blindBoxWeeklyConfig(uid, activityId));
    }

    /**
     * 足球勋章活动
     */
    @RequestMapping("footballConfig")
    private String footballConfig(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, footballActivityService.footballConfig(uid, activityId));
    }

    /**
     * 迷你任务活动
     */
    @RequestMapping("miniTaskConfig")
    private String miniTaskConfig(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, miniTaskService.miniTaskConfig(uid, activityId));
    }

    /**
     * 足球排行榜活动
     */
    @RequestMapping("glorySoccer")
    private String glorySoccer(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, footballActivityService.fbCompetitionConfig(uid, activityId));
    }

    /**
     * 金字塔排行活动
     */
    @RequestMapping("pyramidConfig")
    private String pyramidConfig(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, pyramidService.pyramidConfig(activityId, uid));
    }

    @RequestMapping("pyramidGetAward")
    private String pyramidGetAward(@RequestParam String uid, @RequestParam String activityId, @RequestParam String boxType) {
        pyramidService.pyramidGetAward(activityId, uid, boxType);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 感恩节活动
     */
    @RequestMapping("thankGivingConfig")
    private String thankGivingConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, thanksgivingService.thankGivingConfig(activityId, uid));
    }

    @RequestMapping("thankGivingGetAward")
    private String thankGivingGetAward(@RequestParam String uid, @RequestParam String activityId) {
        thanksgivingService.thankGivingGetAward(activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * YOU STAR小姐活动
     */
    @RequestMapping("missContestConfig")
    private String missContestConfig(@RequestParam String activityId, @RequestParam String uid, @RequestParam int missType) {
        return createResult(HttpCode.SUCCESS, missContestService.missContestConfig(activityId, uid, missType));
    }

    /**
     * 圣诞老人
     */
    @RequestMapping("christmasGiftConfig")
    private String christmasGiftConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, christmasService.christmasGiftConfig(activityId, uid));
    }

    /**
     * 流星雨
     */
    @RequestMapping("meteorConfig")
    private String meteorConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, meteorService.meteorConfig(activityId, uid));
    }

    /**
     * 植物大战僵尸
     */
    @RequestMapping("plantsWarConfig")
    private String plantsWarConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, plantsWarService.plantsWarConfig(activityId, uid));
    }

    @RequestMapping("plantsWarSearch")
    private String plantsWarSearch(@RequestParam String activityId, @RequestParam(defaultValue = "") String searchId) {
        return createResult(HttpCode.SUCCESS, plantsWarService.plantsWarSearch(activityId, searchId));
    }

    /**
     * 丘比特抽奖
     */
    @RequestMapping("cupidConfig")
    private String cupidConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, cupidService.cupidConfig(activityId, uid));
    }

    @RequestMapping("cupidDraw")
    private String cupidDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam int amount) {
        return createResult(HttpCode.SUCCESS, cupidService.cupidDraw(activityId, uid, amount));
    }

    @RequestMapping("cupidRecord")
    private String cupidRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, cupidService.cupidRecord(activityId, uid, page));
    }

    @RequestMapping("rankNotificationPushMsgTest")
    private String rankNotificationPushMsgTest(@RequestParam String activityId) {
        cupidService.rankNotificationPushMsgTest(activityId);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 开斋活动
     */
    @RequestMapping("eidV2Config")
    private String eidV2Config(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, eidV2Service.eidV2Config(activityId, uid));
    }

    /**
     * 小丑与魔术师
     */
    @RequestMapping("clownsMagicConfig")
    private String clownsMagicConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, clownsMagicService.clownsMagicConfig(activityId, uid));
    }

    @RequestMapping("clownsMagicClaim")
    private String clownsMagicClaim(@RequestParam String activityId, @RequestParam String uid, @RequestParam String taskKey, @RequestParam String drawType) {
        clownsMagicService.clownsMagicClaim(activityId, uid, taskKey, drawType);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 守护地球
     */
    @RequestMapping("guardEarthConfig")
    private String guardEarthConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, guardEarthService.guardEarthConfig(activityId, uid));
    }

    @RequestMapping("guardEarthClaim")
    private String guardEarthClaim(@RequestBody GuardEarthDTO dto) {
        logger.info("guardEarthClaim: {}", JSONObject.toJSONString(dto));
        guardEarthService.guardEarthClaim(dto);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 猎鹰达人
     */
    @RequestMapping("falconConfig")
    private String falconConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, falconService.falconConfig(activityId, uid));
    }

    @RequestMapping("falconLike")
    private String falconLike(@RequestParam String activityId, @RequestParam String uid, @RequestParam String drawType) {
        logger.info("falconLike uid:{}, drawType:{}", uid, drawType);
        falconService.falconLike(activityId, uid, drawType);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("falconExchange")
    private String falconExchange(@RequestParam String activityId, @RequestParam String uid, @RequestParam String drawType, @RequestParam String aid) {
        logger.info("falconExchange uid:{}, drawType:{} aid:{}", uid, drawType, aid);
        falconService.falconExchange(activityId, uid, drawType, aid);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("falconRecord")
    private String falconRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, falconService.falconRecord(activityId, uid, page));
    }

    @RequestMapping("falconSearch")
    private String falconSearch(@RequestParam String activityId, @RequestParam String uid, @RequestParam String search) {
        return createResult(HttpCode.SUCCESS, falconService.falconSearch(activityId, uid, search));
    }

    @RequestMapping("falconWishList")
    private String falconWishList(@RequestParam String activityId, @RequestParam String uid, @RequestParam String drawType, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, falconService.falconWishList(activityId, uid, drawType, page));
    }

    /**
     * 超级玩家
     */
    @RequestMapping("superPlayerConfig")
    private String superPlayerConfig(@RequestParam String activityId, @RequestParam String uid, @RequestParam String queryDate) {
        return createResult(HttpCode.SUCCESS, superPlayerService.superPlayerConfig(activityId, uid, queryDate));
    }

    /**
     * 2024宰牲节
     */
    @RequestMapping(value = "eid2024Config")
    private String eid2024Config(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("eid2024Config activityId:{}, uid:{}", activityId, uid);
        return createResult(HttpCode.SUCCESS, eid2024Service.eid2024Config(activityId, uid));
    }

    @RequestMapping(value = "catchSheep")
    private String catchSheep(@RequestParam String activityId, @RequestParam String uid, @RequestParam int zone, @RequestParam int amount) {
        logger.info("catchSheep activityId:{}, uid:{}, zone:{}, amount:{}", activityId, uid, zone, amount);
        eid2024Service.catchSheep(activityId, uid, zone, amount);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 202406充值活动
     */
    @RequestMapping(value = "rechargeCanivalConfig")
    private String rechargeCanivalConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("rechargeCanivalConfig activityId:{}, uid:{}", activityId, uid);
        return createResult(HttpCode.SUCCESS, rechargeCanivalService.rechargeCanivalConfig(activityId, uid));
    }


    @RequestMapping("commonShareMoment")
    private String commonShareMoment(@RequestParam String activityId,@RequestParam String uid, @RequestParam(required = false) String picture, @RequestParam int slang,
                                     @RequestParam(required = false) String momentText, @RequestParam(required = false) String acName) {
        rechargeCanivalService.commonShareMoment(activityId,uid, picture, slang, momentText, acName);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping(value ="commonShareMoment2", method = RequestMethod.POST)
    private String commonShareMoment2(@RequestParam String activityId, @RequestParam String uid, @RequestBody ShareMomentDTO dto) {
        rechargeCanivalService.commonShareMoment2(activityId,uid , dto);
        return createResult(HttpCode.SUCCESS, null);
    }




    /**
     * 充值挖宝
     */
    @RequestMapping(value = "rechargeDigTreasureConfig")
    private String rechargeDigTreasureConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("rechargeDigTreasureConfig activityId:{}, uid:{}", activityId, uid);
        return createResult(HttpCode.SUCCESS, rechargeDigTreasureService.rechargeDigTreasureConfig(activityId, uid));
    }

    @RequestMapping(value = "rechargeDigTreasureDraw")
    private String rechargeDigTreasureDraw(@RequestBody DiTreasureDrawDTO dto) {
        logger.info("rechargeDigTreasureDraw dto:{}", JSONObject.toJSONString(dto));
        return createResult(HttpCode.SUCCESS, rechargeDigTreasureService.rechargeDigTreasureDraw(dto));
    }

    @RequestMapping(value = "rechargeDigTreasureRefresh")
    private String rechargeDigTreasureRefresh(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("rechargeDigTreasureRefresh activityId:{}, uid:{}", activityId, uid);
        rechargeDigTreasureService.rechargeDigTreasureRefresh(activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping(value = "rechargeDigTreasureRecord")
    private String rechargeDigTreasureRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        logger.info("rechargeDigTreasureRecord activityId:{}, uid:{}, page:{}", activityId, uid, page);
        return createResult(HttpCode.SUCCESS, rechargeDigTreasureService.rechargeDigTreasureRecord(activityId, uid, page));
    }

    /**
     * 每周热帖
     */
    @RequestMapping(value = "momentHotPostConfig")
    private String momentHotPostConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("momentHotPostConfig activityId:{}, uid:{}", activityId, uid);
        return createResult(HttpCode.SUCCESS, momentHotPostService.momentHotPostConfig(activityId, uid));
    }

    @RequestMapping(value = "momentHotPostGet")
    private String momentHotPostGet(@RequestParam String activityId, @RequestParam String uid, @RequestParam String taskKey) {
        logger.info("momentHotPostGet activityId:{}, uid:{}, taskKey:{}", activityId, uid, taskKey);
        momentHotPostService.momentHotPostGet(activityId, uid, taskKey);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 克罗姆游戏活动基本信息
    @RequestMapping("carromGameInfo")
    private String carromGameInfo(@RequestParam String uid) {
        logger.info("carromGameInfo uid:{}", uid);
        return createResult(HttpCode.SUCCESS, carromGameService.carromGameInfo(uid));
    }

    // 克罗姆游戏活动领取
    @RequestMapping("carromGameCollect")
    private String carromGameCollect(@RequestParam String uid, @RequestParam int dayNum) {
        logger.info("carromGameCollect uid:{} dayNum:{}", uid, dayNum);
        return createResult(HttpCode.SUCCESS, carromGameService.carromGameCollect(uid, dayNum));
    }

    // 活动中心配置
    @RequestMapping("eventCenter")
    private String eventCenter(@RequestParam String uid, @RequestParam(required = false, defaultValue = "0") int eventType) {
        return createResult(HttpCode.SUCCESS, eventCenterService.eventCenterList(uid, eventType));
    }

    // 房间返钻基本信息基本信息
    @RequestMapping("getRoomReturnBonusInfos")
    private String getRoomReturnBonusInfos(@RequestParam String uid) {
        logger.info("getRoomReturnBonusInfos uid:{}", uid);
        return createResult(HttpCode.SUCCESS, roomReturnBonusService.getRoomReturnBonusInfos(uid));
    }

    // 房间返钻领取
    @RequestMapping("collectRoomReturnBonus")
    private String collectRoomReturnBonus(@RequestParam String uid) {
        logger.info("collectRoomReturnBonus uid:{} ", uid);
        return createResult(HttpCode.SUCCESS, roomReturnBonusService.collectRoomReturnBonus(uid));
    }
}
