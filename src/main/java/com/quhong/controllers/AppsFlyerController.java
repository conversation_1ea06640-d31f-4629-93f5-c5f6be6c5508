package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.dto.AdjustSourceCallbackDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.intercepters.UserInterceptor;
import com.quhong.service.AppsFlyerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/9/17
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class AppsFlyerController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(AppsFlyerController.class);

    @Resource
    private AppsFlyerService appsFlyerService;

    @RequestMapping(UserInterceptor.VISITOR + "source/appsflyer")
    public String getAppsflyerSourceCallback1(HttpServletRequest request, @RequestBody(required = false) String dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        try {
            appsFlyerService.saveLog(dto);
        } catch (Exception e) {
            logger.error("error to save apps flyer source data, requestId={}, msg={}, {}", requestId, e.getMessage(), e);
        }
        return createResult(HttpCode.SUCCESS, null);
    }

    // 可以考虑使用 @ModelAttribute
    @GetMapping(UserInterceptor.VISITOR + "source/adjust")
    public String getAdjustSourceCallback(@RequestParam(required = false, defaultValue = "") String uid,
                                          @RequestParam(required = false, defaultValue = "") String country,
                                          @RequestParam(required = false, defaultValue = "") String campaign_name,
                                          @RequestParam(required = false, defaultValue = "") String adgroup_name,
                                          @RequestParam(required = false, defaultValue = "") String fb_install_referrer_adgroup_name,
                                          @RequestParam(required = false, defaultValue = "") String meta_install_referrer_adgroup_name,
                                          @RequestParam(required = false, defaultValue = "") String gps_adid,
                                          @RequestParam(required = false, defaultValue = "") String city,
                                          @RequestParam(required = false, defaultValue = "") String idfa,
                                          @RequestParam(required = false, defaultValue = "") String language,
                                          @RequestParam(required = false, defaultValue = "") String network_name,
                                          @RequestParam(required = false, defaultValue = "") String app_version,
                                          @RequestParam(required = false, defaultValue = "") String event_name,
                                          @RequestParam(required = false, defaultValue = "") String app_id,
                                          @RequestParam(required = false, defaultValue = "") String installed_at,
                                          @RequestParam(required = false, defaultValue = "") String impression_time,
                                          @RequestParam(required = false, defaultValue = "") String store) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        try {
            AdjustSourceCallbackDTO dto = new AdjustSourceCallbackDTO(uid, country, campaign_name, adgroup_name, gps_adid,
                    city, idfa, language, network_name, app_version, event_name, app_id, installed_at, impression_time, store, fb_install_referrer_adgroup_name, meta_install_referrer_adgroup_name);
            logger.info("getAdjustSourceCallback. dto={}", JSONObject.toJSONString(dto));
            appsFlyerService.saveAdjustLog(dto);
        } catch (Exception e) {
            logger.error("error to save apps adjust source data, requestId={}, msg={}, {}", requestId, e.getMessage(), e);
        }
        return createResult(HttpCode.SUCCESS, null);
    }

    @PostMapping(UserInterceptor.VISITOR + "fb/delete")
    public String fbDelete(@RequestParam(required = false, defaultValue = "", name = "signed_request") String signedRequest) {
        try {
            return appsFlyerService.fbDelete(signedRequest, false);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            String confirmationCode = String.valueOf(System.currentTimeMillis());
            response.put("url", AppsFlyerService.FACEBOOK_DELETE_URL + "&id=" + confirmationCode);
            response.put("confirmation_code", confirmationCode);
            logger.error("error fbDelete source data, msg={}", e.getMessage(), e);
            return JSON.toJSONString(response);
        }
    }

    @PostMapping(UserInterceptor.VISITOR + "fb/proDelete")
    public String proDelete(@RequestParam(required = false, defaultValue = "", name = "signed_request") String signedRequest) {
        try {
            return appsFlyerService.fbDelete(signedRequest, true);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            String confirmationCode = String.valueOf(System.currentTimeMillis());
            response.put("url", AppsFlyerService.FACEBOOK_DELETE_URL + "&id=" + confirmationCode);
            response.put("confirmation_code", confirmationCode);
            logger.error("error fbDelete source data, msg={}", e.getMessage(), e);
            return JSON.toJSONString(response);
        }
    }
}
