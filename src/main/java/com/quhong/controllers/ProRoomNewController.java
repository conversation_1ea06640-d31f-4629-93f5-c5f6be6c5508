package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.data.dto.ProNewRoomListDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.service.ProRoomNewService;
import com.quhong.task.TaskFactory;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("${baseUrl}/pro")
public class ProRoomNewController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(ProRoomNewController.class);

    @Autowired
    private ProRoomNewService roomNewService;

    @RequestMapping("new_room_list")
    public DeferredResult<String> getNewRoomList(HttpServletRequest request) {
        DeferredResult<String> result = new DeferredResult<>();
        ProNewRoomListDTO listDTO = RequestUtils.getSendData(request, ProNewRoomListDTO.class);
        logger.info("begin get new_room list. uid={} page={}", listDTO.getUid(), listDTO.getPage());
        if (listDTO.getPage() < 1) {
            listDTO.setPage(1);
        }
        try {
            TaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    JSONObject jsonObject = roomNewService.listNewRoom(listDTO);
                    result.setResult(createResult(listDTO, HttpCode.SUCCESS, jsonObject));
                }
            });
            return result;
        } catch (Exception e) {
            logger.error("get new_room list error. {}", e.getMessage(), e);
        }
        result.setResult(createResult(listDTO, HttpCode.SERVER_ERROR, null));
        return result;
    }

}
