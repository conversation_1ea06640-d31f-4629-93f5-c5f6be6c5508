package com.quhong.controllers.inner;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.FriendApplyDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.service.AllOnlineFriendService;
import com.quhong.service.FriendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;


@RestController
@RequestMapping(value = "inner/${baseUrl}/", produces = MediaType.APPLICATION_JSON_VALUE)
public class InnerFriendController {
    private static final Logger logger = LoggerFactory.getLogger(InnerFriendController.class);

    @Resource
    private FriendService friendService;
    @Resource
    private AllOnlineFriendService allOnlineFriendService;

    /**
     * 获取所有的好友列表，有15s的本地缓存
     */
    @PostMapping("getFriendSet")
    ApiResult<Set<String>> getFriendSet(@RequestParam(value = "uid") String uid) {
        logger.info("getFriendSet uid={}", uid);
        if (StringUtils.isEmpty(uid)) {
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        return ApiResult.getOk(friendService.getFriendSet(uid));
    }

    /**
     * 获取好友数量
     */
    @PostMapping("getFriendCount")
    ApiResult<Integer> getFriendCount(@RequestParam(value = "uid") String uid) {
        logger.info("getFriendCount uid={}", uid);
        if (StringUtils.isEmpty(uid)) {
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        return ApiResult.getOk(friendService.getFriendCount(uid));
    }

    /**
     * 分页获取好友列表
     */
    @PostMapping("getPageFriendList")
    ApiResult<List<String>> getPageFriendList(@RequestParam(value = "uid") String uid,
                                              @RequestParam(value = "start") int start,
                                              @RequestParam(value = "end") int end) {
        logger.info("getPageFriendList uid={} start={} end={}", uid, start, end);
        if (StringUtils.isEmpty(uid)) {
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        return ApiResult.getOk(friendService.getPageFriendList(uid, start, end));
    }

    /**
     * 获取所有的在线用户列表，有15s的本地缓存
     */
    @PostMapping("getAllOnlineSet")
    ApiResult<Set<String>> getAllOnlineSet() {
        logger.info("getAllOnlineSet");
        return ApiResult.getOk(allOnlineFriendService.getAllOnlineSet());
    }

    /**
     * 获取新增访客数量
     */
    @PostMapping("getNewVisitorNum")
    ApiResult<Integer> getNewVisitorNum(HttpServletRequest request, @RequestBody HttpEnvData dto) {
        logger.info("getNewVisitorNum uid={}", dto.getUid());
        return ApiResult.getOk(friendService.getNewVisitorNum(dto.getUid()));
    }

    /**
     * 添加好友请求
     */
    @PostMapping("addFriendApply")
    ApiResult<List<String>> addFriendApply(@RequestParam(value = "uid") String uid,
                                           @RequestParam(value = "aid") String aid,
                                           @RequestParam(value = "msg") String msg) {
        FriendApplyDTO req = new FriendApplyDTO();
        req.setUid(uid);
        req.setAid(aid);
        req.setMsg(msg);
        List<String> emptyList = new ArrayList<>();
        try {
            friendService.addFriendApply(req);
        } catch (CommonException e) {
            HttpCode httpCode = e.getHttpCode();
            emptyList.add(httpCode.getMsg(SLangType.ENGLISH));
            emptyList.add(httpCode.getMsg(SLangType.ARABIC));
        }
        return ApiResult.getOk(emptyList);
    }

    /**
     * 成为好友
     */
    @PostMapping("becomeFriend")
    ApiResult<?> becomeFriend(@RequestBody FriendApplyDTO dto) {
        logger.info("becomeFriend dto={}", JSONObject.toJSONString(dto));
        friendService.becomeFriend(dto);
        return ApiResult.getOk();
    }

}
