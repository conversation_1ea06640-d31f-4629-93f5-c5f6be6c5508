package com.quhong.controllers;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.data.RspRoomMicData;
import com.quhong.data.RspRoomMicUserData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.data.RoomMicData;
import com.quhong.redis.CheatGiftRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.room.api.ThirdPartApiAdapter;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AbstractRoomMicController extends AbstractRoomController {
    private static final Logger logger = LoggerFactory.getLogger(AbstractRoomMicController.class);

    @Autowired
    protected RoomActorCache roomActorCache;
    @Autowired
    protected CheatGiftRedis cheatGiftRedis;
    @Autowired
    protected ThirdPartApiAdapter thirdPartApi;
    @Autowired
    protected SudGameRedis sudGameRedis;

    protected List<RspRoomMicData> convertRspList(MongoRoomData roomData, List<RoomMicData> micList) {
        List<RspRoomMicData> retList = new ArrayList<>();
        Map<String, CheatGiftRedis.CheatGiftData> map = cheatGiftRedis.fillCheatGift(micList);
        List<String> inGameList = sudGameRedis.getInGameUserList(roomData.getRid());
        for (RoomMicData micData : micList) {
            RspRoomMicData rspData = new RspRoomMicData();
            rspData.copyFrom(micData);
            String uid = micData.getUid();
            if (!StringUtils.isEmpty(uid)) {
                RoomActorDetailData detailData = roomActorCache.getData(roomData.getRid(), uid, false);
                if (detailData == null) {
                    logger.error("can not find room actor data. roomId={} uid={}", roomData.getRid(), uid);
                    continue;
                }
                RspRoomMicUserData userData = new RspRoomMicUserData();
                userData.copyFrom(detailData);
                userData.setStreamId(thirdPartApi.generateActorStreamId(roomData.getOwnerRid(), detailData.getRid(), detailData.getAid(), roomData.getRtc_type()));
                CheatGiftRedis.CheatGiftData cheatGiftData = map.get(uid);
                userData.setVoice_type(cheatGiftData.getVoiceType());
                if (!StringUtils.isEmpty(cheatGiftData.getPrankMicFrame())) {
                    userData.setMicFrame(cheatGiftData.getPrankMicFrame());
                }
                userData.setGameRunning(inGameList.contains(uid) ? 1 : 0);
                rspData.setUser(userData);
            }
            retList.add(rspData);
        }
        return retList;
    }

    protected void sendMicChange(String roomId, String fromUid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String path = "mic_change";
                Map<String, String> params = new HashMap<>();
                params.put("room_id", roomId);
                params.put("from_uid", fromUid);
                roomSender.sendPost(path, roomId, params);
            }
        });
    }

    protected void sendGreetChange(String roomId, String fromUid, int greetSwitch) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String path = "greet_change";
                Map<String, String> params = new HashMap<>();
                params.put("room_id", roomId);
                params.put("from_uid", fromUid);
                params.put("greet_switch", greetSwitch + "");
                roomSender.sendPost(path, roomId, params);
            }
        });
    }
}
