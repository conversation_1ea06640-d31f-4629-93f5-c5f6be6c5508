package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.PageDTO;
import com.quhong.data.dto.SearchDTO;
import com.quhong.data.vo.RoomActorDetailVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.redis.RoomVisitorsRedis;
import com.quhong.room.RoomActorListService;
import com.quhong.room.data.RoomActorCacheData;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.PageUtils;
import com.quhong.utils.RequestUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("${baseUrl}")
public class RoomActorListController extends AbstractRoomController {
    private static final Logger logger = LoggerFactory.getLogger(RoomActorListController.class);
    /**
     * 用户详细列表
     */
    public static final int DETAIL_PAGE_SIZE = 21;

    @Autowired
    private RoomActorListService roomActorListService;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Autowired
    private RoomVisitorsRedis roomVisitorsRedis;

    /**
     * 获取房间内用户列表
     */
    @RequestMapping("room_actor_list")
    public String getRoomActorList(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendData(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        int page = paramObj.getIntValue("page");
        if (page < 1) {
            page = 1;
        }
        String uid = paramObj.getString("uid");
        String roomId = paramObj.getString("room_id");
        roomIdCheck(roomId);
        if (StringUtils.isEmpty(roomId)) {
            logger.error("req room actor list error. roomId is null. page={} uid={}", page, uid);
            return createError(envData, HttpCode.PARAM_ERROR);
        }
        logger.info("req room actor list. page={} roomId={} uid={}", page, roomId, envData.getUid());
        envData.setRoomId(roomId);
        PageUtils.PageData<RoomActorCacheData> pageData = roomActorListService.findList(envData, page, DETAIL_PAGE_SIZE);
        JSONObject retObj = new JSONObject();
        retObj.put("next_page", pageData.nextPage);
        retObj.put("page_size", pageData.pageSize);
        retObj.put("list", pageData.list);
        retObj.put("total_actors", pageData.list.size());
        logger.info("resp room actor list. page={} list.size={} roomId={} uid={}", page, pageData.totalSize, roomId, envData.getUid());
        return createResult(envData, HttpCode.SUCCESS, retObj);
    }

    /**
     * 获取房间内用户列表详细信息
     */
    @RequestMapping("room_actors_detail")
    public String getRoomActorDetailList(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendData(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        int page = paramObj.getIntValue("page");
        if (page < 1) {
            page = 1;
        }
        String uid = paramObj.getString("uid");
        String roomId = paramObj.getString("room_id");
        roomIdCheck(roomId);
        if (StringUtils.isEmpty(roomId)) {
            logger.error("req room actor detail list error. roomId is null. page={} uid={}", page, uid);
            return createError(envData, HttpCode.PARAM_ERROR);
        }
        ApiResult<String> preResult = checkRoomLimit(roomId, uid, request.getRequestURI());
        if (preResult.isError()) {
            return createError(envData, preResult.getCode());
        }
        logger.info("req room actor list. page={} uid={} roomId={}", page, envData.getUid(), roomId);
        envData.setRoomId(roomId);
        PageUtils.PageData<RoomActorDetailData> pageData;
        // 新版本要展示访客
        boolean addVisitor = !RoomUtils.isGameRoom(roomId) && roomVisitorsRedis.showRoomVisitor(envData);
        int invite = paramObj.getIntValue("invite");
        boolean removeGameRun = invite > 0;
        pageData = roomActorListService.findDetailList(envData, page, DETAIL_PAGE_SIZE, false, addVisitor, removeGameRun);
        JSONObject retObj = new JSONObject();
        retObj.put("next_page", pageData.nextPage);
        retObj.put("page_size", pageData.pageSize);
        retObj.put("list", pageData.list);
        retObj.put("total_actors", pageData.totalSize);
        retObj.put("totalInRoomActors", roomPlayerRedis.getRoomActorsCount(roomId));
        retObj.put("totalVisitorActors", roomVisitorsRedis.getRoomVisitorNum(roomId));
        return createResult(envData, HttpCode.SUCCESS, retObj);
    }

    /**
     * 房间内用户列表搜索
     */
    @RequestMapping("room_actors_search")
    public String roomActorSearch(HttpServletRequest request) {
        SearchDTO req = RequestUtils.getSendData(request, SearchDTO.class);
        logger.info("req room actor search. roomId={} uid={} key={}", req.getRoomId(), req.getUid(), req.getKey());
        if (null == req.getKey() || StringUtils.isEmpty(req.getKey().trim()) || req.getKey().length() > 30) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return createResult(req, HttpCode.SUCCESS, roomActorListService.roomActorSearch(req));
    }

    /**
     * 房间大R列表
     */
    @RequestMapping("vip_lounge")
    public String vipLounge(HttpServletRequest request) {
        PageDTO req = RequestUtils.getSendData(request, PageDTO.class);
        logger.info("req room vip lounge. roomId={} uid={}", req.getRoomId(), req.getUid());
        // 新版本要展示访客
        boolean addVisitor = roomVisitorsRedis.showRoomVisitor(req);
        PageUtils.PageData<RoomActorDetailData> pageData = roomActorListService.findVipLoungeList(req, DETAIL_PAGE_SIZE, addVisitor);
        JSONObject retObj = new JSONObject();
        retObj.put("next_page", pageData.nextPage);
        retObj.put("page_size", pageData.pageSize);
        retObj.put("total_actors", pageData.totalSize);
        retObj.put("list", pageData.list);
        return createResult(req, HttpCode.SUCCESS, retObj);
    }

    /**
     * 房间可选择的用户列表(选择投票目标用户)
     */
    @RequestMapping("selectable_actor_list")
    public String selectableActorList(HttpServletRequest request) {
        PageDTO req = RequestUtils.getSendData(request, PageDTO.class);
        logger.info("req room vip lounge. roomId={} uid={}", req.getRoomId(), req.getUid());
        PageUtils.PageData<RoomActorDetailVO> pageData = roomActorListService.selectableActorList(req, DETAIL_PAGE_SIZE);
        JSONObject retObj = new JSONObject();
        retObj.put("next_page", pageData.nextPage);
        retObj.put("page_size", pageData.pageSize);
        retObj.put("total_actors", pageData.totalSize);
        retObj.put("list", pageData.list);
        return createResult(req, HttpCode.SUCCESS, retObj);
    }


    /**
     * 在线新用户列表
     * /room_actors_new_detail
     */
    @RequestMapping("room_actors_new_detail")
    public String newDetail(HttpServletRequest request) {
        PageDTO req = RequestUtils.getSendData(request, PageDTO.class);
        logger.info("req room new detail. roomId={} uid={}", req.getRoomId(), req.getUid());
        boolean addVisitor = roomVisitorsRedis.showRoomVisitor(req);
        PageUtils.PageData<RoomActorDetailData> pageData = roomActorListService.findNewUserList(req, DETAIL_PAGE_SIZE, addVisitor);
        JSONObject retObj = new JSONObject();
        retObj.put("next_page", pageData.nextPage);
        retObj.put("page_size", pageData.pageSize);
        retObj.put("total_actors", pageData.totalSize);
        retObj.put("list", pageData.list);
        return createResult(req, HttpCode.SUCCESS, retObj);
    }

}
