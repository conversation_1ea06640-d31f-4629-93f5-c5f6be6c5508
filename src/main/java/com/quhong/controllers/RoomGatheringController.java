package com.quhong.controllers;

import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.RoomGatheringDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomHttpCode;
import com.quhong.mysql.dao.GatheringFeeTimesDao;
import com.quhong.feign.IDetectService;
import com.quhong.mysql.data.GatheringFeeTimesData;
import com.quhong.service.RoomGatheringService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间召集
 *
 * <AUTHOR>
 * @date 2023/2/10
 */
@RestController
@RequestMapping("${baseUrl}")
public class RoomGatheringController extends AbstractRoomMicController {

    private static final Logger logger = LoggerFactory.getLogger(RoomGatheringController.class);

    @Resource
    private RoomGatheringService roomGatheringService;
    @Resource
    private IDetectService detectService;
    @Resource
    private GatheringFeeTimesDao gatheringFeeTimesDao;

    /**
     * 脏词检测
     */
    @RequestMapping("checkTextSafe")
    private String checkTextSafe(HttpServletRequest request) {
        RoomGatheringDTO req = RequestUtils.getSendData(request, RoomGatheringDTO.class);
        logger.info("check text safe. roomId={} uid={} msg={}", req.getRoomId(), req.getUid(), req.getMsg());
        // 脏词检测
        if (detectService.detectText(new TextDTO(req.getMsg(), DetectOriginConstant.ROOM_GATHERING, req.getUid())).getData().getIsSafe() == 0) {
            logger.info("Illegal content, please chat civilly. roomId={} uid={} msg={}", req.getRoomId(), req.getUid(), req.getMsg());
            return createResult(req, RoomHttpCode.ILLEGAL_CONTENT, null);
        }
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 房间召集
     */
    @RequestMapping("gathering")
    private String roomGathering(HttpServletRequest request) {
        RoomGatheringDTO req = RequestUtils.getSendData(request, RoomGatheringDTO.class);
        logger.info("room gathering. roomId={} uid={} type={} msg={}", req.getRoomId(), req.getUid(), req.getType(), req.getMsg());
        if (StringUtils.isEmpty(req.getRoomId()) || req.getType() == null) {
            logger.error("room gathering param error. roomId={} type={}", req.getRoomId(), req.getType());
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        roomGatheringService.roomGathering(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 召集记录
     */
    @RequestMapping("gatheringRecord")
    private String gatheringRecord(HttpServletRequest request) {
        RoomGatheringDTO req = RequestUtils.getSendData(request, RoomGatheringDTO.class);
        logger.info("get gathering record. roomId={} uid={} type={} page={}", req.getRoomId(), req.getUid(), req.getType(), req.getPage());
        if (StringUtils.isEmpty(req.getRoomId()) || req.getType() == null) {
            logger.error("get gathering record param error. roomId={} type={}", req.getRoomId(), req.getType());
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        return createResult(req, HttpCode.SUCCESS, roomGatheringService.gatheringRecord(req));
    }

    /**
     * 增加免费召集次数
     */
    @RequestMapping("incFeeGatheringTimes")
    private String incFeeGatheringTimes(HttpServletRequest request) {
        RoomGatheringDTO req = RequestUtils.getSendData(request, RoomGatheringDTO.class);
        if (ServerConfig.isProduct()) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        logger.info("incFeeGatheringTimes. uid={}", req.getUid());
        GatheringFeeTimesData data = gatheringFeeTimesDao.selectOne(req.getUid());
        if (data == null) {
            data = new GatheringFeeTimesData();
            data.setUid(req.getUid());
            data.setNum(1);
            data.setMtime(DateHelper.getNowSeconds());
            gatheringFeeTimesDao.insert(data);
        } else {
            gatheringFeeTimesDao.changeFeeTimes(req.getUid(), 1);
        }
        return createResult(req, HttpCode.SUCCESS, null);
    }
}
