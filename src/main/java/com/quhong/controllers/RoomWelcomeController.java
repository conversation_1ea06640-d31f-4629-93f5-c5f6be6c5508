package com.quhong.controllers;

import com.quhong.data.dto.RoomKickDTO;
import com.quhong.data.dto.WelcomeMsgDTO;
import com.quhong.data.vo.KickUserInfoVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomHttpCode;
import com.quhong.handler.WebController;
import com.quhong.mysql.data.WelcomeMessageReviewData;
import com.quhong.service.RoomKickService;
import com.quhong.service.RoomWelcomeMsgService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间踢人Controller
 *
 * <AUTHOR>
 * @date 2022/7/25
 */
@RestController
@RequestMapping("${baseUrl}")
public class RoomWelcomeController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(RoomWelcomeController.class);

    @Resource
    private RoomWelcomeMsgService roomWelcomeMsgService;

    /**
     *
     */
    @RequestMapping("welcome/selectPageList")
    private String selectPageList(HttpServletRequest request) {
        WelcomeMsgDTO req = RequestUtils.getSendData(request, WelcomeMsgDTO.class);
        logger.info("room welcome select page list. roomId={} uid={} page={}", req.getRoomId(), req.getUid(), req.getPage());
        PageVO<WelcomeMessageReviewData> vo = roomWelcomeMsgService.selectPageList(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    @RequestMapping("welcome/addNewMsg")
    private String addNewMsg(HttpServletRequest request) {
        WelcomeMsgDTO req = RequestUtils.getSendData(request, WelcomeMsgDTO.class);
        logger.info("room welcome add new msg. roomId={} uid={} messageContent={}", req.getRoomId(), req.getUid(), req.getMessageContent());
        roomWelcomeMsgService.addNewMsg(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    @RequestMapping("welcome/updateContent")
    private String updateContent(HttpServletRequest request) {
        WelcomeMsgDTO req = RequestUtils.getSendData(request, WelcomeMsgDTO.class);
        logger.info("room welcome update content. roomId={} uid={} id={} messageContent={}", req.getRoomId(), req.getUid(), req.getId(), req.getMessageContent());
        roomWelcomeMsgService.updateContent(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    @RequestMapping("welcome/delete")
    private String delete(HttpServletRequest request) {
        WelcomeMsgDTO req = RequestUtils.getSendData(request, WelcomeMsgDTO.class);
        logger.info("room welcome delete. roomId={} uid={} id={}", req.getRoomId(), req.getUid(), req.getId());
        roomWelcomeMsgService.delete(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

}
