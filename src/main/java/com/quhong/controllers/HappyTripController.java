package com.quhong.controllers;

import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.HappyTripVO;
import com.quhong.data.vo.OtherSupportUserVO;
import com.quhong.data.vo.PetFeedVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.datas.HttpResult;
import com.quhong.service.HappyTripService;
import com.quhong.service.PetFeedService;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 一起去旅行
 */
@RestController
@RequestMapping(value = "${baseUrl}happyTrip/", produces = MediaType.APPLICATION_JSON_VALUE)
public class HappyTripController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private HappyTripService happyTripService;

    /**
     * 获取基本信息
     */
    @RequestMapping("config")
    private HttpResult<HappyTripVO> happyTripConfig(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(happyTripService.happyTripConfig(activityId, uid));
    }

    /**
     * 团队榜单
     */
    @RequestMapping("teamRank")
    private HttpResult<HappyTripVO> happyTripTeamRank(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(happyTripService.happyTripTeamRank(activityId, uid));
    }

    /**
     * 加入团队
     */
    @RequestMapping("joinTeam")
    private HttpResult<HappyTripVO> happyTripJoinTeam(@RequestParam String activityId, @RequestParam String uid, @RequestParam String captainUid) {
        HappyTripVO happyTripVO = happyTripService.happyTripJoinTeam(activityId, uid, captainUid);
        return HttpResult.getOk(happyTripVO);
    }


    /**
     * 拍卖下注-竞价
     */
    @RequestMapping("bid")
    private HttpResult<HappyTripVO.BidRewardDataVO> happyTripBid(@RequestParam String activityId, @RequestParam String uid,
                                                               @RequestParam int round,@RequestParam int amount) {
        return HttpResult.getOk(happyTripService.happyTripBid(activityId, uid, round, amount));
    }


    /**
     * 竞拍相关的信息
     */
    @RequestMapping("bidInfo")
    private HttpResult<HappyTripVO> happyTripBidInfo(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(happyTripService.happyTripBidInfo(activityId, uid));
    }


    /**
     * 竞拍历史记录
     * type 0:我的投注记录 1:开奖记录
     */
    @RequestMapping("history")
    private HttpResult<HappyTripVO> happyTripHistory(@RequestParam String activityId, @RequestParam String uid,
                                                     @RequestParam int page, @RequestParam int type) {
        return HttpResult.getOk(happyTripService.happyTripRecord(activityId, uid, page, type));
    }

    /**
     * 私信邀请好友
     */
    @RequestMapping("msgShare")
    public HttpResult<?> happyTripMsgShare(@RequestBody ShareActivityDTO dto) {
        happyTripService.happyTripMsgShare(dto);
        return HttpResult.getOk();
    }

    /**
     * 添加到推荐列表
     */
    @RequestMapping("addToRecommendList")
    public HttpResult<?> happyTripAddToRecommendList(@RequestParam String activityId, @RequestParam String uid,int state) {
        happyTripService.addToRecommendList(activityId,uid,state);
        return HttpResult.getOk();
    }

    /**
     * 推荐列表
     */
    @RequestMapping("recommendList")
    public HttpResult<PageVO<OtherSupportUserVO> > happyTripRecommendList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return HttpResult.getOk(happyTripService.recommendUserList(activityId,uid, page));
    }

    /**
     * 搜索用户
     */
    @RequestMapping("searchUser")
    public HttpResult<PrizeConfigVO> happyTripSearchUser(@RequestParam String activityId, @RequestParam String uid, @RequestParam String keyword) {
        return HttpResult.getOk(happyTripService.searchUser(activityId,uid, keyword));
    }

    /**
     * 设置订阅
     */
    @RequestMapping("setRemind")
    public HttpResult<?> happyTripSetRemind(@RequestParam String activityId, @RequestParam String uid, @RequestParam int roundNum, @RequestParam int status) {
        happyTripService.setRemind(activityId, uid, roundNum, status);
        return HttpResult.getOk();
    }

     // 通用添加好友 /activity/commonAddFriendApply
}
