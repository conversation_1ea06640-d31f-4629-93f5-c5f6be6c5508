package com.quhong.controllers;

import com.quhong.data.vo.AvatarLimitationVO;
import com.quhong.data.dto.UpdateActorConfigDTO;
import com.quhong.data.vo.ResourceVersionVO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.ActorConfigService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户自定义设置
 *
 * <AUTHOR>
 * @date 2022/7/18
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class ActorConfigController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(ActorConfigController.class);

    @Resource
    private ActorConfigService actorConfigService;

    /**
     * 更新actor_config
     */
    @RequestMapping("update/actor_config")
    private String updateActorConfig(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("update effects setting. uid={}", req.getUid());
        actorConfigService.updateActorConfig(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 设置语言首选项
     */
    @RequestMapping("setting/video_option")
    private String settingVideoOption(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("setting video option. uid={}", req.getUid());
        return createResult(req, HttpCode.SUCCESS, actorConfigService.settingVideoOption(req));
    }

    /**
     * 设置坐骑首选项
     */
    @RequestMapping("setting/ride_option")
    private String settingRideOption(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("setting ride option. uid={}", req.getUid());
        return createResult(req, HttpCode.SUCCESS, actorConfigService.settingRideOption(req));
    }

    /**
     * 设置是否拒绝加好友
     */
    @RequestMapping("setting/buddy")
    private String settingBuddy(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("setting buddy. uid={}", req.getUid());
        return createResult(req, HttpCode.SUCCESS, actorConfigService.settingBuddy(req));
    }

    /**
     * 设置用户所属区域
     */
    @RequestMapping("set_nlang")
    private String setNlang(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("set nlang. uid={}", req.getUid());
        actorConfigService.setNlang(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 头像限制api
     */
    @RequestMapping("avatar/limitation")
    private String avatarLimitation(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("avatar limitation. uid={}", req.getUid());
        AvatarLimitationVO vo = actorConfigService.avatarLimitation(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 资源版本
     */
    @RequestMapping("resource_version")
    private String resourceVersion(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("resource version. uid={}", req.getUid());
        ResourceVersionVO vo = actorConfigService.resourceVersion();
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 是否有首充优惠的弹窗
     */
    @RequestMapping("boom/frist_recharge")
    private String firstRecharge(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("first recharge. uid={}", req.getUid());
        return createResult(req, new HttpCode(1, ""), null);
    }


    /**
     * 消息顶部通知开关: 默认开, 包含的消息【私信消息、系统通知、好友请求、活动通知】
     */
    @RequestMapping("messageTopSwitch")
    private String messageTopSwitch(HttpServletRequest request) {
        UpdateActorConfigDTO req = RequestUtils.getSendData(request, UpdateActorConfigDTO.class);
        logger.info("messageTopSwitch. uid={}, messageTopSwitch={}", req.getUid(), req.getMessageTopSwitch());
        actorConfigService.setMessageTopSwitch(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }
}
