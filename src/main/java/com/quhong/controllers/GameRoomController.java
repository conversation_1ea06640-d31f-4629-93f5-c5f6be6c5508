package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.RoomConfigConstant;
import com.quhong.data.dto.GameRoomDTO;
import com.quhong.data.dto.RoomDTO;
import com.quhong.data.vo.RoomGameConfigVO;
import com.quhong.data.vo.RoomVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BlockTnConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.service.GameRoomService;
import com.quhong.utils.RequestUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 房间模块/游戏房
 */
@RestController
@RequestMapping("/room_service/game_room")
public class GameRoomController extends AbstractRoomMicController {

    private static final Logger logger = LoggerFactory.getLogger(GameRoomController.class);

    @Resource
    private GameRoomService gameRoomService;

    /**
     * 创建房间前获取游戏信息
     */
    @RequestMapping("preCreate")
    private String preCreate(HttpServletRequest request) {
        HttpEnvData req = RequestUtils.getSendData(request, HttpEnvData.class);
        logger.info("preCreate room uid={} reqId={}", req.getUid(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, gameRoomService.preCreateGameRoom(req));
    }

    /**
     * 创建房间
     */
    @RequestMapping("create")
    private String create(HttpServletRequest request) {
        GameRoomDTO.CreateDTO req = RequestUtils.getSendData(request, GameRoomDTO.CreateDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        long millis = System.currentTimeMillis();
        RoomVO roomVO = gameRoomService.create(req);
        logger.info("create room roomId={} uid={} roomType={} tagId={} reqTime={} timeMillis={} body={}",
                req.getRoomId(), req.getUid(), 5, req.getTagId(), req.getRequestTime(), System.currentTimeMillis() - millis, JSON.toJSONString(req));
        return createResult(req, HttpCode.SUCCESS, roomVO);
    }

    /**
     * 加入房间
     */
    @RequestMapping("join")
    private String join(HttpServletRequest request) {
        GameRoomDTO.CreateDTO req = RequestUtils.getSendData(request, GameRoomDTO.CreateDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        long millis = System.currentTimeMillis();
        RoomVO roomVO = gameRoomService.enterRoom(req);
        logger.info("join room roomId={} uid={} enterRoomType={} reqTime={} timeMillis={}",
                req.getRoomId(), req.getUid(), req.getEnterRoomType(), req.getRequestTime(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, roomVO);
    }

    /**
     * 退出房间 (正常退出房间后通过此接口上报，以便统计房间人数)
     */
    @RequestMapping("quit")
    private String quit(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        long millis = System.currentTimeMillis();
        gameRoomService.quitRoom(req);
        logger.info("quit room roomId={} uid={} reqTime={} requestId={} timeMillis={}",
                req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 房间内切换游戏类型
     */
    @RequestMapping("switchGame")
    private String switchGame(HttpServletRequest request) {
        GameRoomDTO.CreateDTO req = RequestUtils.getSendData(request, GameRoomDTO.CreateDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        req.setEnterRoomType(RoomConfigConstant.ENTER_ROOM_CREAT_TYPE);
        req.setSwipe(1);// 房间内切换游戏
        long millis = System.currentTimeMillis();
        RoomGameConfigVO vo = gameRoomService.switchGame(req);
        logger.info("switchGame room roomId={} uid={} reqTime={} requestId={} timeMillis={}",
                req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 邀请用户玩游戏
     */
    @RequestMapping("inviteUser")
    private String inviteUser(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        long millis = System.currentTimeMillis();
        gameRoomService.inviteUser(req);
        logger.info("quit room roomId={} uid={} reqTime={} requestId={} timeMillis={}", req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, null);
    }
}
