package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.quhong.data.dto.*;
import com.quhong.data.vo.RoomActionLogVO;
import com.quhong.data.vo.RoomInfoVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomHttpCode;
import com.quhong.handler.HttpEnvData;
import com.quhong.service.RoomBlacklistService;
import com.quhong.service.RoomSettingsService;
import com.quhong.utils.RequestUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间服务/房间设置
 */
@RestController
@RequestMapping("${baseUrl}")
public class RoomSettingsController extends AbstractRoomMicController {
    private static final Logger logger = LoggerFactory.getLogger(RoomSettingsController.class);

    @Resource
    private RoomSettingsService roomSettingsService;
    @Resource
    private RoomBlacklistService roomBlacklistService;

    /**
     * 房间设置
     */
    @RequestMapping("roomSetting")
    private String roomSetting(HttpServletRequest request) {
        RoomSettingsDTO req = RequestUtils.getSendData(request, RoomSettingsDTO.class);
        logger.info("room setting roomId={} uid={} reqId={}", req.getRoomId(), req.getUid(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, roomSettingsService.roomSetting(req));
    }

    /**
     * 房间标签列表
     */
    @RequestMapping("getTagList")
    private String getTagList(HttpServletRequest request) {
        RoomSettingsDTO req = RequestUtils.getSendData(request, RoomSettingsDTO.class);
        logger.info("get room tag list. roomId={} slang={} reqId={}", req.getRoomId(), req.getSlang(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, roomSettingsService.getTagList(req));
    }

    /**
     * 更新房间标签
     */
    @RequestMapping("updateTag")
    private String updateTag(HttpServletRequest request) {
        RoomSettingsDTO req = RequestUtils.getSendData(request, RoomSettingsDTO.class);
        logger.info("update room tag roomId={} uid={} reqId={}", req.getRoomId(), req.getUid(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, roomSettingsService.updateTag(req));
    }

    /**
     * 设置/修改/取消房间密码
     */
    @RequestMapping("set_pwd")
    private String setPwd(HttpServletRequest request) {
        RoomPwdDTO req = RequestUtils.getSendData(request, RoomPwdDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        logger.info("set room pwd. roomId={} uid={} opt={} reqTime={} requestId={}", req.getRoomId(), req.getUid(), req.getOpt(), req.getRequestTime(), req.getRequestId());
        if (StringUtils.isEmpty(req.getUid()) || StringUtils.isEmpty(req.getRoomId())) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        if (!RoomUtils.getRoomHostId(req.getRoomId()).equals(req.getUid())) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        return createResult(req, HttpCode.SUCCESS, roomSettingsService.setRoomPwd(req));
    }

    /**
     * 获得房间信息
     */
    @RequestMapping("get_room_info")
    private String getRoomInfo(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        long millis = System.currentTimeMillis();
        RoomInfoVO vo = roomSettingsService.getRoomInfo(req);
        logger.info("get room info roomId={} uid={} reqTime={} requestId={} timeMillis={}",
                req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 更新房间信息
     */
    @RequestMapping("update_room_info")
    private String updateRoomInfo(HttpServletRequest request) {
        RoomInfoDTO req = RequestUtils.getSendData(request, RoomInfoDTO.class);
        logger.info("update room info. roomId={} uid={} theme={}, roomInfoDTO={}", req.getRoomId(), req.getUid(), req.getTheme(), JSON.toJSONString(req));
        return createResult(req, HttpCode.SUCCESS, roomSettingsService.updateRoomInfo(req));
    }

    /**
     * 禁止公屏聊天
     */
    @RequestMapping("chat/lock")
    private String lockRoomChat(HttpServletRequest request) {
        BanRoomChatDTO req = RequestUtils.getSendData(request, BanRoomChatDTO.class);
        logger.info("ban room chat. requestId={} uid={} roomId={} opt={}", req.getRequestId(), req.getUid(), req.getRoom_id(), req.getOpt());
        roomSettingsService.lockRoomChat(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 能否发送图片开关
     */
    @RequestMapping("pic/lock")
    private String lockRoomPicture(HttpServletRequest request) {
        RoomSendPicDTO req = RequestUtils.getSendData(request, RoomSendPicDTO.class);
        logger.info("ban room send picture. requestId={} uid={} roomId={} opt={}", req.getRequestId(), req.getUid(), req.getRoom_id(), req.getOpt());
        roomSettingsService.lockRoomPicture(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 检查房间内发图片权限
     */
    @RequestMapping("pic/check")
    private String sendPicCheck(HttpServletRequest request) {
        RoomSendPicDTO req = RequestUtils.getSendData(request, RoomSendPicDTO.class);
        logger.info("check room send picture permission. requestId={} uid={} roomId={} ", req.getRequestId(), req.getUid(), req.getRoom_id());
        roomSettingsService.sendPicCheck(req, true);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 限制房间内发消息用户等级
     */
    @RequestMapping("/text/limit")
    private String set(HttpServletRequest request) {
        BanRoomChatDTO req = RequestUtils.getSendData(request, BanRoomChatDTO.class);
        logger.info("set send text limit in room. roomId={} uid={} limit={} ", req.getRoom_id(), req.getUid(), req.getLimit());
        roomSettingsService.updateTextLimit(req);
        return createResult(req, RoomHttpCode.SUCCESS, null);
    }

    /**
     * 房主或者副房主清除房间公屏消息
     */
    @RequestMapping("/clearMsg")
    private String clearMsg(HttpServletRequest request) {
        HttpEnvData req = RequestUtils.getSendData(request, HttpEnvData.class);
        logger.info("clearMsg. roomId={} uid={}", req.getRoomId(), req.getUid());
        roomSettingsService.clearMsg(req);
        return createResult(req, RoomHttpCode.SUCCESS, null);
    }

    /**
     * 分页获取房间黑名单
     */
    @RequestMapping("/room_blocked_list")
    private String roomBlockedList(HttpServletRequest request) {
        PageDTO req = RequestUtils.getSendData(request, PageDTO.class);
        logger.info("roomBlockedList. roomId={} uid={}", req.getRoomId(), req.getUid());
        if (null == req.getRoomId()) {
            req.setRoomId(RoomUtils.formatRoomId(req.getUid()));
        }
        return createResult(req, RoomHttpCode.SUCCESS, roomBlacklistService.roomBlockedList(req.getRoomId(), req.getPage()));
    }

    /**
     * 搜索房间黑名单用户
     */
    @RequestMapping("/search_room_block")
    private String searchRoomBlocked(HttpServletRequest request) {
        RoomBlockUserDTO req = RequestUtils.getSendData(request, RoomBlockUserDTO.class);
        logger.info("searchRoomBlocked. roomId={} uid={} key={}", req.getRoomId(), req.getUid(), req.getKey());
        return createResult(req, RoomHttpCode.SUCCESS, roomBlacklistService.searchRoomBlackList(req.getRoomId(), req.getKey()));
    }

    /**
     * 删除房间黑名单
     */
    @RequestMapping("/remove_room_block")
    private String removeRoomBlocked(HttpServletRequest request) {
        RoomBlockUserDTO req = RequestUtils.getSendData(request, RoomBlockUserDTO.class);
        logger.info("removeRoomBlocked. roomId={} uid={} aid={}", req.getRoomId(), req.getUid(), req.getAid());
        return createResult(req, RoomHttpCode.SUCCESS, roomBlacklistService.removeRoomBlackList(req.getRoomId(), req.getAid(), req.getUid()));
    }

    /**
     * 检查征服活动情况
     */
    @RequestMapping("/room/conquer/check")
    private String checkConquerGame(HttpServletRequest request) {
        RoomSendPicDTO req = RequestUtils.getSendData(request, RoomSendPicDTO.class);
        return createResult(req, RoomHttpCode.SUCCESS, roomSettingsService.checkConquerGame(req.getRoom_id(), req.getUid()));
    }

    /**
     * 房间行为操作记录
     */
    @RequestMapping("/room_action_records")
    private HttpResult<PageVO<RoomActionLogVO>> roomActionRecords(@RequestBody RoomActionLogDTO req) {
        return HttpResult.getOk(roomSettingsService.roomActionRecords(req));
    }
}
