package com.quhong.controllers;

import com.quhong.data.dto.RoomLikeDTO;
import com.quhong.data.vo.RoomLikeRankingListVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.WebController;
import com.quhong.service.RoomLikeService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间点赞
 *
 * <AUTHOR>
 * @date 2023/8/3
 */
@RestController
@RequestMapping("${baseUrl}")
public class RoomLikeController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(RoomLikeController.class);

    @Resource
    private RoomLikeService roomLikeService;

    /**
     * 房间点赞
     */
    @RequestMapping("like")
    private String roomLike(HttpServletRequest request) {
        RoomLikeDTO req = RequestUtils.getSendData(request, RoomLikeDTO.class);
        if (StringUtils.isEmpty(req.getRoomId())) {
            logger.error("room like param error, roomId is empty.");
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        // logger.info("room like. roomId={} uid={} likesNum={}", req.getRoomId(), req.getUid(), req.getLikesNum());
        return createResult(req, HttpCode.SUCCESS, roomLikeService.roomLike(req));
    }

    /**
     * 房间点赞排行榜
     */
    @RequestMapping("likeRankingList")
    private String likeRankingList(HttpServletRequest request) {
        RoomLikeDTO req = RequestUtils.getSendData(request, RoomLikeDTO.class);
        logger.info("room like ranking list. roomId={} uid={}", req.getRoomId(), req.getUid());
        RoomLikeRankingListVO vo = roomLikeService.getLikeRankingList(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }
}
