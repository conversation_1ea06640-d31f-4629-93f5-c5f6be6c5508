package com.quhong.controllers;

import com.quhong.data.dto.RoomRocketDTO;
import com.quhong.data.vo.RocketRewardVO;
import com.quhong.data.vo.RoomRocketV2VO;
import com.quhong.data.vo.RoomRocketVO;
import com.quhong.enums.HttpCode;
import com.quhong.service.RoomRocketService;
import com.quhong.service.RoomRocketV2Service;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间火箭
 * 发送礼物集能量爆奖励
 *
 * <AUTHOR>
 * @date 2022/12/17
 */
@RestController
@RequestMapping("${baseUrl}")
public class RoomRocketController extends AbstractRoomMicController {

    public static final Logger logger = LoggerFactory.getLogger(RoomRocketController.class);

    @Resource
    private RoomRocketService roomRocketService;

    @Resource
    private RoomRocketV2Service roomRocketV2Service;

    /**
     * 获取房间火箭活动信息
     */
    @RequestMapping("getRocketInfo")
    private String getRocketInfo(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        RoomRocketDTO req = RequestUtils.getSendData(request, RoomRocketDTO.class);
        logger.info("get room rocket info. roomId={} uid={}", req.getRoomId(), req.getUid());
        RoomRocketVO vo = roomRocketService.getRocketInfo(req);
//        logger.info("get room rocket info. roomId={} uid={} cost={}", req.getRoomId(), req.getUid(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取房间火箭奖励
     */
    @RequestMapping("getRocketReward")
    private String getRocketReward(HttpServletRequest request) {
        RoomRocketDTO req = RequestUtils.getSendData(request, RoomRocketDTO.class);
//        logger.info("get room rocket info. roomId={} uid={} boxId={}", req.getRoomId(), req.getUid(), req.getBoxId());
        RocketRewardVO vo = roomRocketService.getRocketReward(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取房间火箭和宝箱信息
     */
    @RequestMapping("getRocketAndBoxInfo")
    private String getRocketAndBoxInfo(HttpServletRequest request) {
        RoomRocketDTO req = RequestUtils.getSendData(request, RoomRocketDTO.class);
//        logger.info("get room rocket and box info. roomId={} uid={}", req.getRoomId(), req.getUid());
        return createResult(req, HttpCode.SUCCESS, roomRocketService.getRocketAndBoxInfo(req.getRoomId(), req.getUid()));
    }




    /**
     * 获取房间火箭v2活动信息
     */
    @RequestMapping("getRocketInfoV2")
    private String getRocketInfoV2(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        RoomRocketDTO req = RequestUtils.getSendData(request, RoomRocketDTO.class);
        logger.info("get room rocket info v2. roomId={} uid={}", req.getRoomId(), req.getUid());
        RoomRocketV2VO vo = roomRocketV2Service.getRocketInfo(req.getRoomId(), req.getUid());
//        logger.info("get room rocket info v2. roomId={} uid={} cost={}", req.getRoomId(), req.getUid(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取房间火箭v2活动详细信息
     */
    @RequestMapping("getRocketDetailInfoV2")
    private String getRocketDetailInfoV2(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        RoomRocketDTO req = RequestUtils.getSendData(request, RoomRocketDTO.class);
//        logger.info("getRocketDetailInfoV2. roomId={} uid={}", req.getRoomId(), req.getUid());
        RoomRocketV2VO vo = roomRocketV2Service.getRocketDetailInfo(req);
//        logger.info("getRocketDetailInfoV2. roomId={} uid={} cost={}", req.getRoomId(), req.getUid(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取房间火箭v2总榜单
     */
    @RequestMapping("getRocketRoomRank")
    private String getRocketRoomRank(HttpServletRequest request) {
        RoomRocketDTO req = RequestUtils.getSendData(request, RoomRocketDTO.class);
//        logger.info("getRocketRewardV2. roomId={} uid={} boxId={} rocketLevel={}",
//                req.getRoomId(), req.getUid(), req.getBoxId(), req.getRocketLevel());
        RoomRocketV2VO vo = roomRocketV2Service.getRocketRoomRank(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取房间火箭v2奖励弹窗
     */
    @RequestMapping("getRocketRewardV2")
    private String getRocketRewardV2(HttpServletRequest request) {
        RoomRocketDTO req = RequestUtils.getSendData(request, RoomRocketDTO.class);
//        logger.info("getRocketRewardV2. roomId={} uid={} boxId={} rocketLevel={}",
//                req.getRoomId(), req.getUid(), req.getBoxId(), req.getRocketLevel());
        RoomRocketV2VO vo = roomRocketV2Service.getRocketReward(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }
}
