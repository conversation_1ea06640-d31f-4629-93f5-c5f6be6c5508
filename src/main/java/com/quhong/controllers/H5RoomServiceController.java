package com.quhong.controllers;

import com.quhong.data.vo.RoomEventSupportVO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.HttpEnvData;
import com.quhong.service.RoomEventService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 房间模块h5接口
 */
@RestController
@RequestMapping(value = "/room_service/h5/")
public class H5RoomServiceController {

    private static final Logger logger = LoggerFactory.getLogger(H5RoomServiceController.class);

    @Resource
    private RoomEventService roomEventService;

    /**
     * 房间活动扶持政策宣传页
     */
    @RequestMapping("roomEventSupportInfo")
    public HttpResult<RoomEventSupportVO> roomEventSupportInfo(@RequestBody HttpEnvData req) {
        long millis = System.currentTimeMillis();
        RoomEventSupportVO vo = roomEventService.roomEventSupportInfo(req);
        logger.info("h5 roomEventSupportInfo. uid={} roomId={} timeMillis={}", req.getUid(), req.getRoomId(),
                System.currentTimeMillis() - millis);
        return HttpResult.getOk(vo);
    }

}
