package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BlockTnConstant;
import com.quhong.enums.HttpCode;
import com.quhong.handler.HttpEnvData;
import com.quhong.service.LuckyDiceService;
import com.quhong.service.RoomNewService;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.PageVO;
import com.quhong.vo.RoomMicListVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间服务/房间业务
 */
@RestController
@RequestMapping("/room_service/")
public class RoomNewController extends AbstractRoomMicController {
    private static final Logger logger = LoggerFactory.getLogger(RoomNewController.class);

    @Resource
    private RoomNewService roomNewService;
    @Resource
    private LuckyDiceService luckyDiceService;

    /**
     * 创建房间前获取title等信息
     */
    @RequestMapping("preCreate")
    private String preCreate(HttpServletRequest request) {
        HttpEnvData req = RequestUtils.getSendData(request, HttpEnvData.class);
        logger.info("preCreate room uid={} reqId={}", req.getUid(), req.getRequestId());
        if (AppVersionUtils.versionCheck(8592, req)) {
            ApiResult<String> block = checkBlock(req, BlockTnConstant.BLOCK_CREATE_ROOM);
            if (block.isError()) {
                return createError(req, block.getCode());
            }
        }
        return createResult(req, HttpCode.SUCCESS, roomNewService.preCreate(req));
    }

    /**
     * 创建房间
     */
    @RequestMapping("create")
    private String create(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        long millis = System.currentTimeMillis();
        ApiResult<String> block = checkBlock(req, BlockTnConstant.BLOCK_CREATE_ROOM);
        if (block.isError()) {
            return createError(req, block.getCode());
        }
        logger.info("create room roomId={} uid={} body={}", req.getRoomId(), req.getUid(), JSON.toJSONString(req));
        RoomVO roomVO = roomNewService.create(req);
        logger.info("create room roomId={} uid={} roomType={} tagId={} reqTime={} timeMillis={} body={}",
                req.getRoomId(), req.getUid(), req.getRoomType(), req.getTagId(), req.getRequestTime(), System.currentTimeMillis() - millis, JSON.toJSONString(req));
        return createResult(req, HttpCode.SUCCESS, roomVO);
    }

    /**
     * 加入房间
     */
    @RequestMapping("join")
    private String join(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        long millis = System.currentTimeMillis();
        RoomVO roomVO = roomNewService.joinRoom(req);
        logger.info("join room roomId={} uid={} fromScene={} reqTime={} timeMillis={}",
                req.getRoomId(), req.getUid(), req.getFromScene(), req.getRequestTime(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, roomVO);
    }

    /**
     * 高频接口：获取麦位列表
     */
    @RequestMapping("mic_list")
    private String micList(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        logger.info("micList info: {}", JSONObject.toJSONString(req));
        roomIdCheck(req.getRoomId());
        long millis = System.currentTimeMillis();
        RoomMicListVo roomMicList = roomNewService.getRoomMicList(req);
//        logger.info("get room mic list roomId={} uid={} timeMillis={}", req.getRoomId(), req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, roomMicList);
    }

    /**
     * 校验进房密码，成功后返回麦位列表和流信息
     */
    @RequestMapping("pwd_check")
    private String pwdCheck(HttpServletRequest request) {
        PwdCheckDTO req = RequestUtils.getSendData(request, PwdCheckDTO.class);
        roomIdCheck(req.getRoomId());
        req.setIp(RequestUtils.getIpAddress(request));
        logger.info("pwd check, roomId={} uid={} pwd={}", req.getRoomId(), req.getUid(), req.getPwd());
        return createResult(req, HttpCode.SUCCESS, roomNewService.pwdCheck(req));
    }

    /**
     * 获取声网RTM授权
     * https://docs.agora.io/cn/Real-time-Messaging/token_server_rtm
     */
    @RequestMapping("getAgoraRtmToken")
    private String getAgoraRtmToken(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        logger.info("get agora rtm token, roomId={} uid={}", req.getRoomId(), req.getUid());
        return createResult(req, HttpCode.SUCCESS, roomNewService.getAgoraRtmToken(req.getUid()));
    }

    /**
     * 获取即构或声网RTC授权
     */
    @RequestMapping("getToken")
    private String getToken(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        logger.info("get token, roomId={} uid={}", req.getRoomId(), req.getUid());
        return createResult(req, HttpCode.SUCCESS, roomNewService.getToken(req));
    }

    /* ==============以下为迁移的新接口================ */

    /**
     * 进入房间 (成功进入房间后通过此接口上报，以便统计房间人数)
     */
    @RequestMapping("enter")
    private String enter(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        roomNewService.enterRoom(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 退出房间 (正常退出房间后通过此接口上报，以便统计房间人数)
     */
    @RequestMapping("quit")
    private String quid(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        long millis = System.currentTimeMillis();
        roomNewService.quitRoom(req);
        logger.info("quit room roomId={} uid={} reqTime={} requestId={} timeMillis={}",
                req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 获取房间密码
     */
    @RequestMapping("get_pwd")
    private String getRoomPwd(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        RoomPwdVO vo = roomNewService.getRoomPwd(req);
        logger.info("get room pwd. roomId={} uid={} reqTime={} requestId={}", req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 关注房间
     */
    @RequestMapping("follow/add")
    private String addFollow(HttpServletRequest request) {
        RoomNewDTO req = RequestUtils.getSendData(request, RoomNewDTO.class);
        logger.info("addFollow roomId={} uid={} requestId={}", req.getRoomId(), req.getUid(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, roomNewService.addFollow(req));
    }

    /**
     * 取消关注房间
     */
    @RequestMapping("follow/del")
    private String delFollow(HttpServletRequest request) {
        RoomNewDTO req = RequestUtils.getSendData(request, RoomNewDTO.class);
        logger.info("delFollow roomId={} uid={} requestId={}", req.getRoomId(), req.getUid(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, roomNewService.delFollow(req));
    }

    /**
     * 搜索
     */
    @RequestMapping("search")
    private String search(HttpServletRequest request) {
        RoomNewDTO req = RequestUtils.getSendData(request, RoomNewDTO.class);
        logger.info("room search uid={} key={} requestId={}", req.getUid(), req.getKey(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, roomNewService.search(req));
    }

    /**
     * 搜索
     */
    @RequestMapping("searchByKey")
    private String searchByKey(HttpServletRequest request) {
        RoomNewDTO req = RequestUtils.getSendData(request, RoomNewDTO.class);
        logger.info("room search uid={} key={} requestId={}", req.getUid(), req.getKey(), req.getRequestId());
        return createResult(req, HttpCode.SUCCESS, roomNewService.searchByKey(req));
    }

    /**
     * 特殊用户上传房间主题图片
     */
    @RequestMapping("upload/theme")
    private String uploadTheme(HttpServletRequest request) {
        RoomNewDTO req = RequestUtils.getSendData(request, RoomNewDTO.class);
        logger.info("uploadTheme roomId={} uid={} roomThem={}", req.getRoomId(), req.getUid(), req.getRoom_them());
        return createResult(req, HttpCode.SUCCESS, roomNewService.uploadTheme(req));
    }

    /**
     * 传主题时提示用户要vip4以上
     */
    @RequestMapping("check/upload/theme")
    private String checkUploadTheme(HttpServletRequest request) {
        RoomNewDTO req = RequestUtils.getSendData(request, RoomNewDTO.class);
        logger.info("check upload theme roomId={} uid={}", req.getRoomId(), req.getUid());
        roomNewService.checkUploadTheme(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 房间内banner图标按钮
     */
    @RequestMapping("banner/new")
    private String getRoomBanner(HttpServletRequest request) {
//        long millis = System.currentTimeMillis();
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        req.setIp(RequestUtils.getIpAddress(request));
        RoomBannerVO vo = roomNewService.getRoomBanner(req);
//        logger.info("get room banner. roomId={} uid={} reqTime={} requestId={} timeMillis={}", req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 表情资源列表
     */
    @RequestMapping("emoji")
    private String getEmojiList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        EmojiListVO vo = roomNewService.getEmojiList(req);
        logger.info("get emoji list. reqTime={} requestId={} timeMillis={}", req.getRequestTime(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 表情资源列表
     */
    @RequestMapping("emojiManger")
    private String getEmojiManger(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        MsgEmojiListVO vo = roomNewService.getMsgEmojiList(req);
        logger.info("getEmojiManger. reqTime={} requestId={} timeMillis={}", req.getRequestTime(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 房间内发送图片
     */
    @RequestMapping("pic/send")
    private String sendPicture(HttpServletRequest request) {
        RoomSendPicDTO req = RequestUtils.getSendData(request, RoomSendPicDTO.class);
        logger.info("send picture in room. requestId={} uid={} roomId={} ", req.getRequestId(), req.getUid(), req.getRoom_id());
        RoomSendPicVO vo = roomNewService.sendPicture(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 房主端欢迎语
     */
    @RequestMapping("welcome_word")
    private String sendWelcomeWord(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
//        logger.info("send welcome word in room. requestId={} uid={}", req.getRequestId(), req.getUid());
        WelcomeWordVO vo = roomNewService.sendWelcomeWord(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 房间内禁言
     */
    @RequestMapping("ban_msg")
    private String banMsg(HttpServletRequest request) {
        BanMsgDTO req = RequestUtils.getSendData(request, BanMsgDTO.class);
        logger.info("room ban msg. requestId={} uid={} aid={} roomId={}", req.getRequestId(), req.getUid(), req.getAid(), req.getRoomId());
        roomNewService.banMsg(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 房间内取消禁言
     */
    @RequestMapping("cancel/ban_msg")
    private String cancelBanMsg(HttpServletRequest request) {
        BanMsgDTO req = RequestUtils.getSendData(request, BanMsgDTO.class);
        logger.info("cancel room ban msg. requestId={} uid={} aid={} roomId={}", req.getRequestId(), req.getUid(), req.getAid(), req.getRoomId());
        roomNewService.cancelBanMsg(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 麦位静音
     */
    @RequestMapping("mic_opt")
    private String roomMicOpt(HttpServletRequest request) {
        BanMsgDTO req = RequestUtils.getSendData(request, BanMsgDTO.class);
        logger.info("room mic opt. requestId={} uid={} aid={} roomId={} opt={}", req.getRequestId(), req.getUid(), req.getAid(), req.getRoomId(), req.getOpt());
        roomNewService.roomMicOpt(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 所有麦位静音时房主调用
     */
    @RequestMapping("all_mic_down")
    private String allMicDown(HttpServletRequest request) {
        BanMsgDTO req = RequestUtils.getSendData(request, BanMsgDTO.class);
        logger.info("room all mic down. requestId={} uid={} roomId={} opt={}", req.getRequestId(), req.getUid(), req.getRoomId(), req.getOpt());
        roomNewService.allMicDown(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 管理员或者房主操作是否能开启pk竞赛
     */
    @RequestMapping("operate_pk")
    private String operatePk(HttpServletRequest request) {
        RoomPkDTO req = RequestUtils.getSendData(request, RoomPkDTO.class);
        logger.info("room operate pk. requestId={} uid={} roomId={} roomPk={}", req.getRequestId(), req.getUid(), req.getRoomId(), req.getRoom_pk());
        roomNewService.operatePk(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 幸运数字启动
     */
    @RequestMapping("start_dice")
    private String startLuckyNumber(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        logger.info("start lucky number in room. requestId={} uid={} roomId={} ", req.getRequestId(), req.getUid(), req.getRoomId());
        DiceGameVO vo = roomNewService.startLuckyNumber(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 骰子游戏
     */
    @RequestMapping("start_dice2")
    private String startDiceGame(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        logger.info("start dice game in room. requestId={} uid={} roomId={} ", req.getRequestId(), req.getUid(), req.getRoomId());
        DiceGameVO vo = roomNewService.startDiceGame(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 幸运数字V2版本
     * 可设置范围及费用
     */
    @RequestMapping("luckyNumConfig")
    private String luckyNumberConfig(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        logger.info("luckyNumSetting requestId={} uid={} roomId={}, LuckyNumSettingDTO={}", req.getRequestId(), req.getUid(), req.getRoomId(), req);
        LuckyNumConfigVO vo = roomNewService.luckyNumConfig(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }


    @RequestMapping("luckyNumSetting")
    private String luckyNumberSetting(HttpServletRequest request) {
        LuckyNumSettingDTO req = RequestUtils.getSendData(request, LuckyNumSettingDTO.class);
        logger.info("luckyNumSetting requestId={} uid={} roomId={}, LuckyNumSettingDTO={}", req.getRequestId(), req.getUid(), req.getRoomId(), req);
        roomNewService.luckyNumSetting(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    @RequestMapping("sendLuckyNum")
    private String sendLuckyNum(HttpServletRequest request) {
        RoomDTO req = RequestUtils.getSendData(request, RoomDTO.class);
        logger.info("sendLuckyNum in room. requestId={} uid={} roomId={} ", req.getRequestId(), req.getUid(), req.getRoomId());
        LuckyNumVO vo = roomNewService.sendLuckyNum(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    @RequestMapping("luckyNumRecord")
    private String luckyNumRecord(HttpServletRequest request) {
        PageDTO pageDTO = RequestUtils.getSendData(request, PageDTO.class);
        logger.info("sendLuckyNum in room. requestId={} uid={} page={} roomId={} ", pageDTO.getRequestId(), pageDTO.getUid(), pageDTO.getPage(), pageDTO.getRoomId());
        LuckyNumRecordVO vo = roomNewService.luckyNumRecord(pageDTO.getRoomId(), pageDTO.getPage());
        return createResult(pageDTO, HttpCode.SUCCESS, vo);
    }

    /**
     * 检查幸运骰子
     */
    @RequestMapping("lucky_dice/check")
    private HttpResult<LuckyDiceVO.CheckVO> checkLuckyDice(@RequestBody HttpEnvData dto) {
        logger.info("check lucky dice roomId={} uid={}", dto.getRoomId(), dto.getUid());
        return HttpResult.getOk(luckyDiceService.checkLuckyDice(dto.getUid()));
    }

    /**
     * 幸运骰子投注
     */
    @RequestMapping("lucky_dice/throw")
    private HttpResult<LuckyDiceVO.ResultVO> throwLuckyDice(@RequestBody LuckyDiceDTO dto) {
        logger.info("throw lucky dice roomId={} uid={} fee={}", dto.getRoomId(), dto.getUid(), dto.getFee());
        return HttpResult.getOk(luckyDiceService.throwLuckyDice(dto));
    }

    /**
     * 幸运骰子记录
     */
    @RequestMapping("lucky_dice/record")
    private HttpResult<PageVO<LuckyDiceVO.RecordVO>> luckyDiceRecord(@RequestBody PageDTO dto) {
        logger.info("lucky dice record roomId={} uid={}", dto.getRoomId(), dto.getUid());
        return HttpResult.getOk(luckyDiceService.luckyDiceRecord(dto));
    }

    /**
     * 设置BC游戏开关限制
     */
    @RequestMapping("setActorRoomConfigSwitch")
    private HttpResult<Object> setActorRoomConfigSwitch(@RequestBody RoomActorConfigDTO dto) {
        logger.info("setActorRoomConfigSwitch info={}", JSONObject.toJSONString(dto));
        roomNewService.setActorRoomConfigSwitch(dto);
        return HttpResult.getOk();
    }
}
