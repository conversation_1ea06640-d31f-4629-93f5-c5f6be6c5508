package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.MomentConstant;
import com.quhong.constant.MomentHttpCode;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.exception.CommonException;
import com.quhong.feign.IVideoService;
import com.quhong.handler.HttpEnvData;
import com.quhong.http.AsyncHttpClient;
import com.quhong.intercepters.UserInterceptor;
import com.quhong.mongo.data.MomentData;
import com.quhong.service.MomentService;
import com.quhong.service.NearbyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 朋友圈
 */
@RestController
@RequestMapping(value = "/moment")
public class MomentController {
    private static final Logger logger = LoggerFactory.getLogger(MomentController.class);
    private static final String SYS_DEFAULT_AVATARS = "https://cloudcdn.qmovies.tv/activity/op_1664443249_youstar_official.png";
    private static final String YOUTUBE_REGEX = "^.*((youtu\\.be/)|(v/)|(/u/w/)|(embed/)|(shorts/)|(watch\\?v=))([^#&?]*).*";
    private static final Pattern YOUTUBE_PATTERN = Pattern.compile(YOUTUBE_REGEX, Pattern.CASE_INSENSITIVE);
    private static final String YOUTUBE_HOST = "youtu";
    private static final Pattern TITLE_PATTERN = Pattern.compile("<title>.*?</title>", Pattern.CANON_EQ);
    private static final Map<String, String> HEADER_MAP = new HashMap<String, String>() {{
        put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.102 Safari/537.36 Edg/104.0.1293.63");
    }};
    private static final Set<String> BLACK_SET = new HashSet<String>() {{
        add("xxx.com");
    }};
    private static final Set<String> YOUSTAR_ACTIVITY_SET = new HashSet<String>() {{
        add("qmovies.tv");
        add("youstar.top");
        add("youstar.live");
    }};

    @Resource
    private MomentService momentService;
    @Resource
    private AsyncHttpClient asyncHttpClient;
    @Resource
    private IVideoService videoService;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private NearbyService nearbyService;

    public String getYoutubeVideoId(String youtubeUrl) {
        Matcher matcher = YOUTUBE_PATTERN.matcher(youtubeUrl);
        if (matcher.matches()) {
            String groupMatch = matcher.group(8);
            if (groupMatch != null && groupMatch.length() == 11)
                return groupMatch;
        }
        return null;
    }

    /**
     * 朋友圈链接检测
     * Youtube链接返回视频信息，普通链接返回title
     */
    @RequestMapping("checkLink")
    public DeferredResult<HttpResult<MomentData.Quote>> checkLink(@RequestBody MomentDTO req) {
        DeferredResult<HttpResult<MomentData.Quote>> result = new DeferredResult<>(10 * 1000L, HttpResult.getOk(""));
        logger.info("check link uid={} url={}", req.getUid(), req.getKey());
        if (ObjectUtils.isEmpty(req.getKey()) || !req.getKey().startsWith("http")) {
            throw new CommonException(MomentHttpCode.LINK_VALID);
        }
        for (String url : BLACK_SET) {
            if (req.getKey().contains(url)) {
                throw new CommonException(MomentHttpCode.DIRTY_WORD);
            }
        }
        try {
            String url = req.getKey().replace(" ", "");
            UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url).build();
            if (null == uriComponents.getHost()) {
                logger.error("checkLink error uid={} url={}", req.getUid(), req.getKey());
                throw new CommonException(MomentHttpCode.LINK_VALID);
            }
            if (uriComponents.getHost().contains(YOUTUBE_HOST)) {
                String videoId = getYoutubeVideoId(url);
                if (!ObjectUtils.isEmpty(videoId)) {
                    VideoDTO apiDTO = new VideoDTO();
                    apiDTO.setVideoId(videoId);
                    logger.info("get video info. uid={} url={} videoId={}", req.getUid(), url, videoId);
                    HttpResult<YoutubeSourceData> sourceApiResult = videoService.getVideo(apiDTO);
                    if (sourceApiResult.isError()) {
                        throw new CommonException(MomentHttpCode.SERVER_ERROR);
                    }
                    if (null != sourceApiResult.getData()) {
                        YoutubeSourceData sourceData = sourceApiResult.getData();
                        MomentData.Quote quote = new MomentData.Quote();
                        quote.setType(MomentConstant.QUOTE_YOUTUBE_LINK);
                        quote.setContent(sourceData.getTitle());
                        quote.setIcon(sourceData.getThumbnails());
                        quote.setAction(url);
                        quote.setVideoId(sourceData.getVideoId());
                        result.setResult(HttpResult.getOk(quote));
                        return result;
                    }
                }
            }
            asyncHttpClient.get(url, HEADER_MAP, data -> {
                if (ObjectUtils.isEmpty(data.getBody())) {
                    throw new CommonException(MomentHttpCode.LINK_VALID);
                }
                StringBuilder sb = new StringBuilder();
                Matcher ma = TITLE_PATTERN.matcher(data.getBody());
                while (ma.find()) {
                    sb.append(ma.group());
                }
                MomentData.Quote quote = new MomentData.Quote();
                String title = sb.toString().replaceAll("<.*?>", "").replaceAll("&nbsp;", " ");
                quote.setContent(title.length() > 30 ? title.substring(0, 30) + "..." : title);
                quote.setType(MomentConstant.QUOTE_LINK);
                for (String host : YOUSTAR_ACTIVITY_SET) {
                    if (uriComponents.getHost().contains(host)) {
                        quote.setType(MomentConstant.QUOTE_OFFICIAL_LINK);
                        quote.setIcon(SYS_DEFAULT_AVATARS);
                        // 构建官方连接的title
                        if (ObjectUtils.isEmpty(quote.getContent())) {
                            List<String> titleList = uriComponents.getQueryParams().get("title");
                            if (!CollectionUtils.isEmpty(titleList)) {
                                try {
                                    quote.setContent(URLDecoder.decode(titleList.get(0) == null ? "" : titleList.get(0), "utf-8"));
                                } catch (Exception ignored) {
                                }
                            }
                            List<String> iconList = uriComponents.getQueryParams().get("icon");
                            if (!CollectionUtils.isEmpty(iconList)) {
                                try {
                                    quote.setIcon(URLDecoder.decode(iconList.get(0) == null ? "" : iconList.get(0), "utf-8"));
                                } catch (Exception ignored) {
                                }
                            }
                        }
                        break;
                    }
                }
                quote.setAction(url);
                if (ObjectUtils.isEmpty(quote.getContent())) {
                    quote.setContent(url);
                }
                result.setResult(HttpResult.getOk(quote));
            });
        } catch (Exception e) {
            logger.error("checkLink error uid={} url={} {}", req.getUid(), req.getKey(), e.getMessage());
            throw new CommonException(MomentHttpCode.LINK_VALID);
        }
        return result;
    }

    /**
     * 发布朋友圈
     */
    @RequestMapping("publish")
    private HttpResult<Object> publish(@RequestBody PublishDTO req) {
        long timeMillis = System.currentTimeMillis();
        momentService.publish(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("publish moment uid={} cost={} req={}", req.getUid(), cost, JSON.toJSONString(req));
        return HttpResult.getOk();
    }

    /**
     * 发布前调用，校验发布许可、获取朋友圈背景信息等
     */
    @RequestMapping("prePublish")
    private HttpResult<PrePublishVO> prePublish(@RequestBody MomentTopicDTO req) {
        logger.info("pre publish moment uid={}", req.getUid());
        return HttpResult.getOk(momentService.prePublish(req));
    }

    /**
     * 动态详情
     */
    @RequestMapping("moment_detail")
    private HttpResult<MomentInfoVO> momentDetail(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        MomentInfoVO momentInfoVO = momentService.detail(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("moment detail uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk(momentInfoVO);
    }

    /**
     * 访客动态详情
     */
    @RequestMapping(UserInterceptor.VISITOR + "moment_detail")
    private HttpResult<MomentInfoVO> visitorMomentDetail(@RequestBody MomentDTO req) {
        req.setUid("");
        long timeMillis = System.currentTimeMillis();
        MomentInfoVO momentInfoVO = momentService.detailFromCache(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("visitor moment detail uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk(momentInfoVO);
    }

    /**
     * 删除朋友圈
     */
    @RequestMapping("delete")
    private HttpResult<Object> delete(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        momentService.delete(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("delete moment uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk();
    }

    /**
     * 查看朋友圈
     */
    @RequestMapping("show/publish")
    private HttpResult<MomentVO> showPublish(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        MomentVO momentVO = momentService.showPublish(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("show moment uid={} opt={} cost={}", req.getUid(), req.getOpt(), cost);
        return HttpResult.getOk(momentVO);
    }

    /**
     * 访客查看朋友圈
     */
    @RequestMapping(UserInterceptor.VISITOR + "show/publish")
    private HttpResult<MomentVO> visitorShowPublish(@RequestBody MomentDTO req) {
        req.setUid("");
        long timeMillis = System.currentTimeMillis();
        MomentVO momentVO = momentService.getPublicMomentFromCache(req);
        if (2 == req.getOpt()) {
            momentVO.setList(Collections.emptyList());
        }
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("visitor show moment uid={} opt={} cost={}", req.getUid(), req.getOpt(), cost);
        return HttpResult.getOk(momentVO);
    }

    /**
     * 获取@用户列表
     */
    @RequestMapping("show/get_friends_list")
    private HttpResult<AtListVO> getFriendsList(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        AtListVO listVO = momentService.getFriendsList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("show friends list uid={} cost={}", req.getUid(), cost);
        return HttpResult.getOk(listVO);
    }

    /**
     * 搜索用户
     */
    @RequestMapping("show/search")
    private HttpResult<FriendSearchVO> search(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        FriendSearchVO friendSearchVO = momentService.search(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("search friends uid={} key={} cost={}", req.getUid(), req.getKey(), cost);
        return HttpResult.getOk(friendSearchVO);
    }

    /**
     * 评论
     */
    @RequestMapping("comment")
    private HttpResult<CommentResultVO> comment(@RequestBody CommentDTO req) {
        logger.info("comment moment uid={} mid={} commentId={}", req.getUid(), req.getMid(), req.getComment_id());
        return HttpResult.getOk(momentService.comment(req));
    }

    /**
     * 点击评论数获取合并的评论详情
     */
    @RequestMapping("comment_detail")
    private HttpResult<CommentListVO> commentDetail(@RequestBody CommentDTO req) {
        long timeMillis = System.currentTimeMillis();
        CommentListVO commentDetail = momentService.commentDetail(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("comment detail uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk(commentDetail);
    }

    /**
     * 新接口：评论列表
     */
    @RequestMapping("commentList")
    private HttpResult<MomentVO> commentList(@RequestBody CommentDTO req) {
        if (req.getPage() <= 0) {
            req.setPage(1);
        }
        long timeMillis = System.currentTimeMillis();
        MomentVO momentVO = momentService.commentList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("comment list uid={} mid={} sortBy={} cost={}", req.getUid(), req.getMid(), req.getSortBy(), cost);
        return HttpResult.getOk(momentVO);
    }

    /**
     * 点赞评论
     */
    @RequestMapping("commentLike")
    private HttpResult<Object> commentLike(@RequestBody CommentDTO req) {
        long timeMillis = System.currentTimeMillis();
        momentService.commentLike(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("comment like uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk();
    }

    /**
     * 删除评论
     */
    @RequestMapping("del/comment")
    private HttpResult<Object> delComment(@RequestBody CommentDTO req) {
        long timeMillis = System.currentTimeMillis();
        momentService.delComment(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("del comment moment uid={} mid={} commentId={} cost={}", req.getUid(), req.getMid(), req.getComment_id(), cost);
        return HttpResult.getOk();
    }

    /**
     * 举报评论
     */
    @RequestMapping("report/comment")
    private HttpResult<Object> reportComment(@RequestBody CommentDTO req) {
        logger.info("report comment moment uid={} mid={} commentId={}", req.getUid(), req.getMid(), req.getComment_id());
        return HttpResult.getOk();
    }

    /**
     * 拉黑评论
     */
    @RequestMapping("block/comment")
    private HttpResult<Object> blockComment(@RequestBody CommentDTO req) {
        logger.info("block comment moment uid={} mid={} commentId={}", req.getUid(), req.getMid(), req.getComment_id());
        return HttpResult.getOk();
    }

    /**
     * 点赞朋友圈
     */
    @RequestMapping("like")
    private HttpResult<Object> like(@RequestBody LikeDTO req) {
        long timeMillis = System.currentTimeMillis();
        momentService.like(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("like moment uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk();
    }

    /**
     * 礼物打赏
     */
    @Deprecated
    @RequestMapping("giftReward")
    private HttpResult<GiftRewordVO> giftReward(@RequestBody GiftRewardDTO req) {
        long timeMillis = System.currentTimeMillis();
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("moment gift reward uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk(momentService.giftReward(req));
    }

    /**
     * 获取打赏礼物面板
     */
    @Deprecated
    @RequestMapping("rewardPanel")
    private HttpResult<RewardPanelVO> rewardPanel(@RequestBody HttpEnvData req) {
        logger.info("moment gift reward  panel uid={}", req.getUid());
        return HttpResult.getOk(momentService.rewardPanel(req));
    }

    /**
     * 获取打赏礼物列表
     */
    @RequestMapping("rewardList")
    private HttpResult<RewardListVO> rewardList(@RequestBody RewardListDTO req) {
        logger.info("moment gift reward  list uid={}", req.getUid());
        return HttpResult.getOk(momentService.rewardList(req));
    }

    /**
     * 点赞列表
     */
    @RequestMapping("like_list")
    private HttpResult<MomentVO> likeList(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        MomentVO momentVO = momentService.likeList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("moment like list uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk(momentVO);
    }

    /**
     * 访客查看点赞列表
     */
    @RequestMapping(UserInterceptor.VISITOR + "like_list")
    private HttpResult<MomentVO> visitorLikeList(@RequestBody MomentDTO req) {
        req.setUid("");
        long timeMillis = System.currentTimeMillis();
        MomentVO momentVO = momentService.likeList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("visitor moment like list uid={} mid={} cost={}", req.getUid(), req.getMid(), cost);
        return HttpResult.getOk(momentVO);
    }

    /**
     * 举报朋友圈
     */
    @RequestMapping("report")
    private HttpResult<Object> report(@RequestBody MomentDTO req) {
        logger.info("report moment uid={} mid={}", req.getUid(), req.getMid());
        momentService.report(req);
        return HttpResult.getOk();
    }

    /**
     * 拉黑，只有管理员有拉黑的处理
     */
    @RequestMapping("block")
    private HttpResult<Object> block(@RequestBody MomentDTO req) {
        logger.info("block moment uid={} mid={}", req.getUid(), req.getMid());
        momentService.block(req);
        return HttpResult.getOk();
    }

    /**
     * 查看是否有新动态
     */
    @RequestMapping("unread/check")
    private HttpResult<UnreadCheckVO> unreadCheck(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        UnreadCheckVO unreadCheckVO = momentService.unreadCheck(req);
        basePlayerRedis.incrTokenExpireTime(req.getUid());
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("moment unread check uid={} cost={}", req.getUid(), cost);
        return HttpResult.getOk(unreadCheckVO);
    }

    /**
     * 个人主页查看动态
     */
    @RequestMapping("show/personal")
    public HttpResult<MomentVO> showPersonalMoment(@RequestBody PersonalMomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        MomentVO vo = momentService.showPersonalMoment(req.getUid(), req.getAid(), req.getPage(), req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("show personal moment. uid={} aid={} page={} cost={}", req.getUid(), req.getAid(), req.getPage(), cost);
        return HttpResult.getOk(vo);
    }

    /**
     * 新版查看对我的朋友圈点赞和评论的详情
     */
    @RequestMapping("noticeList")
    private HttpResult<MomentVO> noticeList(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        MomentVO momentVO = momentService.noticeList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("moment notice list uid={} page={} cost={}", req.getUid(), req.getPage(), cost);
        return HttpResult.getOk(momentVO);
    }

    /**
     * 新版清除通知列表
     */
    @RequestMapping("clearNotice")
    private HttpResult<Object> clearNotice(@RequestBody MomentDTO req) {
        long timeMillis = System.currentTimeMillis();
        momentService.clearNotice(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("clear notice list uid={} cost={}", req.getUid(), cost);
        return HttpResult.getOk();
    }

    /**
     * 官方账号设置置顶和取消置顶
     * opt=1 置顶， opt = 2 取消置顶 opt = 3 查询置顶截止时间
     */
    @RequestMapping("set/top")
    private HttpResult<SetTopMomentVO> setTop(@RequestBody MomentDTO req) {
        logger.info("moment set top uid={} mid={} setTopEndTime={}", req.getUid(), req.getMid(), req.getTopMomentEndTime());
        SetTopMomentVO vo = momentService.setTop(req);
        return HttpResult.getOk(vo);
    }

    /**
     * 获取朋友圈banner
     */
    @RequestMapping("banner")
    private HttpResult<BannerVO> banner(@RequestBody HttpEnvData req) {
        logger.info("get moment banner uid={}", req.getUid());
        return HttpResult.getOk(momentService.getBanner(req));
    }

    /**
     * 获取朋友圈评论推荐语
     */
    @RequestMapping("getCommentSuggest")
    private HttpResult<CommentSuggestVO> getCommentSuggest(@RequestBody HttpEnvData req) {
        logger.info("getCommentSuggest uid={}", req.getUid());
        return HttpResult.getOk(momentService.getCommentSuggest(req));
    }

    /**
     * 客户端曝光moment数据
     */
    @RequestMapping("exposureMoment")
    private HttpResult<Object> exposureMoment(@RequestBody ExposureDTO req) {
        logger.info("exposureMoment req={}", JSONObject.toJSONString(req));
        nearbyService.exposureMomentScore(req);
        return HttpResult.getOk();
    }

    /**
     * 生成SA Featured和新帖子
     */
    @RequestMapping("genFeaturedAndNewSa")
    private HttpResult<Object> genFeaturedAndNewSa(@RequestParam int startTime, @RequestParam int endTime) {
        logger.info("genFeaturedAndNewSa startTime={} endTime={}", startTime, endTime);
        momentService.genFeaturedAndNewSa(startTime, endTime);
        return HttpResult.getOk();
    }
}
