package com.quhong.controllers;

import com.quhong.data.dto.FriendApplyDTO;
import com.quhong.data.dto.FriendListDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.enums.UserInfoHttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.FriendService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 好友接口Controller
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class FriendController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(FriendController.class);

    @Resource
    private FriendService friendService;

    /**
     * 获取好友列表
     */
    @RequestMapping("/friend/list")
    public String getFriendList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FriendListDTO req = RequestUtils.getSendData(request, FriendListDTO.class);
        FriendsVO vo = friendService.getFriendsList(req);
        logger.info("get friend list. uid={} page={} requestId={} timeMillis={}", req.getUid(), req.getPage(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 新好友列表 （v8.30版本）
     */
    @RequestMapping("/friend/list_new")
    public String getFriendListNew(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FriendListDTO req = RequestUtils.getSendData(request, FriendListDTO.class);
        FriendListVO vo = friendService.getFriendListNew(req);
        logger.info("get friend list. uid={} page={} key={} requestId={} timeMillis={}", req.getUid(), req.getPage(), req.getKey(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取在线好友列表（最多展示10个）
     */
    @RequestMapping("/friend/online")
    public String getOnlineFriendList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FriendListDTO req = RequestUtils.getSendData(request, FriendListDTO.class);
        OnlineFriendVO vo = friendService.getOnlineFriendList(req);
        logger.info("get online friend list.uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取好友，关注，粉丝的列表
     */
    @RequestMapping("/friend/merge_list")
    public String getMergeList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FriendListDTO req = RequestUtils.getSendData(request, FriendListDTO.class);
        MergeListVO vo = friendService.getMergeList(req);
        logger.info("get friend list. uid={} page={} listType={} requestId={} timeMillis={}", req.getUid(), req.getPage(), req.getListType(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 好友申请列表
     */
    @RequestMapping("/friend/apply_list")
    public String getFriendApplyList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FriendListDTO req = RequestUtils.getSendData(request, FriendListDTO.class);
        FriendApplyVO vo = friendService.getFriendApplyList(req);
        logger.info("get friend apply list. uid={} page={} requestId={} timeMillis={}", req.getUid(), req.getPage(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 添加好友申请
     */
    @RequestMapping("/friend/add_apply")
    public String addFriendApply(HttpServletRequest request) {
        FriendApplyDTO req = RequestUtils.getSendData(request, FriendApplyDTO.class);
        logger.info("add friend apply. uid={} aid={} msg={} requestId={}", req.getUid(), req.getAid(), req.getMsg(), req.getRequestId());
        if (checkParamError(req.getUid(), req.getAid())) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        friendService.addFriendApply(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 批量添加好友申请
     */
    @RequestMapping("/friend/apply_multiple")
    public String addMultipleFriendApply(HttpServletRequest request) {
        FriendApplyDTO req = RequestUtils.getSendData(request, FriendApplyDTO.class);
        String uid = req.getUid();
        List<String> aids = req.getAids();
        if (CollectionUtils.isEmpty(aids)) {
            logger.info("aids is empty, add multiple friend apply fail.");
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        logger.info("add multiple friend apply. uid={} aids={} msg={} requestId={}"
                , uid, Arrays.toString(aids.toArray()), req.getMsg(), req.getRequestId());
        for (String aid : aids) {
            if (checkParamError(uid, aid)) {
                continue;
            }
            req.setAid(aid);
            friendService.addFriendApply(req);
        }
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 删除好友申请消息
     */
    @RequestMapping("/friend/delete_apply")
    public String deleteFriendApply(HttpServletRequest request) {
        FriendApplyDTO req = RequestUtils.getSendData(request, FriendApplyDTO.class);
        logger.info("delete friend apply. uid={} aid={} requestId={}", req.getUid(), req.getAid(), req.getRequestId());
        if (checkParamError(req.getUid(), req.getAid())) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        friendService.deleteFriendApply(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 处理好友申请, 同意或者拒绝
     */
    @RequestMapping("/friend/handle_apply")
    public String handleFriendApply(HttpServletRequest request) {
        FriendApplyDTO req = RequestUtils.getSendData(request, FriendApplyDTO.class);
        logger.info("handle friend apply. uid={} aid={} opt={} requestId={}", req.getUid(), req.getAid(), req.getOpt(), req.getRequestId());
        if (checkParamError(req.getUid(), req.getAid())) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        friendService.handleFriendApply(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 删除好友
     */
    @RequestMapping("/friend/delete")
    public String deleteFriend(HttpServletRequest request) {
        FriendApplyDTO req = RequestUtils.getSendData(request, FriendApplyDTO.class);
        logger.info("delete friend. uid={} aid={} requestId={}", req.getUid(), req.getAid(), req.getRequestId());
        if (checkParamError(req.getUid(), req.getAid())) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        friendService.deleteFriend(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * pk邀请好友时的搜索接口
     */
    @RequestMapping("/friend/search")
    public String getFriendSearchList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FriendListDTO req = RequestUtils.getSendData(request, FriendListDTO.class);
        String uid = req.getUid();
        String key = req.getKey();
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(key.trim())) {
            return createResult(req, UserInfoHttpCode.CODE_PARAMETER_ERR, null);
        }
        if (StringUtils.isEmpty(uid)) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        FriendSearchVO vo = friendService.getFriendSearchList(req);
        logger.info("get friend search list. uid={} key={} requestId={} timeMillis={}", key, uid, req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 推荐好友列表
     */
    @RequestMapping("/friend/recommend")
    public String getFriendRecommendList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FriendListDTO req = RequestUtils.getSendData(request, FriendListDTO.class);
        FriendRecommendVO vo = friendService.getFriendRecommendList(req);
        logger.info("get friend recommend list.uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * friends模块的好友列表，相比于me页面的信息更加详细
     */
    @RequestMapping("/friend/detail_list")
    public String getFriendDetailList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FriendListDTO req = RequestUtils.getSendData(request, FriendListDTO.class);
        FriendDetailVO vo = friendService.getFriendDetailList(req);
        logger.info("get friend detail list.uid={} page={} requestId={} timeMillis={}", req.getUid(), req.getPage(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 检测参数uid，aid是否有问题
     * true参数有问题 false参数没问题
     */
    private Boolean checkParamError(String uid, String aid) {
        return StringUtils.isEmpty(uid) || StringUtils.isEmpty(aid) || Objects.equals(uid, aid);
    }
}
