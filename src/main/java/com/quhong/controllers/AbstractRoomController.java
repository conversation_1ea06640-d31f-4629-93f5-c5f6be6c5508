package com.quhong.controllers;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.data.ActorData;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BlockTnConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.redis.BlockRedis;
import com.quhong.room.EnterRoomCheckService;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

public class AbstractRoomController extends WebController {
    private static final Logger logger = LoggerFactory.getLogger(AbstractRoomController.class);

    @Autowired
    private RoomKickRedis roomKickRedis;
    @Autowired
    protected RoomWebSender roomSender;
    @Autowired
    protected EnterRoomCheckService checkService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;

    public AbstractRoomController() {

    }

    public void roomIdCheck(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            throw new CommonException(RoomHttpCode.ROOM_PARAM_ERROR);
        }
    }

    protected ApiResult<String> checkBlock(HttpEnvData envData, int blockType) {
        String uid = envData.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (null == actorData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String tn_id = actorData.getTn_id();
        String blockTime = blockRedis.checkBlock(tn_id, blockType);
        String msgEn = "";
        String msgAr = "";
        switch (blockType) {
            case BlockTnConstant.BLOCK_MIC:
                msgEn = String.format(BlockTnConstant.MSG_BLOCK_MIC_EN, blockTime);
                msgAr = String.format(BlockTnConstant.MSG_BLOCK_MIC_AR, blockTime);
                break;
            case BlockTnConstant.BLOCK_ROOM_FILE:
                msgEn = String.format(BlockTnConstant.MSG_BLOCK_ROOM_FILE_EN, blockTime);
                msgAr = String.format(BlockTnConstant.MSG_BLOCK_ROOM_FILE_AR, blockTime);
                break;
            case BlockTnConstant.BLOCK_CREATE_ROOM:
                msgEn = String.format(BlockTnConstant.MSG_BLOCK_CREATE_ROOM_EN, blockTime);
                msgAr = String.format(BlockTnConstant.MSG_BLOCK_CREATE_ROOM_AR, blockTime);
                break;
            case BlockTnConstant.BLOCK_MSG:
                msgEn = String.format(BlockTnConstant.MSG_BLOCK_MSG_EN, blockTime);
                msgAr = String.format(BlockTnConstant.MSG_BLOCK_MSG_AR, blockTime);
                break;
            default:
                break;
        }
        if (!StringUtils.isEmpty(blockTime)) {
            HttpCode blockError = HttpCode.BLOCK_ERROR;
            HttpCode newError = new HttpCode(blockError.getCode(), msgEn, msgAr);
            return ApiResult.getError(newError);
        }
        return ApiResult.getOk();
    }

    protected ApiResult<String> heartCheckRoomLimit(HttpServletRequest request, String roomId, String uid) {
        if (StringUtils.isEmpty(roomId)) {
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        if (roomKickRedis.isKick(roomId, uid)) {
            logger.info("actor has kicked from room. path={} roomId={} uid={}", request.getRequestURI(), roomId, uid);
            return ApiResult.getError(RoomHttpCode.KICK_OUt);
        }
        if (!checkService.checkCanEnter(roomId, uid)) {
            logger.info("enter room check failed. path={} roomId={} uid={}", request.getRequestURI(), roomId, uid);
            return ApiResult.getError(RoomHttpCode.PERMIT_ERROR);
        }
        return ApiResult.getOk();
    }

    protected ApiResult<String> checkRoomLimit(String roomId, String uid, String sourceCheck) {
        if (StringUtils.isEmpty(roomId)) {
            logger.info("roomId is null. path={} uid={}", sourceCheck, uid);
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        if (!roomPlayerRedis.checkInRoom(roomId, uid)) {
            logger.info("actor do not in room. path={} roomId={} uid={}", sourceCheck, roomId, uid);
            return ApiResult.getError(HttpCode.NOT_IN_ROOM);
        }
        if (roomKickRedis.isKick(roomId, uid)) {
            logger.info("actor has kicked from room. path={} roomId={} uid={}", sourceCheck, roomId, uid);
            return ApiResult.getError(RoomHttpCode.KICK_OUt);
        }
        if (!checkService.checkCanEnter(roomId, uid)) {
            logger.info("enter room check failed. path={} roomId={} uid={}", sourceCheck, roomId, uid);
            return ApiResult.getError(RoomHttpCode.PERMIT_ERROR);
        }
        return ApiResult.getOk();
    }

    protected void sendRoomHeart(String roomId, String uid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String path = "room_heart";
                Map<String, String> params = new HashMap<>();
                params.put("room_id", roomId);
                params.put("uid", uid);
                roomSender.sendPost(path, roomId, params);
            }
        });
    }
}
