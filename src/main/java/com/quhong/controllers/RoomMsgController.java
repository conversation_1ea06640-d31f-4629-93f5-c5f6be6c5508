package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.*;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.RoomMsgDTO;
import com.quhong.data.dto.RoomSendPicDTO;
import com.quhong.data.vo.BadWordMsgVO;
import com.quhong.data.vo.ChatHallMsgVO;
import com.quhong.data.vo.OnlineUsersVO;
import com.quhong.data.vo.RoomMsgVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.ImageDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IDetectService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BubbleDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.msg.room.ChatHallMsg;
import com.quhong.msg.room.EmojiPushMsg;
import com.quhong.msg.room.RoomRecommendFollowMsg;
import com.quhong.msg.room.SendRoomMsg;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.redis.BlockRedis;
import com.quhong.redis.ChatHallRedis;
import com.quhong.redis.RepeatMsgRedis;
import com.quhong.room.RoomMsgService;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.*;
import com.quhong.service.mysql.BadWordService;
import com.quhong.utils.RequestUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 房间服务/房间消息
 */
@RestController
@RequestMapping("/room_service/")
public class RoomMsgController extends AbstractRoomController {
    private static final Logger logger = LoggerFactory.getLogger(RoomMsgController.class);
    private static final Object EMPTY_OBJ = new Object();
    private static final JSONObject EMPTY_JSON_OBJ = new JSONObject();

    public static final int REPEAT_MSG_COST = 10;
    private static final int ACT_TYPE = 933;
    private static final String REPEAT_MSG_TITLE = "Repeat message";
    private static final Set<String> COMPETITOR_SET = new HashSet<>(Arrays.asList("yalla"));

    private static final boolean IS_VIP_LIMIT = true;
    private static final int VIP_LIMIT = 3;
    private static final int LEVEL_LIMIT = 40;
    private static final String AI_ROOM_ID = "r:655c4c24b661b86b85455f3b";

    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private BadWordService badWordService;
    @Resource
    private RoomMsgService roomMsgService;
    @Resource
    private DetectManageApi detectManageApi;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RepeatMsgRedis repeatMsgRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private CreditRiskService creditRiskService;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private ChatHallRedis chatHallRedis;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private IDetectService detectService;
    @Resource
    private BubbleDao bubbleDao;
    @Resource
    private RoomSettingsService roomSettingsService;
    @Resource
    private RoomOpenAiService roomOpenAiService;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private ActorCommonService actorCommonService;

    /**
     * 发送房间消息
     */
    @RequestMapping("room_msg")
    public HttpResult<RoomMsgVO> receiveRoomMsg(@RequestBody RoomMsgDTO roomMsgDTO) {
        if (null == roomMsgDTO) {
            return HttpResult.getOk(null);
        }
        logger.info("sendRoomMsg. roomId={} uid={} content={}", roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), roomMsgDTO.getContent());
        roomIdCheck(roomMsgDTO.getRoomId());
        ApiResult<String> block = checkBlock(roomMsgDTO, BlockTnConstant.BLOCK_ROOM_FILE);
        if (block.isError()) {
            throw new CommonException(block.getCode());
        }
        String inRoomId = roomPlayerRedis.getActorRoomStatus(roomMsgDTO.getUid());
        if (!roomMsgDTO.getRoomId().equals(inRoomId)) {
            throw new CommonException(RoomHttpCode.NOT_IN_ROOM);
        }
        ActorData actorData = actorDao.getActorDataFromCache(roomMsgDTO.getUid());
        int score = creditRiskService.getCreditRiskScoreByUid(roomMsgDTO.getUid(), actorData.getTn_id(), actorData.getIp());
        if (score < 300) {
            logger.info("actor limit room msg by risk lt 300 score={} uid={} roomId={}", score, roomMsgDTO.getUid(), roomMsgDTO.getRoomId());
            throw new CommonException(RoomHttpCode.USER_RISK_INVALID_ROOM_MSG);
        }
        // 检查脏词
        int msgType = roomMsgDTO.getMsgType();
        if (msgType == MsgType.TEXT || msgType == MsgType.AT_MSG) {
            String msgContent = roomMsgDTO.getMsgType() == MsgType.AT_MSG ? JSONObject.parseObject(roomMsgDTO.getContent()).getString("content") : roomMsgDTO.getContent();
            BadWordMsgVO badWordMsgVO = getBadWordMsgVO(msgContent);
            if (badWordMsgVO != null) {
                logger.info("send msg contain dirty text. can not send text msg. roomId={} uid={} content={} msgType={} ", roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), roomMsgDTO.getContent(), roomMsgDTO.getMsgType());
                asyncCreditRisk(roomMsgDTO.getUid());
                return HttpResult.getOk(new RoomMsgVO(roomMsgDTO.getMsgType(), roomMsgDTO.getSlang(), badWordMsgVO.getContent()));
            }
        }

        // 是否被禁止发公屏消息
        if (blockRedis.getBlockUserPublicMsgStatus(roomMsgDTO.getUid()) != 0) {
            logger.info("actor BlockUserPublicMsg score={} uid={} roomId={}", score, roomMsgDTO.getUid(), roomMsgDTO.getRoomId());
            throw new CommonException(RoomHttpCode.ACCOUNT_HAS_DISABLED);
        }

        SendRoomMsg msg = new SendRoomMsg();
        msg.setMsgType(roomMsgDTO.getMsgType());
        msg.setSlang(roomMsgDTO.getSlang());
        msg.setContent(roomMsgDTO.getContent());
        String content = getMsgContent(roomMsgDTO);
        msg.setContent(content);
        checkDirtyAndSendMsg(roomMsgDTO, msg);

        // if(AI_ROOM_ID.equals(roomMsgDTO.getRoomId())){
        //     BaseTaskFactory.getFactory().addSlow(new Task() {
        //         @Override
        //         protected void execute() {
        //             roomOpenAiService.sendOpenAiResponse(roomMsgDTO);
        //         }
        //     });
        // }
        return HttpResult.getOk(new RoomMsgVO(roomMsgDTO.getMsgType(), roomMsgDTO.getSlang(), content));
    }

    /**
     * 检查发送聊天大厅消息权限
     */
    @RequestMapping("check_hall_msg")
    public HttpResult<OnlineUsersVO> check(@RequestBody RoomSendPicDTO dto) {
        logger.info("check hall msg. uid={}", dto.getUid());
        if (true) {
            int vipLevel = vipInfoDao.getIntVipLevel(dto.getUid());
            int userLevel = userLevelDao.getUserLevel(dto.getUid());
            sendHallCheck(dto.getUid(), vipLevel, userLevel);
        } else {
            roomSettingsService.sendPicCheck(dto, false);
        }
        return HttpResult.getOk();
    }

    private void sendHallCheck(String uid, int vipLevel, int userLevel) {
        if (IS_VIP_LIMIT) {
            if (vipLevel < VIP_LIMIT) {
                logger.info("actor limit chat hall vipLevel={} uid={}", vipLevel, uid);
                throw new CommonException(RoomHttpCode.CHAT_HALL_VIP_LIMIT, VIP_LIMIT);
            }
        } else {
            if (userLevel < LEVEL_LIMIT) {
                logger.info("actor limit chat hall userLevel={} uid={}", userLevel, uid);
                throw new CommonException(RoomHttpCode.CHAT_HALL_LEVEL_LIMIT, LEVEL_LIMIT);
            }
        }
    }

    /**
     * 发送聊天大厅消息
     */
    @RequestMapping("send_hall_msg")
    public HttpResult<ChatHallMsgVO> sendHallMsg(@RequestBody RoomMsgDTO dto) {
        if (null == dto) {
            return HttpResult.getOk(null);
        }
        if (ServerConfig.isNotProduct() || DateHelper.getNowSeconds() >= 1706734800) {
            throw new CommonException(RoomHttpCode.UPDATE_APP);
        }
        logger.info("sendHallMsg. roomId={} uid={} content={}", dto.getRoomId(), dto.getUid(), dto.getContent());
        ApiResult<String> block = checkBlock(dto, BlockTnConstant.BLOCK_ROOM_FILE);
        if (block.isError()) {
            throw new CommonException(block.getCode());
        }
        dto.setContent(getMsgContent(dto));
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        int vipLevel = vipInfoDao.getIntVipLevel(actorData.getUid());
        int userLevel = userLevelDao.getUserLevel(dto.getUid());
        ChatHallMsg msg = new ChatHallMsg();
        msg.setAid(dto.getUid());
        msg.setName(actorData.getName());
        msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        msg.setGender(actorData.getFb_gender());
        msg.setGender(actorData.getFb_gender());
        msg.setVipLevel(vipLevel);
        msg.setVipMedal(actorCommonService.getCommonVipMedal(dto.getUid(), vipLevel));
        msg.setFromRoomId(0 == actorData.getAccept_talk() ? null : dto.getRoomId());
        msg.setContent(dto.getContent());
        msg.setMsgType(dto.getMsgType());
        msg.setBubbleId(bubbleDao.getBubbleId(dto.getUid()));
        ChatHallMsgVO vo = new ChatHallMsgVO();
        BeanUtils.copyProperties(msg, vo);
        vo.setMsgId(String.valueOf(msg.getMsgId()));
        // 检查脏词
        String msgContent = dto.getMsgType() == MsgType.AT_MSG ? JSONObject.parseObject(dto.getContent()).getString("content") : dto.getContent();
        BadWordMsgVO badWordMsgVO = getBadWordMsgVO(msgContent);
        if (badWordMsgVO != null) {
            logger.info("send chat hall msg contain dirty text. can not send text msg. roomId={} uid={} content={} msgType={}", dto.getRoomId(), dto.getUid(), dto.getContent(), dto.getMsgType());
            asyncCreditRisk(dto.getUid());
            return HttpResult.getOk(vo);
        }
        // 涉及竞品内容仅自己可见
        for (String competitor : COMPETITOR_SET) {
            if (dto.getContent().toLowerCase().contains(competitor.toLowerCase())) {
                return HttpResult.getOk(vo);
            }
        }
        int score = creditRiskService.getCreditRiskScoreByUid(dto.getUid(), actorData.getTn_id(), actorData.getIp());
        if (score < 300) {
            logger.info("actor limit chat hall score={} uid={} roomId={}", score, dto.getUid(), dto.getRoomId());
            throw new CommonException(RoomHttpCode.USER_RISK_INVALID_ROOM_MSG);
        }
        // 配置1: 用户等级高于XXX级可聊天。点击发送按钮时toast提示：用户等级低于XXX级不可聊天。
        // 配置2: 大亨等级N级及以上可聊天，默认大亨3级及以上。点击发送时弹出权限不足弹窗“大亨N及以上可聊天，是否购买大亨？” 按钮：取消/购买。点击购买跳转到大亨配置的等级页面。
        // 发送图片时客户端已通过check接口校验，不再校验
        if (dto.getMsgType() != MsgType.IMAGE) {
            sendHallCheck(dto.getUid(), vipLevel, userLevel);
        }
        // 每个账号分享到世界房间有次数限制：每日限制3次。超过3次点击世界房间图标时toast提示：今日分享次数已用完。页面不关闭。
        if (dto.getMsgType() == MsgType.SHARE_ROOM && chatHallRedis.incrShareCount(dto.getUid()) > 3) {
            logger.info("chat hall share room limit uid={}", dto.getUid());
            throw new CommonException(RoomHttpCode.CHAT_HALL_SHARE_LIMIT);
        }
        if (dto.getMsgInfo() == null) {
            dto.setMsgInfo(EMPTY_JSON_OBJ);
        }
        vo.setMsgInfo(dto.getMsgInfo());
        if (dto.getMsgType() == MsgType.IMAGE) {
            if (dto.getMsgInfo() == null || ObjectUtils.isEmpty(dto.getMsgInfo().getString("img"))) {
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            String url = ImageUrlGenerator.createCdnUrl(dto.getMsgInfo().getString("img"));
            dto.getMsgInfo().put("url", url);
            dto.getMsgInfo().put("thumbnailUrl", ImageUrlGenerator.generateRoomThumbnailUrl(url, false));
            dto.getMsgInfo().put("thumbnailStaticUrl", ImageUrlGenerator.generateRoomThumbnailUrl(url, true));
            asyncSendPic(msg, dto, vo, url);
        } else {
            checkDirtyAndSendChatHall(dto, msg, vo, userLevel);
        }
        return HttpResult.getOk(vo);
    }

    public void asyncSendPic(ChatHallMsg msg, RoomMsgDTO dto, ChatHallMsgVO vo, String url) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                try {
                    boolean isSafe = detectService.detectImage(new ImageDTO(url, DetectOriginConstant.SCREEN_PICTURE, dto.getUid())).getData().getIsSafe() == 1;
                    if (isSafe) {
                        sendChatHallMsg(msg, vo, dto);
                    } else {
                        creditRisk(dto.getUid());
                    }
                } catch (Exception e) {
                    sendChatHallMsg(msg, vo, dto);
                }
            }
        });
    }

    private void checkDirtyAndSendChatHall(RoomMsgDTO roomMsgDTO, ChatHallMsg msg, ChatHallMsgVO vo, int userLevel) {
        if (userLevel >= 0 && userLevel <= 5) {
            detectManageApi.asyncSafeText(msg.getContent(), DetectOriginConstant.SCREEN_MESSAGE, roomMsgDTO.getUid(), isSafeText -> {
                if (isSafeText) {
                    sendChatHallMsg(msg, vo, roomMsgDTO);
                } else {
                    creditRisk(roomMsgDTO.getUid());
                    logger.info("send msg contain dirty text. can not send text msg. content={} roomId={} uid={}", roomMsgDTO.getContent(), roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
                }
            });
        } else {
            sendChatHallMsg(msg, vo, roomMsgDTO);
        }
    }

    private void sendChatHallMsg(ChatHallMsg msg, ChatHallMsgVO vo, RoomMsgDTO roomMsgDTO) {
        msg.setMsgInfo(roomMsgDTO.getMsgInfo().toJSONString());
        roomWebSender.sendGlobalMsg(msg.getAid(), msg);
        chatHallRedis.addCHatHallRecord(vo);
        // 任务:发送大厅消息
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(msg.getAid(), msg.getRoomId(), "", "", CommonMqTaskConstant.SEND_HALL_MSG, 1));
        // 聊天大厅消息事件埋点
        RoomMsgNewEvent event = new RoomMsgNewEvent();
        event.setSend_msg_type(1);
        event.setUid(roomMsgDTO.getUid());
        event.setRoom_id(msg.getRoomId());
        event.setMsg_type(msg.getMsgType());
        event.setMsg_id(String.valueOf(msg.getMsgId()));
        event.setFrom_os(roomMsgDTO.getOs());
        event.setFrom_uid(roomMsgDTO.getUid());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 获取大厅消息记录
     */
    @RequestMapping("get_hall_msg")
    public HttpResult<List<ChatHallMsgVO>> getHallMsg(@RequestBody HttpEnvData dto) {
        logger.info("get chat hall msg uid={}", dto.getUid());
        return HttpResult.getOk(getChatHallList());
    }

    @Cacheable(value = "getChatHallList", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public List<ChatHallMsgVO> getChatHallList() {
        return chatHallRedis.getChatHallList();
    }

    /**
     * 发送房间重复消息
     */
    @PostMapping("room_repeat_msg")
    public String receiveRoomRepeatMsg(HttpServletRequest request) {
        RoomMsgDTO roomMsgDTO = RequestUtils.getSendData(request, RoomMsgDTO.class);
        if (null == roomMsgDTO) {
            return createResult(HttpCode.SUCCESS, null);
        }
        logger.info("sendRoomRepeatMsg. roomId={} uid={} content={} repeatTimes={} repeatInterval={}", roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), roomMsgDTO.getContent(), roomMsgDTO.getRepeatTimes(), roomMsgDTO.getRepeatInterval());
        // 参数校验
        checkParam(roomMsgDTO);
        // 校验是否被拉黑
        ApiResult<String> block = checkBlock(roomMsgDTO, BlockTnConstant.BLOCK_ROOM_FILE);
        if (block.isError()) {
            return createError(roomMsgDTO, block.getCode());
        }
        MongoRoomData roomData = roomDao.getDataFromCache(roomMsgDTO.getRoomId());
        if (roomData == null) {
            logger.error("can not find room data. uid={} roomId={}", roomMsgDTO.getUid(), roomMsgDTO.getRoomId());
            return createError(roomMsgDTO, RoomHttpCode.PARAM_ERROR);
        }
        // 校验房间是否禁言
        if (roomData.getChat_locked() != 0) {
            logger.info("The room owner has closed the public chat area. uid={} roomId={}", roomMsgDTO.getUid(), roomMsgDTO.getRoomId());
            return createError(roomMsgDTO, RoomHttpCode.THE_PUBLIC_CHAT_AREA_HAS_CLOSED);
        }
        // 校验房间限制发言用户等级
        int userLevel = userLevelDao.getUserLevel(roomMsgDTO.getUid());
        if (roomData.getTextLimit() != null && roomData.getTextLimit() >= userLevel) {
            logger.info("The public chat area has been restricted to LV.{} and below cannot send messages. uid={} roomId={}", roomData.getTextLimit(), roomMsgDTO.getUid(), roomMsgDTO.getRoomId());
            throw new CommonException(RoomHttpCode.THE_PUBLIC_CHAT_AREA_HAS_BEEN_RESTRICTED, roomData.getTextLimit());
        }
        // 校验用户是否被禁言
        if (micFrameRedis.getForbidTime(roomMsgDTO.getRoomId(), roomMsgDTO.getUid()) > 0) {
            logger.info("You are banned from sending text messages by room owner. uid={} roomId={}", roomMsgDTO.getUid(), roomMsgDTO.getRoomId());
            return createError(roomMsgDTO, RoomHttpCode.YOU_ARE_BANNED_SEND_MSG);
        }
        // 校验是否已经存在正在执行的任务
        if (repeatMsgRedis.hasRunningTask(roomMsgDTO.getRoomId(), roomMsgDTO.getUid())) {
            logger.info("Sending a repeat message task is currently in progress. uid={} roomId={}", roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
            return createError(roomMsgDTO, RoomHttpCode.HAVE_REPEAT_MSG_TASK_IN_PROGRESS);
        }
        // 校验是否含有脏词
        if (badWordService.isContainBadWord(roomMsgDTO.getContent(), BadWordService.MIN_MATCH_TYPE)) {
            logger.info("send msg contain dirty text. can not send text msg. roomId={} uid={} content={} msgType={} ", roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), roomMsgDTO.getContent(), roomMsgDTO.getMsgType());
            return createError(roomMsgDTO, RoomHttpCode.ILLEGAL_CONTENT);
        }
        if (roomMsgDTO.getContent().length() > 490) {
            logger.info("msg content length great then {}. do not send. roomId={} uid={}", 490, roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
            roomMsgDTO.setContent(roomMsgDTO.getContent().substring(0, 490) + "...");
        }
        // 0到5级，走异步检测
        if (userLevel <= 5) {
            ActorData actorData = actorDao.getActorData(roomMsgDTO.getUid());
            int beans = actorData != null ? actorData.getBeans() : 0;
            if (beans < REPEAT_MSG_COST * roomMsgDTO.getRepeatTimes()) {
                logger.info("Your diamonds are not enough. uid={}", roomMsgDTO.getUid());
                throw new CommonException(RoomHttpCode.UNION_NOT_ENOUGH_DIAMOND);
            }
            detectManageApi.asyncSafeText(roomMsgDTO.getContent(), DetectOriginConstant.SCREEN_MESSAGE, roomMsgDTO.getUid(), isSafeText -> {
                if (isSafeText) {
                    try {
                        // 扣除钻石费用
                        deductCost(roomMsgDTO.getUid(), roomMsgDTO.getRoomId(), REPEAT_MSG_COST * roomMsgDTO.getRepeatTimes());
                        // 发送重复消息
                        doSendRepeatMsg(roomMsgDTO);
                    } catch (CommonException e) {
                        logger.info("send room repeat msg fail.");
                    }
                } else {
                    logger.info("send msg contain dirty text. can not send text msg. content={} roomId={} uid={}", roomMsgDTO.getContent(), roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
                }
            });
        } else {
            // 扣除钻石费用
            deductCost(roomMsgDTO.getUid(), roomMsgDTO.getRoomId(), REPEAT_MSG_COST * roomMsgDTO.getRepeatTimes());
            // 发送重复消息
            doSendRepeatMsg(roomMsgDTO);
        }
        return createResult(roomMsgDTO, HttpCode.SUCCESS, null);
    }

    /**
     * 发送表情
     */
    @PostMapping("/send_emoji")
    public String sendEmoji(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendData(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        String roomId = paramObj.getString("room_id");
        String uid = envData.getUid();
        ApiResult<String> block = checkBlock(envData, BlockTnConstant.BLOCK_ROOM_FILE);
        if (block.isError()) {
            return createError(envData, block.getCode());
        }
        ApiResult<String> preResult = checkRoomLimit(roomId, uid, request.getRequestURI());
        if (preResult.isError()) {
            return createError(envData, preResult.getCode());
        }
        String iconFile = paramObj.getString("icon_file");
        String nameText = paramObj.getString("nameText");
        int cycles = paramObj.getIntValue("cycles");
        EmojiPushMsg msg = new EmojiPushMsg();
        msg.setIcon_file(iconFile);
        msg.setNameText(nameText);
        msg.setCycles(cycles);
        roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
        // 增加麦位表情埋点
        String scene = RoomUtils.isVoiceRoom(roomId) ? EmoticonSendEvent.SEND_SCENE_1 : EmoticonSendEvent.SEND_SCENE_4;
        doEmoticonSendEvent(uid, roomId, iconFile, nameText, scene);
        return createResult(envData, HttpCode.SUCCESS, EMPTY_OBJ);
    }

    private void doEmoticonSendEvent(String uid, String roomId, String iconFile, String nameText, String scene) {
        EmoticonSendEvent event = new EmoticonSendEvent();
        event.setRoom_id(roomId);
        event.setUid(uid);
        event.setScene(scene);
        event.setEmoticon_name(nameText);
        event.setCtime(DateHelper.getNowSeconds());
        if (!ObjectUtils.isEmpty(iconFile)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("emoticon_file_name", iconFile);
            event.setOther_info(jsonObject.toJSONString());
        }
        eventReport.track(new EventDTO(event));
    }

    /**
     * http发送房间文本消息
     */
    private void checkDirtyAndSendMsg(RoomMsgDTO roomMsgDTO, SendRoomMsg msg) {
        int userLevel = userLevelDao.getUserLevel(roomMsgDTO.getUid());
        if (userLevel >= 0 && userLevel <= 5 && (roomMsgDTO.getMsgType() == MsgType.TEXT || roomMsgDTO.getMsgType() == MsgType.AT_MSG)) {
            // 0到10级，走异步检测
            detectManageApi.asyncSafeText(msg.getContent(), DetectOriginConstant.SCREEN_MESSAGE, roomMsgDTO.getUid(), isSafeText -> {
                if (isSafeText) {
                    doSendMsg(roomMsgDTO, msg);
                } else {
                    creditRisk(roomMsgDTO.getUid());
                    logger.info("send msg contain dirty text. can not send text msg. content={} roomId={} uid={}", roomMsgDTO.getContent(), roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
                }
            });
        } else {
            doSendMsg(roomMsgDTO, msg);
        }
    }


    private void asyncCreditRisk(String uid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (actorData != null) {
                    creditRiskService.creditRisk(uid, actorData.getTn_id(), actorData.getIp(), CreditRiskService.TYPE_SCREEN_MSG, 200, 10, 5);
                }
            }
        });
    }


    private void creditRisk(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData != null) {
            creditRiskService.creditRisk(uid, actorData.getTn_id(), actorData.getIp(), CreditRiskService.TYPE_SCREEN_MSG, 200, 10, 5);
        }
    }

    private void doSendMsg(RoomMsgDTO roomMsgDTO, SendRoomMsg msg) {
        if (msg.getMsgType() == MsgType.AT_MSG) {
            try {
                JSONObject contentObject = JSONObject.parseObject(roomMsgDTO.getContent());
                String aid = contentObject.getString("aid");
                RoomRecommendFollowMsg recommendFollowMsg = roomMsgService.getRoomRecommendFollowMsg(roomMsgDTO.getUid(), aid);
                if (null != recommendFollowMsg) {
                    roomWebSender.sendPlayerWebMsg(roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), aid, recommendFollowMsg, false);
                }
                if (roomMsgDTO.getGreetType() == 1) {
                    repeatMsgRedis.recordGreetStatus(roomMsgDTO.getUid(), aid);
                    commonTaskService.sendCommonTaskMq(new CommonMqTopicData(roomMsgDTO.getUid(), roomMsgDTO.getRoomId(), aid, "", CommonMqTaskConstant.WELCOME_USER_IN_ROOM, 1));
                    logger.info("uid:{} WelcomeNew aid:{}", roomMsgDTO.getUid(), aid);
                    RoomMsgNewEvent event = new RoomMsgNewEvent();
                    event.setRoom_id(msg.getRoomId());
                    event.setMsg_type(10);
                    event.setSend_msg_type(0);
                    event.setUid(roomMsgDTO.getUid());
                    event.setMsg_id(String.valueOf(msg.getMsgId()));
                    event.setFrom_os(roomMsgDTO.getOs());
                    event.setFrom_uid(roomMsgDTO.getUid());
                    event.setFrom_uid(roomMsgDTO.getUid());
                    event.setTo_uid(aid);
                    event.setCtime(DateHelper.getNowSeconds());
                    eventReport.track(new EventDTO(event));
                }
            } catch (Exception e) {
                logger.error("parse aid error, error msg={}", e.getMessage(), e);
            }
        }

        if (msg.getMsgType() == MsgType.SEND_EMOJI) {
            roomWebSender.sendRoomWebMsg(roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), msg, false, 857);
            String scene = RoomUtils.isVoiceRoom(roomMsgDTO.getRoomId()) ? EmoticonSendEvent.SEND_SCENE_2 : EmoticonSendEvent.SEND_SCENE_5;
            doEmoticonSendEvent(roomMsgDTO.getUid(), roomMsgDTO.getRoomId(), msg.getContent(), "", scene);
        } else {
            roomWebSender.sendRoomWebMsg(roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), msg, false);
        }
    }

    private String getMsgContent(RoomMsgDTO roomMsgDTO) {
        if (roomMsgDTO.getMsgType() == MsgType.AT_MSG) {
            try {
                JSONObject contentObject = JSONObject.parseObject(roomMsgDTO.getContent());
                String aid = contentObject.getString("aid");
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                if (actorData != null) {
                    contentObject.put("name", actorData.getName());
                }
                String content = contentObject.getString("content");
                if (null != content && content.length() > 420) {
                    contentObject.put("content", content.substring(0, 420) + "...");
                }
                return contentObject.toJSONString();
            } catch (Exception e) {
                logger.error("parse aid error, error msg={}", e.getMessage(), e);
            }
        } else if (roomMsgDTO.getMsgType() == MsgType.SEND_EMOJI) {
            return roomMsgDTO.getContent();
        } else {
            if (roomMsgDTO.getContent().length() > 495) {
                logger.info("msg content length great then {}. do not send. roomId={} uid={}", 495, roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
                return roomMsgDTO.getContent().substring(0, 495) + "...";
            }
        }
        return roomMsgDTO.getContent();
    }

    /**
     * 获取BadWordMsgVO
     */
    private BadWordMsgVO getBadWordMsgVO(String msgContent) {
        try {
            if (!badWordService.isContainBadWord(msgContent, BadWordService.MIN_MATCH_TYPE)) {
                return null;
            }
            String content = badWordService.replaceBadWord(msgContent, BadWordService.MIN_MATCH_TYPE, "*");
            BadWordMsgVO badWordMsgVO = new BadWordMsgVO();
            badWordMsgVO.setContent(content);
            return badWordMsgVO;
        } catch (Exception e) {
            logger.error("check bad word fail.{}", e.getMessage(), e);
            return null;
        }
    }

    private void checkParam(RoomMsgDTO roomMsgDTO) {
        if (StringUtils.isEmpty(roomMsgDTO.getRoomId())) {
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (roomMsgDTO.getRepeatInterval() < 500 || roomMsgDTO.getRepeatInterval() > 5000) {
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (roomMsgDTO.getRepeatTimes() < 2 || roomMsgDTO.getRepeatTimes() > 10) {
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
    }

    private void doSendRepeatMsg(RoomMsgDTO roomMsgDTO) {
        repeatMsgRedis.saveRepeatMsgTask(roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
        // 发送第一条消息
        SendRoomMsg msg = new SendRoomMsg();
        msg.setMsgType(MsgType.TEXT);
        msg.setSlang(roomMsgDTO.getSlang());
        msg.setContent(roomMsgDTO.getContent());
        msg.setIsRepeatMsg(2);
        roomWebSender.sendRoomWebMsg(roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), msg, false);
        // 执行重复发消息任务
        int leftRepeatTimes = roomMsgDTO.getRepeatTimes() - 1;
        msg.setIsRepeatMsg(1);
        int delayTime = roomMsgDTO.getRepeatInterval();
        for (int i = 1; i <= leftRepeatTimes; i++) {
            boolean isLast = i == leftRepeatTimes;
            TimerService.getService().addDelay(new DelayTask(delayTime * i) {
                @Override
                protected void execute() {
                    doSendRepeatMsgTask(roomMsgDTO, msg, isLast);
                }
            });
        }
        // 数数埋点
        RoomRepeatMsgEvent event = new RoomRepeatMsgEvent();
        event.setUid(roomMsgDTO.getUid());
        event.setRoom_id(roomMsgDTO.getRoomId());
        event.setRepeat_msg_times(roomMsgDTO.getRepeatTimes());
        event.setRepeat_msg_interva(roomMsgDTO.getRepeatInterval() / 1000d);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 执行重复发消息任务
     */
    private void doSendRepeatMsgTask(RoomMsgDTO roomMsgDTO, SendRoomMsg msg, boolean isLast) {
        // 校验是否是最后一条消息
        if (isLast) {
            msg.setIsRepeatMsg(3);
            repeatMsgRedis.removeRepeatMsgTask(roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
        }
        // 校验是否在房间
        if (!roomPlayerRedis.checkInRoom(roomMsgDTO.getRoomId(), roomMsgDTO.getUid())) {
            logger.info("send repeat msg user not in room. roomId={} uid={}", roomMsgDTO.getRoomId(), roomMsgDTO.getUid());
            return;
        }
        // 校验房间是否设置了禁止发送公屏消息
        MongoRoomData roomData = roomDao.getDataFromCache(roomMsgDTO.getRoomId());
        if (roomData == null || roomData.getChat_locked() != 0) {
            return;
        }
        // 校验房间限制发言用户等级
        if (roomData.getTextLimit() != null && roomData.getTextLimit() >= userLevelDao.getUserLevel(roomMsgDTO.getUid())) {
            return;
        }
        // 校验用户是否被禁言
        if (micFrameRedis.getForbidTime(roomMsgDTO.getRoomId(), roomMsgDTO.getUid()) > 0) {
            return;
        }
        roomWebSender.sendRoomWebMsg(roomMsgDTO.getRoomId(), roomMsgDTO.getUid(), msg, false);
    }

    /**
     * 扣除发送重复消息的费用
     */
    private void deductCost(String uid, String roomId, int costBeans) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(ACT_TYPE);
        moneyDetailReq.setChanged(-costBeans);
        moneyDetailReq.setTitle(REPEAT_MSG_TITLE);
        moneyDetailReq.setDesc("");
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                logger.info("Your diamonds are not enough. Recharge now? uid={}", uid);
                throw new CommonException(RoomHttpCode.UNION_NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, msg={}", result.getCode().getMsg());
            throw new CommonException(RoomHttpCode.SERVER_ERROR);
        }
    }
}
