package com.quhong.stat;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.FruitPartyEvent;
import com.quhong.analysis.RoomMaxOnlineUsersEvent;
import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.dao.RoomUserStatDao;
import com.quhong.mysql.data.RoomUserStatData;
import com.quhong.redis.CollectionCommonRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.room.redis.RoomRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 房间在线用户人数在线搜集器
 */
@Component
public class RoomUserCollection extends TaskQueue {
    private static final Logger logger = LoggerFactory.getLogger(RoomUserCollection.class);
    private static final int ONLINE_INTERVAL = 60 * 1000;

    @Autowired
    private RoomUserStatDao roomUserStatDao;
    @Autowired
    private RoomRedis roomRedis;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Autowired
    private CollectionCommonRedis collectionCommonRedis;
    @Autowired
    private EventReport eventReport;

    public RoomUserCollection() {
    }

    @PostConstruct
    public void postInit() {
        onTick();
        TimerService.getService().addDelay(new LoopTask(this, ONLINE_INTERVAL) {
            @Override
            protected void execute() {
                onTick();
            }
        });
    }

    private void onTick() {
        long startTime = DateHelper.DEFAULT.getDateTime();
        String dateTime = DateHelper.DEFAULT.formatDateTime(new Date(startTime));

        String dateTimeH = DateHelper.DEFAULT.formatDateInHour(); //yyyy-MM-dd#HH
        String dateTimeKey = getHashOnlineReportKey(dateTimeH);
        Map<String, String> allRoomCount = collectionCommonRedis.getCommonHashAllMapStr(dateTimeKey);
//        logger.info("pre collection dateTimeH:{} allRoomCount.size:{}", dateTimeH, allRoomCount.size());
        if (CollectionUtils.isEmpty(allRoomCount)) {
            collectionCommonRedis.setExpire(dateTimeKey);
            long now = System.currentTimeMillis();
            int cTime = (int) (now / 1000);
            String beforeDateTimeH = DateHelper.DEFAULT.formatDateInHour(new Date(now - 3600 * 1000));
            String beforeDateTimeKey = getHashOnlineReportKey(beforeDateTimeH);
            Map<String, Integer> beforeAllRoomCount = collectionCommonRedis.getCommonHashAll(beforeDateTimeKey);
            if (!CollectionUtils.isEmpty(beforeAllRoomCount)) {
                for (Map.Entry<String, Integer> entry : beforeAllRoomCount.entrySet()) {
                    String roomId = entry.getKey();
                    String hostUid = RoomUtils.getRoomHostId(roomId);
                    int onlineCount = entry.getValue();
                    if (onlineCount > 0) {
                        reportEvent(roomId, hostUid, beforeDateTimeH, onlineCount, cTime);
                    }
                }
            }

            String beforeTwoTimeH = DateHelper.DEFAULT.formatDateInHour(new Date(now - 2*3600 * 1000));
            String beforeTwoTimeKey = getHashOnlineReportKey(beforeTwoTimeH);
            // 删除前两个小时的数据
            boolean isDelSuccess = collectionCommonRedis.delCommonKey(beforeTwoTimeKey);
            logger.info("collection dateTimeH:{} allRoomCount.size:{}" +
                            " beforeDateTimeH:{} beforeAllRoomCount.size:{}" +
                            " isDelSuccess:{} beforeTwoTimeKey:{}",
                    dateTimeH, allRoomCount.size(),
                    beforeDateTimeH, beforeAllRoomCount.size()
                    , isDelSuccess, beforeTwoTimeKey);
        }

        Set<String> allRoomSet = roomRedis.getAllRoomSet();
        List<RoomUserStatData> list = new ArrayList<>();
        int ctime = DateHelper.getNowSeconds();
        for (String roomId : allRoomSet) {
            try {
                int onlineUserCount = roomPlayerRedis.getRoomActorsCount(roomId);
                int onlineRobotCount = roomPlayerRedis.getRoomRobotCount(roomId);
                list.add(new RoomUserStatData(roomId, onlineUserCount, onlineRobotCount, dateTime, ctime));

                int num = Integer.parseInt(allRoomCount.getOrDefault(roomId, "0"));
                int maxNum = Math.max(num, onlineUserCount);
                if (maxNum > 0) {
                    allRoomCount.put(roomId, String.valueOf(maxNum));
                }

            } catch (Exception e) {
                logger.error("room user collection error. roomId={} error message={}", roomId, e.getMessage());
            }
        }
        try {
            if (list.size() > 0) {
//                logger.info("after collection dateTimeKey:{} allRoomCount.size:{}", dateTimeKey, allRoomCount.size());
                collectionCommonRedis.setCommonHashDataAll(dateTimeKey, allRoomCount);
                roomUserStatDao.insertList(list, ctime);
            }
        } catch (Exception e) {
            logger.error("insert room user stat error. size={} error message={}", list.size(), e.getMessage());
        }
    }

    private String getHashOnlineReportKey(String dateTimeH) {
        return String.format("onlineReport:%s", dateTimeH);
    }

    private void reportEvent(String roomId, String hostId, String date, int maxOnline, int cTime) {
        // 数数埋点
        RoomMaxOnlineUsersEvent event = new RoomMaxOnlineUsersEvent();
        event.setUid(hostId);
        event.setRoom_id(roomId);
        event.setDate(date);
        event.setMax_online_users(maxOnline);
        event.setCtime(cTime);
        eventReport.track(new EventDTO(event));
    }
}
