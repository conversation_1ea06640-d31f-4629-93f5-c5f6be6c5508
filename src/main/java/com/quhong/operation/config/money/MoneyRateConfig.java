package com.quhong.operation.config.money;

import org.apache.commons.collections4.MapUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


@Component
@PropertySource(value = "classpath:money.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "beans-and-rate")
public class MoneyRateConfig {

    private Map<Integer, BigDecimal> beansDollarMap;
    private Map<Integer, BigDecimal> beansDollarHuaweiMap;
    private Map<Integer, BigDecimal> jollyPayMap;
    private Map<String, Double> interestRate;
    private Map<String, Map<Long, Double>> tapPayCurrencyAmount;

    public Map<Integer, BigDecimal> getBeansDollarMap() {
        return beansDollarMap;
    }

    public void setBeansDollarMap(Map<Integer, BigDecimal> beansDollarMap) {
        if (MapUtils.isEmpty(beansDollarMap)) {
            beansDollarMap = new HashMap<>();
        }
        this.beansDollarMap = beansDollarMap;
    }

    public Map<Integer, BigDecimal> getBeansDollarHuaweiMap() {
        return beansDollarHuaweiMap;
    }

    public void setBeansDollarHuaweiMap(Map<Integer, BigDecimal> beansDollarHuaweiMap) {
        if (MapUtils.isEmpty(beansDollarHuaweiMap)) {
            beansDollarHuaweiMap = new HashMap<>();
        }
        this.beansDollarHuaweiMap = beansDollarHuaweiMap;
    }

    public Map<Integer, BigDecimal> getJollyPayMap() {
        return jollyPayMap;
    }

    public void setJollyPayMap(Map<Integer, BigDecimal> jollyPayMap) {
        if (MapUtils.isEmpty(jollyPayMap)) {
            jollyPayMap = new HashMap<>();
        }
        this.jollyPayMap = jollyPayMap;
    }

    public Map<String, Double> getInterestRate() {
        return interestRate;
    }

    public void setInterestRate(Map<String, Double> interestRate) {
        if (MapUtils.isEmpty(interestRate)) {
            interestRate = new HashMap<>();
        }
        this.interestRate = interestRate;
    }

    public Map<String, Map<Long, Double>> getTapPayCurrencyAmount() {
        return tapPayCurrencyAmount;
    }

    public void setTapPayCurrencyAmount(Map<String, Map<Long, Double>> tapPayCurrencyAmount) {
        if (MapUtils.isEmpty(tapPayCurrencyAmount)) {
            tapPayCurrencyAmount = new HashMap<>();
        }
        this.tapPayCurrencyAmount = tapPayCurrencyAmount;
    }
}
