package com.quhong.operation.config;

import com.quhong.operation.core.LoginFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.DispatcherType;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

/**
 * operation后台登录态校验过滤器配置
 * <AUTHOR>
 * @date 2020/6/11
 */
@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<LoginFilter> loginFilter() {
        LoginFilter loginFilter = new LoginFilter();

        Set<String> set = new LinkedHashSet<>();
        set.add("/*");

        Map<String, String> map = new HashMap<>();
        map.put("encoding", "UTF-8");
        map.put(loginFilter.EXCLUSIONS_URL, "/operation/test/resultMap, /operation/test/map, /operation/operation/wahoQuery");
        map.put(loginFilter.EXCLUSIONS_MATCH_URL, "^/operation/music/.+");

        FilterRegistrationBean<LoginFilter> filter = new FilterRegistrationBean<>();
        filter.setFilter(loginFilter);
        filter.setName("loginFilter");
        filter.setDispatcherTypes(DispatcherType.REQUEST);
        filter.setUrlPatterns(set);
//        filter.setAsyncSupported(true);
        filter.setInitParameters(map);
        // 先过滤负数小的数，负数完了，再执行正数大的数
        filter.setOrder(-2);
        return filter;
    }
}
