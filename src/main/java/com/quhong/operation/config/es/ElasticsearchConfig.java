package com.quhong.operation.config.es;

import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.transport.TransportAddress;
import org.elasticsearch.transport.client.PreBuiltTransportClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @date 2020/6/16
 */
@Configuration
public class ElasticsearchConfig {

    @Value("${spring.database.writeEs.cluster-nodes}")
    private String writeClusterNodes;
    @Value("${spring.database.writeEs.clusterName}")
    private String writeClusterName;
    @Value("${spring.database.readEs.cluster-nodes}")
    private String readClusterNodes;
    @Value("${spring.database.readEs.clusterName}")
    private String readClusterName;

    @Bean("writeEsTemplate")
    @Autowired
    public ElasticsearchTemplate getReadEsTemp(TransportClient transportClient) {
        return new ElasticsearchTemplate(transportClient);
    }

    @Bean
    public TransportClient transportClient() {
        return client(writeClusterNodes, writeClusterName);
    }

    @Bean("readEsTemplate")
    public ElasticsearchTemplate getReadEsTemp2() {
        TransportClient client = client(readClusterNodes, readClusterName);
        return new ElasticsearchTemplate(client);
    }

    private TransportClient client(String nodes, String name) {
        TransportClient client = null;
        try {
            int DEFAULT_PORT = 9300;
            TransportAddress master = new TransportAddress(InetAddress.getByName(nodes), DEFAULT_PORT);
            Settings settings = Settings.builder()
                    // 集群的名称
                    .put("cluster.name", name)
                    // 自动发现节点
                    .put("client.transport.sniff", true)
                    .build();
            client = new PreBuiltTransportClient(settings).addTransportAddress(master);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return client;
    }

}
