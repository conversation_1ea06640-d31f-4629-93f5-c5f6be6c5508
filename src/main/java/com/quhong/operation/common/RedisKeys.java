package com.quhong.operation.common;

/**
 * <AUTHOR>
 * @date 2020/6/19
 */
public class RedisKeys {

    public final static String BADGE_SOURCE = "badge_source";
    public final static String USER_TAKE_RIPPLE = "user_take_ripple";
    public final static String RIPPLE_SOURCE = "ripple_source";
    public final static String NEW_CREATE_ROOM = "new_create_room";
    public final static String API_CACHE_ = "api_cache_";
    public final static String GET_ROOM_INFO_NEW = "ToolApi.get_room_info_new";
    public final static String JOIN_SOURCE = "join_source";
    public final static String USER_LEVEL = "user_level";
    public final static String VIP_LEVEL = "vip_level";
    public final static String USER_TAKE_MIC = "user_take_mic";
    public final static String MIC_SOURCE = "mic_source";
    public final static String IMSI_BAN_LIST = "imsi_ban_list";
    public final static String IMEI_BAN_LIST = "imei_ban_list";
    public final static String IOS_BAN_LIST = "ios_ban_list";
    public final static String ANDROID_ID = "android_id";
    public final static String ACTOR_CACHE = "u:cache:";

    /**
     * 拼接某个用户在某房间内禁言的key
     *
     * @param room_id 房间id
     * @param uid     用户id
     * @return key
     */
    public static String getRoomTalkKey(String room_id, String uid) {
        return room_id + "_" + uid;
    }

    /**
     * 拼接某个用户在某房间内禁麦的key  上面是禁言，这个是禁麦
     *
     * @param room_id 房间id
     * @param uid     用户id
     * @return key
     */
    public static String getRoomForbidMic(String room_id, String uid) {
        return room_id + "_" + uid;
    }

    /**
     * @param roomId
     * @return
     */
    public static String getRoomMicKey(String roomId) {
        return "room_mic_set_" + roomId;
    }

    public static String getUserLevelKey(String aid) {
        return "user_level" + aid;
    }

    /**
     * 通过uid获取用户等级的key
     *
     * @param uid
     * @return
     */
    public static String getRoomLevelKey(String uid) {
        return "rlvl_" + uid;
    }

    public static String devoteRoomListKey(String period) {
        return "new_room_devote_list_" + period;
    }

    public static String getCameraKey(String uid) {
        return "camera_" + uid;
    }

    public static String getReportKey(String uid) {
        return "rep_" + uid;
    }

    public static String getRoomKickKey(String uid, String room_id) {
        return "rmkick_" + uid + "_" + room_id;
    }

    public static String getRoomRecordKey(String room_id, String uid) {
        return room_id + "_#_" + uid;
    }

    public static String getRedisCacheActorKey(String uid) {
        return ACTOR_CACHE + uid;
    }

}
