package com.quhong.operation.common;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/28
 */
public class HttpResult<T> implements Serializable {

    private static final long serialVersionUID = 9090226637452678226L;
    private final static int SUCCESS_CODE = 0;
    private final static int FAILURE_CODE = 500;
    private final static String SUCCESS_MSG = "success";

    private Integer code;
    private String msg;
    private T data;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }

    public HttpResult<T> ok() {
        this.code = SUCCESS_CODE;
        this.msg = SUCCESS_MSG;
        return this;
    }

    public HttpResult<T> ok(T data) {
        this.code = SUCCESS_CODE;
        this.msg = SUCCESS_MSG;
        this.data = data;
        return this;
    }


    public HttpResult<T> error() {
        this.code = FAILURE_CODE;
        return this;
    }

    public HttpResult<T> error(String msg) {
        this.code = FAILURE_CODE;
        this.msg = msg;
        return this;
    }

    public HttpResult<T> error(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        return this;
    }

    @Override
    public String toString() {
        return "HttpResult{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }

}
