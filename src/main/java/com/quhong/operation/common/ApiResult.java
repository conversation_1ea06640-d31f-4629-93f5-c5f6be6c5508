package com.quhong.operation.common;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
public class ApiResult<T> implements Serializable {

    public final static int SUCCEED_CODE = 200;
    public final static int FAILURE_CODE = 500;
    private static final long serialVersionUID = 5470439706413565710L;

    private Integer status;
    private Integer code;
    private String msg;
    private T data;

    public ApiResult<T> ok() {
        this.status = SUCCEED_CODE;
        return this;
    }

    public ApiResult<T> ok(T data) {
        this.status = SUCCEED_CODE;
        this.msg = "SUCCEED";
        this.data = data;
        return this;
    }

    public ApiResult<T> ok(int code, T data) {
        this.status = SUCCEED_CODE;
        this.code = code;
        this.data = data;
        return this;
    }

    public ApiResult<T> error(String msg) {
        this.status = FAILURE_CODE;
        this.msg = msg;
        return this;
    }

    public ApiResult<T> error(int code, String msg) {
        this.status = FAILURE_CODE;
        this.code = code;
        this.msg = msg;
        return this;
    }

    public Integer getStatus() {
        return this.status;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public T getData() {
        return this.data;
    }

    public boolean isOK() {
        if (SUCCEED_CODE == status) {
            return true;
        }
        return false;
    }

    @Override
    public String toString() {
        return "ApiResult{" +
                "status=" + status +
                ", code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }

}
