package com.quhong.operation.clients.tencent;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/car")
public class CARController {
    private final static Logger logger = LoggerFactory.getLogger(CARController.class);
    private static final String SUCCESS = "{\"code\":0}";

    @Resource
    private CARService carService;

    /**
     * 录制回调，需要在腾讯云后台配置
     * <a href="https://cloud.tencent.com/document/product/267/47026">录制文件事件通知</a>
     */
    @PostMapping("/callback")
    public String callback(@RequestBody JSONObject body) {
        if (null == body) {
            logger.error("car callback body is null");
            return SUCCESS;
        }
        int eventType = body.getIntValue("event_type");
        String taskId = body.getString("stream_id");
        if (ObjectUtils.isEmpty(taskId)) {
            logger.error("car callback stream_id is null, body={}", JSON.toJSONString(body));
            return SUCCESS;
        }
        if (100 == eventType) {
            // 直播录制（文件） event_type = 100
            logger.info("car callback: {}", JSON.toJSONString(body));
            carService.callback(taskId, body.getString("video_url"), body.getString("duration"));
        } else if (341 == eventType) {
            // 录制异常事件 event_type = 341
            logger.error("car callback: {}", JSON.toJSONString(body));
            carService.endTask(taskId);
        } else {
            logger.info("unknown car callback: {}", JSON.toJSONString(body));
        }
        return SUCCESS;
    }
}
