package com.quhong.operation.clients.tencent;

import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class CloudAppRenderingTask {
    public final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private CARService carService;


    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "2 * * * * ?")
    public void renderingTask() {
        if (ServerConfig.isNotProduct()) {
            // 仅在正式服上线
            return;
        }
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        try {
            carService.taskScheduled();
        } catch (Exception e) {
            logger.error("renderingTask error: {}", e.getMessage());
        }
    }

}
