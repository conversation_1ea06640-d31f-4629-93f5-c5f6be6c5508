package com.quhong.operation.clients.tencent;

import com.quhong.datas.HttpResult;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.data.VideoRecordTaskData;
import com.quhong.mysql.service.VideoRecordTaskService;
import com.quhong.operation.clients.tencent.DTO.CreateDTO;
import com.quhong.operation.clients.tencent.DTO.TaskReviewDTO;
import com.quhong.operation.clients.tencent.VO.TaskReviewVO;
import com.quhong.operation.share.vo.PageVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * 视频录制
 */
@RestController
@RequestMapping("/video_task/")
public class VideoRecordTaskController {
    private final static Logger logger = LoggerFactory.getLogger(VideoRecordTaskController.class);

    @Resource
    private VideoRecordTaskService videoRecordTaskService;

    /**
     * 获取任务列表
     */
    @GetMapping("list")
    public HttpResult<PageVo<VideoRecordTaskData>> getTaskList(@RequestParam(defaultValue = "1") int page) {
        return HttpResult.getOk(videoRecordTaskService.getPageRecord(page));
    }

    /**
     * 新增录制任务
     */
    @PostMapping("add")
    public HttpResult<Object> addTask(@RequestBody CreateDTO dto) {
        int rid = dto.getRid();
        int startTime = dto.getStartTime();
        int endTime = dto.getEndTime();
        if (startTime <= 0 || endTime <= 0 || startTime >= endTime || ObjectUtils.isEmpty(rid)) {
            throw new CommonH5Exception(1, "参数错误");
        }
        videoRecordTaskService.createTask(rid, startTime, endTime);
        return HttpResult.getOk();
    }

    /**
     * 修改录制任务
     */
    @PostMapping("update")
    public HttpResult<Object> updateTask(@RequestBody CreateDTO dto) {
        String id = dto.getId();
        int rid = dto.getRid();
        int startTime = dto.getStartTime();
        int endTime = dto.getEndTime();
        if (ObjectUtils.isEmpty(id) || startTime <= 0 || endTime <= 0 || startTime >= endTime || ObjectUtils.isEmpty(rid)) {
            throw new CommonH5Exception(1, "参数错误");
        }
        videoRecordTaskService.updateTask(id, rid, startTime, endTime);
        return HttpResult.getOk();
    }

    /**
     * 删除录制任务
     */
    @GetMapping("delete")
    public HttpResult<Object> deleteTask(@RequestParam String id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new CommonH5Exception(1, "参数错误");
        }
        videoRecordTaskService.removeTask(id);
        return HttpResult.getOk();
    }

    /**
     * 上报用户观看时长
     */
    @PostMapping("taskReview")
    public HttpResult<Object> taskReview(@RequestParam String uid, @RequestBody TaskReviewDTO dto) {
        logger.info("taskReview uid={} taskId={} duration={}", uid, dto.getTaskId(), dto.getDuration());
        videoRecordTaskService.taskReview(uid, dto.getTaskId(), dto.getDuration());
        return HttpResult.getOk();
    }

    /**
     * 获取用户使用统计
     */
    @GetMapping("getUserStatistics")
    public HttpResult<List<TaskReviewVO>> getUserStatistics() {
        return HttpResult.getOk(videoRecordTaskService.getUserStatistics());
    }
}
