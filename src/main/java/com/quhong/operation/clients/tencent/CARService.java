package com.quhong.operation.clients.tencent;

import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.data.VideoRecordTaskData;
import com.quhong.mysql.service.VideoRecordTaskService;
import com.quhong.operation.clients.tencent.VO.TaskConfigVO;
import com.quhong.operation.constant.CarConstant;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * 腾讯云渲染、云直播
 * <a href="https://cloud.tencent.com/document/product/267/86994">云端原生录制</a>
 */
@Service
public class CARService {
    private final static Logger logger = LoggerFactory.getLogger(CARService.class);
    // 观察者名单: 8348500~8348509
    private static final List<String> WATCH_LIST = Arrays.asList("686cfc82e13de63cfc6c476e", "686cfc865a4c81542630e971", "686cfc88e13de63cfc6c476f",
            "686cfc8be13de63cfc6c4770", "686cfc8e5a4c81542630e972", "686cfc91e13de63cfc6c4771", "686cfc94e13de63cfc6c4772", "686cfc975a4c81542630e973",
            "686cfc995a4c81542630e974", "686cfc9ce13de63cfc6c4773");

    @Resource
    private VideoRecordTaskService videoRecordTaskService;
    @Resource
    private TencentCloudApi tencentCloudApi;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private MonitorSender monitorSender;


    public void taskScheduled() {
        for (VideoRecordTaskData task : videoRecordTaskService.getTaskListToStart()) {
            startTask(task.getId());
        }
        for (VideoRecordTaskData task : videoRecordTaskService.getTaskListToEnd()) {
            endTask(task.getId());
        }
    }

    public synchronized TaskConfigVO getTaskConfig() {
        List<VideoRecordTaskData> activeTaskList = videoRecordTaskService.getTaskListByStatus(CarConstant.START_RECORD);
        if (ObjectUtils.isEmpty(activeTaskList)) {
            return null;
        }
        // 正在被使用的观察者
        Set<String> inUseWatcher = CollectionUtil.listToPropertySet(activeTaskList, VideoRecordTaskData::getAid);
        for (VideoRecordTaskData taskData : activeTaskList) {
            if (!ObjectUtils.isEmpty(taskData.getAid())) {
                // 跳过已经分配了aid的任务
                continue;
            }
            for (String aid : WATCH_LIST) {
                if (!inUseWatcher.contains(aid)) {
                    videoRecordTaskService.updateTaskAid(taskData.getId(), aid);
                    String token = UUID.randomUUID().toString().replace("-", "");
                    basePlayerRedis.addToken(aid, token);
                    monitorSender.info("ustar_java_exception", "分配视频录制任务", "taskId=" + taskData.getId() + "\n" + "uid=" + aid + "\n" + "roomRid=" + taskData.getRid());
                    return new TaskConfigVO(aid, token, taskData.getRoomId());
                }
            }
        }
        return null;
    }

    public void startTask(String taskId) {
        videoRecordTaskService.startRecord(taskId);
        TaskConfigVO taskConfig = getTaskConfig();
        if (null == taskConfig) {
            logger.error("startTask taskId={} no available watcher", taskId);
            return;
        }
        // 申请并发
        tencentCloudApi.applyConcurrent(taskId);
        // 创建会话
        tencentCloudApi.createSession(taskId, taskConfig);
        // 开始推流
        tencentCloudApi.startPublishStream(taskId);
        logger.info("startTask taskId={}", taskId);
    }

    public void endTask(String taskId) {
        // 结束任务
        videoRecordTaskService.endRecord(taskId);
        // 停止推流
        tencentCloudApi.stopPublishStream(taskId);
        // 结束会话
        if (DateHelper.getNowSeconds() > 1753945200) {
            // 2025-07-31 15:00:00前不调用销毁tx排查问题
            tencentCloudApi.destroySession(taskId);
        }
        logger.info("endTask taskId={}", taskId);
    }

    public void callback(String taskId, String filePath, String duration) {
        videoRecordTaskService.finishRecord(taskId, filePath, duration);
        logger.info("callback taskId={} filePath={} duration={}", taskId, filePath, duration);
    }
}
