package com.quhong.operation.clients.tencent;

import com.alibaba.fastjson.JSON;
import com.quhong.operation.clients.tencent.VO.TaskConfigVO;
import com.tencentcloudapi.car.v20220110.CarClient;
import com.tencentcloudapi.car.v20220110.models.*;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class TencentCloudApi {
    private static final Logger logger = LoggerFactory.getLogger(TencentCloudApi.class);
    public static Credential cred = new Credential("IKIDwkOqR5d3JQMJBtOfljWfdkcGgroX1HJA", "gBg095h7JJ5f1B7qMdfjaDertkOp46ue");
    public static CarClient client = new CarClient(cred, "ap-shanghai");

    public void applyConcurrent(String uid) {
        try {
            ApplyConcurrentRequest applyConcurrentRequest = new ApplyConcurrentRequest();
            applyConcurrentRequest.setUserId(uid);
            applyConcurrentRequest.setUserIp("*************");
            applyConcurrentRequest.setProjectId("cap-wo3t5hw3");
            ApplyConcurrentResponse response = client.ApplyConcurrent(applyConcurrentRequest);
            logger.info("applyConcurrent: {}", JSON.toJSONString(response));
        } catch (TencentCloudSDKException e) {
            logger.error("applyConcurrent error: ", e);
        }
    }

    public String createParams(String uid, String token, String roomId) {
        return "--es uid " + uid + " --es token " + token + " --es roomId " + roomId;
    }

    public void createSession(String uid, TaskConfigVO taskConfig) {
        try {
            CreateSessionRequest req = new CreateSessionRequest();
            req.setUserId(uid);
            req.setUserIp("*************");
            // req.setClientSession(clientSession);
            req.setApplicationParameters(createParams(taskConfig.getUid(), taskConfig.getToken(), taskConfig.getRoomId()));
            req.setRunMode("RunWithoutClient");
            CreateSessionResponse response = client.CreateSession(req);
            logger.info("createSession: uid={} resp={}", uid, JSON.toJSONString(response));
        } catch (TencentCloudSDKException e) {
            logger.error("createSession error: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public void destroySession(String uid) {
        try {
            DestroySessionRequest req = new DestroySessionRequest();
            req.setUserId(uid);
            DestroySessionResponse response = client.DestroySession(req);
            logger.info("destroySession: uid={} resp={}", uid, JSON.toJSONString(response));
        } catch (TencentCloudSDKException e) {
            logger.error("destroySession error: ", e);
        }
    }

    public void startPublishStream(String uid) {
        try {
            StartPublishStreamRequest req = new StartPublishStreamRequest();
            req.setUserId(uid);
            StartPublishStreamResponse response = client.StartPublishStream(req);
            logger.info("startPublishStream: uid={} resp={}", uid, JSON.toJSONString(response));
        } catch (TencentCloudSDKException e) {
            logger.error("startPublishStream error: ", e);
        }
    }

    public void stopPublishStream(String uid) {
        try {
            StopPublishStreamRequest req = new StopPublishStreamRequest();
            req.setUserId(uid);
            StopPublishStreamResponse response = client.StopPublishStream(req);
            logger.info("stopPublishStream: uid={} resp={}", uid, JSON.toJSONString(response));
        } catch (TencentCloudSDKException e) {
            logger.error("stopPublishStream error: ", e);
        }
    }
}
