package com.quhong.operation.clients.zego.DTO;

import com.alibaba.fastjson.annotation.JSONField;

public class StorageParams {
    /**
     * 录制文件存储服务提供商，目前支持的存储服务提供商如下：
     * 1：亚马逊 S3
     * 2：阿里云 OSS
     * 3：腾讯云 COS
     * 4：七牛云 Kodo
     * 5：阿里云 Vod
     * 6：腾讯云 Vod
     * 7：华为云 OBS
     */
    @JSONField(name = "Vendor")
    private int vendor = 1;
    @JSONField(name = "Region")
    private String region = "ap-south-1"; // 云存储指定的地区信息
    @JSONField(name = "Bucket")
    private String bucket = "youstar-zego"; // 云存储 bucket
    @JSONField(name = "AccessKeyId")
    private String accessKeyId = "********************"; // 云存储的 access key，建议提供只写权限的访问密钥
    @JSONField(name = "AccessKeySecret")
    private String accessKeySecret = "KHGjWAssYNTHtogrqiSKdKfM7h0+be6NSxtCBJXM"; // 云存储的 secret key

    public int getVendor() {
        return vendor;
    }

    public void setVendor(int vendor) {
        this.vendor = vendor;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }
}
