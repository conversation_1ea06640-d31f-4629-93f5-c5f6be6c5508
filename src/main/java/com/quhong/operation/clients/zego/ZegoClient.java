package com.quhong.operation.clients.zego;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.operation.clients.zego.DTO.*;
import com.quhong.room.RoomStreamService;
import com.quhong.room.api.zego.ZegoApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Service
public class ZegoClient extends ZegoApi {
    private static final Logger logger = LoggerFactory.getLogger(ZegoClient.class);

    private static final String START_RECORD = "https://cloudrecord-api.zego.im/?Action=StartRecord";
    private static final String STOP_RECORD = "https://cloudrecord-api.zego.im/?Action=StopRecord";

    private static final RecordInputParams recordInputParams = new RecordInputParams();
    private static final RecordOutputParams recordOutputParams = new RecordOutputParams();
    private static final StorageParams storageParams = new StorageParams();


    @Resource
    private WebClient webClient;
    @Resource
    private RoomStreamService roomStreamService;

    @PostConstruct
    private void init() {
        if (ServerConfig.isProduct()) {
            recordOutputParams.setCallbackUrl("https://api.qmovies.tv/operation_java/api/zego/callback");
        } else {
            recordOutputParams.setCallbackUrl("https://test.qmovies.tv/operation_java/api/zego/callback");
        }
    }

    public StartRecordResp startRecord(String roomId, Integer rid) {
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(START_RECORD);
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            String streamId = roomStreamService.genStreamId(roomId);
            recordOutputParams.setOutputFolder("/zegoVideo/" + rid);
            StartRecordDTO dto = new StartRecordDTO(streamId, recordInputParams, recordOutputParams, storageParams);
            HttpResponseData<String> responseData = webClient.sendRestfulPost(url, JSON.toJSONString(dto), null);
            String body = responseData.getBody();
            if (StringUtils.isEmpty(body)) {
                logger.error("startRecord error, http status={} roomId={} streamId={}", responseData.getStatus(), roomId, streamId);
                return null;
            }
            return JSON.parseObject(body, StartRecordResp.class);
        } catch (Exception e) {
            logger.error("startRecord error roomId={} msg={}", roomId, e.getMessage());
            return null;
        }
    }

    public StartRecordResp stopRecord(String taskId) {
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(STOP_RECORD);
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("TaskId", taskId);
            HttpResponseData<String> responseData = webClient.sendRestfulPost(url, jsonObject.toJSONString(), null);
            String body = responseData.getBody();
            if (StringUtils.isEmpty(body)) {
                logger.error("stopRecord error, http status={} taskId={}", responseData.getStatus(), taskId);
                return null;
            }
            return JSON.parseObject(body, StartRecordResp.class);
        } catch (Exception e) {
            logger.error("startRecord error taskId={} msg={}", taskId, e.getMessage());
            return null;
        }
    }
}
