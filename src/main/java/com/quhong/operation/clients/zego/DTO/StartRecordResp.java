package com.quhong.operation.clients.zego.DTO;

import com.alibaba.fastjson.annotation.JSONField;

public class StartRecordResp {
    @JSONField(name = "Code")
    private int code; // 错误码
    @JSONField(name = "Message")
    private String message; // 错误描述
    @JSONField(name = "RequestId")
    private String requestId; // 请求 ID
    @JSONField(name = "Data")
    private StartRecordData data; // 响应对象

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public StartRecordData getData() {
        return data;
    }

    public void setData(StartRecordData data) {
        this.data = data;
    }
}
