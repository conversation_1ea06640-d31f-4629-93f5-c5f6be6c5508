package com.quhong.operation.clients.zego.DTO;

import com.alibaba.fastjson.annotation.JSONField;

public class RecordInputParams {
    /**
     * 录制模式。
     * 1：单流录制
     * 2：混流录制
     */
    @JSONField(name = "RecordMode")
    private int recordMode = 2;
    /**
     * 录制媒体流类型，仅适用于音视频流，白板只会录制视频。
     * 1：仅录制音频
     * 2：仅录制视频
     * 3：录制音视频（音视频文件合并）（默认值）
     * 4：录制音视频（音频视频文件分离）
     */
    @JSONField(name = "StreamType")
    private int streamType = 1;
    @JSONField(name = "MaxIdleTime")
    private int maxIdleTime = 300;
    /**
     * 混流参数，RecordMode 为 2 时必选
     */
    @JSONField(name = "MixConfig")
    private MixConfig mixConfig;

    public int getRecordMode() {
        return recordMode;
    }

    public void setRecordMode(int recordMode) {
        this.recordMode = recordMode;
    }

    public int getStreamType() {
        return streamType;
    }

    public void setStreamType(int streamType) {
        this.streamType = streamType;
    }

    public int getMaxIdleTime() {
        return maxIdleTime;
    }

    public void setMaxIdleTime(int maxIdleTime) {
        this.maxIdleTime = maxIdleTime;
    }

    public MixConfig getMixConfig() {
        return mixConfig;
    }

    public void setMixConfig(MixConfig mixConfig) {
        this.mixConfig = mixConfig;
    }
}
