package com.quhong.operation.clients.zego.DTO;

import java.util.List;

public class CallbackDetail {
    /**
     * 录制文件上传整体状态。
     * 1: 全部文件上传成功
     * 2: 部分文件上传成功（文件信息列表中有至少一个文件的 status 不为 3）。
     */
    private int upload_status;
    private List<FileInfo> file_info; // 文件信息列表。列表为空表示没有产生录制文件（房间内没有用户推流）。

    public int getUpload_status() {
        return upload_status;
    }

    public void setUpload_status(int upload_status) {
        this.upload_status = upload_status;
    }

    public List<FileInfo> getFile_info() {
        return file_info;
    }

    public void setFile_info(List<FileInfo> file_info) {
        this.file_info = file_info;
    }
}
