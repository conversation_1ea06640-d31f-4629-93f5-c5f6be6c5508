package com.quhong.operation.clients.zego.DTO;

public class FileInfo {

    private String user_id; // 录制流对应的推流用户 ID（混流时，为 mix_output_stream_id）。
    private String user_name; // 录制流对应的推流用户昵称（混流时，为 MixOutputStreamId）。
    private String stream_id; // 录制流对应的流 ID（混流时，为 MixOutputStreamId）。
    private String file_id; // 录制文件名称，详情请参考 开始录制 中的 OutputFileRule 参数。
    private String video_id; // 阿里云 Vod、腾讯云 Vod 上传成功得到的视频 ID 参数。阿里云 Vod 对应的是 VideoId，腾讯云 Vod 对应的是 FileId。
    private String file_url; // 文件访问 URL。第三方存储为七牛云或阿里云 Vod 时不返回。
    private String output_file_format; // 输出录制文件的格式，包括 “mp4”、“flv”、“hls”、“aac” 和 “jpg”。
    private long file_size; // 文件大小，单位：字节。
    private int duration; // 文件时长，单位：ms。
    private int resolution_width; // 视频分辨率宽，单位：像素。
    private int resolution_height; // 视频分辨率高，单位：像素。
    /**
     * 文件媒体类型。
     * 1：只有音频
     * 2：只有视频
     * 3：音视频
     */
    private int media_track_type;

    private long begin_timestamp; // 接收到房间流新增信令时的 Unix 时间戳，单位：ms。
    private long custom_begin_timestamp; // 用户自定义时间戳，该时间戳带在流 SEI 信息中，按照约定的协议从 SEI 中解析。
    /**
     * 文件状态。
     * 1：录制中，表示正在录制文件。
     * 2：上传中，表示正在上传录制文件至客户指定云存储。
     * 3：上传成功，表示上传录制文件至客户指定云存储成功。
     * 4：已经上传至备份云存储，表示上传客户指定云存储失败，已上传至 ZEGO 备份云存储。
     * 5：上传失败，表示上传客户指定云存储和 ZEGO 备份云存储均失败。
     */
    private int status;

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getStream_id() {
        return stream_id;
    }

    public void setStream_id(String stream_id) {
        this.stream_id = stream_id;
    }

    public String getFile_id() {
        return file_id;
    }

    public void setFile_id(String file_id) {
        this.file_id = file_id;
    }

    public String getVideo_id() {
        return video_id;
    }

    public void setVideo_id(String video_id) {
        this.video_id = video_id;
    }

    public String getFile_url() {
        return file_url;
    }

    public void setFile_url(String file_url) {
        this.file_url = file_url;
    }

    public String getOutput_file_format() {
        return output_file_format;
    }

    public void setOutput_file_format(String output_file_format) {
        this.output_file_format = output_file_format;
    }

    public long getFile_size() {
        return file_size;
    }

    public void setFile_size(long file_size) {
        this.file_size = file_size;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getResolution_width() {
        return resolution_width;
    }

    public void setResolution_width(int resolution_width) {
        this.resolution_width = resolution_width;
    }

    public int getResolution_height() {
        return resolution_height;
    }

    public void setResolution_height(int resolution_height) {
        this.resolution_height = resolution_height;
    }

    public int getMedia_track_type() {
        return media_track_type;
    }

    public void setMedia_track_type(int media_track_type) {
        this.media_track_type = media_track_type;
    }

    public long getBegin_timestamp() {
        return begin_timestamp;
    }

    public void setBegin_timestamp(long begin_timestamp) {
        this.begin_timestamp = begin_timestamp;
    }

    public long getCustom_begin_timestamp() {
        return custom_begin_timestamp;
    }

    public void setCustom_begin_timestamp(long custom_begin_timestamp) {
        this.custom_begin_timestamp = custom_begin_timestamp;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
