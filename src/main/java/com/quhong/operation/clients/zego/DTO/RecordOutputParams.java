package com.quhong.operation.clients.zego.DTO;

import com.alibaba.fastjson.annotation.JSONField;

public class RecordOutputParams {

    /**
     * 指定录制生成文件的格式，默认为 “mp4”。目前支持 “mp4”、“flv”、“hls”、“jpg” 和 "mp3"。
     * 如果录制 “mp4” 或 “flv”，且 StreamType 设为 4，则录制生成的音频文件格式为 aac。
     */
    @JSONField(name = "OutputFileFormat")
    private String outputFileFormat = "mp3";
    /**
     * 录制文件在第三方云存储的输出目录，默认为根目录。
     */
    @JSONField(name = "OutputFolder")
    private String outputFolder;
    /**
     * <a href="https://api.qmovies.tv/operation_java/api/zego/callback">prod</a>
     * <a href="https://test.qmovies.tv/operation_java/api/zego/callback">test</a>
     */
    @JSONField(name = "CallbackUrl")
    private String callbackUrl; // 自定义回调地址的 URL，不填写此参数时会使用服务开通时配置的回调地址。URL 支持 HTTP 和 HTTPS 协议。

    public String getOutputFileFormat() {
        return outputFileFormat;
    }

    public void setOutputFileFormat(String outputFileFormat) {
        this.outputFileFormat = outputFileFormat;
    }

    public String getOutputFolder() {
        return outputFolder;
    }

    public void setOutputFolder(String outputFolder) {
        this.outputFolder = outputFolder;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }
}
