package com.quhong.operation.clients.zego.DTO;

import com.alibaba.fastjson.annotation.JSONField;

public class StartRecordDTO {
    @JSONField(name = "RoomId")
    private String roomId; // 待录制的房间 ID
    @JSONField(name = "RecordInputParams")
    private RecordInputParams recordInputParams; // 录制任务输入参数
    @JSONField(name = "RecordOutputParams")
    private RecordOutputParams recordOutputParams; // 录制任务输出参数
    @JSONField(name = "StorageParams")
    private StorageParams storageParams; // 录制任务的存储配置

    public StartRecordDTO() {
    }

    public StartRecordDTO(String roomId, RecordInputParams recordInputParams, RecordOutputParams recordOutputParams, StorageParams storageParams) {
        this.roomId = roomId;
        recordInputParams.setMixConfig(new MixConfig(roomId));
        this.recordInputParams = recordInputParams;
        this.recordOutputParams = recordOutputParams;
        this.storageParams = storageParams;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public RecordInputParams getRecordInputParams() {
        return recordInputParams;
    }

    public void setRecordInputParams(RecordInputParams recordInputParams) {
        this.recordInputParams = recordInputParams;
    }

    public RecordOutputParams getRecordOutputParams() {
        return recordOutputParams;
    }

    public void setRecordOutputParams(RecordOutputParams recordOutputParams) {
        this.recordOutputParams = recordOutputParams;
    }

    public StorageParams getStorageParams() {
        return storageParams;
    }

    public void setStorageParams(StorageParams storageParams) {
        this.storageParams = storageParams;
    }
}
