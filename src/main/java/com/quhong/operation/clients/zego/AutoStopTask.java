package com.quhong.operation.clients.zego;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.ZegoRecord;
import com.quhong.mysql.service.ZegoRecordService;
import com.quhong.operation.clients.zego.DTO.StartRecordResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AutoStopTask {
    private final static Logger logger = LoggerFactory.getLogger(AutoStopTask.class);
    private static final int LOOP_TIME = 30 * 60 * 1000;

    @Resource
    private ZegoClient zegoClient;
    @Resource
    private ZegoRecordService zegoRecordService;

    /**
     * 超过4个小时自动关闭
     */
    @Scheduled(fixedDelay = LOOP_TIME, initialDelay = 5000)
    private void autoStopRecord() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<ZegoRecord> zegoRecordList = zegoRecordService.getNotStopTasks();
        logger.info("autoStopRecord zegoRecordList={}", zegoRecordList.size());
        for (ZegoRecord zegoRecord : zegoRecordList) {
            // 超过4个小时自动关闭
            if (nowSeconds - zegoRecord.getStartTime() > 4 * 60 * 60) {
                zegoRecordService.endRecord(zegoRecord.getTaskId());
                StartRecordResp resp = zegoClient.stopRecord(zegoRecord.getTaskId());
                if (null != resp && 0 == resp.getCode()) {
                    logger.info("auto stop record taskId={}", zegoRecord.getTaskId());
                } else {
                    logger.info("auto stop record taskId={} message={}", zegoRecord.getTaskId(), (null != resp ? resp.getMessage() : ""));
                }
            }
        }
    }
}
