package com.quhong.operation.clients.zego.DTO;

public class RecordCallback {
    private long app_id; // ZEGO 给开发者分配的 AppID，唯一标识一个应用。
    private String task_id; // 录制任务 ID，长度固定为 16 个字节的字符串。
    private String room_id; // 录制房间 ID。
    /**
     * event_type	Int	事件通知类型。
     * 1：录制文件上传状态通知，详细信息请查看 detail 参数。
     * 2：录制任务异常结束状态通知，详细信息请查看 detail 参数。
     * 3：录制过程中自定义背景图/水印图下载失败通知，详细信息请查看 detail 参数。
     * 4：录制过程中房间内流数量为 0 通知。
     * 房间内流的数量变为 0 后的 30 秒内会触发此事件。首次触发后，若房间内一直无流，会每隔 30 秒触发一次此事件直到有流或者任务因无流超时异常结束。
     * 5：录制正常退出通知。
     * 6：录制的流不存在，详细信息请查看 detail 参数。
     */
    private int event_type;
    private String message; // 事件描述。
    private String nonce; // 随机数，用于检验串计算。
    private String timestamp; // 回调发送时的 Unix 时间戳，用于检验串计算。
    private String signature; // 检验串，验证回调发送方身份。
    private int sequence; // 消息序列号，从 0 开始计数。
    private CallbackDetail detail; // 事件详细信息，根据 event_type 不同，该参数的成员不同。

    public long getApp_id() {
        return app_id;
    }

    public void setApp_id(long app_id) {
        this.app_id = app_id;
    }

    public String getTask_id() {
        return task_id;
    }

    public void setTask_id(String task_id) {
        this.task_id = task_id;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getEvent_type() {
        return event_type;
    }

    public void setEvent_type(int event_type) {
        this.event_type = event_type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public int getSequence() {
        return sequence;
    }

    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    public CallbackDetail getDetail() {
        return detail;
    }

    public void setDetail(CallbackDetail detail) {
        this.detail = detail;
    }
}
