package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.vo.WelcomePackReceiverVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ResTypeEnum;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.data.PackData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.WelcomePackConfigDao;
import com.quhong.mysql.dao.WelcomePackLogDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.WelcomePackConfigData;
import com.quhong.mysql.data.WelcomePackLogData;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.WelcomePackLogCondition;
import com.quhong.operation.share.dto.SendWelcomePackDTO;
import com.quhong.operation.share.dto.WelcomePackConfigDTO;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.WelcomePackRecordVO;
import com.quhong.operation.share.vo.WelcomePackVO;
import com.quhong.service.OfficialMsgService;
import com.quhong.service.WelcomePackActionService;
import com.quhong.utils.PageUtils;
import com.quhong.vo.PageVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.quhong.mysql.dao.WelcomePackConfigDao.STATUS_NORMAL;
import static com.quhong.service.WelcomePackActionService.*;

/**
 * 礼包Service
 *
 * <AUTHOR>
 * @date 2023/8/30
 */
@Service
public class WelcomePackService {
    private static final Logger logger = LoggerFactory.getLogger(WelcomePackService.class);

    @Resource
    private ManagerDao managerDao;
    @Resource
    private WelcomePackActionService welcomePackActionService;
    @Resource
    private WelcomePackConfigDao welcomePackConfigDao;
    @Resource
    private ResourceConfigService resourceConfigService;
    @Resource
    private GiftDao giftDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private WelcomePackLogDao welcomePackLogDao;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private NoticeNewDao noticeNewDao;

    public PageResultVO<WelcomePackVO> selectList(String uid, BaseCondition condition) {
        Manager manager = managerDao.getDataByUid(uid);
        if (manager == null) {
            throw new CommonH5Exception(new HttpCode(1, "您的账号异常"));
        }
        PageResultVO<WelcomePackVO> pageVO = new PageResultVO<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        List<WelcomePackConfigData> packConfigList = welcomePackConfigDao.getAllList();
        if (CollectionUtils.isEmpty(packConfigList)) {
            pageVO.setList(Collections.emptyList());
            pageVO.setTotal(0);
            return pageVO;
        }
        // 投递礼包时权限不足的账号无法看到礼包
        int roleLevel = manager.getRole() == null ? 1 : manager.getRole();
        packConfigList = packConfigList.stream().filter(p -> p.getOperationRoleLevel().contains(roleLevel)).collect(Collectors.toList());
        PageUtils.PageData<WelcomePackConfigData> pageData = PageUtils.getPageData(packConfigList, page, pageSize);
        if (CollectionUtils.isEmpty(pageData.list)) {
            pageVO.setList(Collections.emptyList());
            pageVO.setTotal(0);
            return pageVO;
        }
        List<WelcomePackVO> list = new ArrayList<>();
        for (WelcomePackConfigData data : pageData.list) {
            WelcomePackVO vo = new WelcomePackVO();
            BeanUtils.copyProperties(data, vo);
            vo.setRewardList(data.getPackList());
            list.add(vo);
        }
        pageVO.setList(list);
        pageVO.setTotal(packConfigList.size());
        return pageVO;
    }

    /**
     * 保存或修改礼包
     *
     * @param uid 操作人uid
     * @param dto 礼包信息
     * @return void
     */
    public void saveOrUpdate(String uid, WelcomePackConfigDTO dto) {
        checkSaveParam(dto);
        WelcomePackConfigData data = new WelcomePackConfigData();
        BeanUtils.copyProperties(dto, data);
        if (Objects.isNull(dto.getId())) {
            // 创建礼包
            data.setStatus(STATUS_NORMAL);
            data.setCreateTime(DateHelper.getNowSeconds());
            data.setCreateUid(uid);
            data.setUpdateTime(DateHelper.getNowSeconds());
            data.setUpdateUid(uid);
            welcomePackConfigDao.save(data);
        } else {
            // 修改礼包
            data = welcomePackConfigDao.getById(dto.getId());
            if (data == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            // 将DTO的数据拷贝到现有数据对象
            BeanUtils.copyProperties(dto, data);
            data.setStatus(STATUS_NORMAL);
            data.setUpdateTime(DateHelper.getNowSeconds());
            data.setUpdateUid(uid);
            welcomePackConfigDao.updateById(data);
        }
    }

    /**
     * 校验参数
     *
     * @param dto 礼包信息
     * @return void
     */
    private void checkSaveParam(WelcomePackConfigDTO dto) {
        if (!StringUtils.hasText(dto.getName())) {
            throw new CommonH5Exception(new HttpCode(1, "礼包名字不能为空"));
        }
        if (!StringUtils.hasText(dto.getTitle()) || !StringUtils.hasText(dto.getTitleAr())) {
            throw new CommonH5Exception(new HttpCode(1, "推送标题不能为空"));
        }
        if (!StringUtils.hasText(dto.getDesc()) || !StringUtils.hasText(dto.getDescAr())) {
            throw new CommonH5Exception(new HttpCode(1, "推送标题不能为空"));
        }
        if (CollectionUtils.isEmpty(dto.getPackList())) {
            throw new CommonH5Exception(new HttpCode(1, "奖励不能为空"));
        }
        if (CollectionUtils.isEmpty(dto.getAdminRoleLevel())) {
            throw new CommonH5Exception(new HttpCode(1, "admin账号权限不能为空"));
        }
        if (CollectionUtils.isEmpty(dto.getOperationRoleLevel())) {
            throw new CommonH5Exception(new HttpCode(1, "运营平台账号权限不能为空"));
        }
        for (PackData data : dto.getPackList()) {
            if (data == null) {
                throw new CommonH5Exception(new HttpCode(1, "有背包奖励为空，请检查"));
            }
            if (data.getResType() == null || data.getResType() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "系统错误，资源类型为空，请联系技术人员"));
            }
            // 获取资源名称用于错误提示
            ResTypeEnum resTypeEnum = ResTypeEnum.getByType(data.getResType());
            String resTypeName = (resTypeEnum != null) ? resTypeEnum.getNameEn() : "未知资源类型(" + data.getResType() + ")";
            // 历史原因。金币，钻石的ResType()和ResTypeEnum枚举中的不一样，需要手动转，ResTypeEnum枚举中是老的值
            if (BaseDataResourcesConstant.TYPE_DIAMOND==data.getResType()) {
                resTypeName = "Diamonds";
            }
            if (BaseDataResourcesConstant.TYPE_COIN==data.getResType()){
                resTypeName = "Coins";
            }
            // 检查资源id
            if (data.getResId() == null || data.getResId() == 0) {
                // 钻石金币不检查资源id
                if (data.getResType() != BaseDataResourcesConstant.TYPE_DIAMOND && data.getResType() != BaseDataResourcesConstant.TYPE_COIN) {
                    throw new CommonH5Exception(new HttpCode(1, String.format("%s奖励id为空，请检查", resTypeName)));
                }
            }
            // 检查资源类型
            if (data.getResType() == BaseDataResourcesConstant.TYPE_BAG_GIFT) {
                GiftData bagGift = giftDao.getBagGiftOne(data.getResId());
                if (bagGift == null) {
                    throw new CommonH5Exception(new HttpCode(1, "礼物要先设置成背包礼物才能下发"));
                }
            }
            // 检查图标
            if (!StringUtils.hasText(data.getIcon())) {
                throw new CommonH5Exception(new HttpCode(1, String.format("%s奖励图标为空，请检查", resTypeName)));
            }
            // 检查数量或天数
            if (data.getResType() == BaseDataResourcesConstant.TYPE_BADGE) {
                if (data.getNum() == null || data.getNum() == 0 || data.getNum() < -1) {
                    throw new CommonH5Exception(new HttpCode(1, String.format("%s奖励有效期或数量有误，请检查", resTypeName)));
                }
            }
            if (data.getNum() == null || data.getNum() == 0 || data.getNum() < 0) {
                throw new CommonH5Exception(new HttpCode(1, String.format("%s奖励有效期或数量有误，请检查", resTypeName)));
            }
        }
    }


    /**
     * 删除礼包
     *
     * @param uid 操作人uid
     * @param id  礼包id
     * @return void
     */
    public void remove(String uid, Integer id) {
        Manager manager = managerDao.getDataByUid(uid);
        WelcomePackConfigData data = welcomePackConfigDao.getById(id);
        if (data == null || data.getStatus() == WelcomePackConfigDao.STATUS_DELETED) {
            throw new CommonH5Exception(new HttpCode(1, "该礼包已删除"));
        }
        if (manager == null || !data.getOperationRoleLevel().contains(manager.getRole())) {
            throw new CommonH5Exception(new HttpCode(1, "权限不足"));
        }
        data.setUpdateTime(DateHelper.getNowSeconds());
        data.setUpdateUid(uid);
        data.setStatus(WelcomePackConfigDao.STATUS_DELETED);
        welcomePackConfigDao.updateById(data);
    }

    /**
     * 检测rid
     *
     * @param ridsText rid集合
     * @return com.quhong.operation.share.vo.PageVO<com.quhong.operation.share.vo.WelcomePackReceiverVO>
     */
    public PageVO<WelcomePackReceiverVO> check(String ridsText) {
        if (!StringUtils.hasText(ridsText)) {
            throw new CommonH5Exception(new HttpCode(1, "rid不能为空"));
        }
        PageVO<WelcomePackReceiverVO> vos = welcomePackActionService.check(ridsText);
        if (vos.getList().size() > 10) {
            throw new CommonH5Exception(new HttpCode(1, "rid最多十个"));
        }
        return vos;
    }

    /**
     * 发送礼包
     *
     * @param uid 操作人uid
     * @param dto 发送礼包参数
     * @return void
     */
    public void send(String uid, SendWelcomePackDTO dto) {
        Manager manager = managerDao.getDataByUid(uid);
        if (manager == null) {
            throw new CommonH5Exception(new HttpCode(1, "账号异常，请联系技术人员"));
        }
        welcomePackActionService.sendWelcomePack(dto.getAidSet(), dto.getPackId(), manager.getAccount(), manager.getRole(), WelcomePackActionService.OPERATION_SOURCE);
    }

    /**
     * 撤销发送礼包
     *
     * @param uid 操作人uid
     * @param dto 撤销发送礼包参数
     * @return void
     */
    public void withdraw(String uid, SendWelcomePackDTO dto) {
        Manager manager = managerDao.getDataByUid(uid);
        if (manager == null) {
            throw new CommonH5Exception(new HttpCode(1, "账号异常，请联系技术人员"));
        }
        // 获取礼包发送记录
        WelcomePackLogData packLogData = welcomePackLogDao.getById(dto.getRecordId());
        if (null == packLogData) {
            throw new CommonH5Exception(new HttpCode(1, "未找到礼包发送记录"));
        }
        if (packLogData.getStatus() == WelcomePackLogDao.WITHDRAW_STATUS) {
            logger.info("revert welcome pack. pack has reverted. id={}", packLogData.getPackId());
            throw new CommonH5Exception(new HttpCode(1, "该礼包已撤回"));
        }
        if (DateHelper.getNowSeconds() - packLogData.getCtime() > TimeUnit.DAYS.toSeconds(1)) {
            logger.info("revert welcome pack. pack post more then one day. id={}", packLogData.getPackId());
            throw new CommonH5Exception(new HttpCode(1, "礼包下发已超过一天，无法撤回"));
        }
        WelcomePackConfigData welcomePackConfig = welcomePackConfigDao.getById(packLogData.getPackId());
        if (null == welcomePackConfig) {
            throw new CommonH5Exception(new HttpCode(1, "未找到礼包"));
        }
        if (!welcomePackConfig.getOperationRoleLevel().contains(manager.getRole())) {
            throw new CommonH5Exception(new HttpCode(1, "权限不足"));
        }
        for (PackData packData : welcomePackConfig.getPackList()) {
            // 撤回资源
            if (packData.getResType() == BaseDataResourcesConstant.TYPE_COIN) {
                heartRecordDao.changeHeartByUid(packLogData.getUid(), -packData.getNum(), WITHDRAW_TITLE, WITHDRAW_TITLE);
            } else if (packData.getResType() == BaseDataResourcesConstant.TYPE_DIAMOND) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setRoomId("");
                moneyDetailReq.setUid(packLogData.getUid());
                moneyDetailReq.setAtype(WITHDRAW_ATYPE);
                moneyDetailReq.setChanged(-packData.getNum());
                moneyDetailReq.setTitle(WITHDRAW_TITLE);
                moneyDetailReq.setDesc(WITHDRAW_TITLE);
                dataCenterService.reduceBeans(moneyDetailReq);
            } else {
                mqSenderService.asyncHandleResources(getResourcesDTO(packLogData.getUid(), packData, 4));
            }
        }
        // 删除官方通知
        officialMsgService.deleteMsg(packLogData.getOfficialMsgId());
        // 更新消息数量
        noticeNewDao.deleteNotificationMsgByOfficialId(packLogData.getOfficialMsgId());
        // 更新日志
        packLogData.setStatus(WelcomePackLogDao.WITHDRAW_STATUS);
        packLogData.setRevertName(manager.getAccount());
        packLogData.setUtime(DateHelper.getNowSeconds());
        welcomePackLogDao.updateById(packLogData);
    }

    private List<PackData> fillPackList(Integer packId) {
        WelcomePackConfigData data = welcomePackConfigDao.getById(packId);
        if (data == null || CollectionUtils.isEmpty(data.getPackList())) {
            return Collections.emptyList();
        }
        return data.getPackList();
    }

    /**
     * 礼包投递记录
     *
     * @param condition 查询条件
     * @return com.quhong.operation.share.vo.PageResultVO<com.quhong.operation.share.vo.WelcomePackRecordVO>
     */
    public PageResultVO<WelcomePackRecordVO> record(WelcomePackLogCondition condition) {
        PageResultVO<WelcomePackRecordVO> resultVO = new PageResultVO<>();
        IPage<WelcomePackLogData> iPage = welcomePackLogDao.selectPage(condition.getStrId(), condition.getStartDate(), condition.getEndDate(), condition.getPage(), condition.getPageSize());
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            return resultVO;
        }
        List<WelcomePackRecordVO> resultListVO = new ArrayList<>();
        for (WelcomePackLogData record : iPage.getRecords()) {
            WelcomePackRecordVO vo = new WelcomePackRecordVO();
            BeanUtils.copyProperties(record, vo);
            ActorData actorData = actorDao.getActorDataFromCache(record.getUid());
            if (null == actorData) {
                continue;
            }
            vo.setName(actorData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            vo.setRid(actorData.getRid());
            vo.setStrRid(actorData.getStrRid());
            vo.setCountry(actorData.getCountry());
            vo.setGender(actorData.getFb_gender() == 1 ? "男" : "女");
            vo.setRegisterTime(new ObjectId(actorData.getUid()).getTimestamp());
            vo.setPackList(fillPackList(record.getPackId()));
            resultListVO.add(vo);
        }
        resultVO.setList(resultListVO);
        resultVO.setTotal(iPage.getTotal());
        return resultVO;
    }
}