package com.quhong.operation.server;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.datas.DayTimeData;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.BeautifulRidChangeLogOPDao;
import com.quhong.operation.dao.OPBigRDayDao;
import com.quhong.operation.dao.OpBigRDao;
import com.quhong.operation.dao.OperationActorDao;
import com.quhong.operation.share.dto.pay.PayUserListDTO;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.BeautifulRidChangeLogOP;
import com.quhong.operation.share.mysql.OPBigRDayData;
import com.quhong.operation.share.mysql.OpBigRData;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.pay.BeautifulRidLogVO;
import com.quhong.operation.share.vo.pay.PayLossVO;
import com.quhong.operation.share.vo.pay.PayUserInfoReportVO;
import com.quhong.operation.share.vo.pay.PayUserInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class PayServer {

    private static final Logger logger = LoggerFactory.getLogger(PayServer.class);

    @Resource
    private OPBigRDayDao opBigRDayDao;
    @Resource
    private OpBigRDao opBigRDao;
    @Autowired
    private OperationActorDao actorDao;
    @Resource
    private BeautifulRidChangeLogOPDao beautifulRidChangeLogOPDao;

    public PayUserInfoVO userInfo(String date) {
        PayUserInfoVO resultVO = new PayUserInfoVO();
        DayTimeData daytimedata = DateHelper.ARABIAN.getContinuesDays(date);
        long nowStamp = daytimedata.getTime() * 1000L;
        Date nowDate = new Date(nowStamp);
        long lastTamp = DateHelper.ARABIAN.getDayOffset(nowStamp, -1);
        Date lastDate = new Date(lastTamp);
        String nowDay = DateHelper.ARABIAN.formatDateInDay2(nowDate);
        String lastDay = DateHelper.ARABIAN.formatDateInDay2(lastDate);
        int now = Integer.parseInt(nowDay);
        int last = Integer.parseInt(lastDay);
        OPBigRDayData nowBigR = opBigRDayDao.getData(now);
        OPBigRDayData lastBigR = opBigRDayDao.getData(last);
        if (nowBigR != null) {
            PayLossVO halfLoss = new PayLossVO();
            PayLossVO readyLoss = new PayLossVO();
            PayLossVO severeLoss = new PayLossVO();
            PayLossVO allLoss = new PayLossVO();
            halfLoss.setBigR(nowBigR.getR4l3Count());
            halfLoss.setMiddleR(nowBigR.getR3l3Count());
            halfLoss.setSmallR(nowBigR.getR2l3Count());
            readyLoss.setBigR(nowBigR.getR4l2Count());
            readyLoss.setMiddleR(nowBigR.getR3l2Count());
            readyLoss.setSmallR(nowBigR.getR2l2Count());
            severeLoss.setBigR(nowBigR.getR4l4Count());
            severeLoss.setMiddleR(nowBigR.getR3l4Count());
            severeLoss.setSmallR(nowBigR.getR2l4Count());
            allLoss.setBigR(nowBigR.getR4l5Count());
            allLoss.setMiddleR(nowBigR.getR3l5Count());
            allLoss.setSmallR(nowBigR.getR2l5Count());
            resultVO.setBigRNum(nowBigR.getR4Count());
            resultVO.setMiddleRNum(nowBigR.getR3Count());
            resultVO.setSmallRNum(nowBigR.getR2Count());
            resultVO.setHalfLoss(halfLoss);
            resultVO.setReadyLoss(readyLoss);
            resultVO.setSevereLoss(severeLoss);
            resultVO.setAllLoss(allLoss);
            if (lastBigR != null) {
                resultVO.setBigRFlag(nowBigR.getR4Count() - lastBigR.getR4Count());
                resultVO.setMiddleRFlag(nowBigR.getR3Count() - lastBigR.getR3Count());
                resultVO.setSmallRFlag(nowBigR.getR2Count() - lastBigR.getR2Count());
            }
        }
        return resultVO;
    }

    public List<PayUserInfoReportVO> getAllPayUser() {
        List<OPBigRDayData> list = opBigRDayDao.getAll();
        List<PayUserInfoReportVO> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            int order = 1;
            for (OPBigRDayData opBigRDayData : list) {
                PayUserInfoReportVO payUserInfoReportVO = new PayUserInfoReportVO();
                payUserInfoReportVO.setOrder(order);
                String date = dayTime2Day(opBigRDayData.getDayTime() + "");
                payUserInfoReportVO.setDate(date);
                payUserInfoReportVO.setBigRNum(opBigRDayData.getR4Count());
                payUserInfoReportVO.setMiddleRNum(opBigRDayData.getR3Count());
                payUserInfoReportVO.setSmallRNum(opBigRDayData.getR2Count());
                payUserInfoReportVO.setHalfLoss(opBigRDayData.getR2l3Count() + opBigRDayData.getR3l3Count() + opBigRDayData.getR4l3Count());
                payUserInfoReportVO.setReadyLoss(opBigRDayData.getR2l2Count() + opBigRDayData.getR3l2Count() + opBigRDayData.getR4l2Count());
                payUserInfoReportVO.setSevereLoss(opBigRDayData.getR2l4Count() + opBigRDayData.getR3l4Count() + opBigRDayData.getR4l4Count());
                payUserInfoReportVO.setAllLoss(opBigRDayData.getR2l5Count() + opBigRDayData.getR3l5Count() + opBigRDayData.getR4l5Count());
                order = order + 1;
                resultList.add(payUserInfoReportVO);
            }
        }
        return resultList;
    }

    private String dayTime2Day(String dayTime) {
        Date date = com.quhong.operation.utils.DateHelper.ARABIAN.parseDay2(dayTime);
        return DateHelper.ARABIAN.formatDateInDay(date);
    }

    public JSONObject payUserList(PayUserListDTO payUserListDTO) {
        JSONObject result = new JSONObject();
        int userValue = payUserListDTO.getUserValue();
        int loseLevel = payUserListDTO.getLoseLevel();
        int search = payUserListDTO.getSearch();
        int page = payUserListDTO.getPage();
        int pageSize = payUserListDTO.getPageSize();
        String aType = payUserListDTO.getaType();
        Actor actorData = null;
        String uid = null;
        if (search != -1) {
            ApiResult<Actor> actor = actorDao.getActorByRid(search);
            if (actor.isOK()) {
                if (actor.getData() == null) {
                    return result;
                }
                actorData = actor.getData();
                uid = actorData.get_id().toString();
            } else {
                return result;
            }
        }
        if ("data".equals(aType)) {
            List<OpBigRData> bigRList = null;
            if (page != -1) {
                PageResultVO<OpBigRData> pageResultVO = opBigRDao.pageBigR(userValue, loseLevel, uid, page, pageSize);
                bigRList = pageResultVO.getList();
                result.put("total", pageResultVO.getTotal());
            } else {
                bigRList = opBigRDao.listBigR(userValue, loseLevel, uid);
                result.put("total", bigRList.size());
            }
            List<JSONObject> items = new ArrayList<>();
            for (int i = 0; i < bigRList.size(); i++) {
                OpBigRData opBigRData = bigRList.get(i);
                JSONObject item = new JSONObject();
                item.put("rank", i + 1);
                item.put("user_value", opBigRData.getrLevel());
                item.put("uid", opBigRData.getUserId());
                if (actorData == null) {
                    Actor bigActor = actorDao.getActorByUid(opBigRData.getUserId()).getData();
                    item.put("rid", bigActor.getRid());
                    item.put("beans", bigActor.getBeans());
                } else {
                    item.put("rid", actorData.getRid());
                    item.put("beans", actorData.getBeans());
                }
                item.put("last_30_charge", opBigRData.getLast30Charge());
                item.put("honor_exp", opBigRData.getHonorExp());
                item.put("lose_level", opBigRData.getLossLevel());
                JSONObject perform = new JSONObject();
                perform.put("last_li_time", DateHelper.ARABIAN.formatDateTime(new Date(opBigRData.getLastLiTime() * 1000)));
                perform.put("last_15_rtime", opBigRData.getLast15Rtime());
                perform.put("last_7_send", opBigRData.getLast7Send());
                perform.put("last_7_recv", opBigRData.getLast7Recv());
                perform.put("last_cg_time", DateHelper.ARABIAN.formatDateTime(new Date(opBigRData.getLastCgTime() * 1000)));
                item.put("lose_perform", perform);
                items.add(item);
            }
            result.put("items", items);
        } else {
            result.put("items", new ArrayList<>());
            result.put("total", 0);
        }
        return result;
    }

    public List<BeautifulRidLogVO> beautifulRidLog(String aid, String atype) {
        List<BeautifulRidLogVO> result = new ArrayList<>();
        if ("beaut".equals(atype)) {
            List<BeautifulRidChangeLogOP> list = beautifulRidChangeLogOPDao.listByUid(aid);
            for (BeautifulRidChangeLogOP beautifulRidChangeLogOP : list) {
                BeautifulRidLogVO beautifulRidLogVO = new BeautifulRidLogVO();
                BeanUtils.copyProperties(beautifulRidChangeLogOP, beautifulRidLogVO);
                beautifulRidLogVO.setcTime(DateHelper.ARABIAN.formatDateTime(new Date(beautifulRidChangeLogOP.getcTime() * 1000L)));
                result.add(beautifulRidLogVO);
            }
        }
        return result;
    }
}
