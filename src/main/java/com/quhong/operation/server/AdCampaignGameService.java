package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.dao.AdCampaignGameDao;
import com.quhong.mysql.data.AdCampaignGameData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.RoomTypeGameListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 广告系列游戏映射配置服务
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-30
 */
@Service
public class AdCampaignGameService {
    private static final Logger logger = LoggerFactory.getLogger(AdCampaignGameService.class);

    @Resource
    private AdCampaignGameDao adCampaignGameDao;

    /**
     * 游戏配置
     */
    public RoomTypeGameListVO roomTypeGameList() {
        RoomTypeGameListVO vo = new RoomTypeGameListVO();
        List<RoomTypeGameListVO.RoomTypeGameInfo> roomTypeGameInfoList = new ArrayList<>();
        RoomTypeGameListVO.RoomTypeGameInfo roomTypeGameInfo = new RoomTypeGameListVO.RoomTypeGameInfo();
        roomTypeGameInfo.setRoomType(1);
        roomTypeGameInfo.setRoomTypeName("Voice Room");
        List<RoomTypeGameListVO.GameInfo> gameInfoList = new ArrayList<>();
        gameInfoList.add(new RoomTypeGameListVO.GameInfo(1, "Lucky Wheel"));
        gameInfoList.add(new RoomTypeGameListVO.GameInfo(2, "Ludo"));
        gameInfoList.add(new RoomTypeGameListVO.GameInfo(3, "UNO"));
        gameInfoList.add(new RoomTypeGameListVO.GameInfo(4, "Monster Crush"));
        gameInfoList.add(new RoomTypeGameListVO.GameInfo(5, "Domino"));
        gameInfoList.add(new RoomTypeGameListVO.GameInfo(6, "Carrom Pool"));
        gameInfoList.add(new RoomTypeGameListVO.GameInfo(8, "Jackaroo"));
        gameInfoList.add(new RoomTypeGameListVO.GameInfo(9, "Baloot"));
        roomTypeGameInfo.setGameInfoList(gameInfoList);
        roomTypeGameInfoList.add(roomTypeGameInfo);


        RoomTypeGameListVO.RoomTypeGameInfo roomTypeGameInfo2 = new RoomTypeGameListVO.RoomTypeGameInfo();
        roomTypeGameInfo2.setRoomType(5);
        roomTypeGameInfo2.setRoomTypeName("Game Room");
        List<RoomTypeGameListVO.GameInfo> gameInfoList2 = new ArrayList<>();
        gameInfoList2.add(new RoomTypeGameListVO.GameInfo(2, "Ludo"));
        gameInfoList2.add(new RoomTypeGameListVO.GameInfo(3, "UNO"));
        gameInfoList2.add(new RoomTypeGameListVO.GameInfo(4, "Monster Crush"));
        gameInfoList2.add(new RoomTypeGameListVO.GameInfo(5, "Domino"));
        gameInfoList2.add(new RoomTypeGameListVO.GameInfo(6, "Carrom Pool"));
        gameInfoList2.add(new RoomTypeGameListVO.GameInfo(7, "Billiard (8 Ball)"));
        gameInfoList2.add(new RoomTypeGameListVO.GameInfo(8, "Jackaroo"));
        gameInfoList2.add(new RoomTypeGameListVO.GameInfo(9, "Baloot"));
        roomTypeGameInfo2.setGameInfoList(gameInfoList2);
        roomTypeGameInfoList.add(roomTypeGameInfo2);
        vo.setRoomTypeGameInfoList(roomTypeGameInfoList);
        return vo;
    }


    /**
     * 广告系列游戏映射列表查询
     */
    public PageResultVO<AdCampaignGameData> list(BaseCondition condition) {
        PageResultVO<AdCampaignGameData> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        IPage<AdCampaignGameData> pageData = adCampaignGameDao.selectPageList(search, condition.getStatus(), page, pageSize);
        pageVO.setList(pageData.getRecords());
        pageVO.setTotal(pageData.getTotal());
        return pageVO;
    }

    /**
     * 新增广告系列游戏映射
     */
    public void addData(AdCampaignGameData dto) {
        // 参数校验
        validateAdCampaignGameData(dto);
        
        // 检查广告系列是否已存在
        AdCampaignGameData existingData = adCampaignGameDao.findByCampaign(dto.getCampaign());
        if (existingData != null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "广告系列已存在，请勿重复添加");
        }
        
        AdCampaignGameData data = new AdCampaignGameData();
        BeanUtils.copyProperties(dto, data);
        data.setMtime(DateHelper.getNowSeconds());
        data.setCtime(DateHelper.getNowSeconds());
        adCampaignGameDao.insert(data);
    }

    /**
     * 校验广告系列游戏映射数据
     */
    private void validateAdCampaignGameData(AdCampaignGameData dto) {
        // 校验广告系列
        if (StringUtils.isEmpty(dto.getCampaign())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "广告系列不能为空");
        }
        
        // 校验广告系列长度
        if (dto.getCampaign().length() > 200) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "广告系列长度不能超过255个字符");
        }
        
        // 校验游戏Key长度
        if (dto.getGameType() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "游戏类型输入有误");
        }

        // 校验状态
        if (dto.getStatus() == null || (dto.getStatus() != 0 && dto.getStatus() != 1)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "状态必须为0（禁用）或1（启用）");
        }
    }

    /**
     * 更新广告系列游戏映射
     */
    public void updateData(AdCampaignGameData dto) {
        if (dto.getId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "ID不能为空");
        }

        AdCampaignGameData data = adCampaignGameDao.selectOne(dto.getId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "数据不存在");
        }

        // 参数校验
        validateAdCampaignGameData(dto);
        
        // 检查广告系列是否被其他记录使用（排除当前记录）
        AdCampaignGameData existingData = adCampaignGameDao.findByCampaign(dto.getCampaign());
        if (existingData != null && !existingData.getId().equals(dto.getId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "广告系列已被其他记录使用");
        }
        
        BeanUtils.copyProperties(dto, data);
        data.setMtime(DateHelper.getNowSeconds());
        adCampaignGameDao.update(data);
    }

    /**
     * 删除广告系列游戏映射（软删除，设置状态为0）
     */
    public void deleteData(Integer id) {
        if (id == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "ID不能为空");
        }

        AdCampaignGameData data = adCampaignGameDao.selectOne(id);
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "数据不存在");
        }

        // 设置状态为禁用
        data.setStatus(0);
        data.setMtime(DateHelper.getNowSeconds());
        adCampaignGameDao.update(data);
    }
}
