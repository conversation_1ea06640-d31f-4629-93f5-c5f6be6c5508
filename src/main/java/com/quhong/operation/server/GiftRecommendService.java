package com.quhong.operation.server;

import com.quhong.core.config.ServerConfig;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.GiftRecommendDao;
import com.quhong.mongo.data.GiftRecommendData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.dto.GiftRecommendDTO;
import com.quhong.operation.share.vo.GiftRecommendVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class GiftRecommendService {
    private static final Logger logger = LoggerFactory.getLogger(GiftRecommendService.class);
    private static final String RECOMMEND_URL = ServerConfig.isProduct() ? "https://static.youstar.live/spread/?recommendId=" : "https://test2.qmovies.tv/spread/?recommendId=";

    @Resource
    private GiftRecommendDao giftRecommendDao;



    public PageResultVO<GiftRecommendVO> giftRecommendList(BaseCondition condition){
        PageResultVO<GiftRecommendVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<GiftRecommendData>  giftRecommendList = giftRecommendDao.selectPage(start, pageSize);

        List<GiftRecommendVO> voList = new ArrayList<>();
        for(GiftRecommendData data: giftRecommendList){
            GiftRecommendVO vo = new GiftRecommendVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            vo.setRecommend_link(RECOMMEND_URL + vo.getDocId());
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(giftRecommendDao.selectCount());
        return pageVO;
    }


    public void addGiftRecommendData(GiftRecommendDTO dto){

        GiftRecommendData data = new GiftRecommendData();
        BeanUtils.copyProperties(dto, data);
        giftRecommendDao.save(data);
    }

    public void updateGiftRecommendData(GiftRecommendDTO dto) {

        GiftRecommendData data = giftRecommendDao.findDataById(dto.getDocId());
        if(data == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        Update update = new Update();
        update.set("extra_config", dto.getExtra_config());
        update.set("gifts", dto.getGifts());
        giftRecommendDao.updateData(data, update);

    }


}
