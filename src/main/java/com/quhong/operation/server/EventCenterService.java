package com.quhong.operation.server;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.EventCenterDao;
import com.quhong.mongo.data.EventCenterData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.EventCenterVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.service.HomeBannerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class EventCenterService{
    private static final Logger logger = LoggerFactory.getLogger(EventCenterService.class);

    @Resource
    private EventCenterDao eventCenterDao;
    @Resource
    private ActorDao actorDao;



    public PageResultVO<EventCenterVO> eventCenterList(BaseCondition condition){
        PageResultVO<EventCenterVO> pageVO = new PageResultVO<>();
        int status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<EventCenterData> EventCenterList = eventCenterDao.selectPage(search, status, start, pageSize);
        List<EventCenterVO> voList = new ArrayList<>();
        for(EventCenterData data: EventCenterList){
            EventCenterVO vo = new EventCenterVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            if (data.getCycle() == 1) {
                // 周期活动
                int cycleTime = data.getEndTime() - data.getStartTime();
                int nowTime = DateHelper.getNowSeconds();
                int cycleNum = (nowTime - data.getStartTime()) / cycleTime;
                vo.setStartTime(data.getStartTime() + cycleNum * cycleTime);
                vo.setEndTime(data.getEndTime() + cycleNum * cycleTime);
            }
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(eventCenterDao.selectCount(search, status));
        return pageVO;
    }

    private void paramCheck(EventCenterVO dto){
        try {
            if(StringUtils.isEmpty(dto.getNameEn()) || StringUtils.isEmpty(dto.getNameAr())){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "标题未设置");
            }

            if(StringUtils.isEmpty(dto.getIconEn()) || StringUtils.isEmpty(dto.getIconAr())){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "图标未设置");
            }

            if(dto.getFilterType() == HomeBannerService.FILTER_TYPE_USER){
                String[] ridList = dto.getFilterItem().split(",");
                List<String> uidList = new ArrayList<>();

                for (String ridStr : ridList) {
                    ActorData actorData = actorDao.getActorByStrRid(ridStr);
                    if(actorData == null){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                    }
                    uidList.add(actorData.getUid());
                }
                dto.setFilterItem(String.join(",", uidList));
            } else if (dto.getFilterType() == HomeBannerService.FILTER_TYPE_REGISTER) {
                String[] splitDay = dto.getFilterItem().split("-");
                int startDay = Integer.parseInt(splitDay[0]);
                int endDay = Integer.parseInt(splitDay[1]);
            }
        }catch (Exception e){
            logger.error("addIndexBanner error:{}", e.getMessage(), e);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), e.getMessage());
        }
    }


    public void addEventCenterData(EventCenterVO dto){

        this.paramCheck(dto);
        EventCenterData data = new EventCenterData();
        BeanUtils.copyProperties(dto, data);
        int currentTime = DateHelper.getNowSeconds();
        data.setMtime(currentTime);
        data.setCtime(currentTime);
        eventCenterDao.save(data);
    }

    public void updateEventCenterData(EventCenterVO dto) {

        EventCenterData data = eventCenterDao.getDataByID(dto.getDocId());
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        this.paramCheck(dto);
        Update update = new Update();
        update.set("nameEn", dto.getNameEn());
        update.set("nameAr", dto.getNameAr());
        update.set("descEn", dto.getDescEn());
        update.set("descAr", dto.getDescAr());
        update.set("iconEn", dto.getIconEn());
        update.set("iconAr", dto.getIconAr());
        update.set("link", dto.getLink());
        update.set("eventTypeList", dto.getEventTypeList());
        update.set("startTime", dto.getStartTime());
        update.set("endTime", dto.getEndTime());
        update.set("showStartTime", dto.getShowStartTime());
        update.set("showEndTime", dto.getShowEndTime());
        update.set("sticky", dto.getSticky());
        update.set("status", dto.getStatus());
        update.set("cycle", dto.getCycle());
        update.set("filterType", dto.getFilterType());
        update.set("filterItem", dto.getFilterItem());
        update.set("mtime", DateHelper.getNowSeconds());
        eventCenterDao.updateData(dto.getDocId(), update);
    }
}
