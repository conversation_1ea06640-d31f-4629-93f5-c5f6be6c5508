package com.quhong.operation.server;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.SpringUtils;
import com.quhong.enums.ServerType;
import com.quhong.monitor.BaseMonitorSender;
import com.quhong.monitor.MonitorSender;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.constant.ChargeBeanApplyConstant;
import com.quhong.operation.dao.*;
import com.quhong.operation.share.data.IncreaseDiamondData;
import com.quhong.operation.share.el.MoneyDetailES;
import com.quhong.operation.share.elasticsearch.dao.MoneyDetailEsDao;
import com.quhong.operation.share.mongobean.*;
import com.quhong.operation.share.mysql.PartyGirl;
import com.quhong.operation.share.vo.ChargeBeanApplyVO;
import com.quhong.operation.share.vo.ChargeTotalVO;
import com.quhong.operation.share.vo.ChargeTotalVOs;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.operation.utils.StringUtil;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020/7/7
 */
@Service
public class BeansManagerServer {

    private final static Logger logger = LoggerFactory.getLogger(BeansManagerServer.class);
    private final String ADMIN_DIAMONDS_DAILY_TITLE = "admin diamonds daily";
    private final String PARTY_GIRL_TITLE = "admin charge for party girl";
    private final String PARTY_GIRL_DESC = "Party Girl support"; // 跟新party girl打钻名单
    private final String EVERY_DAY_DESC = "admin charge for every day"; // 跟新every day打钻名单
    private final String ADMIN_CHARGE_SALARY_DESC = "admin charge for salary";
    private final Integer OPERATION_SEND_TYPE = 5;
    private final String ES_FRIDAY_SALARY_CHARGE_TITLE = "operating room friday salary"; // 周五运营房薪资打钻
    private final String ES_SALARY_TITLE = "admin charge for sundry salary"; // 批量打钻
    private final String UPDATE_ROLL_CALL_TITLE = "update roll call title"; // 跟新打钻名单
    private final String RAMADAN_GIRLS_SUPPORT_DESC = "Ramadan girls support";
    private final String RAMADAN_GIRLS_SUPPORT_TITLE = "Ramadan girls support";
    private final String USER_QUESTIONNAIRE_TITLE = "User Questionnaire";
    private final String Q_A_REWARDS_TITLE = "Q and A Rewards";

    @Resource(name = "writeEsTemplate")
    private ElasticsearchTemplate writeEsTemplate;
    @Autowired
    private MoneyOpLogDao moneyOpLogDao;
    @Autowired
    private MoneyToolServer moneyToolServer;
    @Autowired
    private PartyGirlDao partyGirlDao;
    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private ChargeBeanApplyDao chargeBeanApplyDao;
    @Autowired
    private ManagerDao managerDao;
    @Value("${beans:0}")
    private Integer[] beans;
    @Autowired
    private ChargeBeanFailureListDao chargeBeanFailureListDao;
    @Autowired
    private FileServer fileServer;
    @Autowired
    private PtgSendBeansDao ptgSendBeansDao;
    @Resource
    private MoneyDetailEsDao moneyDetailEsDao;

    private static final Pattern intPattern = Pattern.compile("[^0-9]");

    /**
     * 获取充值统计信息
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 统计信息
     */
    public ApiResult<ChargeTotalVOs> getChargeTotal(Integer start, Integer end) {
        logger.info("method getTotal param start={}, end={}", start, end);
        ApiResult<ChargeTotalVOs> result = new ApiResult<>();
        Set<String> set = new HashSet<>();

        // 获取admin 荣誉充值记录
        ApiResult<List<MoneyOpLog>> apiResult = moneyOpLogDao.getMoneyOpList(start, end, 1);
        logger.info("getMoneyOpList charge=1 result code={} msg={}", apiResult.getCode(), apiResult.getMsg());
        if (!apiResult.isOK()) {
            return result.error(apiResult.getMsg());
        }
        ChargeTotalVO honorVo = getMoneyOpLogData(apiResult.getData(), set);
        Set<String> totalPeopleNumSet = new HashSet<>(set);
        set.clear();

        // 获取admin 非荣誉充值记录
        apiResult = moneyOpLogDao.getMoneyOpList(start, end, 2);
        logger.info("getMoneyOpList charge=2 result code={} msg={}", apiResult.getCode(), apiResult.getMsg());
        if (!apiResult.isOK()) {
            return result.error(apiResult.getMsg());
        }
        ChargeTotalVO notHonorVo = getMoneyOpLogData(apiResult.getData(), set);
        totalPeopleNumSet.addAll(set);
        Set<String> gratisPeopleNumSet = new HashSet<>(set);
        set.clear();

        // 获取es中需要的数据
        List<MoneyDetailES> list = getESOperationData(start, end);

        // 业务统计
        set.clear();
        ChargeTotalVOs vos = operationSendBeansTotal(list, set);
        totalPeopleNumSet.addAll(set);
        gratisPeopleNumSet.addAll(set);
        set.clear();

        vos.setAdminHonorCharge(honorVo); // admin 荣誉充值
        vos.setRealityBeans(honorVo); // 目前真实钻就是admin 荣誉充值
        vos.setAdminNotHonorCharge(notHonorVo); // admin 非荣誉充值

        // 添加免费钻的统计
        totalGratis(vos, gratisPeopleNumSet.size());
        // 全部数据统计汇总
        totalAll(vos, totalPeopleNumSet.size());
        totalPeopleNumSet.clear();
        gratisPeopleNumSet.clear();

        return result.ok(vos);
    }

    private ChargeTotalVO getMoneyOpLogData(List<MoneyOpLog> honorList, Set<String> set) {
        ChargeTotalVO vo = new ChargeTotalVO();
        set.clear();
        for (MoneyOpLog log : honorList) {
            if (null == log) {
                continue;
            }
            vo.addBeans(log.getBeans());
            vo.addCount();
            set.add(log.getUid());
        }
        vo.setChargePeople(set.size());
        return vo;
    }

    /**
     * 通过条件获取es数据
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return list
     */
    private List<MoneyDetailES> getESOperationData(Integer start, Integer end) {
        if (end < start) {
            return new ArrayList<>();
        }
        QueryBuilder gteMtime = QueryBuilders.rangeQuery("mtime").gte(start).lte(end);
        QueryBuilder type = QueryBuilders.termQuery("atype", 5);
        QueryBuilder queryBuilder = QueryBuilders.boolQuery().must(gteMtime).must(type);

        int esPageSize = 10000;
        Pageable page = PageRequest.of(0, esPageSize); // 分页0页开始 每页大小
        List<MoneyDetailES> list = new ArrayList<>();
        boolean flag = true;
        while (flag) {
            SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(queryBuilder).withIndices(moneyDetailEsDao.getMoneyDetailIndexNames(-3)).withPageable(page).build();
            List<MoneyDetailES> detailList = writeEsTemplate.queryForList(searchQuery, MoneyDetailES.class);
            if (null == detailList || esPageSize > detailList.size()) {
                flag = false;
            } else {
                page = page.next();
            }
            if (!CollectionUtils.isEmpty(detailList)) {
                list.addAll(detailList);
            }
        }
        logger.info("result list size={}", list.size());
        return list;
    }

    /**
     * 统计operation免费打钻的数据
     *
     * @param list 免费打钻记录
     * @return 统计的数据
     */
    private ChargeTotalVOs operationSendBeansTotal(List<MoneyDetailES> list, Set<String> set) {
        if (CollectionUtils.isEmpty(list)) {
            ChargeTotalVOs vos = new ChargeTotalVOs();
            vos.setEverydayCharge(new ChargeTotalVO());
            vos.setPartyGirlCharge(new ChargeTotalVO());
            vos.setFridaySalaryCharge(new ChargeTotalVO());
            vos.setSalaryCharge(new ChargeTotalVO());
            return vos;
        }
        set.clear();

        // everyday manager room send beans
        ChargeTotalVO everydayManagerRoom = new ChargeTotalVO();
        Set<String> everydayPeople = new HashSet<>();
        // party girl
        ChargeTotalVO partyGirl = new ChargeTotalVO();
        Set<String> partyGirlPeople = new HashSet<>();
        // 周五薪资发放
        ChargeTotalVO fridaySalary = new ChargeTotalVO();
        Set<String> fridaySalaryPeople = new HashSet<>();
        // 拉新、答疑、监控工资打钻
        ChargeTotalVO salary = new ChargeTotalVO();
        Set<String> salaryPeople = new HashSet<>();
        for (MoneyDetailES detail : list) {
            if (null == detail) {
                continue;
            }
            if ("admin diamonds daily".equals(detail.getTitle())) {
                everydayManagerRoom.addCount();
                everydayManagerRoom.addBeans(detail.getChanged());
                everydayPeople.add(detail.getUid());
            }

            if ("admin charge for party girl".equals(detail.getTitle())) {
                partyGirl.addCount();
                partyGirl.addBeans(detail.getChanged());
                partyGirlPeople.add(detail.getUid());
            }

            if ("operating room friday salary".equals(detail.getTitle())) {
                fridaySalary.addCount();
                fridaySalary.addBeans(detail.getChanged());
                fridaySalaryPeople.add(detail.getUid());
            }

            if (ES_SALARY_TITLE.equals(detail.getTitle())) {
                salary.addCount();
                salary.addBeans(detail.getChanged());
                salaryPeople.add(detail.getUid());
            }


        }

        everydayManagerRoom.setChargePeople(everydayPeople.size());
        partyGirl.setChargePeople(partyGirlPeople.size());
        fridaySalary.setChargePeople(fridaySalaryPeople.size());
        salary.setChargePeople(salaryPeople.size());

        ChargeTotalVOs vos = new ChargeTotalVOs();
        vos.setEverydayCharge(everydayManagerRoom);
        vos.setPartyGirlCharge(partyGirl);
        vos.setFridaySalaryCharge(fridaySalary);
        vos.setSalaryCharge(salary);

        set.addAll(everydayPeople);
        set.addAll(partyGirlPeople);
        set.addAll(fridaySalaryPeople);
        set.addAll(salaryPeople);
        return vos;
    }

    /**
     * 统计免费钻的数据
     *
     * @param vos       数据来源于数据归属
     * @param peopleNum 得到免费钻的人数
     */
    private void totalGratis(ChargeTotalVOs vos, Integer peopleNum) {
        ChargeTotalVO vo = new ChargeTotalVO();
        // 免费钻次数等于operation后台免费钻加非荣誉钻次数
        vo.setChargeCount(
                vos.getAdminNotHonorCharge().getChargeCount() +
                        vos.getEverydayCharge().getChargeCount() +
                        vos.getPartyGirlCharge().getChargeCount() +
                        vos.getFridaySalaryCharge().getChargeCount() +
                        vos.getSalaryCharge().getChargeCount());
        // 免费钻等于operation后台免费钻加非荣誉钻
        vo.setChargeBeans(
                vos.getAdminNotHonorCharge().getChargeBeans() +
                        vos.getEverydayCharge().getChargeBeans() +
                        vos.getPartyGirlCharge().getChargeBeans() +
                        vos.getFridaySalaryCharge().getChargeBeans() +
                        vos.getSalaryCharge().getChargeBeans());
        vo.setChargePeople(peopleNum);
        vos.setGratisBeans(vo);
    }

    private void totalAll(ChargeTotalVOs vos, Integer peopleNum) {
        ChargeTotalVO vo = new ChargeTotalVO();
        vo.setChargeBeans(vos.getRealityBeans().getChargeBeans() +
                vos.getGratisBeans().getChargeBeans());
        vo.setChargeCount(vos.getRealityBeans().getChargeCount() +
                vos.getGratisBeans().getChargeCount());
        vo.setChargePeople(peopleNum);
        vos.setTotal(vo);
    }

    /**
     * 周五薪资打钻方法
     *
     * @param file 打钻rid名单文件
     * @param type 打钻类型              等待加入
     * @param desc es打钻desc
     * @return 打钻失败名单
     */
    public ApiResult<List<String>> fridaySendBeans(MultipartFile file, Integer type, String desc, String esTitle, String uid) {
        ApiResult<List<String>> result = new ApiResult<>();
        List<String> chargeBeanError = new ArrayList<>();
        try {
            Map<String, List<String>> listMap = ExcelUtils.readExcel(file.getInputStream());
            // 校验打钻数额，只接受2000，4000，7000,8000和10000数额的打钻
            for (String header : listMap.keySet()) {
                String beanNum = header.substring("bean=".length());
                try {
                    int bean = Integer.parseInt(beanNum);
                    if (Arrays.stream(beans).noneMatch(value -> value == bean)) {
                        chargeBeanError.add("打钻数额不正确！请检查数额：" + bean);
                    }
                } catch (Exception e) {
                    logger.error("parse header bean value exception header={}", e.getMessage());
                    chargeBeanError.add("parse header bean value exception header=" + header);
                    return result.ok(chargeBeanError);
                }
            }
            if (!chargeBeanError.isEmpty()) {
                return result.ok(chargeBeanError);
            }
            ChargeBeanApply chargeBeanApply = new ChargeBeanApply();
            List<UserChargeBean> chargeBeanList = new ArrayList<>();
            chargeBeanApply.setDesc(desc);
            chargeBeanApply.setEsTitle(esTitle);
            chargeBeanApply.setUid(uid);
            chargeBeanApply.setCtime(DateHelper.getNowSeconds());
            chargeBeanApply.setMtime(DateHelper.getNowSeconds());
            chargeBeanApply.setStatus(ChargeBeanApplyConstant.STATUS_PENDING);
            chargeBeanApply.setChargeType(1);
            chargeBeanApply.setReadStatus(ChargeBeanApplyConstant.UN_READ);
            chargeBeanApply.setUsername(managerDao.getDataByUid(uid).getAccount());
            for (String header : listMap.keySet()) {
                List<String> list = listMap.get(header);
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                logger.info("header row value={}", header);
                if (!header.startsWith("bean=")) {
                    logger.info("header not start with 'bean=' header={}", header);
                    chargeBeanError.add("header not start with 'bean=', header=" + header);
                    continue;
                }
                String beanNum = header.substring("bean=".length());
                int bean;
                try {
                    bean = Integer.parseInt(beanNum);
                } catch (Exception e) {
                    logger.error("parse header bean value exception header={}", e.getMessage());
                    chargeBeanError.add("parse header bean value exception header=" + header);
                    continue;
                }
                //打钻操作
                for (String rid : list) {
                    if (StringUtils.isEmpty(rid.trim())) {
                        logger.info("rid is empty, rid={}", rid);
                        continue;
                    }
                    UserChargeBean userChargeBean = new UserChargeBean();
                    userChargeBean.setRid(rid.trim());
                    userChargeBean.setNum(bean);
                    chargeBeanList.add(userChargeBean);
                }
                //插入数据apply
                chargeBeanApply.setApplyList(chargeBeanList);
                chargeBeanApplyDao.saveOne(chargeBeanApply);
            }
            //打钻告警群
            if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
                sendAlarmGroup(chargeBeanApply, "周五薪资打钻");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.ok(chargeBeanError);
    }

    private void sendAlarmGroup(ChargeBeanApply chargeBeanApply, String desc) {
        if (Objects.isNull(chargeBeanApply)) {
            return;
        }
        BaseMonitorSender monitorSender = SpringUtils.getBean(MonitorSender.class);
        List<UserChargeBean> applyList = chargeBeanApply.getApplyList();
        int diamondsSum = 0;
        Set<String> uidSet = new HashSet<>();
        for (UserChargeBean bean : applyList) {
            diamondsSum += bean.getNum();
            uidSet.add(bean.getRid());
        }
        monitorSender.info("diamonds", desc, "人数 =" + uidSet.size() + ",钻石总数 =" + diamondsSum);
    }

    public ApiResult<List<String>> handlerFridaySendBeans(String applyId, int status) {
        ApiResult<List<String>> result = new ApiResult<>();
        List<String> chargeBeanError = new ArrayList<>();
        ChargeBeanApply chargeBeanApply = chargeBeanApplyDao.getApply(applyId);
        if (chargeBeanApply == null) {
            logger.error("charge beans apply error. charge beans apply not exist. apply_id={}", applyId);
            return result.error("charge beans apply not exist");
        }
        if (chargeBeanApply.getStatus() != ChargeBeanApplyConstant.STATUS_PENDING) {
            logger.error("charge beans apply error. charge apply processed. apply_id={} status={}", applyId, chargeBeanApply.getStatus());
            return result.error("charge beans apply processed");
        }
        chargeBeanApplyDao.updateStatus(chargeBeanApply.get_id().toString(), status);
        List<UserChargeBean> applyList = chargeBeanApply.getApplyList();
        if (status == ChargeBeanApplyConstant.STATUS_AGREE) {
            if (CollectionUtils.isEmpty(chargeBeanApply.getApplyList())) {
                // 更新打钻名单
                boolean flag = confirmUpdateRollCall(chargeBeanApply);
                return flag ? result.ok(chargeBeanError) : result.error("更新打钻名单失败");
            }
            //打钻操作
            String desc = chargeBeanApply.getDesc();
            String esTitle = chargeBeanApply.getEsTitle();
            for (UserChargeBean beanApply : applyList) {
                logger.info("rid={},num={}", beanApply.getRid(), beanApply.getNum());
                sendBeans(chargeBeanError, beanApply.getNum(), beanApply.getRid(), esTitle, desc);
            }
        } else {
            if (PARTY_GIRL_TITLE.equals(chargeBeanApply.getEsTitle())) {
                ptgSendBeansDao.updateStatus(chargeBeanApply.get_id().toString(), 2);
            }
        }
        return result.ok(chargeBeanError);
    }

    private Boolean confirmUpdateRollCall(ChargeBeanApply chargeBeanApply) {
        if (PARTY_GIRL_TITLE.equals(chargeBeanApply.getEsTitle())) {
            ptgSendBeansDao.delete(1);
            ptgSendBeansDao.updateStatus(chargeBeanApply.get_id().toString(), 1);
        }
        return true;
    }

    public List<ChargeBeanApplyVO> getChargeApplyData(String uid) {
        List<ChargeBeanApply> chargeBeanApplyList;
        if (!StringUtils.isEmpty(uid)) {
            chargeBeanApplyList = chargeBeanApplyDao.listApply(uid, ChargeBeanApplyConstant.STATUS_REJECT, ChargeBeanApplyConstant.UN_READ);
        } else {
            chargeBeanApplyList = chargeBeanApplyDao.getAll(ChargeBeanApplyConstant.STATUS_PENDING, ES_FRIDAY_SALARY_CHARGE_TITLE);
        }
        return getChargeBeanApplyVOS(chargeBeanApplyList);
    }

    public List<ChargeBeanApplyVO> getChargeApplyData() {
        List<String> esTitleList = new ArrayList<>();
        esTitleList.add(ES_SALARY_TITLE);
        esTitleList.add(USER_QUESTIONNAIRE_TITLE);
        esTitleList.add(Q_A_REWARDS_TITLE);
        List<ChargeBeanApply> chargeBeanApplyList = chargeBeanApplyDao.getAll(ChargeBeanApplyConstant.STATUS_PENDING, esTitleList);
        return getChargeBeanApplyVOS(chargeBeanApplyList);
    }

    public List<ChargeBeanApplyVO> getUpdateRollCallApplyList() {
        List<String> esTitleList = new ArrayList<>();
        esTitleList.add(PARTY_GIRL_TITLE);
        esTitleList.add(ADMIN_DIAMONDS_DAILY_TITLE);
        List<ChargeBeanApply> chargeBeanApplyList = chargeBeanApplyDao.getAll(ChargeBeanApplyConstant.STATUS_PENDING, esTitleList);
        return getChargeBeanApplyVOS(chargeBeanApplyList);
    }

    private List<ChargeBeanApplyVO> getChargeBeanApplyVOS(List<ChargeBeanApply> chargeBeanApplyList) {
        List<ChargeBeanApplyVO> resList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(chargeBeanApplyList)) {
            for (ChargeBeanApply chargeBeanApply : chargeBeanApplyList) {
                ChargeBeanApplyVO chargeBeanApplyVO = new ChargeBeanApplyVO();
                chargeBeanApplyVO.copyFrom(chargeBeanApply);
                resList.add(chargeBeanApplyVO);
            }
        }
        return resList;
    }

    private void sendBeans(List<String> chargeBeanError, int bean, String rid, String esTitle, String desc) {
        try {
            // 打钻方法
            Long mLeft = sendBeans(bean, rid, esTitle, desc, false);
            if (mLeft < 1) {
                chargeBeanError.add(rid + "=" + bean);
                logger.error("rid={} send beans fail", rid);
            }
        } catch (Exception e) {
            chargeBeanError.add(rid + "=" + bean);
            logger.error("rid={} send beans fail, error msg={}", rid, e.getMessage());
        }
    }

    /**
     * 批量打钻接口
     *
     * @param ridList 批量名单
     * @param beans   钻石数
     * @param title   打钻标题
     * @param desc    打钻备注
     * @return 错误打钻名单
     */
    public ApiResult<List<String>> batchSendBean(List<String> ridList, int beans, String title, String desc) {
        ApiResult<List<String>> result = new ApiResult<>();
        List<String> errorList = new ArrayList<>();
        for (String value : ridList) {
            String rid = value.trim();
            if (StringUtils.isEmpty(rid)) {
                errorList.add("rid=" + value);
                continue;
            }
            Long mLeft = sendBeans(beans, rid, title, desc, true);
            if (mLeft < 1) {
                errorList.add("rid=" + rid);
            }
        }

        return result.ok(errorList);
    }

    /**
     * 打钻
     *
     * @param beans    钻石
     * @param rid      rid
     * @param title    标题
     * @param desc     备注
     * @param everyday 是否每日打钻
     * @return 打钻后钻石
     */
    private Long sendBeans(int beans, String rid, String title, String desc, boolean everyday) {
        if (StringUtils.isEmpty(rid)) {
            return -1L;
        }
        if (0 == beans) {
            return -1L;
        }
        ApiResult<Long> result =
                moneyToolServer.chargeBeansByRid(rid, beans, title, OPERATION_SEND_TYPE, desc, everyday);
        if (!result.isOK()) {
            logger.info("rid={} charge fail, fail info: {}", rid, result.getMsg());
            return -1L;
        }
        return result.getData();
    }

    /**
     * 更新partyGirl名单
     *
     * @param file partyGirl名单
     * @return 结果
     */
    public ApiResult<Boolean> updatePartyGirlRollCall(MultipartFile file) {
        ApiResult<Boolean> result = new ApiResult<>();
        try {
            partyGirlDao.dropCollection();
            String[] weekDays = new String[]{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"};
            for (String weekDay : weekDays) {
                Map<String, List<String>> listMap = ExcelUtils.readExcel(file.getInputStream(), weekDay);
                if (CollectionUtils.isEmpty(listMap)) {
                    continue;
                }
                for (String header : listMap.keySet()) {
                    if (!header.startsWith("rid")) {
                        logger.error("header not start with 'rid' header={}", header);
                        continue;
                    }
                    List<String> ridList = listMap.get(header);
                    PartyGirl pg = new PartyGirl();
                    for (String rid : ridList) {
                        ApiResult<Actor> apiResult = actorDao.getUidByStrRid(rid);
                        if (apiResult.isOK() && null != apiResult.getData()) {
                            pg.setUid(apiResult.getData().get_id().toString());
                            pg.setRid(rid);
                            pg.setWhatDay(weekDay);
                            partyGirlDao.save(pg);
                        } else {
                            logger.error("rid={} not exist {}", rid, apiResult.getMsg());
                        }
                    }
                }
            }
        } catch (IOException e) {
            logger.error("{}", e.getMessage(), e);
            return result.error(e.getMessage());
        }
        return result.ok(true);
    }

    /**
     * 批量打钻
     *
     * @param file
     * @param type
     * @param desc
     * @param title
     * @param uid
     * @return
     */
    public ApiResult<String> salaryBeans(MultipartFile file, Integer type, String desc, String title, String uid) {
        ApiResult<String> result = new ApiResult<>();
        String batchNo = StringUtil.getUUID();
        boolean falg = true;
        if ("Questionnaire reward".equals(desc)) {
            title = USER_QUESTIONNAIRE_TITLE;
        }
        if ("Q and A Rewards".equals(desc)) {
            title = Q_A_REWARDS_TITLE;
        }
        try {
            Map<String, List<String>> listMap = ExcelUtils.readExcel(file.getInputStream());
            Set<String> headerSet = listMap.keySet();
            // 校验模板格式
            if (!(headerSet.contains("rid") && headerSet.contains("打钻数量") && headerSet.size() == 2)) {
                logger.error("模板格式不正确");
                return result.ok("模板格式不正确");
            }
            String[] rids = listMap.get("rid").toArray(new String[listMap.get("rid").size()]);
            String[] beans = listMap.get("打钻数量").toArray(new String[listMap.get("rid").size()]);
            if (rids.length == 0) {
                return result.ok("数据为空");
            }
            List<IncreaseDiamondData> diamondDataList = new ArrayList<>();
            int beanNumSum = 0;
            int maxBeanNum = 3000;
            for (int i = 0; i < rids.length; i++) {
                try {
                    int beanNum = Integer.parseInt(beans[i]);
                    // 单次单个rid打钻数量不能超过3000钻
                    if (beanNum > maxBeanNum) {
                        logger.error("单次单个RID的数量为{},不能超过{}", beanNum, maxBeanNum);
                        falg = false;
                        // 保存失败名单
                        ChargeBeanFailureList failureList = new ChargeBeanFailureList();
                        SimpleDateFormat df = new SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒");
                        failureList.setFailureRid(rids[i]);
                        failureList.setBatchNo(batchNo);
                        failureList.setDiamondNum(beanNum);
                        failureList.setUid(uid);
                        failureList.setReason("超过打钻上限"+ maxBeanNum + "，下发失败");
                        failureList.setFailureTime(df.format(new Date(System.currentTimeMillis() + 60 * 60 * 1000 * 8)));
                        failureList.setCtime(DateHelper.getNowSeconds());
                        chargeBeanFailureListDao.saveOne(failureList);
                    }
                    IncreaseDiamondData data = new IncreaseDiamondData();
                    //去掉非数字的所有字符串
                    String ridStr = rids[i].trim();
                    if (StringUtils.isEmpty(ridStr)) {
                        logger.info("rid is empty, rid={}", ridStr);
                        continue;
                    }
                    data.setRid(ridStr);
                    data.setBean(beanNum);
                    diamondDataList.add(data);
                    beanNumSum += beanNum;
                } catch (Exception e) {
                    logger.error("parse beanNum or rid error,rid={},beanNum={}", rids[i], beans[i], e);
                    return result.error("parse beanNum or rid error");
                }
            }
            if (!falg) {
                return result.error(3333, batchNo);
            }
            // 单次批量打钻总额上线不能超过500000
            if (beanNumSum > 500000) {
                logger.error("单次批量打钻总额为{},不能超过500000", beanNumSum);
                return result.error("超过单次批量打钻上限导致下发失败，请检查钻石数量分批次打钻");
            }
            ChargeBeanApply chargeBeanApply = new ChargeBeanApply();
            List<UserChargeBean> chargeBeanList = new ArrayList<>();
            chargeBeanApply.setDesc(desc);
            chargeBeanApply.setEsTitle(title);
            chargeBeanApply.setUid(uid);
            chargeBeanApply.setCtime(DateHelper.getNowSeconds());
            chargeBeanApply.setMtime(DateHelper.getNowSeconds());
            chargeBeanApply.setStatus(ChargeBeanApplyConstant.STATUS_PENDING);
            chargeBeanApply.setChargeType(1);
            chargeBeanApply.setReadStatus(ChargeBeanApplyConstant.UN_READ);
            chargeBeanApply.setUsername(managerDao.getDataByUid(uid).getAccount());
            //打钻操作
            for (IncreaseDiamondData data : diamondDataList) {
                UserChargeBean userChargeBean = new UserChargeBean();
                userChargeBean.setRid(data.getRid());
                userChargeBean.setNum(data.getBean());
                chargeBeanList.add(userChargeBean);
            }
            //插入数据apply
            chargeBeanApply.setApplyList(chargeBeanList);
            chargeBeanApplyDao.saveOne(chargeBeanApply);
            //打钻告警群
            if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
                sendAlarmGroup(chargeBeanApply, "批量打钻");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.ok();
    }

}
