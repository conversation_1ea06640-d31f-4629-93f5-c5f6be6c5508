package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.dao.RoomMicThemeDao;
import com.quhong.mysql.data.RoomMicThemeData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Service
public class RoomMicThemeService {
    private static final Logger logger = LoggerFactory.getLogger(RoomMicThemeService.class);

    @Resource
    private RoomMicThemeDao  roomMicThemeDao;


    public PageResultVO<RoomMicThemeData> roomMicThemeConfigList(BaseCondition condition) {
        PageResultVO<RoomMicThemeData> pageVO = new PageResultVO<>();
        int status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        IPage<RoomMicThemeData> iPageData = roomMicThemeDao.selectPageList(search, status, start, pageSize);
        pageVO.setList(iPageData.getRecords());
        pageVO.setTotal(iPageData.getTotal());
        return pageVO;
    }

    public void addRoomMicTheme(RoomMicThemeData dto) {
        if (ObjectUtils.isEmpty(dto.getDetailUrl()) || ObjectUtils.isEmpty(dto.getUrl())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "列表图或预览图未上传");
        }

        if(dto.getTid() <= 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "麦位主题背景id未填写");
        }

        RoomMicThemeData data = new RoomMicThemeData();
        BeanUtils.copyProperties(dto, data);
        data.setCtime(DateHelper.getNowSeconds());
        roomMicThemeDao.insertOne(data);
    }

    public void updateRoomMicTheme(RoomMicThemeData dto) {
        RoomMicThemeData data = roomMicThemeDao.selectById(dto.getId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "麦位主题为空");
        }

        if (ObjectUtils.isEmpty(dto.getDetailUrl()) || ObjectUtils.isEmpty(dto.getUrl())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "列表图或预览图未上传");
        }

        if(dto.getTid() <= 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "麦位主题背景id未填写");
        }

        BeanUtils.copyProperties(dto, data);
        roomMicThemeDao.updateOne(data);
    }

}
