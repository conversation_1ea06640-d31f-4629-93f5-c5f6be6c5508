package com.quhong.operation.server;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ResourceGroupData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ExtraMicFrameDao;
import com.quhong.mongo.dao.MicFrameDao;
import com.quhong.mongo.dao.MicFrameSourceDao;
import com.quhong.mongo.data.MicFrameSourceData;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.dto.MicFrameDTO;
import com.quhong.operation.share.vo.MicFrameVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceVO;
import com.quhong.operation.utils.StringUtil;
import com.quhong.redis.GoodsListHomeRedis;
import com.quhong.room.redis.MicFrameRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class MicFrameService implements ResourceService {
    private static final Logger logger = LoggerFactory.getLogger(MicFrameService.class);
    public static final int TYPE_MIC = 2;

    @Resource
    private MicFrameSourceDao micFrameSourceDao;
    @Resource
    private MicFrameDao micFrameDao;
    @Resource
    private ExtraMicFrameDao extraMicFrameDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;



    public PageResultVO<MicFrameVO> micFrameList(ItemCondition condition){
        PageResultVO<MicFrameVO> pageVO = new PageResultVO<>();
        Integer status = condition.getStatus();
        Integer itemType = condition.getItemType();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<MicFrameSourceData> micFrameList = micFrameSourceDao.selectMicFramePage(itemType, status, search, start, pageSize);
        // List<Integer> micFrameIdList = micFrameList.stream().map(MicFrameSourceData::getMic_id).collect(Collectors.toList());
        // Map<Integer, Integer> resourceGroupMap1 = micFrameDao.findResourceGroupList(micFrameIdList).stream().collect(Collectors.toMap(ResourceGroupData::getResourceId, ResourceGroupData::getCount));
        // Map<Integer, Integer> resourceGroupMap2 = extraMicFrameDao.findResourceGroupList(micFrameIdList).stream().collect(Collectors.toMap(ResourceGroupData::getResourceId, ResourceGroupData::getCount));

        List<MicFrameVO> voList = new ArrayList<>();
        for(MicFrameSourceData data: micFrameList){
            MicFrameVO vo = new MicFrameVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            vo.setCache_mic(micFrameRedis.getMicSource(data.getMic_id()));
            // int count1 = resourceGroupMap1.getOrDefault(data.getMic_id(), 0);
            // int count2 = resourceGroupMap2.getOrDefault(data.getMic_id(), 0);
            vo.setOwnUser(0);

            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(micFrameSourceDao.selectCount(itemType, status, search));
        return pageVO;
    }


    public void addMicFrameData(MicFrameDTO dto){

        if(StringUtils.isEmpty(dto.getMic_icon()) || StringUtils.isEmpty(dto.getMic_source())){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "图标或资源图未上传");
        }

        MicFrameSourceData data = new MicFrameSourceData();
        BeanUtils.copyProperties(dto, data);
        MicFrameSourceData lastData = micFrameSourceDao.getLastMicFrameData();
        int nextId = lastData != null ? lastData.getMic_id() + 1 : 1;
        data.setMic_id(nextId);
        data.setC_time(DateHelper.getNowSeconds());
        micFrameSourceDao.insert(data);

        micFrameRedis.setMicSource(data.getMic_id(), data.getMic_source());
        if(data.getItem_type() == 5 && data.getIs_new() == 1 && data.getStatus() == 1){
            goodsListHomeRedis.addNewGoodsRankingScore(TYPE_MIC, nextId);
        }


    }

    public void updateMicFrameData(MicFrameDTO dto) {

        MicFrameSourceData data = micFrameSourceDao.getMicFrameDataByID(dto.getDocId());
        if(data == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if(data.getItem_type() == 5 && data.getItem_type() != dto.getItem_type()){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "已上传的资源不能修改类型");
        }


        Update update = new Update();
        update.set("name", dto.getName() != null ? dto.getName():"");
        update.set("name_ar", dto.getName_ar() != null ? dto.getName_ar(): "");
        update.set("status", dto.getStatus());
        update.set("seven_times_cost", dto.getSeven_times_cost());
        update.set("item_type", dto.getItem_type());
        update.set("is_new", dto.getIs_new());
        update.set("forder", dto.getForder());
        update.set("buy_type", dto.getBuy_type());
        update.set("beans", dto.getBeans());
        update.set("days", dto.getDays());

        if (!StringUtil.isEmpty(dto.getMic_icon())){
            update.set("mic_icon", dto.getMic_icon());
        }

        if (!StringUtil.isEmpty(dto.getMic_source())){
            update.set("mic_source", dto.getMic_source());
        }
        micFrameSourceDao.updateData(dto.getDocId(), update);

        if (!StringUtil.isEmpty(dto.getMic_source())){
            micFrameRedis.setMicSource(data.getMic_id(), dto.getMic_source());
        }

        if(data.getItem_type() == 5){
            if(dto.getIs_new() == 1 && dto.getStatus() == 1){
                goodsListHomeRedis.addNewGoodsRankingScore(TYPE_MIC, data.getMic_id());
            }

            if(dto.getIs_new() == 0 || dto.getStatus() == 0){
                goodsListHomeRedis.deleteItemNewGoodsRanking(TYPE_MIC, data.getMic_id());
            }

            if(dto.getStatus() == 0){
                goodsListHomeRedis.deleteItemNewGoodsRanking(TYPE_MIC, data.getMic_id());
                goodsListHomeRedis.deleteItemHotGoodsRanking(TYPE_MIC, data.getMic_id());
            }
        }
    }

    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<MicFrameSourceData> micFrameList = micFrameSourceDao.selectMicFramePage(-1, condition.getStatus(), search, start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for(MicFrameSourceData data: micFrameList){
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getMic_id());
            vo.setResourceIcon(data.getMic_icon());
            vo.setResourceNameEn(data.getName());
            vo.setResourceNameAr(data.getName_ar());
            vo.setResourcePrice(data.getBeans());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(micFrameSourceDao.selectCount(-1, condition.getStatus(), search));
        return pageVO;
    }
}
