package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.WhiteTestData;
import com.quhong.operation.share.condition.WhiteTestCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.WhiteTestVO;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class WhiteTestService {
    private static final Logger logger = LoggerFactory.getLogger(WhiteTestService.class);

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;

    private String getSearchUid(String search, int type) {

        String searchUid = "";
        if (StringUtils.isEmpty(search)) {
            return searchUid;
        }

        try {
            ActorData actorData = actorDao.getActorByStrRid(search);
            if (actorData != null) {
                searchUid = actorData.getUid();
            }

            if (type == WhiteTestDao.WHITE_TYPE_ROOM_ID) {
                searchUid = RoomUtils.formatRoomId(searchUid);
            }
            return searchUid;

        } catch (Exception e) {
            if (search.length() == 24) {
                return search;
            }
        }
        return searchUid;
    }


    public PageResultVO<WhiteTestVO> getDataList(WhiteTestCondition condition) {
        PageResultVO<WhiteTestVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int type = condition.getType();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        String searchUid = getSearchUid(search, type);
        IPage<WhiteTestData> pageData = whiteTestDao.selectPage(type, searchUid, page, pageSize);

        List<WhiteTestVO> voList = new ArrayList<>();
        for (WhiteTestData data : pageData.getRecords()) {
            WhiteTestVO vo = new WhiteTestVO();
            BeanUtils.copyProperties(data, vo);
            String whiteId = WhiteTestDao.WHITE_TYPE_USER.contains(data.getType()) ? data.getWhiteId() : RoomUtils.getRoomHostId(data.getWhiteId());
            ActorData actorData = actorDao.getActorDataFromCache(whiteId);
            vo.setRid(actorData.getStrRid());
            vo.setOriginalId(actorData.getOriginalRid());
            vo.setBeans(actorData.getBeans());
            vo.setRegisterTime(new ObjectId(whiteId).getTimestamp());
            if (data.getType() == WhiteTestDao.WHITE_TYPE_ROOM_ID) {
                MongoRoomData mongoRoomData = mongoRoomDao.findData(data.getWhiteId());
                vo.setRoomName(mongoRoomData != null ? mongoRoomData.getName() : "null");
            }
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(pageData.getTotal());
        return pageVO;
    }

    private List<String> getRidList(String ridData) {

        List<String> ridList = new ArrayList<>();
        try {
            ridData = ridData.trim().replace(" ", "").replace("'", "").replace("\n", "");
            String[] stringList = ridData.split(",");
            ridList.addAll(Arrays.asList(stringList));
            return ridList;
        } catch (Exception e) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
    }

    public void addData(WhiteTestVO dto) {
        String ridData = dto.getRid();   // 多个id
        String belong = dto.getBelong().trim();
        int type = dto.getType();
        int currentTime = DateHelper.getNowSeconds();
        if (StringUtils.isEmpty(ridData) || StringUtils.isEmpty(belong)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        List<String> ridList = getRidList(ridData);
        for (String rid : ridList) {
            ActorData actorData = actorDao.getActorByStrRid(rid);
            if (actorData == null) {
                logger.error("actorData not find:{}", rid);
                continue;
            }

            if (type == WhiteTestDao.WHITE_TYPE_RID || type == WhiteTestDao.WHITE_TYPE_ROOM_ID) {
                String addWhiteId = type == WhiteTestDao.WHITE_TYPE_ROOM_ID ? RoomUtils.formatRoomId(actorData.getUid()) : actorData.getUid();
                WhiteTestData whiteTestData = whiteTestDao.selectByWhiteIdType(addWhiteId, type);

                if (whiteTestData == null) {
                    whiteTestData = new WhiteTestData();
                    whiteTestData.setWhiteId(addWhiteId);
                    whiteTestData.setBelong(belong);
                    whiteTestData.setType(type);
                    whiteTestData.setCtime(currentTime);
                    whiteTestDao.insertOne(whiteTestData);
                }
            } else if (type == WhiteTestDao.WHITE_TYPE_TN_ID) {
                String tnId = actorData.getTn_id();
                if (StringUtils.isEmpty(tnId)) {
                    logger.error("user:{}, tn_id is empty", rid);
                    continue;
                }
                List<MongoActorData> actorDataList = actorDao.findListByTnId(tnId);
                for (MongoActorData user : actorDataList) {
                    String userUid = user.get_id().toString();
                    WhiteTestData data = whiteTestDao.selectByWhiteIdType(userUid, type);
                    if (data == null) {
                        WhiteTestData whiteTestData = new WhiteTestData();
                        whiteTestData.setWhiteId(userUid);
                        whiteTestData.setTnId(tnId);
                        whiteTestData.setBelong(belong);
                        whiteTestData.setType(type);
                        whiteTestData.setCtime(currentTime);
                        whiteTestDao.insertOne(whiteTestData);
                    }
                }
            }
        }
    }


    public void updateData(WhiteTestVO dto) {
        Integer id = dto.getId();
        WhiteTestData whiteTestData = whiteTestDao.selectById(id);
        String belong = dto.getBelong().trim();
        if (whiteTestData == null || StringUtils.isEmpty(belong)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        whiteTestData.setBelong(belong);
        whiteTestDao.updateById(whiteTestData);
    }

    public void deleteData(WhiteTestVO dto) {

        int type = dto.getType();
        String whiteId = dto.getWhiteId();
        String tnId = dto.getTnId();
        if (type == WhiteTestDao.WHITE_TYPE_TN_ID && !StringUtils.isEmpty(tnId)) {
            whiteTestDao.deleteByTnId(tnId, type);
        } else {
            whiteTestDao.deleteByWhiteId(whiteId, type);
        }
    }

}
