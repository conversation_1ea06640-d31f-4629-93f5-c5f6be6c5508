package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.quhong.data.StarBeatPoolRewardData;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.CarnivalGameConfig;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.StarBeatPoolRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CarnivalGameService {
    private static final Logger logger = LoggerFactory.getLogger(CarnivalGameService.class);

    @Resource
    private CarnivalGameDao carnivalGameDao;
    @Resource
    private StarBeatPoolRedis starBeatPoolRedis;
    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private MicFrameSourceDao micFrameSourceDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private GiftDao giftDao;

    public PageResultVO<?> carnivalGameConfig(BaseCondition condition) {
        PageResultVO<CarnivalGameConfig> pageVO = new PageResultVO<>();
        List<CarnivalGameData> allCarnivalGameDataList = carnivalGameDao.getAllCarnivalGameData();
        List<CarnivalGameConfig> voList = new ArrayList<>();
        for (CarnivalGameData data : allCarnivalGameDataList) {
            CarnivalGameConfig config = new CarnivalGameConfig();
            config.setDocId(data.get_id().toString());
            BeanUtils.copyProperties(data, config);
            // 整合数据，形成树形数据结构
            data.getPrizeConfigList().sort(Comparator.comparing(CarnivalGameData.PrizeConfig::getOrderNum));
            List<CarnivalGameData.PrizeConfig> prizeConfigList = data.getPrizeConfigList();
            List<CarnivalGameConfig.PrizeVOConfig> prizeVOConfigList = new ArrayList<>();
            for (CarnivalGameData.PrizeConfig prizeConfig : prizeConfigList) {
                if (prizeConfig.getParentId() > 0) {
                    continue;
                }
                CarnivalGameConfig.PrizeVOConfig prizeVOConfig = new CarnivalGameConfig.PrizeVOConfig();
                BeanUtils.copyProperties(prizeConfig, prizeVOConfig);
                prizeVOConfig.getChildrenList().addAll(prizeConfigList.stream().filter(item -> item.getParentId() == prizeConfig.getPrizeId()).collect(Collectors.toList()));
                prizeVOConfigList.add(prizeVOConfig);
            }
            config.setPrizeConfigVOList(prizeVOConfigList);
            // 设置奖池
            StarBeatPoolRewardData poolRewardData = starBeatPoolRedis.getPoolReward(config.getCategory());
            config.setRewardNum(poolRewardData == null ? 0 : poolRewardData.getRewardNum());
            config.setRewardLuckyNum(poolRewardData == null ? 0 : poolRewardData.getRewardLuckyNum());
            voList.add(config);
        }
        pageVO.setList(voList);
        return pageVO;
    }

    /**
     * 修改-基本配置（抽奖费、概率、分配比例）
     */
    public void updateBaseCarnivalGameConfig(CarnivalGameConfig dto) {
        CarnivalGameData data = carnivalGameDao.getCarnivalGameDataById(dto.getDocId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未找到配置");
        }
        if (dto.getGameAllocation() < 0 || dto.getLuckyAllocation() < 0 || dto.getSystemAllocation() < 0) {
            logger.error("updateBaseCarnivalGameConfig param-error-1 :{}", JSON.toJSONString(dto));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖池分配不能小于零");
        }
        int total = dto.getGameAllocation() + dto.getLuckyAllocation() + dto.getSystemAllocation();
        if (total > 100 || total <= 0) {
            logger.error("updateBaseCarnivalGameConfig param-error-2 :{}", JSON.toJSONString(dto));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖池总和小于等于0 或 大于100");
        }
        if (dto.getPoolMax() <= 0 || dto.getPoolMin() >= 0) {
            logger.error("updateBaseCarnivalGameConfig param-error-3 :{}", JSON.toJSONString(dto));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖池上限 / 奖池下限 配置不正确 (奖池上限必须为正数, 奖池下限必须为负数)");
        }
        if (dto.getPoolMaxRate() <= 0 || dto.getPoolMinRate() <= 0) {
            logger.error("updateBaseCarnivalGameConfig param-error-4 :{}", JSON.toJSONString(dto));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖池上限/下限触发概率配置不正确, 概率范围1-99");
        }

        ResourceConfigData resourceConfigData = resourceConfigDao.getResourceDataFromDb(dto.getTicketId());
        if (resourceConfigData == null) {
            logger.error("updateBaseCarnivalGameConfig param-error-5 :{}", JSON.toJSONString(dto));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖券id配置不正确, 没有该抽奖券信息");
        }

        if (CollectionUtils.isEmpty(dto.getPrizeFreeList())) {
            logger.error("updateBaseCarnivalGameConfig param-error-6 :{}", JSON.toJSONString(dto));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖费用配置不能为空");
        }

        for (CarnivalGameData.PrizeFree prizeFree : dto.getPrizeFreeList()) {
            if (prizeFree.getPrizePrice() <= 0 || prizeFree.getPrizeTimes() <= 0) {
                logger.error("updateBaseCarnivalGameConfig param-error-7 :{}", JSON.toJSONString(dto));
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖费用配置不正确, 费用或次数不能小于等于0");
            }
        }

        if (dto.getExpand() <= 0) {
            logger.error("updateBaseCarnivalGameConfig param-error-8 :{}", JSON.toJSONString(dto));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "膨胀率配置有误");
        }

        dto.getPrizeFreeList().sort(Comparator.comparing(CarnivalGameData.PrizeFree::getPrizeTimes));
        Update update = new Update();
        update.set("ticketId", dto.getTicketId());
        update.set("poolMax", dto.getPoolMax());
        update.set("poolMaxRate", dto.getPoolMaxRate());
        update.set("poolMin", dto.getPoolMin());
        update.set("poolMinRate", dto.getPoolMinRate());
        update.set("poolLuckyNum", dto.getPoolLuckyNum());
        update.set("expand", dto.getExpand());
        update.set("gameAllocation", dto.getGameAllocation());
        update.set("luckyAllocation", dto.getLuckyAllocation());
        update.set("systemAllocation", dto.getSystemAllocation());
        update.set("prizeFreeList", dto.getPrizeFreeList());
        carnivalGameDao.updateData(dto.getDocId(), update);
    }

    /**
     * 增加奖励配置
     */

    public void checkPrizeConfig(CarnivalGameData.PrizeConfig prizeConfig, Map<Integer, CarnivalGameData.PrizeConfig> prizeConfigMap) {
        if (prizeConfig == null) {
            logger.error("checkPrizeConfig param-error-1");
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "配置为空");
        }

        if (prizeConfig.getPrizeValue() <= 0) {
            logger.error("checkPrizeConfig param-error-2 :{}", JSON.toJSONString(prizeConfig));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖品价值配置错误");
        }

        if (StringUtils.isEmpty(prizeConfig.getResourceNameEn()) || StringUtils.isEmpty(prizeConfig.getResourceNameAr())) {
            logger.error("checkPrizeConfig param-error-3 :{}", JSON.toJSONString(prizeConfig));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖品名称未配置");
        }

        if (prizeConfig.getLuckyPoolNum() < 0) {
            logger.error("checkPrizeConfig param-error-4 :{}", JSON.toJSONString(prizeConfig));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "幸运奖池数量配置有误");
        }

        if (prizeConfig.getParentId() == 0 && (prizeConfig.getRateA() <= 0 || prizeConfig.getRateB() <= 0 || prizeConfig.getRateC() <= 0)) {
            logger.error("checkPrizeConfig param-error-5 :{}", JSON.toJSONString(prizeConfig));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "配置概率有误");
        }

        if (prizeConfig.getParentId() > 0 && prizeConfigMap.get(prizeConfig.getParentId()) == null) {
            logger.error("checkPrizeConfig param-error-6 :{}", JSON.toJSONString(prizeConfig));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未找到父级奖励id");
        }

        if (prizeConfig.getParentId() > 0 && prizeConfig.getResourceType() == -1) {
            logger.error("checkPrizeConfig param-error-7 :{}", JSON.toJSONString(prizeConfig));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "子奖励不能是礼盒");
        }

        if (StringUtils.isEmpty(prizeConfig.getResourceIcon())) {
            logger.error("checkPrizeConfig param-error-8 :{}", JSON.toJSONString(prizeConfig));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖品图标未设置");
        }

        if (prizeConfig.getPrizeRealValue() < 0) {
            logger.error("checkPrizeConfig param-error-8 :{}", JSON.toJSONString(prizeConfig));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖品奖池价值配置不正确");
        }

        if (prizeConfig.getResourceType() == BaseDataResourcesConstant.TYPE_MIC) {
            MicFrameSourceData micFrameSourceData = micFrameSourceDao.findData(prizeConfig.getResourceId());
            if (micFrameSourceData != null && micFrameSourceData.getItem_type() == BaseDataResourcesConstant.STORE_BUY_ITEM_TYPE) {
                logger.error("checkPrizeConfig param-error-9 :{}", JSON.toJSONString(prizeConfig));
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "不能选择商店按佩戴天过期的麦位框");
            }
        }
        if (prizeConfig.getResourceType() == BaseDataResourcesConstant.TYPE_RIDE) {
            JoinSourceData joinSourceData = joinSourceDao.findSourceData(prizeConfig.getResourceId());
            if (joinSourceData != null && joinSourceData.getItem_type() == BaseDataResourcesConstant.STORE_BUY_ITEM_TYPE) {
                logger.error("checkPrizeConfig param-error-10 :{}", JSON.toJSONString(prizeConfig));
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "不能选择商店按佩戴天过期的坐骑");
            }
        }

        if (prizeConfig.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT) {
            GiftData extraGift = giftDao.getGiftFromDb(prizeConfig.getResourceId());
            if (extraGift == null || extraGift.getBagGift() <= 0) {
                logger.error("checkPrizeConfig param-error-11 :{}", JSON.toJSONString(prizeConfig));
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "所选择的礼物不是背包礼物，请先将该礼物添加到背包礼物列表【资源管理->礼物】");
            }
        }

    }

    public void updateLuckyNumPool(CarnivalGameData data) {
        StarBeatPoolRewardData poolRewardData = starBeatPoolRedis.getPoolReward(data.getCategory());
        if (poolRewardData != null) {
            poolRewardData.setLuckyNumMap(Collections.emptyMap());
            starBeatPoolRedis.savePoolReward(poolRewardData, data.getCategory());
        }
    }

    public void addPrizeConfig(CarnivalGameConfig dto) {
        CarnivalGameData data = carnivalGameDao.getCarnivalGameDataById(dto.getDocId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未找到配置");
        }

        Map<Integer, CarnivalGameData.PrizeConfig> prizeConfigMap = data.getPrizeConfigList().stream().collect(Collectors.toMap(CarnivalGameData.PrizeConfig::getPrizeId, Function.identity()));
        CarnivalGameData.PrizeConfig prizeConfig = dto.getPrizeConfig();
        checkPrizeConfig(prizeConfig, prizeConfigMap);
        int autoPrizeId = starBeatPoolRedis.incPrizeIndexNum();
        if (autoPrizeId == 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "自动生成配置id出错");
        }
        prizeConfig.setPrizeId(autoPrizeId);
        data.getPrizeConfigList().add(prizeConfig);
        Update update = new Update();
        update.set("prizeConfigList", data.getPrizeConfigList());
        carnivalGameDao.updateData(dto.getDocId(), update);
        updateLuckyNumPool(data);
    }

    /**
     * 更新奖励配置
     */
    public void updatePrizeConfig(CarnivalGameConfig dto) {
        CarnivalGameData data = carnivalGameDao.getCarnivalGameDataById(dto.getDocId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未找到配置");
        }

        Map<Integer, CarnivalGameData.PrizeConfig> prizeConfigMap = data.getPrizeConfigList().stream().collect(Collectors.toMap(CarnivalGameData.PrizeConfig::getPrizeId, Function.identity()));
        CarnivalGameData.PrizeConfig prizeConfig = dto.getPrizeConfig();

        if (prizeConfigMap.get(prizeConfig.getPrizeId()) == null) {
            logger.error("addPrizeConfig param-error-3 :{}", JSON.toJSONString(dto));
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "没有该奖励配置");
        }
        checkPrizeConfig(prizeConfig, prizeConfigMap);
        for (CarnivalGameData.PrizeConfig config : data.getPrizeConfigList()) {
            if (config.getPrizeId() == prizeConfig.getPrizeId()) {
                if (prizeConfig.getResourceType() != config.getResourceType()) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "不能修改奖品类型");
                }
                BeanUtils.copyProperties(prizeConfig, config);
            }
        }

        Update update = new Update();
        update.set("prizeConfigList", data.getPrizeConfigList());
        carnivalGameDao.updateData(dto.getDocId(), update);
        updateLuckyNumPool(data);
    }


}
