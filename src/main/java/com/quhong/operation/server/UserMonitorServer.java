package com.quhong.operation.server;

import com.quhong.mongo.config.OpMongoBean;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.AdminUserDao;
import com.quhong.operation.dao.OperationActorDao;
import com.quhong.operation.share.enumerate.BlockEnum;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.UserMonitor;
import com.quhong.operation.share.mongobean.UserMonitorLog;
import com.quhong.operation.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.data.mongodb.core.query.Criteria.where;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Service
public class UserMonitorServer {

    private final static Logger logger = LoggerFactory.getLogger(UserMonitorServer.class);

    @Resource(name = OpMongoBean.MOVIES)
    private MongoTemplate mongoTemp;
    @Autowired
    private AdminUserDao adminUserDao;
    @Autowired
    private OperationActorDao actorDao;

    /**
     * 获取时间段内的封号信息
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param uid       userId
     * @return list
     */
    public ApiResult<List<UserMonitorLog>> getUserMonitorLogList(Integer startTime, Integer endTime, String uid) {
        ApiResult<List<UserMonitorLog>> result = new ApiResult<>();
        //1:警告 2：冻结；3：封禁； 0 释放
        Criteria criteria = new Criteria();
        criteria.orOperator(where("code").is(1), where("code").is(2), where("code").is(3));
        Query query = new Query(criteria);
        if (!StringUtil.isEmptyOrBlank(uid)) {
            query.addCriteria(Criteria.where("uid").is(uid));
        }
        if (null != startTime && null != endTime) {
            query.addCriteria(Criteria.where("opt_time").gte(startTime).lt(endTime));
        }

        List<UserMonitorLog> list = mongoTemp.find(query, UserMonitorLog.class);

        if (null == list) list = new ArrayList<>();
        logger.info("get actor monitor log list size={}", list.size());
        return result.ok(list);
    }

    /**
     * 获取时间段内的监控信息
     *
     * @param uid userId
     * @return list
     */
    public ApiResult<List<UserMonitorLog>> getMonitorLogByUid(String uid) {
        ApiResult<List<UserMonitorLog>> result = new ApiResult<>();

        Query query = new Query(Criteria.where("uid").is(uid));
        List<UserMonitorLog> list = mongoTemp.find(query, UserMonitorLog.class);
        if (null == list) list = new ArrayList<>();
        if (list.size() > 0) {
            String userName = getNameByUid(uid);
            Map<String, String> optUserMap = new HashMap<>();
            // 遍历填充操作人名称
            for (UserMonitorLog log : list) {
                log.setName(userName);
                log.setOperation(BlockEnum.getNameByCode(log.getCode()));

                //填充操作人
                String optUid = log.getOperator();
                if (optUserMap.containsKey(optUid)) {
                    // 已查处，不再去mongo查询，直接填充
                    log.setOptUser(optUserMap.get(optUid));
                } else {
                    // 查询mongo数据库
                    ApiResult<String> apiResult = adminUserDao.getAdminUserNameByUid(optUid);
                    String optName = apiResult.getData();
                    log.setOptUser(optName);
                    optUserMap.put(optUid, optName);
                }
            }
        }

        return result.ok(list);
    }

    /**
     * 通过uid先去查redis，没有再查mongo获取用户名称
     *
     * @param uid uid
     * @return name
     */
    private String getNameByUid(String uid) {
        String userName = "";
        ApiResult<Actor> actorResult = actorDao.getActorByUid(uid);
        if (actorResult.isOK() && null != actorResult.getData()) {
            userName = actorResult.getData().getName();
        }
        logger.info("uid={} find name={}", uid, userName);
        return userName;
    }

    /**
     * 获取用户当前警告，冻结，封禁状态
     *
     * @param uid 用户userId
     * @return 状态
     */
    public ApiResult<String> getActorNowStatus(String uid) {
        ApiResult<String> result = new ApiResult<>();

        Query query = new Query(Criteria.where("uid").is(uid));
        query.limit(1);
        query.with(Sort.by(
                Sort.Order.desc("opt_time")));
        List<UserMonitor> list = mongoTemp.find(query, UserMonitor.class);

        String status = "异常";
        if (CollectionUtils.isEmpty(list)) {
            status = "正常";
        } else {
            Integer code = list.get(0).getCode();
            if (1 == code) {
                status = "警告";
            } else if (2 == code) {
                status = "冻结";
            } else if (3 == code) {
                status = "封禁";
            }
        }

        logger.info("uid={} actor status={}", uid, status);
        return result.ok(status);
    }

}
