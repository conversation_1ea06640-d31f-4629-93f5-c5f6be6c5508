package com.quhong.operation.server;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.datas.DayTimeData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.feign.DataCenterService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.config.DBMysqlBean;
import com.quhong.operation.dao.ActorStatDao;
import com.quhong.operation.dao.ChannelBeansAmountDao;
import com.quhong.operation.dao.ChannelBeansLogDao;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.httpResult.OperationHttpCode;
import com.quhong.operation.share.dto.ChannelDrillDto;
import com.quhong.operation.share.dto.ChannelDrillUserDto;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.mysql.ChannelBeansAmountData;
import com.quhong.operation.share.mysql.ChannelBeansLogData;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.share.vo.reports.ChannelBeansAmountLogVO;
import com.quhong.operation.share.vo.reports.ChannelBeansDayStatVO;
import com.quhong.operation.share.vo.reports.ChannelSendBeansLogVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Component
public class ChannelBeansServer {

    private static final Logger logger = LoggerFactory.getLogger(ChannelBeansServer.class);

    @Autowired
    private ChannelBeansAmountDao channelBeansAmountDao;

    @Autowired
    private ChannelBeansLogDao channelBeansLogDao;

    @Autowired
    private ActorDao actorDao;

    @Autowired
    private DataCenterService dataCenterService;

    @Autowired
    private ActorStatDao actorStatDao;

    @Autowired
    private ManagerDao managerDao;

    /**
     * 打钻
     *
     * @param channelDrillDto
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class, RuntimeException.class}, transactionManager = DBMysqlBean.USTAR_TRANSACTION)
    public ApiResult<ChannelDrillVO> sendBeans(ChannelDrillDto channelDrillDto) {
        ApiResult<ChannelDrillVO> result = new ApiResult<>();
        ChannelDrillVO channelDrillVO = new ChannelDrillVO();
        List<ChannelDrillDetailVO> channelDrillDetails = new ArrayList<>();
        String channelId = channelDrillDto.getChannelId();
        //查找channl数据
        ChannelBeansAmountData channelBeansData = channelBeansAmountDao.getDataByChannelId(channelId);
        if (channelBeansData == null) {
            logger.error("channel send beans error. channelId not exist. channelId = {}", channelId);
            return ApiResult.getError(OperationHttpCode.CHANNEL_ID_NOT_EXIST);
        }
        int limitBeans = channelBeansData.getBeans() == null ? 0 : channelBeansData.getBeans();
        int cid = channelBeansData.getId();
        //开始打钻
        channelDrillVO.setBeforeBeans(limitBeans);
        List<ChannelDrillUserDto> list = channelDrillDto.getList();
        for (ChannelDrillUserDto channelDrillUserDto : list) {
            ChannelDrillDetailVO channelDrillDetailVO = new ChannelDrillDetailVO();
            int status = 0;
            String reason = "";
            int addBeans = 0;
            Integer rid = channelDrillUserDto.getRid();
            Integer beans = channelDrillUserDto.getBeans();
            ActorData actor = actorDao.getActorByRid(rid);
            //用户不存在
            if (actor == null) {
                status = 1;
                reason = "user not exist";
                channelDrillDetailVO.setRid(rid);
                channelDrillDetailVO.setBeans(addBeans);
                channelDrillDetailVO.setReason(reason);
                channelDrillDetailVO.setStatus(status);
                channelDrillDetails.add(channelDrillDetailVO);
                logger.error("channel send beans error. rid = {} beans = {} reason = {} channelId = {}", rid, addBeans, reason, channelId);
                continue;
            }
            //用户存在，但是渠道额度不足
            String name = actor.getName();
            if (beans > limitBeans) {
                status = 1;
                reason = "Insufficient channel balance";
                channelDrillDetailVO.setRid(rid);
                channelDrillDetailVO.setBeans(addBeans);
                channelDrillDetailVO.setReason(reason);
                channelDrillDetailVO.setStatus(status);
                channelDrillDetailVO.setName(name);
                channelDrillDetails.add(channelDrillDetailVO);
                logger.error("channel send beans error. rid = {} beans = {} reason = {} channelId = {}", rid, addBeans, reason, channelId);
                continue;
            }
            //更新渠道可打钻额度
            int currentTime = (int) (new Date().getTime() / 1000);
            int beforeBeans = limitBeans;
            limitBeans = limitBeans - beans;
            int afterBeans = limitBeans;
            ChannelBeansAmountData channelDrillLimitData = new ChannelBeansAmountData();
            channelDrillLimitData.setId(cid);
            channelDrillLimitData.setBeans(limitBeans);
            channelDrillLimitData.setMtime(currentTime);
            channelBeansAmountDao.updateById(channelDrillLimitData);
            logger.info("channel send bean reduce success,channelId = {} residue beans = {}", channelId, limitBeans);
            //调用data-center服务的打钻接口开始打钻
            MoneyDetailReq moneyDetail = new MoneyDetailReq();
            moneyDetail.setId(new ObjectId().toString());
            moneyDetail.setUid(actor.getUid());
            moneyDetail.setAtype(1);
            moneyDetail.setTitle("agent charge");
            moneyDetail.setDesc("agent charge");
            moneyDetail.setChanged(beans);
            ApiResult<String> httpResult = dataCenterService.chargeBeans(moneyDetail);
            //三方接口调用失败，事务回滚
            logger.info("test data_center add_bean code={}", httpResult.getCode().getCode());
            if (httpResult.isError()) {
                logger.error("data-center server charge_beans invoking error.code={} msg={} rid = {} uid = {} beans = {} channelId = {}",
                        httpResult.getCode(), httpResult.getCode().getMsg(), rid, actor.getUid(), beans, channelId);
                throw new RuntimeException("data-center server charge_beans invoking error,result code not 200");
            }
            //打钻流水插入log表
            ChannelBeansLogData channelDrillLogData = new ChannelBeansLogData();
            channelDrillLogData.setChannelId(channelId);
            channelDrillLogData.setAid(actor.getUid());
            channelDrillLogData.setBeforeBeans(beforeBeans);
            channelDrillLogData.setCostBeans(beans);
            channelDrillLogData.setAfterBeans(afterBeans);
            channelDrillLogData.setStatus(ChannelBeansLogData.STATUS_DRILL);
            channelDrillLogData.setCtime(currentTime);
            channelDrillLogData.setMtime(currentTime);
            String remark = "";
            if (!StringUtils.isEmpty(channelDrillUserDto.getRemark())) {
                remark = channelDrillUserDto.getRemark();
            }
            channelDrillLogData.setRemark(remark);
            channelBeansLogDao.saveOne(channelDrillLogData);
            //成功数据拼装
            addBeans = beans;
            channelDrillDetailVO.setRid(rid);
            channelDrillDetailVO.setBeans(addBeans);
            channelDrillDetailVO.setReason(reason);
            channelDrillDetailVO.setStatus(status);
            channelDrillDetailVO.setName(name);
            channelDrillDetails.add(channelDrillDetailVO);
        }
        channelDrillVO.setList(channelDrillDetails);
        channelDrillVO.setChannelId(channelId);
        channelDrillVO.setAfterBeans(limitBeans);
        return result.ok(channelDrillVO);
    }

    /**
     * 通过rid获取用户数据
     *
     * @param agentName
     * @return
     */
    public ApiResult<ChannelBeanVO> getAgentData(String agentName) {
        ApiResult<ChannelBeanVO> target = new ApiResult<>();
        ChannelBeansAmountData channelBeanData = channelBeansAmountDao.getDataByChannelId(agentName);
        if (channelBeanData == null) {
            logger.error("channel bean count not exist. agentName={}", agentName);
            return target.error(4001, "channel bean count not exist");
        }
        ChannelBeanVO channelBeanVO = new ChannelBeanVO();
        channelBeanVO.setAgentName(channelBeanData.getChannelId());
        channelBeanVO.setAmount(channelBeanData.getBeans());
        return target.ok(channelBeanVO);
    }

    /**
     * 获取打钻记录
     *
     * @param agentName
     * @param page
     * @return
     */
    public ApiResult<PageResultVO<List<ChannelBeanRecordVO>>> getChannelBeanRecord(String agentName, int page) {
        ApiResult<PageResultVO<List<ChannelBeanRecordVO>>> target = new ApiResult<>();
        List<ChannelBeanRecordVO> list = new ArrayList<>();
        PageHelper.startPage(page, 10);
        List<ChannelBeansLogData> logList = channelBeansLogDao.listByChannelId(agentName, -1);
        PageInfo<ChannelBeansLogData> pageInfo = new PageInfo<>(logList);
        long total = pageInfo.getTotal();
        for (ChannelBeansLogData channelBeansLogData : logList) {
            ChannelBeanRecordVO recordVO = new ChannelBeanRecordVO();
            recordVO.setDate(DateHelper.BEIJING.formatDateTime(new Date(channelBeansLogData.getCtime() * 1000L)));
            recordVO.setRemark(channelBeansLogData.getRemark());
            recordVO.setDiamonds(channelBeansLogData.getCostBeans());
            recordVO.setBalance(channelBeansLogData.getAfterBeans());
            String aid = channelBeansLogData.getAid();
            ActorData actorData = actorDao.getActorData(aid);
            if (actorData != null) {
                int rid = actorData.getRid();
                recordVO.setRid(rid);
            }
            list.add(recordVO);
        }
        PageResultVO pageResultVO = new PageResultVO();
        pageResultVO.setTotal(total);
        pageResultVO.setList(list);
        return target.ok(pageResultVO);
    }

    /**
     * channel id充值额度
     *
     * @param channelId 渠道id
     * @param beans     重置钻石额度
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}, transactionManager = DBMysqlBean.USTAR_TRANSACTION)
    public ApiResult<ChannelDrillVO> addAmount(String channelId, int beans, String uid) {
        ApiResult<ChannelDrillVO> result = new ApiResult<>();
        ChannelBeansAmountData channelBeansData = channelBeansAmountDao.getDataByChannelId(channelId);
        int currentTime = (int) (new Date().getTime() / 1000);
        int beforeBeans = 0;
        int afterBeans = 0;
        if (channelBeansData == null) {
            afterBeans = beans;
            //channel_drill_limit中没有channelId的数据，就新增一条
            ChannelBeansAmountData insertData = new ChannelBeansAmountData();
            insertData.setChannelId(channelId);
            insertData.setBeans(afterBeans);
            insertData.setCtime(currentTime);
            insertData.setMtime(currentTime);
            channelBeansAmountDao.saveOne(insertData);
        } else {
            beforeBeans = channelBeansData.getBeans();
            afterBeans = beforeBeans + beans;
            //更新channel_drill_limit的数据
            ChannelBeansAmountData updateData = new ChannelBeansAmountData();
            updateData.setId(channelBeansData.getId());
            updateData.setMtime(currentTime);
            updateData.setBeans(afterBeans);
            channelBeansAmountDao.updateById(updateData);
        }
        //将更新数据记录到log表中
        ChannelBeansLogData channelBeansLogData = new ChannelBeansLogData();
        channelBeansLogData.setChannelId(channelId);
        channelBeansLogData.setAid(uid);
        channelBeansLogData.setBeforeBeans(beforeBeans);
        channelBeansLogData.setCostBeans(beans);
        channelBeansLogData.setAfterBeans(afterBeans);
        channelBeansLogData.setStatus(ChannelBeansLogData.STATUS_RESET_BEAN);
        channelBeansLogData.setRemark("");
        channelBeansLogData.setCtime(currentTime);
        channelBeansLogData.setMtime(currentTime);
        channelBeansLogDao.saveOne(channelBeansLogData);
        //数据拼装
        ChannelDrillVO channelDrillVO = new ChannelDrillVO();
        channelDrillVO.setBeforeBeans(beforeBeans);
        channelDrillVO.setChannelId(channelId);
        channelDrillVO.setAfterBeans(afterBeans);
        logger.info("config amount limit success channelId = {} beforeBeans = {} afterBeans = {}", channelId, beforeBeans, afterBeans);
        return result.ok(channelDrillVO);
    }

    /**
     * 清空渠道钻石额度
     *
     * @param channelId
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}, transactionManager = DBMysqlBean.USTAR_TRANSACTION)
    public ApiResult<ChannelDrillVO> clearAmount(String channelId, String uid) {
        ApiResult<ChannelDrillVO> result = new ApiResult<>();
        ChannelBeansAmountData channelBeansData = channelBeansAmountDao.getDataByChannelId(channelId);
        int currentTime = (int) (new Date().getTime() / 1000);
        if (channelBeansData == null) {
            return result.error(OperationHttpCode.CHANNEL_ID_NOT_EXIST);
        }
        //更新额度
        int beforeBeans = channelBeansData.getBeans();
        ChannelBeansAmountData updateData = new ChannelBeansAmountData();
        updateData.setId(channelBeansData.getId());
        updateData.setMtime(currentTime);
        updateData.setBeans(0);
        channelBeansAmountDao.updateById(updateData);
        //新增额度重置记录数据
        ChannelBeansLogData channelBeansLogData = new ChannelBeansLogData();
        channelBeansLogData.setChannelId(channelId);
        channelBeansLogData.setAid(uid);
        channelBeansLogData.setBeforeBeans(beforeBeans);
        channelBeansLogData.setCostBeans(0 - beforeBeans);
        channelBeansLogData.setAfterBeans(0);
        channelBeansLogData.setStatus(ChannelBeansLogData.STATUS_RESET_BEAN);
        channelBeansLogData.setRemark("");
        channelBeansLogData.setCtime(currentTime);
        channelBeansLogData.setMtime(currentTime);
        channelBeansLogDao.saveOne(channelBeansLogData);
        //数据拼装
        ChannelDrillVO channelDrillVO = new ChannelDrillVO();
        channelDrillVO.setBeforeBeans(beforeBeans);
        channelDrillVO.setChannelId(channelId);
        channelDrillVO.setAfterBeans(0);
        logger.info("clear amount limit success channelId = {} beforeBeans = {} afterBeans = {}", channelId, beforeBeans, 0);
        return result.ok(channelDrillVO);
    }

    /**
     * 获取所有的渠道号及其当前余额
     *
     * @return
     */
    public ApiResult<List<ChannelBeansInfoVO>> listAllChannelBeans() {
        ApiResult<List<ChannelBeansInfoVO>> result = new ApiResult<>();
        List<ChannelBeansAmountData> channelAmountList = channelBeansAmountDao.getAll();
        if (CollectionUtils.isEmpty(channelAmountList)) {
            return result.error(OperationHttpCode.CHANNEL_SIZE_EMPTY);
        }
        List<ChannelBeansInfoVO> list = new ArrayList<>();
        for (ChannelBeansAmountData channelBeansAmountData : channelAmountList) {
            ChannelBeansInfoVO channelBeansInfoVO = new ChannelBeansInfoVO();
            channelBeansInfoVO.setChannelId(channelBeansAmountData.getChannelId());
            channelBeansInfoVO.setBeans(channelBeansAmountData.getBeans());
            list.add(channelBeansInfoVO);
        }
        return result.ok(list);
    }

    /**
     * 获取打钻详细数据
     *
     * @param date
     * @param channel
     * @return
     */
    public ApiResult<List<ChannelSendBeansLogVO>> listSendBeansLog(String date, String channel) {
        ApiResult<List<ChannelSendBeansLogVO>> result = new ApiResult<>();
        List<ChannelSendBeansLogVO> sendBeansLogVOList = new ArrayList<>();
        DayTimeData daytimedata = DateHelper.ARABIAN.getContinuesDays(date);
        List<ChannelBeansLogData> channelBeansLogList = channelBeansLogDao.listByTime(daytimedata, channel, ChannelBeansLogData.STATUS_DRILL);
        Set<String> uidSet = new HashSet<>();
        for (ChannelBeansLogData channelBeansLogData : channelBeansLogList) {
            String aid = channelBeansLogData.getAid();
            if (!StringUtils.isEmpty(aid)) {
                uidSet.add(aid);
            }
        }
        List<Actor> actors = actorStatDao.listByUid(uidSet);
        Map<String, Actor> actorMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(actors)) {
            for (Actor actor : actors) {
                actorMap.put(actor.get_id().toString(), actor);
            }
        }
        for (ChannelBeansLogData channelBeansLogData : channelBeansLogList) {
            ChannelSendBeansLogVO channelSendBeansLogVO = new ChannelSendBeansLogVO();
            channelSendBeansLogVO.setDate(date);
            channelSendBeansLogVO.setChannel(channelBeansLogData.getChannelId());
            channelSendBeansLogVO.setSendTime(DateHelper.ARABIAN.formatDateTime(DateHelper.formatDate(channelBeansLogData.getCtime())));
            channelSendBeansLogVO.setBeans(channelBeansLogData.getCostBeans());
            String aid = channelBeansLogData.getAid();
            Actor actor = actorMap.get(aid);
            if (actor != null) {
                channelSendBeansLogVO.setRid(actor.getRid());
                channelSendBeansLogVO.setUserName(actor.getName());
            }
            sendBeansLogVOList.add(channelSendBeansLogVO);
        }
        return result.ok(sendBeansLogVOList);
    }

    /**
     * 获取每日打钻总数统计数据
     *
     * @param start
     * @param end
     * @param channel
     * @return
     */
    public ApiResult<List<ChannelBeansDayStatVO>> listSendBeansStat(String start, String end, String channel) {
        ApiResult<List<ChannelBeansDayStatVO>> result = new ApiResult<>();
        List<ChannelBeansDayStatVO> dayStatVOList = new ArrayList<>();
        List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(start, end);
        if (StringUtils.isEmpty(channel)) {
            ApiResult<List<ChannelBeansInfoVO>> channelResult = listAllChannelBeans();
            List<ChannelBeansInfoVO> channelList = channelResult.getData();
            for (DayTimeData dayTimeData : dayTimeDataList) {
                for (ChannelBeansInfoVO channelBeansInfoVO : channelList) {
                    ChannelBeansDayStatVO channelBeansDayStatVO = new ChannelBeansDayStatVO();
                    String channelId = channelBeansInfoVO.getChannelId();
                    int sumBeans = channelBeansLogDao.getSumBeans(dayTimeData, channelId, ChannelBeansLogData.STATUS_DRILL);
                    channelBeansDayStatVO.setDate(dayTimeData.getDate());
                    channelBeansDayStatVO.setChannel(channelId);
                    channelBeansDayStatVO.setBeans(sumBeans);
                    dayStatVOList.add(channelBeansDayStatVO);
                }
            }
        } else {
            for (DayTimeData dayTimeData : dayTimeDataList) {
                ChannelBeansDayStatVO channelBeansDayStatVO = new ChannelBeansDayStatVO();
                int sumBeans = channelBeansLogDao.getSumBeans(dayTimeData, channel, ChannelBeansLogData.STATUS_DRILL);
                channelBeansDayStatVO.setDate(dayTimeData.getDate());
                channelBeansDayStatVO.setChannel(channel);
                channelBeansDayStatVO.setBeans(sumBeans);
                dayStatVOList.add(channelBeansDayStatVO);
            }
        }
        return result.ok(dayStatVOList);
    }

    /**
     * 获取渠道打钻额度修改记录
     *
     * @param start
     * @param end
     * @param channel
     * @return
     */
    public ApiResult<List<ChannelBeansAmountLogVO>> listChannelAmountChangeLog(String start, String end, String channel) {
        ApiResult<List<ChannelBeansAmountLogVO>> result = new ApiResult<>();
        List<ChannelBeansAmountLogVO> channelBeansAmountVOList = new ArrayList<>();
        DayTimeData daytimedata = new DayTimeData();
        List<DayTimeData> daytimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
        daytimedata.setTime(daytimeList.get(0).getTime());
        daytimedata.setEndTime(daytimeList.get(daytimeList.size() - 1).getEndTime());
        List<ChannelBeansLogData> channelBeansList = channelBeansLogDao.listByTime(daytimedata, channel, ChannelBeansLogData.STATUS_RESET_BEAN);
        for (ChannelBeansLogData channelBeansLogData : channelBeansList) {
            ChannelBeansAmountLogVO channelBeansAmountLogVO = new ChannelBeansAmountLogVO();
            channelBeansAmountLogVO.setChannel(channelBeansLogData.getChannelId());
            channelBeansAmountLogVO.setBeforeAmount(channelBeansLogData.getBeforeBeans());
            channelBeansAmountLogVO.setAfterAmount(channelBeansLogData.getAfterBeans());
            channelBeansAmountLogVO.setCostAmount(channelBeansLogData.getCostBeans());
            channelBeansAmountLogVO.setOpTime(DateHelper.ARABIAN.formatDateTime(DateHelper.formatDate(channelBeansLogData.getCtime())));
            String aid = channelBeansLogData.getAid();
            if (!StringUtils.isEmpty(aid)) {
                Manager manager = managerDao.getDataByUid(aid);
                if (manager != null) {
                    channelBeansAmountLogVO.setOpUser(manager.getAccount());
                }
            }
            channelBeansAmountVOList.add(channelBeansAmountLogVO);
        }
        return result.ok(channelBeansAmountVOList);
    }

}
