package com.quhong.operation.server;


import com.quhong.data.ActorData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.StartPageDao;
import com.quhong.operation.share.dto.StartPageDto;
import com.quhong.operation.share.mongobean.StartPage;

import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.StartPageVO;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class StartPageServer {
    private static final Logger logger = LoggerFactory.getLogger(StartPageServer.class);

    @Autowired
    private StartPageDao startPageDao;

    @Autowired
    private ActorDao actorDao;

    @Autowired
    private MongoRoomDao mongoRoomDao;




    public ApiResult<PageResultVO<StartPageVO>> listStartPage(int valid, int page, int pageSize, String search){
        ApiResult<PageResultVO<StartPageVO>> result = new ApiResult<>();
        PageResultVO<StartPageVO> pageResultVO = new PageResultVO<>();
        long total = startPageDao.countByStatus(valid, search);
        pageResultVO.setTotal(total);

        List<StartPage> startPageDataList = startPageDao.pageDataByStatus(valid, page, pageSize, search);
        List<StartPageVO> startPageVos = StartPageData2VO(startPageDataList);
        pageResultVO.setList(startPageVos);
        return result.ok(pageResultVO);
    }

    private List<StartPageVO> StartPageData2VO(List<StartPage> startPageDataList){
        List<StartPageVO> list = new ArrayList<>();
        for (StartPage startPage : startPageDataList) {
            StartPageVO startPageVO = new StartPageVO();

            startPageVO.setFlashId(startPage.getFlashId());
            startPageVO.setTitle(startPage.getTitle() != null? startPage.getTitle():"");
            startPageVO.setTitleAr(startPage.getTitleAr() != null?startPage.getTitleAr():"");
            startPageVO.setUrl(startPage.getUrl() != null? startPage.getUrl():"");
            startPageVO.setUrlAr(startPage.getUrlAr() != null? startPage.getUrlAr():"");

            startPageVO.setIphonexUrl(startPage.getIphonexUrl() != null? startPage.getIphonexUrl():"");
            startPageVO.setIphonexUrlAr(startPage.getIphonexUrlAr() != null? startPage.getIphonexUrlAr():"");
            startPageVO.setLink(startPage.getLink());
            startPageVO.setSkip(startPage.getSkip());
            startPageVO.setValid(startPage.getValid());
            startPageVO.setAtype(startPage.getAtype());
            startPageVO.setRoomId(startPage.getRoomId());
            startPageVO.setJumpAid(startPage.getJumpAid());
            startPageVO.setIsDelete(startPage.getIsDelete());
            startPageVO.setValidTime(startPage.getValidTime() != null?startPage.getValidTime():-1);

            if (startPage.getAtype() == 99 && startPage.getRoomId() != null){
                // 通过roomId 获取RoomRid
                String aid = startPage.getRoomId().substring(2);
                ActorData fromActorData = actorDao.getActorDataFromCache(aid);
                startPageVO.setRoomRid(Integer.toString(fromActorData.getRid()));

            } else if (startPage.getAtype() == 10 && startPage.getJumpAid() != null){
                // 通过jumpAid 获取rid
                ActorData fromActorData = actorDao.getActorDataFromCache(startPage.getJumpAid());
                startPageVO.setJumpRid(Integer.toString(fromActorData.getRid()));
            } else {
                startPageVO.setRoomRid("");
                startPageVO.setJumpRid("");
            }
            list.add(startPageVO);
        }
        return list;
    }

    public ApiResult addStartPage(StartPageDto addDto){
        ApiResult result = new ApiResult();

        Integer atype = addDto.getAtype();
        String roomRid = addDto.getRoomRid();
        String jumpRid = addDto.getJumpRid();
        String url = addDto.getUrl();
        String urlAr = addDto.getUrlAr();
        if(url == null || urlAr == null){
            return result.error("url or urlAr is empty");
        }

        StartPage lastStartPage = startPageDao.getLastStartPage();
        Integer nextId = lastStartPage != null? lastStartPage.getFlashId():1;

        StartPage startPageData = new StartPage();
        startPageData.setFlashId(nextId);
        startPageData.setTitle(addDto.getTitle());
        startPageData.setTitleAr(addDto.getTitleAr());
        startPageData.setLink(addDto.getLink());
        startPageData.setSkip(addDto.getSkip());
        startPageData.setAtype(atype);
        startPageData.setUrl(addDto.getUrl());
        startPageData.setUrlAr(addDto.getUrlAr());
        startPageData.setIphonexUrl(addDto.getIphonexUrl() != null?addDto.getIphonexUrl():"");
        startPageData.setIphonexUrl(addDto.getIphonexUrl() != null?addDto.getIphonexUrl():"");

        startPageData.setIsDelete(0);
        startPageData.setValid(0);
        startPageData.setRoomId("");
        startPageData.setJumpAid("");

        if(atype == 99){
            ActorData fromActorData = actorDao.getActorByRid(Integer.parseInt(roomRid));
            if (fromActorData == null){
                return result.error("roomRid is error");
            }

            String actionRoomId = "r:" + fromActorData.getUid();
            MongoRoomData roomData = mongoRoomDao.findData(actionRoomId);
            if(roomData == null){
                return result.error("roomRid is error");
            }

            startPageData.setRoomId(roomData.getRid());

        }


        if(atype == 10){
            ActorData fromActorData = actorDao.getActorByRid(Integer.parseInt(jumpRid));
            if (fromActorData == null){
                return result.error("jumpRid is error");
            }
            startPageData.setJumpAid(fromActorData.getUid());

        }

        startPageDao.saveOne(startPageData);

        return result.ok();

    }

    public ApiResult updateStartPage(StartPageDto updateDto) {
        ApiResult result = new ApiResult();

        StartPage startPageData = startPageDao.getDataByFlashID(updateDto.getFlashId());
        if(startPageData != null){
            Integer atype = updateDto.getAtype() != null? updateDto.getAtype():0;

            StartPage updateStartPage = new StartPage();
            updateStartPage.setFlashId(updateDto.getFlashId());
            updateStartPage.setTitle(updateDto.getTitle());
            updateStartPage.setTitleAr(updateDto.getTitleAr());
            updateStartPage.setUrl(updateDto.getUrl());
            updateStartPage.setUrlAr(updateDto.getUrlAr());
            updateStartPage.setIphonexUrl(updateDto.getIphonexUrl());
            updateStartPage.setIphonexUrlAr(updateDto.getIphonexUrlAr());
            updateStartPage.setLink(updateDto.getLink());
            updateStartPage.setSkip(updateDto.getSkip());
            updateStartPage.setAtype(atype);
            updateStartPage.setValid(updateDto.getValid());

            if(atype == 99){
                ActorData fromActorData = actorDao.getActorByRid(Integer.parseInt(updateDto.getRoomRid()));
                if (fromActorData == null){
                    return result.error("roomRid is error");
                }

                String actionRoomId = "r:" + fromActorData.getUid();
                MongoRoomData roomData = mongoRoomDao.findData(actionRoomId);
                if(roomData == null){
                    return result.error("roomRid is error");
                }

                updateStartPage.setRoomId(roomData.getRid());

            }

            if(atype == 10){
                ActorData fromActorData = actorDao.getActorByRid(Integer.parseInt(updateDto.getJumpRid()));
                if (fromActorData == null){
                    return result.error("jumpRid is error");
                }
                startPageData.setJumpAid(fromActorData.getUid());

            }

            startPageDao.updateOne(updateStartPage);
            return result.ok();
        }
        logger.error("updateStartPage error. dto = {}", updateDto);
        return result.error("update failed");
    }

}
