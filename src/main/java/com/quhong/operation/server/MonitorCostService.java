package com.quhong.operation.server;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MonitorWarnName;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.MonitorCostConfigDao;
import com.quhong.mongo.data.MonitorCostConfigData;
import com.quhong.monitor.MonitorSender;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.MonitorWarnCondition;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.MonitorCostVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class MonitorCostService {
    private static final Logger logger = LoggerFactory.getLogger(MonitorCostService.class);

    @Resource
    private MonitorCostConfigDao monitorCostConfigDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ManagerDao managerDao;


    public PageResultVO<MonitorCostVO> getDataList(MonitorWarnCondition condition){
        PageResultVO<MonitorCostVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int status = condition.getStatus();
        int warnType = condition.getWarnType();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<MonitorCostConfigData> dataList = monitorCostConfigDao.selectPage(status, search, warnType, start, pageSize);
        List<MonitorCostVO> list = new ArrayList<>();
        for (MonitorCostConfigData data : dataList) {
            MonitorCostVO vo = new MonitorCostVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            list.add(vo);
        }
        pageVO.setList(list);
        pageVO.setTotal(monitorCostConfigDao.selectCount(status, search));
        return pageVO;
    }

    private void paramCheck(MonitorCostVO dto){
        if(StringUtils.isEmpty(dto.getWarnName()) || StringUtils.isEmpty(dto.getBeanTitle())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "告警标题或钻石标题为空");
        }

        if(CollectionUtils.isEmpty(dto.getTypeList())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "钻石类型不能为空");
        }
        for (Integer beanType : dto.getTypeList()){
            if(beanType == null || beanType <= 0){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "告警钻石类型配置有误");
            }
        }

        if(dto.getMaxLimit() < 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "总支出告警额不能小于0");
        }

        if(dto.getMinLimit() > 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "总收入告警额不能大于0");
        }
    }


    public void addData(String uid, MonitorCostVO dto){
        paramCheck(dto);
        monitorCostConfigDao.save(dto);
        Manager manager = managerDao.getDataByUid(uid);
        String jsonData = JSONObject.toJSONString(dto);
        String warnDesc = String.format("【成本盈亏配置】新增告警配置: %s", dto.getWarnName());
        String warnDetail = String.format("操作者:%s \n 总支出告警额: %s, \n 总收入告警额: %s, \n 提交参数: %s", manager.getAccount(), dto.getMaxLimit(), dto.getMinLimit(), jsonData);
        monitorSender.info(MonitorWarnName.WARN_ACTIVITY_COST, warnDesc, warnDetail);
    }


    public void updateData(String uid, MonitorCostVO dto) {
        String docId = dto.getDocId();
        MonitorCostConfigData data = monitorCostConfigDao.getDataByID(docId);
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String originData = JSONObject.toJSONString(data);

        paramCheck(dto);
        Update update = new Update();
        update.set("warnName", dto.getWarnName());
        update.set("beanTitle", dto.getBeanTitle());
        update.set("warnType", dto.getWarnType());
        update.set("typeList", dto.getTypeList());
        update.set("maxLimit", dto.getMaxLimit());
        update.set("minLimit", dto.getMinLimit());
        update.set("status", dto.getStatus());
        monitorCostConfigDao.updateData(data, update);

        Manager manager = managerDao.getDataByUid(uid);
        String jsonData = JSONObject.toJSONString(dto);
        String warnDesc = String.format("【成本盈亏配置】更新告警配置: %s", dto.getWarnName());
        String warnDetail = String.format("操作者:%s \n 总支出告警额: %s, \n 总收入告警额: %s, \n 原配置数据: %s \n 新配置数据: %s", manager.getAccount(), dto.getMaxLimit(), dto.getMinLimit(), originData, jsonData);
        monitorSender.info(MonitorWarnName.WARN_ACTIVITY_COST, warnDesc, warnDetail);
    }

    public void deleteData(MonitorCostVO dto) {
        monitorCostConfigDao.delete(dto.getDocId());
    }

}
