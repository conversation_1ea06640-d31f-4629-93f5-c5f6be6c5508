package com.quhong.operation.server;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.config.OpMongoBean;
import com.quhong.mongo.data.BanReasonData;
import com.quhong.mysql.slave_mapper.ustar_log.CommonMapper;
import com.quhong.operation.dao.ActorStatDao;
import com.quhong.operation.dao.AdminUserDao;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;


//@Service
public class ExportService {

    private final static Logger logger = LoggerFactory.getLogger(ExportService.class);

    @Resource(name = OpMongoBean.OPERATION)
    private MongoTemplate mongoTemp;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private ActorStatDao actorStatDao;
    @Resource
    private AdminUserDao adminUserDao;

    @Document(collection = "admin_handle_log")
    public static class AdminHandleLog {
        private String uid;
        private String op_uid;
        private int c_time;

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getOp_uid() {
            return op_uid;
        }

        public void setOp_uid(String op_uid) {
            this.op_uid = op_uid;
        }

        public int getC_time() {
            return c_time;
        }

        public void setC_time(int c_time) {
            this.c_time = c_time;
        }
    }

    public <T> T findOne(Query query, Class<T> entityClass, MongoTemplate template) {
        try {
            return template.findOne(query, entityClass);
        } catch (Exception e) {
            logger.error("query find one error. query={} entityClass={}", query.toString(), entityClass.getName(), e);
            return null;
        }
    }

    public <T> List<T> find(Query query, Class<T> entityClass, MongoTemplate template) {
        try {
            return template.find(query, entityClass);
        } catch (Exception e) {
            logger.error("query find error. query={} entityClass={}", query.toString(), entityClass.getName(), e);
            return new ArrayList<>();
        }
    }

    @PostConstruct
    public void postInit() {
        try {
            // 近三个月封号记录
            Criteria criteria = Criteria.where("mtime").gt(1634832000);
            List<BanReasonData> banReasonDataList = find(new Query(criteria), BanReasonData.class, mongoTemplate);
            Map<String, BanReasonData> map = CollectionUtil.listToKeyMap(banReasonDataList, BanReasonData::getUid);
            if (!CollectionUtils.isEmpty(map)) {
                List<JSONObject> list = commonMapper.getRechargeDailyInfo(map.keySet());
                if (null != list) {
                    Set<String> uidSet = new HashSet<>();
                    Map<String, Float> rechargeMap = new HashMap<>();
                    for (JSONObject o : list) {
                        String uid = o.getString("uid");
                        if (!StringUtils.isEmpty(uid)) {
                            uidSet.add(uid);
                            rechargeMap.compute(uid, (k, v) -> null == v ? o.getFloatValue("recharge_money") : v + o.getFloatValue("recharge_money"));
                        }
                    }
                    List<Actor> actorList = actorStatDao.listByUid(uidSet);
                    Map<String, Actor> actorMap = CollectionUtil.listToKeyMap(actorList, Actor::getUid);
                    for (String uid : actorMap.keySet()) {
                        BanReasonData banReasonData = map.get(uid);
                        String opName = adminUserDao.getAdminUserNameByUid(banReasonData.getOp_uid()).getData();
                        logger.info("rid={} rechargeMoney={} opName={} blockDate={}",
                                actorMap.get(uid).getRid(), rechargeMap.get(uid), opName, DateHelper.formatDate(banReasonData.getMtime()));
                    }
                }
            }
        } catch (Exception e) {
            logger.error("postInit", e);
        }
    }
}
