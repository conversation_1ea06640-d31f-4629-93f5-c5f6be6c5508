package com.quhong.operation.server;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.IndexBannerDao;
import com.quhong.mongo.data.IndexBannerData;
import com.quhong.operation.share.condition.BannerCondition;
import com.quhong.operation.share.vo.IndexBannerVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.StringUtil;
import com.quhong.service.HomeBannerService;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class IndexBannerService {
    private static final Logger logger = LoggerFactory.getLogger(IndexBannerService.class);


    @Resource
    private IndexBannerDao bannerDao;
    @Resource
    private ActorDao actorDao;

    public PageResultVO<IndexBannerVO> indexBannerList(BannerCondition condition){
        PageResultVO<IndexBannerVO> pageVO = new PageResultVO<>();
        Integer valid = condition.getValid();
        Integer area = condition.getArea();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPage_size() == null ? 30 : condition.getPage_size();
        int start = (page - 1) * pageSize;

        List<IndexBannerData> bannerList = bannerDao.selectPage(condition.getSearch(), valid, area, start, pageSize);

        List<IndexBannerVO> voList = new ArrayList<>();
        for(IndexBannerData data: bannerList){
            IndexBannerVO vo = new IndexBannerVO();
            BeanUtils.copyProperties(data, vo);
            vo.setBanner_id(data.get_id().toString());

            if(data.getAtype() == IndexBannerDao.ACTION_TYPE_ROOM && !StringUtils.isEmpty(data.getActionValue())){
                String actionUid = RoomUtils.getRoomHostId(data.getActionValue());
                ActorData actorData = actorDao.getActorDataFromCache(actionUid);
                vo.setRoom_rid(actorData.getStrRid());
                vo.setActionValue(actorData.getStrRid());
            }

            if(data.getAtype() == IndexBannerDao.ACTION_TYPE_USER && !StringUtils.isEmpty(data.getActionValue())){
                ActorData actorData = actorDao.getActorDataFromCache(data.getActionValue());
                vo.setActionValue(actorData.getStrRid());
            }

            if(data.getFilterType() == HomeBannerService.FILTER_TYPE_USER && !StringUtils.isEmpty(data.getFilterItem())){
                String[] uidList = data.getFilterItem().split(",");
                List<String> ridList = new ArrayList<>();
                for (String uidStr : uidList) {
                    ActorData actorData = actorDao.getActorDataFromCache(uidStr);
                    ridList.add(actorData.getStrRid());
                }
                vo.setFilterItem(String.join(",", ridList));
            }
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(bannerDao.selectCount(valid, area));
        return pageVO;
    }


    public void addIndexBanner(IndexBannerVO dto){

        try {
            if(StringUtils.isEmpty(dto.getPreview()) || StringUtils.isEmpty(dto.getPreview_ar())){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }

            IndexBannerData bannerData = new IndexBannerData();
            BeanUtils.copyProperties(dto, bannerData);
            bannerData.setUrl(StringUtils.isEmpty(dto.getUrl()) ? dto.getUrl() : dto.getUrl().trim());
            if(dto.getAtype() == IndexBannerDao.ACTION_TYPE_ROOM && !StringUtils.isEmpty(dto.getActionValue())){
                ActorData actorData = actorDao.getActorByStrRid(dto.getActionValue());
                if(actorData == null){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "输入房间id有误");
                }
                bannerData.setRoom_id(RoomUtils.formatRoomId(actorData.getUid()));
                bannerData.setActionValue(RoomUtils.formatRoomId(actorData.getUid()));
            }

            if(dto.getAtype() == IndexBannerDao.ACTION_TYPE_USER && !StringUtils.isEmpty(dto.getActionValue())){
                ActorData actorData = actorDao.getActorByStrRid(dto.getActionValue());
                if(actorData == null){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "输入用户id有误");
                }
                bannerData.setActionValue(actorData.getUid());
            }

            if(dto.getFilterType() == HomeBannerService.FILTER_TYPE_USER){
                String[] ridList = dto.getFilterItem().split(",");
                List<String> uidList = new ArrayList<>();

                for (String ridStr : ridList) {
                    ActorData actorData = actorDao.getActorByStrRid(ridStr);
                    if(actorData == null){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                    }
                    uidList.add(actorData.getUid());
                }
                bannerData.setFilterItem(String.join(",", uidList));
            } else if (dto.getFilterType() == HomeBannerService.FILTER_TYPE_REGISTER) {
                String[] splitDay = dto.getFilterItem().split("-");
                int startDay = Integer.parseInt(splitDay[0]);
                int endDay = Integer.parseInt(splitDay[1]);
            } else if (dto.getFilterType() == HomeBannerService.FILTER_TYPE_LIMITED_RECHARGE) {
                // 首笔充值活动过滤类型，不需要特殊的参数处理
                // filterItem 可以为空或者用于其他配置
            }

            bannerData.setMtime(DateHelper.getNowSeconds());
            bannerData.setDataVersion(1);
            bannerDao.saveOne(bannerData);
        }catch (Exception e){
            logger.error("addIndexBanner error:{}", e.getMessage(), e);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
    }

    public void updateBannerData(IndexBannerVO dto) {

        try {
            IndexBannerData data = bannerDao.getDataByID(dto.getBanner_id());
            if(data == null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }

            Update update = new Update();
            update.set("title", dto.getTitle());
            update.set("title_ar", dto.getTitle_ar());
            update.set("valid", dto.getValid());
            update.set("url", StringUtils.isEmpty(dto.getUrl()) ? dto.getUrl() : dto.getUrl().trim());
            update.set("banner_order", dto.getBanner_order());
            update.set("valid", dto.getValid());
            update.set("area", dto.getArea());
            update.set("atype", dto.getAtype());
            update.set("web_type", dto.getWeb_type());
            update.set("startTime", dto.getStartTime());
            update.set("endTime", dto.getEndTime());
            update.set("filterType", dto.getFilterType());
            update.set("filterItem", dto.getFilterItem());
            update.set("mtime", DateHelper.getNowSeconds());
            update.set("dataVersion", data.getDataVersion() + 1);
            update.set("actionValue", dto.getActionValue());

            if(dto.getAtype() == IndexBannerDao.ACTION_TYPE_ROOM && !StringUtils.isEmpty(dto.getRoom_rid())){
                ActorData actorData = actorDao.getActorByStrRid(dto.getRoom_rid());
                if(actorData == null){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                }
                update.set("room_id", RoomUtils.formatRoomId(actorData.getUid()));
                update.set("actionValue", RoomUtils.formatRoomId(actorData.getUid()));
            }

            if(dto.getAtype() == IndexBannerDao.ACTION_TYPE_USER && !StringUtils.isEmpty(dto.getActionValue())){
                ActorData actorData = actorDao.getActorByStrRid(dto.getActionValue());
                if(actorData == null){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "输入用户id有误");
                }
                update.set("actionValue", actorData.getUid());
            }

            if(dto.getFilterType() == HomeBannerService.FILTER_TYPE_USER){
                String[] ridList = dto.getFilterItem().split(",");
                List<String> uidList = new ArrayList<>();
                for (String ridStr : ridList) {
                    ActorData actorData = actorDao.getActorByStrRid(ridStr);
                    if(actorData == null){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                    }
                    uidList.add(actorData.getUid());
                }
                update.set("filterItem", String.join(",", uidList));
            }else if (dto.getFilterType() == HomeBannerService.FILTER_TYPE_REGISTER) {
                String[] splitDay = dto.getFilterItem().split("-");
                int startDay = Integer.parseInt(splitDay[0]);
                int endDay =Integer.parseInt(splitDay[1]);
            } else if (dto.getFilterType() == HomeBannerService.FILTER_TYPE_LIMITED_RECHARGE) {
                // 首笔充值活动过滤类型，不需要特殊的参数处理
                // filterItem 可以为空或者用于其他配置
            }

            if (!StringUtil.isEmpty(dto.getPreview())){
                update.set("preview", dto.getPreview());
            }

            if (!StringUtil.isEmpty(dto.getPreview_ar())){
                update.set("preview_ar", dto.getPreview_ar());
            }

            bannerDao.updateData(dto.getBanner_id(), update);
        }catch (Exception e){
            logger.error("updateBannerData error:{}", e.getMessage(), e);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
    }
}
