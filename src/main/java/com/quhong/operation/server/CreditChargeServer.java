package com.quhong.operation.server;

import com.mongodb.client.result.UpdateResult;
import com.quhong.mongo.config.MongoBean;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.OperationActorDao;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.dao.MoneyOpLogDao;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.AdminUser;
import com.quhong.operation.share.mongobean.CreditChargeBill;
import com.quhong.operation.share.vo.CreditTotalVO;
import com.quhong.operation.share.vo.OnCreditVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.StringUtil;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/6/28
 */
@Service
public class CreditChargeServer {

    private final static Logger logger = LoggerFactory.getLogger(CreditChargeServer.class);

    @Resource(name=MongoBean.MOVIES)
    private MongoTemplate mongoTemp1;
    @Resource(name="operation_mongo_bean")
    private MongoTemplate mongoTemp2;
    @Autowired
    private ManagerDao managerDao;
    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private MoneyOpLogDao moneyOpLogDao;

    /**
     * @param startStamp
     * @param endStamp
     * @param rid
     * @return
     */
    public ApiResult<CreditTotalVO> getOnCreditList(Integer startStamp, Integer endStamp, Integer rid, Integer state) {
        logger.info("rid={}, startStamp={}, endStamp={}", rid, startStamp, endStamp);
        ApiResult<CreditTotalVO> result = new ApiResult<>();
        Criteria criteria = Criteria.where("c_time").gte(startStamp).lt(endStamp);
        // 将通过uid查询到的数据存入map中，已重复使用，不需要多次重复查mongo
        Map<String, Actor> actorMap = new HashMap<>();
        // 将操作人admin的查询到的数据存入map中，已重复使用，不需要多次重复查mongo
        Map<String, String> adminMap = new HashMap<>();

        if (null != rid) {
            ApiResult<Actor> apiResult = actorDao.getActorByRidOrBeforeRid(rid);
            logger.info("rid={}, actor result={}", rid, apiResult);
            if (!apiResult.isOK() || null == apiResult.getData()) {
                return result.error(apiResult.getMsg());
            }
            String aid = apiResult.getData().get_id().toString();
            actorMap.put(aid, apiResult.getData());
            criteria.and("uid").is(aid);
        }

        if (null != state && 1 == state) {
            criteria.and("state").ne(2);
        } else if (null != state && 2 == state) {
            criteria.and("state").is(2);
        }

        long star = System.currentTimeMillis();
        List<CreditChargeBill> creditList = mongoTemp2.find(new Query(criteria), CreditChargeBill.class);
        if (CollectionUtils.isEmpty(creditList)) {
            return result.ok();
        }
        logger.info("criteria={}, creditChargeBill size={} time={}", criteria, creditList.size(), System.currentTimeMillis() - star);

        // 将mongo share 转 VO
        List<OnCreditVO> list = new ArrayList<>();
        for (CreditChargeBill credit : creditList) {
            OnCreditVO vo = new OnCreditVO();
            BeanUtils.copyProperties(credit, vo);
            vo.setId(credit.get_id().toString());
            if (null == vo.getState() || 0 == vo.getState()) vo.setState(1);
            list.add(vo);
        }

        // 获取credit done 的数据，并合并到list中
        if (null == state) {
            star = System.currentTimeMillis();
            ApiResult<List<OnCreditVO>> creditDoneResult = moneyOpLogDao.getCreditDoneList(rid, startStamp, endStamp);
            if (creditDoneResult.isOK() && !CollectionUtils.isEmpty(creditDoneResult.getData())) {
                list.addAll(creditDoneResult.getData());
            }
            logger.info("rid={}, creditChargeBill size={}, time={}",
                    rid, creditList.size(), System.currentTimeMillis() - star);
        }

        // 按赊账时间排序
        list = sort(list);

        // 查更少的字段减少内存占用
        Document fields = new Document();
        fields.put("name", true);
        fields.put("country", true);
        fields.put("rid", true);
        fields.put("beans", true);
        Document field = new Document();
        field.put("account", true);
        Integer time = 0;
        Integer paid = 0;
        Integer totalBeans = 0;
        Integer paidBeans = 0;
        // 业务处理
        for (OnCreditVO vo : list) {
            // 把赊账时间和结清时间转字符串格式
            if (null != vo.getcTime() && 0 != vo.getcTime())
                vo.setCreditTime(DateHelper.ARABIAN.datetimeToStr(new Date(vo.getcTime() * 1000L)));
            if (null != vo.getEndTime() && 0 != vo.getEndTime())
                vo.setSettleTime(DateHelper.ARABIAN.datetimeToStr(new Date(vo.getEndTime() * 1000L)));

            // 查看用户的信息是否已经查出，若已查则不需要重新去mongo中查询
            String aid = vo.getUid();
            if (actorMap.containsKey(aid)) {
                vo.setUserName(actorMap.get(aid).getName());
                vo.setCountry(actorMap.get(aid).getCountry());
                vo.setRid(actorMap.get(aid).getRid());
                vo.setCurrentBeans(actorMap.get(aid).getBeans());
            } else {
                Document queryObject = new Document();
                queryObject.put("_id", aid);
                Actor actor = mongoTemp1.findOne(new BasicQuery(queryObject, fields), Actor.class);
                // 将actor放入map中
                if (null != actor) {
                    actorMap.put(aid, actor);
                    vo.setUserName(actor.getName());
                    vo.setCountry(actor.getCountry());
                    vo.setRid(actor.getRid());
                    vo.setCurrentBeans(actor.getBeans());
                }
            }

            // 获取adminUser的名称
            String adminUid = vo.getAdminUid();
            if (adminMap.containsKey(adminUid)) {
                vo.setAdminName(adminMap.get(adminUid));
            } else {
                Document queryObject = new Document();
                queryObject.put("uid", adminUid);
                AdminUser admin = mongoTemp1.findOne(new BasicQuery(queryObject, field), AdminUser.class);
                logger.info("admin user {}", admin);
                // 将adminActor放入map中
                if (null != admin) {
                    adminMap.put(adminUid, admin.getAccount());
                    vo.setAdminName(admin.getAccount());
                }
            }

            // 统计数据
            time++;
            totalBeans += vo.getBeans();
            if (2 == vo.getState()) {
                paid++;
                paidBeans += vo.getBeans();
            }
        }

        CreditTotalVO vo = new CreditTotalVO();
        vo.setOnCreditList(list);
        vo.setTime(time);
        vo.setPaid(paid);
        vo.setTotalBeans(totalBeans);
        vo.setPaidBeans(paidBeans);
        int scale = 1;
        vo.setCredibility1(new BigDecimal(paid).divide(new BigDecimal(time), scale, BigDecimal.ROUND_DOWN).doubleValue());
        vo.setCredibility2(new BigDecimal(paidBeans).divide(new BigDecimal(totalBeans), scale, BigDecimal.ROUND_DOWN).doubleValue());
        return result.ok(vo);
    }

    /**
     * 批量更新creditChargeBill的state状态
     *
     * @param ids
     * @param state
     * @param uid
     * @return
     */
    public ApiResult<Integer> updateCreditChargeState(String[] ids, Integer state, String uid) {
        ApiResult<Integer> result = new ApiResult<>();
        // 获取adminUser信息
//        ApiResult<Integer> apiResult = managerServer.verifyAdminLevelByUid(uid);
//        if (!apiResult.isOK())
//            return result.error(apiResult.getMsg());
//         // 判断当前用户adminUser 等级不能小于2 TODO 询问需要多少role等级才能更新赊账记录
//        if (apiResult.getData() < 2)
//            return result.error("you admin level less than 2");

        // 开始业务更新credit记录状态
        CreditChargeBill credit = null;
        Integer updateNum = 0;
        for (String id : ids) {
            // id是空的，没必要查询数据库，直接跳过循环
            if (StringUtil.isEmptyOrBlank(id)) continue;
            ;

            credit = mongoTemp2.findById(id, CreditChargeBill.class);
            logger.info("id={}, findById result {}", id, credit);
            // 如果数据不存在或者状态已是等于state则跳过此次循环
            if (null == credit || state == credit.getState()) continue;

            credit.setState(state);
            Update update = new Update();
            update.set("state", state);
            UpdateResult ur = mongoTemp2.updateFirst(
                    new Query(Criteria.where("_id").is(id)), update, CreditChargeBill.class);
            if (ur.getModifiedCount() == 1) {
                updateNum++;
            } else if (ur.getModifiedCount() > 1) {
                logger.error("_id={}, modified count={}", id, ur.getModifiedCount());
            }
        }

        return result.ok(updateNum);
    }

    // 将list中的creditVO按cTime时间排序
    private List<OnCreditVO> sort(List<OnCreditVO> list) {
        OnCreditVO[] arr = new OnCreditVO[list.size()];
        arr = list.toArray(arr);
        fastSort(arr, 0, arr.length - 1);
        return Arrays.asList(arr);
    }

    public static void fastSort(OnCreditVO[] intArr, int begin, int end) {
        if (end <= begin) {
            return;
        }
        int pivot = partition(intArr, begin, end);
        fastSort(intArr, begin, pivot - 1);
        fastSort(intArr, pivot + 1, end);
    }

    private static int partition(OnCreditVO[] intArr, int begin, int end) {
        int pivot = begin;
        OnCreditVO temp;
        for (int i = begin; i < end; i++) {
            if (intArr[i].getcTime() < intArr[end].getcTime()) {
                temp = intArr[pivot];
                intArr[pivot] = intArr[i];
                intArr[i] = temp;
                pivot++;
            }
        }
        temp = intArr[pivot];
        intArr[pivot] = intArr[end];
        intArr[end] = temp;
        return pivot;
    }

}
