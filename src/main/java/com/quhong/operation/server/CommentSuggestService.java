package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.dao.CommentSuggestDao;
import com.quhong.mysql.data.CommentSuggestData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CommentSuggestService {
    private static final Logger logger = LoggerFactory.getLogger(CommentSuggestService.class);

    @Resource
    private CommentSuggestDao commentSuggestDao;

    /**
     * 组件列表操作
     */
    public PageResultVO<CommentSuggestData> list(BaseCondition condition) {
        PageResultVO<CommentSuggestData> pageVO = new PageResultVO<>();
        int status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        IPage<CommentSuggestData> pageData = commentSuggestDao.selectPageList(search, status, page, pageSize);
        pageVO.setList(pageData.getRecords());
        pageVO.setTotal(pageData.getTotal());
        return pageVO;
    }

    public void addData(CommentSuggestData dto) {
        CommentSuggestData data = new CommentSuggestData();
        BeanUtils.copyProperties(dto, data);
        data.setMtime(DateHelper.getNowSeconds());
        data.setCtime(DateHelper.getNowSeconds());
        commentSuggestDao.insertOne(data);
    }

    public void updateData(CommentSuggestData dto) {
        CommentSuggestData data = commentSuggestDao.selectOne(dto.getId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "数据不存在");
        }
        BeanUtils.copyProperties(dto, data);
        data.setMtime(DateHelper.getNowSeconds());
        commentSuggestDao.updateOne(data);
    }
}
