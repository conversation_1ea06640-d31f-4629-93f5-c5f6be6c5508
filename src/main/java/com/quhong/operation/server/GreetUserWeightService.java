package com.quhong.operation.server;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.GreetWeightData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.GreetUserWeightVO;
import com.quhong.redis.NewGreetRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 社交推荐用户权重设置
 */
@Service
public class GreetUserWeightService {
    private static final Logger logger = LoggerFactory.getLogger(GreetUserWeightService.class);

    @Resource
    private NewGreetRedis newGreetRedis;
    @Resource
    private ManagerDao managerDao;

    /**
     * 获取用户权重
     */
    public GreetUserWeightVO list() {
        GreetUserWeightVO vo = new GreetUserWeightVO();
        List<GreetWeightData> userWeightList = newGreetRedis.getGreetUserWeight();
        for (GreetWeightData data : userWeightList) {
            if (!StringUtils.isEmpty(data.getOpUser())) {
                Manager manager = managerDao.getDataByUid(data.getOpUser());
                data.setOpUser(manager != null ? manager.getAccount() : "");
            }
        }
        vo.setUserWeightList(userWeightList);
        return vo;
    }



    /**
     * 更新用户权重
     */
    public void updateData(String uid, GreetWeightData dto) {
        // 参数校验
        // validateAdCampaignGameData(dto);
        if (dto.getRegionWeight() > 1 || dto.getRegisterWeight() > 1 || dto.getOnlineWeight() > 1 || dto.getInterestWeight() > 1 || dto.getReplyWeight() > 1 || dto.getProfileWeight() > 1 || dto.getSexWeight() > 1){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "权重不能大于1");
        }

        if (dto.getRegionWeight() < 0 || dto.getRegisterWeight() < 0 || dto.getOnlineWeight() < 0 || dto.getInterestWeight() < 0 || dto.getReplyWeight() < 0 || dto.getProfileWeight() < 0 || dto.getSexWeight() < 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "权重不能小于0");
        }

        List<GreetWeightData> userWeightList = newGreetRedis.getGreetUserWeight();
        for (GreetWeightData data : userWeightList) {
            if (data.getUserType() == dto.getUserType()) {
                BeanUtils.copyProperties(dto, data);
                data.setOpUser(uid);
                data.setMtime(DateHelper.getNowSeconds());
                break;
            }
        }
        newGreetRedis.setGreetUserWeight(userWeightList);
    }
}
