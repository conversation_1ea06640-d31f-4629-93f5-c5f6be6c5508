package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.constant.ReportOriginConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.UserReportJoinData;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MReportData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.MomentTopicData;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.data.UserReportData;
import com.quhong.operation.share.condition.ReportUserCondition;
import com.quhong.operation.share.dto.BlockUserMsgDTO;
import com.quhong.operation.share.dto.MReportDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ReportUserVO;
import com.quhong.operation.share.vo.UserReportVO;
import com.quhong.operation.utils.StringUtil;
import com.quhong.redis.BlockRedis;
import com.quhong.room.RoomWebSender;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class ReportUserService {
    private static final Logger logger = LoggerFactory.getLogger(ReportUserService.class);
    private static final List<String> BLOCK_TYPE_LIST = Arrays.asList("publicMsg", "privateMsg");
    private static final List<String> PRIVATE_OFFICIAL_TITLE_DESC_EN = Arrays.asList(
            "Block private messages",
            "Due to violation of the community code of conduct, the system has disabled your private message function for xxx days. Please chat in a civilized and friendly manner.",
            "Due to violation of the community code of conduct, the system has disabled your private message function. Please chat in a civilized and friendly manner.");

    private static final List<String> PRIVATE_OFFICIAL_TITLE_DESC_AR = Arrays.asList(
            "حظر الرسائل الخاصة",
            "بسبب انتهاك قواعد سلوك المجتمع، قام النظام بتعطيل وظيفة الرسائل الخاصة لديك لمدة xxx من الأيام. يرجى الدردشة بطريقة حضارية وودية.",
            "بسبب انتهاك قواعد سلوك المجتمع، قام النظام بتعطيل وظيفة الرسائل الخاصة. يرجى الدردشة بطريقة حضارية وودية.");

    private static final List<String> PUBLIC_OFFICIAL_TITLE_DESC_EN = Arrays.asList(
            "Block room chat",
            "Due to a violation of the community code of conduct, the system has disabled the text chat function in your room for xxx days. Please chat in a civilized and friendly manner.",
            "Due to a violation of the community code of conduct, the system has disabled the text chat function in your room. Please chat in a civilized and friendly manner.");

    private static final List<String> PUBLIC_OFFICIAL_TITLE_DESC_AR = Arrays.asList(
            "حظر دردشة الغرفة",
            "بسبب انتهاك قواعد سلوك المجتمع، قام النظام بتعطيل وظيفة الدردشة النصية في غرفتك لمدة xxx من الأيام. يرجى الدردشة بطريقة حضارية وودية.",
            "بسبب انتهاك قواعد سلوك المجتمع، قام النظام بتعطيل وظيفة الدردشة النصية. يرجى الدردشة بطريقة حضارية وودية.");

    @Resource
    private MReportDao mReportDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private UserReportDao userReportDao;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    protected RoomWebSender roomWebSender;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private MomentTopicDao momentTopicDao;

    public PageResultVO<ReportUserVO> reportUserList(ReportUserCondition condition){
        PageResultVO<ReportUserVO> pageVO = new PageResultVO<>();
        int status = condition.getStatus();
        int newType = condition.getNewType();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        String searchUid = "";

        if(!StringUtil.isEmpty(search)){
            ActorData actor = actorDao.getActorByStrRid(search);
            if(actor == null){
                return pageVO;
            }

            searchUid = actor.getUid();
        }
        List<MReportData>  mReportList = mReportDao.selectMReportDataPage(newType, status, searchUid, start, pageSize);

        List<ReportUserVO> voList = new ArrayList<>();
        for(MReportData data: mReportList){
            ReportUserVO vo = new ReportUserVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());

            String fromUid = data.getUid();
            ActorData actor = actorDao.getActorDataFromCache(fromUid);
            vo.setReportRid(actor.getRid());
            vo.setReportName(actor.getName());
            vo.setReportHead(actor.getHead());

            // 是mongo数据生成的时间视为注册时间
            vo.setRegisterTime(new ObjectId(fromUid).getTimestamp());
            vo.setRechargeMoney(actorPayExternalDao.getUserRechargeMoney(fromUid));

            int new_type = data.getNew_type();
            String targetId = data.getTarget_id();

            if(new_type > 0){
                JSONObject jsonObject = new JSONObject();
                if(new_type == 1){
                    ActorData targetActor = actorDao.getActorDataFromCache(targetId);
                    jsonObject.put("reportedRid", targetActor.getRid());
                    jsonObject.put("reportedUid", targetActor.getUid());
                    jsonObject.put("reportedHead", targetActor.getHead());
                } else if (new_type == 2) {
                    MongoRoomData roomData = mongoRoomDao.getDataFromCache(targetId);
                    ActorData targetActor = actorDao.getActorDataFromCache(targetId.substring(2));
                    jsonObject.put("reportedRid", targetActor.getRid());
                    jsonObject.put("reportedUid", targetActor.getUid());
                    jsonObject.put("reportedHead", roomData.getHead());

                }else if (new_type == 3){
                    jsonObject.put("reportedMid", targetId);
                }else if (new_type == 4){
                    RoomEventData eventData = roomEventDao.selectById(Integer.parseInt(targetId));
                    String eventUid = eventData.getRoomId().substring(2);
                    ActorData targetActor = actorDao.getActorDataFromCache(eventUid);
                    jsonObject.put("reportedUid", eventUid);
                    jsonObject.put("reportedRid", targetActor.getRid());
                    jsonObject.put("eventName", eventData.getName());
                    jsonObject.put("eventDesc", eventData.getDescription());
                    jsonObject.put("eventCoverHead", eventData.getEventCoverUrl());
                }

                vo.setReportInfo(jsonObject);
            }

            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(mReportDao.selectCount(status, search));
        return pageVO;
    }


    public void updateMReporteData(MReportDTO dto) {

        MReportData data = mReportDao.getMReportDataByID(dto.getDocId());
        if(data == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }


        Update update = new Update();
        update.set("status", dto.getStatus());
        mReportDao.updateData(dto.getDocId(), update);
    }

    /**
     * 847版本举报
     */
    public PageResultVO<UserReportVO> reportUserRecordList(ReportUserCondition condition){
        PageResultVO<UserReportVO> pageVO = new PageResultVO<>();

        String search = "-1".equals(condition.getSearch())? null : condition.getSearch();
        String origin = "-1".equals(condition.getOrigin()) ? null : condition.getOrigin();
        String reasonSelect = "-1".equals(condition.getReasonSelect())? null : condition.getReasonSelect();
        int reportNum = condition.getReportNum();
        int startTime = DateHelper.ARABIAN.stringDateToStampSecond(condition.getStart());
        int endTime = DateHelper.ARABIAN.stringDateToStampSecond(condition.getEnd()) + 86400;
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;


        String searchUid = null;
        if(!StringUtil.isEmpty(search)){
            ActorData actor = actorDao.getActorByStrRid(search);
            if(actor == null){
                return pageVO;
            }
            searchUid = actor.getUid();
        }

        List<UserReportJoinData>  mReportList = userReportDao.getUserReportJoinData(startTime, endTime, searchUid, origin, reasonSelect, reportNum, start, pageSize);

        List<UserReportVO> voList = new ArrayList<>();
        for(UserReportJoinData data: mReportList){

            try {
                UserReportVO vo = new UserReportVO();
                BeanUtils.copyProperties(data, vo);

                String fromUid = data.getUid();
                ActorData actor = actorDao.getActorDataFromCache(fromUid);
                vo.setRid(actor.getStrRid());

                String targetId = data.getTargetId();
                if(vo.getOrigin().equals(ReportOriginConstant.FAMILY)){
                    FamilyData familyData = familyDao.selectByFamilyRid(Integer.valueOf(targetId));
                    if(familyData != null){
                        ActorData otherActor = actorDao.getActorDataFromCache(familyData.getOwnerUid());
                        vo.setReportedRid(otherActor != null ? otherActor.getStrRid() : "");
                    }

                } else if (vo.getOrigin().equals(ReportOriginConstant.MOMENT_TOPIC)) {
                    MomentTopicData topicData = momentTopicDao.selectByTopicRid(Integer.valueOf(targetId));

                    if(topicData != null){
                        ActorData otherActor = actorDao.getActorDataFromCache(topicData.getOwnerUid());
                        vo.setReportedRid(otherActor != null ? otherActor.getStrRid() : "");
                    }
                    
                } else {
                    ActorData reportedActor = actorDao.getActorDataFromCache(targetId);
                    vo.setReportedRid(reportedActor.getStrRid());
                    vo.setReportedPrivateMsg(blockRedis.getBlockUserPrivateMsgStatus(targetId) != 0 ? 1 : 0);
                    vo.setReportedPublicMsg(blockRedis.getBlockUserPublicMsgStatus(targetId) != 0 ? 1 : 0);
                }
                vo.setImages(JSON.parseArray(data.getImageInfo(), String.class));



                voList.add(vo);
            }catch (Exception e){
                logger.error("reportUserRecordList error UserReportJoinData:{}", JSONObject.toJSONString(data));
                continue;
            }
        }

        pageVO.setList(voList);
        pageVO.setTotal(userReportDao.getUserReportJoinCountData(startTime, endTime, searchUid, origin, reasonSelect, reportNum));
        return pageVO;
    }

    public PageResultVO<UserReportVO> reportUserRecordDetailList(ReportUserCondition condition){
        PageResultVO<UserReportVO> pageVO = new PageResultVO<>();

        IPage<UserReportData> pageReportList = userReportDao.selectPage(condition.getTargetId(),  condition.getOrigin(), condition.getReasonSelect(), condition.getPage(), condition.getPageSize());

        List<UserReportVO> voList = new ArrayList<>();
        for(UserReportData data: pageReportList.getRecords()){
            try {
                UserReportVO vo = new UserReportVO();
                BeanUtils.copyProperties(data, vo);

                String fromUid = data.getUid();
                ActorData actor = actorDao.getActorDataFromCache(fromUid);
                vo.setRid(actor.getStrRid());

                String targetUid = data.getTargetId();
                ActorData reportedActor = actorDao.getActorDataFromCache(targetUid);
                vo.setReportedRid(reportedActor.getStrRid());
                vo.setReportedPrivateMsg(blockRedis.getBlockUserPrivateMsgStatus(targetUid) != 0 ? 1 : 0);
                vo.setReportedPublicMsg(blockRedis.getBlockUserPublicMsgStatus(targetUid) != 0 ? 1 : 0);
                vo.setImages(JSON.parseArray(data.getImageInfo(), String.class));
                voList.add(vo);
            }catch (Exception e){
                logger.error("reportUserRecordDetailList error UserReportJoinData:{}", JSONObject.toJSONString(data));
            }
        }

        pageVO.setList(voList);
        pageVO.setTotal(pageReportList.getTotal());
        return pageVO;
    }

    public void blockUserMsg(BlockUserMsgDTO dto) {
        String aid = dto.getAid();
        String blockType = dto.getBlockType();
        int timeOut = dto.getTimeOut();
        if(StringUtil.isEmpty(aid)){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if(!BLOCK_TYPE_LIST.contains(blockType)){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        ActorData actor = actorDao.getActorDataFromCache(aid);
        if(actor == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if(BLOCK_TYPE_LIST.get(0).equals(blockType)){
            if (timeOut == 0) {
                blockRedis.removeBlockUserPublicMsg(aid);
            } else {
                blockRedis.addBlockUserPublicMsg(aid, timeOut);
                officialMsgPush(aid, blockType, timeOut);
            }
        }else {
            if (timeOut == 0) {
                blockRedis.removeBlockUserPrivateMsgScore(aid);
            } else {
                blockRedis.addBlockUserPrivateMsg(aid, timeOut);
                officialMsgPush(aid, blockType, timeOut);
            }
        }
    }


    private void officialMsgPush(String uid, String blockType, int timeOut) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor. uid={}", uid);
            return;
        }

        int slang = actorData.getSlang() == 1 ? 1 : 2;
        List<String> pushNewEN = BLOCK_TYPE_LIST.get(0).equals(blockType) ? PUBLIC_OFFICIAL_TITLE_DESC_EN : PRIVATE_OFFICIAL_TITLE_DESC_EN;
        List<String> pushNewAR = BLOCK_TYPE_LIST.get(0).equals(blockType) ? PUBLIC_OFFICIAL_TITLE_DESC_AR : PRIVATE_OFFICIAL_TITLE_DESC_AR;
        List<String> pushNew = slang == SLangType.ARABIC ?pushNewAR : pushNewEN;



        OfficialData officialData = new OfficialData();
        officialData.setSubTitle("");
        officialData.setTo_uid(uid);
        officialData.setValid(1);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setAtype(0);
        officialData.setTitle(pushNew.get(0));
        officialData.setBody(timeOut < 0 ? pushNew.get(2) : pushNew.get(1).replace("xxx", String.valueOf(timeOut)));
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        }
    }
}
