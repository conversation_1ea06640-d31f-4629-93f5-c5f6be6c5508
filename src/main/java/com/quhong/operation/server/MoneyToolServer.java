package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ServerType;
import com.quhong.feign.DataCenterService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.OperationActorDao;
import com.quhong.operation.share.el.MoneyDetailES;
import com.quhong.operation.share.mongobean.Actor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 钻石充值服务
 * @date 2020/7/20
 */
@Service
public class MoneyToolServer {

    private final static Logger logger = LoggerFactory.getLogger(MoneyToolServer.class);

    @Autowired
    private OperationActorDao operationActorDao;
    @Resource
    private ActorDao actorDao;
    @Autowired
    private DataCenterService dataCenterService;

    /**
     * 通过rid充值
     * 涉及到分布式原因，不能使用rid直接充值。需要转uid
     *
     * @param rid     rid
     * @param beans   充值钻石数
     * @param title   充值类型
     * @param actType 充值类型
     * @param desc    描述
     */
    public ApiResult<Long> chargeBeansByRid(String rid, int beans, String title, Integer actType, String desc, boolean everyday) {
        String uid = "";
        boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
        if (test) {
            ActorData actorData = actorDao.getActorByStrRidFromDb(rid);
            if (null == actorData) {
                logger.error("test rid={} user is not existed or invalid", rid);
                return new ApiResult<Long>().error("user is not existed or invalid");
            }
            uid = actorData.getUid();
        } else {
            ApiResult<Actor> result = operationActorDao.getUidByStrRid(rid);
            if (!result.isOK()) {
                logger.error("rid={} user is not existed or invalid", rid);
                return new ApiResult<Long>().error("user is not existed or invalid");
            }
            Actor actor = result.getData();
            // 新增规则：打钻时检测名单上用户的账户剩余钻石数，如果该用户的剩余钻石数大于/等于20000，则不执行打钻。
            if (everyday && actor.getBeans() >= 20000) {
                logger.error("user beans greater than or equal 20000, uid={}", actor.getUid());
                return new ApiResult<Long>().error("user beans greater than or equal 20000, uid=" + actor.getUid());
            }
            uid = actor.get_id().toString();
        }
        MoneyDetailES money = new MoneyDetailES();
        money.setUid(uid);
        money.setChanged(beans);
        money.setAtype(actType);
        money.setTitle(title);
        money.setDesc(desc);
        return chargeBeansByUid(money);
    }

    /**
     * 通过uid充值
     *
     * @param money 打钻信息
     */
    public ApiResult<Long> chargeBeansByUid(MoneyDetailES money) {
        ApiResult<Long> result = new ApiResult<>();
        MoneyDetailReq moneyDetail = new MoneyDetailReq();
        moneyDetail.setRandomId();
        moneyDetail.setUid(money.getUid());
        moneyDetail.setAtype(money.getAtype());
        moneyDetail.setTitle(money.getTitle());
        moneyDetail.setDesc(money.getDesc());
        moneyDetail.setChanged(money.getChanged());
        logger.info("charge beans money={}", JSON.toJSONString(money));
        com.quhong.enums.ApiResult<String> apiResult = dataCenterService.chargeBeans(moneyDetail);
        if (apiResult.isError()) {
            logger.error("data center server charge_beans invoking error.code={} msg={} uid ={} beans={}",
                    apiResult.getCode(), apiResult.getCode().getMsg(), money.getUid(), money.getChanged());
            return result.error("data-center server charge_beans invoking error.");
        }
        return result.ok(10L);
    }
}
