package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.UserFeedbackDao;
import com.quhong.mysql.data.UserFeedbackData;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.FeedbackCondition;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.UserFeedbackVO;
import com.quhong.redis.UserFeedbackRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class UserFeedbackService {
    private static final Logger logger = LoggerFactory.getLogger(UserFeedbackService.class);

    @Resource
    private UserFeedbackDao userFeedbackDao;
    @Resource
    private UserFeedbackRedis userFeedbackRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private ManagerDao managerDao;

    public PageResultVO<UserFeedbackVO> getDataList(FeedbackCondition condition){
        PageResultVO<UserFeedbackVO> pageVO = new PageResultVO<>();
        String start = condition.getStart();
        String end = condition.getEnd();
        int status = condition.getStatus();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        String origin = condition.getOrigin();
        String problemSelect = condition.getProblemSelect();
        int feedbackNum = condition.getFeedbackNum();
        int userType = condition.getUserType();
        int startTime = DateHelper.ARABIAN.stringDateToStampSecond(start);
        int endTime = DateHelper.ARABIAN.stringDateToStampSecond(end) + 86400;
        IPage<UserFeedbackData> pageGift = userFeedbackDao.selectPage(condition.getFeedbackId(), startTime, endTime, origin, userType, problemSelect, feedbackNum, status, page, pageSize);

        List<UserFeedbackVO> voList = new ArrayList<>();
        for (UserFeedbackData data : pageGift.getRecords()) {
            UserFeedbackVO vo = new UserFeedbackVO();
            BeanUtils.copyProperties(data, vo);
            vo.setImages(JSON.parseArray(data.getImageInfo(), String.class));
            ActorData actorData = null;
            if(data.getFeedbackId().length() == 24){
                actorData = actorDao.getActorDataFromCache(data.getFeedbackId());
            }else {
                actorData = actorDao.getActorByDistinctId(data.getFeedbackId());
            }
            if(actorData != null){
                vo.setRid(actorData.getStrRid());
                vo.setOriginalId(actorData.getOriginalRid());
                vo.setRechargeMoney(actorPayExternalDao.getUserRechargeMoney(actorData.getUid()).doubleValue());
            }

            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }



    public void updateData(String uid, UserFeedbackData dto){
        UserFeedbackData feedbackData = userFeedbackDao.selectById(dto.getId());
        if(feedbackData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        feedbackData.setReplyInfo(dto.getReplyInfo());
        if(dto.getStatus() == 1){
            feedbackData.setUnread(1);
            userFeedbackRedis.setFeedbackStatus(feedbackData.getFeedbackId());
        }

        Manager manager = managerDao.getDataByUid(uid);
        feedbackData.setHandleUser(manager != null ? manager.getAccount() : "");
        feedbackData.setStatus(dto.getStatus());
        feedbackData.setMtime(DateHelper.getNowSeconds());
        userFeedbackDao.updateOne(feedbackData);

    }

}
