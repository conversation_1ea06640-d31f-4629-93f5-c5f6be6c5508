package com.quhong.operation.server;


import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.BackStageConfigConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.MongoThemeDao;
import com.quhong.mongo.data.MongoThemeData;
import com.quhong.mysql.dao.BackstageConfigDao;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.condition.RoomBGCondition;
import com.quhong.operation.share.dto.RoomBGDTO;
import com.quhong.operation.share.dto.RoomBackGroundDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceVO;
import com.quhong.operation.share.vo.RoomBackGroundTypeVO;
import com.quhong.operation.share.vo.RoomBackGroundVO;
import com.quhong.operation.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class RoomBackGroundService implements ResourceService {
    private static final Logger logger = LoggerFactory.getLogger(RoomBackGroundService.class);

    @Resource
    private MongoThemeDao mongoThemeDao;

    @Resource
    private BackstageConfigDao backstageConfigDao;

    @Resource
    private MongoRoomDao mongoRoomDao;


    public List<RoomBackGroundTypeVO> backGroundType(){

        List<RoomBackGroundTypeVO> list = new ArrayList<>();

        String configData = backstageConfigDao.getConfigData(BackStageConfigConstant.BACKGROUND_CONFIG);
        RoomBackGroundDTO roomBackGroundDTO = JSONObject.parseObject(configData, RoomBackGroundDTO.class);
        for (Map.Entry<String, Integer> entry : roomBackGroundDTO.getBgType().entrySet()) {
            RoomBackGroundTypeVO roomBackGroundTypeVO = new RoomBackGroundTypeVO();
            roomBackGroundTypeVO.setTitle(entry.getKey());
            roomBackGroundTypeVO.setType(entry.getValue());
            list.add(roomBackGroundTypeVO);
        }
        Collections.sort(list);
        return list;
    }



    public PageResultVO<RoomBackGroundVO> roomBGPageList(RoomBGCondition roomBGCondition){
        PageResultVO<RoomBackGroundVO> pageVO = new PageResultVO<>();
        String search = roomBGCondition.getSearch();
        Integer bgType = roomBGCondition.getBgType();
        Integer status = roomBGCondition.getStatus();
        int page = roomBGCondition.getPage() == null ? 1 : roomBGCondition.getPage();
        int pageSize = roomBGCondition.getPage_size() == null ? 30 : roomBGCondition.getPage_size();
        int start = (page - 1) * pageSize;

        List<MongoThemeData> mongoThemeList = mongoThemeDao.selectPage(search, bgType, status, start, pageSize);

        List<RoomBackGroundVO> voList = new ArrayList<>();
        for(MongoThemeData themeData: mongoThemeList){
            RoomBackGroundVO roomBackGroundVO = new RoomBackGroundVO();
            BeanUtils.copyProperties(themeData, roomBackGroundVO);
            roomBackGroundVO.setBgType(themeData.getType());
            roomBackGroundVO.setDocId(themeData.get_id().toString());
            roomBackGroundVO.setCount(mongoRoomDao.themeCount(themeData.getTid()));
            voList.add(roomBackGroundVO);
        }

        pageVO.setList(voList);
        pageVO.setTotal(mongoThemeDao.selectCount(search, bgType, status));
        return pageVO;
    }


    public void addRoomBG(RoomBGDTO roomBGDTO){


        MongoThemeData lastThemeData = mongoThemeDao.getLastThemeData();
        int nextId = lastThemeData != null ? lastThemeData.getTid()+1 : 1;

        MongoThemeData mongoThemeData = new MongoThemeData();
        mongoThemeData.setTid(nextId);
        mongoThemeData.setTitle(roomBGDTO.getTitle());
        mongoThemeData.setTitleAr(roomBGDTO.getTitleAr());
        mongoThemeData.setStatus(roomBGDTO.getStatus());
        mongoThemeData.setType(roomBGDTO.getBgType());
        mongoThemeData.setCurrencyType(roomBGDTO.getCurrencyType());
        mongoThemeData.setPrice(roomBGDTO.getPrice());
        mongoThemeData.setBgurl(roomBGDTO.getBackground());
        mongoThemeData.setPreview(roomBGDTO.getPreview());
        mongoThemeData.setNewtheme(roomBGDTO.getNewtheme());
        mongoThemeData.setOrderid(0);
        mongoThemeData.setValidDate(roomBGDTO.getValidDate());

        mongoThemeDao.saveThemeOne(mongoThemeData);

    }

    public void updateRoomBG(RoomBGDTO roomBGDTO) {

        MongoThemeData mongoThemeData = mongoThemeDao.getThemeDataByID(roomBGDTO.getDocId());
        if(mongoThemeData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        Update update = new Update();
        update.set("title", roomBGDTO.getTitle());
        update.set("titleAr", roomBGDTO.getTitleAr());
        update.set("status", roomBGDTO.getStatus());
        update.set("type", roomBGDTO.getBgType());
        update.set("currencyType", roomBGDTO.getCurrencyType());
        update.set("price", roomBGDTO.getPrice());

        if (!StringUtil.isEmpty(roomBGDTO.getBackground())){
            update.set("bgurl", roomBGDTO.getBackground());
        }

        if (!StringUtil.isEmpty(roomBGDTO.getPreview())){
            update.set("preview", roomBGDTO.getPreview());
        }

        update.set("newtheme", roomBGDTO.getNewtheme());
        update.set("validDate", roomBGDTO.getValidDate());
        mongoThemeDao.updateThemeData(roomBGDTO.getDocId(), update);
    }

    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<MongoThemeData> mongoThemeList = mongoThemeDao.selectPage(search, -1,  condition.getStatus(), start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for(MongoThemeData data: mongoThemeList){
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getTid());
            vo.setResourceIcon(data.getPreview());
            vo.setResourceNameEn(data.getTitle());
            vo.setResourceNameAr(data.getTitleAr());
            vo.setResourcePrice(data.getPrice());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(mongoThemeDao.selectCount(search, -1, condition.getStatus()));
        return pageVO;
    }
}
