package com.quhong.operation.server;

import com.quhong.constant.MatchConstant;
import com.quhong.datas.DayTimeData;
import com.quhong.operation.dao.OperationActorDao;
import com.quhong.operation.share.data.FriendSwipeInfo;
import com.quhong.operation.share.vo.crush.SuperLikeVO;
import com.quhong.operation.utils.ActorUtils;
import com.quhong.operation.utils.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class CrushReportServer {

    private static final Logger logger = LoggerFactory.getLogger(CrushReportServer.class);

    @Autowired
    private OperationActorDao actorDao;

    /**
     * 超级喜欢报表
     *
     * @param dayTimeData 日期
     * @param app         上线包
     * @param user        1新用户0老用户
     * @param gender      1男2女
     * @return
     */
    public SuperLikeVO superLikeReport(DayTimeData dayTimeData, int app, int user, int gender) {
        return new SuperLikeVO();
    }

    private Set<String> getSwipeUser(List<FriendSwipeInfo> swipeList, boolean isFrom) {
        Set<String> uidSet = new HashSet<>();
        for (FriendSwipeInfo friendSwipeInfo : swipeList) {
            String uid = friendSwipeInfo.getUid();
            if (!isFrom) {
                uid = friendSwipeInfo.getAid();
            }
            uidSet.add(uid);
        }
        return uidSet;
    }

    private List<FriendSwipeInfo> filterSwipe(List<FriendSwipeInfo> swipeList, Set<String> swipeUser, boolean isFrom) {
        List<FriendSwipeInfo> list = new ArrayList<>();
        for (FriendSwipeInfo friendSwipeInfo : swipeList) {
            String uid = "";
            if (isFrom) {
                uid = friendSwipeInfo.getUid();
            } else {
                uid = friendSwipeInfo.getAid();
            }
            if (swipeUser.contains(uid)) {
                list.add(friendSwipeInfo);
            }
        }
        return list;
    }

    private Set<String> getSwipeActors(DayTimeData dayTimeData, List<FriendSwipeInfo> swipeList, int app, int user, int gender) {
        Set<String> uidSet = new HashSet<>();
        for (FriendSwipeInfo friendSwipeInfo : swipeList) {
            String uid = friendSwipeInfo.getUid();
            String aid = friendSwipeInfo.getAid();
            uidSet.add(uid);
            uidSet.add(aid);
        }
        if (app == -1 && user == -1 && gender == -1) {
            return uidSet;
        }
        String appPackage = AppUtils.getAppPackage(app);
        uidSet = actorDao.filterActors(uidSet, gender, appPackage, -1, null);
        if (user != -1) {
            Set<String> newlySet = new HashSet<>();
            Set<String> oldSet = new HashSet<>();
            for (String uid : uidSet) {
                if (ActorUtils.isNewRegisterActor(uid, dayTimeData.getTime() * 1000L)) {
                    //新用户
                    newlySet.add(uid);
                } else {
                    //老用户
                    oldSet.add(uid);
                }
            }
            if (user == 1) {
                uidSet = newlySet;
            } else {
                uidSet = oldSet;
            }
        }
        return uidSet;
    }


    private List<FriendSwipeInfo> getSwipeList(List<FriendSwipeInfo> swipeList, int event, boolean isUnlock) {
        Set<Integer> typeSet = new HashSet<>();
        if (event == MatchConstant.LIKE) {
            //喜欢
            typeSet.add(1);
            typeSet.add(-1);
            typeSet.add(0);
        } else if (event == MatchConstant.SUPER_LIKE) {
            //超级喜欢
            typeSet.add(3);
        } else if (event == MatchConstant.DISLIKE) {
            //不喜欢，skip
            typeSet.add(2);
        }
        List<FriendSwipeInfo> eventOpList = new ArrayList<>();
        if (isUnlock) {
            for (FriendSwipeInfo stat : swipeList) {
                int unlock = stat.getUnlock();
                if (typeSet.contains(stat.getType()) && unlock == 1) {
                    eventOpList.add(stat);
                }
            }
        } else {
            for (FriendSwipeInfo stat : swipeList) {
                if (typeSet.contains(stat.getType())) {
                    eventOpList.add(stat);
                }
            }
        }
        return eventOpList;
    }

}
