package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataResourcesService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.RechargeCouponDao;
import com.quhong.mysql.dao.UserRechargeCouponDao;
import com.quhong.mysql.data.RechargeCouponData;
import com.quhong.mysql.data.UserRechargeCouponData;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.RechargeCouponCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.CouponOptionListVO;
import com.quhong.operation.share.vo.CouponUseRecordVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Service
public class RechargeCouponService implements ResourceService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private RechargeCouponDao rechargeCouponDao;
    @Resource
    private UserRechargeCouponDao userRechargeCouponDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private DataResourcesService dataResourcesService;
    @Resource
    private MqSenderService mqSenderService;

    public PageResultVO<RechargeCouponData> selectPage(RechargeCouponCondition condition) {
        IPage<RechargeCouponData> iPage = rechargeCouponDao.selectPage(-1, condition.getSearch(),
                condition.getStatus(), condition.getPage(), condition.getPageSize());
        return new PageResultVO<>(iPage);
    }

    public void save(String uid, RechargeCouponData dto) {
        RechargeCouponData data;
        Manager manager = managerDao.getDataByUid(uid);
        String operator = manager != null ? manager.getAccount() : "";
        int nowSeconds = DateHelper.getNowSeconds();
        if (dto.getId() != null && dto.getId() != 0) {
            // 修改
            data = rechargeCouponDao.selectOneById(dto.getId());
            if (!dto.getType().equals(data.getType())) {
                throw new CommonH5Exception(new HttpCode(1, "显示规则不支持修改"));
            }
            BeanUtils.copyProperties(dto, data, "operator", "mtime", "ctime");
            data.setOperator(operator);
            data.setMtime(nowSeconds);
            rechargeCouponDao.update(data);
        } else {
            // 新增
            data = new RechargeCouponData();
            BeanUtils.copyProperties(dto, data, "operator", "mtime", "ctime");
            data.setOperator(operator);
            data.setMtime(nowSeconds);
            data.setCtime(nowSeconds);
            rechargeCouponDao.insert(data);
        }
    }

    public PageResultVO<CouponUseRecordVO> useRecord(RechargeCouponCondition condition) {
        String aid = "";
        if (StringUtils.hasLength(condition.getRid())) {
            ActorData actorData = actorDao.getActorByStrRid(condition.getRid());
            aid = actorData != null ? actorData.getUid() : "---";
        }
        IPage<UserRechargeCouponData> iPage = userRechargeCouponDao.selectPage(aid, condition.getCouponId(), condition.getSearch(),
                condition.getExtraProp(), condition.getSource(), condition.getStatus(), condition.getPage(), condition.getPageSize());
        List<CouponUseRecordVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (UserRechargeCouponData data : iPage.getRecords()) {
                RechargeCouponData couponData = rechargeCouponDao.selectOneById(data.getCouponId());
                if (couponData == null) {
                    continue;
                }
                CouponUseRecordVO vo = new CouponUseRecordVO();
                ActorData user = actorDao.getActorDataFromCache(data.getUid());
                vo.setAid(user.getUid());
                vo.setRid(user.getOriginalRid());
                vo.setStrRid(user.getStrRid());
                vo.setCouponId(data.getCouponId());
                vo.setExtraProp(couponData.getExtraProp());
                vo.setStrValidDay(getStrValidDay(couponData));
                vo.setLeftValidDay(getLeftValidDay(data, couponData));
                vo.setTitle(couponData.getTitle());
                vo.setSource(data.getSource());
                vo.setGetTime(data.getCtime());
                vo.setRechargeNum(getStrRechargeNum(data.getRechargeDiamonds(), couponData.getExtraProp()));
                vo.setStatus(data.getStatus());
                vo.setOrderId(data.getOrderId());
                list.add(vo);
            }
        }
        return new PageResultVO<>(iPage.getTotal(), list);
    }

    public void sendCoupon(String managerUid, String strRids, int couponId) {
        RechargeCouponData couponData = rechargeCouponDao.selectOne(couponId, 0, 1);
        if (couponData == null) {
            logger.info("can not find valid coupon data. couponId={}", couponId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String[] strRidArr = strRids.replace(" ", "").replace("，", ",").split(",");
        for (String strRid : strRidArr) {
            ActorData actorData = actorDao.getActorByStrRid(strRid);
            if (actorData == null) {
                logger.error("can not find actor data. strRid={}", strRid);
                continue;
            }
            ResourcesDTO resourcesDTO = new ResourcesDTO();
            resourcesDTO.setUid(actorData.getUid());
            resourcesDTO.setResId(String.valueOf(couponId));
            resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_RECHARGE_COUPON);
            resourcesDTO.setDays(couponData.getValidDay());
            resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
            Manager manager = managerDao.getDataByUid(managerUid);
            resourcesDTO.setDesc(String.format("%s账号发放", manager.getAccount()));
            resourcesDTO.setmTime(DateHelper.getNowSeconds());
            resourcesDTO.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
            resourcesDTO.setGetWay(0);
            mqSenderService.asyncHandleResources(resourcesDTO);
        }
    }

    public void removeCoupon(String aid, int couponId) {
        UserRechargeCouponData couponData = userRechargeCouponDao.selectOne(aid, couponId, 0);
        if (couponData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(aid);
        resourcesDTO.setResId(String.valueOf(couponId));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_RECHARGE_COUPON);
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_DELETE);
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(0);
        resourcesDTO.setDesc("remove recharge coupon");
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setGetWay(1);
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.error("remove recharge coupon failure. aid={} couponId={} code={} msg={}", aid, couponId, result.getCode().getCode(), result.getCode().getMsg());
            throw new CommonH5Exception(new HttpCode(1, "删除失败"));
        }
    }

    public CouponOptionListVO optionList() {
        return new CouponOptionListVO(userRechargeCouponDao.selectSourceList());
    }

    private Integer getLeftValidDay(UserRechargeCouponData data, RechargeCouponData couponData) {
        int nowTime = DateHelper.getNowSeconds();
        if (couponData.getType() == 0) {
            return nowTime >= data.getEndTime() ? 0 : (data.getEndTime() - nowTime) / (int)TimeUnit.DAYS.toSeconds(1);
        } else {
            if (nowTime >= couponData.getStartTime()) {
                return nowTime >= couponData.getEndTime() ? 0 : (couponData.getEndTime() - nowTime) / (int)TimeUnit.DAYS.toSeconds(1);
            } else {
                return (couponData.getEndTime() - couponData.getStartTime()) / (int)TimeUnit.DAYS.toSeconds(1);
            }
        }
    }

    private String getStrValidDay(RechargeCouponData couponData) {
        if (couponData.getType() == 0) {
            return couponData.getValidDay() + "天";
        }
        return getStrDate(couponData.getStartTime()) + "~" + getStrDate(couponData.getEndTime());
    }

    private String getStrDate(int time) {
        return DateHelper.ARABIAN.formatDateInDay(new Date(time * 1000L)).replace("-", "/");
    }

    private String getStrRechargeNum(Integer rechargeDiamonds, BigDecimal extraProp) {
        return rechargeDiamonds + "+" + BigDecimal.valueOf(rechargeDiamonds).multiply(extraProp).setScale(0, BigDecimal.ROUND_HALF_DOWN);
    }

    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        IPage<RechargeCouponData> sourceList = rechargeCouponDao.selectPage(0, condition.getSearch(), condition.getStatus(), start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for(RechargeCouponData data: sourceList.getRecords()){
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getId());
            vo.setResourceIcon("");
            vo.setResourceNameEn(data.getTitle());
            vo.setResourceNameAr(data.getTitleAr());
            vo.setResourcePrice(0);
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(sourceList.getTotal());
        return pageVO;
    }
}
