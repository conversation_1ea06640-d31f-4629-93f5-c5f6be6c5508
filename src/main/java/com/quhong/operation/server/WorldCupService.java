package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.dao.WorldCupMatchDao;
import com.quhong.mysql.dao.WorldCupTeamDao;
import com.quhong.mysql.data.WorldCupMatchData;
import com.quhong.mysql.data.WorldCupTeamData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class WorldCupService {
    private static final Logger logger = LoggerFactory.getLogger(WorldCupService.class);
    private static final List<Integer> WIN_TYPE_LIST = Arrays.asList(0, 1, 2, -1);


    @Resource
    private WorldCupTeamDao teamDao;

    @Resource
    private WorldCupMatchDao matchDao;


    public PageResultVO<WorldCupTeamVO> teamPageList(BaseCondition condition){
        PageResultVO<WorldCupTeamVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        IPage<WorldCupTeamData> pageData = teamDao.selectPageList(search, page, pageSize);

        List<WorldCupTeamVO> voList = new ArrayList<>();
        for(WorldCupTeamData data: pageData.getRecords()){
            WorldCupTeamVO teamVO = new WorldCupTeamVO();
            BeanUtils.copyProperties(data, teamVO);
            voList.add(teamVO);
        }
        pageVO.setList(voList);
        pageVO.setTotal(pageData.getTotal());
        return pageVO;
    }


    public int addTeamData(WorldCupTeamVO dto){

        WorldCupTeamData teamData = new WorldCupTeamData();
        teamData.setTeamIcon(dto.getTeamIcon());
        teamData.setTeamName(dto.getTeamName());
        teamData.setTeamNameAr(dto.getTeamNameAr());

        int curTime = DateHelper.getNowSeconds();
        teamData.setCtime(curTime);
        teamData.setMtime(curTime);
        return teamDao.insertOne(teamData);

    }

    public int updateTeamData(WorldCupTeamVO dto) {

        WorldCupTeamData teamData = teamDao.selectOne(dto.getId());
        if(teamData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        teamData.setTeamIcon(dto.getTeamIcon());
        teamData.setTeamName(dto.getTeamName());
        teamData.setTeamNameAr(dto.getTeamNameAr());
        teamData.setShowStatus(dto.getShowStatus());
        teamData.setMtime(DateHelper.getNowSeconds());

        WorldCupTeamData winnerData = teamDao.hadWinner();
        if(winnerData == null){
            teamData.setWinner(dto.getWinner());
        }else {
            if (dto.getWinner() == 1 && winnerData.getId().equals(teamData.getId())) {
                teamData.setWinner(dto.getWinner());
            } else if (dto.getWinner() == 0 && !winnerData.getId().equals(teamData.getId())) {
                teamData.setWinner(dto.getWinner());
            }else {
                throw new CommonH5Exception(HttpCode.GAME_OVER);
            }
        }
        
        return teamDao.updateOne(teamData);
    }



    public PageResultVO<WorldCupMatchVO> matchList(WorldCupMatchVO dto){
        PageResultVO<WorldCupMatchVO> pageVO = new PageResultVO<>();
        if ( dto.getShowStatus() == null ){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }


        List<WorldCupMatchData> pageData = matchDao.selectOperationList(dto.getShowStatus());

        Map<Integer, WorldCupTeamData> teamMap = teamDao.getTeamFromCache().stream().collect(Collectors.toMap(WorldCupTeamData::getId, Function.identity()));;


        List<WorldCupMatchVO> voList = new ArrayList<>();
        for(WorldCupMatchData data: pageData){
            WorldCupMatchVO matchVO = new WorldCupMatchVO();
            BeanUtils.copyProperties(data, matchVO);
            WorldCupTeamData firstTeam = teamMap.get(data.getFirstKey());
            WorldCupTeamData secondTeam = teamMap.get(data.getSecondKey());
            matchVO.setFirstTeam(firstTeam.getTeamName());
            matchVO.setFirstTeamAr(firstTeam.getTeamNameAr());
            matchVO.setFirstTeamIcon(firstTeam.getTeamIcon());
            matchVO.setSecondTeam(secondTeam.getTeamName());
            matchVO.setSecondTeamAr(secondTeam.getTeamNameAr());
            matchVO.setSecondTeamIcon(secondTeam.getTeamIcon());
            matchVO.setShowStatus(data.getShowStatus());
            voList.add(matchVO);
        }
        pageVO.setList(voList);
        pageVO.setTotal(voList.size());
        return pageVO;
    }


    public int addMatchData(WorldCupMatchVO dto){

        WorldCupMatchData matchData = new WorldCupMatchData();
        Integer firstKey = dto.getFirstKey();
        Integer secondKey = dto.getSecondKey();

        if (firstKey == null || secondKey == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }


        WorldCupTeamData teamFirst = teamDao.selectOne(dto.getFirstKey());
        WorldCupTeamData teamSecond = teamDao.selectOne(dto.getSecondKey());

        if (teamFirst == null || teamSecond == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (StringUtils.isEmpty(dto.getMatchStage()) || StringUtils.isEmpty(dto.getMatchType())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if(matchDao.selectOneByMatchType(dto.getMatchType()) != null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "存在该场次");
        }

        matchData.setMatchStage(dto.getMatchStage());
        matchData.setMatchType(dto.getMatchType());
        matchData.setFirstKey(dto.getFirstKey());
        matchData.setSecondKey(dto.getSecondKey());
        matchData.setFirstTeamScore(dto.getFirstTeamScore()!=null?dto.getFirstTeamScore():0);
        matchData.setSecondTeamScore(dto.getSecondTeamScore()!=null?dto.getSecondTeamScore():0);
        matchData.setShowStatus(dto.getShowStatus());
        matchData.setStatus(0);
        matchData.setStartTime(dto.getStartTime());
        matchData.setEndTime(dto.getEndTime());
        matchData.setWinType(0);

        int curTime = DateHelper.getNowSeconds();
        matchData.setCtime(curTime);
        matchData.setMtime(curTime);
        return matchDao.insertOne(matchData);
    }


    public int updateMatchData(WorldCupMatchVO dto) {

        WorldCupMatchData matchData = matchDao.selectOne(dto.getId());
        if(matchData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        Integer firstKey = dto.getFirstKey();
        Integer secondKey = dto.getSecondKey();
        if (firstKey == null || secondKey == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        WorldCupTeamData teamFirst = teamDao.selectOne(dto.getFirstKey());
        WorldCupTeamData teamSecond = teamDao.selectOne(dto.getSecondKey());

        if (teamFirst == null || teamSecond == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (StringUtils.isEmpty(dto.getMatchStage()) || StringUtils.isEmpty(dto.getMatchType())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        matchData.setFirstTeamScore(dto.getFirstTeamScore()!=null?dto.getFirstTeamScore():0);
        matchData.setSecondTeamScore(dto.getSecondTeamScore()!=null?dto.getSecondTeamScore():0);
        matchData.setFirstKey(dto.getFirstKey());
        matchData.setSecondKey(dto.getSecondKey());
        matchData.setMatchType(dto.getMatchType());
        matchData.setMatchStage(dto.getMatchStage());
        matchData.setStartTime(dto.getStartTime());
        matchData.setEndTime(dto.getEndTime());
        matchData.setMtime(DateHelper.getNowSeconds());
        matchData.setShowStatus(dto.getShowStatus());

        Integer winType = dto.getWinType();
        if(WIN_TYPE_LIST.contains(winType)){
            matchData.setWinType(winType);
        }
        return matchDao.updateOne(matchData);
    }


    public PageResultVO<WorldCupTeamVO> championList(){
        PageResultVO<WorldCupTeamVO> pageVO = new PageResultVO<>();
        List<WorldCupTeamData> pageData = teamDao.selectChampionList();

        List<WorldCupTeamVO> voList = new ArrayList<>();
        for(WorldCupTeamData data: pageData){
            WorldCupTeamVO teamVO = new WorldCupTeamVO();
            BeanUtils.copyProperties(data, teamVO);
            voList.add(teamVO);
        }
        pageVO.setList(voList);
        pageVO.setTotal(pageData.size());
        return pageVO;
    }


}
