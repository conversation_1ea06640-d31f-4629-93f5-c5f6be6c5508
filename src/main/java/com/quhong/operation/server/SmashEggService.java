package com.quhong.operation.server;

import com.quhong.constant.ResourceConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mysql.dao.SmashEggConfigDao;
import com.quhong.mysql.data.SmashEggConfigData;
import com.quhong.operation.share.condition.SmashEggCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class SmashEggService {
    private static final Logger logger = LoggerFactory.getLogger(SmashEggService.class);
    private static final Set<String> SUPPORT_RESOURCE = new HashSet<>(Arrays.asList("mic", "buddle", "ride", "ripple", "diamond", "badge", "gift", "float_screen", "thanks"));


    @Resource
    private SmashEggConfigDao smashEggConfigDao;



    public PageResultVO<SmashEggConfigData> getDataList(SmashEggCondition condition){
        PageResultVO<SmashEggConfigData> pageVO = new PageResultVO<>();
        Integer status = condition.getStatus();
        Integer luckyGood = condition.getLuckyGood();

        List<SmashEggConfigData> dataList = smashEggConfigDao.selectList(status, luckyGood);
        pageVO.setList(dataList);
        pageVO.setTotal(dataList.size());
        return pageVO;
    }


    public void addData(SmashEggConfigData dto){

        if(StringUtils.isEmpty(dto.getIcon()) || StringUtils.isEmpty(dto.getPageAwardUrl())){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }


        if (!SUPPORT_RESOURCE.contains(dto.getRewardType())){
            logger.info("rewardType not support {}", dto.getRewardType());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if (ResourceConstant.DIAMOND.equals(dto.getRewardType())) {
            if (null == dto.getRewardNum()) {
                logger.info("钻石数量不能为空 {}", dto.getRewardType());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
        } else if (ResourceConstant.THANKS.equals(dto.getRewardType())) {
            logger.info("无中奖上传");

        } else {
            if (null == dto.getSourceId()) {
                logger.info("资源id不能为空 {}", dto.getRewardType());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            if (null == dto.getRewardTime()) {
                logger.info("资源时长不能为空 {}", dto.getRewardType());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
        }

        List<SmashEggConfigData> smashEggConfigDataList = smashEggConfigDao.selectList(-1, -1);
        int maxIndex = smashEggConfigDataList.stream().filter(obj -> obj.getWinType().matches("^[1-9]\\d*$")).mapToInt(obj -> Integer.parseInt(obj.getWinType())).max().getAsInt();
        String nextWinType = String.valueOf(maxIndex + 1);
        SmashEggConfigData data = new SmashEggConfigData();
        BeanUtils.copyProperties(dto, data);
        data.setWinType(nextWinType);
        smashEggConfigDao.insertOne(data);

    }

    public void updateData(SmashEggConfigData dto) {

        SmashEggConfigData data = smashEggConfigDao.selectOne(dto.getId());
        if(data == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if (!SUPPORT_RESOURCE.contains(dto.getRewardType())){
            logger.info("rewardType not support {}", dto.getRewardType());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if (ResourceConstant.DIAMOND.equals(dto.getRewardType())) {
            if (null == dto.getRewardNum()) {
                logger.info("钻石数量不能为空 {}", dto.getRewardType());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
        } else if (ResourceConstant.THANKS.equals(dto.getRewardType())) {
            logger.info("无中奖上传");

        } else {
            if (null == dto.getSourceId()) {
                logger.info("资源id不能为空 {}", dto.getRewardType());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            if (null == dto.getRewardTime()) {
                logger.info("资源时长不能为空 {}", dto.getRewardType());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
        }

        BeanUtils.copyProperties(dto, data);
        smashEggConfigDao.updateOne(data);
    }

}
