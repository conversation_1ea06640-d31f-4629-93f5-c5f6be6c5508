package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.MomentDeleteEvent;
import com.quhong.analysis.TopicBackendReviewEvent;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.MomentTopicSetTopData;
import com.quhong.dto.InnerMomentTopicUpdateDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.MomentCategoryInterestDao;
import com.quhong.mysql.dao.MomentTopicCategoryDao;
import com.quhong.mysql.dao.MomentTopicDao;
import com.quhong.mysql.dao.UserLabelDao;
import com.quhong.mysql.data.LabelConfigData;
import com.quhong.mysql.data.MomentCategoryInterestData;
import com.quhong.mysql.data.MomentTopicCategoryData;
import com.quhong.mysql.data.MomentTopicData;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.MomentTopicCondition;
import com.quhong.operation.share.dto.MomentTopicCategoryLabelDTO;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.MomentTopicCategoryLabelVO;
import com.quhong.operation.share.vo.MomentTopicVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.MomentTopicSetTopRedis;
import com.quhong.service.HomeBannerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class MomentTopicService {
    private static final Logger logger = LoggerFactory.getLogger(MomentTopicService.class);
    private static final Pattern pattern = Pattern.compile("^\\d+(\\.\\d+)?$"); // 匹配正整数和正浮点数包含0

    @Resource
    private MomentTopicCategoryDao momentTopicCategoryDao;
    @Resource
    private MomentTopicDao momentTopicDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private UserLabelDao userLabelDao;
    @Resource
    private MomentCategoryInterestDao momentCategoryInterestDao;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private MomentTopicSetTopRedis momentTopicSetTopRedis;

    /**
     * 话题分类管理
     */
    public PageResultVO<MomentTopicCategoryData> categoryList(BaseCondition condition) {
        PageResultVO<MomentTopicCategoryData> pageVO = new PageResultVO<>();
        IPage<MomentTopicCategoryData> pageData = momentTopicCategoryDao.selectPageList(condition.getSearch(), condition.getStatus(), condition.getPage(), condition.getPageSize());
        pageVO.setList(pageData.getRecords());
        pageVO.setTotal(pageData.getTotal());
        return pageVO;
    }

    public void categoryAdd(MomentTopicCategoryData data) {
        if (StringUtils.isEmpty(data.getCategoryNameEn()) || StringUtils.isEmpty(data.getCategoryNameAr())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "分类名称为空");
        }
        data.setCtime(DateHelper.getNowSeconds());
        momentTopicCategoryDao.insert(data);
    }

    public void categoryUpdate(MomentTopicCategoryData dto) {

        MomentTopicCategoryData data = momentTopicCategoryDao.selectById(dto.getId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (StringUtils.isEmpty(data.getCategoryNameEn()) || StringUtils.isEmpty(data.getCategoryNameAr())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "分类名称为空");
        }
        BeanUtils.copyProperties(dto, data);
        momentTopicCategoryDao.update(data);
    }


    public PageResultVO<MomentTopicCategoryLabelVO> categoryLabelList(int id, String search) {
        MomentTopicCategoryData data = momentTopicCategoryDao.selectById(id);
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        List<LabelConfigData> allLabelConfigList = userLabelDao.getAllLabelConfig();
        if (!StringUtils.isEmpty(search)) {
            allLabelConfigList = allLabelConfigList.stream().filter(item -> item.getLabelName().contains(search)).collect(Collectors.toList());
        }

        List<Integer> categoryInterestIdList = momentCategoryInterestDao.selectCategoryInterestList(id).stream().map(MomentCategoryInterestData::getInterestId).collect(Collectors.toList());
        List<MomentTopicCategoryLabelVO> momentTopicCategoryLabelVOList = new ArrayList<>();
        for (LabelConfigData item : allLabelConfigList) {
            MomentTopicCategoryLabelVO momentTopicCategoryLabelVO = new MomentTopicCategoryLabelVO();
            BeanUtils.copyProperties(item, momentTopicCategoryLabelVO);
            momentTopicCategoryLabelVO.setSelect(categoryInterestIdList.contains(item.getLabelId()) ? 1 : 0);
            momentTopicCategoryLabelVOList.add(momentTopicCategoryLabelVO);
        }
        PageResultVO<MomentTopicCategoryLabelVO> pageVO = new PageResultVO<>();
        pageVO.setList(momentTopicCategoryLabelVOList);
        return pageVO;
    }

    public void categoryLabelSet(MomentTopicCategoryLabelDTO dto) {
        int categoryId = dto.getCategoryId();
        int labelId = dto.getLabelId();
        MomentTopicCategoryData data = momentTopicCategoryDao.selectById(categoryId);
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        List<Integer> allLabelConfigIdList = userLabelDao.getAllLabelConfig().stream().map(LabelConfigData::getLabelId).collect(Collectors.toList());
        if (!allLabelConfigIdList.contains(labelId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        int option = dto.getOption();
        if (option <= 0) {
            MomentCategoryInterestData momentCategoryInterestData = momentCategoryInterestDao.selectByCategoryAndLabel(categoryId, labelId);
            if (momentCategoryInterestData == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "不存在该关系");
            }
            momentCategoryInterestDao.deleteById(momentCategoryInterestData.getId());
        } else {
            MomentCategoryInterestData momentCategoryInterestData = momentCategoryInterestDao.selectByCategoryAndLabel(categoryId, labelId);
            if (momentCategoryInterestData != null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "已存在该关系");
            }

            momentCategoryInterestData = new MomentCategoryInterestData();
            momentCategoryInterestData.setCategoryId(categoryId);
            momentCategoryInterestData.setInterestId(labelId);
            momentCategoryInterestData.setCtime(DateHelper.getNowSeconds());
            momentCategoryInterestDao.insert(momentCategoryInterestData);
        }

    }

    private Map<Integer, MomentTopicSetTopData> deleteInvalidData(Map<Integer, MomentTopicSetTopData> allTopicTopMap) {
        int nowTime = DateHelper.getNowSeconds();
        boolean isDel = false;
        for (MomentTopicSetTopData topData : allTopicTopMap.values()) {
            if (nowTime > topData.getEndTime()) {
                momentTopicSetTopRedis.delSetTopHashData(String.valueOf(topData.getTopicId()));
                isDel = true;
            }
        }
        if (isDel) {
            return momentTopicSetTopRedis.getSetTopHashAll();
        } else {
            return allTopicTopMap;
        }
    }

    /**
     * 话题管理
     */
    public PageResultVO<MomentTopicVO> momentTopicList(MomentTopicCondition condition) {
        PageResultVO<MomentTopicVO> pageVO = new PageResultVO<>();
        String searchUid = null;
        Map<Integer, MomentTopicSetTopData> allTopicTopMap = momentTopicSetTopRedis.getSetTopHashAll();
        allTopicTopMap = deleteInvalidData(allTopicTopMap);

        List<MomentTopicVO> momentTopicVOList = new ArrayList<>();
        if ("-1".equals(condition.getSearchId())) {
            List<MomentTopicSetTopData> topicTopList = new ArrayList<>(allTopicTopMap.values());
            topicTopList.sort(MomentTopicSetTopRedis.SORT_NUM_DESC);
            Map<Integer, MomentTopicCategoryData> categoryDataMap = momentTopicCategoryDao.selectAllData().stream().collect(Collectors.toMap(MomentTopicCategoryData::getId, Function.identity()));
            for (MomentTopicSetTopData setTopData : topicTopList) {
                MomentTopicVO momentTopicVO = new MomentTopicVO();
                MomentTopicData data = momentTopicDao.selectById(setTopData.getTopicId());
                BeanUtils.copyProperties(data, momentTopicVO);

                momentTopicVO.setHead(ImageUrlGenerator.generateNormalUrl(data.getHead()));
                ActorData owner = actorDao.getActorDataFromCache(data.getOwnerUid());
                momentTopicVO.setOwnerId(owner.getStrRid());
                momentTopicVO.setCategoryNameEn(categoryDataMap.get(data.getCategoryId()).getCategoryNameEn());

                if(setTopData.getFilterType() == HomeBannerService.FILTER_TYPE_USER && !StringUtils.isEmpty(setTopData.getFilterItem())){
                    String[] uidList = setTopData.getFilterItem().split(",");
                    List<String> ridList = new ArrayList<>();
                    for (String uidStr : uidList) {
                        ActorData actorData = actorDao.getActorDataFromCache(uidStr);
                        ridList.add(actorData.getStrRid());
                    }
                    setTopData.setFilterItem(String.join(",", ridList));
                }
                momentTopicVO.setSetTopData(setTopData);
                momentTopicVOList.add(momentTopicVO);
            }
            pageVO.setTotal(topicTopList.size());
        } else {
            if (!StringUtils.isEmpty(condition.getSearchId())) {
                ActorData actorData = actorDao.getActorByStrRid(condition.getSearchId());

                if (actorData == null) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "搜索用户不存在");
                }
                searchUid = actorData.getUid();
            }

            IPage<MomentTopicData> pageData = momentTopicDao.selectPageList(condition.getSearch(), condition.getCategoryId(), searchUid, condition.getStatus(),
                    condition.getStartTime(), condition.getEndTime(), condition.getPage(), condition.getPageSize());

            Map<Integer, MomentTopicCategoryData> categoryDataMap = momentTopicCategoryDao.selectAllData().stream().collect(Collectors.toMap(MomentTopicCategoryData::getId, Function.identity()));

            for (MomentTopicData data : pageData.getRecords()) {
                MomentTopicVO momentTopicVO = new MomentTopicVO();
                BeanUtils.copyProperties(data, momentTopicVO);
                momentTopicVO.setHead(ImageUrlGenerator.generateNormalUrl(data.getHead()));
                ActorData owner = actorDao.getActorDataFromCache(data.getOwnerUid());
                momentTopicVO.setOwnerId(owner.getStrRid());
                momentTopicVO.setCategoryNameEn(categoryDataMap.get(data.getCategoryId()).getCategoryNameEn());

                MomentTopicSetTopData setTopData = allTopicTopMap.get(momentTopicVO.getId());
                if(setTopData!=null&&setTopData.getFilterType() == HomeBannerService.FILTER_TYPE_USER && !StringUtils.isEmpty(setTopData.getFilterItem())){
                    String[] uidList = setTopData.getFilterItem().split(",");
                    List<String> ridList = new ArrayList<>();
                    for (String uidStr : uidList) {
                        ActorData actorData = actorDao.getActorDataFromCache(uidStr);
                        ridList.add(actorData.getStrRid());
                    }
                    setTopData.setFilterItem(String.join(",", ridList));
                }
                momentTopicVO.setSetTopData(setTopData);
                momentTopicVOList.add(momentTopicVO);
            }
            pageVO.setTotal(pageData.getTotal());
        }
        pageVO.setList(momentTopicVOList);

        return pageVO;
    }

    private void momentTopicCheck(MomentTopicVO data) {
        if (StringUtils.isEmpty(data.getName())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "话题名称不能为空");
        }

        if (StringUtils.isEmpty(data.getHead())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "头像不能为空");
        }

        if (StringUtils.isEmpty(data.getAnnounce())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "公告不能为空");
        }

        if (data.getCategoryId() == null || data.getCategoryId() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未选择分类");
        }

        if (StringUtils.isEmpty(data.getOwnerId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "所属用户id为空");
        }
    }

    public void momentTopicAdd(MomentTopicVO data) {
        momentTopicCheck(data);
        ActorData actorData = actorDao.getActorByStrRid(data.getOwnerId());
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "所属用户不存在");
        }

        String ownerUid = actorData.getUid();
        int num = MomentTopicDao.WHITE_MULTI_UER.getOrDefault(ownerUid, 1);
        List<MomentTopicData> myMomentTopicDataList = momentTopicDao.selectListByOwnerUid(ownerUid);
        if (!CollectionUtils.isEmpty(myMomentTopicDataList) && myMomentTopicDataList.size() >= num) {
            logger.info("ownerUid:{} have topic max can not handle change owner size:{} num:{}", ownerUid, myMomentTopicDataList.size(), num);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "该用户已存在已通过或待审核的话题, 不能再新增");
        }

        InnerMomentTopicUpdateDTO bo = new InnerMomentTopicUpdateDTO();
        bo.setName(data.getName());
        bo.setHead(data.getHead());
        bo.setAnnounce(data.getAnnounce());
        bo.setCategoryId(data.getCategoryId());
        bo.setToStatus(1);
        bo.setAuditReview("");
        bo.setUid(ownerUid);
        ApiResult<Object> apiResult = iMomentService.createMomentTopic(bo);
        if (apiResult.isError()) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), apiResult.getCode().getMsg());
        }
        MomentTopicSetTopData setTopData = data.getSetTopData();
        if (setTopData != null) {
            if (setTopData.getStartTime() != null && setTopData.getEndTime() != null & setTopData.getSortNum() != null) {
                MomentTopicData nameMomentTopicData = momentTopicDao.selectByName(data.getName());
                setTopData.setTopicId(nameMomentTopicData.getId());
                checkFilterType(setTopData);
                momentTopicSetTopRedis.setSetTopHashData(nameMomentTopicData.getId(), data.getSetTopData());
            }
        }
    }

    public void momentTopicUpdate(String uid, MomentTopicVO data) {
        momentTopicCheck(data);
        int topicId = data.getId();
        MomentTopicData momentTopicData = momentTopicDao.selectById(topicId);
        if (momentTopicData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "不存在该话题");
        }

        if (momentTopicData.getStatus() >= 2) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "已审核不通过的话题, 不能再操作");
        }


        ActorData actorData = actorDao.getActorByStrRid(data.getOwnerId());
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "所属用户不存在");
        }

        InnerMomentTopicUpdateDTO bo = new InnerMomentTopicUpdateDTO();
        String ownerUid = actorData.getUid();
        String originOwnerUid = momentTopicData.getOwnerUid();
        if (!originOwnerUid.equals(ownerUid)) {

            int num = MomentTopicDao.WHITE_MULTI_UER.getOrDefault(ownerUid, 1);
            List<MomentTopicData> myMomentTopicDataList = momentTopicDao.selectListByOwnerUid(ownerUid);
            if (!CollectionUtils.isEmpty(myMomentTopicDataList) && myMomentTopicDataList.size() >= num) {
                logger.info("ownerUid:{} have topic max can not handle change owner topicId:{} size:{} num:{}", ownerUid, topicId, myMomentTopicDataList.size(), num);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "该用户已存在已通过或待审核的话题, 不能再转属");
            }
            bo.setAid(ownerUid);
        }

        bo.setTopicRid(momentTopicData.getRid());
        bo.setName(data.getName());
        bo.setHead(data.getHead());
        bo.setAnnounce(data.getAnnounce());
        bo.setCategoryId(data.getCategoryId());
        bo.setToStatus(data.getStatus());
        bo.setAuditReview(data.getAuditReview());
        bo.setUid(originOwnerUid);
        ApiResult<Object> apiResult = iMomentService.updateMomentTopic(bo);
        if (apiResult.isError()) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), apiResult.getCode().getMsg());
        }

        MomentTopicSetTopData setTopData = data.getSetTopData();
        if (setTopData != null) {
            if (setTopData.getStartTime() != null && setTopData.getEndTime() != null & setTopData.getSortNum() != null) {
                setTopData.setTopicId(topicId);
                checkFilterType(setTopData);
                momentTopicSetTopRedis.setSetTopHashData(topicId, setTopData);
            }
        }

        Manager manager = managerDao.getDataByUid(uid);
        TopicBackendReviewEvent event = new TopicBackendReviewEvent();
        event.setUid(uid);
        event.setOperator(manager.getAccount());
        event.setCtime(DateHelper.getNowSeconds());
        event.setOperate_type(String.valueOf(data.getStatus()));
        event.setTopic_name(data.getName());
        event.setTopic_id(String.valueOf(data.getId()));
        event.setTopic_create_uid(data.getOwnerUid());
        eventReport.track(new EventDTO(event));

    }

    public void momentTopicTopDelete(String uid, MomentTopicVO data) {
        if (data.getId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "话题id不能为空");
        }
        int topicId = data.getId();
        momentTopicSetTopRedis.delSetTopHashData(String.valueOf(topicId));
    }

    private void checkFilterType(MomentTopicSetTopData setTopData) {
        if(setTopData.getFilterType() == HomeBannerService.FILTER_TYPE_USER){
            String[] ridList = setTopData.getFilterItem().split(",");
            List<String> uidList = new ArrayList<>();
            for (String ridStr : ridList) {
                ActorData actorData = actorDao.getActorByStrRid(ridStr);
                if(actorData == null){
                    logger.info("rid:{} not exist", ridStr);
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                }
                uidList.add(actorData.getUid());
            }
            setTopData.setFilterItem(String.join(",", uidList));
        } else if (setTopData.getFilterType() == HomeBannerService.FILTER_TYPE_REGISTER) {
            String[] splitDay = setTopData.getFilterItem().split("-");
            int startDay = Integer.parseInt(splitDay[0]);
            int endDay = Integer.parseInt(splitDay[1]);
        }else if (setTopData.getFilterType() != 0 &&StringUtils.isEmpty( setTopData.getFilterItem())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "过滤条件不能为空");
        } else if (setTopData.getFilterType() == 0) {
            setTopData.setFilterItem(null);
        }
    }
}
