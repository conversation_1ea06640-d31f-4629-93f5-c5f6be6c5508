package com.quhong.operation.server;

import com.quhong.mongo.dao.BadgeListDao;
import com.quhong.mongo.data.BadgeListData;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class BadgeSourceService implements ResourceService {
    private static final Logger logger = LoggerFactory.getLogger(BadgeSourceService.class);

    @Resource
    private BadgeListDao badgeListDao;

    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<BadgeListData>  badgeSourceList = badgeListDao.selectBadgeListDataPage(condition.getStatus(), search, start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for(BadgeListData data: badgeSourceList){
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getBadge_id());
            vo.setResourceIcon(data.getIcon());
            vo.setResourceNameEn(data.getName());
            vo.setResourceNameAr(data.getAr_name());
            vo.setResourceType(condition.getResourceType());
            vo.setResourcePrice(0);
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(badgeListDao.selectCount(condition.getStatus(), search));
        return pageVO;
    }
}
