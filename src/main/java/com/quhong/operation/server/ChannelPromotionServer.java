package com.quhong.operation.server;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.datas.DayTimeData;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.config.money.MoneyRateConfig;
import com.quhong.operation.constant.DelConstants;
import com.quhong.operation.constant.MoneyDetailATypeConsts;
import com.quhong.operation.dao.*;
import com.quhong.operation.share.dto.*;
import com.quhong.operation.share.el.MoneyDetailES;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.mysql.ChannelPromotionData;
import com.quhong.operation.share.mysql.ChannelSourceData;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.MathUtils;
import com.quhong.operation.utils.MongoUtils;
import com.quhong.operation.utils.ValidateUtils;
import com.quhong.utils.UrlEncodeHelper;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

@Component
public class ChannelPromotionServer {

    private static final Logger logger = LoggerFactory.getLogger(ChannelPromotionServer.class);

    @Autowired
    private ChannelPromotionDao channelPromotionDao;

    @Autowired
    private ChannelSourceDao channelSourceDao;

    @Autowired
    private ManagerDao managerDao;

    @Autowired
    private WebClient webClient;

    @Autowired
    private DauStatDao dauStatDao;

    @Autowired
    private ActorStatDao actorStatDao;

    @Autowired
    private MoneyDetailStatDao moneyDetailStatDao;

    @Autowired
    private InnerSandboxRedis innerSandboxRedis;

    @Autowired
    private MoneyRateConfig moneyRateConfig;

    private static final String GOOGLE_BASE_URL = "https://youstar.page.link?";

    private static final String OFFICAL_URL = "https://www.youstar.in/";

    private static final String CONTENT_PATH = "campaign";

    @Autowired
    private OperationActorDao actorDao;

    /**
     * 获取渠道来源list
     *
     * @return
     */
    public ApiResult<List<ChannelSourceVO>> listSource() {
        ApiResult<List<ChannelSourceVO>> result = new ApiResult<>();
        List<ChannelSourceVO> voList = new ArrayList<>();
        List<ChannelSourceData> channelSourceList = channelSourceDao.listAll();
        for (ChannelSourceData channelSourceData : channelSourceList) {
            ChannelSourceVO channelSourceVO = new ChannelSourceVO();
            BeanUtils.copyProperties(channelSourceData, channelSourceVO);
            voList.add(channelSourceVO);
        }
        return result.ok(voList);
    }

    /**
     * 获取channelId数组
     *
     * @return
     */
    public ApiResult<List<ChannelIdVO>> listChannelId() {
        ApiResult<List<ChannelIdVO>> result = new ApiResult<>();
        List<ChannelIdVO> list = new ArrayList<>();
        List<ChannelPromotionData> channelPromotionList = channelPromotionDao.getAll();
        for (ChannelPromotionData channelPromotionData : channelPromotionList) {
            ChannelIdVO channelIdVO = new ChannelIdVO();
            channelIdVO.setChannelName(channelPromotionData.getChannelName());
            Integer sourceId = channelPromotionData.getSourceId();
            String channelId = sourceId + "";
            if (!StringUtils.isEmpty(channelPromotionData.getChannelId())) {
                channelId = channelId + "_" + channelPromotionData.getChannelId();
            }
            channelIdVO.setChannelId(channelId);
            channelIdVO.setId(channelPromotionData.getId());
            list.add(channelIdVO);
        }
        return result.ok(list);
    }

    /**
     * 获取渠道列表
     *
     * @param listDTO
     * @return
     */
    public ApiResult<PageResultVO<ChannelPromotionVO>> listChannel(ChannelPromotionListDTO listDTO) {
        ApiResult<PageResultVO<ChannelPromotionVO>> result = new ApiResult<>();
        PageResultVO<ChannelPromotionVO> pageResultVO = new PageResultVO<>();
        //请求参数处理
        String opUser = listDTO.getOpUser();
        if (opUser.equals("全部")) {
            opUser = null;
        }
        Integer status = listDTO.getStatus();
        String end = listDTO.getEnd();
        String start = listDTO.getStart();
        Integer page = listDTO.getPage();
        List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
        int startTime = dayTimeList.get(0).getTime();
        int endTime = dayTimeList.get(dayTimeList.size() - 1).getEndTime();
        //分页查询渠道
        List<ChannelPromotionData> channelPromotionList = channelPromotionDao.listPageData(startTime, endTime, opUser, status, page, 20);
        pageResultVO.setTotal(channelPromotionDao.count(startTime, endTime, opUser, status));
        //数据拼装
        List<ChannelPromotionVO> voList = new ArrayList<>();
        for (ChannelPromotionData channelPromotionData : channelPromotionList) {
            ChannelPromotionVO channelPromotionVO = new ChannelPromotionVO();
            BeanUtils.copyProperties(channelPromotionData, channelPromotionVO);
            String shortUrl = channelPromotionData.getShortUrl();
            channelPromotionVO.setShortUrl(shortUrl);
            Integer ctime = channelPromotionData.getCtime();
            Integer dataStatus = channelPromotionData.getStatus();
            channelPromotionVO.setDate(DateHelper.BEIJING.formatDateTime(new Date(ctime * 1000L)));
            switch (dataStatus) {
                case ChannelPromotionData.STATUS_ACTIVE:
                    channelPromotionVO.setStatus("有效");
                    break;
                case ChannelPromotionData.STATUS_STOP:
                    channelPromotionVO.setStatus("无效");
                    break;
                default:
                    channelPromotionVO.setStatus("");
                    break;
            }
            channelPromotionVO.setChannelId(channelPromotionData.getId());
            voList.add(channelPromotionVO);
        }
        pageResultVO.setList(voList);
        return result.ok(pageResultVO);
    }

    /**
     * 新增渠道
     *
     * @param addDTO
     * @return
     */
    public ApiResult<String> addChannel(ChannelPromotionAddDTO addDTO, String opUid) throws UnsupportedEncodingException {
        ApiResult<String> result = new ApiResult<>();
        String channelName = addDTO.getChannelName();
        Integer rid = addDTO.getRid();
        Integer sourceId = addDTO.getSourceId();
        Integer status = addDTO.getStatus();
        int ctime = (int) (new Date().getTime() / 1000);
        //判断这个名字是否存在过
        ChannelPromotionData validData = channelPromotionDao.getDateByName(channelName);
        if (validData != null) {
            logger.error("add promotion channel error.channel is already exist. param = {} opUid = {}", addDTO.toString(), opUid);
            return result.error("channel name is already exist");
        }
        //设置数据库实体类属性
        ChannelPromotionData channelPromotionData = new ChannelPromotionData();
        channelPromotionData.setCtime(ctime);
        channelPromotionData.setMtime(ctime);
        channelPromotionData.setDel(DelConstants.OK);
        channelPromotionData.setChannelName(channelName);
        channelPromotionData.setStatus(status);
        Manager manager = managerDao.getDataByUid(opUid);
        channelPromotionData.setOpUser(manager.getAccount());
        ChannelSourceData channelSource = channelSourceDao.getDataBySourceId(sourceId);
        if (channelSource == null) {
            logger.error("channel source not exist. sourceId = {}", sourceId);
            return result.error("channel source not exist");
        }
        channelPromotionData.setSourceId(sourceId);
        channelPromotionData.setSourceName(channelSource.getSourceName());
        ApiResult<String> apiResult = configChannelLink(channelPromotionData, channelSource, rid);
        if (!apiResult.isOK()) {
            return result.error(apiResult.getMsg());
        }
        channelPromotionDao.saveOne(channelPromotionData);
        logger.info("add promotion channel success. param = {} opUid = {}", addDTO.toString(), opUid);
        return result.ok();
    }

    /**
     * 更新渠道
     *
     * @param updateDTO
     * @return
     */
    public ApiResult<String> updateChannel(ChannelPromotionUpdateDTO updateDTO, String opUid) throws UnsupportedEncodingException {
        ApiResult<String> result = new ApiResult<>();
        Integer channelId = updateDTO.getChannelId();
        String channelName = updateDTO.getChannelName();
        Integer rid = updateDTO.getRid();
        Integer sourceId = updateDTO.getSourceId();
        Integer status = updateDTO.getStatus();
        int mtime = (int) (new Date().getTime() / 1000);
        ChannelPromotionData channelPromotionData = new ChannelPromotionData();
        channelPromotionData.setMtime(mtime);
        channelPromotionData.setId(channelId);
        if (!StringUtils.isEmpty(channelName)) {
            ChannelPromotionData validData = channelPromotionDao.getDateByName(channelName);
            if (validData != null) {
                logger.error("update promotion channel error.channel is already exist. param = {} opUid = {}", updateDTO.toString(), opUid);
                return result.error("channel name is already exist");
            }
            channelPromotionData.setChannelName(channelName);
        }
        if (status != null) {
            channelPromotionData.setStatus(status);
        }
        Manager manager = managerDao.getDataByUid(opUid);
        channelPromotionData.setOpUser(manager.getAccount());
        ChannelSourceData channelSource = channelSourceDao.getDataBySourceId(sourceId);
        if (channelSource == null) {
            logger.error("channel source not exist. sourceId = {}", sourceId);
            return result.error("channel source not exist");
        }
        channelPromotionData.setSourceId(sourceId);
        channelPromotionData.setSourceName(channelSource.getSourceName());
        ApiResult<String> apiResult = configChannelLink(channelPromotionData, channelSource, rid);
        if (!apiResult.isOK()) {
            return result.error(apiResult.getMsg());
        }
        channelPromotionDao.updateOne(channelPromotionData);
        logger.info("update promotion channel success. param = {} opUid = {}", updateDTO.toString(), opUid);
        return result.ok();
    }

    /**
     * 删除渠道
     *
     * @param channelId
     * @param opUid
     * @return
     */
    public ApiResult<String> removeChannel(int channelId, String opUid) {
        ApiResult<String> apiResult = new ApiResult<>();
        Manager manager = managerDao.getDataByUid(opUid);
        String opUser = manager.getAccount();
        channelPromotionDao.deleteById(channelId, opUser);
        logger.info("delete promotion channel success. channelId = {} opUid = {}", channelId, opUid);
        return apiResult.ok();
    }

    /**
     * 获取推广渠道没提实时数据
     *
     * @param channelId
     * @param dayTimeData
     * @param os
     * @return
     */
    public ChannelPromotionDayVO listChannelDayData(int sourceId, String channelId, DayTimeData dayTimeData, int os) {
        if (channelId != null && channelId.equals("total")) {
            channelId = null;
        }
        ChannelPromotionDayVO channelPromotionDayVO = new ChannelPromotionDayVO();
        channelPromotionDayVO.setDate(dayTimeData.getDate());
        int dau = dauStatDao.getDauByDay(os, dayTimeData, null);
        channelPromotionDayVO.setDau(dau);
        channelPromotionDayVO.setAddUserCount(actorStatDao.listNewActor(dayTimeData, os, "sys").size());
        List<Actor> channelAddActors = actorStatDao.listNewActor(dayTimeData, os, channelId);
        //获取沙盒账号
        Set<String> sandBoxUidSet = innerSandboxRedis.getAllSandBoxUid();
        logger.info("inner sandbox all uid = {} size = {}", sandBoxUidSet, sandBoxUidSet.size());
        //获取推广新增的用户数量
        Set<String> uidSet = new HashSet<>();
        if (sourceId != -1) {
            for (Actor actor : channelAddActors) {
                String promotionId = actor.getPromotionId();
                if (StringUtils.isEmpty(channelId)) {
                    if (!StringUtils.isEmpty(promotionId) && promotionId.contains("_")) {
                        String[] splits = promotionId.split("_");
                        String sourceIdStr = splits[0];
                        if (ValidateUtils.isNumeric(sourceIdStr)) {
                            int promotionSourceId = Integer.parseInt(sourceIdStr);
                            if (promotionSourceId == sourceId) {
                                if (!sandBoxUidSet.contains(actor.get_id().toString())) {
                                    uidSet.add(actor.get_id().toString());
                                }
                            }
                        }
                    }
                } else {
                    if (!sandBoxUidSet.contains(actor.get_id().toString()) && !StringUtils.isEmpty(promotionId)) {
                        uidSet.add(actor.get_id().toString());
                    }
                }
            }
        } else {
            for (Actor actor : channelAddActors) {
                String promotionId = actor.getPromotionId();
                if (!sandBoxUidSet.contains(actor.get_id().toString()) && !StringUtils.isEmpty(promotionId)) {
                    String uid = actor.get_id().toString();
                    uidSet.add(uid);
                }
            }
        }
        channelPromotionDayVO.setChannelAddUserCount(uidSet.size());
        if (CollectionUtils.isEmpty(uidSet)) {
            uidSet.add("");
        }
        int oneMonthTime = (int) (DateHelper.ARABIAN.getMonthOffset(dayTimeData.getTime() * 1000L, 1) / 1000);
        List<MoneyDetailES> moneyDetails = moneyDetailStatDao.listByATypeAndUid(dayTimeData.getTime(), oneMonthTime, uidSet, MoneyDetailATypeConsts.PAY_CHARGE);
        Map<Integer, BigDecimal> beansDollarMap = moneyRateConfig.getBeansDollarMap();
        Set<String> moneyDetailUidSet = new HashSet<>();
        long channelPayCount = 0;
        BigDecimal channelPayDecimal = new BigDecimal(BigInteger.ZERO);
        for (MoneyDetailES moneyDetail : moneyDetails) {
            String uid = moneyDetail.getUid();
            if (!sandBoxUidSet.contains(uid)) {
                moneyDetailUidSet.add(uid);
                channelPayCount = channelPayCount + 1;
                Integer changed = moneyDetail.getChanged();
                BigDecimal changeMoney = beansDollarMap.get(changed);
                channelPayDecimal = channelPayDecimal.add(changeMoney);
            }
        }
        channelPromotionDayVO.setChannelPayUserCount(moneyDetailUidSet.size());
        channelPromotionDayVO.setChannelPayCount((int) channelPayCount);
        channelPromotionDayVO.setChannelPayMoney(MathUtils.round(channelPayDecimal) + "");
        return channelPromotionDayVO;
    }

    private ApiResult<String> configChannelLink(ChannelPromotionData channelPromotionData, ChannelSourceData channelSource, Integer rid) throws UnsupportedEncodingException {
        ApiResult<String> result = new ApiResult<>();
        Actor actor = null;
        if (rid != null) {
            ApiResult<Actor> actorApiResult = actorDao.getActorByRidOrBeforeRid(rid);
            if (!actorApiResult.isOK()) {
                logger.error("actor not exist. rid = {}", rid);
                return result.error(actorApiResult.getMsg());
            }
            actor = actorApiResult.getData();
        }
        String channelId = new ObjectId().toString();
        channelPromotionData.setChannelId(channelId);
        //生成原始链接
        String channelName = "";
        if (!StringUtils.isEmpty(channelPromotionData.getChannelName())) {
            channelName = channelPromotionData.getChannelName();
        }
        GoogleUrlDTO googleUrlDTO = new GoogleUrlDTO();
        googleUrlDTO.setApn("in.dradhanus.liveher");
        googleUrlDTO.setIbi("com.stonemobile.youstar");
        googleUrlDTO.setIsi("1377028992");
        googleUrlDTO.setOfl("https://www.youstar.live/");
        String urlParamStr = UrlEncodeHelper.createLinkByObject(googleUrlDTO);
        String url = OFFICAL_URL + CONTENT_PATH + "?ID=" + channelSource.getSourceId() + "_" + channelId + "&name=" + channelName + "&channel=" + channelSource.getSourceName() + "&" + urlParamStr;
        url = "https://youstar.page.link/?link=" + url;
        if (actor != null) {
            String roomId = "r:" + actor.get_id().toString();
            url = url + "&roomId=" + roomId;
        }
        channelPromotionData.setUrl(url);
        //生成短链
        String shortUrlLink = OFFICAL_URL + CONTENT_PATH + "?ID=" + channelSource.getSourceId() + "_" + channelId;
        if (actor != null) {
            String roomId = "r:" + actor.get_id().toString();
            shortUrlLink = shortUrlLink + "&roomId=" + roomId;
        }
        GoogleShortUrlDTO googleShortUrlDTO = new GoogleShortUrlDTO();
        googleShortUrlDTO.setApn("in.dradhanus.liveher");
        googleShortUrlDTO.setIbi("com.stonemobile.youstar");
        googleShortUrlDTO.setIsi("1377028992");
        googleShortUrlDTO.setAmv("45");
        googleShortUrlDTO.setLink(shortUrlLink);
        googleShortUrlDTO.setOfl("https://www.youstar.live/");
        String paramStr = UrlEncodeHelper.createLinkByObject(googleShortUrlDTO);
        String fullUrl = GOOGLE_BASE_URL + paramStr;
        String shortUrl = getGoogleShortLink(fullUrl);
        if (StringUtils.isEmpty(shortUrl)) {
            return result.error("create short link url error");
        }
        channelPromotionData.setShortUrl(shortUrl);
        return result.ok();
    }

    private String getGoogleShortLink(String fullUrl) {
        String firebaseLink = "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=AIzaSyDtiskxIZtGuC1fRnhria_WN8XnG7RFETw";
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("longDynamicLink", fullUrl);
        String jsonParam = JSON.toJSONString(dataMap);
        Map<String, String> map = new HashMap<>();
        map.put("Content-Type", "application/json");
        HttpResponseData<String> responseData = webClient.sendRestfulPost(firebaseLink, jsonParam, map);
        if (!responseData.isOK()) {
            logger.error("google short link request error. fullUrl = {}", fullUrl);
            return null;
        }
        String body = responseData.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        return jsonObject.getString("shortLink");
    }

    private void downChannelPromotionRidExcel() {
        ExcelWriter excelWriter = null;
        try {
            List<String> dateList = new ArrayList<>();
            dateList.add("2021-05-15");
            dateList.add("2021-05-16");
            dateList.add("2021-05-17");
            String fileName = "channelPromotionRid.xlsx";
            excelWriter = EasyExcel.write(fileName, RidExcelDTO.class).build();
            for (String date : dateList) {
                DayTimeData dayTimeData = DateHelper.ARABIAN.getContinuesDays(date);
                List<Actor> channelAddActors = actorStatDao.listNewActor(dayTimeData, -1, "1001_6093936b4394bb64f7051335");
                List<RidExcelDTO> ridExcelDTOList = new ArrayList<>();
                for (Actor channelAddActor : channelAddActors) {
                    Integer rid = channelAddActor.getRid();
                    RidExcelDTO ridExcelDTO = new RidExcelDTO();
                    ridExcelDTO.setRid(rid);
                    ridExcelDTOList.add(ridExcelDTO);
                }
                WriteSheet writeSheet = EasyExcel.writerSheet(date).build();
                excelWriter.write(ridExcelDTOList, writeSheet);
            }
            logger.info("finish write channel promotion rid excel");
        } catch (Exception e) {
            logger.error("download channel promotion rid excel error. msg = {}", e.getMessage(), e);
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    public static void main(String[] args) {
        String start = "2021-04-23";
        DayTimeData continuesDays = DateHelper.ARABIAN.getContinuesDays(start);
        Criteria criteria = MongoUtils.setCriteriaBy_idRange(continuesDays.getTime(), continuesDays.getEndTime());
        Query query = new Query(criteria);
        logger.info("sql = {}", query.toString());
        String uid = "60a04cbb4e68e9d3fda06bf4";
        logger.info("ctime = {}", new ObjectId(uid).getTimestamp());
        int oneMonthTime = (int) (DateHelper.ARABIAN.getMonthOffset(continuesDays.getTime() * 1000L, 1) / 1000);
        logger.info("oneMonthTime = {}", oneMonthTime);
    }

}
