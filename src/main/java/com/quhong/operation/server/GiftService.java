package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.ResourceGroupData;
import com.quhong.data.condition.GiftCondition;
import com.quhong.data.vo.ZipInfoVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.AdvancedGiftDao;
import com.quhong.mongo.dao.GiftBagDao;
import com.quhong.mongo.data.AdvancedGiftData;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.GiftSortDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.GiftSortData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.dto.BlindBoxDTO;
import com.quhong.operation.share.dto.GiftDTO;
import com.quhong.operation.share.dto.GiftPanelDTO;
import com.quhong.operation.share.dto.InsertGiftDesDTO;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.CollectionUtil;
import com.quhong.operation.utils.ZipUtil;
import com.quhong.utils.K8sUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class GiftService implements ResourceService {

    private static final Logger logger = LoggerFactory.getLogger(GiftService.class);

    private final static String FILE_BUKET_PATH = "gift/";
    private final static String ZIP_PREFIX = "gift_";
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private final static List<Integer> BIND_BOX_GIFT_LIST = Arrays.asList(1323, 1324, 1325, 1326, 1327, 1328);

    @Resource
    private GiftDao giftDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private GiftBagDao giftBagDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private GiftSortDao giftSortDao;
    @Resource
    private AdvancedGiftDao advancedGiftDao;
    @Resource
    private K8sUtils k8sUtils;

    public PageResultVO<GiftVO> giftList(GiftCondition condition) {
        PageResultVO<GiftVO> pageVO = new PageResultVO<>();

        IPage<GiftData> pageGift = giftDao.selectPageList(condition);
        // List<Integer> giftIdList = pageGift.getRecords().stream().filter(item -> item.getBagGift() > 0).map(GiftData::getRid).collect(Collectors.toList());
        // Map<Integer, Integer> resourceGroupMap = giftBagDao.findResourceGroupList(giftIdList).stream().collect(Collectors.toMap(ResourceGroupData::getResourceId, ResourceGroupData::getCount));

        List<GiftVO> voList = new ArrayList<>();
        for (GiftData data : pageGift.getRecords()) {
            GiftVO vo = new GiftVO();
            BeanUtils.copyProperties(data, vo);
            long ctime = data.getCtime().getTime();
            vo.setCtime(ctime / 1000L);
            vo.setZipInfoVO(JSONObject.parseObject(data.getZipInfo(), GiftVO.ZipInfoVO.class));
            vo.setLevel(vo.getZipInfoVO() != null && vo.getZipInfoVO().getLevel() != null ? vo.getZipInfoVO().getLevel() : 0);
            vo.setCachePrice(data.getPrice());
            vo.setCacheGtype(data.getGtype());
            // vo.setTotalNum(resourceGroupMap.getOrDefault(data.getRid(), 0));   // 拥有该礼物人数
            vo.setTotalNum(0);   // 拥有该礼物人数
            if (data.getIsFusionAnimation() == GiftDao.FUSION_MODE_3) {
                ActorData actorData = actorDao.getActorDataFromCache(data.getFusionId());
                vo.setFusionId(actorData.getStrRid());
            }

            if (data.getIsFusionAnimation() == GiftDao.FUSION_MODE_4) {
                FamilyData familyData = familyDao.selectById(Integer.valueOf(data.getFusionId()));
                vo.setFusionId(String.valueOf(familyData.getRid()));
            }
            // 礼物配置说明
            vo.setIsGiftDes(data.getGprop() == 0 ? 0 : 1);
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }


    private List<Map<String, String>> convertAddZip(GiftDTO dto) {
        List<Map<String, String>> urlList = new ArrayList<>();
        if (!StringUtils.isEmpty(dto.getActionUrl())) {
            Map<String, String> actMap = new HashMap<>();
            actMap.put("fileName", "act");
            actMap.put("fileUrl", dto.getActionUrl());
            urlList.add(actMap);
        }

        if (!StringUtils.isEmpty(dto.getAudioUrl())) {
            Map<String, String> videoMap = new HashMap<>();
            videoMap.put("fileName", "voice");
            videoMap.put("fileUrl", dto.getAudioUrl());
            urlList.add(videoMap);
        }

        if (!StringUtils.isEmpty(dto.getFusionUrl())) {
            Map<String, String> fusionMap = new HashMap<>();
            fusionMap.put("fileName", "fusion");
            fusionMap.put("fileUrl", dto.getFusionUrl());
            urlList.add(fusionMap);
        }
        return urlList;
    }


    private List<Map<String, String>> convertUpdateZip(GiftDTO dto, GiftData giftData) {
        List<Map<String, String>> urlList = new ArrayList<>();
        if (!Objects.equals(dto.getActionUrl(), giftData.getActionUrl()) ||
                !Objects.equals(dto.getAudioUrl(), giftData.getAudioUrl()) ||
                !Objects.equals(dto.getFusionUrl(), giftData.getFusionUrl())) {
            if (!StringUtils.isEmpty(dto.getActionUrl())) {
                Map<String, String> actMap = new HashMap<>();
                actMap.put("fileName", "act");
                actMap.put("fileUrl", dto.getActionUrl());
                urlList.add(actMap);
            }

            if (!StringUtils.isEmpty(dto.getAudioUrl())) {
                Map<String, String> videoMap = new HashMap<>();
                videoMap.put("fileName", "voice");
                videoMap.put("fileUrl", dto.getAudioUrl());
                urlList.add(videoMap);
            }

            if (!StringUtils.isEmpty(dto.getFusionUrl())) {
                Map<String, String> fusionMap = new HashMap<>();
                fusionMap.put("fileName", "fusion");
                fusionMap.put("fileUrl", dto.getFusionUrl());
                urlList.add(fusionMap);
            }
        }
        return urlList;
    }

    private void asyncUpdateSource(GiftData giftData, List<Map<String, String>> urlList) {
        if (!urlList.isEmpty()) {
            String zipPath = ZipUtil.loadUrlZipUploadFile(ZIP_PREFIX, urlList);
            Map<String, Object> zipFileMeta = ZipUtil.calculateZipFileMD5(zipPath);
            String zipFileUrl = ZipUtil.uploadZipFile(zipPath, FILE_BUKET_PATH);
            giftData = giftDao.selectOne(giftData.getRid());
            JSONObject jsonObject = JSON.parseObject(giftData.getZipInfo());
            if (jsonObject == null) {
                jsonObject = new JSONObject();
            }

            jsonObject.put("url", zipFileUrl);
            jsonObject.put("md5", zipFileMeta.get("fileMd5"));
            giftData.setZipSize((Long) zipFileMeta.getOrDefault("fileSize", 0));
            giftData.setZipInfo(jsonObject.toJSONString());
            giftDao.updateOne(giftData);
        }
    }

    /**
     * 同类礼物处理
     * giftData = null 新增 礼物
     * giftData != null 更新礼物
     */

    // 为礼物增加父类关联礼物配置
    private void similarGiftAddShowDetail(int giftId) {
        GiftData parentGiftData = giftDao.getGiftFromDb(giftId);
        if (parentGiftData == null) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "所属关联礼物id输入有误");
        }

        if (parentGiftData.getParentId() > 0) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "所属关联礼物id已经关联其他礼物, 不能多级关联");
        }


        int gamePlay = parentGiftData.getGamePlay();
        if (gamePlay > 0 && gamePlay != GiftDao.GAME_PLAY_SIMILAR_GIFT) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "所属关联礼物id已经有其他礼物玩法");
        }

        JSONObject parentGiftZipInfo = JSON.parseObject(parentGiftData.getZipInfo());
        int parentGiftShowDetail = parentGiftZipInfo.getIntValue(GiftDao.SHOW_DETAIL_KEY);
        if (parentGiftShowDetail > 0 && parentGiftShowDetail != GiftDao.SHOW_DETAIL_SIMILAR_GIFT) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "所属关联礼物id已经有其他礼物玩法");
        }

        // 同类礼物数量限制(最多7个, 包含父类礼物)
        int initNum = 1;
        List<GiftData> giftPersonList = giftDao.getGiftPersonByParentId(giftId);
        initNum += CollectionUtils.isEmpty(giftPersonList) ? 0 : giftPersonList.size();
        if (initNum >= 7) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "关联礼物(包含原礼物)最多设置7个");
        }

        parentGiftZipInfo.put(GiftDao.SHOW_DETAIL_KEY, GiftDao.SHOW_DETAIL_SIMILAR_GIFT);
        parentGiftData.setZipInfo(JSONObject.toJSONString(parentGiftZipInfo));
        parentGiftData.setGamePlay(GiftDao.GAME_PLAY_SIMILAR_GIFT);
        giftDao.updateOne(parentGiftData);
    }

    // 为礼物移除父类关联礼物配置
    private void similarGiftRemoveShowDetail(int similarGiftId, int giftId) {

        // 除去要移除的关联礼物, 是否还有其他礼物有关联该礼物 有则不移除SHOW_DETAIL
        GiftData leftGiftData = giftDao.getGiftByParentId(similarGiftId, giftId);
        if (leftGiftData != null) {
            return;
        }
        GiftData parentGiftData = giftDao.getGiftFromDb(giftId);
        if (parentGiftData == null) {
            return;
        }
        JSONObject parentGiftZipInfo = JSON.parseObject(parentGiftData.getZipInfo());
        parentGiftZipInfo.remove(GiftDao.SHOW_DETAIL_KEY);
        parentGiftData.setZipInfo(JSONObject.toJSONString(parentGiftZipInfo));
        parentGiftData.setGamePlay(GiftDao.GAME_PLAY_DETAIL_GIFT);
        giftDao.updateOne(parentGiftData);
    }


    private void similarGiftHandle(GiftDTO dto, GiftData giftData) {

        int dtoParentId = dto.getParentId();
        int giftId = dto.getRid();
        int giftDataParentId = giftData.getParentId();

        if (dtoParentId > 0 && dtoParentId == giftId) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "关联礼物不能关联自己");
        }

        int updateFlag = 0;
        // 0: 不更新父礼物、
        // 1: 新增礼物, 为父礼物增加showDetail、
        // 2: 为父礼物增加showDetail, 删除旧的父礼物showDetail
        // 3: 仅删除父礼物showDetail

        if (dtoParentId > 0 && giftDataParentId == 0) {
            updateFlag = 1;
        } else if (dtoParentId > 0 && giftDataParentId > 0) {
            updateFlag = 2;
        } else if (dtoParentId == 0 && giftDataParentId > 0) {
            updateFlag = 3;
        }

        logger.info("similarGiftHandle dtoParentId:{}, updateParent: {}", dtoParentId, updateFlag);
        switch (updateFlag) {
            case 1:
                similarGiftAddShowDetail(dtoParentId);
                break;
            case 2:
                similarGiftAddShowDetail(dtoParentId);
                similarGiftRemoveShowDetail(dto.getRid(), giftDataParentId);
                break;
            case 3:
                similarGiftRemoveShowDetail(dto.getRid(), giftDataParentId);
                break;
        }
    }


    public Integer addGiftData(String uid, GiftDTO dto) {

        if (StringUtils.isEmpty(dto.getGicon())) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "礼物图标不能为空");
        }
        if (dto.getPrice() < 3) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "礼物价格不正确");
        }
        if (dto.getGatype() <= 0) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "礼物播放类型不正确");
        }
        if (dto.getGptype() <= 0) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "礼物面板所属不正确");
        }
        String internKey = uid + dto.getGname();
        synchronized (stringPool.intern(internKey)) {
            GiftData data = new GiftData();
            GiftData lastData = giftDao.selectLastIdOne();

            BeanUtils.copyProperties(dto, data);
            data.setRid(lastData.getRid() + 1);
            data.setCtime(new Timestamp(System.currentTimeMillis()));
            JSONObject zipInfo = new JSONObject();

            if (dto.getGptype() == 5) {
                zipInfo.put("level", dto.getLevel());
            }

            zipInfo.put("hot", dto.getHot());
            data.setZipInfo(zipInfo.toJSONString());
            data.setStatus(0);
            data.setGstatus(0);
            data.setParentId(0);
            data.setBagGift(0);

            if (data.getIsFusionAnimation() == GiftDao.FUSION_MODE_3) {
                ActorData actorData = actorDao.getActorByStrRid(data.getFusionId());
                if (actorData == null) {
                    throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "融合礼物指定用户ID有误");
                }
                data.setFusionId(actorData.getUid());
            }

            if (data.getIsFusionAnimation() == GiftDao.FUSION_MODE_4) {
                FamilyData familyData = familyDao.selectByFamilyRid(Integer.valueOf(data.getFusionId()));
                if (familyData == null) {
                    throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "融合礼物指定家族ID有误");
                }
                data.setFusionId(String.valueOf(familyData.getId()));
            }
            giftDao.insertOne(data);
            similarGiftHandle(dto, data);  // 关联礼物处理
            // 礼物资源异步处理
            List<Map<String, String>> urlList = convertAddZip(dto);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    asyncUpdateSource(data, urlList);
                    // 上线新礼物时，更新礼物面板排序
                    GiftPanelDTO dto = new GiftPanelDTO();
                    dto.setShowType(data.getGstatus()==1?1:0);
                    dto.setGpType(data.getGptype());
                    List<GiftPanelDTO.Gift> giftList = new ArrayList<>();
                    giftList.add(new GiftPanelDTO.Gift(data.getRid(), 1));
                    dto.setGiftList(giftList);
                    onlineNewGiftUpdatePanelOrderOpt(dto);
                }
            });
            return data.getRid();
        }
    }


    public void updateGiftData(GiftDTO dto) {

        GiftData data = giftDao.selectOne(dto.getRid());
        if (data == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if (dto.getPrice() < 3) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "礼物价格不正确");
        }

        if (dto.getGatype() <= 0) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "礼物播放类型不正确");
        }

        if (dto.getGptype() <= 0) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "礼物面板所属不正确");
        }

        // 同类礼物处理
        similarGiftHandle(dto, data);

        List<Map<String, String>> urlList = convertUpdateZip(dto, data);
        BeanUtils.copyProperties(dto, data);
        JSONObject zipInfo = JSON.parseObject(data.getZipInfo());

        if (dto.getGptype() == 5) {
            zipInfo.put("level", dto.getLevel());
        }

        zipInfo.put("hot", dto.getHot());
        data.setZipInfo(zipInfo.toJSONString());

        if (dto.getIsFusionAnimation() == GiftDao.FUSION_MODE_3) {
            ActorData actorData = actorDao.getActorByStrRid(dto.getFusionId());
            if (actorData == null) {
                throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "融合礼物指定用户ID有误");
            }
            data.setFusionId(actorData.getUid());
        }

        if (dto.getIsFusionAnimation() == GiftDao.FUSION_MODE_4) {
            FamilyData familyData = familyDao.selectByFamilyRid(Integer.valueOf(dto.getFusionId()));
            if (familyData == null) {
                throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "融合礼物指定家族ID有误");
            }
            data.setFusionId(String.valueOf(familyData.getId()));
        }

        giftDao.updateOne(data);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                asyncUpdateSource(data, urlList);
            }
        });
    }


    // 盲盒礼物配置
    public PageResultVO<BlindBoxVO> blindBoxList(BaseCondition condition) {
        PageResultVO<BlindBoxVO> pageVO = new PageResultVO<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int status = condition.getStatus();

        IPage<GiftData> pageGift = giftDao.selectBlindBoxPageList(status, page, pageSize);

        List<BlindBoxVO> voList = new ArrayList<>();
        for (GiftData data : pageGift.getRecords()) {
            BlindBoxVO vo = new BlindBoxVO();
            BeanUtils.copyProperties(data, vo);

            long ctime = data.getCtime().getTime();
            vo.setCtime(ctime / 1000L);
            vo.setZipInfoVO(JSONObject.parseObject(data.getZipInfo(), BlindBoxVO.ZipInfoVO.class));
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }

    public void blindBoxAdd(BlindBoxDTO dto) {

        if (StringUtils.isEmpty(dto.getRid())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        GiftData giftData = giftDao.selectOne(dto.getRid());
        BlindBoxDTO.ZipInfoVO zipInfoVO = dto.getZipInfoVO();

        if (giftData == null || zipInfoVO == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        List<BlindBoxDTO.RandomGift> randomGiftList = zipInfoVO.getRandomGiftList();
        if (randomGiftList == null || randomGiftList.isEmpty()) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        double totalProb = 0;
        for (BlindBoxDTO.RandomGift randomGift : randomGiftList) {
            totalProb += randomGift.getProb();
            int giftId = randomGift.getGiftId();

            GiftData tempGiftData = giftDao.getGiftFromDb(giftId);


            if (tempGiftData == null) {
                logger.error("giftId not find error: {}", randomGift.getGiftId());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }

            GiftDTO giftDTO = new GiftDTO();
            BeanUtils.copyProperties(tempGiftData, giftDTO);
            giftDTO.setHot(1);
            giftDTO.setInsertExtraGift(true);
            this.updateGiftData(giftDTO);
            giftDao.addGiftIdInBlindBox(giftId);
        }

        int totalProbRate = (int) totalProb;

        if (totalProbRate != 100) {
            logger.error("totalProb error: {}", totalProbRate);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        JSONObject jsonObject = JSONObject.parseObject(giftData.getZipInfo());

        jsonObject.put("ztype", 7);
        jsonObject.put("showDetail", 2);
        jsonObject.put("descUrl", zipInfoVO.getDescUrl());
        jsonObject.put("desc", zipInfoVO.getDesc());
        jsonObject.put("descAr", zipInfoVO.getDescAr());
        jsonObject.put("propIcon", zipInfoVO.getPropIcon());
        jsonObject.put("webType", 1);
        jsonObject.put("width", zipInfoVO.getWidth());
        jsonObject.put("height", zipInfoVO.getHeight());
        jsonObject.put("randomPoolSize", zipInfoVO.getRandomPoolSize());
        jsonObject.put("randomGiftList", zipInfoVO.getRandomGiftList());
        giftData.setZipInfo(JSON.toJSONString(jsonObject));
        giftData.setBlindBox(1);
        giftDao.updateOne(giftData);
    }


    public void blindBoxDelete(BlindBoxDTO dto) {

        if (StringUtils.isEmpty(dto.getRid())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        GiftData giftData = giftDao.selectOne(dto.getRid());

        JSONObject jsonObject = JSONObject.parseObject(giftData.getZipInfo());
        List<BlindBoxDTO.RandomGift> randomGiftList = jsonObject.getJSONArray("randomGiftList").toJavaList(BlindBoxDTO.RandomGift.class);
        if (randomGiftList != null && !randomGiftList.isEmpty()) {
            for (BlindBoxDTO.RandomGift randomGift : randomGiftList) {
                int giftRid = randomGift.getGiftId();
                GiftData tempGiftData = giftDao.getGiftFromCache(giftRid);
                GiftDTO giftDTO = new GiftDTO();
                BeanUtils.copyProperties(tempGiftData, giftDTO);
                giftDTO.setHot(0);
                this.updateGiftData(giftDTO);
            }
        }

        JSONObject commonZipInfo = new JSONObject();
        commonZipInfo.put("url", jsonObject.getOrDefault("url", ""));
        commonZipInfo.put("md5", jsonObject.getOrDefault("md5", ""));
        giftData.setZipInfo(JSON.toJSONString(commonZipInfo));
        giftData.setBlindBox(0);
        giftDao.updateOne(giftData);
    }


    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();

        GiftCondition giftCondition = new GiftCondition();
        BeanUtils.copyProperties(condition, giftCondition);
        IPage<GiftData> pageGift = giftDao.selectPageList(giftCondition);
        List<ResourceVO> voList = new ArrayList<>();
        for (GiftData data : pageGift.getRecords()) {
            ResourceVO vo = new ResourceVO();
            vo.setResourceNameEn(data.getGname());
            vo.setResourceNameAr(data.getGnamear());
            vo.setResourceIcon(data.getGicon());
            vo.setResourceId(data.getRid());
            vo.setResourcePrice(data.getPrice());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }

    public PageVO<GiftPanelVO> panelShow(int showType, int gpType) {
        List<GiftData> dataList = giftDao.getGiftPanelData(showType, gpType);
        List<GiftPanelVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            List<GiftSortData> giftSortList = giftSortDao.selectList(showType, gpType);
            List<Integer> unlockGiftList = advancedGiftDao.getListByTypeFromCache(AdvancedGiftDao.PLAY_TYPE_UNLOCK).stream().map(AdvancedGiftData::getGiftId).collect(Collectors.toList());
            Map<Integer, Integer> orderMap = Collections.emptyMap();
            if (!CollectionUtils.isEmpty(giftSortList)) {
                orderMap = giftSortList.stream().collect(Collectors.toMap(GiftSortData::getGiftId, GiftSortData::getPanelOrder));
            }
            Map<Integer, Integer> finalOrderMap = orderMap;
            Comparator<GiftData> orderAsc = Comparator.comparingInt(o -> finalOrderMap.getOrDefault(o.getRid(), 1000));
            dataList.sort(orderAsc.thenComparing(GiftData::getForder));
            int order = 0;
            for (GiftData data : dataList) {
                if (data.getParentId() > 0) {
                    continue;
                }
                ZipInfoVO zipInfoVO = parseZipInfo(data.getZipInfo());
                int ztype = zipInfoVO.getZtype();
                // 当配置是解锁礼物时,排除有效的且不是用于解锁的那些礼物
                if (ztype == GiftDao.GAME_PLAY_LOCK_GIFT && !unlockGiftList.contains(data.getRid())) {
                    continue;
                }
                order++;
                list.add(new GiftPanelVO(data.getRid(), data.getGicon(), data.getGname(), data.getGtype(), data.getPrice(), order));
            }
        }
        return new PageVO<>(list);
    }

    public void updatePanelOrder(GiftPanelDTO dto) {
        List<GiftSortData> giftSortList = giftSortDao.selectList(dto.getShowType(), dto.getGpType());
        Map<Integer, GiftSortData> giftSortMap = CollectionUtil.listToKeyMap(giftSortList, GiftSortData::getGiftId);

        if (!CollectionUtils.isEmpty(dto.getGiftList())) {
            for (GiftPanelDTO.Gift gift : dto.getGiftList()) {
                GiftSortData giftSortData = giftSortMap.get(gift.getGiftId());
                if (giftSortData == null) {
                    giftSortData = new GiftSortData();
                    giftSortData.setShowType(dto.getShowType());
                    giftSortData.setPanelType(dto.getGpType());
                    giftSortData.setGiftId(gift.getGiftId());
                    giftSortData.setPanelOrder(gift.getPanelOrder());
                    giftSortDao.insert(giftSortData);
                } else {
                    giftSortDao.update(dto.getShowType(), dto.getGpType(), gift.getGiftId(), gift.getPanelOrder());
                }
            }
            for (GiftSortData giftSortData : giftSortList) {
                if (!dto.getGiftList().stream().anyMatch(gift -> gift.getGiftId() == giftSortData.getGiftId())) {
                    giftSortDao.delete(giftSortData.getId());
                }
            }
        }
    }

    public void onlineNewGiftUpdatePanelOrderOpt(GiftPanelDTO dto) {
        if(dto.getShowType()!=0){
            // 房间外私信跟游戏房的排序都改
            dto.setShowType(1);
            onlineNewGiftUpdatePanelOrder(dto);

            dto.setShowType(2);
            onlineNewGiftUpdatePanelOrder(dto);
        }else{
            onlineNewGiftUpdatePanelOrder(dto);
        }
    }

    public void onlineNewGiftUpdatePanelOrder(GiftPanelDTO dto) {
        List<GiftSortData> giftSortList = giftSortDao.selectList(dto.getShowType(), dto.getGpType());
        Map<Integer, GiftSortData> giftSortMap = CollectionUtil.listToKeyMap(giftSortList, GiftSortData::getGiftId);
        int order = 0;
        if (!CollectionUtils.isEmpty(dto.getGiftList())) {
            List<Integer> unlockGiftList = advancedGiftDao.getListByTypeFromCache(AdvancedGiftDao.PLAY_TYPE_UNLOCK).stream().map(AdvancedGiftData::getGiftId).collect(Collectors.toList());
            for (GiftPanelDTO.Gift gift : dto.getGiftList()) {
                GiftData data = giftDao.getGiftFromDb(gift.getGiftId());
                if (data == null) {
                    logger.info("online new gift update panel order, cannot find gift. giftId={}", gift.getGiftId());
                    continue;
                }
                if (data.getParentId() > 0) {
                    continue;
                }
                ZipInfoVO zipInfoVO = parseZipInfo(data.getZipInfo());
                int ztype = zipInfoVO.getZtype();
                // 当配置是解锁礼物时,排除有效的且不是用于解锁的那些礼物
                if (ztype == GiftDao.GAME_PLAY_LOCK_GIFT && !unlockGiftList.contains(data.getRid())) {
                    continue;
                }
                order ++;
                GiftSortData giftSortData = giftSortMap.get(gift.getGiftId());
                if (giftSortData == null) {
                    giftSortData = new GiftSortData();
                    giftSortData.setShowType(dto.getShowType());
                    giftSortData.setPanelType(dto.getGpType());
                    giftSortData.setGiftId(gift.getGiftId());
                    giftSortData.setPanelOrder(order);
                    giftSortDao.insert(giftSortData);
                    logger.info("online new gift insert new gift panel order. giftId={} gName={} order={}", gift.getGiftId(), data.getGname(), order);
                } else {
                    giftSortDao.update(dto.getShowType(), dto.getGpType(), gift.getGiftId(), order);
                    logger.info("online new gift update gift panel order. giftId={} gName={} order={}", gift.getGiftId(), data.getGname(), order);
                }
            }
        }
        if (order == 0) {
            logger.info("no gift need to update panel order");
        } else {
            for (GiftSortData giftSortData : giftSortList) {
                order++;
                giftSortDao.update(dto.getShowType(), dto.getGpType(), giftSortData.getGiftId(), order);
            }
        }
    }


    public ZipInfoVO parseZipInfo(String zipInfoStr) {
        if (!StringUtils.isEmpty(zipInfoStr)) {
            ZipInfoVO zipInfoVO = JSONObject.parseObject(zipInfoStr, ZipInfoVO.class);
            zipInfoVO.setUrl(zipInfoVO.getUrl());
            return zipInfoVO;
        }
        return new ZipInfoVO();
    }

    /**
     * 检查定时上下线礼物状态
     * 检查scheduleTime=1的礼物，根据时间条件更新状态
     */
    public void checkScheduledGifts() {
        long startTime = System.currentTimeMillis();
        int currentTime = DateHelper.getNowSeconds();
        int updateCount = 0;

        try {
            // 查询所有开启定时上下线的礼物
            List<GiftData> scheduledGifts = giftDao.selectScheduledGifts();
            if (CollectionUtils.isEmpty(scheduledGifts)) {
                logger.info("No scheduled gifts found");
                return;
            }

            logger.info("Found {} scheduled gifts to check", scheduledGifts.size());

            for (GiftData gift : scheduledGifts) {
                boolean needUpdate = false;
                boolean isOnline = false;
                // 检查上线时间：如果upTime > 0且当前时间戳大于upTime时，更新upTime=0，status=1
                if (gift.getUpTime() > 0 && currentTime >= gift.getUpTime()) {
                    gift.setUpTime(0);
                    gift.setStatus(1);
                    needUpdate = true;
                    isOnline = true;
                    logger.info("Gift {} scheduled to go online, setting status=1", gift.getRid());
                }

                // 检查下线时间：如果downTime > 0且当前时间戳大于downTime时，更新downTime=0，status=0
                if (gift.getDownTime() > 0 && currentTime >= gift.getDownTime()) {
                    gift.setDownTime(0);
                    gift.setStatus(0);
                    needUpdate = true;
                    logger.info("Gift {} scheduled to go offline (current time < downTime), setting status=0", gift.getRid());
                }

                // 检查是否需要关闭定时功能：如果upTime=0且downTime=0时，设置scheduleTime=0
                if (gift.getUpTime() == 0 && gift.getDownTime() == 0) {
                    gift.setScheduleTime(0);
                    needUpdate = true;
                    logger.info("Gift {} has no scheduled times, disabling schedule_time", gift.getRid());
                }

                // 更新礼物数据
                if (needUpdate) {
                    giftDao.updateOne(gift);
                    updateCount++;
                    if (isOnline) {
                        // 上线新礼物时，更新礼物面板排序
                        GiftPanelDTO dto = new GiftPanelDTO();
                        dto.setShowType(gift.getGstatus()==1?1:0);
                        dto.setGpType(gift.getGptype());
                        List<GiftPanelDTO.Gift> giftList = new ArrayList<>();
                        giftList.add(new GiftPanelDTO.Gift(gift.getRid(), 1));
                        dto.setGiftList(giftList);
                        onlineNewGiftUpdatePanelOrderOpt(dto);
                    }
                }
            }

            logger.info("Scheduled gifts check completed. Updated {} gifts, cost={}ms", updateCount, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            logger.error("Error checking scheduled gifts: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时检查GiftData中被设置为bagGift=1的礼物是否还有在giftBag中，没有则修改bagGift为0
     * 每小时执行一次
     */
    public void syncGiftBagStatus() {
        // 只在主节点执行
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            // 1. 查询所有设置为背包礼物的礼物列表
            List<GiftData> bagGiftList = giftDao.selectBagGiftList();
            if (CollectionUtils.isEmpty(bagGiftList)) {
                return;
            }
            // 2. 获取所有背包礼物的ID集合
            List<Integer> bagGiftIdList = bagGiftList.stream().map(GiftData::getRid).collect(Collectors.toList());

            // 3. 检查这些礼物在gift_bag中是否还存在
            List<ResourceGroupData> existingGiftList = giftBagDao.findResourceGroupList(bagGiftIdList);
            if (CollectionUtils.isEmpty(existingGiftList)) {
                return;
            }
            List<Integer> existingGiftIds = existingGiftList.stream().map(ResourceGroupData::getResourceId).collect(Collectors.toList());

            // 4. 找出不存在的礼物ID
            bagGiftIdList.removeAll(existingGiftIds);

            // 过滤掉盲盒礼物
            bagGiftIdList.removeAll(BIND_BOX_GIFT_LIST);
            if (CollectionUtils.isEmpty(bagGiftIdList)) {
                return;
            }

            // 5. 批量更新这些礼物的bagGift状态为0
            int updatedCount = giftDao.updateBagGiftStatus(bagGiftIdList, 0);
            logger.info("syncGiftBagStatus updateCount:{}, bagGiftIdList：{}, count: {}ms", updatedCount, bagGiftIdList, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            logger.error("syncGiftBagStatus e={}", e.getMessage(), e);
        }
    }


    /**
     * 新增或修改礼物说明
     * @param dto
     * @return void
     */
    public void addOrUpdateGiftDesc(InsertGiftDesDTO dto) {
        validateInsertGiftDesDTO(dto);
        GiftData giftData = giftDao.selectOne(dto.getRid());
        if (giftData == null) {
            throw new CommonH5Exception(new HttpCode(1, "id错误，找不到该礼物资源"));
        }
        if (giftData.getGprop() == 1){
            throw new CommonH5Exception(new HttpCode(1, "该礼物资源已配置整蛊礼物，不能修改或配置礼物说明"));
        }
        JSONObject zipInfoJson = JSON.parseObject(giftData.getZipInfo());
        if (zipInfoJson == null) {
            logger.error("Original zipInfo: {}", giftData.getZipInfo());
            zipInfoJson = new JSONObject();
        }
        zipInfoJson.put("ztype", 4);
        zipInfoJson.put("webType", 1);
        zipInfoJson.put("showDetail", 1);
        zipInfoJson.put("desc", dto.getDesc());
        zipInfoJson.put("descAr", dto.getDescAr());
        zipInfoJson.put("descUrl", dto.getDescUrl());
        zipInfoJson.put("propIcon", dto.getPropIcon());
        giftData.setGprop(dto.getIsGiftDes() == 1 ? 2 :  giftData.getGprop());
        giftData.setZipInfo(zipInfoJson.toJSONString());
        giftDao.updateOne(giftData);
    }


    /**
     * 删除或不配置礼物说明
     * @param rid
     * @return void
     */

    public void deleteGiftDesc(int rid) {
        // 礼物ID校验
        if (rid == 0) {
            throw new CommonH5Exception(new HttpCode(1, "礼物ID不能为空"));
        }
        GiftData giftData = giftDao.selectOne(rid);
        if (giftData == null) {
            throw new CommonH5Exception(new HttpCode(1, "id错误，找不到该礼物资源"));
        }
        if (giftData.getGprop() == 1){
            throw new CommonH5Exception(new HttpCode(1, "该礼物资源已配置整蛊礼物，不能修改或配置礼物说明"));
        }
        // 使用JSONObject保持原有字段
        JSONObject zipInfoJson = JSON.parseObject(giftData.getZipInfo());
        if (zipInfoJson == null) {
            zipInfoJson = new JSONObject();
        }
        // 删除礼物说明相关字段
        zipInfoJson.remove("ztype");
        zipInfoJson.remove("webType");
        zipInfoJson.remove("showDetail");
        zipInfoJson.remove("desc");
        zipInfoJson.remove("descAr");
        zipInfoJson.remove("descUrl");
        zipInfoJson.remove("propIcon");
        giftData.setGprop(0);
        giftData.setZipInfo(zipInfoJson.toJSONString());
        giftDao.updateOne(giftData);
    }
    /**
     * 校验礼物说明DTO参数
     */
    private void validateInsertGiftDesDTO(InsertGiftDesDTO dto) {
        // 礼物ID校验
        if (dto.getRid() == 0) {
            throw new CommonH5Exception(new HttpCode(1, "礼物ID不能为空"));
        }
        // 是否带礼物说明校验
        if (dto.getIsGiftDes() == null) {
            throw new CommonH5Exception(new HttpCode(1, "是否配置礼物说明不能为空"));
        }
        // 如果启用了礼物说明(isGiftDes=1)，则必须提供相关字段
        if (dto.getIsGiftDes() == 1) {
            // 英语介绍校验
            if (!StringUtils.hasLength(dto.getDesc())) {
                throw new CommonH5Exception(new HttpCode(1, "英语礼物介绍不能为空"));
            }
            // 阿拉伯语介绍校验
            if (!StringUtils.hasLength(dto.getDesc())) {
                throw new CommonH5Exception(new HttpCode(1, "阿拉伯语礼物介绍不能为空"));
            }
            // 图片URL校验
            if (!StringUtils.hasLength(dto.getPropIcon())) {
                throw new CommonH5Exception(new HttpCode(1, "礼物图标不能为空"));
            }
        }
    }

}
