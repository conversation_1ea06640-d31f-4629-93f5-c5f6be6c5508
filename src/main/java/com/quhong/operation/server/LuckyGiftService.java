package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.LuckyGiftConfigDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.LuckyGiftConfigData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.GiftVO;
import com.quhong.operation.share.vo.LuckyGiftVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.LuckyGiftNewRedis;
import com.quhong.redis.LuckyGiftRedis;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class LuckyGiftService {
    private static final Logger logger = LoggerFactory.getLogger(LuckyGiftService.class);
    private static final Set<String> SUPPORT_RESOURCE = new HashSet<>(Arrays.asList("mic", "buddle", "ride",
            "ripple", "diamond", "badge", "gift", "float_screen", "thanks",ResourceConstant.BACK_GROUND,ResourceConstant.HONOR_TITLE));

    @Resource
    private LuckyGiftConfigDao luckyGiftConfigDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private LuckyGiftNewRedis luckyGiftNewRedis;
    @Resource
    private LuckyGiftRedis luckyGiftRedis;


    public PageResultVO<LuckyGiftVO> getDataList(BaseCondition condition) {
        PageResultVO<LuckyGiftVO> pageVO = new PageResultVO<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        IPage<GiftData> pageGift = giftDao.selectLuckGiftPageList(condition.getStatus(), page, pageSize);

        List<LuckyGiftVO> voList = new ArrayList<>();
        for (GiftData data : pageGift.getRecords()) {
            LuckyGiftVO vo = new LuckyGiftVO();
            BeanUtils.copyProperties(data, vo);

            GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(data.getZipInfo(), GiftVO.ZipInfoVO.class);
            vo.setZipInfo(zipInfoVO);
            vo.setLuckyGiftConfigList(luckyGiftConfigDao.selectList(data.getRid()));
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }


    public void addData(LuckyGiftVO dto) {

        GiftData giftData = giftDao.selectOne(dto.getRid());
        if (giftData == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if (giftData.getGamePlay() > 0) {
            logger.info("giftData gamePlay already set {}", giftData.getGamePlay());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        List<LuckyGiftConfigData> luckyGiftConfigList = dto.getLuckyGiftConfigList();
        if (null != luckyGiftConfigList) {
            for (LuckyGiftConfigData configData : luckyGiftConfigList) {
                if (!SUPPORT_RESOURCE.contains(configData.getRewardType())) {
                    logger.info("rewardType not support {}", configData.getRewardType());
                    throw new CommonException(HttpCode.PARAM_ERROR);
                }

                if (StringUtils.isEmpty(configData.getIcon())) {
                    throw new CommonException(HttpCode.PARAM_ERROR);
                }

                if (ResourceConstant.DIAMOND.equals(configData.getRewardType())) {
                    if (null == configData.getRewardNum()) {
                        logger.info("钻石数量不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                } else if (ResourceConstant.HEART.equals(configData.getRewardType())) {
                    if (null == configData.getRewardNum()) {
                        logger.info("金币数量不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                } else if (ResourceConstant.THANKS.equals(configData.getRewardType())) {
                    logger.info("无中奖上传");

                } else {
                    if (null == configData.getSourceId()) {
                        logger.info("资源id不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                    if (null == configData.getRewardTime()) {
                        logger.info("资源时长不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                }
                configData.setGiftId(dto.getRid());
            }
            luckyGiftConfigDao.insertMany(luckyGiftConfigList);
        }

        giftData.setGamePlay(GiftDao.GAME_PLAY_LUCKY_GIFT);
        GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
        zipInfoVO.setZtype(GiftDao.GAME_PLAY_LUCKY_GIFT);
        zipInfoVO.setWebType(1);
        zipInfoVO.setShowDetail(1);
        zipInfoVO.setDesc(dto.getZipInfo().getDesc());
        zipInfoVO.setDescAr(dto.getZipInfo().getDescAr());
        zipInfoVO.setDescUrl(dto.getZipInfo().getDescUrl());
        zipInfoVO.setPropIcon(dto.getZipInfo().getPropIcon());
        zipInfoVO.setHeight(dto.getZipInfo().getHeight());
        zipInfoVO.setWidth(dto.getZipInfo().getWidth());
        zipInfoVO.setJackpot(dto.getZipInfo().getJackpot());
        if (null != dto.getZipInfo().getJackpot() && 1 == dto.getZipInfo().getJackpot()) {
            zipInfoVO.setShowDetail(3);
        }
        giftData.setZipInfo(JSON.toJSONString(zipInfoVO));
        giftDao.updateOne(giftData);
    }

    public void updateData(LuckyGiftVO dto) {

        int giftId = dto.getRid();
        GiftData giftData = giftDao.selectOne(giftId);
        if (giftData == null || giftData.getGamePlay() != GiftDao.GAME_PLAY_LUCKY_GIFT) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        List<LuckyGiftConfigData> luckyGiftConfigList = dto.getLuckyGiftConfigList();
        if (null != luckyGiftConfigList) {
            for (LuckyGiftConfigData configData : luckyGiftConfigList) {
                Integer recordId = configData.getId();

                if (!SUPPORT_RESOURCE.contains(configData.getRewardType())) {
                    logger.info("rewardType not support {}", configData.getRewardType());
                    throw new CommonException(HttpCode.PARAM_ERROR);
                }

                if (StringUtils.isEmpty(configData.getIcon())) {
                    throw new CommonException(HttpCode.PARAM_ERROR);
                }

                if (ResourceConstant.DIAMOND.equals(configData.getRewardType())) {
                    if (null == configData.getRewardNum()) {
                        logger.info("钻石数量不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                } else if (ResourceConstant.HEART.equals(configData.getRewardType())) {
                    if (null == configData.getRewardNum()) {
                        logger.info("金币数量不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                } else if (ResourceConstant.THANKS.equals(configData.getRewardType())) {
                    logger.info("无中奖上传");

                } else {
                    if (null == configData.getSourceId()) {
                        logger.info("资源id不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                    if (null == configData.getRewardTime()) {
                        logger.info("资源时长不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                }

                configData.setGiftId(giftId);
                if (recordId == null) {
                    configData.setCtime(DateHelper.getNowSeconds());
                    luckyGiftConfigDao.insertOne(configData);
                } else {
                    luckyGiftConfigDao.updateOne(configData);
                }
            }


            // 清除配置删除项
            List<LuckyGiftConfigData> LuckyGiftConfigList = luckyGiftConfigDao.selectList(giftId);
            Map<Integer, LuckyGiftConfigData> configMap = CollectionUtil.listToKeyMap(luckyGiftConfigList, LuckyGiftConfigData::getId);
            for (LuckyGiftConfigData data : LuckyGiftConfigList) {
                if (!configMap.containsKey(data.getId())) {
                    luckyGiftConfigDao.removeById(data.getId());
                }
            }
        }

        GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
        zipInfoVO.setDesc(dto.getZipInfo().getDesc());
        zipInfoVO.setDescAr(dto.getZipInfo().getDescAr());
        zipInfoVO.setDescUrl(dto.getZipInfo().getDescUrl());
        zipInfoVO.setPropIcon(dto.getZipInfo().getPropIcon());
        zipInfoVO.setHeight(dto.getZipInfo().getHeight());
        zipInfoVO.setWidth(dto.getZipInfo().getWidth());
        zipInfoVO.setJackpot(dto.getZipInfo().getJackpot());
        if (null != dto.getZipInfo().getJackpot() && 1 == dto.getZipInfo().getJackpot()) {
            zipInfoVO.setShowDetail(3);
        }
        giftData.setZipInfo(JSON.toJSONString(zipInfoVO));
        giftDao.updateOne(giftData);
        if (null == dto.getZipInfo().getJackpot() || 0 == dto.getZipInfo().getJackpot()) {
            luckyGiftNewRedis.deletePoolSize(giftId);
        }
    }

    public void deleteData(LuckyGiftVO dto) {

        GiftData giftData = giftDao.selectOne(dto.getRid());
        if (giftData == null || giftData.getGamePlay() != GiftDao.GAME_PLAY_LUCKY_GIFT) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        luckyGiftConfigDao.deleteData(giftData.getRid());
        GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
        zipInfoVO.setZtype(null);
        zipInfoVO.setWebType(null);
        zipInfoVO.setShowDetail(null);
        zipInfoVO.setDesc(null);
        zipInfoVO.setDescAr(null);
        zipInfoVO.setDescUrl(null);
        zipInfoVO.setPropIcon(null);
        zipInfoVO.setHeight(null);
        zipInfoVO.setWidth(null);
        zipInfoVO.setJackpot(null);
        giftData.setGamePlay(0);
        giftData.setZipInfo(JSON.toJSONString(zipInfoVO));
        giftDao.updateOne(giftData);
    }

    public List<LuckyGiftConfigData> listJackpotConfig() {
        return luckyGiftConfigDao.selectList(0);
    }

    public void addJackpotConfig(LuckyGiftConfigData dto) {
        dto.setId(null);
        dto.setGiftId(0);
        dto.setNameEn("jackpot lucky gift");
        dto.setNameAr("jackpot lucky gift");
        dto.setRewardType("");
        dto.setRewardTime(0);
        dto.setSourceId(0);
        dto.setBigPush(0);
        dto.setIcon("");
        dto.setCtime(DateHelper.getNowSeconds());
        luckyGiftConfigDao.insertOne(dto);
        luckyGiftRedis.clearLuckyGiftPool(0);
    }

    public void updateJackpotConfig(LuckyGiftConfigData dto) {
        dto.setGiftId(0);
        dto.setNameAr("jackpot lucky gift");
        dto.setNameEn("jackpot lucky gift");
        dto.setRewardType("");
        dto.setRewardTime(0);
        dto.setSourceId(0);
        dto.setBigPush(0);
        dto.setIcon("");
        luckyGiftConfigDao.updateOne(dto);
        luckyGiftRedis.clearLuckyGiftPool(0);
    }

    public void deleteJackpotConfig(int id) {
        luckyGiftConfigDao.removeById(id);
    }
}
