package com.quhong.operation.server;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.datas.DayTimeData;
import com.quhong.operation.common.OsConstant;
import com.quhong.operation.constant.AppPackageConstant;
import com.quhong.operation.constant.MoneyDetailATypeConsts;
import com.quhong.operation.dao.*;
import com.quhong.operation.share.dto.user.NfRetainedInfoDTO;
import com.quhong.operation.share.mysql.UserOnlineData;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.UserOnlineVo;
import com.quhong.operation.share.vo.user.NfRetainedInfoVO;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;

@Component
public class UserServer {

    private static final Logger logger = LoggerFactory.getLogger(UserServer.class);

    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private RoomMicStatDao roomMicStatDao;
    @Autowired
    private GiftRecordMgDao giftRecordMgDao;
    @Autowired
    private MoneyDetailStatDao moneyDetailStatDao;
    @Autowired
    private AdminDauDao dauDao;
    @Autowired
    private UserOnlineDao userOnlineDao;

    public NfRetainedInfoVO getNfRetainedInfo(NfRetainedInfoDTO nfRetainedInfoDTO) {
        NfRetainedInfoVO result = new NfRetainedInfoVO();
        String cType = nfRetainedInfoDTO.getcType();
        DayTimeData dayTimeData = nfRetainedInfoDTO.getDate();
        result.setdStr(dayTimeData.getDate());
        int pkgType = nfRetainedInfoDTO.getPkgType();
        int platform = nfRetainedInfoDTO.getPlatform();
        int qType = nfRetainedInfoDTO.getqType();
        Set<String> uidSet = nfUserList(dayTimeData, pkgType, platform, qType);
        if (CollectionUtils.isEmpty(uidSet)) {
            return result;
        }
        Set<String> destNfSet = new HashSet<>();
        if ("total".equals(cType)) {
            destNfSet = uidSet;
        } else if ("mic".equals(cType)) {
            destNfSet = roomMicStatDao.getGtMicTimeUsers(dayTimeData.getTime(), dayTimeData.getEndTime(), uidSet, 300);
        } else if ("gift".equals(cType)) {
            Set<String> giftUidSet = giftRecordMgDao.getGtCountUsers(dayTimeData.getTime(), dayTimeData.getEndTime(), 3, -1);
            destNfSet = filterUsers(uidSet, giftUidSet);
        } else if ("charge".equals(cType)) {
            List<TotalVO> chargeList = moneyDetailStatDao.listStatByATypeAndUid(dayTimeData.getTime(), dayTimeData.getEndTime(), uidSet, MoneyDetailATypeConsts.PAY_CHARGE);
            for (TotalVO totalVO : chargeList) {
                destNfSet.add(totalVO.getUid());
            }
        }
        if (CollectionUtils.isEmpty(destNfSet)) {
            return result;
        }
        return fillRemainData(result, destNfSet, dayTimeData.getDate());
    }

    private NfRetainedInfoVO fillRemainData(NfRetainedInfoVO result, Set<String> personSet, String dateStr) {
        result.setUser0(personSet.size());
        int day1Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(1).toString(), personSet);
        result.setUser1(day1Num);
        result.setUser1Rate(getRate(day1Num, personSet.size()));
        int day2Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(2).toString(), personSet);
        result.setUser2(day2Num);
        result.setUser2Rate(getRate(day2Num, personSet.size()));
        int day3Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(3).toString(), personSet);
        result.setUser3(day3Num);
        result.setUser3Rate(getRate(day3Num, personSet.size()));
        int day4Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(4).toString(), personSet);
        result.setUser4(day4Num);
        result.setUser4Rate(getRate(day4Num, personSet.size()));
        int day5Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(5).toString(), personSet);
        result.setUser5(day5Num);
        result.setUser5Rate(getRate(day5Num, personSet.size()));
        int day6Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(6).toString(), personSet);
        result.setUser6(day6Num);
        result.setUser6Rate(getRate(day6Num, personSet.size()));
        int day7Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(7).toString(), personSet);
        result.setUser7(day7Num);
        result.setUser7Rate(getRate(day7Num, personSet.size()));
        return result;
    }

    private Set<String> nfUserList(DayTimeData dayTimeData, int pkgType, int platform, int qType) {
        String app = "";
        if (pkgType == 0) {
            app = "com.youstar.android.lite";
        } else if (pkgType == 1) {
            app = "in.dradhanus.liveher";
        } else if (pkgType == 3) {
            app = "com.stonemobile.youstar";
        } else if (AppPackageConstant.YOUSTAR == pkgType) {
            app = "in.dradhanus.liveher,com.youstar.android.lite";
        }
        if (platform == 2 && qType == 0) {
            return actorDao.getActors(dayTimeData.getTime(), dayTimeData.getEndTime(), null, app, -1, -1);
        } else if (platform == 2 && qType == 1) {
            return actorDao.getActors(dayTimeData.getTime(), dayTimeData.getEndTime(), null, app, -1, 2);
        } else if (platform == 0 && qType == 0) {
            return actorDao.getActors(dayTimeData.getTime(), dayTimeData.getEndTime(), null, app, 2, -1);
        } else if (platform == 0 && qType == 1) {
            return actorDao.getActors(dayTimeData.getTime(), dayTimeData.getEndTime(), null, app, 2, 2);
        } else if (platform == 1 && qType == 0) {
            return actorDao.getActors(dayTimeData.getTime(), dayTimeData.getEndTime(), null, app, 1, -1);
        } else if (platform == 1 && qType == 1) {
            return actorDao.getActors(dayTimeData.getTime(), dayTimeData.getEndTime(), null, app, 1, 2);
        } else if (platform == 3 && qType == 0) {
            return actorDao.getActors(dayTimeData.getTime(), dayTimeData.getEndTime(), "huawei", app, -1, -1);
        } else if (platform == 3 && qType == 1) {
            return actorDao.getActors(dayTimeData.getTime(), dayTimeData.getEndTime(), "huawei", app, -1, 2);
        }
        return null;
    }

    private Set<String> filterUsers(Set<String> uidSet, Set<String> aidSet) {
        if (CollectionUtils.isEmpty(uidSet) || CollectionUtils.isEmpty(aidSet)) {
            return new HashSet<>();
        }
        Set<String> result = new HashSet<>();
        for (String aid : aidSet) {
            if (uidSet.contains(aid)) {
                result.add(aid);
            }
        }
        return result;
    }

    private double getRate(int divisor, int dividend) {
        if (divisor == 0 || dividend == 0) {
            return 0.0;
        }
        double result = new BigDecimal((float) divisor / (float) dividend).setScale(2, RoundingMode.HALF_UP).doubleValue();
        return result;
    }

    @Cacheable(value = "reports", key = "targetClass + methodName + #p0",
            condition = "T(com.quhong.core.utils.DateHelper).ARABIAN.formatDateInDay().compareTo(#date)>0", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<UserOnlineVo> getOnlineData(String date) {
        logger.info("find user online date, date = {}", date);
        Integer[] times = DateHelper.ARABIAN.getTimeWhereRange(date);
        int start = times[0];
        int end = times[1];
        //获取全部的在线信息
        List<UserOnlineData> totalUserOnlineList = userOnlineDao.findUserOnlineData(start, end, OsConstant.TOTAL);
        //获取ios在线信息
        List<UserOnlineData> iosUserOnlineList = userOnlineDao.findUserOnlineData(start, end, OsConstant.IOS);
        //获取安卓在线信息
        List<UserOnlineData> androidUserOnlineList = userOnlineDao.findUserOnlineData(start, end, OsConstant.ANDROID);
        //处理ios
        List<UserOnlineVo> iosTarget = parseUserOnlineList(iosUserOnlineList, OsConstant.IOS, start, end);
        Map<String, UserOnlineVo> iosMap = new HashMap<>();
        for (UserOnlineVo userOnlineVo : iosTarget) {
            iosMap.put(userOnlineVo.getTimeStr(), userOnlineVo);
        }
        //处理安卓
        List<UserOnlineVo> androidTarget = parseUserOnlineList(androidUserOnlineList, OsConstant.ANDROID, start, end);
        Map<String, UserOnlineVo> androidMap = new HashMap<>();
        for (UserOnlineVo userOnlineVo : androidTarget) {
            androidMap.put(userOnlineVo.getTimeStr(), userOnlineVo);
        }
        //处理全部
        List<UserOnlineVo> target = parseUserOnlineList(totalUserOnlineList, OsConstant.TOTAL, start, end);
        for (UserOnlineVo userOnlineVo : target) {
            String timeStr = userOnlineVo.getTimeStr();
            UserOnlineVo iosUserOnlineVo = iosMap.get(timeStr);
            if (iosUserOnlineVo != null) {
                userOnlineVo.setIosUserCount(iosUserOnlineVo.getIosUserCount());
            }
            UserOnlineVo androidUserOnlineVo = androidMap.get(timeStr);
            if (androidUserOnlineVo != null) {
                userOnlineVo.setAndroidUserCount(androidUserOnlineVo.getAndroidUserCount());
            }
        }
        logger.info("user online select finish");
        return target;
    }

    private List<UserOnlineVo> parseUserOnlineList(List<UserOnlineData> userOnlineList, int os, int start, int end) {
        List<UserOnlineVo> target = new ArrayList<>();
        long lastUpdateTime = 0;
        for (UserOnlineData onlineData : userOnlineList) {
            if (lastUpdateTime != 0) {
                long updateTime = onlineData.getCtime() * 1000L;
                while (updateTime - lastUpdateTime > 80 * 1000) {
                    UserOnlineVo addData = new UserOnlineVo();
                    lastUpdateTime = lastUpdateTime + 60 * 1000;
                    String time = DateHelper.BEIJING.getHourTime(new Date(lastUpdateTime));
                    addData.setTimeStr(time);
                    target.add(addData);
                }
            }
            UserOnlineVo userOnlineVo = new UserOnlineVo();
            lastUpdateTime = onlineData.getCtime() * 1000L;
            String time = DateHelper.BEIJING.getHourTime(new Date(lastUpdateTime));
            if (os == OsConstant.TOTAL) {
                userOnlineVo.setUserCount(onlineData.getUserOnlineCount());
                userOnlineVo.setRoomCount(onlineData.getRoomActiveCount());
                userOnlineVo.setRobotUserCount(onlineData.getRobotOnlineCount());
                userOnlineVo.setManUserNum(onlineData.getManOnlineCount());
                userOnlineVo.setNewManUserNum(onlineData.getNewManOnlineCount());
                userOnlineVo.setGirlUserNum(onlineData.getGirlOnlineCount());
                userOnlineVo.setNewGirlUserNum(onlineData.getNewGirlOnlineCount());
                userOnlineVo.setPtgNum(onlineData.getPtgOnlineCount());
                userOnlineVo.setRechargeOnlineCount(onlineData.getRechargeOnlineCount());
                userOnlineVo.setLiveRoomCount(onlineData.getLiveRoomCount());
                userOnlineVo.setVoiceRoomCount(onlineData.getVoiceRoomCount());
                userOnlineVo.setLudoRoomCount(onlineData.getLudoRoomCount());
                userOnlineVo.setVideoRoomCount(onlineData.getVideoRoomCount());
                userOnlineVo.setTurntableRoomCount(onlineData.getTurntableRoomCount());
            } else if (os == OsConstant.IOS) {
                userOnlineVo.setIosUserCount(onlineData.getUserOnlineCount() - onlineData.getRobotOnlineCount());
            } else if (os == OsConstant.ANDROID) {
                userOnlineVo.setAndroidUserCount(onlineData.getUserOnlineCount() - onlineData.getRobotOnlineCount());
            }
            userOnlineVo.setTimeStr(time);
            target.add(userOnlineVo);
        }
        if (lastUpdateTime == 0) {
            lastUpdateTime = start * 1000L;
        }
        long updateTime = end * 1000L;
        while (updateTime - lastUpdateTime > 80 * 1000) {
            UserOnlineVo addData = new UserOnlineVo();
            lastUpdateTime = lastUpdateTime + 60 * 1000;
            String time = DateHelper.BEIJING.getHourTime(new Date(lastUpdateTime));
            addData.setTimeStr(time);
            target.add(addData);
        }
        return target;
    }
}
