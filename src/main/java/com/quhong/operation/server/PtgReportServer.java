package com.quhong.operation.server;

import com.quhong.constant.MatchConstant;
import com.quhong.datas.DayTimeData;
import com.quhong.mysql.data.PtgCrushLogData;
import com.quhong.operation.dao.DauStatDao;
import com.quhong.operation.dao.OperationActorDao;
import com.quhong.operation.share.data.FriendSwipeInfo;
import com.quhong.operation.share.mysql.MysqlMsgRecordData;
import com.quhong.operation.share.vo.PtgCrushDayVO;
import com.quhong.operation.share.vo.PtgCrushNUDayVO;
import com.quhong.operation.utils.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class PtgReportServer {

    private static final Logger logger = LoggerFactory.getLogger(PtgReportServer.class);


    @Resource
    private DauStatDao dauStatDao;
    @Autowired
    private OperationActorDao actorDao;


    /**
     * 获取ptg端的快捷匹配数据
     *
     * @param dayTimeData
     * @return
     */
    public PtgCrushDayVO getPtgCrushData(DayTimeData dayTimeData) {
        return new PtgCrushDayVO();
    }

    /**
     * 获取新用户端的快捷匹配数据
     *
     * @param dayTimeData
     * @return
     */
    public PtgCrushNUDayVO getNewlyUserCrushData(DayTimeData dayTimeData, int gender, int app) {
        return new PtgCrushNUDayVO();
    }

    private List<PtgCrushLogData> filterPtgCrush(List<PtgCrushLogData> ptgCrushList, int user, int app, Set<String> uidSet, boolean validFrom) {
        if (user == -1 && app == -1) {
            return ptgCrushList;
        }
        uidSet = actorDao.filterActors(uidSet, user, AppUtils.getAppPackage(app), -1, null);
        List<PtgCrushLogData> list = new ArrayList<>();
        if (validFrom) {
            for (PtgCrushLogData ptgCrushLogData : ptgCrushList) {
                String uid = ptgCrushLogData.getUid();
                if (uidSet.contains(uid)) {
                    list.add(ptgCrushLogData);
                }
            }
        } else {
            for (PtgCrushLogData ptgCrushLogData : ptgCrushList) {
                String aid = ptgCrushLogData.getAid();
                if (uidSet.contains(aid)) {
                    list.add(ptgCrushLogData);
                }
            }
        }
        return list;
    }

    private Set<String> getSendMsgUser(List<MysqlMsgRecordData> msgList, Set<String> userSet) {
        Set<String> uidSet = new HashSet<>();
        for (MysqlMsgRecordData mysqlMsgRecordData : msgList) {
            String fromUid = mysqlMsgRecordData.getFromUid();
            String toUid = mysqlMsgRecordData.getToUid();
            if (userSet.contains(fromUid)) {
                uidSet.add(fromUid);
            } else if (userSet.contains(toUid)) {
                uidSet.add(toUid);
            }
        }
        return uidSet;
    }

    private Set<String> getMsgReplayUser(List<MysqlMsgRecordData> msgList, Set<String> fromUidSet) {
        Set<String> uidSet = new HashSet<>();
        for (MysqlMsgRecordData mysqlMsgRecordData : msgList) {
            String fromUid = mysqlMsgRecordData.getFromUid();
            if (uidSet.contains(fromUid)) {
                continue;
            }
            if (fromUidSet.contains(fromUid)) {
                uidSet.add(fromUid);
            }
        }
        return uidSet;
    }

    private Set<String> getMsgReplayUserPlug(List<MysqlMsgRecordData> msgList, Set<String> fromUidSet) {
        Set<String> uidSet = new HashSet<>();
        for (MysqlMsgRecordData mysqlMsgRecordData : msgList) {
            String fromUid = mysqlMsgRecordData.getFromUid();
            String toUid = mysqlMsgRecordData.getToUid();
            if (fromUidSet.contains(fromUid)) {
                uidSet.add(toUid);
            }
        }
        return uidSet;
    }


    private int userLoginNum(int start, int end, Set<String> uidSet) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return 0;
        }
        return dauStatDao.findOnlineUserCount("", start, end, uidSet);
    }

    private List<FriendSwipeInfo> getEventSwipes(List<FriendSwipeInfo> swipeList, int event, Set<String> uidSet, Set<String> aidSet) {
        Set<Integer> typeSet = new HashSet<>();
        if (event == MatchConstant.LIKE) {
            //喜欢
            typeSet.add(1);
            typeSet.add(-1);
            typeSet.add(0);
        } else if (event == MatchConstant.SUPER_LIKE) {
            //超级喜欢
            typeSet.add(3);
        } else if (event == MatchConstant.DISLIKE) {
            //不喜欢，skip
            typeSet.add(2);
        }
        List<FriendSwipeInfo> eventOpList = new ArrayList<>();
        for (FriendSwipeInfo stat : swipeList) {
            String uid = stat.getUid();
            String aid = stat.getAid();
            if (typeSet.contains(stat.getType()) && uidSet.contains(uid) && aidSet.contains(aid)) {
                eventOpList.add(stat);
            }
        }
        return eventOpList;
    }
}
