package com.quhong.operation.server;

import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.MonitorWarnConfigDao;
import com.quhong.mongo.data.MonitorWarnConfigData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.operation.share.condition.MonitorWarnCondition;
import com.quhong.operation.share.vo.MonitorWarnVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class MonitorWarnService {
    private static final Logger logger = LoggerFactory.getLogger(MonitorWarnService.class);

    @Resource
    private MonitorWarnConfigDao monitorWarnConfigDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;

    private String getSearchUid(String search,  int type){

        String searchUid = "";
        try {
            int searchRid = Integer.parseInt(search);
            ActorData actorData = actorDao.getActorByRid(searchRid);
            if(actorData != null){
                searchUid = actorData.getUid();
            }

            if(type == WhiteTestDao.WHITE_TYPE_ROOM_ID){
                searchUid = RoomUtils.formatRoomId(searchUid);
            }
            return searchUid;

        }catch (Exception e){
            if(search.length() == 24){
                return search;
            }
        }
        return searchUid;
    }


    public PageResultVO<MonitorWarnVO> getDataList(MonitorWarnCondition condition){
        PageResultVO<MonitorWarnVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int status = condition.getStatus();
        int warnType = condition.getWarnType();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<MonitorWarnConfigData> monitorWarnConfigDataList = monitorWarnConfigDao.selectPage(status, search, warnType, start, pageSize);

        List<MonitorWarnVO> voList = new ArrayList<>();
        for (MonitorWarnConfigData data : monitorWarnConfigDataList) {
            MonitorWarnVO vo = new MonitorWarnVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            vo.setTypeStrList(data.getTypeList().stream().map(Object::toString).collect(Collectors.joining(",")));

            if(!CollectionUtils.isEmpty(data.getIgnoreList())){
                List<Integer> ignoreList = new ArrayList<>();
                for (String ignoreUid : data.getIgnoreList()) {
                    ActorData actorData = actorDao.getActorDataFromCache(ignoreUid);
                    if(actorData == null){
                        continue;
                    }
                    ignoreList.add(actorData.getRid());
                }
                vo.setIgnoreStrList(ignoreList.stream().map(Object::toString).collect(Collectors.joining(",")));
            }
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(monitorWarnConfigDao.selectCount(status, search));
        return pageVO;
    }

    private List<Integer> getIntegerList(String strData){

        List<Integer> list = new ArrayList<>();
        try {
            strData = strData.trim().replace(" ", "").replace("'", "").replace("\n", "");
            String[] stringList = strData.split(",");
            for (String rid: stringList) {
                list.add(Integer.parseInt(rid));
            }
            return list;
        }catch (Exception e){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
    }

    public void addData(MonitorWarnVO dto){

        String typeStrData = dto.getTypeStrList();   // 多个type
        int baseWarnNum = dto.getBaseWarnNum();
        if(StringUtils.isEmpty(typeStrData) || baseWarnNum <= 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }


        MonitorWarnConfigData data = new MonitorWarnConfigData();
        BeanUtils.copyProperties(dto, data);

        String ignoreRidData = dto.getIgnoreStrList();   // 多个id
        List<Integer> typeList = getIntegerList(typeStrData);
        data.setTypeList(typeList);

        String ignoreStrList = dto.getIgnoreStrList();
        if(!StringUtils.isEmpty(ignoreStrList)){
            List<Integer> ignoreList = getIntegerList(ignoreStrList);
            List<String> ignoreUidList = new ArrayList<>();
            for (Integer ignoreRid : ignoreList) {
                ActorData actorData = actorDao.getActorByRid(ignoreRid);
                if(actorData == null){
                    continue;
                }
                ignoreUidList.add(actorData.getUid());
            }
            data.setIgnoreList(ignoreUidList);
        }
        monitorWarnConfigDao.save(data);
    }


    public void updateData(MonitorWarnVO dto) {
        String docId = dto.getDocId();
        MonitorWarnConfigData data = monitorWarnConfigDao.getDataByID(docId);
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        String typeStrData = dto.getTypeStrList();   // 多个type
        int baseWarnNum = dto.getBaseWarnNum();
        if(StringUtils.isEmpty(typeStrData) || baseWarnNum <= 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        String ignoreRidData = dto.getIgnoreStrList();   // 多个id
        List<Integer> typeList = getIntegerList(typeStrData);

        Update update = new Update();
        update.set("warnTitle", dto.getWarnTitle());
        update.set("warnType", dto.getWarnType());
        update.set("timeType", dto.getWarnType());
        update.set("typeList", typeList);
        update.set("baseWarnNum", dto.getBaseWarnNum());
        update.set("incrWarnNum", dto.getIncrWarnNum());
        update.set("warnDescription", dto.getWarnDescription());
        update.set("status", dto.getStatus());


        String ignoreStrList = dto.getIgnoreStrList();
        if(!StringUtils.isEmpty(ignoreStrList)){
            List<Integer> ignoreList = getIntegerList(ignoreStrList);
            List<String> ignoreUidList = new ArrayList<>();
            for (Integer ignoreRid : ignoreList) {
                ActorData actorData = actorDao.getActorByRid(ignoreRid);
                if(actorData == null){
                    continue;
                }
                ignoreUidList.add(actorData.getUid());
            }
            update.set("ignoreList", ignoreUidList);
        }
        monitorWarnConfigDao.updateData(data, update);
    }

    public void deleteData(MonitorWarnVO dto) {
        monitorWarnConfigDao.delete(dto.getDocId());
    }

}
