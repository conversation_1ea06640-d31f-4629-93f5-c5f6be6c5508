package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.constant.WelcomeMessageReviewConstant;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.WelcomeMessageReviewDao;
import com.quhong.mysql.data.WelcomeMessageReviewData;
import com.quhong.operation.share.condition.WelcomeMessageCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.OpWelcomeMessageReviewVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 房间欢迎消息审核操作服务类
 *
 * <AUTHOR>
 * @date 2025/8/25 16:18
 */
@Service
public class WelcomeMessageReviewService {
    private static final Logger logger = LoggerFactory.getLogger(WelcomeMessageReviewService.class);
    @Resource
    private WelcomeMessageReviewDao welcomeMessageReviewDao;
    @Resource
    private ActorDao actorDao;

    /**
     * 分页获取欢迎消息审核数据
     */
    public PageResultVO<OpWelcomeMessageReviewVO> selectPageList(WelcomeMessageCondition condition) {
        // 参数校验
        if (condition.getPage() == null || condition.getPage() < 1) {
            condition.setPage(1);
        }
        if (condition.getPageSize() == null || condition.getPageSize() < 1) {
            condition.setPageSize(20);
        }

        List<WelcomeMessageReviewData> pageList = welcomeMessageReviewDao.selectPageList(
                condition.getPage(), condition.getPageSize(), condition);

        List<OpWelcomeMessageReviewVO> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pageList)) {
            for (WelcomeMessageReviewData data : pageList) {
                OpWelcomeMessageReviewVO vo = new OpWelcomeMessageReviewVO();
                BeanUtils.copyProperties(data, vo);
                if (StringUtils.hasText(data.getRoomId())) {
                    ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(data.getRoomId()));
                    vo.setRid(actorData.getRid() + "");
                }
                voList.add(vo);
            }
        }
        long total = welcomeMessageReviewDao.selectCount(condition);
        return new PageResultVO<>(total, voList);
    }


    /**
     * 插入欢迎消息审核数据
     */
    public void insert(WelcomeMessageReviewData data) {
        welcomeMessageReviewDao.insert(data);
    }

    /**
     * 批量插入欢迎消息审核数据
     */
    public void batchInsert(String roomId, List<WelcomeMessageReviewData> insertList) {
        welcomeMessageReviewDao.batchInsert(roomId, insertList);
    }

    /**
     * 修改审核状态
     */
    public void updateReviewAction(String operatorUid, WelcomeMessageCondition condition) {
        checkUpdateReviewActionParam(operatorUid, condition);

        // 查询当前记录状态
        WelcomeMessageReviewData currentData = welcomeMessageReviewDao.selectById(condition.getRoomId(), condition.getId());
        if (currentData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "记录不存在");
        }

        // 已通过的不能再修改为其他状态
        if (WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED.equals(currentData.getReviewAction())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "已通过的记录不能再修改状态");
        }

        welcomeMessageReviewDao.updateReviewAction(condition.getRoomId(), condition.getId(), operatorUid,
                condition.getReviewAction(), condition.getRejectReason());

        logger.info("更新欢迎消息审核状态成功，roomId={}, id={}, operatorUid={}, reviewAction={}",
                condition.getRoomId(), condition.getId(), operatorUid, condition.getReviewAction());
    }

    /**
     * 更新欢迎消息
     */
    public void updateContent(String roomId, Integer id, String messageContent) {
        welcomeMessageReviewDao.updateContent(roomId, id, messageContent);
    }

    /**
     * 删除欢迎消息
     */
    public void delete(String roomId, Integer id) {
        welcomeMessageReviewDao.delete(roomId, id);
    }


    /**
     * 校验更新审核状态的参数
     */
    private void checkUpdateReviewActionParam(String operatorUid, WelcomeMessageCondition condition) {
        if (!StringUtils.hasText(operatorUid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "操作人uid不能为空");
        }

        if (!StringUtils.hasText(condition.getRoomId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "房间ID不能为空");
        }

        if (condition.getId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "记录ID不能为空");
        }

        if (condition.getReviewAction() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "审核状态不能为空");
        }

        // 校验审核状态值
        if (!WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING.equals(condition.getReviewAction()) &&
            !WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED.equals(condition.getReviewAction()) &&
            !WelcomeMessageReviewConstant.REVIEW_STATUS_REJECTED.equals(condition.getReviewAction())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "审核状态值无效");
        }

        // 状态为拒绝时必须设置原因，原因小于50个字符
        if (WelcomeMessageReviewConstant.REVIEW_STATUS_REJECTED.equals(condition.getReviewAction())) {
            if (!StringUtils.hasText(condition.getRejectReason())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "拒绝时必须设置拒绝原因");
            }
            if (condition.getRejectReason().length() > WelcomeMessageReviewConstant.REJECT_REASON_MAX_LENGTH) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(),
                        "拒绝原因不能超过" + WelcomeMessageReviewConstant.REJECT_REASON_MAX_LENGTH + "个字符");
            }
        }
    }

    /**
     * 校验基本参数
     */
    private void checkParam(WelcomeMessageCondition condition) {
        if (condition.getId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "id不能为空");
        }
        if (condition.getReviewAction() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "审核状态不能为空");
        }
    }


}
