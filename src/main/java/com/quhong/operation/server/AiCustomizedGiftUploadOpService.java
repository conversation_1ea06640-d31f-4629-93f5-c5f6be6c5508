package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.AiCustomizedGiftUploadDao;
import com.quhong.mysql.data.AiCustomizedGiftUploadData;
import com.quhong.operation.share.dto.AiCustomizedGiftUploadDTO;
import com.quhong.operation.share.dto.SharingOfficerDTO;
import com.quhong.operation.share.vo.AiCustomizedGiftUploadOpVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class AiCustomizedGiftUploadOpService {

    private static final Logger logger = LoggerFactory.getLogger(AiCustomizedGiftUploadOpService.class);

    @Resource
    private AiCustomizedGiftUploadDao aiCustomizedGiftUploadDao;

    @Resource
    private ActorDao actorDao;

    /**
     * 分页查询AI定制礼物上传记录
     */
    public PageResultVO<AiCustomizedGiftUploadOpVO> aiCustomizedGiftUploadList(AiCustomizedGiftUploadDTO dto) {
        AiCustomizedGiftUploadData filterData = new AiCustomizedGiftUploadData();


        if (!StringUtils.isEmpty(dto.getSearch())) {
            ActorData actorData = actorDao.getActorByStrRid(dto.getSearch());
            if (actorData == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), String.format("搜索用户不存在:{}", dto.getSearch()));
            }
            filterData.setUid(actorData.getUid());
        }

        if (dto.getState() != null&&dto.getState() != -1) {
            filterData.setState(dto.getState());
        }

        if (!StringUtils.isEmpty(dto.getMonth())) {
            filterData.setMonth(dto.getMonth());
        }

        IPage<AiCustomizedGiftUploadData> iPage = aiCustomizedGiftUploadDao.selectPage(filterData, dto.getPage(), dto.getPageSize());
        List<AiCustomizedGiftUploadData> dataList = iPage.getRecords();
        List<AiCustomizedGiftUploadOpVO> voList = new ArrayList<>();

        for (AiCustomizedGiftUploadData item : dataList) {
            AiCustomizedGiftUploadOpVO itemVO = new AiCustomizedGiftUploadOpVO();
            BeanUtils.copyProperties(item, itemVO);

            // 获取用户信息
            ActorData actorData = actorDao.getActorDataFromCache(item.getUid());
            if (actorData != null) {
                itemVO.setUserRid(actorData.getStrRid());
            }

            // 设置状态描述
            itemVO.setStateDesc(AiCustomizedGiftUploadOpVO.getStateDescription(item.getState()));

            voList.add(itemVO);
        }

        PageResultVO<AiCustomizedGiftUploadOpVO> pageVO = new PageResultVO<>();
        pageVO.setList(voList);
        pageVO.setTotal(iPage.getTotal());
        return pageVO;
    }

    /**
     * 新增AI定制礼物上传记录
     */
    public void aiCustomizedGiftUploadAdd(AiCustomizedGiftUploadDTO dto) {
        if (StringUtils.isEmpty(dto.getUid()) || StringUtils.isEmpty(dto.getImageUrl()) || StringUtils.isEmpty(dto.getMonth())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误：uid、imageUrl、month不能为空");
        }

        int now = DateHelper.getNowSeconds();
        AiCustomizedGiftUploadData data = new AiCustomizedGiftUploadData();
        data.setUid(dto.getUid());
        data.setImageUrl(dto.getImageUrl());
        data.setMonth(dto.getMonth());
        data.setState(dto.getState() != null ? dto.getState() : AiCustomizedGiftUploadDao.STATE_PENDING);
        data.setDescNotice(dto.getDescNotice());
        data.setCtime(now);
        data.setMtime(now);

        aiCustomizedGiftUploadDao.insert(data);
        logger.info("新增AI定制礼物上传记录成功，uid: {}, month: {}", dto.getUid(), dto.getMonth());
    }


    public void aiCustomizedGiftUploadUpdateAll(String adminUid, AiCustomizedGiftUploadDTO dto) {
        if (CollectionUtils.isEmpty(dto.getRidList())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误");
        }
        for (Integer rid : dto.getRidList()) {
            AiCustomizedGiftUploadDTO oneDto = new AiCustomizedGiftUploadDTO();
            oneDto.setRid(rid);
            oneDto.setState(dto.getState());
            aiCustomizedGiftUploadUpdate(adminUid, oneDto);
        }
    }

    /**
     * 更新AI定制礼物上传记录状态
     */
    public void aiCustomizedGiftUploadUpdate(String adminUid, AiCustomizedGiftUploadDTO dto) {
        if (dto.getRid() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误：rid不能为空");
        }

        AiCustomizedGiftUploadData data = aiCustomizedGiftUploadDao.selectOneById(dto.getRid());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "记录不存在");
        }

        boolean isChange = false;

        // 状态更新逻辑：已经通过的不能再更改
        if (dto.getState() != null && data.getState() != AiCustomizedGiftUploadDao.STATE_APPROVED) {
            isChange = true;
            if (data.getState() == AiCustomizedGiftUploadDao.STATE_PENDING && dto.getState() == AiCustomizedGiftUploadDao.STATE_REJECTED) {
                data.setFailCount(data.getFailCount() == null ? 1 : data.getFailCount() + 1);
            }
            logger.info("更新AI定制礼物状态，rid: {}, 原状态: {}, 新状态: {} 失败次数: {}", dto.getRid(), data.getState(), dto.getState(), data.getFailCount());
            data.setState(dto.getState());
        } else if (dto.getState() != null && data.getState() == AiCustomizedGiftUploadDao.STATE_APPROVED) {
            logger.info("已通过的记录不能再更改状态，rid: {}", dto.getRid());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "已通过的记录不能再更改状态");
        }

        // 更新备注
        if (dto.getDescNotice() != null) {
            data.setDescNotice(dto.getDescNotice());
            isChange = true;
        }

        if (isChange) {
            data.setMtime(DateHelper.getNowSeconds());
            aiCustomizedGiftUploadDao.update(data);
            logger.info("更新AI定制礼物上传记录成功，rid: {}, adminUid: {}", dto.getRid(), adminUid);
        }
    }

    /**
     * 根据ID查询单条记录
     */
    public AiCustomizedGiftUploadOpVO getAiCustomizedGiftUploadById(Integer rid) {
        if (rid == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误：rid不能为空");
        }

        AiCustomizedGiftUploadData data = aiCustomizedGiftUploadDao.selectOneById(rid);
        if (data == null) {
            return null;
        }

        AiCustomizedGiftUploadOpVO vo = new AiCustomizedGiftUploadOpVO();
        BeanUtils.copyProperties(data, vo);

        // 获取用户信息
        ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
        if (actorData != null) {
            vo.setUserRid(actorData.getStrRid());
        }

        // 设置状态描述
        vo.setStateDesc(AiCustomizedGiftUploadOpVO.getStateDescription(data.getState()));

        return vo;
    }
}
