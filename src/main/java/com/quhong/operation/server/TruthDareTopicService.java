package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.dao.TruthDareTopicDao;
import com.quhong.mysql.data.TruthDareTopicData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.TruthDareCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Service
public class TruthDareTopicService {
    private static final Logger logger = LoggerFactory.getLogger(TruthDareTopicService.class);

    @Resource
    private TruthDareTopicDao truthDareTopicDao;


    public PageResultVO<TruthDareTopicData> truthDareTopicList(TruthDareCondition condition) {
        PageResultVO<TruthDareTopicData> pageVO = new PageResultVO<>();
        int status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        IPage<TruthDareTopicData> iPageData = truthDareTopicDao.selectPageList(search, status, condition.getTopicType(), page, pageSize);
        pageVO.setList(iPageData.getRecords());
        pageVO.setTotal(iPageData.getTotal());
        return pageVO;
    }


    private void paramCheck(TruthDareTopicData dto){
        String nameEn = dto.getNameEn();
        String nameAr = dto.getNameAr();
        if (ObjectUtils.isEmpty(nameEn) || ObjectUtils.isEmpty(nameAr)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "话题内容未填写");
        }
        nameEn = nameEn.trim();
        nameAr = nameAr.trim();

        if (nameEn.length() == 0 || nameEn.length() >= 300) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "话题内容未填写或大于字符长度上限(300)");
        }

        if (nameAr.length() == 0 || nameAr.length() >= 300) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "话题内容未填写或大于字符长度上限(300)");
        }
        dto.setNameEn(nameEn);
        dto.setNameAr(nameAr);
    }

    public void addTruthDareTopic(TruthDareTopicData dto) {
        paramCheck(dto);
        TruthDareTopicData data = new TruthDareTopicData();
        BeanUtils.copyProperties(dto, data);
        data.setMtime(DateHelper.getNowSeconds());
        data.setCtime(DateHelper.getNowSeconds());
        truthDareTopicDao.insert(data);
    }

    public void updateTruthDareTopic(TruthDareTopicData dto) {
        TruthDareTopicData data = truthDareTopicDao.selectById(dto.getId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "数据不存在");
        }
        paramCheck(dto);

        BeanUtils.copyProperties(dto, data);
        truthDareTopicDao.update(data);
    }

}
