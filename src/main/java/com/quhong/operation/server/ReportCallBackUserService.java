package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.MomentTopicOperateRecordEvent;
import com.quhong.analysis.StaffBindUserNewEvent;
import com.quhong.config.AsyncConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.ActorLastLoginData;
import com.quhong.data.vo.PopularListNewVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.BadgeListData;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.operation.country.CountryQuery;
import com.quhong.operation.share.condition.ReportCallBackUserCondition;
import com.quhong.operation.share.condition.RookieRoomUserConfigCondition;
import com.quhong.operation.share.condition.StaffBindUserCondition;
import com.quhong.operation.share.data.CountryData;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ReportCallBackUserVO;
import com.quhong.operation.share.vo.RookieRoomConfigVO;
import com.quhong.operation.utils.StringUtil;
import com.quhong.redis.GiftPlayRedis;
import com.quhong.redis.NewRookieRoomRedis;
import com.quhong.redis.OperationCommonRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.PageUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.video.data.NaviVideoData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ReportCallBackUserService {
    private static final Logger logger = LoggerFactory.getLogger(ReportCallBackUserService.class);
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final List<String> VALID_CAMPAIGN = Arrays.asList("unknow", "organic", "(none)");

    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private StaffNewDao staffNewDao;
    @Resource
    private RoomUserOnlineDao roomUserOnlineDao;
    @Resource
    private StaffBindUserNewDao staffBindUserNewDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private OperationCommonRedis operationCommonRedis;
    @Autowired
    private CountryQuery countryQuery;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private ActorCampaignDao actorCampaignDao;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private GiftPlayRedis giftPlayRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private EventReport eventReport;

    public PageResultVO<StaffBindUserRechargeJoinData> staffBindUserRechargeList(StaffBindUserCondition condition) {
        PageResultVO<StaffBindUserRechargeJoinData> pageVO = new PageResultVO<>();

        Integer[] timeArr = com.quhong.operation.utils.DateHelper.ARABIAN.getStartOrEndSeconds(condition.getStartDate(), condition.getEndDate());
        int startTime = timeArr[0];
        int endTime = --timeArr[1];
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        String searchUid = null;
        if (!StringUtil.isEmpty(condition.getSearch())) {
            ActorData actor = actorDao.getActorByStrRid(condition.getSearch());
            if (actor == null) {
                return pageVO;
            }
            searchUid = actor.getUid();
        }

        int currentTime = DateHelper.getNowSeconds();
        int last30DayTime = currentTime - 30 * 86400;
        int last60DayTime = currentTime - 60 * 86400;
        int last90DayTime = currentTime - 90 * 86400;

        List<StaffBindUserRechargeJoinData> dataList = staffBindUserNewDao.getStaffBindUserRechargeJoinData(
                startTime, endTime, searchUid, condition.getStaffId(), condition.getStatus(),
                condition.getBindType(), last30DayTime, last60DayTime, last90DayTime, start, pageSize,
                condition.getSortCtime(), condition.getLast30DaySort(), condition.getLast60DaySort(), condition.getLast90DaySort(), condition.getTotalDaySort());

        List<StaffBindUserRechargeJoinData> voList = new ArrayList<>();
        Map<Integer, StaffNewData> staffNewDataMap = staffNewDao.selectAllByStatusCache(0).stream().collect(Collectors.toMap(StaffNewData::getId, Function.identity()));
        for (StaffBindUserRechargeJoinData data : dataList) {

            try {
                StaffBindUserRechargeJoinData vo = new StaffBindUserRechargeJoinData();
                BeanUtils.copyProperties(data, vo);
                StaffNewData staffNewData = staffNewDataMap.get(data.getStaffId());
                vo.setStaffName(staffNewData != null ? staffNewData.getName() : "");

                ActorData actor = actorDao.getActorDataFromCache(data.getUid());
                vo.setUserId(actor.getStrRid());

                // 注册时间
                int registerTime = new ObjectId(actor.getUid()).getTimestamp();
                if (0 != registerTime) {
                    vo.setRegisterTime(com.quhong.operation.utils.DateHelper.ARABIAN.dateToStr(new Date(registerTime * 1000L)));
                }

                // IP国家
                CountryData countryData = countryQuery.find(actor.getIp());
                if (countryData != null) {
                    vo.setIpCountry(countryData.getCountry());
                }

                // 最后一次登录时间
                ActorLastLoginData lastLogin = actor.getLastLogin();
                if (null != lastLogin && null != lastLogin.getLogoutTime() && 0 != lastLogin.getLogoutTime()) {
                    vo.setLogoutTime(com.quhong.operation.utils.DateHelper.ARABIAN.dateToStr(new Date(lastLogin.getLogoutTime() * 1000L)));
                }

                voList.add(vo);
            } catch (Exception e) {
                logger.error("staffBindUserRechargeList error:{}", JSONObject.toJSONString(data));
                continue;
            }
        }

        pageVO.setList(voList);
        pageVO.setTotal(staffBindUserNewDao.getStaffBindUserRechargeJoinDataCount(
                startTime, endTime, searchUid, condition.getStaffId(), condition.getStatus(),
                condition.getBindType(), last30DayTime, last60DayTime, last90DayTime));
        return pageVO;
    }

    public PageResultVO<StaffBindUserSummaryJoinData> staffBindUserSummaryList(StaffBindUserCondition condition) {
        PageResultVO<StaffBindUserSummaryJoinData> pageVO = new PageResultVO<>();

        Integer[] timeArr = com.quhong.operation.utils.DateHelper.ARABIAN.getStartOrEndSeconds(condition.getStartDate(), condition.getEndDate());
        int startTime = timeArr[0];
        int endTime = --timeArr[1];
        int currentTime = DateHelper.getNowSeconds();
        int last30DayTime = currentTime - 30 * 86400;
        int last60DayTime = currentTime - 60 * 86400;
        int last90DayTime = currentTime - 90 * 86400;

        List<StaffBindUserSummaryJoinData> dataList = staffBindUserNewDao.getStaffBindUserSummaryJoinData(startTime, endTime,
                condition.getStaffId(), condition.getStatus(), condition.getBindType(), last30DayTime, last60DayTime, last90DayTime);

        List<StaffBindUserSummaryJoinData> voList = new ArrayList<>();
        Map<Integer, StaffNewData> staffNewDataMap = staffNewDao.selectAllByStatusCache(0).stream().collect(Collectors.toMap(StaffNewData::getId, Function.identity()));
        String staffName = "全部";
        if (condition.getStaffId() > 0) {
            StaffNewData staffNewData = staffNewDataMap.get(condition.getStaffId());
            staffName = staffNewData != null ? staffNewData.getName() : "全部";
        }

        StaffBindUserSummaryJoinData totalSummary = new StaffBindUserSummaryJoinData();
        totalSummary.setRecordDay("阶段汇总");
        totalSummary.setStaffName(staffName);

        for (StaffBindUserSummaryJoinData data : dataList) {

            try {
                StaffBindUserSummaryJoinData vo = new StaffBindUserSummaryJoinData();
                BeanUtils.copyProperties(data, vo);
                vo.setStaffName(staffName);
                totalSummary.setTotalUser(totalSummary.getTotalUser() + data.getTotalUser());
                totalSummary.setTotalRechargeUser(totalSummary.getTotalRechargeUser() + data.getTotalRechargeUser());
                totalSummary.setTotalLast30Day(totalSummary.getTotalLast30Day() + data.getTotalLast30Day());
                totalSummary.setTotalBt30DayTo60Day(totalSummary.getTotalBt30DayTo60Day() + data.getTotalBt30DayTo60Day());
                totalSummary.setTotalBt60DayTo90Day(totalSummary.getTotalBt60DayTo90Day() + data.getTotalBt60DayTo90Day());
                voList.add(vo);
            } catch (Exception e) {
                logger.error("staffBindUserSummaryList error:{}", JSONObject.toJSONString(data));
                continue;
            }
        }

        voList.add(0, totalSummary);
        pageVO.setList(voList);
        pageVO.setTotal(1);
        return pageVO;
    }


    public ReportCallBackUserVO preAdd(ReportCallBackUserCondition dto) {
        if (!StringUtils.hasLength(dto.getRids()) && CollectionUtils.isEmpty(dto.getRidList())) {
            throw new CommonH5Exception(new HttpCode(1, "rid不能为空"));
        }
        checkParam(dto);
        checkResExist(dto);
        Set<String> ridSet;
        if (StringUtils.hasLength(dto.getRids())) {
            String[] rids = dto.getRids().trim().replace("，", ",").split(",");
            int ridsLength = rids.length;
            ridSet = new HashSet<>(Arrays.asList(rids));
            logger.info("rids.length:{} ridSet.size():{} ", ridsLength, ridSet.size());
        } else {
            ridSet = new HashSet<>(dto.getRidList());
        }
        if (ridSet.size() > 50) {
            throw new CommonH5Exception(new HttpCode(1, "一次上传名单数不能大于50"));
        }
        int bindType = dto.getBindType();

        StringBuilder errorMsgSb = new StringBuilder();

        Map<String, List<String>> tnIdMapUidList = new HashMap<>();

        Map<String, String> uidMapTnId = new HashMap<>();

        Map<String, List<ActorCampaignData>> uidCampaignListMap = new HashMap<>();

        // 1 召回-近2个月设备（本账号或者设备其他账号）活跃累计超过180分钟
        // 2 召回-近2个月设备有其他账号充值
        // 8 拓新-设备已绑定其他账号
        // 10 拓新-当前设备关联数大于1(已经不统计)
        // 13 拓新-当前设备有超过30天前注册的账号
        Map<String, String> tnIdMapReason = new HashMap<>();

        // 3  召回-账号注册时间小于2个月
        // 12 召回-是机器人或者测试账号
        // 7  拓新-账号注册时间大于1个月
        // 9  拓新-是广告投放的用户
        // 11 拓新-是机器人或者测试账号
        Map<String, String> uidMapReason = new HashMap<>();


        synchronized (stringPool.intern("lock:callBack:new:preAdd")) {
            for (String strRid : ridSet) {
                try {
                    ActorData actorData = actorDao.getActorByStrRid(strRid);
                    if (null == actorData) {
                        errorMsgSb.append("rid:").append(strRid).append("找不到对应的用户,请更改后重新验证 \n");
                        continue;
                    }
                    String uid = actorData.getUid();
                    StaffBindUserNewData staffBindUserNewData = staffBindUserNewDao.selectOneByUid
                            (uid, dto.getBindType(), 1);
                    if (staffBindUserNewData != null) {
                        errorMsgSb.append("rid:").append(strRid).append("账号已经被绑定,请更改后重新验证 \n");
                        continue;
                    }

                    if (bindType == StaffBindUserNewDao.BIND_BACK_TYPE) {
                        if (ActorUtils.isNewRegisterActor(uid, 60)) {
                            uidMapReason.put(uid, "3");
                        }
                        if (actorData.getRobot() >= 1) {
                            uidMapReason.put(uid, getPreRecord(uidMapReason.get(uid), "12"));
                        }
                    } else {
                        if (!ActorUtils.isNewRegisterActor(uid, 30)) {
                            uidMapReason.put(uid, "7");
                        }
                        if (actorData.getRobot() >= 1) {
                            uidMapReason.put(uid, getPreRecord(uidMapReason.get(uid), "11"));
                        }
                    }

                    if (StringUtils.hasLength(actorData.getTn_id())) {
                        List<String> templateList = tnIdMapUidList.computeIfAbsent(actorData.getTn_id(), k -> new ArrayList<>());
                        templateList.add(uid);
                    }
                    // 可能一个号同时录入了靓号与普通号，这里以uid为key保存下
                    String toTnId = uidMapTnId.get(actorData.getUid());
                    if (StringUtils.hasLength(toTnId)) {
                        logger.info("same device uid:{} strRid:{} tn_id:{}", actorData.getUid(), strRid, actorData.getTn_id());
                    } else {
                        uidMapTnId.put(actorData.getUid(), actorData.getTn_id());
                    }
//                    uidMapTnId.compute(actorData.getUid(), (k, v) -> {
//                        if (null != v) {
//                            logger.info("same uid:{} strRid:{} tn_id:{}", actorData.getUid(), strRid, actorData.getTn_id());
//                        }
//                        return actorData.getTn_id();
//                    });

                } catch (Exception e) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "账号输入有误");
                }
            }
            String errorMsg = errorMsgSb.toString();
            if (StringUtils.hasLength(errorMsg)) {
                throw new CommonH5Exception(new HttpCode(1, errorMsg));
            }
            if (CollectionUtils.isEmpty(uidMapTnId.keySet())) {
                throw new CommonH5Exception(new HttpCode(1, "没有找到有效的用户"));
            }
            Set<String> allUid = uidMapTnId.keySet();
            logger.info("valid user count=uidMapTnId.size:{} valid device count=tnIdMapUid.size:{} allUid:{}",
                    uidMapTnId.size(), tnIdMapUidList.size(), allUid);
            List<ActorCampaignData> actorCampaignDataList = actorCampaignDao.selectListByUidList(allUid);
            if (!CollectionUtils.isEmpty(actorCampaignDataList)) {
                uidCampaignListMap = actorCampaignDataList.stream().collect(Collectors.groupingBy(ActorCampaignData::getUid));
                logger.info(" valid campaign count=Campaign.size:{}", actorCampaignDataList.size());
            }

            if (bindType == StaffBindUserNewDao.BIND_NEW_TYPE) {
                for (String itemTnId : tnIdMapUidList.keySet()) {
                    List<StaffBindUserNewData> staffBindUserNewDataList = staffBindUserNewDao.selectListByTnId
                            (itemTnId, dto.getBindType(), 1);
                    if (!CollectionUtils.isEmpty(staffBindUserNewDataList)) {
                        tnIdMapReason.compute(itemTnId, (k, v) -> getPreRecord(v, "8"));
                    }
                }
                List<MongoActorData> allActorList = actorDao.getActorListByTnIdList(tnIdMapUidList.keySet());
                Map<String, List<MongoActorData>> tnIdActorListMap =
                        allActorList.stream().collect(Collectors.groupingBy(MongoActorData::getTn_id));
                logger.info("device to all actor count=allActorList.size:{} " +
                        "valid device count=tnIdActorListMap.size:{}", allActorList.size(), tnIdActorListMap.size());
                for (Map.Entry<String, List<MongoActorData>> entry : tnIdActorListMap.entrySet()) {
                    String tnId = entry.getKey();
                    List<MongoActorData> actorList = entry.getValue();
//                    if (actorList.size() > 1) {
//                        tnIdMapReason.compute(tnId, (k, v) -> getPreRecord(v, "10"));
//                    }
                    long actorBeforeOneMonthCount = actorList.stream()
                            .filter(k -> {
                                String toUid = k.get_id().toString();
                                // 30天前注册的账号数
                                return !ActorUtils.isNewRegisterActor(toUid, 30);
                            }).count();
                    if (actorBeforeOneMonthCount >= 1) {
                        tnIdMapReason.compute(tnId, (k, v) -> getPreRecord(v, "13"));
                    }

                }
            } else {
                List<MongoActorData> allActorList = actorDao.getActorListByTnIdList(tnIdMapUidList.keySet());
                Map<String, List<MongoActorData>> tnIdActorListMap =
                        allActorList.stream().collect(Collectors.groupingBy(MongoActorData::getTn_id));
                logger.info("device to all actor count=allActorList.size:{} " +
                        "valid device count=tnIdActorListMap.size:{}", allActorList.size(), tnIdActorListMap.size());

                // 获取今天0点整的时间戳
                int endTime = DateHelper.DEFAULT.stringDateToStampSecond(DateHelper.DEFAULT.formatDateInDay()) - (int) TimeUnit.DAYS.toSeconds(15);
                int startTime = endTime - (int) TimeUnit.DAYS.toSeconds(60);

                List<CompletableFuture<Boolean>> taskList = new ArrayList<>();

                for (Map.Entry<String, List<MongoActorData>> entry : tnIdActorListMap.entrySet()) {
                    String tnId = entry.getKey();
                    List<MongoActorData> actorList = entry.getValue();
                    List<String> actorUidList = actorList.stream().map(k -> k.get_id().toString())
                            .collect(Collectors.toList());
                    taskList.add(supplyAsync(tnId, actorUidList, startTime, endTime, tnIdMapReason, tnIdMapUidList));
                }
                for (CompletableFuture<Boolean> result : taskList) {
                    try {
                        Boolean done = result.get(40, TimeUnit.SECONDS);
                    } catch (InterruptedException | ExecutionException | TimeoutException e) {
                        logger.error("result msg {} ", e.getMessage(), e);
                    }
                }
            }
        }

        // VO输出
        ReportCallBackUserVO vo = new ReportCallBackUserVO();
        List<ReportCallBackUserVO.ReasonVO> reasonList = new ArrayList<>();
        Set<String> successAddTnIdSet = new HashSet<>();
        for (Map.Entry<String, String> entry : uidMapTnId.entrySet()) {
            String itemUid = entry.getKey();
            String itemTnId = entry.getValue();
            String reason = null;
            int status = 1;

            String tnReason = tnIdMapReason.get(itemTnId);
            String uidReason = uidMapReason.get(itemUid);
            List<ActorCampaignData> actorCampaignDataList = uidCampaignListMap.get(itemUid);
            String medium = getMedium(actorCampaignDataList);

            if (StringUtils.hasLength(tnReason)) {
                reason = getPreRecord(reason, tnReason);
            }
            if (StringUtils.hasLength(uidReason)) {
                reason = getPreRecord(reason, uidReason);
            }
            if (bindType == StaffBindUserNewDao.BIND_NEW_TYPE && !VALID_CAMPAIGN.contains(medium)) {
                reason = getPreRecord(reason, "9");
            }
            if (StringUtils.hasLength(reason)) {
                status = 0;
            }
            // 拓新，同一批用户里面的设备，每个设备只能加一个用户
            if (bindType == StaffBindUserNewDao.BIND_NEW_TYPE && successAddTnIdSet.contains(itemTnId) && status == 1) {
                reason = getPreRecord(reason, "8");
                status = 0;
                logger.info("device only one user bindType:{} itemUid:{} itemTnId:{} reason:{} continue",
                        bindType, itemUid, itemTnId, reason);
                continue;
            }
            successAddTnIdSet.add(itemTnId);
            ReportCallBackUserVO.ReasonVO reasonVO = new ReportCallBackUserVO.ReasonVO();
            ActorData actorData = actorDao.getActorDataFromCache(itemUid);
            reasonVO.setRid(actorData.getStrRid());
            reasonVO.setUid(itemUid);
            reasonVO.setTnId(itemTnId);
            reasonVO.setStatus(status);
            reasonVO.setInvalidReason(reason);
            reasonVO.setMedium(medium);
            reasonList.add(reasonVO);
        }

        String objId = new ObjectId() + "-" + dto.getBindType();
        operationCommonRedis.setCommonStrValue(objId, JSON.toJSONString(reasonList));
        vo.setAddSubmitId(objId);
        vo.setReasonList(reasonList);
        logger.info("preAdd success objId:{} reasonList.size():{}", objId, reasonList.size());
        return vo;
    }


    public void add(ReportCallBackUserCondition dto) {
        if (StringUtils.isEmpty(dto.getAddSubmitId())) {
            throw new CommonH5Exception(new HttpCode(1, "addSubmitId不能为空"));
        }
        checkParam(dto);
        checkResExist(dto);
        String jsonValue = operationCommonRedis.getCommonStrValue
                (dto.getAddSubmitId());
        if (StringUtils.isEmpty(jsonValue)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "验证数据已经过期，请重新验证再添加");
        }
        List<ReportCallBackUserVO.ReasonVO> reasonList = JSON.parseArray(jsonValue, ReportCallBackUserVO.ReasonVO.class);
        if (CollectionUtils.isEmpty(reasonList)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "验证数据已经过期，请重新验证再添加");
        }
        List<StaffBindUserNewData> sqlDatalist = new ArrayList<>();
        String recordDay = DateHelper.ARABIAN.formatDateInDay2(); // yyyyMMdd
        int nowTime = DateHelper.getNowSeconds();
        synchronized (stringPool.intern("lock:callBack:new:preAdd")) {
            for (ReportCallBackUserVO.ReasonVO item : reasonList) {
                StaffBindUserNewData itemVO = new StaffBindUserNewData();
                BeanUtils.copyProperties(item, itemVO);
                itemVO.setStaffId(dto.getStaffId());
                itemVO.setRecordDay(Integer.parseInt(recordDay));
                itemVO.setBindType(dto.getBindType());
                itemVO.setCtime(nowTime);
                sqlDatalist.add(itemVO);
                reportStaffBindUserNewEvent(itemVO);
            }
            if (!CollectionUtils.isEmpty(sqlDatalist)) {
                logger.info("add success addSubmitId:{} sqlDatalist:{}", dto.getAddSubmitId(), sqlDatalist.size());
                staffBindUserNewDao.batchInsertOrUpdate(sqlDatalist);
                operationCommonRedis.deleteCommonStrValue(dto.getAddSubmitId());
            } else {
                logger.info("add fail is empty addSubmitId:{} ", dto.getAddSubmitId());
            }

        }

    }

    public ReportCallBackUserVO getSameByDevice(ReportCallBackUserCondition dto) {
        if (dto.getTnId() == null) {
            throw new CommonH5Exception(new HttpCode(1, "tnId参数错误"));
        }
        List<StaffBindUserNewData> staffBindUserNewDataList = staffBindUserNewDao.selectListByTnId
                (dto.getTnId(), StaffBindUserNewDao.BIND_NEW_TYPE, 1);
        List<ReportCallBackUserVO.UserVO> userList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(staffBindUserNewDataList)) {
            for (StaffBindUserNewData item : staffBindUserNewDataList) {
                String uid = item.getUid();
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                ReportCallBackUserVO.UserVO userVO = new ReportCallBackUserVO.UserVO();
                userVO.setName(actorData.getName());
                userVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                userVO.setUid(uid);
                userVO.setRid(actorData.getStrRid());
                userVO.setRegTime(new ObjectId(uid).getTimestamp());
                userList.add(userVO);
            }
        }
        ReportCallBackUserVO vo = new ReportCallBackUserVO();
        vo.setUserList(userList);
        return vo;
    }

    private String getPreRecord(String old, String addChar) {
        if (StringUtils.isEmpty(old)) {
            return addChar;
        } else {
            return old + "," + addChar;
        }
    }

    public CompletableFuture<Boolean> supplyAsync(String tnId, List<String> actorUidList, int startTime, int endTime,
                                                  Map<String, String> tnIdMapReason, Map<String, List<String>> tnIdMapUidList) {
        return CompletableFuture.supplyAsync(() -> {
            long totalTime = roomUserOnlineDao.userTotalOnRoomTimeByUidSet(startTime, endTime, actorUidList);
            logger.info("roomTime tnId:{} actorUidList.size:{} totalTime:{}", tnId, actorUidList.size(), totalTime);
            if (totalTime > (int) TimeUnit.MINUTES.toSeconds(180)) {
                tnIdMapReason.compute(tnId, (k, v) -> getPreRecord(v, "1"));
            }
            List<String> destUidList = tnIdMapUidList.get(tnId);
            actorUidList.removeAll(destUidList);
            if (!CollectionUtils.isEmpty(actorUidList)) {
                int rechargeBean = rechargeDailyInfoDao.getUserTotalRechargeBeanByUidList(actorUidList, startTime, endTime);
                logger.info("recharge tnId:{} actorUidList.size:{} rechargeBean:{}", tnId, actorUidList.size(), rechargeBean);
                if (rechargeBean > 0) {
                    tnIdMapReason.compute(tnId, (k, v) -> getPreRecord(v, "2"));
                }
            }
            return true;
        }, executor).exceptionally(ex -> {
            logger.error("supplyAsync exception tnId:{} startTime:{} endTime:{} ex:{} ", tnId, startTime, endTime, ex.getMessage(), ex);
            return false;
        });
    }

    private void checkParam(ReportCallBackUserCondition dto) {
        if (dto.getBindType() == null || !StaffBindUserNewDao.BIND_TYPE_LIST.contains(dto.getBindType())) {
            throw new CommonH5Exception(new HttpCode(1, "bindType参数错误"));
        }
        if (dto.getStaffId() == null || dto.getStaffId() == 0) {
            throw new CommonH5Exception(new HttpCode(1, "staffId不能为空"));
        }
    }

    private void checkResExist(ReportCallBackUserCondition dto) {
        StaffNewData staffNewData = staffNewDao.selectById(dto.getStaffId());
        if (staffNewData == null || staffNewData.getStatus() != 1) {
            throw new CommonH5Exception(new HttpCode(1, "该员工不存在或者无效"));
        }
    }

    private String getMedium(List<ActorCampaignData> actorCampaignDataList) {
        if (CollectionUtils.isEmpty(actorCampaignDataList)) {
            return "unknow";
        } else {
            ActorCampaignData campaignData = actorCampaignDataList.get(0);
            String medium = campaignData != null ? campaignData.getMedium() : "unknow";
            return StringUtils.isEmpty(medium) ? "unknow" : medium;
        }
    }

    public ReportCallBackUserVO getAllStaff(ReportCallBackUserCondition dto) {
        List<StaffNewData> staffNewDataList = staffNewDao.selectAllByStatusCache(1);
        List<ReportCallBackUserVO.UserVO> userList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(staffNewDataList)) {
            for (StaffNewData item : staffNewDataList) {
                Integer rid = item.getId();
                ReportCallBackUserVO.UserVO userVO = new ReportCallBackUserVO.UserVO();
                userVO.setName(item.getName());
                userVO.setRid(String.valueOf(rid));
                userList.add(userVO);
            }
        }
        ReportCallBackUserVO vo = new ReportCallBackUserVO();
        vo.setUserList(userList);
        return vo;
    }


    public void deleteBindUser(ReportCallBackUserCondition dto) {
        if (dto.getItemId() == null || dto.getItemId() < 1) {
            throw new CommonH5Exception(new HttpCode(1, "itemId参数错误"));
        }
        StaffBindUserNewData staffBindUserNewData = staffBindUserNewDao.selectById(dto.getItemId());
        if (staffBindUserNewData == null) {
            logger.info("itemId:{} not exist", dto.getItemId());
            throw new CommonH5Exception(new HttpCode(1, "itemId不存在"));
        } else {
            int count = staffBindUserNewDao.deleteById(dto.getItemId());
            logger.info("delete itemId:{}  count:{} success", dto.getItemId(), count);
        }
    }


    private void checkRookieRoomUserParam(RookieRoomUserConfigCondition dto) {
        if (dto.getType() < 1 || dto.getCmdType() < 1) {
            throw new CommonH5Exception(new HttpCode(1, "type或者cmdType参数错误"));
        }

    }

    public RookieRoomConfigVO addOrDeleteRookieRoomUser(RookieRoomUserConfigCondition dto) {
        if (!StringUtils.hasLength(dto.getRids()) && CollectionUtils.isEmpty(dto.getRidList())) {
            throw new CommonH5Exception(new HttpCode(1, "rid不能为空"));
        }
        checkRookieRoomUserParam(dto);
        Set<String> ridSet;
        if (StringUtils.hasLength(dto.getRids())) {
            String[] rids = dto.getRids().trim().replace("，", ",").split(",");
            int ridsLength = rids.length;
            ridSet = new HashSet<>(Arrays.asList(rids));
            logger.info("rids.length:{} ridSet.size():{} ", ridsLength, ridSet.size());
        } else {
            ridSet = new HashSet<>(dto.getRidList());
        }
        if (ridSet.size() > 10000) {
            throw new CommonH5Exception(new HttpCode(1, "一次上传名单数不能大于10000"));
        }

        StringBuilder errorMsgSb = new StringBuilder();
        Set<String> uidSet = new HashSet<>();

        for (String strRid : ridSet) {
            try {
                ActorData actorData = actorDao.getActorByStrRid(strRid);
                if (null == actorData) {
                    errorMsgSb.append("rid:").append(strRid).append("找不到对应的用户,请更改后重新验证 \n");
                    continue;
                }
                uidSet.add(actorData.getUid());
            } catch (Exception e) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "账号输入有误");
            }
        }

        String errorMsg = errorMsgSb.toString();
        if (StringUtils.hasLength(errorMsg)) {
            throw new CommonH5Exception(new HttpCode(1, errorMsg));
        }
        int cmdType = dto.getCmdType();
        int useType = dto.getType();
        Long succesCount = null;
        if (useType == 2) {
            uidSet = uidSet.stream()
                    .map(s -> "r:" + s)
                    .collect(Collectors.toSet());
        }
        logger.info("uidSet size:{} uidSet:{}", uidSet.size(), uidSet);

        if (cmdType == 1) {
            if (useType == 1) {
                succesCount = newRookieRoomRedis.addAllUserByRedis(uidSet);
            } else {
                succesCount = newRookieRoomRedis.addAllRoomByRedis(uidSet);
            }
        } else if (cmdType == 2) {
            if (useType == 1) {
                succesCount = newRookieRoomRedis.removeAllUserByRedis(uidSet);
            } else {
                succesCount = newRookieRoomRedis.removeAllRoomByRedis(uidSet);
            }
        }
        RookieRoomConfigVO vo = new RookieRoomConfigVO();
        vo.setSuccessCount(Math.toIntExact(succesCount == null ? 0 : succesCount));
        return vo;
    }


    public PageResultVO<RookieRoomConfigVO> rookieRoomUserConfigList(RookieRoomUserConfigCondition condition) {
        PageResultVO<RookieRoomConfigVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int configType = condition.getType();
        int start = (page - 1) * pageSize;

        String searchUid = null;
        Set<String> rookieData = null;
        if (!StringUtil.isEmpty(condition.getSearch())) {
            ActorData actor = actorDao.getActorByStrRid(condition.getSearch());
            if (actor == null) {
                return pageVO;
            }
            searchUid = actor.getUid();
        }


        if (configType == 1) {
            // 迎新用户
            rookieData = newRookieRoomRedis.getNewRookieUserByRedis();
            if (StringUtils.hasLength(searchUid) && !rookieData.contains(searchUid)) {
                throw new CommonH5Exception(new HttpCode(1, "该用户不是迎新用户"));
            }
        } else {
            // 迎新房间
            rookieData = newRookieRoomRedis.getNewRookieRoomByRedis();
            if (StringUtils.hasLength(searchUid) && !rookieData.contains(RoomUtils.formatRoomId(searchUid))) {
                throw new CommonH5Exception(new HttpCode(1, "该房间不是迎新房间"));
            }

        }

        if (StringUtils.hasLength(searchUid)) {
            if (configType == 2) {
                searchUid = RoomUtils.formatRoomId(searchUid);
            }
            rookieData = new HashSet<>(Collections.singletonList(searchUid));
        }

        List<RookieRoomConfigVO> dataList = new ArrayList<>();
        List<String> recommendRoomList = Collections.emptyList();
        if (configType == 2) {
            List<PopularListNewVO> recommendList = giftPlayRedis.getSocialRoomListNew();
            recommendRoomList = recommendList.stream().map(k -> k.getRoomId()).collect(Collectors.toList());
        }
        for (String item : rookieData) {
            try {
                RookieRoomConfigVO vo = new RookieRoomConfigVO();
                String hostUid = item;
                if (configType == 2) {
                    hostUid = RoomUtils.getRoomHostId(item);
                    vo.setRecommendStatus(recommendRoomList.contains(item) ? 1 : 2);
                    Set<String> inRoomUserSet = roomPlayerRedis.getRoomActors(item);
                    long onlineNewCount = inRoomUserSet == null ? 0 :
                            inRoomUserSet.stream().filter(one -> {
                                ActorData actorData = actorDao.getActorDataFromCache(one);
                                if (actorData != null && ActorUtils.isNewDeviceAccount(one, actorData.getFirstTnId())) {
                                    return true;
                                }
                                return false;
                            }).count();

                    vo.setUserCount(inRoomUserSet == null ? 0 : inRoomUserSet.size());
                    vo.setUserNewCount((int) onlineNewCount);
                    vo.setType(2);
                } else {
                    vo.setType(1);
                }
                ActorData actor = actorDao.getActorDataFromCache(hostUid);
                vo.setHostRid(actor.getStrRid());
                vo.setHostUid(hostUid);
                vo.setHostName(actor.getName());
                // IP国家
                CountryData countryData = countryQuery.find(actor.getIp());
                if (countryData != null) {
                    vo.setHostCountry(countryData.getCountry());
                }
                dataList.add(vo);
            } catch (Exception e) {
                logger.error("RookieRoomUserConfigCondition error:{}", JSONObject.toJSONString(condition));
            }
        }
        if (configType == 2) {
            dataList.sort(Comparator.comparing(RookieRoomConfigVO::getRecommendStatus)
                    .thenComparing(Comparator.comparing(RookieRoomConfigVO::getUserCount).reversed())
                    .thenComparing(Comparator.comparing(RookieRoomConfigVO::getUserNewCount).reversed())
            );
        }
        PageUtils.PageData<RookieRoomConfigVO> pageData = PageUtils.getPageData(dataList, page, pageSize);
        pageVO.setList(pageData.list);
        pageVO.setTotal(pageData.totalSize);
        return pageVO;
    }

    public void reportStaffBindUserNewEvent(StaffBindUserNewData staffData) {
        // 埋点
        StaffBindUserNewEvent event = new StaffBindUserNewEvent();
        event.setUid(staffData.getUid());
        event.setRecord_day(staffData.getRecordDay() + "");
        event.setStaff_id(staffData.getStaffId());
        event.setTn_id(staffData.getTnId());
        event.setDesc(staffData.getDesc());
        event.setStatus(staffData.getStatus());
        event.setBind_type(staffData.getBindType());
        event.setInvalid_reason(staffData.getInvalidReason());
        event.setCtime(DateHelper.getNowSeconds());
        event.setMedium(staffData.getMedium());
        eventReport.track(new EventDTO(event));
    }

}
