package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.MineBackgroundDao;
import com.quhong.mysql.dao.UserResourceDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.MineBackgroundData;
import com.quhong.mysql.data.UserResourceData;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.condition.UserResCondition;
import com.quhong.operation.share.dto.ResourceConfigDTO;
import com.quhong.operation.share.dto.UserResDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceConfigVO;
import com.quhong.operation.share.vo.ResourceVO;
import com.quhong.operation.share.vo.UserResRecordVO;
import com.quhong.operation.utils.StringUtil;
import com.quhong.operation.utils.ZipUtil;
import com.quhong.redis.GoodsListHomeRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
@Service
public class ResourceConfigService implements ResourceService {

    private static final Logger logger = LoggerFactory.getLogger(ResourceConfigService.class);

    private final static String FILE_PATH = "resource/";
    private final static String ZIP_PREFIX = "resource_";

    private final static int ITEM_TYPE_STORE_BUY = 5;

    private static final Interner<String> STRING_POOL = Interners.newWeakInterner();

    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CommonDao commonDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private MineBackgroundDao mineBackgroundDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private UserResourceDao userResourceDao;
    @Resource
    private BadgeListDao badgeListDao;
    @Resource
    private MicFrameSourceDao micFrameSourceDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private BuddleSourceDao buddleSourceDao;
    @Resource
    private RippleSourceDao rippleSourceDao;
    @Resource
    private FloatScreenSourceDao floatScreenSourceDao;
    @Resource
    private MongoThemeDao mongoThemeDao;
    @Resource
    private MqSenderService mqSenderService;


    /**
     * 分页查询资源列表
     */
    public PageResultVO<ResourceConfigVO> getSourceList(ItemCondition condition) {
        PageResultVO<ResourceConfigVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<ResourceConfigData> sourceList = resourceConfigDao.selectResourcePage(condition.getResType(), condition.getItemType(), condition.getStatus(), condition.getSearch(), start, pageSize);
        List<ResourceConfigVO> voList = new ArrayList<>();
        for (ResourceConfigData data : sourceList) {
            ResourceConfigVO vo = new ResourceConfigVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());

            // For TYPE_ENTRY_EFFECT resources, extract width, height, and entryTrack from URL
            if (data.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
                // Extract parameters from resourceUrl
                extractEntryEffectParams(data.getResourceUrl(), vo);
            }

            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(resourceConfigDao.selectResourceCount(condition.getResType(), condition.getItemType(), condition.getStatus(), condition.getSearch()));
        return pageVO;
    }

    public void addResourceData(String uid, ResourceConfigDTO dto) {

        if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
            if (StringUtils.isEmpty(dto.getAnimationUrl())) {
                throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "资源图未上传");
            }
        }
        String internKey = uid + dto.getName();
        synchronized (STRING_POOL.intern(internKey)) {
            ResourceConfigData data = new ResourceConfigData();
            BeanUtils.copyProperties(dto, data);
            ResourceConfigData lastData = resourceConfigDao.getLastResourceData();
            int nextId = lastData != null ? lastData.getResourceId() + 1 : 1;
            data.setResourceId(nextId);
            data.setCtime(DateHelper.getNowSeconds());
            data.setResourceMd5("");
            if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
                String resourceUrl = data.getAnimationUrl();
                String resourceUrlAr = data.getAnimationUrlAr();

                // Append width, height, and entryTrack parameters if they are not 0
                if (dto.getWidth() > 0 && dto.getHeight() > 0) {
                    resourceUrl = appendEntryEffectParams(resourceUrl, dto.getWidth(), dto.getHeight());
                    resourceUrlAr = appendEntryEffectParams(resourceUrlAr, dto.getWidth(), dto.getHeight());
                }

                data.setResourceUrl(resourceUrl);
                data.setResourceUrlAr(resourceUrlAr);
            } else {
                data.setResourceUrl(dto.getResourceUrl());
                data.setResourceUrlAr(dto.getResourceUrlAr());
            }

            if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_HONOR_TITLE) {
                data.setDesc(dto.getName());
                data.setDescAr(dto.getNameAr());
            }

            resourceConfigDao.save(data);
            if (data.getItemType() == ITEM_TYPE_STORE_BUY && data.getIsNew() == 1 && data.getStatus() == 1) {
                goodsListHomeRedis.addNewGoodsRankingScore(data.getResourceType(), nextId);
            }
        }
    }

    public void updateResourceData(ResourceConfigDTO dto) {
        ResourceConfigData data = resourceConfigDao.getResourceDataById(dto.getDocId());
        if (data == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (data.getItemType() == 5 && data.getItemType() != dto.getItemType()) {
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "已上传的资源不能修改类型");
        }
        Update update = new Update();
        update.set("name", dto.getName() != null ? dto.getName() : "");
        update.set("nameAr", dto.getNameAr() != null ? dto.getNameAr() : "");
        update.set("icon", dto.getIcon() != null ? dto.getIcon() : "");
        update.set("iconAr", dto.getIconAr() != null ? dto.getIconAr() : "");
        update.set("desc", dto.getDesc() != null ? dto.getDesc() : "");
        update.set("descAr", dto.getDescAr() != null ? dto.getDescAr() : "");
        update.set("itemType", dto.getItemType());
        update.set("status", dto.getStatus());
        update.set("animationType", dto.getAnimationType());
        update.set("currencyType", dto.getCurrencyType());
        update.set("price", dto.getPrice());
        update.set("days", dto.getDays());
        update.set("order", dto.getOrder());
        update.set("isNew", dto.getIsNew());

        // For TYPE_ENTRY_EFFECT, we need to handle the URL parameters
        if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
            String resourceUrl = dto.getResourceUrl();
            String resourceUrlAr = dto.getResourceUrlAr();

            // Append parameters if they are not 0
            if (dto.getWidth() > 0 && dto.getHeight() > 0) {
                resourceUrl = appendEntryEffectParams(resourceUrl, dto.getWidth(), dto.getHeight());
                resourceUrlAr = appendEntryEffectParams(resourceUrlAr, dto.getWidth(), dto.getHeight());
            }

            update.set("resourceUrl", resourceUrl);
            update.set("resourceUrlAr", resourceUrlAr);
        } else {
            update.set("resourceUrl", dto.getResourceUrl());
            update.set("resourceUrlAr", dto.getResourceUrlAr());
        }
        if (!StringUtil.isEmpty(dto.getAnimationUrl()) && !dto.getAnimationUrl().equals(data.getAnimationUrl())) {
            update.set("animationUrl", dto.getAnimationUrl());
            if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
                String resourceUrl = dto.getAnimationUrl();
                if (dto.getWidth() > 0 && dto.getHeight() > 0) {
                    resourceUrl = appendEntryEffectParams(resourceUrl, dto.getWidth(), dto.getHeight());
                }
                update.set("resourceUrl", resourceUrl);
            }
        }
        if (!StringUtil.isEmpty(dto.getAnimationUrlAr()) && !dto.getAnimationUrlAr().equals(data.getAnimationUrlAr())) {
            update.set("animationUrlAr", dto.getAnimationUrlAr());
            if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
                String resourceUrlAr = dto.getAnimationUrlAr();
                if (dto.getWidth() > 0 && dto.getHeight() > 0) {
                    resourceUrlAr = appendEntryEffectParams(resourceUrlAr, dto.getWidth(), dto.getHeight());
                }
                update.set("resourceUrlAr", resourceUrlAr);
            }
        }
        if (!StringUtil.isEmpty(dto.getVideoUrl()) && !dto.getVideoUrl().equals(data.getVideoUrl())) {
            update.set("videoUrl", dto.getVideoUrl());
        }

        if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_HONOR_TITLE) {
            update.set("desc", dto.getName());
            update.set("descAr", dto.getNameAr());
        }

        resourceConfigDao.updateData(dto.getDocId(), update);

        if (data.getItemType() == ITEM_TYPE_STORE_BUY) {
            if (dto.getIsNew() == 1 && dto.getStatus() == 1) {
                goodsListHomeRedis.addNewGoodsRankingScore(data.getResourceType(), data.getResourceId());
            }
            if (dto.getIsNew() == 0 || dto.getStatus() == 0) {
                goodsListHomeRedis.deleteItemNewGoodsRanking(data.getResourceType(), data.getResourceId());
            }
            if (dto.getStatus() == 0) {
                goodsListHomeRedis.deleteItemNewGoodsRanking(data.getResourceType(), data.getResourceId());
                goodsListHomeRedis.deleteItemHotGoodsRanking(data.getResourceType(), data.getResourceId());
            }
        }
    }

    private void asyncUpdateSource(int resId, int resType, List<Map<String, String>> urlList) {
        ResourceConfigData resource = resourceConfigDao.getResourceDataFromDb(resId, resType);
        if (resource == null) {
            return;
        }
        String zipPath = ZipUtil.loadUrlZipUploadFile(ZIP_PREFIX, urlList);
        Map<String, Object> zipFileMeta = ZipUtil.calculateZipFileMD5(zipPath);
        String zipFileUrl = ZipUtil.uploadZipFile(zipPath, FILE_PATH);
        Update update = new Update();
        update.set("resourceUrl", zipFileUrl);
        update.set("resourceMd5", zipFileMeta.get("fileMd5"));
        resourceConfigDao.updateData(resource.get_id().toString(), update);
    }

    private List<Map<String, String>> convertAddZip(ResourceConfigDTO dto) {
        List<Map<String, String>> urlList = new ArrayList<>();
        if (!StringUtils.isEmpty(dto.getAnimationUrl())) {
            Map<String, String> actMap = new HashMap<>(2);
            actMap.put("fileName", "act");
            actMap.put("fileUrl", dto.getAnimationUrl());
            urlList.add(actMap);
        }
        if (!StringUtils.isEmpty(dto.getVideoUrl())) {
            Map<String, String> videoMap = new HashMap<>(2);
            videoMap.put("fileName", "voice");
            videoMap.put("fileUrl", dto.getVideoUrl());
            urlList.add(videoMap);
        }
        return urlList;
    }

    private List<Map<String, String>> convertUpdateZip(ResourceConfigDTO dto, ResourceConfigData data) {
        List<Map<String, String>> urlList = new ArrayList<>();
        if ((!StringUtils.isEmpty(dto.getAnimationUrl()) && !Objects.equals(dto.getAnimationUrl(), data.getAnimationUrl()))) {
            Map<String, String> actMap = new HashMap<>(2);
            actMap.put("fileName", "act");
            actMap.put("fileUrl", dto.getAnimationUrl());
            urlList.add(actMap);
            if (!StringUtils.isEmpty(dto.getVideoUrl())) {
                Map<String, String> videoMap = new HashMap<>(2);
                videoMap.put("fileName", "voice");
                videoMap.put("fileUrl", dto.getVideoUrl());
                urlList.add(videoMap);
            }
        }
        return urlList;
    }

    public void sendReward(UserResDTO dto) {
        checkParam(dto);
        checkResExist(dto.getResType(), dto.getResId());
        String[] rids = dto.getStrRid().trim().replace("，", ",").split(",");
        StringBuilder errorMsgSb = new StringBuilder();
        List<String> aidList = new ArrayList<>();
        for (String strRid : rids) {
            try {
                ActorData actorData = actorDao.getActorByStrRid(strRid);
                if (null == actorData) {
                    errorMsgSb.append("rid:").append(strRid).append("找不到对应的用户 \n");
                    continue;
                }
                aidList.add(actorData.getUid());

            } catch (Exception e) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "账号输入有误");
            }
        }
        String errorMsg = errorMsgSb.toString();
        if (StringUtils.hasLength(errorMsg)) {
            throw new CommonH5Exception(new HttpCode(1, errorMsg));
        }

        int actionType = dto.getResType() == BaseDataResourcesConstant.TYPE_HONOR_TITLE ? 2 : 1;
        for (String aid : aidList) {
            mqSenderService.asyncHandleResources(getResourcesDTO(aid, dto.getResId(), dto.getResType(), dto.getNum(), dto.getDays(), actionType, dto.getFeature()));
        }
    }

    public void removeReward(UserResDTO dto) {
        checkParam(dto);

        if (StringUtils.isEmpty(dto.getAid())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        mqSenderService.asyncHandleResources(getResourcesDTO(dto.getAid(), dto.getResId(), dto.getResType(), 0, 0, 4, dto.getFeature()));
    }

    private void checkResExist(int resType, int resId) {
        switch (resType) {
            case BaseDataResourcesConstant.TYPE_BADGE:
                BadgeListData badgeData = badgeListDao.findData(resId);
                if (null == badgeData || badgeData.getValid() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_MIC:
                MicFrameSourceData micFrameData = micFrameSourceDao.findData(resId);
                if (null == micFrameData || micFrameData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIDE:
                JoinSourceData joinData = joinSourceDao.findSourceData(resId);
                if (null == joinData || joinData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                if (joinData.getItem_type() == BaseDataResourcesConstant.STORE_BUY_ITEM_TYPE) {
                    throw new CommonH5Exception(new HttpCode(1, "不能选择商店购买的坐骑"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_BAG_GIFT:
                GiftData extraGift = giftDao.getGiftFromDb(resId);
                if (extraGift == null || extraGift.getBagGift() <= 0) {
                    throw new CommonH5Exception(new HttpCode(1, "礼物要先设置成背包礼物才能下发"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                BuddleSourceData bubbleData = buddleSourceDao.findData(resId);
                if (null == bubbleData || bubbleData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                RippleSourceData rippleData = rippleSourceDao.findData(resId);
                if (null == rippleData || rippleData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN:
                FloatScreenSourceData screenData = floatScreenSourceDao.findData(resId);
                if (null == screenData || screenData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_MINE_BACKGROUND:
                MongoThemeData themeData = mongoThemeDao.findData(resId);
                if (null == themeData || themeData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_ENTRY_EFFECT:
            case BaseDataResourcesConstant.TYPE_HONOR_TITLE:
            case BaseDataResourcesConstant.TYPE_TICKET:
                ResourceConfigData resourceData = resourceConfigDao.getResourceDataFromDb(resId, resType);
                if (null == resourceData || resourceData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            default: {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
        }
    }

    private ResourcesDTO getResourcesDTO(String aid, int resId, int resType, Integer num, Integer days, int actionType, int feature) {
        ResourcesDTO dto = new ResourcesDTO();
        dto.setUid(aid);
        dto.setResId(String.valueOf(resId));
        dto.setResType(resType);
        if (BaseDataResourcesConstant.TYPE_BAG_GIFT == resType || BaseDataResourcesConstant.TYPE_TICKET == resType) {
            dto.setNum(num);
            dto.setDays(days);
        } else {
            dto.setNum(1);
            dto.setDays(days);
        }
        if (BaseDataResourcesConstant.TYPE_MINE_BACKGROUND == resType) {
            dto.setRoomId(RoomUtils.formatRoomId(aid));
        }
        dto.setGetWay(1);
        dto.setActionType(actionType);
        dto.setmTime(DateHelper.getNowSeconds());
        // 自然天过期
        if (BaseDataResourcesConstant.TYPE_MIC == resType) {
            dto.setGainType(feature);
        } else {
            dto.setGainType(0);
        }
        return dto;
    }

    private void checkParam(UserResDTO dto) {
        if (!StringUtils.hasLength(dto.getStrRid())) {
            throw new CommonH5Exception(new HttpCode(1, "rid不能为空"));
        }
        if (dto.getResType() == null || dto.getResType() == 0) {
            throw new CommonH5Exception(new HttpCode(1, "资源类型不能为空"));
        }
        if (dto.getResType() == BaseDataResourcesConstant.TYPE_BAG_GIFT) {
            if (dto.getNum() < 0) {
                throw new CommonH5Exception(new HttpCode(1, "数量不能为负数"));
            }
            if (dto.getResId() == null || dto.getResId() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "资源id不能为空"));
            }
        } else if (dto.getResType() == BaseDataResourcesConstant.TYPE_BADGE) {
            if (dto.getResId() == null || dto.getResId() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "资源id不能为空"));
            }
            if (dto.getDays() == null || dto.getDays() < -1 || dto.getDays() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "勋章奖励有效天数无效"));
            }
        } else {
            if (dto.getResId() == null || dto.getResId() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "资源id不能为空"));
            }
        }
    }

    public PageResultVO<UserResRecordVO> userResRecord(UserResCondition condition) {
        ActorData actorData = null;
        String aid = "";
        String strRid = condition.getStrRid();

        if (!StringUtils.isEmpty(strRid)) {

            if (strRid.length() == 24) {
                aid = strRid;
            } else {
                actorData = actorDao.getActorByStrRid(strRid);
                if (actorData == null) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "查询账号输入有误");
                }
                aid = actorData.getUid();
            }
        }

        int page = condition.getPage() != null ? Math.max(1, condition.getPage()) : 1;
        int pageSize = condition.getPageSize() != null ? Math.max(10, condition.getPageSize()) : 10;
        int start = (page - 1) * pageSize;
        PageResultVO<UserResRecordVO> resultVO = new PageResultVO<>();
        List<UserResRecordVO> list = new ArrayList<>();
        long total = 0;
        switch (condition.getResType()) {
            case BaseDataResourcesConstant.TYPE_BADGE:
                for (BadgeData userResData : selectList(aid, "badge_id", condition.getResId(), start, pageSize, BadgeData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    BadgeListData resData = selectOne("badge_id", userResData.getBadge_id(), BadgeListData.class);
                    vo.setResId(userResData.getBadge_id());
                    vo.setResIcon(resData != null ? resData.getIcon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime(userResData.getGet_time());
                    vo.setLeftTime(getStrLeftTime(userResData.getGet_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "badge_id", condition.getResId(), BadgeData.class);
                break;
            case BaseDataResourcesConstant.TYPE_MIC:
                if (condition.getFeature() == 1) {
                    for (MicFrameData userResData : selectList(aid, "mic_id", condition.getResId(), start, pageSize, MicFrameData.class)) {
                        UserResRecordVO vo = new UserResRecordVO();
                        fillUserInfo(vo, userResData.getUid(), actorData);
                        MicFrameSourceData resData = selectOne("mic_id", userResData.getMic_id(), MicFrameSourceData.class);
                        vo.setResId(userResData.getMic_id());
                        vo.setResIcon(resData != null ? resData.getMic_icon() : "");
                        vo.setResName(resData != null ? resData.getName() : "");
                        vo.setWearStatus(userResData.getStatus());
                        vo.setCtime((int) userResData.getC_time());
                        vo.setLeftTime(userResData.getLeft_times() + "天");
                        list.add(vo);
                    }
                    total = selectCount(aid, "mic_id", condition.getResId(), MicFrameData.class);
                } else if (condition.getFeature() == 2) {
                    for (VipMicFrameData userResData : selectList(aid, "mic_id", condition.getResId(), start, pageSize, VipMicFrameData.class)) {
                        UserResRecordVO vo = new UserResRecordVO();
                        fillUserInfo(vo, userResData.getUid(), actorData);
                        MicFrameSourceData resData = selectOne("mic_id", userResData.getMic_id(), MicFrameSourceData.class);
                        vo.setResId(userResData.getMic_id());
                        vo.setResIcon(resData != null ? resData.getMic_icon() : "");
                        vo.setResName(resData != null ? resData.getName() : "");
                        vo.setWearStatus(userResData.getStatus());
                        vo.setCtime((int) userResData.getC_time());
                        vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                        list.add(vo);
                    }
                    total = selectCount(aid, "mic_id", condition.getResId(), VipMicFrameData.class);
                } else {
                    for (ExtraMicFrameData userResData : selectList(aid, "mic_id", condition.getResId(), start, pageSize, ExtraMicFrameData.class)) {
                        UserResRecordVO vo = new UserResRecordVO();
                        fillUserInfo(vo, userResData.getUid(), actorData);
                        MicFrameSourceData resData = selectOne("mic_id", userResData.getMic_id(), MicFrameSourceData.class);
                        vo.setResId(userResData.getMic_id());
                        vo.setResIcon(resData != null ? resData.getMic_icon() : "");
                        vo.setResName(resData != null ? resData.getName() : "");
                        vo.setWearStatus(userResData.getStatus());
                        vo.setCtime((int) userResData.getC_time());
                        vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                        list.add(vo);
                    }
                    total = selectCount(aid, "mic_id", condition.getResId(), ExtraMicFrameData.class);
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIDE:
                for (JoinCartonData userResData : selectList(aid, "join_carton_id", condition.getResId(), start, pageSize, JoinCartonData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    JoinSourceData resData = selectOne("join_carton_id", userResData.getJoin_carton_id(), JoinSourceData.class);
                    vo.setResId(userResData.getJoin_carton_id());
                    vo.setResIcon(resData != null ? resData.getJoin_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "join_carton_id", condition.getResId(), JoinCartonData.class);
                break;
            case BaseDataResourcesConstant.TYPE_BAG_GIFT:
                for (GiftBagData userResData : selectList(aid, "gift_id", condition.getResId(), start, pageSize, GiftBagData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    GiftData resData = giftDao.getGiftFromCache(userResData.getGift_id());
                    vo.setResId(userResData.getGift_id());
                    vo.setResIcon(resData != null ? resData.getGicon() : "");
                    vo.setResName(resData != null ? resData.getGname() : "");
                    vo.setWearStatus(0);
                    vo.setCtime(userResData.getGet_time());
                    vo.setLeftTime(getStrLeftTime(userResData.getGet_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "gift_id", condition.getResId(), GiftBagData.class);
                break;
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                for (BubbleData userResData : selectList(aid, "buddle_id", condition.getResId(), start, pageSize, BubbleData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    BuddleSourceData resData = selectOne("buddle_id", userResData.getBuddle_id(), BuddleSourceData.class);
                    vo.setResId(userResData.getBuddle_id());
                    vo.setResIcon(resData != null ? resData.getBuddle_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "buddle_id", condition.getResId(), BubbleData.class);
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                for (RippleData userResData : selectList(aid, "ripple_id", condition.getResId(), start, pageSize, RippleData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    RippleSourceData resData = selectOne("ripple_id", userResData.getRipple_id(), RippleSourceData.class);
                    vo.setResId(userResData.getRipple_id());
                    vo.setResIcon(resData != null ? resData.getRipple_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "ripple_id", condition.getResId(), RippleData.class);
                break;
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN:
                for (FloatScreenData userResData : selectList(aid, "screen_id", condition.getResId(), start, pageSize, FloatScreenData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    FloatScreenSourceData resData = selectOne("screen_id", userResData.getScreen_id(), FloatScreenSourceData.class);
                    vo.setResId(userResData.getScreen_id());
                    vo.setResIcon(resData != null ? resData.getScreen_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "screen_id", condition.getResId(), FloatScreenData.class);
                break;
            case BaseDataResourcesConstant.TYPE_MINE_BACKGROUND:
                IPage<MineBackgroundData> iPage = mineBackgroundDao.selectPageByUidAndBgId(aid, condition.getResId(), page, pageSize);
                for (MineBackgroundData userResData : iPage.getRecords()) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    MongoThemeData resData = selectOne("tid", userResData.getBgId(), MongoThemeData.class);
                    vo.setResId(userResData.getBgId());
                    vo.setResIcon(resData != null ? resData.getPreview() : "");
                    vo.setResName(resData != null ? resData.getTitle() : "");
                    MongoRoomData roomData = mongoRoomDao.getDataFromCache(userResData.getRoomId());
                    vo.setWearStatus(roomData != null && roomData.getTheme() == userResData.getBgId() ? 1 : 0);
                    vo.setCtime(userResData.getCtime());
                    vo.setLeftTime(getStrLeftTime(userResData.getCtime(), userResData.getEndTime()));
                    list.add(vo);
                }
                total = iPage.getTotal();
                break;
            case BaseDataResourcesConstant.TYPE_ENTRY_EFFECT:
            case BaseDataResourcesConstant.TYPE_HONOR_TITLE:
            case BaseDataResourcesConstant.TYPE_TICKET:
                List<UserResourceData> userResDataList = userResourceDao.selectList(aid, condition.getResType(), condition.getResId(), start, pageSize);
                for (UserResourceData userResData : userResDataList) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    ResourceConfigData resData = selectOne("resourceId", userResData.getResourceId(), ResourceConfigData.class);
                    vo.setResId(userResData.getResourceId());
                    vo.setResIcon(resData != null ? resData.getIcon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime(userResData.getCtime());
                    vo.setLeftTime(getStrLeftTime(userResData.getCtime(), userResData.getEndTime()));
                    list.add(vo);
                }
                total = userResourceDao.selectCount(aid, condition.getResType(), condition.getResId());
                break;
        }
        resultVO.setList(list);
        resultVO.setTotal(total);
        return resultVO;
    }

    public List<UserResRecordVO> userResRecordReports(int resType, String aid, int resId, int feature) {
        List<UserResRecordVO> list = new ArrayList<>();
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        switch (resType) {
            case BaseDataResourcesConstant.TYPE_BADGE:
                for (BadgeData userResData : selectAllList(aid, "badge_id", resId, BadgeData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    BadgeListData resData = selectOne("badge_id", userResData.getBadge_id(), BadgeListData.class);
                    vo.setResId(userResData.getBadge_id());
                    vo.setResIcon(resData != null ? resData.getIcon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getGet_time() * 1000L)));
                    vo.setLeftTime(getStrLeftTime(userResData.getGet_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                break;
            case BaseDataResourcesConstant.TYPE_MIC:
                if (feature == 1) {
                    for (MicFrameData userResData : selectAllList(aid, "mic_id", resId, MicFrameData.class)) {
                        UserResRecordVO vo = new UserResRecordVO();
                        fillUserInfo(vo, userResData.getUid(), actorData);
                        MicFrameSourceData resData = selectOne("mic_id", userResData.getMic_id(), MicFrameSourceData.class);
                        vo.setResId(userResData.getMic_id());
                        vo.setResIcon(resData != null ? resData.getMic_icon() : "");
                        vo.setResName(resData != null ? resData.getName() : "");
                        vo.setWearStatus(userResData.getStatus());
                        vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getC_time() * 1000L)));
                        vo.setLeftTime(userResData.getLeft_times() + "天");
                        list.add(vo);
                    }
                } else {
                    for (ExtraMicFrameData userResData : selectAllList(aid, "mic_id", resId, ExtraMicFrameData.class)) {
                        UserResRecordVO vo = new UserResRecordVO();
                        fillUserInfo(vo, userResData.getUid(), actorData);
                        MicFrameSourceData resData = selectOne("mic_id", userResData.getMic_id(), MicFrameSourceData.class);
                        vo.setResId(userResData.getMic_id());
                        vo.setResIcon(resData != null ? resData.getMic_icon() : "");
                        vo.setResName(resData != null ? resData.getName() : "");
                        vo.setWearStatus(userResData.getStatus());
                        vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getC_time() * 1000L)));
                        vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                        list.add(vo);
                    }
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIDE:
                for (JoinCartonData userResData : selectAllList(aid, "join_carton_id", resId, JoinCartonData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    JoinSourceData resData = selectOne("join_carton_id", userResData.getJoin_carton_id(), JoinSourceData.class);
                    vo.setResId(userResData.getJoin_carton_id());
                    vo.setResIcon(resData != null ? resData.getJoin_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getC_time() * 1000L)));
                    vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                break;
            case BaseDataResourcesConstant.TYPE_BAG_GIFT:
                for (GiftBagData userResData : selectAllList(aid, "gift_id", resId, GiftBagData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    GiftData resData = giftDao.getGiftFromCache(userResData.getGift_id());
                    vo.setResId(userResData.getGift_id());
                    vo.setResIcon(resData != null ? resData.getGicon() : "");
                    vo.setResName(resData != null ? resData.getGname() : "");
                    vo.setWearStatus(0);
                    vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getGet_time() * 1000L)));
                    vo.setLeftTime(getStrLeftTime(userResData.getGet_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                break;
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                for (BubbleData userResData : selectAllList(aid, "buddle_id", resId, BubbleData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    BuddleSourceData resData = selectOne("buddle_id", userResData.getBuddle_id(), BuddleSourceData.class);
                    vo.setResId(userResData.getBuddle_id());
                    vo.setResIcon(resData != null ? resData.getBuddle_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getC_time() * 1000L)));
                    vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                for (RippleData userResData : selectAllList(aid, "ripple_id", resId, RippleData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    RippleSourceData resData = selectOne("ripple_id", userResData.getRipple_id(), RippleSourceData.class);
                    vo.setResId(userResData.getRipple_id());
                    vo.setResIcon(resData != null ? resData.getRipple_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getC_time() * 1000L)));
                    vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                break;
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN:
                for (FloatScreenData userResData : selectAllList(aid, "screen_id", resId, FloatScreenData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    FloatScreenSourceData resData = selectOne("screen_id", userResData.getScreen_id(), FloatScreenSourceData.class);
                    vo.setResId(userResData.getScreen_id());
                    vo.setResIcon(resData != null ? resData.getScreen_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getC_time() * 1000L)));
                    vo.setLeftTime(getStrLeftTime(userResData.getC_time(), userResData.getEnd_time()));
                    list.add(vo);
                }
                break;
            case BaseDataResourcesConstant.TYPE_MINE_BACKGROUND:
                for (MineBackgroundData userResData : mineBackgroundDao.selectListByUidAndBgId(aid, resId)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    MongoThemeData resData = selectOne("tid", userResData.getBgId(), MongoThemeData.class);
                    vo.setResId(userResData.getBgId());
                    vo.setResIcon(resData != null ? resData.getPreview() : "");
                    vo.setResName(resData != null ? resData.getTitle() : "");
                    MongoRoomData roomData = mongoRoomDao.getDataFromCache(userResData.getRoomId());
                    vo.setWearStatus(roomData != null && roomData.getTheme() == userResData.getBgId() ? 1 : 0);
                    vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getCtime() * 1000L)));
                    vo.setLeftTime(getStrLeftTime(userResData.getCtime(), userResData.getEndTime()));
                    list.add(vo);
                }
                break;
            case BaseDataResourcesConstant.TYPE_ENTRY_EFFECT:
            case BaseDataResourcesConstant.TYPE_HONOR_TITLE:
            case BaseDataResourcesConstant.TYPE_TICKET:
                List<UserResourceData> userResDataList = userResourceDao.selectAllList(aid, resType, resId);
                for (UserResourceData userResData : userResDataList) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    ResourceConfigData resData = selectOne("resourceId", userResData.getResourceId(), ResourceConfigData.class);
                    vo.setResId(userResData.getResourceId());
                    vo.setResIcon(resData != null ? resData.getIcon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCreateTime(DateHelper.BEIJING.formatDateTime(new Date(userResData.getCtime() * 1000L)));
                    vo.setLeftTime(getStrLeftTime(userResData.getCtime(), userResData.getEndTime()));
                    list.add(vo);
                }
                break;
        }
        return list;
    }

    private String getStrLeftTime(long starTime, long endTime) {
        if (endTime == Integer.MAX_VALUE) {
            return "永久";
        }
        return (endTime - starTime) / TimeUnit.DAYS.toSeconds(1) + "天";
    }

    /**
     * Appends width, height, and entryTrack parameters to a URL if they are not 0
     *
     * @param url    The original URL
     * @param width  The width parameter
     * @param height The height parameter
     * @return The URL with parameters appended
     */
    private String appendEntryEffectParams(String url, int width, int height) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }

        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);

            // Only append parameters if they are not 0
            if (width > 0) {
                urlBuilder.replaceQueryParam("width", width);
            }

            if (height > 0) {
                urlBuilder.replaceQueryParam("height", height);
            }

            return urlBuilder.build(false).encode().toUriString();
        } catch (Exception e) {
            logger.error("Error appending parameters to URL: {}, error: {}", url, e.getMessage(), e);
            return url;
        }
    }

    /**
     * Extracts width, height, and entryTrack parameters from a URL and sets them on the VO
     *
     * @param url The URL to extract parameters from
     * @param vo  The ResourceConfigVO to set the parameters on
     */
    private void extractEntryEffectParams(String url, ResourceConfigVO vo) {
        if (StringUtils.isEmpty(url)) {
            return;
        }

        try {
            // Parse the URL to extract query parameters
            URL parsedUrl = new URL(url);
            String query = parsedUrl.getQuery();

            if (StringUtils.isEmpty(query)) {
                return;
            }

            // Split the query string by & to get individual parameters
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length != 2) {
                    continue;
                }

                String key = keyValue[0];
                String value = keyValue[1];

                // Set the appropriate field based on the parameter name
                if ("width".equals(key)) {
                    try {
                        vo.setWidth(Integer.parseInt(value));
                    } catch (NumberFormatException e) {
                        logger.info("Error parsing width parameter: {}", value, e);
                    }
                } else if ("height".equals(key)) {
                    try {
                        vo.setHeight(Integer.parseInt(value));
                    } catch (NumberFormatException e) {
                        logger.info("Error parsing height parameter: {}", value, e);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error extracting parameters from URL: {}, error: {}", url, e.getMessage(), e);
        }
    }

    private void fillUserInfo(UserResRecordVO vo, String uid, ActorData actorData) {
        if (actorData == null || !uid.equals(actorData.getUid())) {
            actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                return;
            }
        }
        vo.setUid(actorData.getUid());
        vo.setRid(actorData.getOriginalRid());
        vo.setStrRid(actorData.getStrRid());
        vo.setGender(actorData.getFb_gender());
    }


    public <T> T selectOne(String resIdField, Integer resId, Class<T> entityClass) {
        try {
            Criteria criteria = Criteria.where(resIdField).is(resId);
            return commonDao.findOne(new Query(criteria), entityClass);
        } catch (Exception e) {
            logger.error("selectOne error. resIdField={} resId={} class={} {} ", resIdField, resId, entityClass.getName(), e.getMessage(), e);
            return null;
        }
    }

    public <T> List<T> selectList(String uid, String resIdField, Integer resId, int start, int pageSize, Class<T> entityClass) {
        try {
            Query query = new Query(buildCriteria(uid, resIdField, resId));
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort);
            query.skip(start).limit(pageSize);
            List<T> list = commonDao.findList(query, entityClass);
            return !CollectionUtils.isEmpty(list) ? list : Collections.emptyList();
        } catch (Exception e) {
            logger.error("selectList error. uid={} resIdField={} resId={} start={} pageSize={} class={} {} ", uid, resIdField, resId, start, pageSize, entityClass.getName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public <T> List<T> selectAllList(String uid, String resIdField, Integer resId, Class<T> entityClass) {
        try {
            Query query = new Query(buildCriteria(uid, resIdField, resId));
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort);
            List<T> list = commonDao.findList(query, entityClass);
            return !CollectionUtils.isEmpty(list) ? list : Collections.emptyList();
        } catch (Exception e) {
            logger.error("selectAllList error. uid={} resIdField={} resId={} class={} {} ", uid, resIdField, resId, entityClass.getName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public <T> int selectCount(String uid, String resIdField, Integer resId, Class<T> entityClass) {
        try {
            return commonDao.QueryCount(new Query(buildCriteria(uid, resIdField, resId)), entityClass);
        } catch (Exception e) {
            logger.error("selectCount error. uid={} resIdField={} resId={} class={} {} ", uid, resIdField, resId, entityClass.getName(), e.getMessage(), e);
            return 0;
        }
    }

    private Criteria buildCriteria(String uid, String resIdField, Integer resId) {
        Criteria criteria = new Criteria();
        if (StringUtils.hasLength(uid)) {
            criteria = criteria.and("uid").is(uid);
        }
        if (resId != null && resId > 0) {
            criteria = criteria.and(resIdField).is(resId);
        }
        return criteria;
    }


    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<ResourceConfigData> sourceList = resourceConfigDao.selectResourcePage(condition.getResourceType(), -1, condition.getStatus(), condition.getSearch(), start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for (ResourceConfigData data : sourceList) {
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getResourceId());
            vo.setResourceIcon(data.getIcon());
            vo.setResourceNameEn(data.getName());
            vo.setResourceNameAr(data.getNameAr());
            vo.setResourcePrice(data.getPrice());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(resourceConfigDao.selectResourceCount(condition.getResourceType(), -1, condition.getStatus(), condition.getSearch()));
        return pageVO;
    }
}
