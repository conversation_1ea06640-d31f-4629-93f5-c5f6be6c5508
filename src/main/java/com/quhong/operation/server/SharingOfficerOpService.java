package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.analysis.BackendReviewRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.SharingOfficerLingGanDao;
import com.quhong.mysql.dao.SharingOfficerUploadLogDao;
import com.quhong.mysql.data.SharingOfficerLingGanData;
import com.quhong.mysql.data.SharingOfficerUploadLogData;
import com.quhong.operation.share.dto.SharingOfficerDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.SharingOfficerLogOpVO;
import com.quhong.operation.share.vo.SharingOfficerOpVO;
import com.quhong.service.ResourceKeyHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class SharingOfficerOpService {
    private static final Logger logger = LoggerFactory.getLogger(SharingOfficerOpService.class);
    private static final String ACTIVITY_TITLE_EN = "test Sharing Officer";
    private static final String ACTIVITY_TITLE_AR = "test Sharing Officer";

    @Resource
    private SharingOfficerUploadLogDao sharingOfficerUploadLogDao;
    @Resource
    private SharingOfficerLingGanDao sharingOfficerLingGanDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    protected EventReport eventReport;

    public PageResultVO<SharingOfficerLogOpVO> sharingOfficerUploadLogList(SharingOfficerDTO dto) {
        SharingOfficerUploadLogData filterData = new SharingOfficerUploadLogData();
        BeanUtils.copyProperties(dto, filterData);
        if (!StringUtils.isEmpty(dto.getSearch())) {
            ActorData actorData = actorDao.getActorByStrRid(dto.getSearch());
            filterData.setUid(actorData.getUid());
        }
        if (dto.getState() == -1) {
            filterData.setState(null);
        }
        if (dto.getChannelType() == -1) {
            filterData.setChannelType(null);
        }
        if (dto.getLevelType() == -1) {
            filterData.setLevelType(null);
        }
        IPage<SharingOfficerUploadLogData> iPage = sharingOfficerUploadLogDao.selectPage(filterData, dto.getPage(), dto.getPageSize(), null, null);
        List<SharingOfficerUploadLogData> logDataList = iPage.getRecords();
        List<SharingOfficerLogOpVO> logDataVOList = new ArrayList<>();
        for (SharingOfficerUploadLogData item : logDataList) {
            SharingOfficerLogOpVO itemVO = new SharingOfficerLogOpVO();
            BeanUtils.copyProperties(item, itemVO);
            ActorData actorData = actorDao.getActorDataFromCache(item.getUid());
            itemVO.setUserRid(actorData.getStrRid());
            logDataVOList.add(itemVO);
        }
        PageResultVO<SharingOfficerLogOpVO> pageVO = new PageResultVO<>();
        pageVO.setList(logDataVOList);
        pageVO.setTotal(iPage.getTotal());
        return pageVO;
    }

    public void sharingOfficerUploadLogUpdateAll(String adminUid, SharingOfficerDTO dto) {
        if (CollectionUtils.isEmpty(dto.getRidList())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误");
        }
        for (Integer rid : dto.getRidList()) {
            SharingOfficerDTO oneDto = new SharingOfficerDTO();
            oneDto.setRid(rid);
            oneDto.setState(dto.getState());
            sharingOfficerUploadLogUpdate(adminUid, oneDto);
        }
    }

    public void sharingOfficerUploadLogUpdate(String adminUid, SharingOfficerDTO dto) {
        if (dto.getRid() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误");
        }
        SharingOfficerUploadLogData logData = sharingOfficerUploadLogDao.selectOneById(dto.getRid());
        if (logData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误,输入的id没有找到数据");
        }
        if (logData.getState() == 1 && dto.getState() != null && dto.getState() != 1) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "已经通过审核的，不能再改为其他状态，否则可能造成奖励重复下发");
        }
        boolean isChange = false;
        // 不能改已经通过了的,目的是防止重复下发
        if (dto.getState() != null && logData.getState() != 1) {
            if (dto.getState() == 1) {
                // 下发资源
                resourceKeyHandlerService.sendResourceData(logData.getUid(), SharingOfficerUploadLogDao.MATCH_LEVEL_RES_MAP.get(logData.getLevelType()),
                        ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN);
            }
            logData.setState(dto.getState());
            isChange = true;
        }

        if (dto.getChannelType() != null) {
            logData.setChannelType(dto.getChannelType());
            isChange = true;
        }
        if (dto.getDescNotice() != null) {
            logData.setDescNotice(dto.getDescNotice());
            isChange = true;
        }
        if (isChange) {
            logData.setMtime(DateHelper.getNowSeconds());
            sharingOfficerUploadLogDao.update(logData);
            doUpdateReportEvent(adminUid, logData);
        }
    }


    public PageResultVO<SharingOfficerOpVO> sharingOfficerLingGanList(SharingOfficerDTO dto) {
        SharingOfficerLingGanData filterData = new SharingOfficerLingGanData();
        if (!StringUtils.isEmpty(dto.getSearch())) {
            filterData.setName(dto.getSearch());
        }
        List<SharingOfficerOpVO> voList = new ArrayList<>();
        IPage<SharingOfficerLingGanData> iPage = sharingOfficerLingGanDao.selectPage(filterData, dto.getPage(), dto.getPageSize());
        for (SharingOfficerLingGanData item : iPage.getRecords()) {
            SharingOfficerOpVO itemVO = new SharingOfficerOpVO();
            BeanUtils.copyProperties(item, itemVO);
            itemVO.setNoticeAList(fillToList(item.getNoticeList()));
            itemVO.setImagesAList(fillToList(item.getImagesList()));
            itemVO.setVideoAList(fillToList(item.getVideoList()));
            voList.add(itemVO);
        }

        PageResultVO<SharingOfficerOpVO> pageVO = new PageResultVO<>();
        pageVO.setTotal(iPage.getTotal());
        pageVO.setList(voList);
        return pageVO;
    }

    public void sharingOfficerLingGanAdd(SharingOfficerDTO dto) {
        if (dto.getName() == null || dto.getState() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误");
        }
        int now = DateHelper.getNowSeconds();
        SharingOfficerLingGanData lingGanData = new SharingOfficerLingGanData();
        lingGanData.setName(dto.getName());
        lingGanData.setState(dto.getState());
        lingGanData.setNoticeList(toListStr(dto.getNoticeAList()));
        lingGanData.setImagesList(toListStr(dto.getImagesAList()));
        lingGanData.setVideoList(toListStr(dto.getVideoAList()));
        lingGanData.setCtime(now);
        lingGanData.setMtime(now);
        sharingOfficerLingGanDao.insert(lingGanData);
    }

    public void sharingOfficerLingGanUpdate(SharingOfficerDTO dto) {
        if (dto.getRid() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误");
        }
        SharingOfficerLingGanData lingGanData = sharingOfficerLingGanDao.selectOneById(dto.getRid());
        if (lingGanData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数错误,输入的id没有找到数据");
        }
        boolean isChange = false;
        if (dto.getName() != null) {
            lingGanData.setName(dto.getName());
            isChange = true;
        }
        if (dto.getState() != null) {
            lingGanData.setState(dto.getState());
            isChange = true;
        }
        if (!CollectionUtils.isEmpty(dto.getNoticeAList())) {
            lingGanData.setNoticeList(toListStr(dto.getNoticeAList()));
            isChange = true;
        }
        if (!CollectionUtils.isEmpty(dto.getImagesAList())) {
            lingGanData.setImagesList(toListStr(dto.getImagesAList()));
            isChange = true;
        }
        if (!CollectionUtils.isEmpty(dto.getVideoAList())) {
            lingGanData.setVideoList(toListStr(dto.getVideoAList()));
            isChange = true;
        }

        if (isChange) {
            lingGanData.setMtime(DateHelper.getNowSeconds());
            sharingOfficerLingGanDao.update(lingGanData);
        }
    }

    private List<String> fillToList(String itemNameListStr) {
        List<String> toList = new ArrayList<>();
        if (!StringUtils.isEmpty(itemNameListStr)) {
            String[] nameArray = itemNameListStr.split(",");
            List<String> nameList = Arrays.asList(nameArray);
            toList.addAll(nameList);
        }
        return toList;
    }

    private String toListStr(List<String> itemNameList) {
        String itemNameListStr = null;
        if (!CollectionUtils.isEmpty(itemNameList)) {
            itemNameListStr = String.join(",", itemNameList);
        }
        return itemNameListStr;
    }


    private void doUpdateReportEvent(String adminUid, SharingOfficerUploadLogData logData) {
        BackendReviewRecordEvent eventProperties = new BackendReviewRecordEvent();
        EventDTO eventDTO = new EventDTO(logData.getUid(), eventProperties.getEventName());
        String key = String.format("%s-%s", logData.getUid(), logData.getCtime());
        eventDTO.setEventId(key);
        eventDTO.addProperties("operater", adminUid);
        eventDTO.addProperties("modify_ctime", logData.getMtime());
        eventDTO.addProperties("review_status", logData.getState());
        eventReport.trackUpdate(eventDTO);
    }
}
