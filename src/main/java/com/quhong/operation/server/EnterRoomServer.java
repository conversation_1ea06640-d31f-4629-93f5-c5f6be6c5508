package com.quhong.operation.server;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mysql.dao.MonthShardingDao;
import com.quhong.mysql.slave_mapper.ustar_log.EnterRoomMapper;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.DauStatDao;
import com.quhong.operation.share.mysql.EnterRoom;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.utils.ActorUtils;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.MongoUtils;
import com.quhong.operation.utils.StringUtil;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/4
 */
@Service
public class EnterRoomServer extends MonthShardingDao<EnterRoomMapper> {

    private final static Logger logger = LoggerFactory.getLogger(EnterRoomServer.class);

    @Autowired
    private EnterRoomMapper enterRoomMapper;
    @Autowired
    private DauStatDao dauStatDao;

    private static final int DAY_TIME = 24 * 60 * 60;

    public EnterRoomServer() {
        super("s_enter_room");
    }

    /**
     * 某房间内用户在线平均时长
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param roomId    房间rid
     * @return 平均时长
     */
    public ApiResult<Double> roomActorOnlineAvgTime(Integer startTime, Integer endTime, String roomId) {
        ApiResult<Double> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        BigDecimal sum = BigDecimal.ZERO;
        Long count = 0L;
        for (String tableSuffix : tableSuffixArr) {
            TotalVO vo = enterRoomMapper.roomActorOnlineTime(tableSuffix, startTime, endTime, roomId);
            if (null != vo && null != vo.getSumNum() && null != vo.getCountNum()) {
                sum = sum.add(vo.getSumNum());
                count += vo.getCountNum();
            }
        }

        if (sum.equals(BigDecimal.ZERO) || count <= 0) return result.ok(0.00);

        BigDecimal avg = sum.divide(new BigDecimal(count), 2, BigDecimal.ROUND_HALF_DOWN);

        logger.info("param start={} end={} roomId={} roomActorOnlineAvgTime count={}",
                startTime, endTime, roomId, avg.doubleValue());

        return result.ok(avg.doubleValue());
    }

    /**
     * 新用户进入房间的人数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param roomId    房间rid
     * @return 人数
     */
    public ApiResult<Integer> newActorInPerson(Integer startTime, Integer endTime, String roomId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Set<String> set = new HashSet<>();
        for (String tableSuffix : tableSuffixArr) {
            List<String> list = enterRoomMapper.newActorInRoomPerson(tableSuffix, startTime, endTime, roomId);
            if (!CollectionUtils.isEmpty(list)) {
                set.addAll(list);
            }
        }
        logger.info("param start={} end={} userId={} newActorInPerson count={}",
                startTime, endTime, roomId, set.size());

        return result.ok(set.size());
    }

    /**
     * 新用户进入该房间的次数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param roomId    房间rid
     * @return 次数
     */
    public ApiResult<Integer> newActorInCount(Integer startTime, Integer endTime, String roomId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Integer count = 0;
        for (String tableSuffix : tableSuffixArr) {
            Integer num = enterRoomMapper.newActorInRoomCount(tableSuffix, startTime, endTime, roomId);
            if (null != num && num > 0)
                count += num;
        }
        logger.info("param start={} end={} userId={} newActorInCount count={}",
                startTime, endTime, roomId, count);

        return result.ok(count);
    }

    /**
     * 进入房间次数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param userId    用户user_id
     * @return 次数
     */
    public ApiResult<Integer> actorJoinRoomCount(Integer startTime, Integer endTime, String userId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Integer count = 0;
        for (String tableSuffix : tableSuffixArr) {
            if (!checkExist(tableSuffix)) {
                continue;
            }
            Integer num = enterRoomMapper.actorJoinRoomCount(tableSuffix, startTime, endTime, userId);
            if (null != num && num > 0)
                count += num;
        }
//        logger.info("param start={} end={} userId={} actorJoinRoomCount count={}",
//                startTime, endTime, userId, count);

        return result.ok(count);
    }

    /**
     * 进入房间人数和次数
     *
     * @param startTime     开始时间
     * @param endTime       结尾时间
     * @param newUserUidSet 新用户id集合
     * @return 进入房间人数, 进房次数
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2.hashCode()",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<Integer> actorJoinRoomPersonCount(Integer startTime, Integer endTime, Set<String> newUserUidSet) {
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        List<Integer> result = new ArrayList<>();
        result.add(0);
        result.add(0);
        for (String tableSuffix : tableSuffixArr) {
            if (!checkExist(tableSuffix)) {
                continue;
            }
            if (CollectionUtils.isEmpty(newUserUidSet)) {
                return result;
            }
            List<EnterRoom> enterRoomList = enterRoomMapper.selectUserCount(tableSuffix, startTime, endTime, newUserUidSet);
            Set<String> set = new HashSet<>(enterRoomList.size());
            for (EnterRoom enterRoom : enterRoomList) {
                set.add(enterRoom.getUserId());
            }
            result.set(0, result.get(0) + set.size());
            result.set(1, result.get(1) + enterRoomList.size());
        }
        logger.info("param start={} end={} actorJoinRoomPersonCount count={}", startTime, endTime, result);
        return result;
    }

    /**
     * 进入房间数量
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param userId    用户user_id
     * @return 数量
     */
    public ApiResult<Integer> actorJoinRoomNum(Integer startTime, Integer endTime, String userId) {
        long l = System.currentTimeMillis();
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Set<String> set = new HashSet<>();
        for (String tableSuffix : tableSuffixArr) {
            if (!checkExist(tableSuffix)) {
                continue;
            }
            List<String> list = enterRoomMapper.actorJoinRoomNum(tableSuffix, startTime, endTime, userId);
            if (!CollectionUtils.isEmpty(list)) {
                set.addAll(list);
            }
        }
        logger.info("param start={} end={} userId={} actorJoinRoomNum count={} time={}",
                startTime, endTime, userId, set.size(), System.currentTimeMillis() - l);

        return result.ok(set.size());
    }

    /**
     * 获取时间内在房的用户的uid
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param roomId    房间id
     * @return 用户的uid集合
     */
    public ApiResult<List<String>> getRoomActorList(Integer startTime, Integer endTime, String roomId) {
        ApiResult<List<String>> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        List<String> uidList = new ArrayList<>();
        for (String tableSuffix : tableSuffixArr) {
            List<String> list = enterRoomMapper.getRoomActorList(tableSuffix, startTime, endTime, roomId);

            if (!CollectionUtils.isEmpty(list)) uidList.addAll(list);
        }
        logger.info("param start={} end={} roomId={} getRoomActorList count={}",
                startTime, endTime, roomId, uidList.size());
        return result.ok(uidList);
    }

    /**
     * 获取迎新房的当天新增的用户
     *
     * @param dateStr      当天日期
     * @param rookieStatus 0：非迎新房；1：迎新房；null：全部；
     * @return uid列表
     */
    public ApiResult<List<EnterRoom>> getEnterRoomUserId(String dateStr, Integer rookieStatus) {
        ApiResult<List<EnterRoom>> result = new ApiResult<>();
        logger.info("getEnterRoomUserId param date={} rs={}", dateStr, rookieStatus);
        if (StringUtil.isEmptyOrBlank(dateStr)) {
            return result.ok(new ArrayList<>());
        }
        // 过滤开始时间
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
        String[] uidRange = getUidWhereRange(time);

        // 获取表名后缀
        String tableSuffix = DateHelper.ARABIAN.getTableSuffixByString(dateStr);

        List<EnterRoom> list = enterRoomMapper.getEnterRoomUserId(
                tableSuffix, uidRange[0], uidRange[1], time[0], time[1], rookieStatus);
        if (CollectionUtils.isEmpty(list)) list = new ArrayList<>();

        logger.info("getEnterRoomUserId start={} end={} rookie={} list size={}",
                time[0], time[1], rookieStatus, list.size());
        return result.ok(list);
    }

    /**
     * 获取用户进入房间人次
     *
     * @param dateStr 某天日期
     * @param uidList 批量用户
     * @return 人次
     */
    public ApiResult<List<Integer>> personEnterRoom(String dateStr, List<String> uidList) {
        ApiResult<List<Integer>> result = new ApiResult<>();
        if (StringUtil.isEmptyOrBlank(dateStr)) {
            return result.ok(new ArrayList<>());
        }
        // 过滤开始时间
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
//        String[] uidRange = getUidWhereRange(time);

        // 获取表名后缀
        String tableSuffix = DateHelper.ARABIAN.getTableSuffixByString(dateStr);
        List<Integer> list = enterRoomMapper.personEnterRoom(tableSuffix, uidList, time[0], time[1]);
        if (CollectionUtils.isEmpty(list)) list = new ArrayList<>();

        logger.info("personEnterRoom start={} end={} list size={}", time[0], time[1], list.size());
        return result.ok(list);
    }

    /**
     * 判断用户是否注册当天就进入了迎新房
     *
     * @param uid 用户id
     * @return 进入了返回true
     */
    public ApiResult<Boolean> isRegisterInWelcomeRoom(String uid) {
        ApiResult<Boolean> result = new ApiResult<>();
        ObjectId oid = new ObjectId(uid);
        Date date = oid.getDate();
        // 过滤开始时间
        Long startTime = DateHelper.ARABIAN.setStartTime(date) / 1000;
        Integer start = startTime.intValue();
        Integer end = start + 24 * 60 * 60;

        // 获取表名后缀
        String tableSuffix = DateHelper.ARABIAN.getTableSuffixByDate(date);
        Integer count = enterRoomMapper.isRegisterInRoom(tableSuffix, uid, 1, start, end);

        logger.info("isRegisterInWelcomeRoom uid {} in room count {}", uid, count);
        return result.ok(count > 0);
    }

    /**
     * 获取进入迎新房的数据，同表已去重，多表未去重。
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 信息
     */
    public ApiResult<List<EnterRoom>> inWelcomeRoomActor(Integer startTime, Integer endTime) {
        logger.info("inWelcomeRoomActor param s={} e={}", startTime, endTime);
        ApiResult<List<EnterRoom>> result = new ApiResult<>();

        List<EnterRoom> uidList = new ArrayList<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        for (String tableSuffix : tableSuffixArr) {
            List<EnterRoom> list = enterRoomMapper.inWelcomeRoomActor(tableSuffix, startTime, endTime);
            if (!CollectionUtils.isEmpty(list)) uidList.addAll(list);
        }
        logger.info("inWelcomeRoomActor return size={}", uidList.size());
        return result.ok(uidList);
    }

    public Map<String, Map<String, Object>> findRookieRoomUserCount(String channel, Integer startTime, Integer endTime) {
        logger.info("start find rookie room user count channel = {},start = {},end = {}", channel, startTime, endTime);
        Map<String, Map<String, Object>> target = new HashMap<>();
        int os = 0;
        switch (channel) {
            case "Android":
                os = 0;
                break;
            case "iOS":
                os = 1;
                break;
            default:
                os = -1;
                break;
        }
        String[] yDates = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime - 1);
        long nowStamp = new Date().getTime() / 1000;
        int nowTime = (int) nowStamp;
        int periodSize = yDates.length;
        for (String yDate : yDates) {
            Map<String, Object> map = new HashMap<>();
            int start = DateHelper.ARABIAN.stringDateToStampSecond(yDate);
//            logger.info("=============   start date = {} ; time = {}=================",yDates[i],start);
            //当前跟踪的新用户
            Set<String> uidSet = new HashSet<>();
            int denominator = 0;
            for (int j = 1; j < periodSize + 1; j++) {
                int end = start + DAY_TIME;
                if (end > nowTime) {
                    int userCount = 0;
                    String tableSuffix = DateHelper.ARABIAN.getTableSuffixByDate(new Date(start * 1000L));
                    if (j == 1) {
                        List<EnterRoom> users = enterRoomMapper.selectRookieUserCount(tableSuffix, os, start, end);
                        for (EnterRoom user : users) {
                            //计算新用户数量 userCount
                            if (ActorUtils.isNewRegisterActor(user.getUserId(), start * 1000L)) {
                                //添加新用户
                                uidSet.add(user.getUserId());
                                userCount++;
                            }
                        }
                        denominator = userCount;
                    } else {
                        //非第0天数据查询
//                        logger.info("not zero day select new uid size = {};denominator = {} ====",uidSet.size(),denominator);
                        userCount = dauStatDao.findOnlineUserCount(channel, start, end, uidSet);
                    }
                    String rate = getRate(userCount, denominator, 1);
//                    logger.info("start time = {},end time = {},table suffix = {},user count = {},rate = {}",start,end,tableSuffix,userCount,rate);
                    //map添加rate 和 counts
                    map.put("count_" + (j - 1), userCount);
                    map.put("rate_" + (j - 1), rate);
                    break;
                }
                int userCount = 0;
                String tableSuffix = DateHelper.ARABIAN.getTableSuffixByDate(new Date(start * 1000L));
                if (j == 1) {
                    //第 0 天数据
                    List<EnterRoom> users = enterRoomMapper.selectRookieUserCount(tableSuffix, os, start, end);
                    for (EnterRoom user : users) {
                        //计算新用户数量 userCount
                        if (ActorUtils.isNewRegisterActor(user.getUserId(), start * 1000L)) {
                            //添加新用户
                            uidSet.add(user.getUserId());
                            userCount++;
                        }
                    }
                    denominator = userCount;
                } else {
                    //非第0天数据查询
//                    logger.info("not zero day select new uid size = {};denominator = {} ====",uidSet.size(),denominator);
                    userCount = dauStatDao.findOnlineUserCount(channel, start, end, uidSet);
                }
                //计算最后的留存率
                String rate = getRate(userCount, denominator, 1);
//                logger.info("start time = {},end time = {},table suffix = {},user count = {},rate = {}",start,end,tableSuffix,userCount,rate);
                //添加用户数量和rate
                map.put("count_" + (j - 1), userCount);
                map.put("rate_" + (j - 1), rate);
                start = end;
            }
            target.put(yDate, map);
        }
        return target;
    }


    /**
     * 获取过滤的uid范围
     *
     * @param time 时间范围
     * @return uid范围
     */
    private String[] getUidWhereRange(Integer[] time) {
        String startUid = MongoUtils.create_idBySecond(time[0]);
        String endUid = MongoUtils.create_idBySecond(time[1]);
        return new String[]{startUid, endUid};
    }

    /**
     * 获取百分比
     *
     * @param divisor  除数
     * @param dividend 被除数
     * @param bit      省约位数
     * @return
     */
    private String getRate(Integer divisor, Integer dividend, Integer bit) {
        if (dividend == null || divisor == null || bit == null)
            return null;
        if (divisor == 0 || dividend == 0) {
            return "0%";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(bit);
        String result = numberFormat.format((float) divisor / (float) dividend * 100);
        return result + "%";
    }

}
