package com.quhong.operation.server;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.EmojiConfigDao;
import com.quhong.mongo.dao.EmojiResourceDao;
import com.quhong.mongo.data.EmojiConfigData;
import com.quhong.mongo.data.EmojiResourceData;
import com.quhong.operation.share.condition.EmojiCondition;
import com.quhong.operation.share.vo.EmojiConfigVO;
import com.quhong.operation.share.vo.EmojiVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class EmojiService {
    private static final Logger logger = LoggerFactory.getLogger(EmojiService.class);

    @Resource
    private EmojiResourceDao emojiResourceDao;
    @Resource
    private EmojiConfigDao emojiConfigDao;


    public PageResultVO<EmojiConfigVO> emojiConfigList(EmojiCondition condition) {
        PageResultVO<EmojiConfigVO> pageVO = new PageResultVO<>();
        Integer status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<EmojiConfigData> emojiList = emojiConfigDao.selectEmojiConfigPage(condition.getEmojiType(), status, search, start, pageSize);
        List<EmojiConfigVO> voList = new ArrayList<>();
        for (EmojiConfigData data : emojiList) {
            EmojiConfigVO vo = new EmojiConfigVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(emojiConfigDao.selectCount(condition.getEmojiType(), status, search));
        return pageVO;
    }

    public void addEmojiConfig(EmojiConfigVO dto) {
        if (ObjectUtils.isEmpty(dto.getCategoryIconUrl()) || ObjectUtils.isEmpty(dto.getNameEn()) || ObjectUtils.isEmpty(dto.getNameAr())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "分类图标或名称未填写");
        }
        EmojiConfigData data = new EmojiConfigData();
        BeanUtils.copyProperties(dto, data);
        emojiConfigDao.insert(data);
    }

    public void updateEmojiConfig(EmojiConfigVO dto) {
        EmojiConfigData data = emojiConfigDao.getEmojiConfigByID(dto.getDocId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        Update update = new Update();
        update.set("emojiType", dto.getEmojiType());
        update.set("emojiTypeList", dto.getEmojiTypeList());
        update.set("nameEn", dto.getNameEn());
        update.set("nameAr", dto.getNameAr());
        update.set("categoryIconUrl", dto.getCategoryIconUrl());
        update.set("orderNum", dto.getOrderNum());
        update.set("status", dto.getStatus());
        update.set("vipLimit", dto.getVipLimit());
        emojiConfigDao.updateData(dto.getDocId(), update);
    }



    public PageResultVO<EmojiVO> emojiList(EmojiCondition condition) {
        PageResultVO<EmojiVO> pageVO = new PageResultVO<>();
        Integer status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<EmojiResourceData> emojiList = emojiResourceDao.selectEmojiPage(status, condition.getEmojiConfigId(), search, start, pageSize);
        List<EmojiVO> voList = new ArrayList<>();
        for (EmojiResourceData data : emojiList) {
            EmojiVO vo = new EmojiVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            vo.setEmojiUrl(data.getIcon_file());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(emojiResourceDao.selectCount(status, condition.getEmojiConfigId(), search));
        return pageVO;
    }

    public void addEmojiData(EmojiVO dto) {
        if(StringUtil.isEmpty(dto.getEmojiConfigId())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "分类id为空");
        }

        EmojiConfigData config = emojiConfigDao.getEmojiConfigByID(dto.getEmojiConfigId());
        if (config == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "所选分类不存在");
        }

        EmojiResourceData data = new EmojiResourceData();
        BeanUtils.copyProperties(dto, data);
        data.setIcon_file(dto.getEmojiUrl());
        emojiResourceDao.insert(data);
    }

    public void updateEmojiData(EmojiVO dto) {
        EmojiResourceData data = emojiResourceDao.getEmojiDataByID(dto.getDocId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if(StringUtil.isEmpty(dto.getEmojiConfigId())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "分类id为空");
        }

        EmojiConfigData config = emojiConfigDao.getEmojiConfigByID(dto.getEmojiConfigId());
        if (config == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "所选分类不存在");
        }

        Update update = new Update();
        update.set("emojiConfigId", dto.getEmojiConfigId());
        update.set("status", dto.getStatus());
        update.set("order", dto.getOrder());
        update.set("name", dto.getName());
        update.set("nameAr", dto.getNameAr());
        update.set("emojiSubType", dto.getEmojiSubType());
        update.set("cycles", dto.getCycles());
        if (!StringUtil.isEmpty(dto.getIcon())) {
            update.set("icon", dto.getIcon());
        }
        if (!StringUtil.isEmpty(dto.getEmojiUrl())) {
            update.set("icon_file", dto.getEmojiUrl());
        }
        emojiResourceDao.updateData(dto.getDocId(), update);
    }
}
