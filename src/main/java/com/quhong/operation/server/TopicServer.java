package com.quhong.operation.server;

import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.TopicArDao;
import com.quhong.operation.dao.TopicEnDao;
import com.quhong.operation.share.dto.FriendTopicAddDTO;
import com.quhong.operation.share.dto.FriendTopicUpdateDTO;
import com.quhong.operation.share.mongobean.TopicArData;
import com.quhong.operation.share.mongobean.TopicEnData;
import com.quhong.operation.share.vo.FriendTopicVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class TopicServer {

    private static final Logger logger = LoggerFactory.getLogger(TopicServer.class);

    private static final int LANG_EN = 1; //英语

    private static final int LANG_AR = 2; //阿语

    @Autowired
    private TopicEnDao topicEnDao;

    @Autowired
    private TopicArDao topicArDao;

    /**
     * 朋友圈话题新增
     * @param addDTO
     * @return
     */
    public ApiResult<String> addFriendTopic(FriendTopicAddDTO addDTO){
        ApiResult<String> result = new ApiResult<>();
        int ctime = (int)(new Date().getTime() / 1000);
        String topicEnName = addDTO.getTopicEnName();
        String topicArName = addDTO.getTopicArName();
        Integer status = addDTO.getStatus();
        Integer enOrder = addDTO.getEnOrder();
        Integer arOrder = addDTO.getArOrder();
        //插入英文topic
        TopicEnData topicEnData = topicEnDao.getDataByTopicName(topicEnName);
        if(topicEnData != null){
            logger.error("add friend topic error. en topic is exist. topic name = {}",topicEnName);
            return result.error("en topic is exist");
        }
        topicEnData = new TopicEnData();
        topicEnData.setTopicName(topicEnName);
        topicEnData.setStatus(status);
        topicEnData.setOrder(enOrder);
        topicEnData.setOpts(0);
        topicEnData.setCtime(ctime);
        topicEnDao.saveOne(topicEnData);
        //插入阿拉伯文topic
        TopicArData topicArData = topicArDao.getDataByTopicName(topicArName);
        if(topicArData != null){
            logger.error("add friend topic error. ar topic is exist. topic name = {}",topicArName);
            return result.error("ar topic is exist");
        }
        topicArData = new TopicArData();
        topicArData.setTopicName(topicArName);
        topicArData.setStatus(status);
        topicArData.setOrder(arOrder);
        topicArData.setOpts(0);
        topicArData.setCtime(ctime);
        topicArDao.saveOne(topicArData);
        return result.ok();
    }

    /**
     * 朋友圈话题更新
     * @param updateDTO
     * @return
     */
    public ApiResult<String> updateFriendTopic(FriendTopicUpdateDTO updateDTO){
        ApiResult<String> result = new ApiResult<>();
        int ctime = (int)(new Date().getTime() / 1000);
        String topicId = updateDTO.getTopicId();
        String topicName = updateDTO.getTopicName();
        Integer status = updateDTO.getStatus();
        Integer order = updateDTO.getOrder();
        TopicEnData topicEnData = topicEnDao.getDataById(topicId);
        if(topicEnData != null){
            TopicEnData updateEnData = new TopicEnData();
            updateEnData.set_id(topicEnData.get_id());
            updateEnData.setCtime(ctime);
            if(order != null){
                updateEnData.setOrder(order);
            }
            if(status != null){
                updateEnData.setStatus(status);
            }
            if(!StringUtils.isEmpty(topicName)){
                updateEnData.setTopicName(topicName);
            }
            topicEnDao.updateOne(updateEnData);
            return result.ok();
        }
        TopicArData topicArData = topicArDao.getDataById(topicId);
        if(topicArData != null){
            TopicArData updateArData = new TopicArData();
            updateArData.set_id(topicArData.get_id());
            updateArData.setCtime(ctime);
            if(order != null){
                updateArData.setOrder(order);
            }
            if(status != null){
                updateArData.setStatus(status);
            }
            if(!StringUtils.isEmpty(topicName)){
                updateArData.setTopicName(topicName);
            }
            topicArDao.updateOne(updateArData);
            return result.ok();
        }
        logger.error("update friend topic error. en and ar topic not have this data. dto = {}",updateDTO.toString());
        return result.error("update failed");
    }

    /**
     * 获取朋友圈话题列表
     * @param status
     * @param lang
     * @param topic
     * @param page
     * @return
     */
    public ApiResult<PageResultVO<FriendTopicVO>> listFriendTopic(int status, int lang, String topic,int page){
        ApiResult<PageResultVO<FriendTopicVO>> result = new ApiResult<>();
        PageResultVO<FriendTopicVO> pageResultVO = new PageResultVO<>();
        if(lang == LANG_EN){
            long total = topicEnDao.countByStatus(status, topic);
            pageResultVO.setTotal(total);
            List<TopicEnData> topicEnDataList = topicEnDao.pageDataByStatus(status, topic, page, 20);
            List<FriendTopicVO> enVos = topicEnData2VO(topicEnDataList);
            pageResultVO.setList(enVos);
            return result.ok(pageResultVO);
        }
        long total = topicArDao.countByStatus(status, topic);
        pageResultVO.setTotal(total);
        List<TopicArData> topicArDataList = topicArDao.pageDataByStatus(status, topic, page, 20);
        List<FriendTopicVO> arVos = topicArData2VO(topicArDataList);
        pageResultVO.setList(arVos);
        return result.ok(pageResultVO);
    }

    private List<FriendTopicVO> topicEnData2VO(List<TopicEnData> topicEnDataList){
        List<FriendTopicVO> list = new ArrayList<>();
        for (TopicEnData topicEnData : topicEnDataList) {
            FriendTopicVO friendTopicVO = new FriendTopicVO();
            friendTopicVO.setTopicName(topicEnData.getTopicName());
            friendTopicVO.setTopicId(topicEnData.get_id().toString());
            Integer status = topicEnData.getStatus();
            if(status != null){
                switch (status){
                    case TopicEnData.STATUS_ACTIVE:
                        friendTopicVO.setStatus("有效");
                        break;
                    case TopicEnData.STATUS_STOP:
                        friendTopicVO.setStatus("无效");
                        break;
                    default:
                        friendTopicVO.setStatus("其它");
                        break;
                }
            }
            friendTopicVO.setOpts(topicEnData.getOpts() == null?0:topicEnData.getOpts());
            friendTopicVO.setOrder(topicEnData.getOrder() == null?0:topicEnData.getOrder());
            friendTopicVO.setDate(DateHelper.BEIJING.timestampToDatetimeStr(topicEnData.getCtime()* 1000L));
            list.add(friendTopicVO);
        }
        return list;
    }

    private List<FriendTopicVO> topicArData2VO(List<TopicArData> topicArDataList){
        List<FriendTopicVO> list = new ArrayList<>();
        for (TopicArData topicArData : topicArDataList) {
            FriendTopicVO friendTopicVO = new FriendTopicVO();
            friendTopicVO.setTopicName(topicArData.getTopicName());
            friendTopicVO.setTopicId(topicArData.get_id().toString());
            Integer status = topicArData.getStatus();
            if(status != null){
                switch (status){
                    case TopicEnData.STATUS_ACTIVE:
                        friendTopicVO.setStatus("有效");
                        break;
                    case TopicEnData.STATUS_STOP:
                        friendTopicVO.setStatus("无效");
                        break;
                    default:
                        friendTopicVO.setStatus("其它");
                        break;
                }
            }
            friendTopicVO.setOpts(topicArData.getOpts() == null?0:topicArData.getOpts());
            friendTopicVO.setOrder(topicArData.getOrder() == null?0:topicArData.getOrder());
            friendTopicVO.setDate(DateHelper.BEIJING.timestampToDatetimeStr(topicArData.getCtime()* 1000L));
            list.add(friendTopicVO);
        }
        return list;
    }

}
