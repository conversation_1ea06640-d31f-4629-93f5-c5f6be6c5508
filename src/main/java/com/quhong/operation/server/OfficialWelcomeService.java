package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.operation.share.condition.OfficialWelcomeCondition;
import com.quhong.operation.share.dto.OfficialWelcomeDTO;
import com.quhong.operation.share.vo.OfficialWelcomeVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.DataRedisBean;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 官方欢迎房间-国家映射服务
 */
@Service
public class OfficialWelcomeService {
    private static final Logger logger = LoggerFactory.getLogger(OfficialWelcomeService.class);
    
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private ActorDao actorDao;
    
    // 国家到房间的映射 hash结构 country -> Set<roomId>
    private String getOfficialWelcomeCountryToRoomsKey() {
        return "hash:official:welcome:country:rooms";
    }

    // 房间到国家的映射 hash结构 roomId -> Set<country>
    private String getOfficialWelcomeRoomToCountriesKey() {
        return "hash:official:welcome:room:countries";
    }
    
    /**
     * 查询房间映射列表
     */
    public PageResultVO<OfficialWelcomeVO> list(OfficialWelcomeCondition condition) {
        logger.info("查询官方欢迎映射列表: {}", JSONObject.toJSONString(condition));
        
        PageResultVO<OfficialWelcomeVO> pageVO = new PageResultVO<>();
        List<OfficialWelcomeVO> resultList = new ArrayList<>();
        String search = condition.getSearch();
        if(!ObjectUtils.isEmpty(condition.getSearch())) {
            ActorData actorData = actorDao.getActorByStrRid(search);
            if (actorData == null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "搜索用户不存在");
            }
            search = RoomUtils.formatRoomId(actorData.getUid());
        }

        try {
            fillAllOffcialList(search, resultList);
            // 分页处理
            int page = condition.getPage();
            int pageSize = condition.getPageSize();
            int start = (page - 1) * pageSize;
            int end = Math.min(start + pageSize, resultList.size());
            
            pageVO.setTotal(resultList.size());
            if (start < resultList.size()) {
                pageVO.setList(resultList.subList(start, end));
            } else {
                pageVO.setList(new ArrayList<>());
            }
            
        } catch (Exception e) {
            logger.error("查询官方欢迎映射列表失败", e);
            pageVO.setList(new ArrayList<>());
            pageVO.setTotal(0);
        }
        return pageVO;
    }

    public void fillAllOffcialList(String search, List<OfficialWelcomeVO> resultList) {
        // 获取所有房间到国家的映射
        Map<Object, Object> roomToCountriesMap = redisTemplate.opsForHash().entries(getOfficialWelcomeRoomToCountriesKey());
        for (Map.Entry<Object, Object> entry : roomToCountriesMap.entrySet()) {
            String roomId = String.valueOf(entry.getKey());
            String countriesStr = String.valueOf(entry.getValue());

            // 过滤条件
            if (!StringUtils.isEmpty(search) && !roomId.equals(search)) {
                continue;
            }

            ActorData hostActorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId));
            if (hostActorData == null){
                continue;
            }
            Set<String> countries = JSON.parseObject(countriesStr, new TypeReference<Set<String>>(){});
            OfficialWelcomeVO vo = new OfficialWelcomeVO();
            vo.setWelcomeRid(hostActorData.getStrRid());
            vo.setRoomId(roomId);
            vo.setCountries(countries);
            vo.setCountriesStr(countries.stream().collect(Collectors.joining(",")));
            resultList.add(vo);
        }
    }

    /**
     * 设置房间对应的国家（主要操作）
     */
    public void setRoomCountries(OfficialWelcomeDTO dto) {
        logger.info("设置房间国家映射: {}", JSONObject.toJSONString(dto));
        
        if (StringUtils.isEmpty(dto.getWelcomeRid())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "用户id不能为空");
        }

        // 支持多个rid，用英文逗号隔开
        String[] rids = dto.getWelcomeRid().split(",");
        
        for (String rid : rids) {
            rid = rid.trim(); // 去除可能的空格
            if (StringUtils.isEmpty(rid)) {
                continue; // 跳过空的rid
            }
            
            ActorData actorData = actorDao.getActorByStrRid(rid);
            if (actorData == null) {
                logger.error("用户不存在，rid: {}", rid);
                continue;
            }

            try {
                String roomId = RoomUtils.formatRoomId(actorData.getUid());
                Set<String> newCountries = dto.getCountries();

                // 获取原来的国家列表
                String roomToCountriesKey = getOfficialWelcomeRoomToCountriesKey();
                String countriesStr = (String) redisTemplate.opsForHash().get(roomToCountriesKey, roomId);
                Set<String> countries = ObjectUtils.isEmpty(countriesStr) ? new HashSet<>() : JSON.parseObject(countriesStr, new TypeReference<Set<String>>(){});

                if (CollectionUtils.isEmpty(newCountries)) {
                    redisTemplate.opsForHash().delete(getOfficialWelcomeRoomToCountriesKey(), roomId);
                    String countryToRoomsKey = getOfficialWelcomeCountryToRoomsKey();
                    for (String country : countries) {
                        String roomIdListStr = (String) redisTemplate.opsForHash().get(countryToRoomsKey, country);
                        Set<String> roomIdList = ObjectUtils.isEmpty(roomIdListStr) ? new HashSet<>() : JSON.parseObject(roomIdListStr, new TypeReference<Set<String>>(){});
                        roomIdList.remove(roomId);
                        if (CollectionUtils.isEmpty(roomIdList)) {
                            redisTemplate.opsForHash().delete(countryToRoomsKey, country);
                        }else {
                            redisTemplate.opsForHash().put(countryToRoomsKey, country, JSON.toJSONString(roomIdList));
                        }
                    }
                }else {
                    // 移除原来设置的国家
                    countries.removeAll(newCountries);
                    String countryToRoomsKey = getOfficialWelcomeCountryToRoomsKey();
                    for (String country : countries) {
                        String roomIdListStr = (String) redisTemplate.opsForHash().get(countryToRoomsKey, country);
                        Set<String> roomIdList = ObjectUtils.isEmpty(roomIdListStr) ? new HashSet<>() : JSON.parseObject(roomIdListStr, new TypeReference<Set<String>>(){});
                        roomIdList.remove(roomId);
                        if (CollectionUtils.isEmpty(roomIdList)) {
                            redisTemplate.opsForHash().delete(countryToRoomsKey, country);
                        }else {
                            redisTemplate.opsForHash().put(countryToRoomsKey, country, JSON.toJSONString(roomIdList));
                        }
                    }

                    // 设置新的国家
                    redisTemplate.opsForHash().put(roomToCountriesKey, roomId, JSON.toJSONString(newCountries));
                    for (String country : newCountries) {
                        String roomIdListStr = (String) redisTemplate.opsForHash().get(countryToRoomsKey, country);
                        Set<String> roomIdList = ObjectUtils.isEmpty(roomIdListStr) ? new HashSet<>() : JSON.parseObject(roomIdListStr, new TypeReference<Set<String>>(){});
                        roomIdList.add(roomId);
                        redisTemplate.opsForHash().put(countryToRoomsKey, country, JSON.toJSONString(roomIdList));
                    }
                }
                logger.info("设置房间国家映射成功，rid: {}, 房间ID: {}, 国家列表: {}", rid, roomId, newCountries);
            } catch (Exception e) {
                logger.error("设置房间国家映射失败，rid: {}", rid, e);
                throw new CommonH5Exception(HttpCode.SERVER_ERROR.getCode(), "设置失败，rid: " + rid + ", 错误: " + e.getMessage());
            }
        }
    }
} 