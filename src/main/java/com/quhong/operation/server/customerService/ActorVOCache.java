package com.quhong.operation.server.customerService;

import com.quhong.cache.CacheMap;
import com.quhong.data.ActorData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.ActorPayExternalData;
import com.quhong.operation.share.vo.customerService.ActorVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
public class ActorVOCache {

    private CacheMap<String, ActorVO> cacheMap = new CacheMap<>();

    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private UserLevelDao userLevelDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public ActorVO getActorVO(String uid) {
        ActorVO actorVo = cacheMap.getData(uid);
        if (actorVo == null) {
            actorVo = createActorVO(uid);
            cacheMap.cacheData(uid, actorVo);
        }
        return actorVo;
    }

    private ActorVO createActorVO(String uid) {
        ActorVO actorVO = new ActorVO();
        ActorData actorData = actorDao.getActorData(uid);
        actorVO.copyFrom(actorData);
        String head = ImageUrlGenerator.generateRoomUserUrl(actorData.getHead());
        actorVO.setHead(head);
        actorVO.setVipLevel(vipInfoDao.getIntVipLevel(uid));
        actorVO.setLevel(userLevelDao.getUserLevel(uid));
        ActorPayExternalData actorPayExternalData = actorPayExternalDao.selectOne(uid);
        if (actorPayExternalData != null){
            actorVO.setRechargeTotal(String.valueOf(actorPayExternalData.getRechargeMoney()));
            actorVO.setRechargeChannel(String.valueOf(actorPayExternalData.getRechargeChannel()));
            actorVO.setRechargeTime(String.valueOf(actorPayExternalData.getLastTime()));
        }

        return actorVO;
    }
}
