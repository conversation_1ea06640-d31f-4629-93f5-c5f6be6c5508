package com.quhong.operation.server;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ResourceGroupData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.RippleDao;
import com.quhong.mongo.dao.RippleSourceDao;
import com.quhong.mongo.data.RippleSourceData;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.dto.RippleSourceDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceVO;
import com.quhong.operation.share.vo.RippleSourceVO;
import com.quhong.operation.utils.StringUtil;
import com.quhong.redis.GoodsListHomeRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class RippleSourceService implements ResourceService{
    private static final Logger logger = LoggerFactory.getLogger(RippleSourceService.class);
    public static final int TYPE_RIPPLE = 7; // 声波

    @Resource
    private RippleSourceDao rippleSourceDao;
    @Resource
    private RippleDao rippleDao;

    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;

    public PageResultVO<RippleSourceVO> rippleSourceList(ItemCondition condition){
        PageResultVO<RippleSourceVO> pageVO = new PageResultVO<>();
        Integer status = condition.getStatus();
        Integer itemType = condition.getItemType();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<RippleSourceData>  rippleSourceList = rippleSourceDao.selectRippleSourcePage(itemType, status, search, start, pageSize);
        // List<Integer> rippleIdList = rippleSourceList.stream().map(RippleSourceData::getRipple_id).collect(Collectors.toList());
        // Map<Integer, Integer> resourceGroupMap = rippleDao.findResourceGroupList(rippleIdList).stream().collect(Collectors.toMap(ResourceGroupData::getResourceId, ResourceGroupData::getCount));

        List<RippleSourceVO> voList = new ArrayList<>();
        for(RippleSourceData data: rippleSourceList){
            RippleSourceVO vo = new RippleSourceVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            vo.setCache_sources(data.getRipple_sources());
            // vo.setOwnUser(resourceGroupMap.getOrDefault(data.getRipple_id(), 0));
            vo.setOwnUser(0);
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(rippleSourceDao.selectCount(itemType, status, search));
        return pageVO;
    }


    public void addRippleSourceData(RippleSourceDTO dto){

        if(StringUtils.isEmpty(dto.getRipple_icon()) || StringUtils.isEmpty(dto.getRipple_sources())){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "资源图未上传");
        }

        RippleSourceData data = new RippleSourceData();
        BeanUtils.copyProperties(dto, data);
        RippleSourceData lastData = rippleSourceDao.getLastRippleSourceData();
        int nextId = lastData != null ? lastData.getRipple_id() + 1 : 1;
        data.setRipple_id(nextId);
        data.setC_time(DateHelper.getNowSeconds());
        rippleSourceDao.insert(data);
        if(data.getItem_type() == 5 && data.getIs_new() == 1 && data.getStatus() == 1){
            goodsListHomeRedis.addNewGoodsRankingScore(TYPE_RIPPLE, nextId);
        }


    }

    public void updateRippleSourceData(RippleSourceDTO dto) {

        RippleSourceData data = rippleSourceDao.getRippleSourceDataByID(dto.getDocId());
        if(data == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if(data.getItem_type() == 5 && data.getItem_type() != dto.getItem_type()){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "已上传的资源不能修改类型");
        }

        Update update = new Update();
        update.set("name", dto.getName() != null? dto.getName() : "");
        update.set("name_ar", dto.getName_ar() != null ? dto.getName_ar(): "");
        update.set("status", dto.getStatus());
        update.set("forder", dto.getForder());
        update.set("buy_type", dto.getBuy_type());
        update.set("beans", dto.getBeans());
        update.set("days", dto.getDays());
        update.set("is_new", dto.getIs_new());
        update.set("item_type", dto.getItem_type());

        if (!StringUtil.isEmpty(dto.getRipple_icon())){
            update.set("ripple_icon", dto.getRipple_icon());
        }

        if (!StringUtil.isEmpty(dto.getRipple_sources())){
            update.set("ripple_sources", dto.getRipple_sources());
        }
        rippleSourceDao.updateData(dto.getDocId(), update);

        if(data.getItem_type() == 5){

            if(dto.getIs_new() == 1 && dto.getStatus() == 1){
                goodsListHomeRedis.addNewGoodsRankingScore(TYPE_RIPPLE, data.getRipple_id());
            }

            if(dto.getIs_new() == 0 || dto.getStatus() == 0){
                goodsListHomeRedis.deleteItemNewGoodsRanking(TYPE_RIPPLE, data.getRipple_id());
            }

            if(dto.getStatus() == 0){
                goodsListHomeRedis.deleteItemNewGoodsRanking(TYPE_RIPPLE, data.getRipple_id());
                goodsListHomeRedis.deleteItemHotGoodsRanking(TYPE_RIPPLE, data.getRipple_id());
            }
        }
    }


    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<RippleSourceData>  rippleSourceList = rippleSourceDao.selectRippleSourcePage(-1, condition.getStatus(), search, start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for(RippleSourceData data: rippleSourceList){
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getRipple_id());
            vo.setResourceIcon(data.getRipple_icon());
            vo.setResourceNameEn(data.getName());
            vo.setResourceNameAr(data.getName_ar());
            vo.setResourcePrice(data.getBeans());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(rippleSourceDao.selectCount(-1, condition.getStatus(), search));
        return pageVO;
    }
}
