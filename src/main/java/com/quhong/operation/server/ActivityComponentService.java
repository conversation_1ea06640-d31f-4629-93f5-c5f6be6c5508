package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.constant.ActivityComponentConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.component.ComponentCommonConfig;
import com.quhong.data.component.ComponentLevelReward;
import com.quhong.data.component.ComponentRankReward;
import com.quhong.data.component.ComponentTaskConfig;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActivityComponentTemplateDao;
import com.quhong.mongo.data.ActivityComponentTemplate;
import com.quhong.mysql.dao.ActivityComponentDataDao;
import com.quhong.mysql.data.ActivityComponent;
import com.quhong.operation.share.condition.ActivityComponentCondition;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.service.ResourceKeyHandlerService;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ActivityComponentService {
    private static final Logger logger = LoggerFactory.getLogger(ActivityComponentService.class);

    @Resource
    private ActivityComponentDataDao activityComponentDataDao;
    @Resource
    private ActivityComponentTemplateDao activityComponentTemplateDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    /**
     * 组件列表操作
     */
    public PageResultVO<ActivityComponent> list(ActivityComponentCondition condition) {
        PageResultVO<ActivityComponent> pageVO = new PageResultVO<>();
        int componentType = condition.getComponentType();
        int status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        IPage<ActivityComponent> pageData = activityComponentDataDao.selectPageList(search, componentType, status, page, pageSize);
        pageVO.setList(pageData.getRecords());
        pageVO.setTotal(pageData.getTotal());
        return pageVO;
    }

    public void addData(ActivityComponent dto) {
        ActivityComponent data = new ActivityComponent();
        BeanUtils.copyProperties(dto, data);
        activityComponentDataDao.insert(data);
    }

    public void updateData(ActivityComponent dto) {
        ActivityComponent data = activityComponentDataDao.selectOne(dto.getId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "数据不存在");
        }
        BeanUtils.copyProperties(dto, data);
        activityComponentDataDao.update(data);
    }

    /**
     * 组件活动模板操作
     */
    public PageResultVO<ActivityComponentTemplate> templateList(BaseCondition condition) {
        PageResultVO<ActivityComponentTemplate> pageVO = new PageResultVO<>();
        int status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<ActivityComponentTemplate> pageData = activityComponentTemplateDao.selectPage(search, status, start, pageSize);
        pageVO.setList(pageData);
        pageVO.setTotal(activityComponentTemplateDao.selectCount(search, status));
        return pageVO;
    }


    public void addTemplate(ActivityComponentTemplate dto) {
        ActivityComponentTemplate data = new ActivityComponentTemplate();
        paramCheck(dto, true);
        BeanUtils.copyProperties(dto, data);
        int currentTime = DateHelper.getNowSeconds();
        data.setStatus(ActivityComponentConstant.STATUS_INIT);
        data.setCtime(currentTime);
        data.setMtime(currentTime);
        activityComponentTemplateDao.save(data);
    }

    public void updateTemplate(ActivityComponentTemplate dto) {
        ActivityComponentTemplate data = activityComponentTemplateDao.getDataByID(dto.getActivityId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "数据不存在");
        }
        paramCheck(dto, false);
        Update update = new Update();
        update.set("nameEn", dto.getNameEn());
        update.set("nameAr", dto.getNameAr());
        update.set("url", dto.getUrl());
        update.set("startTime", dto.getStartTime());
        update.set("endTime", dto.getEndTime());
        update.set("status", dto.getStatus());
        update.set("testStatus", dto.getTestStatus());
        update.set("rankConfigList", dto.getRankConfigList());
        update.set("drawConfigList", dto.getDrawConfigList());
        update.set("taskConfigList", dto.getTaskConfigList());
        activityComponentTemplateDao.updateData(dto.getActivityId(), update);
    }


    private void paramCheck(ActivityComponentTemplate dto, boolean insertFlag){
        String nameEn = dto.getNameEn();
        String nameAr = dto.getNameAr();
        if(ObjectUtils.isEmpty(nameEn) || ObjectUtils.isEmpty(nameAr)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动名不能为空");
        }

        if(dto.getStartTime() <= 0 || dto.getEndTime() <= 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动时间配置有误");
        }

        // 榜单组件check
        List<ComponentCommonConfig> rankConfigList = dto.getRankConfigList();
        if (!CollectionUtils.isEmpty(rankConfigList)){
            for (ComponentCommonConfig rankConfig : rankConfigList) {
                if (insertFlag){
                    rankConfig.setComponentMetaId(new ObjectId().toString());
                }

                if (rankConfig.getScaleSwitch() > 0 && rankConfig.getScaleData() <= 0){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-比例设置有误");
                }

                if(ObjectUtils.isEmpty(rankConfig.getTitleEn()) || ObjectUtils.isEmpty(rankConfig.getTitleAr())){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-名称不能为空");
                }

                if(rankConfig.getPeriod() < 0){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-统计周期设置有误");
                }

                if(rankConfig.getTimeSwitch() > 0 && (rankConfig.getStartTime() <= 0 || rankConfig.getEndTime() <= 0)){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-统计时间设置有误");
                }

                if(rankConfig.getCountryFilter() > 0 && ObjectUtils.isEmpty(rankConfig.getCountry())){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-国家过滤设置有误");
                }

                if(rankConfig.getGenderFilter() > 0 && rankConfig.getGender() < 0){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-性别过滤设置有误");
                }

                if(rankConfig.getGameFilter() > 0 && CollectionUtils.isEmpty(rankConfig.getGameItemList())){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-游戏过滤设置有误");
                }

                if(rankConfig.getGiftFilter() > 0 && CollectionUtils.isEmpty(rankConfig.getGiftIdList())){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-礼物过滤设置有误");
                }

                List<ComponentRankReward> rankRewardList = rankConfig.getRankRewardList();
                if(!CollectionUtils.isEmpty(rankConfigList)){
                    for (ComponentRankReward rankReward : rankRewardList) {
                        if(ObjectUtils.isEmpty(rankReward.getResourceKey())){
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-奖励配置-奖励资源key配置为空");
                        }
                        if (ObjectUtils.isEmpty(resourceKeyHandlerService.getConfigData(rankReward.getResourceKey()))){
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-奖励配置-未找到资源key配置");
                        }
                    }
                }

                // 等级奖励check
                List<ComponentLevelReward> levelRewardList = rankConfig.getLevelRewardList();
                if(!CollectionUtils.isEmpty(levelRewardList)){
                    for (ComponentLevelReward levelReward : levelRewardList) {
                        if(ObjectUtils.isEmpty(levelReward.getResourceKey())){
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-等级奖励-奖励资源key配置为空");
                        }
                        if (ObjectUtils.isEmpty(resourceKeyHandlerService.getConfigData(levelReward.getResourceKey()))){
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "榜单组件-等级奖励-未找到资源key配置");
                        }
                    }
                }
            }
        }

        // 抽奖组件check
        List<ComponentCommonConfig> drawConfigList = dto.getDrawConfigList();
        if (!CollectionUtils.isEmpty(drawConfigList)){
            for (ComponentCommonConfig drawConfig : drawConfigList) {
                if (insertFlag){
                    drawConfig.setComponentMetaId(new ObjectId().toString());
                }

                if (drawConfig.getScaleSwitch() > 0 && drawConfig.getScaleData() <= 0){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖组件-比例设置有误");
                }

                if(ObjectUtils.isEmpty(drawConfig.getTitleEn()) || ObjectUtils.isEmpty(drawConfig.getTitleAr())){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖组件-名称不能为空");
                }

                if(drawConfig.getPeriod() < 0){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖组件-奖池更新周期设置有误");
                }

                if(drawConfig.getTimeSwitch() > 0 && (drawConfig.getStartTime() <= 0 || drawConfig.getEndTime() <= 0)){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖组件-统计时间设置有误");
                }

                if(drawConfig.getUnitPrice() <= 0){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖组件-抽奖单价设置有误");
                }

                if(ObjectUtils.isEmpty(drawConfig.getResourceKey())){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖组件-抽奖资源key设置有误");
                }

                if (ObjectUtils.isEmpty(resourceKeyHandlerService.getConfigData(drawConfig.getResourceKey()))){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "抽奖组件-未找到资源key配置");
                }
            }
        }

        // 任务组件check
        List<ComponentTaskConfig> taskConfigList = dto.getTaskConfigList();
        if (!CollectionUtils.isEmpty(taskConfigList)){
            for (ComponentTaskConfig taskConfig : taskConfigList) {
                if (insertFlag){
                    taskConfig.setTaskMetaId(new ObjectId().toString());
                }

                if(taskConfig.getTimeSwitch() > 0 && (taskConfig.getStartTime() <= 0 || taskConfig.getEndTime() <= 0)){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-统计时间设置有误");
                }

                if (taskConfig.getTaskType() == ActivityComponentConstant.TASK_TYPE_GROUP && (ObjectUtils.isEmpty(taskConfig.getGroupNameEn()) || ObjectUtils.isEmpty(taskConfig.getGroupNameAr()))){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-组任务未配置组任务名称");
                }

                if(taskConfig.getPeriod() < 0){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-任务周期设置有误");
                }

                if (taskConfig.getTaskType() == ActivityComponentConstant.TASK_TYPE_GROUP && ObjectUtils.isEmpty(taskConfig.getGroupResKey())){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-组任务未配置组任务奖励key");
                }

                List<ComponentCommonConfig> taskList = taskConfig.getTaskList();
                if (CollectionUtils.isEmpty(taskList)){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-未配置任务列表");
                }

                for (ComponentCommonConfig task : taskList) {
                    if (insertFlag){
                        task.setComponentMetaId(new ObjectId().toString());
                    }

                    if(ObjectUtils.isEmpty(task.getTitleEn()) || ObjectUtils.isEmpty(task.getTitleAr())){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-任务名称不能为空");
                    }


                    if(task.getCountryFilter() > 0 && ObjectUtils.isEmpty(task.getCountry())){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-任务过滤设置有误");
                    }

                    if(task.getGenderFilter() > 0 && task.getGender() < 0){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-任务性别过滤设置有误");
                    }

                    if(task.getGameFilter() > 0 && CollectionUtils.isEmpty(task.getGameItemList())){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-任务游戏过滤设置有误");
                    }

                    if(task.getGiftFilter() > 0 && CollectionUtils.isEmpty(task.getGiftIdList())){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-任务礼物过滤设置有误");
                    }

                    if(task.getTotalProcess() <= 0){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-任务完成进度不能小于等于0");
                    }

                    if (taskConfig.getTaskType() != ActivityComponentConstant.TASK_TYPE_GROUP && ObjectUtils.isEmpty(task.getResourceKey())){
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务组件-任务奖励资源key未配置");
                    }
                }
            }
        }
    }
}
