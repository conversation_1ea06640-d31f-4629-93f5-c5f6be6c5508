package com.quhong.operation.server;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.LogType;
import com.quhong.mongo.dao.GiftSendNumDao;
import com.quhong.mongo.data.GiftSendNumData;
import com.quhong.mysql.dao.GiftSendNumberDao;
import com.quhong.operation.dao.GiftSendFixDao;
import com.quhong.operation.share.mongobean.GiftSendFix;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Service
public class GiftSyncService {
    private static final Logger logger = LoggerFactory.getLogger(GiftSyncService.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);

    // @PostConstruct
    // public void init() {
    //     BaseTaskFactory.getFactory().addSlow(new Task() {
    //         @Override
    //         protected void execute() {
    //             giftFixSendNumber();
    //         }
    //     });
    // }

    @Resource
    private GiftSendNumDao giftSendNumDao;
    @Resource
    private GiftSendNumberDao giftSendNumberDao;
    @Resource
    private GiftSendFixDao giftSendFixDao;


    private void syncGiftSendNumber(){
        // String startId = "5b18cf8caccfed0011305236";
        // String endId = "5c2a3d000000000000000000";  // 2019-01-01

        // String startId = "5c2a3d000000000000000000"; // 2019-01-01
        // String endId = "5cf14f800000000000000000";  // 2019-06-01

        // String startId = "5cf14f800000000000000000"; // 2019-06-01
        // String endId = "5dbb05000000000000000000";  // 2019-11-01

        // String startId = "5dbb05000000000000000000"; // 2019-11-01
        // String endId = "5e0b70800000000000000000";  // 2020-01-01

        // String startId = "5e0b70800000000000000000"; // 2020-01-01
        // String endId = "5ed3d4800000000000000000";  // 2020-06-01

        // String startId = "5ed3d4800000000000000000"; // 2020-06-01
        // String endId = "5fedf5800000000000000000";  // 2021-01-01

        // String startId = "5fedf5800000000000000000"; // 2021-01-01
        // String endId = "60b508000000000000000000";  // 2021-06-01

        // String startId = "60b508000000000000000000"; // 2021-06-01
        // String endId = "61cf29000000000000000000";  // 2022-01-01

        // String startId = "61cf29000000000000000000"; // 2022-01-01
        // String endId = "62963b800000000000000000";  // 2022-06-01

        // String startId = "62963b800000000000000000"; // 2022-06-01
        // String endId = "63b05c800000000000000000";  // 2023-01-01

        // String startId = "63b05c800000000000000000"; // 2023-01-01
        // String endId = "64776f000000000000000000";  // 2023-06-01

        // String startId = "64776f000000000000000000"; // 2023-06-01
        // String endId = "659190000000000000000000";  // 2024-01-01

        // String startId = "659190000000000000000000"; // 2024-01-01
        // String endId = "6659f4000000000000000000";  // 2024-06-01

        // String startId = "6659f4000000000000000000"; // 2024-06-01
        // String endId = "66d33e000000000000000000";  // 2024-09-01

        // String startId = "66d33e000000000000000000"; // 2024-09-01
        // String endId = "677415000000000000000000";  // 2025-01-01

        String startId = "677415000000000000000000"; // 2025-01-01
        String endId = "67af68800000000000000000";  // 2025-02-15

        msgLogger.info("syncGiftSendNumber start");
        int currentTime = DateHelper.getNowSeconds();
        List<GiftSendNumData> giftSendNumDataList = giftSendNumDao.getGiftSendNumList(startId, endId, -1);
        if (CollectionUtils.isEmpty(giftSendNumDataList)){
            msgLogger.info("syncGiftSendNumber giftSendNumDataList is empty");
            return;
        }

        for (GiftSendNumData giftSendNumData : giftSendNumDataList) {
            if (giftSendNumData.getSync() >= 1){
                continue;
            }
            msgLogger.info("giftSendNumData: {}", JSONObject.toJSONString(giftSendNumData));
            String aid = giftSendNumData.getAid();
            int giftId = giftSendNumData.getGiftId();
            int giftNum = giftSendNumData.getGiftNum();
            int mtime = giftSendNumData.getMtime();
            int ctime = giftSendNumData.get_id().getTimestamp();
            giftSendNumberDao.insertOrUpdateV2(aid, giftId, giftNum, mtime, ctime);
            Update update = new Update();
            update.set("sync", 1);
            giftSendNumDao.updateData(giftSendNumData.get_id().toString(), update);

        }
        msgLogger.info("syncGiftSendNumber end cost={}", DateHelper.getNowSeconds() - currentTime);
        syncMtimeGiftSendNumber(startId, endId, currentTime);
    }

    private void syncGiftSendNumberV2(){
        // String startId = "66facb000000000000000000"; // 2024-10-01
        // String endId = "6723a9800000000000000000";  // 2024-11-01

        String startId = "6723a9800000000000000000"; // 2024-11-01
        String endId = "677415000000000000000000";  // 2025-01-01

        msgLogger.info("syncGiftSendNumberV2 start");
        int currentTime = DateHelper.getNowSeconds();
        List<GiftSendNumData> giftSendNumDataList = giftSendNumDao.getGiftSendNumList(startId, endId, -1);
        if (CollectionUtils.isEmpty(giftSendNumDataList)){
            msgLogger.info("syncGiftSendNumber giftSendNumDataList is empty");
            return;
        }

        for (GiftSendNumData giftSendNumData : giftSendNumDataList) {
            msgLogger.info("giftSendNumData: {}", JSONObject.toJSONString(giftSendNumData));
            String aid = giftSendNumData.getAid();
            int giftId = giftSendNumData.getGiftId();
            int giftNum = giftSendNumData.getGiftNum();
            int mtime = giftSendNumData.getMtime();
            int ctime = giftSendNumData.get_id().getTimestamp();
            giftSendNumberDao.insertOrUpdateV2(aid, giftId, giftNum, mtime, ctime);
        }
        msgLogger.info("syncGiftSendNumberV2 end cost={}", DateHelper.getNowSeconds() - currentTime);
        syncMtimeGiftSendNumber(startId, endId, currentTime);
    }


    private void syncMtimeGiftSendNumber(String startId, String endId, int mtime){
        msgLogger.info("syncMtimeGiftSendNumber start");
        int currentTime = DateHelper.getNowSeconds();
        List<GiftSendNumData> giftSendNumDataList = giftSendNumDao.getGiftSendNumList(startId, endId, mtime);
        if (CollectionUtils.isEmpty(giftSendNumDataList)){
            msgLogger.info("syncMtimeGiftSendNumber giftSendNumDataList is empty");
            return;
        }

        for (GiftSendNumData giftSendNumData : giftSendNumDataList) {
            String aid = giftSendNumData.getAid();
            int giftId = giftSendNumData.getGiftId();
            int giftNum = giftSendNumData.getGiftNum();
            int gMtime = giftSendNumData.getMtime();
            int ctime = giftSendNumData.get_id().getTimestamp();
            giftSendNumberDao.insertOrUpdateV2(aid, giftId, giftNum, gMtime, ctime);
            msgLogger.info("syncMtimeGiftSendNumber giftSendNumData: {}", JSONObject.toJSONString(giftSendNumData));
        }
        msgLogger.info("syncMtimeGiftSendNumber end cost={}", DateHelper.getNowSeconds() - currentTime);
    }


    private void giftFixSendNumber(){
        msgLogger.info("giftFixSendNumber start");
        int currentTime = DateHelper.getNowSeconds();
        List<GiftSendFix> giftSendFixList = giftSendFixDao.getAllGiftSendFixList();
        if (CollectionUtils.isEmpty(giftSendFixList)){
            msgLogger.info("giftFixSendNumber giftSendNumDataList is empty");
            return;
        }

        for (GiftSendFix giftSendFix : giftSendFixList) {
            String aid = giftSendFix.getAid();
            int giftId = giftSendFix.getGiftId();

            List<GiftSendNumData> giftSendNumDataList = giftSendNumDao.getAllGiftSendNumByGiftId(aid, giftId);
            if (CollectionUtils.isEmpty(giftSendNumDataList)){
                return;
            }

            int giftSum = giftSendNumDataList.stream().mapToInt(GiftSendNumData::getGiftNum).sum();
            giftSendNumberDao.insertOrUpdateV3(aid, giftId, giftSum, currentTime, currentTime);
            msgLogger.info("giftFixSendNumber giftSendNumData: {}", JSONObject.toJSONString(giftSendFix));
        }
        msgLogger.info("giftFixSendNumber end cost={}", DateHelper.getNowSeconds() - currentTime);
    }

}
