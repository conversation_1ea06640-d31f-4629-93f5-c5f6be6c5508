package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.dao.MomentBackgroundDao;
import com.quhong.mysql.data.MomentBackgroundData;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.MomentBGCondition;
import com.quhong.operation.share.dto.MomentBGDTO;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.MomentBackGroundVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class MomentBackGroundService {
    private static final Logger logger = LoggerFactory.getLogger(MomentBackGroundService.class);

    @Resource
    private MomentBackgroundDao momentBackgroundDao;
    @Resource
    private ManagerDao managerDao;


    public PageResultVO<MomentBackGroundVO> momentBGPageList(MomentBGCondition condition){
        PageResultVO<MomentBackGroundVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        Integer backgroundType = condition.getBackgroundType();
        Integer costType = condition.getCostType();
        Integer status = condition.getStatus();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        IPage<MomentBackgroundData> recordPage = momentBackgroundDao.selectListPage(search, status, backgroundType, costType, page, pageSize);

        List<MomentBackGroundVO> voList = new ArrayList<>();
        for(MomentBackgroundData data: recordPage.getRecords()){
            MomentBackGroundVO vo = new MomentBackGroundVO();
            BeanUtils.copyProperties(data, vo);

            Manager manager = managerDao.getDataByUid(data.getUploadUid());
            if(manager != null){
                vo.setUploadActor(manager.getAccount());
            }
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(recordPage.getTotal());
        return pageVO;
    }


    public void addMomentBG(String uid, MomentBGDTO dto){

        MomentBackgroundData data = new MomentBackgroundData();
        int currentTime = DateHelper.getNowSeconds();
        BeanUtils.copyProperties(dto, data);
        data.setUploadUid(uid);
        data.setMtime(currentTime);
        data.setCtime(currentTime);

        momentBackgroundDao.insertOne(data);

    }

    public void updateMomentBG(MomentBGDTO dto) {

        MomentBackgroundData momentBackgroundData = momentBackgroundDao.selectById(dto.getId());
        if(momentBackgroundData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        BeanUtils.copyProperties(dto, momentBackgroundData);
        momentBackgroundData.setMtime(DateHelper.getNowSeconds());

        momentBackgroundDao.updateOne(momentBackgroundData);
    }

}
