package com.quhong.operation.server;

import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RoomBannerDao;
import com.quhong.mongo.data.RoomBannerData;
import com.quhong.operation.share.condition.BannerCondition;
import com.quhong.operation.share.dto.RoomBannerDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.RoomBannerVO;
import com.quhong.service.HomeBannerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class RoomBannerService {

    private static final Logger logger = LoggerFactory.getLogger(RoomBannerService.class);

    @Resource
    private RoomBannerDao bannerDao;
    @Resource
    private ActorDao actorDao;

    public PageResultVO<RoomBannerVO> roomBannerList(BannerCondition condition) {
        PageResultVO<RoomBannerVO> pageVO = new PageResultVO<>();
        Integer valid = condition.getValid();
        int showSidebar = condition.getShowSidebar() == null ? -1 : condition.getShowSidebar();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPage_size() == null ? 30 : condition.getPage_size();
        int start = (page - 1) * pageSize;
        List<RoomBannerData> mongoThemeList = bannerDao.selectPage(condition.getSearch(), showSidebar, valid, start, pageSize);
        List<RoomBannerVO> voList = new ArrayList<>();
        for (RoomBannerData data : mongoThemeList) {
            RoomBannerVO vo = new RoomBannerVO();
            BeanUtils.copyProperties(data, vo);
            vo.setBanner_id(data.get_id().toString());
            vo.setEvent(StringUtils.isEmpty(data.getEvent()) ? "" : data.getEvent());
            vo.setGame_type(StringUtils.isEmpty(data.getGame_type()) ? "" : data.getGame_type());
            vo.setRoom_icon(StringUtils.isEmpty(data.getRoom_icon()) ? "" : data.getRoom_icon());
            vo.setRoom_icon_ar(StringUtils.isEmpty(data.getRoom_icon_ar()) ? "" : data.getRoom_icon_ar());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(bannerDao.selectCount(condition.getSearch(), showSidebar, valid));
        return pageVO;
    }


    public void addRoomBanner(RoomBannerDTO dto) {
        RoomBannerData bannerData = new RoomBannerData();
        BeanUtils.copyProperties(dto, bannerData);

        bannerData.setTitle(dto.getTitle());
        bannerData.setTitle_ar(dto.getTitle_ar());
        bannerData.setIcon(dto.getIcon());
        bannerData.setIcon_ar(dto.getIcon_ar());
        bannerData.setRoom_icon(dto.getRoom_icon());
        bannerData.setRoom_icon_ar(dto.getRoom_icon_ar());
        bannerData.setUrl(dto.getUrl());
        bannerData.setValid(dto.getValid());
        bannerData.setBanner_order(dto.getBanner_order());
        bannerData.setBannerType(dto.getBannerType());
        bannerData.setWeb_type(dto.getWeb_type());
        bannerData.setGame_type(dto.getGame_type());
        bannerData.setShow_sidebar(dto.getShow_sidebar());
        bannerData.setStartTime(dto.getStartTime());
        bannerData.setEndTime(dto.getEndTime());
        bannerData.setHot(dto.getHot());

        if(dto.getFilterType() == HomeBannerService.FILTER_TYPE_USER){
            String[] ridList = dto.getFilterItem().split(",");
            List<String> uidList = new ArrayList<>();

            for (String ridStr : ridList) {
                ActorData actorData = actorDao.getActorByStrRid(ridStr);
                if(actorData == null){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                }
                uidList.add(actorData.getUid());
            }
            bannerData.setFilterItem(String.join(",", uidList));
        }

        bannerDao.saveOne(bannerData);
    }

    public void updateBannerData(RoomBannerDTO dto) {
        RoomBannerData data = bannerDao.getDataByID(dto.getBanner_id());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        Update update = new Update();
        update.set("title", dto.getTitle());
        update.set("title_ar", dto.getTitle_ar());
        update.set("valid", dto.getValid());
        update.set("event", dto.getEvent());
        update.set("url", dto.getUrl());
        update.set("banner_order", dto.getBanner_order());
        update.set("bannerType", dto.getBannerType());
        update.set("game_type", dto.getGame_type());
        update.set("web_type", dto.getWeb_type());
        update.set("show_sidebar", dto.getShow_sidebar());
        update.set("icon", dto.getIcon());
        update.set("icon_ar", dto.getIcon_ar());
        update.set("room_icon", dto.getRoom_icon());
        update.set("room_icon_ar", dto.getRoom_icon_ar());
        update.set("startTime", dto.getStartTime());
        update.set("endTime", dto.getEndTime());
        update.set("hot", dto.getHot());
        update.set("filterType", dto.getFilterType());
        update.set("filterItem", dto.getFilterItem());

        if(dto.getFilterType() == HomeBannerService.FILTER_TYPE_USER){
            String[] ridList = dto.getFilterItem().split(",");
            List<String> uidList = new ArrayList<>();
            for (String ridStr : ridList) {
                ActorData actorData = actorDao.getActorByStrRid(ridStr);
                if(actorData == null){
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                }
                uidList.add(actorData.getUid());
            }
            update.set("filterItem", String.join(",", uidList));
        }

        bannerDao.updateData(dto.getBanner_id(), update);
    }
}
