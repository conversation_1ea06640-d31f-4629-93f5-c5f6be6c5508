package com.quhong.operation.server;

import com.quhong.core.config.ServerConfig;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.PosterDao;
import com.quhong.mongo.data.Poster;
import com.quhong.operation.share.dto.PosterDTO;
import com.quhong.operation.share.vo.PageResultVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 海报管理
 */
@Service
public class PosterService {
    private static final Logger logger = LoggerFactory.getLogger(PosterService.class);

    private static  String POSTER_ADDRESS_TEMPLATE = "https://static.youstar.live/posters/?version=%s&posters=%s";

    @Resource
    private PosterDao posterDao;


    @PostConstruct
    public void postInit() {
        if(ServerConfig.isNotProduct()) {
            POSTER_ADDRESS_TEMPLATE = "https://test2.qmovies.tv/posters/?version=%s&posters=%s";
        }
    }

    /**
     * 海报列表分页查询
     */
    public PageResultVO<PosterDTO> list(Integer status, Integer page, Integer pageSize) {
        logger.info("查询海报列表 status={}, page={}, pageSize={}", status, page, pageSize);

        // 参数校验
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 12;
        }

        int start = (page - 1) * pageSize;

        // 查询数据
        List<Poster> posterList = posterDao.selectPage(status, start, pageSize);
        long total = posterDao.countPosters(status);

        // 转换为DTO
        List<PosterDTO> dtoList = new ArrayList<>();
        for (Poster poster : posterList) {
            PosterDTO dto = new PosterDTO();
            BeanUtils.copyProperties(poster, dto);
            dto.setPid(poster.get_id().toString());
            // 设置海报地址
            dto.setAddress(String.format(POSTER_ADDRESS_TEMPLATE, dto.getPid(), poster.getPoster_type()));
            dtoList.add(dto);
        }

        PageResultVO<PosterDTO> result = new PageResultVO<>();
        result.setTotal(total);
        result.setList(dtoList);

        logger.info("查询海报列表完成 total={}, size={}", total, dtoList.size());
        return result;
    }

    /**
     * 添加海报
     */
    public void add(PosterDTO dto) {
        logger.info("添加海报 dto={}", dto);

        // 参数校验
        if (dto == null) {
            logger.error("添加海报失败：dto为空");
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (StringUtils.isEmpty(dto.getTitleEn()) && StringUtils.isEmpty(dto.getTitleAr())) {
            logger.error("添加海报失败：标题不能为空");
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (StringUtils.isEmpty(dto.getPoster_url())) {
            logger.error("添加海报失败：海报图片不能为空");
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        // 转换为实体
        Poster poster = new Poster();
        BeanUtils.copyProperties(dto, poster);

        // 设置默认值
        if (poster.getStatus() == null) {
            poster.setStatus(1); // 默认有效
        }
        if (poster.getPoster_type() == null) {
            poster.setPoster_type(0); // 默认跳转类型
        }
        if (poster.getStepStatus() == null) {
            poster.setStepStatus(0); // 默认未开启跳转按钮
        }

        // 保存
        posterDao.save(poster);
        logger.info("添加海报成功 id={}", poster.get_id().toString());
    }

    /**
     * 更新海报
     */
    public void update(PosterDTO dto) {
        logger.info("更新海报 dto={}", dto);

        // 参数校验
        if (dto == null || StringUtils.isEmpty(dto.getPid())) {
            logger.error("更新海报失败：pid不能为空");
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        // 检查海报是否存在
        Poster existingPoster = posterDao.findOne(dto.getPid());
        if (existingPoster == null) {
            logger.error("更新海报失败：海报不存在 pid={}", dto.getPid());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        // 构建更新对象
        Update update = new Update();

        if (!StringUtils.isEmpty(dto.getTitleEn())) {
            update.set("titleEn", dto.getTitleEn());
        }
        if (!StringUtils.isEmpty(dto.getTitleAr())) {
            update.set("titleAr", dto.getTitleAr());
        }
        if (!StringUtils.isEmpty(dto.getPoster_url())) {
            update.set("poster_url", dto.getPoster_url());
        }
        if (!StringUtils.isEmpty(dto.getPoster_url_ar())) {
            update.set("poster_url_ar", dto.getPoster_url_ar());
        }
        if (dto.getStatus() != null) {
            update.set("status", dto.getStatus());
        }
        if (dto.getPoster_type() != null) {
            update.set("poster_type", dto.getPoster_type());
        }
        if (dto.getStepStatus() != null) {
            update.set("stepStatus", dto.getStepStatus());
        }
        if (dto.getStepConfig() != null) {
            update.set("stepConfig", dto.getStepConfig());
        }

        // 执行更新
        posterDao.updateData(dto.getPid(), update);
        logger.info("更新海报成功 pid={}", dto.getPid());
    }

}