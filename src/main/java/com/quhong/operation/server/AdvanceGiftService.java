package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.AdvancedGiftDao;
import com.quhong.mongo.data.AdvancedGiftData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.operation.share.dto.AdvanceGiftDTO;
import com.quhong.operation.share.vo.AdvancedGiftVO;
import com.quhong.operation.share.vo.GiftVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class AdvanceGiftService {
    private static final Logger logger = LoggerFactory.getLogger(AdvanceGiftService.class);
    private static final List<Integer> TEMP_NUM_LIST = Arrays.asList(1, 7, 17, 27, 77, 99, 666, 999, 9999);

    @Resource
    private AdvancedGiftDao advancedGiftDao;
    @Resource
    private GiftDao giftDao;

    public PageResultVO<AdvancedGiftVO> getDataList(AdvanceGiftDTO dto) {
        PageResultVO<AdvancedGiftVO> pageVO = new PageResultVO<>();
        int page = dto.getPage() != null ? dto.getPage() : 1;
        int size = dto.getPageSize() != null ? dto.getPageSize() : 20;
        int start = (page - 1) * size;
        int playType = dto.getPlayType();
        List<AdvancedGiftData> advancedList = advancedGiftDao.getAllAdvancedGift(playType, start, size);
        long total = advancedGiftDao.getAdvancedGiftCount(playType);
        List<AdvancedGiftVO> voList = new ArrayList<>();
        for (AdvancedGiftData data : advancedList) {
            AdvancedGiftVO vo = new AdvancedGiftVO();
            BeanUtils.copyProperties(data, vo);
            GiftData giftData = giftDao.getGiftFromDb(vo.getGiftId());
            vo.setGiftIcon(giftData != null ? giftData.getGicon() : "");
            vo.setStatus(giftData != null ? giftData.getStatus() : 1);
            if (playType == 0 || playType == 2) {
                GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
                vo.setZipInfo(zipInfoVO);
            }
            List<AdvancedGiftData.AdvancedConfig> configList = vo.getConfigList();
            for (int i = 0; i < configList.size(); i++) {
                AdvancedGiftData.AdvancedConfig item = configList.get(i);
                GiftData otherGiftData = giftDao.getGiftFromDb(item.getGiftId());
                item.setGiftIcon(otherGiftData != null ? otherGiftData.getGicon() : "");
                if (i == 0 && playType == 1) {
                    GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(otherGiftData.getZipInfo(), GiftVO.ZipInfoVO.class);
                    vo.setZipInfo(zipInfoVO);
                }
            }
            vo.setObjId(data.get_id().toString());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(total);
        return pageVO;
    }

    private GiftData checkGiftData(int giftId, boolean isCheckGamePlay) {
        return checkGiftData(giftId, isCheckGamePlay, 0, Collections.EMPTY_LIST);
    }

    private GiftData checkGiftData(int giftId, boolean isCheckGamePlay, int allowUpdateType, List<Integer> allowList) {
        GiftData giftData = giftDao.selectOne(giftId);
        String errorMsg = "";
        if (giftData == null) {
            errorMsg = String.format("giftId %s not find", giftId);
            logger.info(errorMsg);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
        }

        if (isCheckGamePlay) {
            if (allowUpdateType == 0) { // 新增的情况
                if (giftData.getGamePlay() > 0) {
                    errorMsg = String.format("giftId:%s gamePlay already set %s can not set new game play", giftId, giftData.getGamePlay());
                    logger.info(errorMsg);
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
                }
            } else {
                // 更新的情况
                logger.info("giftId:{} allowUpdateType:{} allowList:{} gameType:{}", giftId, allowUpdateType, allowList, giftData.getGamePlay());
                boolean isOk = giftData.getGamePlay() == 0 || (giftData.getGamePlay() == allowUpdateType && allowList.contains(giftId));
                if (!isOk) {
                    errorMsg = String.format("giftId:%s gamePlay already set %s can not update new game play", giftId, giftData.getGamePlay());
                    logger.info(errorMsg);
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
                }
            }
        }
        return giftData;
    }

    private GiftVO.ZipInfoVO updateZipInfo(GiftData giftData, GiftVO.ZipInfoVO fromZipInfoVO, int zType) {
        return updateZipInfo(giftData, fromZipInfoVO, zType, null);
    }


    private GiftVO.ZipInfoVO updateZipInfo(GiftData giftData, GiftVO.ZipInfoVO fromZipInfoVO, int zType, String gameId) {
        giftData.setGamePlay(zType);
        GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
        zipInfoVO.setZtype(zType);
        zipInfoVO.setWebType(1);
        zipInfoVO.setShowDetail(1);
        zipInfoVO.setDesc(fromZipInfoVO.getDesc());
        zipInfoVO.setDescAr(fromZipInfoVO.getDescAr());
        String fromUrl = fromZipInfoVO.getDescUrl();
        fromUrl = StringUtils.isEmpty(fromUrl) ? null : StringUtils.isEmpty(fromUrl.trim()) ? null : fromUrl.trim();
        if (gameId != null) {
            if (!StringUtils.isEmpty(fromUrl)) {
                UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(fromUrl);
                urlBuilder.replaceQueryParam("gameId", gameId);
                String newUrl = urlBuilder.build(false).encode().toUriString();
                zipInfoVO.setDescUrl(newUrl);
            } else {
                zipInfoVO.setDescUrl(null);
            }
        } else {
            zipInfoVO.setDescUrl(fromUrl);
        }
        zipInfoVO.setPropIcon(fromZipInfoVO.getPropIcon());
        zipInfoVO.setHeight(fromZipInfoVO.getHeight());
        zipInfoVO.setWidth(fromZipInfoVO.getWidth());
        giftData.setZipInfo(JSON.toJSONString(zipInfoVO));
        giftDao.updateOne(giftData);
        return zipInfoVO;
    }

    private void updateHotZipInfo(GiftData giftData) {
        giftData.setHot(1);
        GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
        zipInfoVO.setHot(1);
        giftData.setZipInfo(JSON.toJSONString(zipInfoVO));
        giftDao.updateOne(giftData);
    }

    private GiftData fillConfigList(boolean isUpdate, AdvancedGiftData advancedGiftData, Integer giftId, int playType, int playTimeOut, List<AdvancedGiftData.AdvancedConfig> configList, GiftVO.ZipInfoVO fromZipInfo) {
        String errorMsg = "";
        if (giftId == null || configList == null || fromZipInfo == null) {
            errorMsg = "param error giftId or configList or fromZipInfo is null ";
            logger.info(errorMsg);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
        }
        String gameId = advancedGiftData.get_id().toString();
        GiftData giftData = null;
        if (playType == 0) {
            List<Integer> targetList = new ArrayList<>();
            if (isUpdate) {
                targetList.add(advancedGiftData.getGiftId());
            }
            // 普通礼物
            giftData = !isUpdate ? checkGiftData(giftId, true)
                    : checkGiftData(giftId, true, GiftDao.GAME_PLAY_ADVANCED_GIFT, targetList);
            List<GiftData> otherGiftDataList = new ArrayList<>();
            for (AdvancedGiftData.AdvancedConfig item : configList) {
                Integer otherGiftId = item.getGiftId();
                Integer otherGiftNum = item.getGiftNum();
                String otherScreenEn = StringUtils.isEmpty(item.getScreenEn()) ? "" : item.getScreenEn();
                String otherScreenAr = StringUtils.isEmpty(item.getScreenAr()) ? "" : item.getScreenAr();
                if (otherGiftId == null || otherGiftNum == null) {
                    errorMsg = "configList item otherGiftId or otherGiftNum is null ";
                    logger.info(errorMsg);
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
                }
                if (!TEMP_NUM_LIST.contains(otherGiftNum)) {
                    errorMsg = String.format("otherGiftId %s can not set otherGiftNum to %s ", otherGiftId, otherGiftNum);
                    logger.info(errorMsg);
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
                }
                GiftData otherGiftData = checkGiftData(otherGiftId, false); // 进阶礼物
                item.setGiftIcon(otherGiftData.getGicon());
                item.setScreenEn(otherScreenEn);
                item.setScreenAr(otherScreenAr);
                item.setLockTimeOut(0);
                item.setRateNum(0);
                otherGiftDataList.add(otherGiftData);
            }
            if (isUpdate && !giftId.equals(advancedGiftData.getGiftId())) {
                deleteZipInfo(checkGiftData(advancedGiftData.getGiftId(), false));
            }
            updateZipInfo(giftData, fromZipInfo, GiftDao.GAME_PLAY_ADVANCED_GIFT, gameId);

            for (GiftData item : otherGiftDataList) {
                // 无效的特效礼物，改为hot状态,让客户端提前加载
                if(item.getBagGift() <= 0){
                    item.setBagGift(1);
                }

                if (item.getHot() != 1) {
                    updateHotZipInfo(item);
                }
            }


        } else if (playType == 1) {
            if (playTimeOut == 0) {
                errorMsg = "not set playTimeOut";
                logger.info(errorMsg);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
            }
            List<Integer> targetList = Collections.emptyList();
            if (isUpdate) {
                List<AdvancedGiftData.AdvancedConfig> oldList = advancedGiftData.getConfigList();
                targetList = oldList.stream().map(AdvancedGiftData.AdvancedConfig::getGiftId).collect(Collectors.toList());
            }
            giftData = checkGiftData(giftId, false); // 进阶礼物
            List<GiftData> otherGiftDataList = new ArrayList<>();
            for (AdvancedGiftData.AdvancedConfig item : configList) {
                Integer otherGiftId = item.getGiftId();
                Integer otherGiftNum = item.getGiftNum();
                String otherScreenEn = StringUtils.isEmpty(item.getScreenEn()) ? "" : item.getScreenEn();
                String otherScreenAr = StringUtils.isEmpty(item.getScreenAr()) ? "" : item.getScreenAr();
                if (otherGiftId == null || otherGiftNum == null) {
                    errorMsg = "configList item otherGiftId or otherGiftNum is null ";
                    logger.info(errorMsg);
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
                }
                GiftData otherGiftData = !isUpdate ? checkGiftData(otherGiftId, true)
                        : checkGiftData(otherGiftId, true, GiftDao.GAME_PLAY_ADVANCED_GIFT, targetList); // 普通礼物
                item.setGiftIcon(otherGiftData.getGicon());
                item.setScreenEn(otherScreenEn);
                item.setScreenAr(otherScreenAr);
                item.setLockTimeOut(0);
                item.setRateNum(0);
                otherGiftDataList.add(otherGiftData);
            }

            if (isUpdate) {
                List<Integer> newList = configList.stream().map(AdvancedGiftData.AdvancedConfig::getGiftId).collect(Collectors.toList());
                for (Integer oldGid : targetList) {
                    if (!newList.contains(oldGid)) {
                        deleteZipInfo(checkGiftData(oldGid, false));
                    }
                }
            }
            for (GiftData item : otherGiftDataList) {
                updateZipInfo(item, fromZipInfo, GiftDao.GAME_PLAY_ADVANCED_GIFT, gameId);
            }
            // 无效的特效礼物，改为hot状态,让客户端提前加载
            if(giftData.getBagGift() <= 0){
                giftData.setBagGift(1);
            }

            if (giftData.getHot() != 1) {
                updateHotZipInfo(giftData);
            }
        } else if (playType == 2) {
            List<Integer> targetList = new ArrayList<>();
            if (isUpdate) {
                targetList.add(advancedGiftData.getGiftId());
            }
            // 普通礼物
            giftData = !isUpdate ? checkGiftData(giftId, true)
                    : checkGiftData(giftId, true, GiftDao.GAME_PLAY_LOCK_GIFT, targetList);
            List<Integer> targetConfigList = Collections.emptyList();
            if (isUpdate) {
                List<AdvancedGiftData.AdvancedConfig> oldList = advancedGiftData.getConfigList();
                targetConfigList = oldList.stream().map(AdvancedGiftData.AdvancedConfig::getGiftId).collect(Collectors.toList());
            }
            List<GiftData> otherGiftDataList = new ArrayList<>();
            for (AdvancedGiftData.AdvancedConfig item : configList) {
                Integer otherGiftId = item.getGiftId();
                Integer otherGiftNum = item.getGiftNum();
                Integer lockTimeOut = item.getLockTimeOut();
                Integer rateNum = item.getRateNum();
                String otherScreenEn = StringUtils.isEmpty(item.getScreenEn()) ? "" : item.getScreenEn();
                String otherScreenAr = StringUtils.isEmpty(item.getScreenAr()) ? "" : item.getScreenAr();

                if (otherGiftId == null || otherGiftNum == null || lockTimeOut == null || rateNum == null) {
                    errorMsg = "configList item otherGiftId or otherGiftNum or lockTimeOut is null or rateNum is null";
                    logger.info(errorMsg);
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
                }
                // 解锁礼物
                GiftData otherGiftData = !isUpdate ? checkGiftData(otherGiftId, true)
                        : checkGiftData(otherGiftId, true, GiftDao.GAME_PLAY_LOCK_GIFT, targetConfigList);
                item.setGiftIcon(otherGiftData.getGicon());
                item.setScreenEn(otherScreenEn);
                item.setScreenAr(otherScreenAr);
                otherGiftDataList.add(otherGiftData);
            }

            if (isUpdate) {
                List<Integer> newList = configList.stream().map(AdvancedGiftData.AdvancedConfig::getGiftId).collect(Collectors.toList());
                for (Integer oldGid : targetConfigList) {
                    if (!newList.contains(oldGid)) {
                        deleteZipInfo(checkGiftData(oldGid, false));
                    }
                }

                if (!giftId.equals(advancedGiftData.getGiftId())) {
                    deleteZipInfo(checkGiftData(advancedGiftData.getGiftId(), false));
                }
            }

            updateZipInfo(giftData, fromZipInfo, GiftDao.GAME_PLAY_LOCK_GIFT, gameId);
            for (GiftData item : otherGiftDataList) {
                updateZipInfo(item, fromZipInfo, GiftDao.GAME_PLAY_LOCK_GIFT, gameId);
            }

        }
        if (giftData == null) {
            errorMsg = String.format("playType %s not support", playType);
            logger.info(errorMsg);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
        }
        return giftData;
    }


    public void addOrUpdateData(AdvanceGiftDTO dto) {
        String objId = dto.getObjId();
        int playType = dto.getPlayType();
        Integer giftId = dto.getGiftId();
        int playTimeOut = dto.getPlayTimeOut();
        Integer rateNum = dto.getRateNum();
        String screenEn = StringUtils.isEmpty(dto.getScreenEn()) ? "" : dto.getScreenEn();
        String screenAr = StringUtils.isEmpty(dto.getScreenAr()) ? "" : dto.getScreenAr();
        List<AdvancedGiftData.AdvancedConfig> configList = dto.getConfigList();
        GiftVO.ZipInfoVO fromZipInfo = dto.getZipInfo();
        boolean isUpdate = dto.isUpdate();
        String errorMsg = "";
        AdvancedGiftData advancedGiftData = null;
        GiftData giftData = null;
        int now = DateHelper.getNowSeconds();
        if (isUpdate) {
            if (StringUtils.isEmpty(objId)) {
                errorMsg = "objId is empty";
                logger.info(errorMsg);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
            }
            advancedGiftData = advancedGiftDao.getOneById(objId);
            if (advancedGiftData == null) {
                errorMsg = String.format("not find data objId is %s", objId);
                logger.info(errorMsg);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
            }
            playType = advancedGiftData.getPlayType();
            giftData = fillConfigList(isUpdate, advancedGiftData, giftId, playType, playTimeOut, configList, fromZipInfo);
        } else {
            ObjectId objectId = new ObjectId();
            advancedGiftData = new AdvancedGiftData();
            advancedGiftData.setCtime(now);
            advancedGiftData.set_id(objectId);
            giftData = fillConfigList(isUpdate, advancedGiftData, giftId, playType, playTimeOut, configList, fromZipInfo);
        }
        if (playType == 2) {
            advancedGiftData.setRateNum(rateNum);
            advancedGiftData.setLockGiftTag(dto.getLockGiftTag());
        }
        advancedGiftData.setGiftId(giftId);
        advancedGiftData.setGiftIcon(giftData.getGicon());
        advancedGiftData.setGiftVideo(dto.getGiftVideo());
        advancedGiftData.setPlayType(playType);
        advancedGiftData.setPlayTimeOut(playTimeOut);
        advancedGiftData.setScreenEn(screenEn);
        advancedGiftData.setScreenAr(screenAr);
        advancedGiftData.setConfigList(configList);
        advancedGiftData.setMtime(now);
        advancedGiftDao.save(advancedGiftData);
    }

    public void deleteData(AdvanceGiftDTO dto) {
        String objId = dto.getObjId();
        String errorMsg = "";
        if (StringUtils.isEmpty(objId)) {
            errorMsg = "objId is empty";
            logger.info(errorMsg);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
        }
        AdvancedGiftData advancedGiftData = advancedGiftDao.getOneById(objId);
        if (advancedGiftData == null) {
            errorMsg = String.format("not find data objId is %s", objId);
            logger.info(errorMsg);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
        }

        Integer playType = advancedGiftData.getPlayType();
        Integer giftId = advancedGiftData.getGiftId();
        List<AdvancedGiftData.AdvancedConfig> configList = advancedGiftData.getConfigList();
        List<Integer> targetList = configList.stream().map(AdvancedGiftData.AdvancedConfig::getGiftId).collect(Collectors.toList());

        if (playType == null || giftId == null) {
            errorMsg = String.format("playType or giftId is null objId is %s", objId);
            logger.info(errorMsg);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), errorMsg);
        }

        if (playType == 0) {
            GiftData giftData = checkGiftData(giftId, false);
            deleteZipInfo(giftData);

        } else if (playType == 1) {
            for (Integer item : targetList) {
                GiftData giftData = checkGiftData(item, false);
                deleteZipInfo(giftData);
            }

        } else if (playType == 2) {
            GiftData giftData = checkGiftData(giftId, false);
            deleteZipInfo(giftData);
            for (Integer item : targetList) {
                GiftData itemGiftData = checkGiftData(item, false);
                deleteZipInfo(itemGiftData);
            }
        }
        advancedGiftDao.deleteOneById(objId);
        logger.info("delete success objId:{} playType:{} giftId:{}", objId, playType, giftId);
    }

    private void deleteZipInfo(GiftData giftData) {
        giftData.setGamePlay(0);
        GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
        zipInfoVO.setZtype(null);
        zipInfoVO.setWebType(null);
        zipInfoVO.setShowDetail(null);
        zipInfoVO.setDesc(null);
        zipInfoVO.setDescAr(null);
        zipInfoVO.setDescUrl(null);
        zipInfoVO.setPropIcon(null);
        zipInfoVO.setHeight(null);
        zipInfoVO.setWidth(null);
        giftData.setZipInfo(JSON.toJSONString(zipInfoVO));
        giftDao.updateOne(giftData);
        logger.info("deleteZipInfo  success giftId:{} giftName:{} giftIcon:{}", giftData.getRid(), giftData.getGname(), giftData.getGicon());
    }

}
