package com.quhong.operation.server;

import com.quhong.api.AccountLoginService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.RegisterPhoneAccountDTO;
import com.quhong.data.vo.RegisterRespListVO;
import com.quhong.data.vo.RegisterRespVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.monitor.MonitorChecker;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.RoomUserOnlineDao;
import com.quhong.mysql.dao.SlaveRegisterOrLoginLogDao;
import com.quhong.mysql.dao.TnWhiteConfigDao;
import com.quhong.mysql.data.RegisterOrLoginLogData;
import com.quhong.mysql.data.TnWhiteConfigData;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.dao.ActorStatDao;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.LastLogin;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.TnWhiteAddVO;
import com.quhong.operation.share.vo.TnWhiteListVO;
import com.quhong.operation.share.vo.WahoQueryVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class TnConfigServer {

    private static final Logger logger = LoggerFactory.getLogger(TnConfigServer.class);
    private static final int SIZE = 50;
    private static final String REGISTER_DEVICE = "register_device";
    private static final int REGISTER_DEVICE_SHOW_ID = -1;

    @Autowired
    private TnWhiteConfigDao tnWhiteConfigDao;
    @Autowired
    private ActorDao actorDao;
    @Resource
    private AccountLoginService accountLoginService;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private SlaveRegisterOrLoginLogDao slaveRegisterOrLoginLogDao;
    @Resource
    private ActorStatDao actorStatDao;
    @Resource
    private RoomUserOnlineDao roomUserOnlineDao;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;

    private MonitorChecker monitorChecker;

    public TnConfigServer() {
        this.monitorChecker = new MonitorChecker("ustar_java_exception");
    }

    public HttpResult<?> addList(Set<String> sourceList, String tnId) {
        HttpResult<TnWhiteAddVO> result = new HttpResult<>();
        TnWhiteAddVO tnWhiteAddVO = new TnWhiteAddVO();
        Set<String> successList = new HashSet<>();
        Set<String> failList = new HashSet<>();
        List<TnWhiteConfigData> addList = new ArrayList<>();
        String toTnId;
        if (!StringUtils.isEmpty(tnId) && !StringUtils.isEmpty((toTnId = tnId.trim()))) {
            int length = toTnId.length();
            String toUid = REGISTER_DEVICE;
            if (length == 8) {
                // 输入的为请求id
                String reqId = toTnId;
                RegisterOrLoginLogData registerOrLoginLogData = slaveRegisterOrLoginLogDao.findOneByReqId(reqId);
                if (registerOrLoginLogData == null) {
                    return result.error("没有找到该请求id记录 reqId:" + toTnId);
                }
                toTnId = registerOrLoginLogData.getTnId();
                if (StringUtils.isEmpty(toTnId)) {
                    return result.error("没有找到该请求id记录的tnID reqId:" + toTnId);
                }
                if (!StringUtils.isEmpty(registerOrLoginLogData.getUid())) {
                    toUid = registerOrLoginLogData.getUid();
                }
                logger.info("find tnId by reqId success reqId:{} toTnId:{} toUid:{}", reqId, toTnId, toUid);
            }
            if (!tnWhiteConfigDao.queryItem(toTnId)) {
                TnWhiteConfigData tnWhiteConfigData = new TnWhiteConfigData();
                tnWhiteConfigData.setUid(toUid);
                tnWhiteConfigData.setTnId(toTnId);
                tnWhiteConfigData.setcTime(DateHelper.getNowSeconds());
                addList.add(tnWhiteConfigData);
            }
            successList.add(toTnId);
        } else {
            if (CollectionUtils.isEmpty(sourceList)) {
                return result.error("输入列表不能为空");
            }
            sourceList = sourceList.stream().filter(k -> !StringUtils.isEmpty(k.trim())).collect(Collectors.toSet());
            for (String rid : sourceList) {
                ActorData actorData;
                try {
                    actorData = actorDao.getActorByRid(Integer.parseInt(rid));
                } catch (NumberFormatException e) {
                    return result.error("输入的rid必须为数字");
                }
                if (null != actorData && !StringUtils.isEmpty(actorData.getTn_id())) {
                    if (!tnWhiteConfigDao.queryItem(actorData.getTn_id())) {
                        TnWhiteConfigData tnWhiteConfigData = new TnWhiteConfigData();
                        tnWhiteConfigData.setUid(actorData.getUid());
                        tnWhiteConfigData.setTnId(actorData.getTn_id());
                        tnWhiteConfigData.setcTime(DateHelper.getNowSeconds());
                        addList.add(tnWhiteConfigData);
                    }
                    successList.add(rid);
                } else {
                    failList.add(rid);
                }
            }
        }
        if (!CollectionUtils.isEmpty(addList)) {
            tnWhiteConfigDao.insertList(addList);
        }
        tnWhiteAddVO.setSuccessList(successList);
        tnWhiteAddVO.setFailList(failList);
        return result.ok(tnWhiteAddVO);
    }


    public HttpResult<?> deleteItem(int rid, String tnId) {
        int num = 0;
        if (!StringUtils.isEmpty(tnId)) {
            num = tnWhiteConfigDao.deleteItem(tnId.trim());
        } else {
            ActorData actorData = actorDao.getActorByRid(rid);
            if (null != actorData && !StringUtils.isEmpty(actorData.getTn_id())) {
                num = tnWhiteConfigDao.deleteItem(actorData.getTn_id());
            }
        }
        if (num > 0) {
            return new HttpResult<>().ok();
        } else {
            logger.info("delete fail rid={} tnId={}", rid, tnId);
            return new HttpResult<>().error("删除失败! 没有数据或者用户不存在");
        }
    }

    public HttpResult<?> listTnWhite(int rid, String tnId, int page) {
        int start = page > 1 ? (page - 1) * SIZE : 0;
        List<TnWhiteConfigData> listData;
        String toTnId;
        if (!StringUtils.isEmpty(tnId) && !StringUtils.isEmpty((toTnId = tnId.trim()))) {
            listData = tnWhiteConfigDao.getDataByPageByTnId(toTnId, start, SIZE);
        } else if (rid != 0) {
            ActorData actorByRid = actorDao.getActorByRid(rid);
            String uid = actorByRid != null ? actorByRid.getUid() : "";
            listData = tnWhiteConfigDao.getDataByPageByUid(uid, start, SIZE);
        } else {
            listData = tnWhiteConfigDao.getDataByPage(start, SIZE);
        }
        TnWhiteListVO tnWhiteListVO = new TnWhiteListVO();
        List<TnWhiteListVO.TnWhiteBean> listTn = new ArrayList<>();
        for (TnWhiteConfigData item : listData) {
            TnWhiteListVO.TnWhiteBean tnBean = new TnWhiteListVO.TnWhiteBean();
            String itemUid = item.getUid();
            if (REGISTER_DEVICE.equals(itemUid)) {
                tnBean.setRid(REGISTER_DEVICE_SHOW_ID);
            } else {
                ActorData actorData = actorDao.getActorDataFromCache(itemUid);
                tnBean.setRid(actorData.getRid());
            }
            tnBean.setTnId(item.getTnId());
            tnBean.setcTime(item.getcTime());
            listTn.add(tnBean);

        }
        tnWhiteListVO.setTnList(listTn);
        return new HttpResult<>().ok(tnWhiteListVO);
    }

    public HttpResult<?> registerHumanAccount(RegisterPhoneAccountDTO dto) {
        long startTime = System.currentTimeMillis();
        logger.info("registerHumanAccount dto:{}", dto);
        RegisterRespVO vo = accountLoginService.registerHumanAccount(dto);
        logger.info("registerHumanAccount cost:{} vo:{}", System.currentTimeMillis() - startTime, vo);
        return new HttpResult<>().ok(vo);
    }


    public HttpResult<?> getHumanAccountList(RegisterPhoneAccountDTO dto) {
        logger.info("getHumanAccountList dto:{}", dto);
        RegisterRespListVO vo = accountLoginService.getHumanAccountList(dto);
        List<RegisterRespVO> voList = vo.getList();
        if (!CollectionUtils.isEmpty(voList)) {
            for (RegisterRespVO item : voList) {
                String applicantUid = item.getApplicantUid();
                if (!StringUtils.isEmpty(applicantUid)) {
                    Manager manager = managerDao.getDataByUid(applicantUid);
                    String applicantName = manager != null ? manager.getAccount() : "unkown";
                    item.setApplicantName(applicantName);
                }
            }
        }
        logger.info("getHumanAccountList cost:{} toVo:{}", dto, vo);
        return new HttpResult<>().ok(vo);
    }

    public HttpResult<?> updateHumanAccount(RegisterPhoneAccountDTO dto) {
        long startTime = System.currentTimeMillis();
        logger.info("updateHumanAccount dto:{}", dto);
        RegisterRespVO vo = accountLoginService.updateHumanAccount(dto);
        logger.info("updateHumanAccount cost:{} vo:{}", System.currentTimeMillis() - startTime, vo);
        return new HttpResult<>().ok(vo);
    }

    public HttpResult<?> deleteHumanAccount(RegisterPhoneAccountDTO dto) {
        return new HttpResult<>().ok();
    }

    public HttpResult<?> wahoQuery(String wahoTnId) {
        long startTime = System.currentTimeMillis();
        logger.info("wahoQuery wahoTnId:{}", wahoTnId);
        if (StringUtils.isEmpty(wahoTnId)) {
            logger.info("parm is empty wahoTnId={}", wahoTnId);
            return new HttpResult<>().error("查询失败! 参数为空 wahoTnId=" + wahoTnId);
        }
        WahoQueryVO vo = new WahoQueryVO();
        List<Actor> actorList = actorStatDao.getLastLoginOutUsers(wahoTnId);
        int size = 0;
        Set<String> uidSet;
        if (!CollectionUtils.isEmpty(actorList)) {
            Actor one = actorList.get(0);
            String uid = one.get_id().toString();
            Integer rid = one.getRid();
            vo.setYoustarUid(uid);
            vo.setYoustarRid(rid);
            LastLogin lastLogin = one.getLastLogin();
            if (lastLogin != null) {
                Long logoutTime = lastLogin.getLogoutTime();
                vo.setLastLoginTime(lastLogin.getLoginTime());
                vo.setLastLogoutTime(logoutTime);
            }
            uidSet = actorList.stream().map(Actor -> Actor.get_id().toString()).collect(Collectors.toSet());
            long roomTime = roomUserOnlineDao.getStayRoomTimeBySet(uidSet);
            size = actorList.size();
            vo.setRoomTime(roomTime);
            vo.setRechargeAmount(actorPayExternalDao.getUserRechargeMoney(uid).toString());
        }

        logger.info("wahoQuery wahoTnId:{} cost:{} size:{} vo:{}", wahoTnId, System.currentTimeMillis() - startTime, size, vo);
        return new HttpResult<>().ok(vo);
    }


    public HttpResult<?> updateRoomScore(RegisterPhoneAccountDTO req) {
        long startTime = System.currentTimeMillis();
        logger.info("updateRoomScore dto:{}", req);
        if (req.getSize() <= 0) {
            return new HttpResult<>().ok(req);
        }
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(req.getUid(), req.getRoomId(), "", ""
                , CommonMqTaskConstant.TEST_ADD_EXP_ITEM, req.getSize()));

        logger.info("updateRoomScore cost:{} vo:{}", System.currentTimeMillis() - startTime, req);
        return new HttpResult<>().ok(req);
    }

    public HttpResult<?> getMangerList(RegisterPhoneAccountDTO dto) {
        logger.info("getMangerList dto:{}", dto);
        List<Manager> allMangers = managerDao.getAllManager();
        RegisterRespListVO vo = new RegisterRespListVO();
        List<RegisterRespVO> voList = new ArrayList<>();
        for (Manager item : allMangers) {
            RegisterRespVO toItem = new RegisterRespVO();
            toItem.setApplicantUid(item.get_id().toString());
            toItem.setApplicantName(item.getAccount());
            voList.add(toItem);
        }
        vo.setList(voList);
        vo.setTotal(allMangers.size());
        logger.info("getMangerList cost:{} toVo:{}", dto, vo);
        return new HttpResult<>().ok(vo);
    }

    public HttpResult<?> testPhoneWarning(RegisterPhoneAccountDTO req) {
        monitorChecker.startWarningWithPhone("testPhoneWarning告警",
                "opUserName: " + req.getOpUserName() +
                        "触发的测试电话告警");
        return new HttpResult<>().ok(req);
    }
}
