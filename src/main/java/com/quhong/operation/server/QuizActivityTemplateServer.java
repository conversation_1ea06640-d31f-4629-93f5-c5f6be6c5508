package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.QuestionGroupDTO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mysql.config.DBMysqlBean;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.httpResult.OperationHttpCode;
import com.quhong.operation.share.condition.QuizActivityCondition;
import com.quhong.operation.share.dto.CheckpointConfigDTO;
import com.quhong.operation.share.dto.QuestionDTO;
import com.quhong.operation.share.dto.QuizActivityTemplateDTO;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.QuestionVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/27
 */
@Service
public class QuizActivityTemplateServer {

    private static final Logger logger = LoggerFactory.getLogger(QuizActivityTemplateServer.class);

    private static final SimpleDateFormat YYYY_MM_DD_HH_MM_SS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("GMT+5:30");

    @Resource
    private QuestionGroupDao questionGroupDao;
    @Resource
    private QuestionDao questionDao;
    @Resource
    private QuestionAwardDao questionAwardDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private QuizActivityTemplateDao templateDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private QuizCheckpointConfigDao quizCheckpointConfigDao;

    /**
     * 新增题组
     */
    public void insertGroup(QuestionGroupDTO dto) {
        QuestionGroupData questionGroupData = questionGroupDao.selectOneByGid(dto.getGid());
        if (questionGroupData != null) {
            throw new CommonException(OperationHttpCode.GID_ALREADY_EXISTS);
        }
        QuestionGroupData data = new QuestionGroupData();
        Manager manager = managerDao.getDataByUid(dto.getUid());
        if (manager == null) {
            logger.error("can not find manage data. uid={}", dto.getUid());
            throw new CommonException(OperationHttpCode.MANAGE_NOT_EXIST);
        }
        data.setGid(dto.getGid());
        data.setStatus(dto.getStatus());
        data.setType(dto.getType());
        data.setLang(dto.getLang());
        data.setCreator(manager.getAccount());
        data.setCtime(DateHelper.getNowSeconds());
        questionGroupDao.insert(data);
    }

    /**
     * 修改题组
     */
    public void updateGroup(QuestionGroupDTO dto) {
        if (dto.getId() == null) {
            throw new CommonException(new HttpCode(1, "id is null"));
        }
        QuestionGroupData data = questionGroupDao.selectOne(dto.getId());
        data.setId(dto.getId());
        if (data.getStatus() != null) {
            data.setStatus(dto.getStatus());
        }
        if (data.getType() != null) {
            data.setType(dto.getType());
        }
        questionGroupDao.update(data);
    }

    /**
     * 删除题组
     */
    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public void deleteGroup(QuestionGroupDTO dto) {
        if (dto.getId() == null) {
            throw new CommonException(new HttpCode(1, "id is null"));
        }
        List<QuestionData> questionList = questionDao.selectList(dto.getGid());
        if (!CollectionUtils.isEmpty(questionList)) {
            for (QuestionData data : questionList) {
                questionDao.delete(data.getId());
            }
        }
        questionGroupDao.delete(dto.getId());
    }

    /**
     * 新增或修改题目
     */
    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public void save(QuestionDTO dto) {
        QuestionData data = buildQuestionData(dto);
        if (data.getId() == null) {
            // 新增题目
            questionDao.insert(data);
        } else {
            // 修改题目
            questionDao.update(data);
        }
        // 更新题组题目
        updateGroupName(data.getGid());
    }

    /**
     * 更新题组题目
     */
    private void updateGroupName(String gid) {
        QuestionData fristQuestion = questionDao.findFristQuestion(gid);
        QuestionGroupData data = questionGroupDao.selectOneByGid(gid);
        if (data == null) {
            return;
        }
        data.setName(fristQuestion != null ? fristQuestion.getContent() : "");
        questionGroupDao.update(data);
    }

    /**
     * 构建QuestionData
     */
    private QuestionData buildQuestionData(QuestionDTO dto) {
        QuestionData data = new QuestionData();
        data.setId(dto.getId());
        data.setGid(dto.getGid());
        data.setContent(dto.getContent());
        data.setPictureUrl(dto.getPictureUrl());
        data.setOptionContent(JSONObject.toJSONString(dto.getOptionContent()));
        data.setCorrectOption(dto.getCorrectOption());
        if (data.getId() == null) {
            data.setCtime(DateHelper.getNowSeconds());
        }
        return data;
    }

    /**
     * 获取题目列表
     */
    public QuestionVO getQuestionList(QuestionDTO dto) {
        QuestionVO vo = new QuestionVO();
        List<QuestionData> questionList = questionDao.selectList(dto.getGid());
        if (CollectionUtils.isEmpty(questionList)) {
            vo.setList(Collections.emptyList());
            return vo;
        }
        List<QuestionDTO> list = new ArrayList<>();
        for (QuestionData data : questionList) {
            QuestionDTO questionDto = new QuestionDTO();
            dataToDto(data, questionDto);
            list.add(questionDto);
        }
        vo.setList(list);
        return vo;
    }

    private void dataToDto(QuestionData data, QuestionDTO dto) {
        dto.setId(data.getId());
        dto.setGid(data.getGid());
        dto.setContent(data.getContent());
        dto.setPictureUrl(data.getPictureUrl());
        Map<String, String> optionContent;
        if (!StringUtils.isEmpty(data.getOptionContent())) {
            optionContent = JSON.parseObject(data.getOptionContent(), new TypeReference<HashMap<String, String>>() {});
        } else {
            optionContent = Collections.emptyMap();
        }
        dto.setOptionContent(optionContent);
        dto.setCorrectOption(data.getCorrectOption());
    }

    /**
     * 删除题目
     */
    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public void delete(QuestionDTO dto) {
        if (dto.getId() == null) {
            throw new CommonException(new HttpCode(1, "id is null"));
        }
        QuestionData data = questionDao.selectOne(dto.getId());
        if (data == null) {
            throw new CommonException(new HttpCode(1, "has been deleted"));
        }
        questionDao.delete(data.getId());
        // 更新题组题目
        updateGroupName(data.getGid());
    }

    /**
     * 新增或修改模板 id为null是新增 id不为null是修改
     */
    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public JSONObject saveTemplate(QuizActivityTemplateDTO dto) {
        JSONObject result = new JSONObject();
        QuizActivityTemplateData data = new QuizActivityTemplateData();
        if (dto.getId() == null) {
            logger.info("insert quiz activity. id={} quizType={}", dto.getId(), dto.getQuizType());
            // 新增答题活动模板
            data.setQuizType(dto.getQuizType() != null ? dto.getQuizType() : 0);
            data.setStatus(0);
            data.setCtime(new Date());
            data.setAcH5Url(ServerConfig.isProduct() ? "https://static.youstar.live/answer/" : "https://test2.qmovies.tv/answer/");
            if (dto.getFrontConfig() != null) {
                // 新增首页配置
                setFrontConfigInTemplate(dto.getFrontConfig(), data);
                templateDao.insert(data);
                result.put("id", data.getId());
                return result;
            }
            if (dto.getCheckpointConfig() != null) {
                // 新增关卡配置
                setCheckpointConfigInTemplate(dto.getCheckpointConfig(), data);
                templateDao.insert(data);
                saveQuizCheckpointConfig(data.getId(), dto.getCheckpointConfig());
                result.put("id", data.getId());
                return result;
            }
            if (dto.getAnswerConfig() != null) {
                // 新增答题配置
                setAnswerConfigInTemplate(dto.getAnswerConfig(), data);
                templateDao.insert(data);
                result.put("id", data.getId());
                return result;
            }
            if (dto.getRewardConfig() != null) {
                // 新增奖励配置
                data.setCorrectCardUrl(dto.getRewardConfig().getCorrectCardUrl());
                data.setErrorCardUrl(dto.getRewardConfig().getErrorCardUrl());
                templateDao.insert(data);
                saveQuestionAward(dto.getRewardConfig().getReward(), data.getId());
                result.put("id", data.getId());
                return result;
            }
            if (dto.getFontConfig() != null) {
                // 新增字体配置
                setFontConfigInTemplate(dto.getFontConfig(), data);
                templateDao.insert(data);
                result.put("id", data.getId());
                return result;
            }
        } else {
            logger.info("update quiz activity. id={}", dto.getId());
            // 修改答题活动模板
            data.setId(dto.getId());
            if (dto.getFrontConfig() != null) {
                // 修改首页配置
                setFrontConfigInTemplate(dto.getFrontConfig(), data);
                templateDao.update(data);
                result.put("id", data.getId());
                return result;
            }
            if (dto.getCheckpointConfig() != null) {
                // 修改关卡配置
                setCheckpointConfigInTemplate(dto.getCheckpointConfig(), data);
                templateDao.update(data);
                saveQuizCheckpointConfig(data.getId(), dto.getCheckpointConfig());
                result.put("id", data.getId());
                return result;
            }
            if (dto.getAnswerConfig() != null) {
                // 修改答题配置
                setAnswerConfigInTemplate(dto.getAnswerConfig(), data);
                templateDao.update(data);
                result.put("id", data.getId());
                return result;
            }
            if (dto.getRewardConfig() != null) {
                // 修改奖励配置
                data.setCorrectCardUrl(dto.getRewardConfig().getCorrectCardUrl());
                data.setErrorCardUrl(dto.getRewardConfig().getErrorCardUrl());
                saveQuestionAward(dto.getRewardConfig().getReward(), dto.getId());
                if (data.getCorrectCardUrl() != null || data.getErrorCardUrl() != null) {
                    templateDao.update(data);
                }
                result.put("id", data.getId());
                return result;
            }
            if (dto.getFontConfig() != null) {
                // 修改字体配置
                setFontConfigInTemplate(dto.getFontConfig(), data);
                templateDao.update(data);
                result.put("id", data.getId());
                return result;
            }
        }
        return result;
    }

    private void saveQuizCheckpointConfig(Integer templateId, QuizActivityTemplateDTO.CheckpointConfig checkpointConfig) {
        if (CollectionUtils.isEmpty(checkpointConfig.getCheckpointList())) {
            return;
        }
        // 全删全增
        quizCheckpointConfigDao.deleteByTemplateId(templateId);
        questionAwardDao.deleteByActivityId(templateId);
        int count = 1;
        for (CheckpointConfigDTO dto : checkpointConfig.getCheckpointList()) {
            QuizCheckpointConfigData data = new QuizCheckpointConfigData();
            BeanUtils.copyProperties(dto, data);
            data.setTemplateId(templateId);
            data.setCheckpointNo(count);
            quizCheckpointConfigDao.insert(data);
            if (!CollectionUtils.isEmpty(dto.getAwardList())) {
                for (QuestionAwardData awardData : dto.getAwardList()) {
                    awardData.setActivityId(templateId);
                    awardData.setCheckpointNo(count);
                    questionAwardDao.insert(awardData);
                }
            }
            count ++;
        }
    }

    private void setCheckpointConfigInTemplate(QuizActivityTemplateDTO.CheckpointConfig checkpointConfig, QuizActivityTemplateData data) {
        data.setErrorCardUrl(checkpointConfig.getErrorCardUrl());
        data.setCorrectCardUrl(checkpointConfig.getCorrectCardUrl());
        data.setCheckpointBgUrl(checkpointConfig.getCheckpointBgUrl());
        data.setCheckpointBgUrlAr(checkpointConfig.getCheckpointBgUrlAr());
        data.setCheckpointRewardCardUrl(checkpointConfig.getCheckpointRewardCardUrl());
        data.setSuccessRewardCard(checkpointConfig.getSuccessRewardCard());
        data.setFailureRewardCard(checkpointConfig.getFailureRewardCard());
        data.setCheckpointBackBtnUrl(checkpointConfig.getCheckpointBackBtnUrl());
        data.setGiveUpOrContinueBtn(checkpointConfig.getGiveUpOrContinueBtn());
        data.setGiveUpReviveBtn(checkpointConfig.getGiveUpReviveBtn());
    }

    private String getUrlWithParam(String url, Integer activityId) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        urlBuilder.queryParam("activityId", activityId);
        urlBuilder.queryParam("active_name", "quiz_master");
        return urlBuilder.build(false).encode().toUriString();
    }

    private void saveQuestionAward(List<QuestionAwardData> awardList, Integer activityId) {
        if (CollectionUtils.isEmpty(awardList)) {
            return;
        }
        // 全删全增
        questionAwardDao.deleteByActivityId(activityId);
        for (QuestionAwardData awardData : awardList) {
            awardData.setActivityId(activityId);
            questionAwardDao.insert(awardData);
        }
    }

    /**
     * 删除活动模板
     */
    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public void deleteTemplate(QuizActivityTemplateDTO dto) {
        questionAwardDao.deleteByActivityId(dto.getId());
        quizCheckpointConfigDao.deleteByTemplateId(dto.getId());
        templateDao.deleteById(dto.getId());
    }

    /**
     * 活动模板列表
     */
    public PageResultVO<QuizActivityTemplateDTO> getTemplateList(QuizActivityCondition condition) {
        PageResultVO<QuizActivityTemplateDTO> vo = new PageResultVO<>();
        List<QuizActivityTemplateData> templateDataList = templateDao.selectPage(condition.getPage(), condition.getPageSize(), condition.getGid(), condition.getAcName(), condition.getStatus(), condition.getQuizType());
        List<QuizActivityTemplateDTO> templateDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(templateDataList)) {
            String token = basePlayerRedis.getToken(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
            String uid = ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d";
            for (QuizActivityTemplateData templateData : templateDataList) {
                QuizActivityTemplateDTO dto = new QuizActivityTemplateDTO();
                // 首页配置
                QuizActivityTemplateDTO.FrontConfig frontConfig = getFrontConfigFromTemplate(templateData);
                dto.setFrontConfig(frontConfig);
                // 关卡配置
                QuizActivityTemplateDTO.CheckpointConfig checkpointConfig = getCheckpointConfigFromTemplate(templateData);
                dto.setCheckpointConfig(checkpointConfig);
                // 答题配置
                QuizActivityTemplateDTO.AnswerConfig answerConfig = getAnswerConfigFromTemplate(templateData);
                dto.setAnswerConfig(answerConfig);
                // 奖励配置
                QuizActivityTemplateDTO.RewardConfig rewardConfig = getRewardConfigFromTemplate(templateData);
                dto.setRewardConfig(rewardConfig);
                // 答题配置
                QuizActivityTemplateDTO.FontConfig fontConfig = getFontConfigFromTemplate(templateData);
                dto.setFontConfig(fontConfig);

                dto.setId(templateData.getId());
                dto.setQuizType(templateData.getQuizType());
                dto.setGid(templateData.getGid());
                dto.setArGid(templateData.getArGid());
                dto.setAcName(templateData.getAcName());
                dto.setAcNameAr(templateData.getAcNameAr());
                dto.setJoinType(templateData.getJoinType());
                dto.setAcBeginTime(dateToString(templateData.getAcBeginTime()));
                dto.setAcEndTime(dateToString(templateData.getAcEndTime()));
                dto.setAcH5Url(getUrlWithParam(templateData.getAcH5Url(), templateData.getId()));
                dto.setPagePreviewUrl("");
                dto.setCtime(dateToString(templateData.getCtime()));
                dto.setStatus(templateData.getStatus());
                dto.setUid(uid);
                dto.setToken(token);
                templateDTOList.add(dto);
            }
        }
        vo.setList(templateDTOList);
        vo.setTotal(templateDao.selectCount(condition.getGid(), condition.getAcName(), condition.getStatus(), condition.getQuizType()));
        return vo;
    }

    private QuizActivityTemplateDTO.CheckpointConfig getCheckpointConfigFromTemplate(QuizActivityTemplateData templateData) {
        QuizActivityTemplateDTO.CheckpointConfig checkpointConfig = new QuizActivityTemplateDTO.CheckpointConfig();
        checkpointConfig.setCorrectCardUrl(templateData.getCorrectCardUrl());
        checkpointConfig.setErrorCardUrl(templateData.getErrorCardUrl());
        checkpointConfig.setCheckpointBgUrl(templateData.getCheckpointBgUrl());
        checkpointConfig.setCheckpointBgUrlAr(templateData.getCheckpointBgUrlAr());
        checkpointConfig.setCheckpointRewardCardUrl(templateData.getCheckpointRewardCardUrl());
        checkpointConfig.setGiveUpOrContinueBtn(templateData.getGiveUpOrContinueBtn());
        checkpointConfig.setGiveUpReviveBtn(templateData.getGiveUpReviveBtn());
        checkpointConfig.setSuccessRewardCard(templateData.getSuccessRewardCard());
        checkpointConfig.setFailureRewardCard(templateData.getFailureRewardCard());
        checkpointConfig.setCheckpointBackBtnUrl(templateData.getCheckpointBackBtnUrl());
        List<CheckpointConfigDTO> list = new ArrayList<>();
        List<QuizCheckpointConfigData> checkpointConfigList = quizCheckpointConfigDao.selectByTemplateId(templateData.getId());
        if (!CollectionUtils.isEmpty(checkpointConfigList)) {
            for (QuizCheckpointConfigData checkpointConfigData : checkpointConfigList) {
                CheckpointConfigDTO checkpointConfigDTO = new CheckpointConfigDTO();
                BeanUtils.copyProperties(checkpointConfigData, checkpointConfigDTO);
                List<QuestionAwardData> awardList = questionAwardDao.selectByAcIdAndCheckpointNo(templateData.getId(), checkpointConfigData.getCheckpointNo());
                checkpointConfigDTO.setAwardList(CollectionUtils.isEmpty(awardList) ? new ArrayList<>() : awardList);
                list.add(checkpointConfigDTO);
            }
        }
        checkpointConfig.setCheckpointList(list);
        return checkpointConfig;
    }

    private QuizActivityTemplateDTO.RewardConfig getRewardConfigFromTemplate(QuizActivityTemplateData templateData) {
        QuizActivityTemplateDTO.RewardConfig rewardConfig = new QuizActivityTemplateDTO.RewardConfig();
        rewardConfig.setCorrectCardUrl(templateData.getCorrectCardUrl());
        rewardConfig.setErrorCardUrl(templateData.getErrorCardUrl());
        List<QuestionAwardData> awardList = questionAwardDao.selectByActivityId(templateData.getId());
        rewardConfig.setReward(CollectionUtils.isEmpty(awardList) ? Collections.emptyList() : awardList);
        return rewardConfig;
    }

    private String dateToString(Date date) {
        if (date == null) {
            return "";
        }
        YYYY_MM_DD_HH_MM_SS.setTimeZone(TIME_ZONE);
        return YYYY_MM_DD_HH_MM_SS.format(date);
    }

    private void setFrontConfigInTemplate(QuizActivityTemplateDTO.FrontConfig frontConfig, QuizActivityTemplateData data) {
        BeanUtils.copyProperties(frontConfig, data,"acBeginTime", "acEndTime");
        YYYY_MM_DD_HH_MM_SS.setTimeZone(TIME_ZONE);
        try {
            data.setAcBeginTime(YYYY_MM_DD_HH_MM_SS.parse(frontConfig.getAcBeginTime()));
            data.setAcEndTime(YYYY_MM_DD_HH_MM_SS.parse(frontConfig.getAcEndTime()));
        } catch (ParseException e) {
            logger.error(e.getMessage(), e);
        }
        if (data.getAcBeginTime().after(data.getAcEndTime())) {
            throw new CommonException(1, "活动开始时间不能大于活动结束时间");
        }
    }

    private QuizActivityTemplateDTO.FrontConfig getFrontConfigFromTemplate(QuizActivityTemplateData data)  {
        QuizActivityTemplateDTO.FrontConfig frontConfig = new QuizActivityTemplateDTO.FrontConfig();
        BeanUtils.copyProperties(data, frontConfig, "acBeginTime", "acEndTime");
        frontConfig.setAcBeginTime(dateToString(data.getAcBeginTime()));
        frontConfig.setAcEndTime(dateToString(data.getAcEndTime()));
        return frontConfig;
    }

    private void setAnswerConfigInTemplate(QuizActivityTemplateDTO.AnswerConfig answerConfig, QuizActivityTemplateData data) {
        BeanUtils.copyProperties(answerConfig, data,  "score");
        int score = 0;
        if (data.getOnceNum() != 0) {
            score = 100 / data.getOnceNum();
        }
        data.setScore(score);
    }

    private QuizActivityTemplateDTO.AnswerConfig getAnswerConfigFromTemplate(QuizActivityTemplateData data) {
        QuizActivityTemplateDTO.AnswerConfig answerConfig = new QuizActivityTemplateDTO.AnswerConfig();
        BeanUtils.copyProperties(data, answerConfig);
        return answerConfig;
    }

    private void setFontConfigInTemplate(QuizActivityTemplateDTO.FontConfig fontConfig, QuizActivityTemplateData data) {
        BeanUtils.copyProperties(fontConfig, data);
    }

    private QuizActivityTemplateDTO.FontConfig getFontConfigFromTemplate(QuizActivityTemplateData data) {
        QuizActivityTemplateDTO.FontConfig fontConfig = new QuizActivityTemplateDTO.FontConfig();
        BeanUtils.copyProperties(data, fontConfig);
        return fontConfig;
    }

    public JSONObject getGidList(QuestionGroupDTO dto) {
        JSONObject result = new JSONObject();
        List<QuestionGroupData> questionGroupList = questionGroupDao.selectGidByLang(dto.getLang());
        List<String> gidList;
        if (!CollectionUtils.isEmpty(questionGroupList)) {
            gidList = questionGroupList.stream().map(QuestionGroupData::getGid).collect(Collectors.toList());
        } else {
            gidList = new ArrayList<>();
        }
        result.put("gidList", gidList);
        return result;
    }
}
