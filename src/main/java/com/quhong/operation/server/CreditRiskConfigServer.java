package com.quhong.operation.server;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendRecvFirstJoinData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.RiskDeviceDao;
import com.quhong.mysql.dao.RiskUserDao;
import com.quhong.mysql.data.RiskDeviceData;
import com.quhong.mysql.data.RiskUserData;
import com.quhong.operation.share.dto.CreditRiskDTO;
import com.quhong.operation.share.vo.CreditRiskExcelVo;
import com.quhong.operation.share.vo.CreditRiskVO;
import com.quhong.service.CreditRiskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CreditRiskConfigServer {

    private static final Logger logger = LoggerFactory.getLogger(CreditRiskConfigServer.class);
    private static final List<Integer> VALID_STATUS_LIST = Arrays.asList(1, 2, 3);
    /**
     * 总得分倒序排序
     */
    private static final Comparator<CreditRiskExcelVo> TOTAL_SCORE_DESC = Comparator.comparing(CreditRiskExcelVo::getTotalScore).reversed();

    @Resource
    private RiskUserDao riskUserDao;
    @Resource
    private RiskDeviceDao riskDeviceDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CreditRiskService creditRiskService;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;

    public CreditRiskVO query(CreditRiskDTO dto) {
        String keyId = dto.getKeyId().trim();
        if (StringUtils.isEmpty(keyId)) {
            logger.error("keyId:{} is empty ", keyId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int keyType = dto.getKeyType();
        List<CreditRiskVO.CreditRiskItemVO> creditRiskList = new ArrayList<>();
        int totalScore = 0;
        if (keyType == 0) {
            ActorData actorData = actorDao.getActorByRid(Integer.parseInt(keyId));
            totalScore = fillCreditRiskListData(keyId, actorData, creditRiskList);
        } else if (keyType == 1) {
            RiskDeviceData riskDeviceData = riskDeviceDao.selectOne(keyId);
            if (riskDeviceData != null) {
                totalScore = riskDeviceData.getScore();
                creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(keyId, 1, CreditRiskService.AVAILABLE_STATUS, riskDeviceData.getScore()));
            } else {
                creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(keyId, 1, CreditRiskService.AVAILABLE_STATUS, 0));
            }
        } else if (keyType == 2) {
            RiskDeviceData riskDeviceData = riskDeviceDao.selectOne(keyId);
            if (riskDeviceData != null) {
                totalScore = riskDeviceData.getScore();
                creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(keyId, 2, CreditRiskService.AVAILABLE_STATUS, riskDeviceData.getScore()));
            } else {
                creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(keyId, 2, CreditRiskService.AVAILABLE_STATUS, 0));
            }
        } else if (keyType == 3) {
            ActorData actorData = actorDao.getActorData(keyId);
            totalScore = fillCreditRiskListData(keyId, actorData, creditRiskList);
        } else {
            logger.error("不支持的keyType={}", keyType);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        CreditRiskVO vo = new CreditRiskVO();
        vo.setCreditRiskList(creditRiskList);
        vo.setTotalScore(totalScore);
        return vo;
    }

    private int fillCreditRiskListData(String keyId, ActorData actorData, List<CreditRiskVO.CreditRiskItemVO> creditRiskList) {
        int totalScore = 0;
        if (actorData != null) {
            String uid = actorData.getUid();
            String tnId = actorData.getTn_id();
            String ip = actorData.getIp();
            RiskUserData riskUserData = riskUserDao.selectOne(uid);
            if (riskUserData != null) {
                totalScore += riskUserData.getScore();
                creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(uid, 3, riskUserData.getStatus(), riskUserData.getScore()));
            } else {
                totalScore += CreditRiskService.MAX_SCORE;
                creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(uid, 3, CreditRiskService.AVAILABLE_STATUS, CreditRiskService.MAX_SCORE));
            }

            if (!StringUtils.isEmpty(tnId)) {
                RiskDeviceData riskDeviceData = riskDeviceDao.selectOne(tnId);
                if (riskDeviceData != null) {
                    totalScore += riskDeviceData.getScore();
                    creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(tnId, 1, CreditRiskService.AVAILABLE_STATUS, riskDeviceData.getScore()));
                } else {
                    creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(tnId, 1, CreditRiskService.AVAILABLE_STATUS, 0));
                }
            }
            if (!StringUtils.isEmpty(ip)) {
                RiskDeviceData riskDeviceData = riskDeviceDao.selectOne(ip);
                if (riskDeviceData != null) {
                    totalScore += riskDeviceData.getScore();
                    creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(ip, 2, CreditRiskService.AVAILABLE_STATUS, riskDeviceData.getScore()));
                } else {
                    creditRiskList.add(new CreditRiskVO.CreditRiskItemVO(ip, 2, CreditRiskService.AVAILABLE_STATUS, 0));
                }
            }
        } else {
            logger.error("not find keyId={}", keyId);
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "该用户不存在");
        }
        return totalScore;
    }


    public CreditRiskVO updateData(CreditRiskDTO dto) {
        String keyId = dto.getKeyId().trim();
        if (StringUtils.isEmpty(keyId)) {
            logger.error("keyId:{} is empty ", keyId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int keyType = dto.getKeyType();
        Integer toScore = dto.getToScore();
        Integer toStatus = dto.getToStatus();

        Integer score = null;
        Integer status = null;
        if (toScore != null) {
            if (keyType == 3 && toScore >= 0) {
                score = Math.min(toScore, CreditRiskService.MAX_SCORE);
            } else if ((keyType == 1 || keyType == 2) && toScore <= 0) {
                score = Math.max(toScore, CreditRiskService.MIN_SCORE);
            }
        }

        if (toStatus != null && VALID_STATUS_LIST.contains(toStatus)) {
            status = toStatus;
        }

        if (score == null && status == null) {
            logger.error("keyId:{} keyType:{} toScore:{} toStatus:{}", keyId, keyType, toScore, toStatus);
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "toScore or toStatus 传参错误");
        }

        if (keyType == 0) {
            logger.error("keyId:{} keyType:{} ", keyId, keyType);
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "传参错误，更改数据keyType不能传rid类型");
        } else if (keyType == 1) {
            creditRiskService.reSetRiskDeviceScore(keyId, CreditRiskService.TN_DEVICE_TYPE, score);
        } else if (keyType == 2) {
            creditRiskService.reSetRiskDeviceScore(keyId, CreditRiskService.IP_DEVICE_TYPE, score);
        } else if (keyType == 3) {
            creditRiskService.reSetRiskUserScore(keyId, score, status);
            ActorData actorData = actorDao.getActorData(keyId);
            if (actorData == null) {
                logger.error("not find keyId={}", keyId);
                throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "该用户不存在");
            }
            String desc = " 分数改变:" + score + " 状态改变:" + status;
            // creditRiskService.eventLog(keyId, CreditRiskService.TYPE_OPERATION_WHITE_CHANGE, 0, actorData.getTn_id(), actorData.getIp(), desc, true);
        }
        return query(dto);
    }

    public List<CreditRiskExcelVo> getCreditRiskExcelVoList(CreditRiskDTO dto) {

        int userStatus = dto.getUserStatus() == null ? 1 : dto.getUserStatus();
        Integer startScore = dto.getStartScore();
        Integer endScore = dto.getEndScore();

        int now = DateHelper.getNowSeconds();
        List<RiskUserData> riskUserDataList = riskUserDao.selectAll(userStatus, now - 180 * 86400, now);

        List<CreditRiskExcelVo> creditRiskExcelVoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(riskUserDataList)) {
            List<RiskDeviceData> allDevice = riskDeviceDao.selectAll(1, now - 180 * 86400, now);
            List<RiskDeviceData> allIp = riskDeviceDao.selectAll(2, now - 180 * 86400, now);
            Map<String, Integer> allDeviceMap = allDevice.stream().collect(Collectors.toMap(RiskDeviceData::getKeyId, RiskDeviceData::getScore));
            Map<String, Integer> allIpMap = allIp.stream().collect(Collectors.toMap(RiskDeviceData::getKeyId, RiskDeviceData::getScore));
            for (RiskUserData item : riskUserDataList) {
                String uid = item.getUid();
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                String tnId = actorData.getTn_id();
                String ip = actorData.getIp();
                int deviceScore = allDeviceMap.getOrDefault(tnId, 0);
                int ipScore = allIpMap.getOrDefault(ip, 0);
                int totalScore = item.getScore() + deviceScore + ipScore ; // Math.max(item.getScore() + deviceScore + ipScore, 0)
                CreditRiskExcelVo excelVo = new CreditRiskExcelVo();
                excelVo.setRid(actorData.getRid());
                excelVo.setUid(uid);
                excelVo.setTnId(tnId);
                excelVo.setIp(ip);
                excelVo.setTotalCharge(actorPayExternalDao.getUserRechargeMoney(uid).intValue());
                excelVo.setTotalScore(totalScore);
                excelVo.setUserScore(item.getScore());
                excelVo.setDeviceScore(deviceScore);
                excelVo.setIpScore(ipScore);
                creditRiskExcelVoList.add(excelVo);
            }
        }
        creditRiskExcelVoList.sort(TOTAL_SCORE_DESC);

        return creditRiskExcelVoList;
    }


}
