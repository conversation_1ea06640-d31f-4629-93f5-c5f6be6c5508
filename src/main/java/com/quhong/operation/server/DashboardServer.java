package com.quhong.operation.server;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.*;
import com.quhong.operation.share.mysql.Dau;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.DashboardListVO;
import com.quhong.operation.share.vo.DashboardVO;
import com.quhong.operation.utils.DateHelper;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/7/29
 */
@Service
public class DashboardServer {

    private final static Logger logger = LoggerFactory.getLogger(DashboardServer.class);

    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private RoomDao roomDao;
    @Autowired
    private AdminDauDao dauDao;
    @Autowired
    private RoomTimeDao roomTimeDao;
    @Autowired
    private RoomMsgDao roomMsgDao;
    @Autowired
    private GiftRecordMgDao giftRecordMgDao;
    @Autowired
    private ElasticsearchDao elasticsearchDao;

    public ApiResult<DashboardListVO> statAllDashboardData(Integer startTime, Integer endTime) {
        logger.info("method statAllDashboardData param startTime={}, endTime={}", startTime, endTime);
        ApiResult<DashboardListVO> result = new ApiResult<>();
        DashboardListVO vo = new DashboardListVO();

//        vo.setActiveActor(totalActiveActor());
//        vo.setNewActor(totalNewActorCount());
//        vo.setOldActor(totalActiveNewActor());
//        vo.setChargePerson(totalChargePerson());
//        vo.setCreateRoomNum(totalCreateRoom());
//        vo.setChatPerson(totalChatPerson());
//        vo.setSendGiftPerson(totalSendGift());
//        vo.setRoomTimeAvg(totalRoomInTime());

        return result.ok(vo);
    }

    /**
     * 统计活跃用户
     *
     * @return 统计信息
     */
    private DashboardVO totalActiveActor() {
        DashboardVO vo = new DashboardVO();

        // 昨天全部活跃用户
        Integer[] yesterdayTime = yesterdayTime(null);
        List<Dau> onlineActor = activeActorCount(yesterdayTime[0], yesterdayTime[1]);
        Integer yesterday = 0;
        if (null != onlineActor) {
            yesterday = onlineActor.size();
            vo.setYesterday(yesterday.toString());
            Integer ios = 0;
            Integer android = 0;
            for (Dau dau : onlineActor) {
                if ("1".equals(dau.getId())) {
                    ios++;
                } else if ("0".equals(dau.getOs())) {
                    android++;
                }
            }
            // 昨天全部ios活跃用户
            vo.setIos(ios.toString());
            // 昨天全部Android活跃用户
            vo.setAndroid(android.toString());
        }

        // 同比计算
        Integer[] timeArr = preMonthToday(new Date(yesterdayTime[0] * 1000L));
        Integer preMonthYesterdayNum = activeActorCount(timeArr[0], timeArr[1], null);
        Double oyOyBasis = comparison(yesterday, preMonthYesterdayNum);
        vo.setOyOyBasis(oyOyBasis);

        // 最近7天的活跃数
        Integer before7dayStart = nDayBeforeStartTime(new Date(yesterdayTime[1] * 1000L), 7);
        Integer lately = activeActorCount(before7dayStart, yesterdayTime[1], null);
        vo.setLately(lately.toString());

        // 环比
        Integer before14dayStart = nDayBeforeStartTime(new Date(before7dayStart * 1000L), 7);
        Integer lastLast7day = activeActorCount(before14dayStart, before7dayStart, null);
        Double cycleRatio = comparison(lately, lastLast7day);
        vo.setCycleRatio(cycleRatio);

        return vo;
    }

    /**
     * 获取活跃用户数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 活跃用户数
     */
    private List<Dau> activeActorCount(Integer startTime, Integer endTime) {
        ApiResult<List<Dau>> result = dauDao.getActorOnline(startTime, endTime, null);
        if (result.isOK() && null != result.getData()) {
            logger.info("activeActorCount {}", result.getData().size());
            return result.getData();
        }
        logger.info("activeActorCount error info {}", result.getMsg());
        return null;
    }

    /**
     * 获取活跃用户数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param os        是否ios
     * @return 活跃用户数
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Integer activeActorCount(Integer startTime, Integer endTime, Integer os) {
        ApiResult<Set<String>> result = dauDao.getActiveActorByWhere(startTime, endTime, os, null, null, -1);
        if (result.isOK() && null != result.getData()) {
            logger.info("activeActorCount {}", result.getData().size());
            return result.getData().size();
        }
        logger.info("activeActorCount error info {}", result.getMsg());
        return 0;
    }

    /**
     * 统计新增用户数量
     *
     * @return 数量
     */
    private DashboardVO totalNewActorCount() {
        DashboardVO vo = new DashboardVO();

        // 昨天新增用户
        Integer[] yesterdayTime = yesterdayTime(null);
        Integer yesterday = newActorCount(yesterdayTime[0], yesterdayTime[1], null);
        vo.setYesterday(yesterday.toString());

        // 昨天新增ios用户
        Integer ios = newActorCount(yesterdayTime[0], yesterdayTime[1], "1");
        vo.setIos(ios.toString());

        // 昨天新增Android用户
        Integer android = newActorCount(yesterdayTime[0], yesterdayTime[1], "0");
        vo.setAndroid(android.toString());

        // 同比计算
        Integer[] timeArr = preMonthToday(new Date(yesterdayTime[0] * 1000L));
        Integer preMonth = newActorCount(timeArr[0], timeArr[1], null);
        Double oyOyBasis = comparison(yesterday, preMonth);
        vo.setOyOyBasis(oyOyBasis);

        // 最近七天新增用户
        Integer before7dayStart = nDayBeforeStartTime(new Date(yesterdayTime[1] * 1000L), 7);
        Integer lately = newActorCount(before7dayStart, yesterdayTime[1], null);
        vo.setLately(lately.toString());

        // 环比计算
        Integer before14dayStart = nDayBeforeStartTime(new Date(before7dayStart * 1000L), 7);
        Integer lastLast7day = newActorCount(before14dayStart, before7dayStart, null);
        Double cycleRatio = comparison(lately, lastLast7day);
        vo.setCycleRatio(cycleRatio);

        return vo;
    }

    /**
     * 统计一段时间内新增的用户
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param os        1表示ios,0表示安卓 不传则查全部
     * @return 某段时间内新增的用户
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public int newActorCount(Integer startTime, Integer endTime, String os) {
        ApiResult<Integer> result = actorDao.totalNewActor(startTime, endTime, os);
        if (result.isOK() && null != result.getData()) {
            return result.getData();
        }
        return 0;
    }

    /**
     * 统计活跃新用户(次留)
     *
     * @return 统计信息
     */
    private DashboardVO totalActiveNewActor() {
        DashboardVO vo = new DashboardVO();
        // 昨天
        Integer[] yesterdayTime = yesterdayTime(null);
        Integer[] newTime = yesterdayTime(new Date(yesterdayTime[0] * 1000L));

        List<Dau> activeActor = getActiveActor(yesterdayTime[0], yesterdayTime[1]);
        // 昨天活跃用户信息为空，直接返回vo;
        if (null == activeActor) return vo;

        Double yesterday = activeNewActorCount(activeActor, null, newTime[0], newTime[1]);
        vo.setYesterday(yesterday.toString());

        // 昨天ios
        Double ios = activeNewActorCount(activeActor, 1, newTime[0], newTime[1]);
        vo.setIos(ios.toString());

        // 昨天Android
        Double android = activeNewActorCount(activeActor, 0, newTime[0], newTime[1]);
        vo.setAndroid(android.toString());

        // 7天前的
        Integer start = nDayBeforeStartTime(new Date(yesterdayTime[0] * 1000L), 7);
        Integer end = start + 24 * 60 * 60;
        Double lately = activeNewActorCount(yesterdayTime[0], yesterdayTime[1], null, start, end);
        vo.setLately(lately.toString());

        // 30天留存
        start = nDayBeforeStartTime(new Date(yesterdayTime[0] * 1000L), 31);
        end = start + 24 * 60 * 60;
        Double cycleRatio = activeNewActorCount(yesterdayTime[0], yesterdayTime[1], null, start, end);
        vo.setCycleRatio(cycleRatio);
        return vo;
    }

    /**
     * 昨天活跃用户信息
     *
     * @param startTime 在线新用户的开始时间
     * @param endTime   在线新用户的结尾时间
     * @return 用户信息
     */
    private List<Dau> getActiveActor(Integer startTime, Integer endTime) {
        ApiResult<List<Dau>> result = dauDao.getActorOnline(startTime, endTime, null);
        if (result.isOK() && !CollectionUtils.isEmpty(result.getData())) {
            return result.getData();
        }
        return null;
    }

    /**
     * 获取昨天留用户数比
     *
     * @param list  在线新用户
     * @param os    1表示ios 0表示安卓 null表示全部
     * @param start 新增用户开始时间
     * @param end   新增用户结尾时间
     * @return 比率
     */
    private Double activeNewActorCount(List<Dau> list, Integer os, Integer start, Integer end) {
        int count = 0;
        for (Dau dau : list) {
            // 满足条件则是star到end时间内新增的用户
            if (dau.getUserId().compareTo(Long.toHexString(start)) > 0 &&
                    dau.getUserId().compareTo(Long.toHexString(end)) < 0) {
                if (null == os) {
                    count++;
                } else if (os == dau.getOs()) {
                    count++;
                }
            }
        }
        int newNum = newActorCount(start, end, os + "");
        BigDecimal bigDecimal = BigDecimal.ZERO;
        if (count > 0 && newNum > 0) {
            BigDecimal activeNum = new BigDecimal(count);
            BigDecimal preDate = new BigDecimal(newNum);
            bigDecimal = activeNum.divide(preDate, 4, BigDecimal.ROUND_DOWN);
            bigDecimal = bigDecimal.multiply(new BigDecimal(100));
        }
        return bigDecimal.doubleValue();
    }

    /**
     * 获取7和31日留用户数比
     *
     * @param startTime 在线新用户的开始时间
     * @param endTime   在线新用户的结尾时间
     * @param os        1表示ios 0表示安卓 null表示全部
     * @param start     新增用户开始时间
     * @param end       新增用户结尾时间
     * @return 比率
     */
    private Double activeNewActorCount(Integer startTime, Integer endTime, Integer os, Integer start, Integer end) {
        ApiResult<Set<String>> result = dauDao.getActiveActorByWhere(startTime, endTime, os, null, null, -1);
        logger.info("activeActorCount {}", result.getData().size());
        long count = 0L;
        if (result.isOK() && null != result.getData()) {
            for (String uid : result.getData()) {
                // 满足条件则是star到end时间内新增的用户
                if (uid.compareTo(Long.toHexString(start)) > 0 && uid.compareTo(Long.toHexString(end)) < 0) {
                    count++;
                }
            }
        }
        int newNum = newActorCount(start, end, os + "");
        BigDecimal bigDecimal = BigDecimal.ZERO;
        if (count > 0 && newNum > 0) {
            BigDecimal activeNum = new BigDecimal(count);
            BigDecimal preDate = new BigDecimal(newNum);
            bigDecimal = activeNum.divide(preDate, 4, BigDecimal.ROUND_DOWN);
            bigDecimal = bigDecimal.multiply(new BigDecimal(100));
        }
        return bigDecimal.doubleValue();
    }

    /**
     * 统计充值人数
     *
     * @return 统计信息
     */
    private DashboardVO totalChargePerson() {
        DashboardVO vo = new DashboardVO();

        // 昨天充值用户
        Integer[] yesterdayTime = yesterdayTime(null);
        Integer yesterday = chargePersonCount(yesterdayTime[0], yesterdayTime[1]);
        vo.setYesterday(yesterday.toString());

        // 同比
        Integer[] timeArr = preMonthToday(new Date(yesterdayTime[0] * 1000L));
        Integer preMonthYesterdayNum = chargePersonCount(timeArr[0], timeArr[1]);
        Double oyOyBasis = comparison(yesterday, preMonthYesterdayNum);
        vo.setOyOyBasis(oyOyBasis);

        // 最近7天的充值人数
        Integer before7dayStart = nDayBeforeStartTime(new Date(yesterdayTime[1] * 1000L), 7);
        Integer lately = chargePersonCount(before7dayStart, yesterdayTime[1]);
        vo.setLately(lately.toString());

        // 环比
        Integer before14dayStart = nDayBeforeStartTime(new Date(before7dayStart * 1000L), 7);
        Integer lastLastDay = chargePersonCount(before14dayStart, before7dayStart);
        Double cycleRatio = comparison(lately, lastLastDay);
        vo.setCycleRatio(cycleRatio);

        return vo;
    }

    /**
     * 充值人数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 人数
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Integer chargePersonCount(Integer startTime, Integer endTime) {
        QueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("atype", 1))
                .must(QueryBuilders.rangeQuery("mtime").gte(startTime).lt(endTime));
        SortBuilder sort = SortBuilders.fieldSort("mtime").order(SortOrder.DESC); // 排序
        ApiResult<Integer> result = elasticsearchDao.getChargePersonCount(query, sort);
        // 不成功 返回0
        if (!result.isOK() || null == result.getData()) return 0;
        logger.info("param start={} end={} chargePersonCount {}", startTime, endTime, result);
        return result.getData();
    }

    /**
     * 统计创建房间信息
     * TODO 转拿mysql库ustar_log库的s_create_room表
     *
     * @return 统计信息
     */
    private DashboardVO totalCreateRoom() {
        DashboardVO vo = new DashboardVO();

        // 昨天统计
        Integer[] yesterdayTime = yesterdayTime(null);
        Integer yesterday = createRoomCount(yesterdayTime[0], yesterdayTime[1], null);
        vo.setYesterday(yesterday.toString());

        Integer ios = createRoomCount(yesterdayTime[0], yesterdayTime[1], 1);
        vo.setIos(ios + "");
        Integer android = createRoomCount(yesterdayTime[0], yesterdayTime[1], 0);
        vo.setAndroid(android + "");

        // 同比计算
        Integer[] timeArr = preMonthToday(new Date(yesterdayTime[0] * 1000L));
        Integer preMonthYesterdayNum = createRoomCount(timeArr[0], timeArr[1], null);
        Double oyOyBasis = comparison(yesterday, preMonthYesterdayNum);
        vo.setOyOyBasis(oyOyBasis);

        // 最近7天创建房间人数
        Integer before7dayStart = nDayBeforeStartTime(new Date(yesterdayTime[1] * 1000L), 7);
        Integer lately = createRoomCount(before7dayStart, yesterdayTime[1], null);
        vo.setLately(lately.toString());

        // 环比计算
        Integer before14dayStart = nDayBeforeStartTime(new Date(before7dayStart * 1000L), 7);
        Integer lastLast7day = createRoomCount(before14dayStart, before7dayStart, null);
        Double cycleRatio = comparison(lately, lastLast7day);
        vo.setCycleRatio(cycleRatio);

        return vo;
    }

    /**
     * 新创建房间数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 数量
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Integer createRoomCount(Integer startTime, Integer endTime, Integer os) {
        ApiResult<Integer> result = roomDao.getCreateRoomCount(startTime, endTime, os);
        logger.info("createRoomCount {}", result);
        if (result.isOK() && null != result.getData()) return result.getData();
        return 0;
    }

    /**
     * 统计会话数据
     *
     * @return 统计信息
     */
    private DashboardVO totalChatPerson() {
        DashboardVO vo = new DashboardVO();

        // 昨天会话人数
        Integer[] yesterdayTime = yesterdayTime(null);
        Integer yesterday = chatPersonCount(yesterdayTime[0], yesterdayTime[1], null);
        vo.setYesterday(yesterday.toString());

        // 昨天ios会话人数
        Integer ios = chatPersonCount(yesterdayTime[0], yesterdayTime[1], 1);
        vo.setIos(ios.toString());

        // 昨天Android会话人数
        Integer android = chatPersonCount(yesterdayTime[0], yesterdayTime[1], 0);
        vo.setAndroid(android.toString());

        // 同比
        Integer[] timeArr = preMonthToday(new Date(yesterdayTime[0] * 1000L));
        Integer preMonthYesterdayNum = chatPersonCount(timeArr[0], timeArr[1], null);
        Double oyOyBasis = comparison(yesterday, preMonthYesterdayNum);
        vo.setOyOyBasis(oyOyBasis);

        // 最近7天的会话人数
        Integer before7dayStart = nDayBeforeStartTime(new Date(yesterdayTime[1] * 1000L), 7);
        Integer lately = chatPersonCount(before7dayStart, yesterdayTime[1], null);
        vo.setLately(lately.toString());

        // 环比
        Integer before14dayStart = nDayBeforeStartTime(new Date(before7dayStart * 1000L), 7);
        Integer lastLast7day = chatPersonCount(before14dayStart, before7dayStart, null);
        Double cycleRatio = comparison(lately, lastLast7day);
        vo.setCycleRatio(cycleRatio);

        return vo;
    }

    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Integer chatPersonCount(Integer startTime, Integer endTime, Integer fromOs) {
        ApiResult<Integer> result = roomMsgDao.totalChatPerson(startTime, endTime, fromOs);
        if (!result.isOK() || null == result.getData()) {
            return 0;
        }
        return result.getData();
    }

    /**
     * 统计发送礼物人数
     *
     * @return 统计信息
     */
    private DashboardVO totalSendGift() {
        DashboardVO vo = new DashboardVO();

        // 昨天发送礼物
        Integer[] yesterdayTime = yesterdayTime(null);
        Integer yesterday = sendGiftCount(yesterdayTime[0], yesterdayTime[1], null);
        vo.setYesterday(yesterday.toString());

        // 昨天全部ios发送礼物
        Integer ios = sendGiftCount(yesterdayTime[0], yesterdayTime[1], 1);
        vo.setIos(ios.toString());

        // 获取昨天全部Android发送礼物
        Integer android = sendGiftCount(yesterdayTime[0], yesterdayTime[1], 0);
        vo.setAndroid(android.toString());

        // 同比
        Integer[] timeArr = preMonthToday(new Date(yesterdayTime[0] * 1000L));
        Integer preMonthYesterdayNum = sendGiftCount(timeArr[0], timeArr[1], null);
        Double oyOyBasis = comparison(yesterday, preMonthYesterdayNum);
        vo.setOyOyBasis(oyOyBasis);

        // 最近7天的发送礼物人数
        Integer before7dayStart = nDayBeforeStartTime(new Date(yesterdayTime[1] * 1000L), 7);
        Integer lately = sendGiftCount(before7dayStart, yesterdayTime[1], null);
        vo.setLately(lately.toString());

        // 环比
        Integer before14dayStart = nDayBeforeStartTime(new Date(before7dayStart * 1000L), 7);
        Integer lastLast7day = sendGiftCount(before14dayStart, before7dayStart, null);
        Double cycleRatio = comparison(lately, lastLast7day);
        vo.setCycleRatio(cycleRatio);
        return vo;
    }

    /**
     * 获取时间内发送礼物人数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 统计信息
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Integer sendGiftCount(Integer startTime, Integer endTime, Integer os) {
        ApiResult<Integer> result = giftRecordMgDao.totalSendGiftPerson(startTime, endTime, os);
        logger.info("totalSendGiftPerson : {}", result.getData());
        if (result.isOK() && null != result.getData()) return result.getData();
        return 0;
    }

    /**
     * 统计在房平均时长
     *
     * @return 统计信息
     */
    private DashboardVO totalRoomInTime() {
        DashboardVO vo = new DashboardVO();

        // 昨天平均在房秒数
        Integer yesterdayAvg = 0;
        // 前七天数据
        Integer latelyAvg = 0;
        // 最近7天日期
        String[] arr = recentlyNDay(null, 7);
        // 昨天日期
        String yesterday = arr[arr.length - 1];

        // 昨天
        ApiResult<TotalVO> result = roomTimeDao.totalInRoomTime(yesterday);
        if (result.isOK()) {
            TotalVO v = result.getData();
            Integer sumNum = v.getSumNum().intValue();
            Integer countNum = v.getCountNum().intValue();
            yesterdayAvg = countNum < 1 ? 0 : sumNum / countNum;
            vo.setYesterday(calculateTime(sumNum, countNum));
        }

        // 同比计算
        Date date = DateHelper.ARABIAN.stringToDate(yesterday);
        date = DateHelper.ARABIAN.dateAddMonth(date, -1);
        String previousMonthYesterday = DateHelper.ARABIAN.dateToStr(date);
        // 上个月昨天的数据
        result = roomTimeDao.totalInRoomTime(previousMonthYesterday);
        if (result.isOK()) {
            TotalVO v = result.getData();
            Integer sumNum = v.getSumNum().intValue();
            Integer countNum = v.getCountNum().intValue();
            if (sumNum > 0 && countNum > 0) {
                Double oyOyBasis = comparison(yesterdayAvg, sumNum / countNum);
                vo.setOyOyBasis(oyOyBasis);
            }
        }

        // 最近七天
        result = roomTimeDao.totalInRoomTime(Arrays.asList(arr));
        if (result.isOK()) {
            TotalVO v = result.getData();
            Integer sumNum = v.getSumNum().intValue();
            Integer countNum = v.getCountNum().intValue();
            if (sumNum > 0 && countNum > 0) {
                latelyAvg = sumNum / countNum;
                vo.setLately(calculateTime(sumNum, countNum));
            }
        }

        // 环比计算
        Date cycleEndDate = DateHelper.ARABIAN.stringToDate(arr[0]);
        String[] cycleArr = recentlyNDay(cycleEndDate, 7);
        // 7天前的前七天
        result = roomTimeDao.totalInRoomTime(Arrays.asList(cycleArr));
        if (result.isOK()) {
            TotalVO v = result.getData();
            Integer latelyLateLyAvg = 0;
            try {
                latelyLateLyAvg = v.getSumNum().intValue() / v.getCountNum().intValue();
            } catch (Exception e) {
                logger.error("{}, {}", e.getMessage(), e);
            }
            Double cycleRatio = comparison(latelyAvg, latelyLateLyAvg);
            vo.setCycleRatio(cycleRatio);

        }
        return vo;
    }

    /**
     * 将秒数转化为时分秒
     *
     * @param sumNum   总时长
     * @param countNum 人数
     * @return 时间
     */
    private String calculateTime(Integer sumNum, Integer countNum) {
        if (sumNum == 0 || countNum == 0) {
            return "0";
        }
        try {
            // 平均时长秒
            Integer avgSecond = sumNum / countNum;
//            if (avgSecond >= 60) {
//                Integer avgMinute = avgSecond/60;
//                if (avgMinute >= 60) {
//                    Integer avgHour = avgMinute/60;
//                    return avgHour + ":" + avgMinute%60 + ":" + avgSecond%60;
//                }
//                return avgMinute + ":" + avgSecond%60;
//            }
//            return avgSecond.toString();

            return DateHelper.ARABIAN.intToTimeString(avgSecond);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return "0";
    }

    /**
     * 获取前几天的
     *
     * @param several 几天前，传入正数
     * @return "2020-07-02"
     */
    private String nDayAgo(int several) {
        DateHelper dateHelper = DateHelper.ARABIAN;
        Integer endTime = dateHelper.currentTimeZoneSeconds();
        Date date = dateHelper.dateAddDay(new Date(endTime * 1000L), -several);
        return dateHelper.dateToStr(date);
    }

    /**
     * 获取往前推n天日期数组
     * 传了时间则以穿入的时间往以前推
     *
     * @param date 开推时间
     * @param n    n
     * @return 数组
     */
    private String[] recentlyNDay(Date date, int n) {
        DateHelper dateHelper = DateHelper.ARABIAN;
        if (null == date) date = new Date();

        Long endTime = date.getTime() / 1000L;
        // 减一是为了从昨天开始
        endTime = dateHelper.dateAddDay(new Date(endTime * 1000L), -1).getTime() / 1000;

        // --n 是因为会包含两边
        date = dateHelper.dateAddDay(new Date(endTime * 1000L), -(--n));
        Long startTime = date.getTime() / 1000;
        String[] dateDiffArray = dateHelper.getDateDiffArray(startTime.intValue(), endTime.intValue());
        return dateDiffArray;
    }

    /**
     * 获取n天前开始的时间戳
     *
     * @param date 哪天开始计算
     * @param n    倒推第n天
     * @return 时间戳， 秒
     */
    private Integer nDayBeforeStartTime(Date date, int n) {
        Long startTime = DateHelper.ARABIAN.dateAddDay(date, -n).getTime() / 1000L;
        return startTime.intValue();
    }

    /**
     * 获取昨天的开始时间和结尾时间
     *
     * @return
     */
    private Integer[] yesterdayTime(Date date) {
        if (null == date) date = new Date();
        DateHelper dateHelper = DateHelper.ARABIAN;
        // 得到当前最开始时间戳作为过滤的结尾时间 单位：秒
        Long endTime = dateHelper.setStartTime(date) / 1000L;
        // endTime - 1 是为了获取昨天的时间戳，再获取昨天开始时间戳 单位：秒
        Long startTime = dateHelper.setStartTime(new Date((endTime - 1L) * 1000)) / 1000L;
        return new Integer[]{startTime.intValue(), endTime.intValue()};
    }

    /**
     * 获取上个月今天的时间戳数组
     *
     * @param date 指定时间
     * @return 数组
     */
    private static Integer[] preMonthToday(Date date) {
        date = DateHelper.ARABIAN.dateAddMonth(date, -1);
        Long preMonthStart = date.getTime() / 1000;
        // 计算得到preMonthStart明天零点零分零秒的时间
        Long preMonthEnd = preMonthStart + 60 * 60 * 24;
        Integer[] timeArr = new Integer[]{preMonthStart.intValue(), preMonthEnd.intValue()};
        return timeArr;
    }

    /**
     * 计算两个数的环比，同比
     *
     * @param a 当期数据
     * @param b 上期数据
     * @return 比例%
     */
    private Double comparison(Integer a, Integer b) {
        if (a == 0 || b == 0) {
            return 0.00;
        }
        BigDecimal diff = new BigDecimal(a - b);
        BigDecimal preDate = new BigDecimal(b);
        BigDecimal bigDecimal = diff.divide(preDate, 4, BigDecimal.ROUND_DOWN);
        bigDecimal = bigDecimal.multiply(new BigDecimal(100));
        return bigDecimal.doubleValue();
    }

}
