package com.quhong.operation.server;

import com.quhong.constant.FcmMsgTypeConstant;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FcmPushConfigDataDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.FcmPushConfigData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.operation.share.condition.ResourceKeyCondition;
import com.quhong.operation.share.vo.FcmPushConfigDataVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class FcmPushService  extends SlowTaskQueue {
    private static final Logger logger = LoggerFactory.getLogger(FcmPushService.class);

    @Resource
    private FcmPushConfigDataDao fcmPushConfigDataDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;


    public PageResultVO<FcmPushConfigDataVO> fcmPushConfigList(ResourceKeyCondition condition) {
        PageResultVO<FcmPushConfigDataVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        int status = condition.getStatus();
        List<FcmPushConfigData> configList = fcmPushConfigDataDao.selectPage(status, start, pageSize);
        List<FcmPushConfigDataVO> voList = new ArrayList<>();
        for (FcmPushConfigData data : configList) {
            FcmPushConfigDataVO vo = new FcmPushConfigDataVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(fcmPushConfigDataDao.selectCount(status));
        return pageVO;
    }

    public void addFcmPushConfig(FcmPushConfigDataVO dto) {
        fcmPushConfigParamCheck(dto);
        FcmPushConfigData data = new FcmPushConfigData();
        BeanUtils.copyProperties(dto, data);
        int now = DateHelper.getNowSeconds();
        data.setPushDateList(new ArrayList<>());
        data.setCtime(now);
        data.setMtime(now);
        fcmPushConfigDataDao.insert(data);
    }

    public void updateFcmPushConfig(FcmPushConfigDataVO dto) {
        if (StringUtils.isEmpty(dto.getDocId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "docId 不能为空");
        }
        FcmPushConfigData configData = fcmPushConfigDataDao.getDataByID(dto.getDocId());
        if (configData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "当前配置不存在");
        }
        fcmPushConfigParamCheck(dto);

        Update update = new Update();
        update.set("userType", dto.getUserType());
        if (dto.getUserType() == 11){
            String[] rids = dto.getRidList().trim().replace("，", ",").split(",");
            StringBuilder errorMsgSb = new StringBuilder();
            List<String> strRidList = new ArrayList<>();
            for (String strRid : rids) {
                try {
                    ActorData actorData = actorDao.getActorByStrRid(strRid);
                    if (null == actorData) {
                        errorMsgSb.append("rid:").append(strRid).append("找不到对应的用户 \n");
                        continue;
                    }
                    strRidList.add(strRid);
                } catch (Exception e) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "账号输入有误");
                }
            }

            String errorMsg = errorMsgSb.toString();
            if (StringUtils.hasLength(errorMsg)) {
                throw new CommonH5Exception(new HttpCode(1, errorMsg));
            }
            update.set("ridList", String.join(",", strRidList));
        }

        update.set("pushStartDate", dto.getPushStartDate());
        update.set("pushEndDate", dto.getPushEndDate());
        update.set("pushHMS", dto.getPushHMS());
        update.set("actionType", dto.getActionType());
        update.set("actionValue", dto.getActionValue());
        update.set("title", dto.getTitle());
        update.set("content", dto.getContent());
        update.set("imgUrl", dto.getImgUrl());
        update.set("notes", dto.getNotes());
        update.set("status", dto.getStatus());
        update.set("mtime",DateHelper.getNowSeconds());
        fcmPushConfigDataDao.updateData(dto.getDocId(), update);
    }

    public void removeFcmPushConfig(FcmPushConfigDataVO dto) {
        if (StringUtils.isEmpty(dto.getDocId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "docId 不能为空");
        }
        FcmPushConfigData configData = fcmPushConfigDataDao.getDataByID(dto.getDocId());
        if (configData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "当前配置不存在");
        }
        fcmPushConfigDataDao.removeByDocId(dto.getDocId());
    }

    private void fcmPushConfigParamCheck(FcmPushConfigDataVO dto) {
        if (dto.getUserType() <= 0 || StringUtils.isEmpty(dto.getPushStartDate()) || StringUtils.isEmpty(dto.getPushEndDate())
                || dto.getActionType() <= 0 || StringUtils.isEmpty(dto.getTitle())
                || StringUtils.isEmpty(dto.getContent())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数必须大于0,或者title，content不能为空");
        }
        if (dto.getUserType() == 11 && StringUtils.isEmpty(dto.getRidList())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "指定批量RID，ridList不能为空");
        }
        if (dto.getActionType() >= 8 && dto.getActionType() <= 15 && dto.getActionType() != 13
                && StringUtils.isEmpty(dto.getActionValue())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "该跳转类型，必须有actionValue");
        }

        if (dto.getUserType() == 11){
            String[] rids = dto.getRidList().trim().replace("，", ",").split(",");
            StringBuilder errorMsgSb = new StringBuilder();
            List<String> strRidList = new ArrayList<>();
            for (String strRid : rids) {
                try {
                    ActorData actorData = actorDao.getActorByStrRid(strRid);
                    if (null == actorData) {
                        errorMsgSb.append("rid:").append(strRid).append("找不到对应的用户 \n");
                        continue;
                    }
                    strRidList.add(strRid);
                } catch (Exception e) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "账号输入有误");
                }
            }

            String errorMsg = errorMsgSb.toString();
            if (StringUtils.hasLength(errorMsg)) {
                throw new CommonH5Exception(new HttpCode(1, errorMsg));
            }
            dto.setRidList(String.join(",", strRidList));
        }

        // 跳转房间校验
        String actionValue = dto.getActionValue();
        String actionTypeStr = String.valueOf(dto.getActionType());
        if (String.valueOf(dto.getActionType()).equals(FcmMsgTypeConstant.VOICE_ROOM)){
            if(ObjectUtils.isEmpty(actionValue)){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "跳转房间值输入有误");
            }
            ActorData actorData = actorDao.getActorByStrRid(actionValue);
            if (actorData == null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "跳转房间值输入有误");
            }
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(RoomUtils.formatRoomId(actorData.getUid()));
            if(roomData == null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "跳转房间值输入有误");
            }
        }

        if (actionTypeStr.equals(FcmMsgTypeConstant.USER_DETAIL_MSG) || actionTypeStr.equals(FcmMsgTypeConstant.PRIVATE_DETAIL_MSG)){
            if(ObjectUtils.isEmpty(actionValue)){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "跳转用户id输入有误");
            }
            ActorData actorData = actorDao.getActorByStrRid(actionValue);
            if (actorData == null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "跳转用户id输入有误");
            }
        }

        if (actionTypeStr.equals(FcmMsgTypeConstant.WEB_URL)){
            if(ObjectUtils.isEmpty(actionValue) || !actionValue.contains("https://")){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "跳转url输入有误");
            }
        }
    }
}
