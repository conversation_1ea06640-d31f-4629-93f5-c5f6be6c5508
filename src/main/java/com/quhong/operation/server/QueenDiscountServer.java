package com.quhong.operation.server;


import com.quhong.data.ActorData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.QueenDiscountDao;
import com.quhong.mysql.data.QueenDiscountData;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.StartPageDao;
import com.quhong.operation.share.dto.StartPageDto;
import com.quhong.operation.share.mongobean.StartPage;
import com.quhong.operation.share.vo.QueenDiscountVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Component
public class QueenDiscountServer {
    private static final Logger logger = LoggerFactory.getLogger(QueenDiscountServer.class);

    @Autowired
    private QueenDiscountDao queenDiscountDao;

    @Autowired
    private ActorDao actorDao;

    @Autowired
    private MongoRoomDao mongoRoomDao;




    public List<QueenDiscountVO> listQueenDiscount(int page, int pageSize, int search){
        String searchUid = null;
        if(search != -1){
            ActorData actorData = actorDao.getActorByRid(search);
            if(actorData != null){
                searchUid = actorData.getUid();
            }
        }

        List<QueenDiscountData> queenDiscountDataList = queenDiscountDao.selectPageByUid(searchUid, page, pageSize);
        List<QueenDiscountVO> discountList = new ArrayList<>();
        for(QueenDiscountData discountData:queenDiscountDataList){
            QueenDiscountVO queenDiscountVO = new QueenDiscountVO();
            BeanUtils.copyProperties(discountData, queenDiscountVO);
            queenDiscountVO.setRecordId(discountData.getId());

            ActorData actorData = actorDao.getActorDataFromCache(discountData.getUid());
            queenDiscountVO.setRid(actorData.getRid());

            if(!StringUtils.isEmpty(discountData.getToUid())){
                ActorData toActorData = actorDao.getActorDataFromCache(discountData.getToUid());
                queenDiscountVO.setToRid(toActorData.getRid());
            }

            discountList.add(queenDiscountVO);
        }
        return discountList;
    }


    public long countQueenDiscount(int search){
        String searchUid = null;
        if(search != -1){
            ActorData actorData = actorDao.getActorByRid(search);
            if(actorData != null){
                searchUid = actorData.getUid();
            }
        }
        return queenDiscountDao.countQueenDiscount(searchUid);
    }


    public void updateQueenDiscount(QueenDiscountData data) {
        queenDiscountDao.updateOne(data);
    }

}
