package com.quhong.operation.server;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.enums.ServerType;
import com.quhong.mongo.config.OpMongoBean;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.ActorPayExternalData;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.country.CountryQuery;
import com.quhong.operation.dao.AdminUserDao;
import com.quhong.operation.dao.BanReasonStatDao;
import com.quhong.operation.dao.ElasticsearchDao;
import com.quhong.operation.dao.OperationActorDao;
import com.quhong.operation.share.data.CountryData;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.LastLogin;
import com.quhong.operation.share.mongobean.UserLevel;
import com.quhong.operation.share.mongobean.UserMonitorLog;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.BlockUserVO;
import com.quhong.operation.utils.Country;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.StringUtil;
import org.bson.types.ObjectId;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Service
public class BlockedServer {

    private final static Logger logger = LoggerFactory.getLogger(BlockedServer.class);

    @Autowired
    private UserMonitorServer userMonitorServer;
    @Autowired
    private OperationActorDao operationActorDao;
    @Resource
    private ActorDao actorDao;
    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private AdminUserDao adminUserDao;
    @Autowired
    private ElasticsearchDao elasticsearchDao;
    @Resource(name = OpMongoBean.MOVIES)
    private MongoTemplate mongoTemp;
    @Autowired
    private BanReasonStatDao banReasonStatDao;
    @Autowired
    private CountryQuery countryQuery;
    @Autowired
    private ActorPayExternalDao actorPayExternalDao;

    /**
     * 获取封禁列表
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param rid       rid (可以为null)
     * @return 封禁列表
     */
    public ApiResult<List<BlockUserVO>> blockedList(Integer startTime, Integer endTime, Integer rid) {
        ApiResult<List<BlockUserVO>> result = new ApiResult<>();
        Actor actor = null;
        String userId = null;
        if (null != rid) {
            ApiResult<Actor> actorResult = getActorInfoByRidOrUid(rid, null);
            if (!actorResult.isOK()) {
                return result.error(actorResult.getMsg());
            }
            actor = actorResult.getData();
            userId = actor.get_id().toString();
        }

        ApiResult<List<UserMonitorLog>> apiResult = userMonitorServer.getUserMonitorLogList(startTime, endTime, userId);
        if (!apiResult.isOK()) {
            return result.error(apiResult.getMsg());
        }

        List<BlockUserVO> list = new ArrayList<>();
        Map<String, String> optUserMap = new HashMap<>();
        // 业务填充用户信息，充值信息
        for (UserMonitorLog um : apiResult.getData()) {
            if (null == rid) {
                String aid = um.getUid();
                ApiResult<Actor> actorResult = getActorInfoByRidOrUid(null, aid);
                if (!actorResult.isOK()) {
                    return result.error(actorResult.getMsg());
                }
                actor = actorResult.getData();
            }
            BlockUserVO vo = new BlockUserVO();
            fillActorInfo(vo, actor);
            newFillChargeInfo(vo, actor.get_id().toString());
            fillBlockInfo(vo, um, optUserMap);
            if (actor.getValid() == 0 && !(vo.getOperation().equals("账号封禁"))) {
                vo.setOperation(vo.getOperation() + " 系统封禁");
                vo.setCurrentStatus(vo.getOperation() + " 系统封禁");
            }
            if (um.getCode() == 3 || um.getCode() == 2 || um.getCode() == 4) {
                if ("1".equals(um.getBlock_term())) {
                    vo.setBlockTime("24小时");
                } else if ("2".equals(um.getBlock_term())) {
                    vo.setBlockTime("7天");
                } else if ("4".equals(um.getBlock_term())) {
                    vo.setBlockTime("7天");
                } else if ("5".equals(um.getBlock_term())) {
                    vo.setBlockTime("30天");
                } else {
                    vo.setBlockTime("永久");
                }
            }
            list.add(vo);
        }
        //set 设备封禁用户信息
//        List<BanReasonData> banReasonDataList = banReasonStatDao.listBanByMtime(startTime, endTime, userId);
//        for (BanReasonData ban : banReasonDataList) {
//            if (null == rid) {
//                String aid = ban.getUid();
//                ApiResult<Actor> actorResult = getActorInfoByRidOrUid(null, aid);
//                if (!actorResult.isOK()) {
//                    return result.error(actorResult.getMsg());
//                }
//                actor = actorResult.getData();
//            }
//            BlockUserVO vo = new BlockUserVO();
//            fillActorInfo(vo, actor);
//            fillChargeInfo(vo, actor.get_id().toString());
//            //set 封禁数据
//            vo.setOptDate(DateHelper.ARABIAN.datetimeToStr(new Date(ban.getMtime() * 1000L)));
//            vo.setOperation("封禁");
//            vo.setBlockReason(ban.getReason()); // 被封原因
//            String opUid = ban.getOp_uid();
//            if (!StringUtils.isEmpty(opUid)) {
//                ApiResult<String> adminUserResult = adminUserDao.getAdminUserNameByUid(opUid);
//                if (adminUserResult.isOK() && null != adminUserResult.getData()) {
//                    vo.setOptUser(adminUserResult.getData());
//                }
//            }
//            //set 强制封禁状态
//            if (actor.getValid() == 0 && !(vo.getOperation().equals("封禁"))) {
//                vo.setOperation(vo.getOperation() + " 系统封禁");
//                vo.setCurrentStatus(vo.getOperation() + " 系统封禁");
//            }
//            vo.setBlockTime("永久");
//            list.add(vo);
//        }

        return result.ok(list);
    }

    /**
     * 通过用户id获取用户监控记录
     *
     * @param uid 用户userId
     * @return 监控记录集合
     */
    public ApiResult<List<UserMonitorLog>> getUserMonitorLog(String uid) {
        ApiResult<List<UserMonitorLog>> result = new ApiResult<>();

        ApiResult<List<UserMonitorLog>> logResult = userMonitorServer.getMonitorLogByUid(uid);
        if (!logResult.isOK()) {
            logger.error("uid={} get block log fail : {}", uid, logResult.getMsg());
            return result.error(logResult.getMsg());
        }

        return result.ok(logResult.getData());
    }

    /**
     * 填充充值信息
     *
     * @param vo  接收充值信息对象
     * @param uid 用户userId
     */
    private void fillChargeInfo(BlockUserVO vo, String uid) {
        TermsAggregationBuilder agg = AggregationBuilders.terms("group").field("title.keyword").size(9999); // size是查询聚合出来的条数
        agg = agg.subAggregation(AggregationBuilders.sum("sumBean").field("changed"));
        QueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("uid", uid))
                .must(QueryBuilders.termQuery("atype", 1));
        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(queryBuilder)
                .addAggregation(agg)
                .build();
        ApiResult<TotalVO> result = elasticsearchDao.getChargeChannelAndTotalBeans(searchQuery);
        logger.info("getChargeChannelAndTotalBeans result:{}", result);
        if (result.isOK()) {
            vo.setChargeChannel(result.getData().getChannel());
            vo.setTotalChargeBean(result.getData().getSumNum().longValue());
        }
    }

    /**
     * 填充充值信息
     *
     * @param vo  接收充值信息对象
     * @param uid 用户userId
     */
    private void newFillChargeInfo(BlockUserVO vo, String uid) {
        ActorPayExternalData actorPayExternalData = actorPayExternalDao.selectOne(uid);
        if (actorPayExternalData != null) {
            switch (actorPayExternalData.getRechargeChannel()) {
                case 1:
                    vo.setChargeChannel("GP 支付");
                    break;
                case 2:
                    vo.setChargeChannel("Apple 支付");
                    break;
                case 3:
                    vo.setChargeChannel("Tap 支付");
                    break;
                case 4:
                    vo.setChargeChannel("Admin 支付");
                    break;
                case 5:
                    vo.setChargeChannel("JollychicCharge");
                    break;
                case 6:
                    vo.setChargeChannel("huawei_pay");
                    break;
                case 7:
                    vo.setChargeChannel("OPayCharge");
                    break;
                case 8:
                    vo.setChargeChannel("中台支付");
                    break;
                default:
                    vo.setChargeChannel("");
            }
            vo.setTotalChargeMoney(actorPayExternalData.getRechargeMoney() != null ? actorPayExternalData.getRechargeMoney() : new BigDecimal("0"));
        } else {
            vo.setChargeChannel("");
            vo.setTotalChargeMoney(new BigDecimal("0"));
        }
    }


    /**
     * 填充封禁信息
     *
     * @param vo  待填充对象
     * @param um  赋值对象
     * @param map 操作人信息
     */
    private void fillBlockInfo(BlockUserVO vo, UserMonitorLog um, Map<String, String> map) {
        vo.setOptDate(DateHelper.ARABIAN.datetimeToStr(new Date(um.getOptTime() * 1000L)));
        Integer code = um.getCode();
        switch (code) {
            case 1:
                vo.setOperation("警告");
                break;
            case 2:
                vo.setOperation("冻结");
                break;
            case 3:
                vo.setOperation("账号封禁");
                break;
            case 4:
                vo.setOperation("设备封禁");
                break;
            default:
                vo.setOperation("");
                break;
        }
        vo.setBlockReason(um.getReason()); // 被封原因
        String operator = um.getOperator();
        if (map.containsKey(operator)) {
            vo.setOptUser(map.get(operator));
        } else {
            ApiResult<String> result = adminUserDao.getAdminUserNameByUid(operator);
            if (result.isOK() && null != result.getData()) {
                vo.setOptUser(result.getData());
                map.put(operator, result.getData());
            }
        }

    }

    /**
     * 填充用户信息
     *
     * @param vo    待填充对象
     * @param actor 赋值对象
     */
    private void fillActorInfo(BlockUserVO vo, Actor actor) {
        String uid = actor.get_id().toString();
        vo.setRid(actor.getRid());
        vo.setUid(uid);
        vo.setName(actor.getName());
        vo.setGender("1".equals(actor.getFbGender()) ? "男" : "2".equals(actor.getFbGender()) ? "女" : "");
        //通过ip查找国家
        String ip = actor.getIp();
        if (!StringUtils.isEmpty(ip)) {
            CountryData countryData = countryQuery.find(ip);
            if (countryData != null) {
                vo.setCountry(Country.getCountryName(countryData.getCode()));
            }
        } else {
            vo.setCountry(Country.getCountryName(actor.getCountry())); // 获取国家中文名称
        }
        vo.setTerrace("1".equals(actor.getOs()) ? "IOS" : "Android");
        vo.setUlvl(actor.getUlvl());
        vo.setVipLevel(actor.getVlvl());
        vo.setRegisterDate(getActorRegisterDate(actor.get_id()));
        vo.setLastLogin(getActorLastLoginDate(actor.getLastLogin()));

        ApiResult<String> result = userMonitorServer.getActorNowStatus(uid);
        if (!result.isOK()) {
            logger.error("uid={} get actor block status error, error info {}", uid, result.getMsg());
        } else {
            vo.setCurrentStatus(result.getData());
        }

    }

    /**
     * 获取用户注册时间
     *
     * @param objectId actor的_id
     * @return 注册时间
     */
    private String getActorRegisterDate(ObjectId objectId) {
        return DateHelper.ARABIAN.datetimeToStr(new Date(objectId.getTimestamp() * 1000L));
    }

    /**
     * 获取用户最近登录时间
     *
     * @param lastLogin 登录信息
     * @return 时间
     */
    private String getActorLastLoginDate(LastLogin lastLogin) {
        if (null == lastLogin) {
            return "";
        }

        Long loginTime = lastLogin.getLoginTime();
        if (null == loginTime) {
            return "";
        }

        return DateHelper.ARABIAN.datetimeToStr(new Date(loginTime * 1000L));
    }

    /**
     * 获取到Actor用户信息
     *
     * @param uid uid
     * @param map Actor map
     * @return Actor
     */
    private Actor getBlockActorInfo(String uid, Map<String, Actor> map) {
        Actor actor = new Actor();
        if (map.containsKey(uid)) {
            actor = map.get(uid);
        } else {
            ApiResult<Actor> result = getActorInfoByRidOrUid(null, uid);
            if (result.isOK() && null != result.getData()) {
                actor = result.getData();
                map.put(uid, result.getData());
            }
        }
        return actor;
    }

    /**
     * 通过rid或者uid 获取用户信息，包含ulvl和vlvl
     *
     * @param rid rid
     * @param uid userId
     * @return actor
     */
    public ApiResult<Actor> getActorInfoByRidOrUid(Integer rid, String uid) {
        if (null == rid && StringUtil.isEmptyOrBlank(uid)) {
            return new ApiResult<Actor>().error("param rid and uid are empty");
        }
        ApiResult<Actor> result;
        boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
        if (test) {
            result = new ApiResult<>();
            if (!StringUtil.isEmptyOrBlank(uid)) {
                MongoActorData actorData = actorDao.findActorDataFromDB(uid);
                if (actorData != null) {
                    Actor actor = new Actor();
                    BeanUtils.copyProperties(actorData, actor);
                    result.ok(actor);
                } else {
                    result.error(null);
                }
            } else {
                ActorData actorByRid = actorDao.getActorByRid(rid);
                if (actorByRid != null) {
                    Actor actor = new Actor();
                    BeanUtils.copyProperties(actorByRid, actor);
                    result.ok(actor);
                } else {
                    result.error(null);
                }
            }
        } else {
            if (!StringUtil.isEmptyOrBlank(uid)) {
                result = operationActorDao.getActorByUid(uid);
            } else {
                result = operationActorDao.getActorByRid(rid);
            }
        }
        if (!result.isOK()) {
            return result.error(result.getMsg());
        }
        if (null == result.getData()) {
            return result.error("rid=" + rid + " not exist!");
        }
        Actor actor = result.getData();
        // 获取用户等级和vip等级
        getActorUlvlAndVlvl(actor);
        return result.ok(actor);
    }

    /**
     * 获取用户的等级和vip的等级
     *
     * @param actor 用户信息
     */
    private void getActorUlvlAndVlvl(Actor actor) {
        // 获取用户vip等级
        String userId = actor.get_id().toString();
        Integer vipLevel = vipInfoDao.getVipLevel(userId);
        actor.setVlvl(vipLevel);
        // 获取用户等级
        Query query = new Query(Criteria.where("uid").is(userId));
        UserLevel uLevel = mongoTemp.findOne(query, UserLevel.class);
        Integer level = null == uLevel || 1 > uLevel.getLevel() ? -1 : uLevel.getLevel();
        actor.setUlvl(level);
    }


}
