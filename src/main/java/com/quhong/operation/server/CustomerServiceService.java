package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MsgRecordDTO;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.MsgRecordListVO;
import com.quhong.feign.IMsgService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.customerService.ActorVOCache;
import com.quhong.operation.share.dto.CustomerServiceMsgListDTO;
import com.quhong.operation.share.dto.cutomerService.ServiceInfoDTO;
import com.quhong.operation.share.vo.customerService.ActorVO;
import com.quhong.operation.share.vo.customerService.CustomerServiceVO;
import com.quhong.operation.share.vo.customerService.RechargeActorListVO;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.user.CustomerServiceUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 客服系统
 */
@Service
public class CustomerServiceService {
    private static final Logger logger = LoggerFactory.getLogger(CustomerServiceService.class);

    private static final int LATEST_RECHARGE_SIZE = 50;

    @Autowired
    private ActorVOCache actorVOCache;
    @Autowired
    private RechargeRedis rechargeRedis;
    @Autowired
    private IMsgService msgService;
    @Autowired
    private ActorDao actorDao;

    /**
     * 根据rid或者名称获取actor信息
     *
     * @param dto
     * @return
     */
    public HttpResult<CustomerServiceVO> getServiceInfo(ServiceInfoDTO dto) {
        ActorData actorData = null;
        String uid = null;
        if (dto.getRid() != null) {
            actorData = actorDao.getActorByStrRid(String.valueOf(dto.getRid()));
            if (actorData != null) {
                uid = actorData.getUid();
            }
        } else if (StringUtils.hasLength(dto.getName())) {
            actorData = actorDao.getActorByStrRid(dto.getName());
            if (actorData != null) {
                uid = actorData.getUid();
            }
        } else {
            uid = CustomerServiceUser.getUid();
        }
        if (uid == null) {
            // 返回空对象
            logger.error("can not find actorData. requestId={}", dto.getRequestId());
            CustomerServiceVO vo = new CustomerServiceVO();
            return new HttpResult<CustomerServiceVO>().ok(vo);
        }
        logger.info("get actor info. uid={} requestId={}", uid, dto.getRequestId());
        ActorVO actorVO = actorVOCache.getActorVO(uid);
        CustomerServiceVO vo = new CustomerServiceVO();
        vo.setActor(actorVO);
        return new HttpResult<CustomerServiceVO>().ok(vo);
    }

    public HttpResult<RechargeActorListVO> getUserList(CustomerServiceMsgListDTO dto) {
        if (dto.getType() == null || dto.getType() == 0) {
            return getLatestRechargeList();
        } else if (dto.getType() == 2) {
            return getGroupChatUserList(dto.getUserIdList());
        } else {
            return getLatestChatList();
        }
    }

    /**
     * 获取最近充值列表
     *
     * @return
     */
    private HttpResult<RechargeActorListVO> getLatestRechargeList() {
        Set<ZSetOperations.TypedTuple<String>> set = rechargeRedis.getLatestOnlineUser(LATEST_RECHARGE_SIZE);
        List<ActorVO> actorVOList = new ArrayList<>();
        for (ZSetOperations.TypedTuple<String> typedTuple : set) {
            ActorVO actorVO = actorVOCache.getActorVO(typedTuple.getValue());
            if (actorVO == null) {
                continue;
            }
            actorVO.setLastUpdateTime(typedTuple.getScore().longValue());
            actorVOList.add(actorVO);
        }
        actorVOList.sort((o1, o2) -> (int) (o2.getLastUpdateTime() - o1.getLastUpdateTime()));
        RechargeActorListVO vo = new RechargeActorListVO();
        vo.setActorList(actorVOList);
        return new HttpResult<RechargeActorListVO>().ok(vo);
    }

    /**
     * 获取最近通话
     *
     * @return
     */
    private HttpResult<RechargeActorListVO> getLatestChatList() {
        Set<ZSetOperations.TypedTuple<String>> set = rechargeRedis.getLatestChatUser(LATEST_RECHARGE_SIZE);
        List<ActorVO> actorVOList = new ArrayList<>();
        for (ZSetOperations.TypedTuple<String> typedTuple : set) {
            ActorVO actorVO = actorVOCache.getActorVO(typedTuple.getValue());
            if (actorVO == null) {
                continue;
            }
            actorVO.setLastChatTime(typedTuple.getScore().longValue());
            actorVOList.add(actorVO);
        }
        actorVOList.sort((o1, o2) -> (int) (o2.getLastChatTime() - o1.getLastChatTime()));
        RechargeActorListVO vo = new RechargeActorListVO();
        vo.setActorList(actorVOList);
        return new HttpResult<RechargeActorListVO>().ok(vo);
    }

    /**
     * 获取指定用户列表信息
     *
     */
    private HttpResult<RechargeActorListVO> getGroupChatUserList(List<String> userIdList) {
        List<ActorVO> actorVOList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(userIdList)){
            for (String userId : userIdList) {
                ActorData actorData = actorDao.getActorByStrRid(userId);
                if(actorData == null){
                    continue;
                }
                ActorVO actorVO = actorVOCache.getActorVO(actorData.getUid());
                if (actorVO == null) {
                    continue;
                }
                actorVO.setLastUpdateTime(actorData.getLastLogin() != null ? actorData.getLastLogin().getLoginTime() : 0);
                actorVOList.add(actorVO);
            }
            actorVOList.sort((o1, o2) -> (int) (o2.getLastUpdateTime() - o1.getLastUpdateTime()));
        }
        RechargeActorListVO vo = new RechargeActorListVO();
        vo.setActorList(actorVOList);
        return new HttpResult<RechargeActorListVO>().ok(vo);
    }

    private void pushPrivateMsg(SendChatMsgDTO dto){
        dto.setUid(CustomerServiceUser.getUid());
        com.quhong.datas.HttpResult<Object> result = msgService.sendMsg(dto);
        logger.info("send inner msg result. {}", JSON.toJSONString(result));
        if (result.isError()) {
            return;
        }
        // 添加到最近列表中
        rechargeRedis.addToLatestChatRechargeUser(dto.getAid());
    }



    public HttpResult<Object> sendMsg(SendChatMsgDTO dto) {
        if(!CollectionUtils.isEmpty(dto.getAidList())){
            for (String aid : dto.getAidList()) {
                dto.setAid(aid);
                pushPrivateMsg(dto);
            }
        }
        return new HttpResult<>().ok();
    }

    public HttpResult<MsgRecordListVO> getRecordList(MsgRecordDTO dto) {
        dto.setUid(CustomerServiceUser.getUid());
        com.quhong.datas.HttpResult<MsgRecordListVO> result = msgService.getMsgRecordList(dto);
        logger.info("get inner record list. {}", JSON.toJSONString(result));
        if (result.isError()) {
            return new HttpResult<MsgRecordListVO>().error(result.getCode(), result.getMsg());
        }
        return new HttpResult<MsgRecordListVO>().ok(result.getData());
    }
}
