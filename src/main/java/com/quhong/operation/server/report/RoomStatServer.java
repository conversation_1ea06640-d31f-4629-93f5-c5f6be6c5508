package com.quhong.operation.server.report;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.datas.DayTimeData;
import com.quhong.operation.dao.*;
import com.quhong.operation.share.data.AggStatData;
import com.quhong.operation.share.data.RoomReportStatData;
import com.quhong.operation.share.mysql.EnterRoom;
import com.quhong.operation.share.mysql.RoomReportLogData;
import com.quhong.operation.share.vo.reports.room.RoomMusicNewStatVO;
import com.quhong.operation.share.vo.reports.room.RoomMusicStatVO;
import com.quhong.operation.share.vo.reports.room.RoomVideoNewStatVO;
import com.quhong.operation.share.vo.reports.room.RoomVideoStatVO;
import com.quhong.operation.utils.MathUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
public class RoomStatServer {

    private static final Logger logger = LoggerFactory.getLogger(RoomStatServer.class);

    @Autowired
    private DauStatDao dauStatDao;

    @Autowired
    private VideoOptDao videoOptDao;

    @Autowired
    private EnterRoomDao enterRoomDao;

    @Autowired
    private RoomReportLogDao roomReportLogDao;

    @Autowired
    private OperationActorDao actorDao;

    /**
     * 获取每天视频房统计数据
     *
     * @param dayTimeData
     * @param os
     * @return
     */
    public RoomVideoStatVO getVideoRoomStat(DayTimeData dayTimeData, int os) {
        RoomVideoStatVO resultVO = new RoomVideoStatVO();
        resultVO.setDate(dayTimeData.getDate());
        int optOs = os;
        if (os == 2) {
            os = 0;
        }
        //set 开启视频人数、次数
        List<AggStatData> optStatList = videoOptDao.getStatData(null, dayTimeData, optOs);
        int optCount = 0;
        for (AggStatData aggStatData : optStatList) {
            optCount = optCount + aggStatData.getCount();
        }
        resultVO.setOpVideoCount(optCount);
        resultVO.setOpVideoUserCount(optStatList.size());
        //set 进视屏房人数，次数
        List<AggStatData> userDayRoomStats = enterRoomDao.listUserDayRoomStat(null, dayTimeData, os, EnterRoom.ROOM_TYPE_VIDEO);
        int enterRoomCount = 0;
        for (AggStatData aggStatData : userDayRoomStats) {
            enterRoomCount = enterRoomCount + aggStatData.getCount();
        }
        resultVO.setEnterCount(enterRoomCount);
        resultVO.setEnterUserCount(userDayRoomStats.size());
        //set 高峰期平均在线视频房数量、高峰期平均人数
        DayTimeData peakDay = new DayTimeData();
        peakDay.setTime(dayTimeData.getTime() + 19 * 60 * 60);
        peakDay.setEndTime(dayTimeData.getEndTime() + 2 * 60 * 60);
        peakDay.setDate(dayTimeData.getDate());
        RoomReportStatData peakStat = roomReportLogDao.getAvgStatData(peakDay, RoomReportLogData.ROOM_TYPE_VIDEO, 0);
        Double peakRoomAvg = 0.0;
        Double peakUserAvg = 0.0;
        if (peakStat != null) {
            peakRoomAvg = peakStat.getRoomAvgNum() == null ? 0.0 : peakStat.getRoomAvgNum();
            peakUserAvg = peakStat.getUserAvgNum() == null ? 0.0 : peakStat.getUserAvgNum();
        }
        resultVO.setPeakAvgRoomCount(peakRoomAvg.intValue());
        resultVO.setPeakAvgUserCount(peakUserAvg.intValue());
        //set 平均播放视频个数
        RoomReportStatData roomAvgStat = roomReportLogDao.getAvgStatData(dayTimeData, RoomReportLogData.ROOM_TYPE_VIDEO, 0);
        Double videoAvg = 0.0;
        if (roomAvgStat != null) {
            videoAvg = roomAvgStat.getContentAvgNum() == null ? 0.0 : roomAvgStat.getContentAvgNum();
        }
        resultVO.setAvgPlayVideoCount(videoAvg.intValue());
        //set 高峰时期视频房间平均在线时长
        List<AggStatData> peakEnterStatList = enterRoomDao.listDayRoomStat(null, peakDay, os, EnterRoom.ROOM_TYPE_VIDEO);
        int peakEnterTime = 0;
        for (AggStatData aggStatData : peakEnterStatList) {
            peakEnterTime = peakEnterTime + aggStatData.getSum();
        }
        int peakEnterRoomCount = peakEnterStatList.size();
        if (peakEnterTime == 0 || peakEnterRoomCount == 0) {
            resultVO.setPeakAvgRoomTime(0);
        } else {
            int peakAvgRoomTime = peakEnterTime / peakEnterRoomCount;
            resultVO.setPeakAvgRoomTime(peakAvgRoomTime);
        }
        //set 1分钟以内人数，观看1-5分钟人数，观看5-10分钟人数，观看10-20分钟人数，观看20-30分钟人数，观看30分钟以上人数
        int ltOneMinUserCount = 0;
        int ltFiveMinUserCount = 0;
        int ltTenMinUserCount = 0;
        int ltTwentyUserCount = 0;
        int ltThirtyUserCount = 0;
        int gtThirtyUserCount = 0;
        for (AggStatData aggStatData : userDayRoomStats) {
            int onlineTime = aggStatData.getSum();
            if (onlineTime < 60) {
                ltOneMinUserCount++;
            } else if (onlineTime < 5 * 60) {
                ltFiveMinUserCount++;
            } else if (onlineTime < 10 * 60) {
                ltTenMinUserCount++;
            } else if (onlineTime < 20 * 60) {
                ltTwentyUserCount++;
            } else if (onlineTime < 30 * 60) {
                ltThirtyUserCount++;
            } else {
                gtThirtyUserCount++;
            }
        }
        resultVO.setLtOneMinUserCount(ltOneMinUserCount);
        resultVO.setLtFiveMinUserCount(ltFiveMinUserCount);
        resultVO.setLtTenMinUserCount(ltTenMinUserCount);
        resultVO.setLtTwentyMinUserCount(ltTwentyUserCount);
        resultVO.setLtThirtyMinUserCount(ltThirtyUserCount);
        resultVO.setGtThirtyMinUserCount(gtThirtyUserCount);
        return resultVO;
    }

    /**
     * 获取每天新用户视频房统计数据
     *
     * @param dayTimeData
     * @param os
     * @return
     */
    public RoomVideoNewStatVO getNewUserVideoStat(DayTimeData dayTimeData, int os) {
        RoomVideoNewStatVO resultVO = new RoomVideoNewStatVO();
        resultVO.setDate(dayTimeData.getDate());
        int optOs = os;
        if (os == 2) {
            os = 0;
        }
        Set<String> uidSet = actorDao.totalNewActorUidSet(dayTimeData.getTime(), dayTimeData.getEndTime(), os, -1);
        if (CollectionUtils.isEmpty(uidSet)) {
            logger.error("get new user video stat error. new user is empty. date={} os={}", dayTimeData.getDate(), os);
            return resultVO;
        }
        //set 操作视频人数、次数
        List<AggStatData> optStatList = videoOptDao.getStatData(uidSet, dayTimeData, -1);
        int optCount = 0;
        Set<String> optUidSet = new HashSet<>();
        for (AggStatData aggStatData : optStatList) {
            optCount = optCount + aggStatData.getCount();
            optUidSet.add(aggStatData.getUid());
        }
        resultVO.setOpVideoCount(optCount);
        resultVO.setOpVideoUserCount(optStatList.size());
        //set 平均播放时长
        List<AggStatData> enterRoomList = enterRoomDao.listDayRoomStat(uidSet, dayTimeData, os, EnterRoom.ROOM_TYPE_VIDEO);
        int enterRoomTime = 0;
        for (AggStatData aggStatData : enterRoomList) {
            enterRoomTime = enterRoomTime + aggStatData.getSum();
        }
        resultVO.setOnlineTime(enterRoomTime);
        int enterRoomSize = enterRoomList.size();
        if (enterRoomTime == 0 || enterRoomSize == 0) {
            resultVO.setAvgPlayTime(0);
        } else {
            resultVO.setAvgPlayTime(enterRoomTime / enterRoomSize);
        }
        //set 进房次数，进房人数
        List<AggStatData> userDayRoomStats = enterRoomDao.listUserDayRoomStat(uidSet, dayTimeData, os, EnterRoom.ROOM_TYPE_VIDEO);
        int enterCount = 0;
        for (AggStatData aggStatData : userDayRoomStats) {
            enterCount = enterCount + aggStatData.getCount();
        }
        resultVO.setEnterCount(enterCount);
        resultVO.setEnterUserCount(userDayRoomStats.size());
        //set 看1分钟以下人数、看1-3分钟人数、看3-5分钟人数、看5分钟以上人数
        int ltOneMinUserCount = 0;
        int ltThreeMinUserCount = 0;
        int ltFiveMinUserCount = 0;
        int gtFiveMinUserCount = 0;
        Set<String> ltOneMinUidSet = new HashSet<>();
        Set<String> ltThreeMinUidSet = new HashSet<>();
        Set<String> ltFiveMinUidSet = new HashSet<>();
        Set<String> gtFiveMinUidSet = new HashSet<>();
        for (AggStatData aggStatData : userDayRoomStats) {
            int onlineTime = aggStatData.getSum();
            String uid = aggStatData.getUid();
            if (onlineTime < 60) {
                ltOneMinUserCount++;
                ltOneMinUidSet.add(uid);
            } else if (onlineTime < 3 * 60) {
                ltThreeMinUserCount++;
                ltThreeMinUidSet.add(uid);
            } else if (onlineTime < 5 * 60) {
                ltFiveMinUserCount++;
                ltFiveMinUidSet.add(uid);
            } else {
                gtFiveMinUserCount++;
                gtFiveMinUidSet.add(uid);
            }
        }
        resultVO.setLtOneMinUserCount(ltOneMinUserCount);
        resultVO.setLtThreeUserCount(ltThreeMinUserCount);
        resultVO.setLtFiveUserCount(ltFiveMinUserCount);
        resultVO.setGtFiveUserCount(gtFiveMinUserCount);
        //set 开启进房的用户留存
        DayTimeData remainDay = remainDay(dayTimeData);
        int optRemain = remainUserCount(remainDay, os, optUidSet);
        resultVO.setOpVideoRemain(MathUtils.getRate(optRemain, optUidSet.size()));
        //set 看1分钟以下留存、看1-3分钟留存、看3-5分钟留存、看5分钟以上留存
        int ltOneMinRemain = remainUserCount(remainDay, os, ltOneMinUidSet);
        resultVO.setLtOneMinRemain(MathUtils.getRate(ltOneMinRemain, ltOneMinUserCount));
        int ltThreeMinRemain = remainUserCount(remainDay, os, ltThreeMinUidSet);
        resultVO.setLtThreeRemain(MathUtils.getRate(ltThreeMinRemain, ltThreeMinUserCount));
        int ltFiveRemain = remainUserCount(remainDay, os, ltFiveMinUidSet);
        resultVO.setLtFiveRemain(MathUtils.getRate(ltFiveRemain, ltFiveMinUserCount));
        int gtFiveRemain = remainUserCount(remainDay, os, gtFiveMinUidSet);
        resultVO.setGtFiveRemain(MathUtils.getRate(gtFiveRemain, gtFiveMinUserCount));
        return resultVO;
    }

    /**
     * 获取音乐房统计数据
     *
     * @param dayTimeData
     * @param os
     * @return
     */
    public RoomMusicStatVO getMusicStat(DayTimeData dayTimeData, int os) {
        RoomMusicStatVO resultVO = new RoomMusicStatVO();
        resultVO.setDate(dayTimeData.getDate());
        //set dau
        int dau = dauStatDao.getDauByDay(os, dayTimeData, null);
        resultVO.setDau(dau);
        //set 同时在线房间数量、人数
        DayTimeData peakDay = new DayTimeData();
        peakDay.setTime(dayTimeData.getTime() + 19 * 60 * 60);
        peakDay.setEndTime(dayTimeData.getEndTime() + 2 * 60 * 60);
        peakDay.setDate(dayTimeData.getDate());
        RoomReportStatData peakStat = roomReportLogDao.getAvgStatData(peakDay, RoomReportLogData.ROOM_TYPE_MUSIC, 0);
        Double peakRoomAvg = 0.0;
        Double peakUserAvg = 0.0;
        if (peakStat != null) {
            peakRoomAvg = peakStat.getRoomAvgNum() == null ? 0.0 : peakStat.getRoomAvgNum();
            peakUserAvg = peakStat.getUserAvgNum() == null ? 0.0 : peakStat.getUserAvgNum();
        }
        resultVO.setOnlineRoomNum(peakRoomAvg.intValue());
        resultVO.setOnlineUserNum(peakUserAvg.intValue());
        //set 进房人数、次数
        List<AggStatData> enterStatList = enterRoomDao.listUserDayRoomStat(null, dayTimeData, os, EnterRoom.ROOM_TYPE_MUSIC);
        Set<String> enterUidSet = new HashSet<>();
        int enterRoomCount = 0;
        for (AggStatData aggStatData : enterStatList) {
            String uid = aggStatData.getUid();
            enterUidSet.add(uid);
            int count = aggStatData.getCount();
            enterRoomCount = enterRoomCount + count;
        }
        resultVO.setEnterRoomUserNum(enterUidSet.size());
        resultVO.setEnterRoomCount(enterRoomCount);
        //set 点歌人数、点歌次数
        List<AggStatData> musicOptStatList = new ArrayList<>();
        int clickCount = 0;
        for (AggStatData aggStatData : musicOptStatList) {
            int count = aggStatData.getCount();
            clickCount = clickCount + count;
        }
        resultVO.setPlayMusicUserNum(musicOptStatList.size());
        resultVO.setPlayMusicCount(clickCount);
        return resultVO;
    }

    /**
     * 获取新用户音乐房统计数据
     *
     * @param dayTimeData
     * @param os
     * @return
     */
    public RoomMusicNewStatVO getNewUserMusicStat(DayTimeData dayTimeData, int os) {
        RoomMusicNewStatVO resultVO = new RoomMusicNewStatVO();
        resultVO.setDate(dayTimeData.getDate());
        //set 总新增用户数
        Set<String> newUidSet = actorDao.totalNewActorUidSet(dayTimeData.getTime(), dayTimeData.getEndTime(), os, -1);
        int newUserCount = newUidSet.size();
        resultVO.setAddUserNum(newUserCount);
        if (newUserCount == 0) {
            logger.info("get new user music stat. new user is empty. date={} os={}", dayTimeData.getDate(), os);
            return resultVO;
        }
        //set 进音乐房人数、次数
        List<AggStatData> enterStatList = enterRoomDao.listUserDayRoomStat(newUidSet, dayTimeData, os, EnterRoom.ROOM_TYPE_MUSIC);
        Set<String> enterUidSet = new HashSet<>();
        int enterCount = 0;
        for (AggStatData aggStatData : enterStatList) {
            String uid = aggStatData.getUid();
            int count = aggStatData.getCount();
            enterUidSet.add(uid);
            enterCount = enterCount + count;
        }
        resultVO.setEnterRoomUserNum(enterUidSet.size());
        resultVO.setEnterRoomCount(enterCount);
        //set 点歌人数、点歌次数
        List<AggStatData> musicClickStatList = new ArrayList<>();
        Set<String> musicClickUidSet = new HashSet<>();
        int musicClickCount = 0;
        for (AggStatData aggStatData : musicClickStatList) {
            String uid = aggStatData.getUid();
            int count = aggStatData.getCount();
            musicClickUidSet.add(uid);
            musicClickCount = musicClickCount + count;
        }
        resultVO.setPlayMusicUserNum(musicClickUidSet.size());
        resultVO.setPlayMusicCount(musicClickCount);
        //set 进音乐房留存
        DayTimeData remainDay = remainDay(dayTimeData);
        if (!CollectionUtils.isEmpty(enterUidSet)) {
            resultVO.setEnterRemain(MathUtils.getRate(remainUserCount(remainDay, os, enterUidSet), enterUidSet.size()));
        }
        if (!CollectionUtils.isEmpty(musicClickUidSet)) {
            resultVO.setPlayRemain(MathUtils.getRate(remainUserCount(remainDay, os, musicClickUidSet), musicClickUidSet.size()));
        }
        return resultVO;
    }

    private int remainUserCount(DayTimeData dayTimeData, int os, Set<String> uidSet) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return 0;
        }
        List<ActorData> remainActors = actorDao.remainActors(dayTimeData, os, uidSet);
        return remainActors.size();
    }

    private DayTimeData remainDay(DayTimeData dayTimeData) {
        DayTimeData remainDay = new DayTimeData();
        remainDay.setTime(dayTimeData.getEndTime());
        int endTime = (int) (DateHelper.ARABIAN.getNextDay(dayTimeData.getEndTime() * 1000L).getTime() / 1000);
        remainDay.setEndTime(endTime);
        String date = DateHelper.ARABIAN.formatDateInDay(new Date(dayTimeData.getEndTime() * 1000L));
        remainDay.setDate(date);
        return remainDay;
    }

}
