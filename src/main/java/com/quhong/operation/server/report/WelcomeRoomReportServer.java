package com.quhong.operation.server.report;

import com.alibaba.fastjson.JSONObject;
import com.quhong.mongo.config.OpMongoBean;
import com.quhong.mongo.dao.FriendsNumDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.RedisKeys;
import com.quhong.operation.dao.*;
import com.quhong.operation.server.EnterRoomServer;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.LastLogin;
import com.quhong.operation.share.mongobean.UserLevel;
import com.quhong.operation.share.mysql.RoomMic;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.reports.money.WelcomeRoomVO;
import com.quhong.operation.share.vo.reports.money.detail.ConsumeMoneyDetailVO;
import com.quhong.operation.utils.Country;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.StringUtil;
import com.quhong.redis.RedisBean;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/9/19
 */
@Service
public class WelcomeRoomReportServer{

    private final static Logger logger = LoggerFactory.getLogger(WelcomeRoomReportServer.class);

    @Autowired
    private ElasticsearchDao elasticsearchDao;
    @Resource(name= OpMongoBean.MOVIES)
    private MongoTemplate mongoTemp;
    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private RoomMicStatDao roomMicDao;
    @Autowired
    private EnterRoomServer enterRoomServer;
    @Autowired
    private RoomTimeDao roomTimeDao;
    @Autowired
    private GiftRecordMgDao giftRecordMgDao;
    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private FollowRoomOpDao followRoomOpDao;
    @Autowired
    private FollowActorDao followActorDao;
    @Autowired
    private FriendsNumDao friendsNumDao;
    @Autowired
    private ReportsServer reportsServer;

    /**
     * 获取时间段内注册当天进了迎新房且充值的用户信息
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 注册当天进了迎新房且充值的用户信息
     */
    public ApiResult<List<WelcomeRoomVO>> everydayWelcomeRoomNewActorChargeInfo(Integer startTime, Integer endTime) {
        ApiResult<List<WelcomeRoomVO>> result = new ApiResult<>();
        List<WelcomeRoomVO> lists = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            // set 充值相关的数据：充值次数、充值金额、充值渠道
            ApiResult<Map<String, TotalVO>> apiResult = elasticsearchDao.getRegisterAndChargeActor(time[0], time[1]);
            if (!apiResult.isOK()) {
                logger.error("getRegisterAndChargeActor error msg {}", apiResult.getMsg());
                return result.error(apiResult.getMsg());
            }
            Map<String, TotalVO> map = apiResult.getData();
            Set<String> uidSet = map.keySet();
            for (String uid : uidSet) {
                WelcomeRoomVO welcomeRoomVo = everyoneCharge(uid, true);

                if (null != welcomeRoomVo) {
                    // 充值信息：充值次数、金额、渠道
                    welcomeRoomVo.setPayChannel(map.get(uid).getChannel());
                    welcomeRoomVo.setRechargeCount(map.get(uid).getCountNum().intValue());
                    welcomeRoomVo.setRechargeMoney(map.get(uid).getSumNum().toString());
                    lists.add(welcomeRoomVo);
                }
            }
        }

        return result.ok(lists);
    }

    /**
     * 每个用户的某段时间的数据
     *
     * @param vo        用户uid和充值信息
     * @param startTime 开始
     * @param endTime   结束
     * @return 信息
     */
    public ApiResult<List<String>> fillDateChargeNewActor(TotalVO vo, Integer startTime, Integer endTime) {
        ApiResult<List<String>> result = new ApiResult<>();

        String uid = vo.getChannel();
        ApiResult<Boolean> apiResult = enterRoomServer.isRegisterInWelcomeRoom(uid);
        if (!apiResult.isOK() || !apiResult.getData()) {
            logger.info("error msg={} isRegisterInWelcomeRoom = {}", apiResult.getMsg(), apiResult.getData());
            return result.error(apiResult.getMsg());
        }
        // 填充用户信息

        List<String> list = actorInfo(uid);
        otherInfo(list, startTime, endTime, uid);
        list.add(vo.getCountNum() + "");
        list.add(vo.getSumNum().toString());
        return result.ok(list);
    }

    /**
     * 获取某个时间段内充值的用户
     * @param start 开始时间
     * @param end   结尾时间
     * @return 某个时间段内充值的用户
     */
//    @Override
//    public ApiResult<List<List<String>>> taiyakiChargeActorInfo(Integer start, Integer end) {
//        logger.info("taiyakiChargeActorInfo param s={} e={}", start, end);
//        ApiResult<List<List<String>>> result = new ApiResult<>();
//        List<List<String>> lists = new ArrayList<>();
//        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(start, end);
//
//        // 获取每天当天充值的用户
//        for (String dateStr : dateArr) {
//            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
//            // 获取时间段内注册当天充值的用户
//            ApiResult<Map<String, TotalVO>> apiResult = elasticsearchApi.getRegisterAndChargeActor(time[0], time[1]);
//            if (!apiResult.isOK()) {
//                logger.error("getRegisterAndChargeActor error msg {}", apiResult.getMsg());
//                continue;
//            }
//            Map<String, TotalVO> map = apiResult.getData();
//            Set<String> uidSet = map.keySet();
//            for (String uid : uidSet) {
//                List<String> list = everyoneCharge(uid, false);
//
//                if (null != list) {
//                    // 充值信息
//                    list.add(map.get(uid).getCountNum() + "");
//                    list.add(map.get(uid).getSumNum().toString());
//                    lists.add(list);
//                }
//            }
//        }
//
//        return result.ok(lists);
//    }

    /**
     * 充值超过五分钟用户数据
     * @param start 开始时间
     * @param end   结尾时间
     * @return
     */
    public ApiResult<List<WelcomeRoomVO>> mic5MinutesActorInfo(Integer start, Integer end) {
        ApiResult<List<WelcomeRoomVO>> result = new ApiResult<>();
        logger.info("mic5MinutesActorInfo param s={} e={}", start, end);
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(start, end);
        ApiResult<List<RoomMic>> apiResult;
        List<RoomMic> list = new ArrayList<>();
        // 获取注册当天上麦超过5分钟的信息
        for (String dateStr : dateArr) {
            apiResult = roomMicDao.mic5MinutesNewActor(dateStr, 1);
            if (!apiResult.isOK()) {
                logger.error("get mic5MinutesNewActor error msg={}", apiResult.getMsg());
                continue;
            }
            // 没有数据，跳过循环
            if (CollectionUtils.isEmpty(apiResult.getData())) continue;
            list.addAll(apiResult.getData());
        }

        List<WelcomeRoomVO> lists = new ArrayList<>();
        for (RoomMic mic : list) {
            WelcomeRoomVO welcomeRoomVO = fullActorInfo(mic.getUserId());
            Long time = DateHelper.ARABIAN.setStartTime(mic.getCtime()*1000L);
            int s = (int) (time/1000);
            int e = s + 24 * 3600;
            fullOtherInfo(welcomeRoomVO, s, e, mic.getUserId());
            ApiResult<TotalVO> result1 = elasticsearchDao.chargeInfoByUid(s, e, mic.getUserId());
            if (result1.isOK() && null != result1.getData()) {
                // 充值信息：充值次数、金额、渠道
                welcomeRoomVO.setPayChannel(result1.getData().getChannel());
                welcomeRoomVO.setRechargeCount(result1.getData().getCountNum().intValue());
                welcomeRoomVO.setRechargeMoney(result1.getData().getSumNum().toString());
            }
            lists.add(welcomeRoomVO);
        }

        return result.ok(lists);
    }

    /**
     *
     * @param uid actor uid
     * @param flag 是否需要判断注册当天进过迎新房
     * @return
     */
    private WelcomeRoomVO everyoneCharge(String uid, boolean flag) {
        if (flag) {
            ApiResult<Boolean> result = enterRoomServer.isRegisterInWelcomeRoom(uid);
            if (!result.isOK() || !result.getData()) {
                // 不成功或者等于false直接返回
                logger.info("error msg={} data={}", result.getMsg(), result.getData());
                return null;
            }
        }

        // 填充用户信息
        WelcomeRoomVO welcomeRoomVo = fullActorInfo(uid);

        ObjectId oid = new ObjectId(uid);
        // 过滤开始时间
        long startTime = DateHelper.ARABIAN.setStartTime(oid.getDate()) / 1000;
        int start = (int) startTime;
        Integer end = start + 24 * 60 * 60;
        //填充其它信息：房间停留时长、上麦时长、送礼次数、送礼人数等
        fullOtherInfo(welcomeRoomVo, start, end, uid);
        return welcomeRoomVo;
    }

    /**
     * 填充actor：包含了账户余额：只适用于每日迎新房和五分钟的接口
     * @param uid
     * @return
     */
    private WelcomeRoomVO fullActorInfo(String uid){
        WelcomeRoomVO welcomeRoomVo = new WelcomeRoomVO();
        return welcomeRoomVo;
    }

    private List<String> actorInfo(String uid) {
        List<String> list = new ArrayList<>();
        return list;
    }

    /**
     * 新增了钻石消耗数、进房次数等字段、每日迎新报表和五分钟接口适用
     * @param welcomeRoomVo
     * @param start
     * @param end
     * @param uid
     */
    private void fullOtherInfo(WelcomeRoomVO welcomeRoomVo, Integer start, Integer end, String uid){
        logger.info("full otherInfo start {} end {} uid={}", start, end, uid);
        //查询uid进房次数
        ApiResult<Integer> joinRoomCountResult = enterRoomServer.actorJoinRoomCount(start, end, uid);
        if(joinRoomCountResult.isOK()){
            welcomeRoomVo.setJoinRoomCount(joinRoomCountResult.getData());
        }
        logger.info("uid = {},进房次数 = {}",uid,joinRoomCountResult.getData());
        // 房间停留
        ApiResult<TotalVO> result = roomTimeDao.actorInRoomTime(start, end, uid);
        if (result.isOK()){
            welcomeRoomVo.setStayRoomTime(result.getData().getSumNum().toString()); // 累计在房时长
        }
        // 上麦时长
        ApiResult<Integer> micResult = roomMicDao.actorAddUpMicTime(start, end, uid);
        if (micResult.isOK() && null != micResult.getData()){
            welcomeRoomVo.setOnMicTime(micResult.getData());
        }
        // 房间关注数
        ApiResult<Integer> intResult = followRoomOpDao.actorFollowRoomCount(uid);
        if (intResult.isOK()) {
            welcomeRoomVo.setFollowRoomCount(intResult.getData());
        }

        // 关注人数
        intResult = followActorDao.actorFollowActorCount(uid);
        if (intResult.isOK()) {
            welcomeRoomVo.setFollowUserCount(intResult.getData());
        }
        // 好友人数
        welcomeRoomVo.setFriends(friendsNumDao.getUserFriendsNum(uid));

        //钻石相关的消耗数
        ApiResult<ConsumeMoneyDetailVO> listResult = reportsServer.outConsumeInfo(start, end, uid);
        if (listResult.isOK() && listResult.getData() != null) {
            ConsumeMoneyDetailVO consumeList = listResult.getData();
            welcomeRoomVo.setPropCostBeans(consumeList.getBuyConsume());
            welcomeRoomVo.setGiftCostBeans(consumeList.getSendGift());
            welcomeRoomVo.setRedBoxCostBeans(consumeList.getSendRedPacket());
            welcomeRoomVo.setVipCostBeans(consumeList.getVipRecharge());
            welcomeRoomVo.setAdminSubBeans(consumeList.getAdminSubBeans());
            welcomeRoomVo.setSlotMachineCostBeans(consumeList.getTigerMachineGame());
            welcomeRoomVo.setFingerCostBeans(consumeList.getFingerGame());
            welcomeRoomVo.setNewDiceCostBeans(consumeList.getNewDiceGame());
            welcomeRoomVo.setSudoKuCostBeans(consumeList.getSudoKuGame());
            welcomeRoomVo.setTotalCostBeans(consumeList.sum());
        }

        // 送礼人数 发礼物次数
        ApiResult<Integer[]> apiResult = giftRecordMgDao.sendGiftCount(start, end, uid);
        if (apiResult.isOK()) {
            welcomeRoomVo.setSendGiftUserCount(apiResult.getData()[0]);
            welcomeRoomVo.setSendGiftCount(apiResult.getData()[1]);
        }
    }

    private void otherInfo(List<String> list, Integer start, Integer end, String uid) {
        logger.info("otherInfo start {} end {} uid={}", start, end, uid);
        // 房间停留
        ApiResult<TotalVO> result = roomTimeDao.actorInRoomTime(start, end, uid);
        if (result.isOK()) {
            list.add(result.getData().getSumNum().toString()); // 累计在房时长
        } else {
            list.add("");
        }

        // 上麦时间
        ApiResult<Integer> micResult = roomMicDao.actorAddUpMicTime(start, end, uid);
        if (micResult.isOK() && null != micResult.getData()) {
            list.add(micResult.getData().toString());
        } else {
            list.add("");
        }

        // 送礼人数 发礼物次数
        ApiResult<Integer[]> apiResult = giftRecordMgDao.sendGiftCount(start, end, uid);
        if (apiResult.isOK()) {
            list.add(apiResult.getData()[0].toString());
            list.add(apiResult.getData()[1].toString());
        } else {
            list.add("");
            list.add("");
        }
    }

}
