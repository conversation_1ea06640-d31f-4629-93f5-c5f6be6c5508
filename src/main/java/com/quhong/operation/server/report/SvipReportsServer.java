package com.quhong.operation.server.report;

import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.AppleSubDao;
import com.quhong.operation.dao.GoogleSubDao;
import com.quhong.operation.share.vo.SvipSubInfoVo;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.AppleSubDao;
import com.quhong.operation.dao.GoogleSubDao;
import com.quhong.operation.share.mysql.AppleSub;
import com.quhong.operation.share.mysql.GoogleSub;
import com.quhong.operation.share.vo.SvipSubInfoVo;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/20
 */
@Service
public class SvipReportsServer {

    private final static Logger logger = LoggerFactory.getLogger(SvipReportsServer.class);
    @Autowired
    private GoogleSubDao googleSubDao;
    @Autowired
    private AppleSubDao appleSubDao;

    /**
     * 获取一段时间的按天统计的订阅记录
     *
     * @param startTime 开始时间
     * @param entTime   结尾时间
     * @return 统计数据
     */
    public ApiResult<List<SvipSubInfoVo>> svipSubscriptionInfo(Integer startTime, Integer entTime) {
        ApiResult<List<SvipSubInfoVo>> result = new ApiResult<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, entTime);
        List<SvipSubInfoVo> list = new ArrayList<>();
        for (String date : dateArr) {
            SvipSubInfoVo googleVo = getGoogleSubByDay(date);
            SvipSubInfoVo appleVo = getAppleSubByDay(date);
            list.add(googleVo);
            list.add(appleVo);
        }

        return result.ok(list);
    }

    /**
     * 获取某天的 google svip 订阅记录
     *
     * @param currentDate 某天
     * @return 订阅记录
     */
    private SvipSubInfoVo getGoogleSubByDay(String currentDate) {
        Integer[] timeArr = DateHelper.ARABIAN.getTimeWhereRange(currentDate);
        List<GoogleSub> list = googleSubDao.getGoogleValidSubInfo(timeArr[0], timeArr[1]);
        int newSub = 0;
        int cancelSub = 0;
        int allSub = list.size();
        for (GoogleSub sub : list) {
            if (GoogleSub.CANCEL_SVIP_SUB == sub.getFstatus())
                cancelSub++;
            if (!sub.getOrderId().contains(".."))
                newSub++;
        }
        SvipSubInfoVo vo = new SvipSubInfoVo();
        vo.setDate(currentDate);
        vo.setNewSub(newSub);
        vo.setRenewSub(allSub - newSub);
        vo.setCancelSub(cancelSub);
        vo.setRenewSub(0);
        vo.setAllSub(allSub);
        vo.setAllRevenue(0.00);

        return vo;
    }

    /**
     * 获取某天的 apple svip 订阅记录
     *
     * @param currentDate 某天
     * @return 订阅记录
     */
    private SvipSubInfoVo getAppleSubByDay(String currentDate) {
        Integer[] timeArr = DateHelper.ARABIAN.getTimeWhereRange(currentDate);
        List<AppleSub> list = appleSubDao.getAppleValidSubInfo(timeArr[0], timeArr[1]);
        int newSub = 0;
        int renewSub = 0;
        int allSub = list.size();
        for (AppleSub sub : list) {
            if (AppleSub.SUB_SVIP == sub.getFstatus())
                newSub++;
            if (AppleSub.RENEW_SVIP == sub.getFstatus() || AppleSub.SVIP_RESTORE == sub.getFstatus())
                renewSub++;
        }
        SvipSubInfoVo vo = new SvipSubInfoVo();
        vo.setDate(currentDate);
        vo.setNewSub(newSub);
        vo.setRenewSub(renewSub);
        vo.setCancelSub(0);
        vo.setRenewSub(0);
        vo.setAllSub(allSub);
        vo.setAllRevenue(0.00);
        return vo;
    }

    /**
     * 获取一段时间的按天统计的订阅记录
     *
     * @param startTime 开始时间
     * @param entTime   结尾时间
     * @return 统计数据
     */
    public ApiResult<List<List<String>>> svipSubscriptionInfo1(Integer startTime, Integer entTime) {
        ApiResult<List<List<String>>> result = new ApiResult<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, entTime);
        List<List<String>> lists = new ArrayList<>();
        for (String date : dateArr) {
            List<String> list = clazzToList(getGoogleSubByDay(date), getAppleSubByDay(date));
            lists.add(list);
        }
        return result.ok(lists);
    }

    /**
     * 对象转为list
     *
     * @param googleVo google对象
     * @param appleVo  apple对象
     * @return list
     */
    private List<String> clazzToList(SvipSubInfoVo googleVo, SvipSubInfoVo appleVo) {
        List<String> list = new ArrayList<>();
        list.add(googleVo.getDate());

        int newSub = googleVo.getNewSub() + appleVo.getNewSub();
        int renewSub = googleVo.getRenewSub() + appleVo.getRenewSub();
        int cancelSub = googleVo.getCancelSub() + appleVo.getCancelSub();
        int recoverSub = googleVo.getRecoverSub() + appleVo.getRecoverSub();
        int allSub = googleVo.getAllSub() + appleVo.getAllSub();
        double allRevenue = googleVo.getAllRevenue() + appleVo.getAllRevenue();

        list.add(googleVo.getNewSub() + "+" + appleVo.getNewSub() + "=" + newSub);
        list.add(googleVo.getRenewSub() + "+" + appleVo.getRenewSub() + "=" + renewSub);
        list.add(googleVo.getCancelSub() + "+" + appleVo.getCancelSub() + "=" + cancelSub);
        list.add(googleVo.getRecoverSub() + "+" + appleVo.getRecoverSub() + "=" + recoverSub);
        list.add(googleVo.getAllSub() + "+" + appleVo.getAllSub() + "=" + allSub);
        list.add(googleVo.getAllRevenue() + "+" + appleVo.getAllRevenue() + "=" + allRevenue);

        return list;
    }

}
