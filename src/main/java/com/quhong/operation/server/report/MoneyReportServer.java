package com.quhong.operation.server.report;

import com.alibaba.fastjson.JSONObject;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.dao.FriendsNumDao;
import com.quhong.mysql.dao.GoodsDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.GoodsData;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.constant.MoneyDetailATypeConsts;
import com.quhong.operation.country.CountryQuery;
import com.quhong.operation.dao.*;
import com.quhong.operation.server.EnterRoomServer;
import com.quhong.operation.share.data.CountryData;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.BeautifulRid;
import com.quhong.operation.share.mongobean.RechargedUserOpData;
import com.quhong.operation.share.mongobean.UserLevel;
import com.quhong.operation.share.mysql.ApplePay;
import com.quhong.operation.share.mysql.GooglePay;
import com.quhong.operation.share.mysql.SubscriptionLogData;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.MonthlyRechargeVO;
import com.quhong.operation.share.vo.reports.OnlinePayGoodsMarketVO;
import com.quhong.operation.share.vo.reports.OnlinePayInfoVO;
import com.quhong.operation.share.vo.reports.money.detail.ConsumeMoneyDetailVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.StringUtil;
import com.quhong.utils.ActorUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/9/14
 */
@Service
public class MoneyReportServer {

    private final static Logger logger = LoggerFactory.getLogger(MoneyReportServer.class);

    @Autowired
    private GoodsDao goodsDao;
    @Autowired
    private ApplePayDao applePayDao;
    @Autowired
    private GooglePayDao googlePayDao;
    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private RechargedUserOpDao rechargedUserOpDao;
    @Autowired
    private ElasticsearchDao elasticsearchDao;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemp;
    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private ReportsServer reportsServer;
    @Autowired
    private FollowRoomOpDao followRoomOpDao;
    @Autowired
    private FollowActorDao followActorDao;
    @Autowired
    private FriendsNumDao friendsNumDao;
    @Autowired
    private RoomTimeDao roomTimeDao;
    @Autowired
    private GiftRecordMgDao giftRecordMgDao;
    @Autowired
    private SubscriptionLogDao subscriptionLogDao;
    @Autowired
    private CountryQuery countryQuery;
    @Autowired
    private EnterRoomServer enterRoomServer;
    @Autowired
    private MoneyDetailStatDao moneyDetailStatDao;

    /**
     * 获取Apple和Google支付的次数人数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 信息
     */
    public ApiResult<List[]> getOnlineChargeInfo(Integer startTime, Integer endTime) {
        logger.info("getChargeInfo param s={} e={}", startTime, endTime);
        ApiResult<List[]> result = new ApiResult<>();
        List<GoodsData> goodsList = goodsDao.getGoodsList();
        if (CollectionUtils.isEmpty(goodsList)) {
            logger.error("not find goods data");
            return result.error("not find goods data");
        }

        ApiResult<List<ApplePay>> appleResult = applePayDao.getPaySucceedList(startTime, endTime);

        ApiResult<List<GooglePay>> googleResult =
                googlePayDao.getPaySucceedList(new Date(startTime * 1000L), new Date(endTime * 1000L));

        List<OnlinePayInfoVO> payInfoLists =
                fillPayInfo(appleResult.getData(), googleResult.getData(), startTime, endTime, false);
        List<OnlinePayInfoVO> newUserPayInfoLists =
                fillPayInfo(appleResult.getData(), googleResult.getData(), startTime, endTime, true);
        List<OnlinePayGoodsMarketVO> goodsMarketLists =
                fillGoodsMarketInfo(goodsList, googleResult.getData(), appleResult.getData());
        return result.ok(new List[]{payInfoLists, newUserPayInfoLists, goodsMarketLists});
    }

    /**
     * 统计每天的付费情况
     *
     * @param appleList  苹果支付记录
     * @param googleList 谷歌支付记录
     * @param start      开始时间
     * @param end        结尾时间
     * @return 每天的付费情况信息
     */
    private List<OnlinePayInfoVO> fillPayInfo(
            List<ApplePay> appleList, List<GooglePay> googleList, Integer start, Integer end, boolean isNewUser) {
        String[] dateArray = DateHelper.ARABIAN.getDateDiffArray(start, end);
        // 统计每天付费情况
        List<OnlinePayInfoVO> payInfo = new ArrayList<>();
        for (String dateStr : dateArray) {
            payInfo.add(somedayPayInfo(dateStr, googleList, appleList, isNewUser));
        }
        return payInfo;
    }

    /**
     * 获取某天的充值信息
     *
     * @param dateStr    某天
     * @param googleList 谷歌支付信息
     * @param appleList  ios支付信息
     * @return 某天的充值信息
     */
    private OnlinePayInfoVO somedayPayInfo(String dateStr, List<GooglePay> googleList, List<ApplePay> appleList, boolean isNewUser) {
        OnlinePayInfoVO onlinePayInfoVO = new OnlinePayInfoVO();
        onlinePayInfoVO.setDate(dateStr);
        int googleCount = 0;
        Set<String> googlePerNum = new HashSet<>();
        int appleCount = 0;
        Set<String> applePerNum = new HashSet<>();

        Integer end = DateHelper.ARABIAN.getEndSeconds(dateStr);
        Integer start = DateHelper.ARABIAN.getStartSeconds("", end);
        // 苹果支付信息统计
        for (ApplePay pay : appleList) {
            if (start <= pay.getCtime() && end > pay.getCtime()) {
                if (isNewUser) {
                    if (ActorUtils.isNewRegisterActor(pay.getUserId(), 7)) {
                        appleCount++;
                        applePerNum.add(pay.getUserId());
                    }
                } else {
                    appleCount++;
                    applePerNum.add(pay.getUserId());
                }
            }
        }

        long s = (long) start * 1000L;
        long e = (long) end * 1000L;
        // 谷歌支付信息统计
        for (GooglePay pay : googleList) {
            if (s <= pay.getCtime().getTime() && e > pay.getCtime().getTime()) {
                if (isNewUser) {
                    if (ActorUtils.isNewRegisterActor(pay.getUserId(), 7)) {
                        googleCount++;
                        googlePerNum.add(pay.getUserId());
                    }
                } else {
                    googleCount++;
                    googlePerNum.add(pay.getUserId());
                }
            }
        }

        onlinePayInfoVO.setGooglePayCount(googleCount);
        onlinePayInfoVO.setGooglePayUserCount(googlePerNum.size());
        onlinePayInfoVO.setApplePayCount(appleCount);
        onlinePayInfoVO.setApplePayUserCount(applePerNum.size());
        onlinePayInfoVO.setPayCount(googleCount + appleCount);
        applePerNum.addAll(googlePerNum);
        onlinePayInfoVO.setPayUserCount(applePerNum.size());

        return onlinePayInfoVO;
    }

    /**
     * 填充充值商品销售信息
     *
     * @param goodsList  商品列表
     * @param googleList 谷歌支付记录
     * @param appleList  苹果支付记录
     * @return 充值商品销售信息
     */
    private List<OnlinePayGoodsMarketVO> fillGoodsMarketInfo(
            List<GoodsData> goodsList, List<GooglePay> googleList, List<ApplePay> appleList) {

        List<OnlinePayGoodsMarketVO> lists = new ArrayList<>();
        for (GoodsData gs : goodsList) {
            OnlinePayGoodsMarketVO list = goodsMarketInfo(gs, googleList, appleList);
            lists.add(list);
        }

        return lists;
    }

    /**
     * 某个商品销售的情况
     *
     * @param goods      某个商品
     * @param googleList 谷歌销售信息
     * @param appleList  苹果销售信息
     * @return 某个商品销售的情况
     */
    private OnlinePayGoodsMarketVO goodsMarketInfo(GoodsData goods, List<GooglePay> googleList, List<ApplePay> appleList) {
        String showInfo = goods.getShowInfo();
        JSONObject jsonObject = JSONObject.parseObject(showInfo);
        String origin = jsonObject.getString("origin");
        OnlinePayGoodsMarketVO onlinePayGoodsMarketVO = new OnlinePayGoodsMarketVO();
        onlinePayGoodsMarketVO.setPrice(origin);
        String productId = goods.getProductId();
        onlinePayGoodsMarketVO.setProductName(goods.getBeans() + "");
        int googleCount = 0;
        Set<String> googlePerNum = new HashSet<>();
        int appleCount = 0;
        Set<String> applePerNum = new HashSet<>();
        for (GooglePay pay : googleList) {
            if (productId.equals(pay.getProductId())) {
                googleCount++;
                googlePerNum.add(pay.getUserId());
            }

        }
        for (ApplePay pay : appleList) {
            if (productId.equals(pay.getProductId())) {
                appleCount++;
                applePerNum.add(pay.getUserId());
            }
        }

        onlinePayGoodsMarketVO.setGooglePayCount(googleCount);
        onlinePayGoodsMarketVO.setGooglePayUserCount(googlePerNum.size());
        onlinePayGoodsMarketVO.setApplePayCount(appleCount);
        onlinePayGoodsMarketVO.setApplePayUserCount(applePerNum.size());
        onlinePayGoodsMarketVO.setPayCount(googleCount + appleCount);
        applePerNum.addAll(googlePerNum);
        onlinePayGoodsMarketVO.setPayUserCount(applePerNum.size());

        return onlinePayGoodsMarketVO;
    }

    /**
     * 月份线上充值用户详情数据
     *
     * @param startTime startTime
     * @param endTime   endTime
     */
    public List<MonthlyRechargeVO> getMonthlyRechargeInfo(Integer startTime, Integer endTime) {
        List<MonthlyRechargeVO> lists = new ArrayList<>();
        //获取这段时间内进行了首冲的用户id
        List<RechargedUserOpData> rechargedUserList = rechargedUserOpDao.listByCtime(startTime, endTime);
        Set<String> uidSet = new HashSet<>();
        for (RechargedUserOpData rechargedUserOpData : rechargedUserList) {
            uidSet.add(rechargedUserOpData.getUid());
        }
        //开始查询充值数据
        ApiResult<Map<String, Map<String, Integer>>> apiResult = elasticsearchDao.totalActorChargeInfo(startTime, endTime, uidSet);
        if (!apiResult.isOK()) {
            logger.error("getChargeActor error info {}", apiResult.getMsg());
            return lists;
        }
        Map<String, Map<String, Integer>> data = apiResult.getData();
        for (String uid : data.keySet()) {
            ApiResult<Actor> actorApiResult = actorDao.getActorByUid(uid);
            if (!actorApiResult.isOK()) {
                logger.error("uid={} getActorByUid error info {}", uid, actorApiResult.getMsg());
                continue;
            }

            //填充actor信息
            Actor actor = actorApiResult.getData();
            MonthlyRechargeVO vo = fillBeansConsume(actor, startTime, endTime);

            Map<String, Integer> map = data.get(uid);
            vo.setChargeCount(map.get("count") + ""); // 充值次数
            vo.setChargeSum(map.get("sum") + ""); // 充值总额

            // apple支付次数和总额
            vo.setAppleChargeTime(map.get("appleCount") + "");
            vo.setAppleChargeSum(map.get("appleSum") + "");

            // google支付次数和总额
            vo.setGoogleChargeTime(map.get("googleCount") + "");
            vo.setGoogleChargeSum(map.get("googleSum") + "");

            // tap支付次数和总额
            vo.setTapPayTime(map.get("tapCount") + "");
            vo.setTapPaySum(map.get("tapSum") + "");

            // admin线下支付次数和总额
            vo.setAdminChargeTime(map.get("adminCount") + "");
            vo.setAdminChargeSum(map.get("adminSum") + "");

            // 填充轮盘消耗钻石数
            vo.setWheelDiamond(map.get("turntableWorkSum") + "");

            // 填充首次充值时间
            Integer mtime = map.get("mtime");
            if (mtime != null) {
                String firstPayChargeDate = DateHelper.ARABIAN.timestampToDatetimeStr(mtime * 1000L);
                vo.setFirstChargeTime(firstPayChargeDate);
            } else {
                vo.setFirstChargeTime("");
            }
            //set 第一次购买svip
            SubscriptionLogData subscriptionLogData = subscriptionLogDao.findFirstBySubType(uid, 4);
            Long ctime = subscriptionLogData.getCtime();
            if (ctime != null) {
                String firstSvipSubDate = DateHelper.ARABIAN.timestampToDatetimeStr(ctime * 1000);
                vo.setFirstSubSvipTime(firstSvipSubDate);
            } else {
                vo.setFirstSubSvipTime("");
            }
            //set 首冲距离注册天数
            int registerTime = new ObjectId(uid).getTimestamp();
            int firstPayDay = ((mtime - registerTime) / (24 * 60 * 60)) + 1;
            vo.setFirstChargeDay(firstPayDay + "");
            lists.add(vo);
        }
        return lists;
    }


    /**
     * 填充各种钻石消费
     *
     * @param actor 用户信息
     * @param start 开始时间
     * @param end   结尾时间
     * @return list集合
     */
    private MonthlyRechargeVO fillBeansConsume(Actor actor, Integer start, Integer end) {
        String uid = actor.get_id().toString();
        MonthlyRechargeVO vo = fillActorBaseData(actor);

        ApiResult<TotalVO> voResult = elasticsearchDao.adminSendBeansNum(start, end, uid);
        if (voResult.isOK()) {
            vo.setSendDiamondTime(voResult.getData().getCountNum().toString()); // admin送钻次数
            vo.setSendDiamondCount(voResult.getData().getSumNum().toString()); // admin送钻总数
        } else {
            vo.setSendDiamondTime("");
            vo.setSendDiamondCount("");
            logger.error("get admin send bean error uid={}, error info={}", uid, voResult.getMsg());
        }

        voResult = roomTimeDao.actorInRoomTime(start, end, uid);
        // 累计进房次数
        int joinRoomStart = new ObjectId(uid).getTimestamp();
        int joinRoomEnd = (int) (new Date().getTime() / 1000L);
        vo.setEnterRoomTime(enterRoomServer.actorJoinRoomCount(joinRoomStart, joinRoomEnd, uid).getData() + "");
        if (voResult.isOK()) {
            vo.setRoomStayTime(voResult.getData().getSumNum().toString()); // 累计在房时长
            vo.setEnterRoomDay(voResult.getData().getCountNum().toString()); // 累计在房天数
        } else {
            vo.setRoomStayTime("");
            vo.setEnterRoomDay("");
            logger.error("get actor room time error uid={}, error info={}", uid, voResult.getMsg());
        }

        ApiResult<Integer> intResult = followRoomOpDao.actorFollowRoomCount(uid);
        if (intResult.isOK()) {
            vo.setFollowRoomCount(intResult.getData() + ""); // 房间关注数
        } else {
            vo.setFollowRoomCount("");
            logger.error("uid={} get follow room  error message={}", uid, intResult.getMsg());
        }

        intResult = followActorDao.actorFollowActorCount(uid);
        if (intResult.isOK()) {
            vo.setFollowUserCount(intResult.getData() + ""); // 关注了多少人
        } else {
            vo.setFollowUserCount("");
            logger.error("uid={} get follow actor  error message={}", uid, intResult.getMsg());
        }

        vo.setFriendCount(friendsNumDao.getUserFriendsNum(uid) + ""); // 有多少好友

        ApiResult<Integer[]> arrResult = giftRecordMgDao.sendGiftCount(start, end, uid);
        if (arrResult.isOK()) {
            vo.setSendGiftCount(arrResult.getData()[1] + ""); // 送礼次数
            vo.setSendGiftPerson(arrResult.getData()[0] + ""); // 送礼人数
        } else {
            vo.setSendGiftCount("");
            vo.setSendGiftPerson("");
            logger.error("get send info error uid={}, error info={}", uid, arrResult.getMsg());
        }
        vo.setAccountBalances(actor.getBeans() + ""); // 账号余额

        //钻石相关的消耗数
        ApiResult<ConsumeMoneyDetailVO> listResult = reportsServer.outConsumeInfo(start, end, uid);
        if (listResult.isOK() && listResult.getData() != null) {
            ConsumeMoneyDetailVO consumeList = listResult.getData();
            vo.setStagePropertyDiamond(consumeList.getBuyConsume() + ""); // 道具购买消耗钻石
            vo.setGiftDiamond(consumeList.getSendGift() + ""); // 礼物消耗钻石
            vo.setLuckBoxDiamond(consumeList.getSendRedPacket() + ""); // 红包消耗钻石
            vo.setVipDiamond(consumeList.getVipRecharge() + ""); // vip购买消耗钻石
            vo.setAdminDiamond(consumeList.getAdminSubBeans() + ""); // admin减钻
            vo.setSlotMachineDiamond(consumeList.getTigerMachineGame() + ""); // 老虎机消耗钻石
            vo.setFingerGuessDiamond(consumeList.getFingerGame() + ""); // 猜拳消耗钻石
            vo.setNewDiceDiamond(consumeList.getNewDiceGame() + ""); // 新骰子游戏消耗钻石
            vo.setSudokuDiamond(consumeList.getSudoKuGame() + ""); // 九宫格游戏消耗钻石
            //set 幸运卡牌游戏消耗钻石数
            ApiResult<TotalVO> playLuckCardGameBeanResult = moneyDetailStatDao.findBeansByUid(start, end, uid, MoneyDetailATypeConsts.PLAY_LUCK_CARD_GAME);
            int playLuckCardGameBean = 0;
            if (playLuckCardGameBeanResult.isOK()) {
                playLuckCardGameBean = playLuckCardGameBeanResult.getData().getSumNum().intValue();
            }
            vo.setLuckCardDiamond(playLuckCardGameBean + "");
            //set 幸运数字游戏消耗钻石
            ApiResult<TotalVO> playDiceGameBeanResult = moneyDetailStatDao.findBeansByUid(start, end, uid, MoneyDetailATypeConsts.PLAY_DICE_GAME);
            int playDiceGameBean = 0;
            if (playDiceGameBeanResult.isOK()) {
                playDiceGameBean = playDiceGameBeanResult.getData().getSumNum().intValue();
            }
            vo.setDiceDiamond(playDiceGameBean + "");
            //set 总消耗钻石数
            String totalBeanStr = consumeList.sum() + "";
            int totalBean = Integer.parseInt(totalBeanStr) + playLuckCardGameBean + playDiceGameBean;
            vo.setTotalDiamond(totalBean + "");
        }
        return vo;
    }

    /**
     * 从id到VIP等级
     * 填充用户基本信息
     *
     * @param actor 用户基本信息
     * @return list集合
     */
    private MonthlyRechargeVO fillActorBaseData(Actor actor) {
        String uid = actor.get_id().toString();
        Integer rid = actor.getRid();
        MonthlyRechargeVO vo = new MonthlyRechargeVO();
        Integer[] ridArr = ridOrBeautifulRid(uid, rid);
        vo.setId(ridArr[0] + ""); // 原始rid
        vo.setPrettyId(ridArr[1] + ""); // 靓号rid
        vo.setGender("1".equals(actor.getFbGender()) ? "男" : "女");
        if (StringUtil.isEmptyOrBlank(actor.getCountry())) {
            vo.setCountry("");
        } else {
            //通过ip查找国家
            String ip = actor.getIp();
            vo.setCountry("");
            if (!StringUtils.isEmpty(ip)) {
                CountryData countryData = countryQuery.find(ip);
                if (countryData != null) {
                    vo.setCountry(countryData.getCountry());
                }
            }
        }
        vo.setRegTime(DateHelper.ARABIAN.timestampToDatetimeStr(new ObjectId(actor.get_id().toString()).getTimestamp() * 1000L));
        vo.setLoginDay(roomTimeDao.countOnlineDaysByUid(null, uid) + "");
        String lastLogin = "";
        long diff = 0L;
        if (null != actor.getLastLogin() && null != actor.getLastLogin().getLoginTime()
                && actor.getLastLogin().getLoginTime() > 0) {
            lastLogin = DateHelper.ARABIAN.timestampToDatetimeStr(actor.getLastLogin().getLoginTime() * 1000L);
            diff = actor.getLastLogin().getLoginTime() - actor.get_id().getTimestamp();
            int day = 60 * 60 * 24;
            diff = diff / day;
        }
        vo.setLastLoginTime(lastLogin);
        vo.setLifeCycle(diff + "");
        vo.setLoginDevice("1".equals(actor.getOs()) ? "IOS" : "安卓");
        Query query = new Query(Criteria.where("uid").is(uid));
        UserLevel uLevel = mongoTemp.findOne(query, UserLevel.class);
        String level = null == uLevel || 1 > uLevel.getLevel() ? "-1" : uLevel.getLevel().toString();
        vo.setUserLevel(level);
        vo.setVipLevel(vipInfoDao.getIntVipLevel(uid) + "");
        return vo;
    }

    /**
     * 获取原始rid和靓号rid
     *
     * @param uid 用户uid
     * @param rid 用户当前rid
     * @return 数组
     */
    private Integer[] ridOrBeautifulRid(String uid, Integer rid) {
        Query query = new Query(Criteria.where("uid").is(uid));
        query.addCriteria(Criteria.where("beautiful_rid").is(rid));
        query.fields().include("real_rid");
        query.fields().include("beautiful_rid");
        BeautifulRid beautifulRid = mongoTemp.findOne(query, BeautifulRid.class);
        if (null == beautifulRid || null == beautifulRid.getRealRid()) {
            return new Integer[]{rid, 0};
        }
        return new Integer[]{beautifulRid.getRealRid(), rid};
    }
}
