package com.quhong.operation.server.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.quhong.analysis.*;
import com.quhong.data.ActorData;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.ClientOS;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ReportContentType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendsNumDao;
import com.quhong.mongo.dao.PhoneAccountDao;
import com.quhong.mongo.data.PhoneAccountData;
import com.quhong.mysql.dao.*;
import com.quhong.operation.share.condition.OfficialWelcomeCondition;
import com.quhong.operation.share.mysql.PutInConsumeRecord;
import com.quhong.operation.share.vo.PutInConsumeRecordVO;
import com.quhong.mysql.data.*;
import com.quhong.mysql.slave_mapper.ustar_log.ReportSlaveMapper;
import com.quhong.mysql.slave_mapper.ustar_room.RoomMsgMapper;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.constant.AppPackageConstant;
import com.quhong.operation.constant.CountryCodeToName;
import com.quhong.operation.constant.MoneyDetailATypeConsts;
import com.quhong.operation.country.CountryQuery;
import com.quhong.operation.dao.*;
import com.quhong.operation.enums.MsgType;
import com.quhong.operation.server.EnterRoomServer;
import com.quhong.operation.server.ResourceConfigService;
import com.quhong.operation.share.condition.UserResCondition;
import com.quhong.operation.share.data.AggStatData;
import com.quhong.operation.share.data.CountryData;
import com.quhong.operation.share.data.SMOpDeviceRespondsData;
import com.quhong.operation.share.el.MoneyDetailES;
import com.quhong.operation.share.mongobean.*;
import com.quhong.operation.share.mysql.*;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.share.vo.reports.*;
import com.quhong.operation.share.vo.reports.money.MoneyDetailTypeVO;
import com.quhong.operation.share.vo.reports.money.MoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.AllMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.ConsumeMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.InMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.UserBeansWaterVO;
import com.quhong.operation.share.vo.reports.msg.MsgGiftVO;
import com.quhong.operation.utils.*;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.PartyGirlRedis;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.valuecount.InternalValueCount;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2020/6/11
 */
@Service
public class ReportsServer {

    private final static Logger logger = LoggerFactory.getLogger(ReportsServer.class);

    //半年的时间间隔 s
    private final static int HALF_YEAR_TIME = 182 * 24 * 60 * 60;
    private static final int ES_PAGE_SIZE = 10000;
    // event 右滑(喜欢)1、超级喜欢2、匹配成功3、发消息4、回复消息5、不喜欢6
    public static final int ALL = -1;
    public static final int LIKE = 1;
    public static final int SUP_LIKE = 2;
    public static final int MATCHED = 3;
    public static final int SEND_MSG = 4;
    public static final int REPLAY_MSG = 5;
    public static final int DIS_LIKE = 6;
    public static final int MATCH_AND_ROOM = 7;// 使用了匹配并进房的用户
    public static final int MATCH_NOT_ROOM = 8; //使用了匹配但是没有进房的用户
    public static final int ROOM_NOT_MATCH = 9; //进房但是没有使用匹配的用户
    public static final int LOGIN = 10; //登录
    public static final int SIGN_IN = 11; // 签到
    public static final int TASK_FINISH = 12; // 任务
    public static final int SEND_HEART_GIFT = 13; //发送心心任务
    public static final int ENTER_ROOM = 14; // 进房间

    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private RoomTimeDao roomTimeDao;
    @Autowired
    private RoomMsgDao roomMsgDao;
    @Autowired
    private RoomMicStatDao roomMicDao;
    @Autowired
    private FollowRoomOpDao followRoomOpDao;
    @Autowired
    private EnterRoomServer enterRoomServer;
    @Autowired
    private GiftRecordMgDao giftRecordMgDao;
    @Autowired
    private AdminDauDao dauDao;
    @Autowired
    private RoomDao roomDao;
    @Autowired
    private FriendsNumDao friendsNumDao;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemp1;
    @Resource(name = "elasticsearchDao")
    private ElasticsearchDao esServer;
    @Autowired
    private OperationFollowDao operationFollowDao;
    @Autowired
    private EnterRoomDao enterRoomDao;
    @Autowired
    private ActorStatDao actorStatDao;
    @Autowired
    private NewUserHonorStatDao newUserHonorDao;
    @Autowired
    private MoneyDetailStatDao moneyDetailStatDao;
    @Autowired
    private RoomOnlineDao roomOnlineDao;
    @Autowired
    private RoomUserStatDao roomUserStatDao;
    @Autowired
    private RoomUserOnlineSlaveDao roomUserOnlineSlaveDao;
    @Resource(name = "readEsTemplate")
    private ElasticsearchTemplate readEsTemplate;
    @Autowired
    private BeautifulRidChangeLogOPDao beautifulRidChangeLogOPDao;
    @Resource
    private LudoDao ludoDao;
    @Resource
    private LudoRoomOnlineDao ludoRoomOnlineDao;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private MsgRecordDao msgRecordDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private ReportsServer reportsServer; // 解决缓存失效
    @Resource
    private ReportLogStatDao reportLogStatDao;
    @Resource
    private FeedbackHandleDao feedbackHandleDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private PartyGirlRedis partyGirlRedis;
    @Resource
    private ReportSlaveMapper reportSlaveMapper;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private CountryQuery countryQuery;
    @Resource
    private RoomUserOnlineDao roomUserOnlineDao;
    @Resource
    private ActorDao actorCoreDao;
    @Resource
    private DetectUserRecordDao detectUserRecordDao;
    @Resource
    private ThirdDetectLogDao thirdDetectLogDao;
    @Resource
    private ResourceConfigService resourceConfigService;
    @Resource
    private ShuMeiDeviceAccountDao shuMeiDeviceAccountDao;
    @Resource
    private DiscernImageDao discernImageDao;
    @Resource
    private PhoneAccountDao phoneAccountDao;
    @Resource
    private PutInConsumeRecordDao putInConsumeRecordDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private OperationActorDao operationActorDao;

    private static final Pattern intPattern = Pattern.compile("[^0-9]");

    public static final List<Integer> DEVICE_EMULATOR_TYPE = Arrays.asList(203, 204, 205, 206, 209, 216, 301, 302, 303, 401, 402, 1001, 1100, 5010, 501, 214);

    //    @PostConstruct
    public void downloadPTGRemainExcel() {
        String start = "2021-10-25";
        String end = "2021-11-01";
        List<DayTimeData> dayTimeList = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(start, end);
        List<PtgCrushRemainVO> retList = new ArrayList<>();
        for (DayTimeData dayTimeData : dayTimeList) {
            String date = dayTimeData.getDate();
            PtgCrushRemainVO vo = new PtgCrushRemainVO();
            vo.setDate(date);
            Set<String> personSet = getPtgReplayNewUsers(dayTimeData.getDate());
            if (CollectionUtils.isEmpty(personSet)) {
                retList.add(vo);
                continue;
            }
            vo.setUserNum(personSet.size());
            int day1Num = dauDao.getDauCount(LocalDate.parse(date).plusDays(1).toString(), personSet);
            vo.setDay1(day1Num);
            vo.setDay1Rate(getRate(day1Num, personSet.size()));
            int day2Num = dauDao.getDauCount(LocalDate.parse(date).plusDays(2).toString(), personSet);
            vo.setDay2(day2Num);
            vo.setDay2Rate(getRate(day2Num, personSet.size()));
            int day3Num = dauDao.getDauCount(LocalDate.parse(date).plusDays(3).toString(), personSet);
            vo.setDay3(day3Num);
            vo.setDay3Rate(getRate(day3Num, personSet.size()));
            int day4Num = dauDao.getDauCount(LocalDate.parse(date).plusDays(4).toString(), personSet);
            vo.setDay4(day4Num);
            vo.setDay4Rate(getRate(day4Num, personSet.size()));
            int day5Num = dauDao.getDauCount(LocalDate.parse(date).plusDays(5).toString(), personSet);
            vo.setDay5(day5Num);
            vo.setDay5Rate(getRate(day5Num, personSet.size()));
            int day6Num = dauDao.getDauCount(LocalDate.parse(date).plusDays(6).toString(), personSet);
            vo.setDay6(day6Num);
            vo.setDay6Rate(getRate(day6Num, personSet.size()));
            int day7Num = dauDao.getDauCount(LocalDate.parse(date).plusDays(7).toString(), personSet);
            vo.setDay7(day7Num);
            vo.setDay7Rate(getRate(day7Num, personSet.size()));
            retList.add(vo);
        }
        //下载报表
        ExcelUtils.exportExcel(retList, PtgCrushRemainVO.class, "crush_new_remain_" + start + "-" + end + ".xlsx", "sheet");
    }


    /**
     * 获取用户基本信息
     *
     * @param start   开始时间
     * @param end     结束时间
     * @param ridList 哪些用户
     * @return 填充数据
     */
    public ApiResult<List<ActorBaseInfoVO>> listActorInfoByRid(String operatorUid, Integer start, Integer end, List<String> ridList) {
        ApiResult<List<ActorBaseInfoVO>> result = new ApiResult<>();
        List<ActorBaseInfoVO> lists = new LinkedList<>();

        // 获取时间段内的日期数组
        String[] dateDiffArray = DateHelper.ARABIAN.getDateDiffArray(start, end);
        List<Integer> rids = new ArrayList<>();
        Map<Integer, String> beautifulRidMap = new HashMap<>(ridList.size() / 2);
        for (String rid : ridList) {
            String s = rid.trim();
            //去掉非数字的所有字符串
//            s = intPattern.matcher(s).replaceAll("");
            if (!com.alibaba.druid.util.StringUtils.isNumber(s)) {
                ApiResult<Actor> ret = operationActorDao.getUidByStrRid(rid);
                if (!ret.isOK() || null == ret.getData()) {
                    logger.error("rid={} user is not existed or invalid", rid);
                    continue;
                }
                Actor actor = ret.getData();
                beautifulRidMap.put(actor.getRid(), rid);
                s = actor.getRid() + "";
                logger.info("rid is not a number, rid={} strRid={}", rid, s);
            }
            rids.add(Integer.parseInt(s));
        }
        List<Actor> actorList = actorStatDao.listByRid(rids);
        Map<String, Actor> actorMap = new HashMap<>(actorList.size());
        Set<String> uidSet = new HashSet<>(actorList.size());
        Set<String> phoneUidSet = new HashSet<>();
        for (Actor actor : actorList) {
            String rid = actor.getRid() + "";
            actorMap.put(rid, actor);
            uidSet.add(actor.get_id().toString());
            if ("4".equals(actor.getLoginType())) {
                phoneUidSet.add(actor.get_id().toString());
            }
        }
        //获取靓号信息
        Map<Integer, Actor> beautifulActorMap = listBeautifulActorMap(rids);
        logger.info("actor info beautiful user size = {}", beautifulActorMap.size());
        // 用户等级
        List<UserLevel> userLevels = mongoTemp1.find(new Query(Criteria.where("uid").in(uidSet)), UserLevel.class);
        Map<String, UserLevel> userLevelMap = CollectionUtil.listToKeyMap(userLevels, UserLevel::getUid);
        // 获取用户在房间内时长
        // Map<String, Integer> totalInRoomTimeMap = totalInRoomTime(uidSet, dateDiffArray);
        Map<String, Integer> totalInRoomTimeMap = roomUserOnlineDao.userOnRoomTimeByUidSet(start, end, uidSet);
        // 获取用户时间段内在房消费
        Map<String, Integer> roomDevoteSumMap = roomDevoteSum(uidSet, start, end);
        // 获取时间段内充值合计
//        Map<String, Integer> rechargeCountMap = rechargeCount(uidSet, start, end);
        Map<String, Integer> rechargeCountMap = new HashMap<>();
        // 好友数
//        Map<String, Integer> friendsNumMap = friendsNumDao.getActorFriendsNum(uidSet);
        Map<String, Integer> friendsNumMap = new HashMap<>();
        // 用户上麦时长
//        Map<String, Integer> UpMicTimeMap = roomMicDao.actorAddUpMicTimeByUidSet(start, end, uidSet);
        Map<String, Integer> UpMicTimeMap = new HashMap<>();
        // 用户粉丝数
//        List<AggStatData> fansList = operationFollowDao.listStatByAid(uidSet);
//        Map<String, AggStatData> fansStatMap = new HashMap<>();
//        for (AggStatData aggStatData : fansList) {
//            fansStatMap.put(aggStatData.getUid(), aggStatData);
//        }
//        logger.info("actor info fans size = {}", fansList.size());
        // 荣耀积分
//        List<NewUserHonor> honorList = newUserHonorDao.findHonorByUid(uidSet);
        Map<String, NewUserHonor> honorMap = new HashMap<>();
//        for (NewUserHonor newUserHonor : honorList) {
//            honorMap.put(newUserHonor.get_id(), newUserHonor);
//        }
//        logger.info("actor info honor size = {}", honorList.size());
        // 获取房间关注人数
        Set<String> roomIdSet = new HashSet<>();
        for (String uid : uidSet) {
            String roomId = "r:" + uid;
            roomIdSet.add(roomId);
        }
//        List<AggStatData> followRoomActorList = followRoomOpDao.roomFollowActorStat(start, end, roomIdSet);
        Map<String, AggStatData> followRoomActorMap = new HashMap<>();
//        for (AggStatData aggStatData : followRoomActorList) {
//            followRoomActorMap.put(aggStatData.getUid(), aggStatData);
//        }
//        logger.info("actor info follow room actor size = {}", followRoomActorList.size());
        // 获取房间送礼人数
//        Map<String, Integer> roomSendGiftUserMap = giftRecordMgDao.getRoomSendGiftUserStat(start, end, roomIdSet);
//        logger.info("actor info get room send gift user stat size = {}",roomSendGiftUserMap.size());
        Map<String, PhoneAccountData> phoneAccountDataMap = new HashMap<>();
        Set<String> operatorSet = getShowPhoneEmailOperator();
//        if (operatorSet.contains(operatorUid)){
//            phoneAccountDataMap = phoneAccountDao.getUserPhoneMapByUidSet(phoneUidSet);
//        }

        for (Integer fromRid : rids) {
            String fromRidStr = fromRid + "";
            ActorBaseInfoVO actorBaseInfoVO = new ActorBaseInfoVO();
            String fromBeautifulRidStr = beautifulRidMap.get(fromRid);
            actorBaseInfoVO.setFromRid(fromBeautifulRidStr != null ? fromBeautifulRidStr : fromRidStr);
            Actor actor = actorMap.get(fromRidStr);
            // 通过字符串fromRid获取actor
            if (actor == null) {
                actor = beautifulActorMap.get(fromRidStr);
            }
            // 如果actor是空的，则跳过此次循环
            if (null == actor || null == actor.get_id()) {
                logger.error("actor not exist;before rid = {}", fromRidStr);
                continue;
            }

            String uid = actor.get_id().toString();
            actorBaseInfoVO.setAfterRid(actor.getRid());// 返回用户最新rid

            // 获取用户等级
            UserLevel uLevel = userLevelMap.get(uid);
            int level = null == uLevel || 1 > uLevel.getLevel() ? -1 : uLevel.getLevel();
            //是否是安卓用户
            String isAndroid = StringUtil.isEmptyOrBlank(actor.getOs())||"0".equals(actor.getOs()) ? "Yes" : "no";

            int inRoomTime = totalInRoomTimeMap.getOrDefault(uid, 0);
            int roomDevoteSum = roomDevoteSumMap.getOrDefault(uid, 0);
            int rechargeCount = rechargeCountMap.getOrDefault(uid, 0);

            // 是mongo数据生成的时间视为注册时间
            int registerTime = actor.get_id().getTimestamp();
            if (0 != registerTime) {
                actorBaseInfoVO.setRegisteredTime(DateHelper.ARABIAN.dateToStr(new Date(registerTime * 1000L)));
            }
            // 最后一次登录时间
            LastLogin lastLogin = actor.getLastLogin();
            if (null != lastLogin && null != lastLogin.getLogoutTime() && 0 != lastLogin.getLogoutTime()) {
                actorBaseInfoVO.setLogoutTime(DateHelper.ARABIAN.dateToStr(new Date(lastLogin.getLogoutTime() * 1000L)));
            }
            actorBaseInfoVO.setUserLevel(level);
            actorBaseInfoVO.setIsAndroid(isAndroid);
            actorBaseInfoVO.setCountry(actor.getCountry());
            actorBaseInfoVO.setAllRoomTimeCount(inRoomTime);
            actorBaseInfoVO.setFbGender(actor.getFbGender());
            actorBaseInfoVO.setNowBeans(actor.getBeans());
            actorBaseInfoVO.setRoomDevote(roomDevoteSum);
            actorBaseInfoVO.setIp(actor.getIp());
            actorBaseInfoVO.setTnId(actor.getTn_id());
            CountryData countryData = countryQuery.find(actor.getIp());
            if (countryData != null) {
                actorBaseInfoVO.setIpCountry(countryData.getCountry());// IP国家
            }
            actorBaseInfoVO.setImei(actor.getImei());
            actorBaseInfoVO.setAndroidId(actor.getAndroidId());
            actorBaseInfoVO.setRechargeCount(rechargeCount);
            actorBaseInfoVO.setFriendNum(friendsNumMap.getOrDefault(uid, 0));
            actorBaseInfoVO.setAge(actor.getAge());
            Integer vipLevel = vipInfoDao.getVipLevel(uid);
            actorBaseInfoVO.setVipLevel(vipLevel != null ? vipLevel : 0);
            actorBaseInfoVO.setIsPtg(partyGirlRedis.isPartyGirlByRedis(uid) ? 1 : 0);
//            actorBaseInfoVO.setRechargeAmount(actorPayExternalDao.getUserRechargeMoney(uid));
            actorBaseInfoVO.setRechargeAmount(new BigDecimal("0"));
            //set 粉丝数
//            AggStatData fansData = fansStatMap.get(uid);
//            if (fansData != null) {
//                actorBaseInfoVO.setFansNum(fansData.getUserCount());
//            }
            //set 用户在麦时长
            Integer upMicTime = UpMicTimeMap.getOrDefault(uid, 0);
            actorBaseInfoVO.setOnMicTime(upMicTime);
            if (upMicTime > inRoomTime) {
                //上麦时间大于再房总时间时打印
                logger.error("mic time can not gt all time start = {},end = {},uid = {},all time = {},mic time = {}", start, end, uid, inRoomTime, upMicTime);
            }
            //set 用户第一次进入房间id
//            long currentTime = new Date().getTime() / 1000;
//            int roomEnd = (int) currentTime;
//            List<EnterRoom> periodRooms = enterRoomDao.findPeriodRooms(registerTime, roomEnd, uid);
//            if (periodRooms.size() >= 1) {
//                EnterRoom startEnterRoom = periodRooms.get(0);
//                String startRoomId = startEnterRoom.getRoomId();
//                // 获取真实的roomId;即room_id对应的用户rid
//                String startUid = startRoomId.split(":")[1];
//                ApiResult<Actor> startActorResult = actorDao.getActorByUid(startUid);
//                if (startActorResult.isOK()) {
//                    //set 用户第一次进入房间id
//                    actorBaseInfoVO.setFirstInRoomId(startActorResult.getData().getRid());
//                    //set 第一次进房的时间
//                    actorBaseInfoVO.setFirstInRoomTime(DateHelper.ARABIAN.timestampToDatetimeStr(startEnterRoom.getCtime() * 1000L));
//                }
//            }
            //set 用户最后一次进入房间id
//            if (periodRooms.size() >= 1) {
//                int maxEnterTime = 0;
//                Map<Integer, EnterRoom> enterRoomMap = new HashMap<>();
//                for (EnterRoom periodRoom : periodRooms) {
//                    int ctime = periodRoom.getCtime() == null ? 0 : periodRoom.getCtime();
//                    int onlineTime = periodRoom.getOnlineTime() == null ? 0 : periodRoom.getOnlineTime();
//                    int time = ctime + onlineTime;
//                    if (time > maxEnterTime) {
//                        maxEnterTime = time;
//                        enterRoomMap.put(maxEnterTime, periodRoom);
//                    }
//                }
//                EnterRoom endEnterRoom = enterRoomMap.get(maxEnterTime);
//                String endRoomId = endEnterRoom.getRoomId();
//                //获取真实的roomId;即room_id对应的用户rid
//                String endUid = endRoomId.split(":")[1];
//                ApiResult<Actor> endActorResult = actorDao.getActorByUid(endUid);
//                if (endActorResult.isOK()) {
//                    //用户最后一次进入房间id
//                    actorBaseInfoVO.setLastInRoomId(endActorResult.getData().getRid());
//                    //set 最后一次进入房间时间
//                    actorBaseInfoVO.setLastInRoomTime(DateHelper.ARABIAN.timestampToDatetimeStr(maxEnterTime * 1000L));
//                }
//            }
            //设置用户荣耀积分
            NewUserHonor newUserHonor = honorMap.get(uid);
            if (newUserHonor != null && newUserHonor.getBeans() != null) {
                actorBaseInfoVO.setHonorBeans(newUserHonor.getBeans());
            }
            //设置房间关注人数
            String roomId = "r:" + uid;
            AggStatData followRoomActorData = followRoomActorMap.get(roomId);
            if (followRoomActorData != null) {
                actorBaseInfoVO.setRoomFollowUserCount(followRoomActorData.getUserCount());
            }
            //设置发送礼物人数
//            int roomSendGiftUsers = giftRecordMgDao.getRoomSendGiftUsers(start, end, roomId);
//            actorBaseInfoVO.setRoomSendGiftCount(roomSendGiftUsers);

            // 设置phone、email
            actorBaseInfoVO.setLoginType(actor.getLoginType());
            actorBaseInfoVO.setEmail("");
            actorBaseInfoVO.setPhoneNumber("");
            if (operatorSet.contains(operatorUid)) {
                actorBaseInfoVO.setEmail(actor.getEmail());
                PhoneAccountData phoneAccountData = phoneAccountDataMap.get(uid);
                if (phoneAccountData != null) {
                    actorBaseInfoVO.setPhoneNumber(phoneAccountData.get_id());
                }
            }
            lists.add(actorBaseInfoVO);
        }
        return result.ok(lists);
    }

    private Set<String> getShowPhoneEmailOperator() {
        try {
            Set<String> uidSet = redisTemplate.opsForSet().members("set:operation:showPhoneEmailUser");
            if (uidSet == null) {
                return Collections.emptySet();
            }
            return uidSet;
        } catch (Exception e) {
            logger.error("getShowPhoneEmailOperator error msg={}", e.getMessage());
        }
        return Collections.emptySet();
    }

    /**
     * 获取 party girl 一次打钻操作都没有的真实原因
     *
     * @param ridList
     * @return
     */
    public List<PartyGirlSendBeanFailedVO> listPartyGirlBeanFailedReason(List<String> ridList) {
        List<PartyGirlSendBeanFailedVO> target = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ridList)) {
            Set<Integer> ridSet = new HashSet<>();
            for (String rid : ridList) {
                String s = rid.trim();
                //去掉非数字的所有字符串
                s = intPattern.matcher(s).replaceAll("");
                if (StringUtils.isEmpty(s)) {
                    logger.info("rid is not a number, rid={}", rid);
                    continue;
                }
                ridSet.add(Integer.parseInt(s));
            }
            List<Actor> actorList = actorStatDao.listByRid(ridSet);
            Map<String, Actor> actorMap = new HashMap<>();
            for (Actor actor : actorList) {
                String rid = actor.getRid() + "";
                actorMap.put(rid, actor);
            }
            for (String fromRid : ridList) {
                PartyGirlSendBeanFailedVO partyGirlSendBeanFailedVO = new PartyGirlSendBeanFailedVO();
                partyGirlSendBeanFailedVO.setFromRid(fromRid);
                Actor actor = actorMap.get(fromRid);
                // 通过字符串fromRid获取actor
                if (actor == null) {
                    actor = ridExistActor(fromRid);
                }
                // 如果actor是空的，则跳过此次循环
                String reason = "";
                if (null == actor || null == actor.get_id()) {
                    reason = "用户不存在";
                } else {
                    partyGirlSendBeanFailedVO.setAfterRid(actor.getRid());
                    String gender = intPattern.matcher(actor.getFbGender().trim()).replaceAll("");
                    switch (gender) {
                        case "1":
                            reason = "男用户";
                            break;
                        case "2":
                            reason = "女用户";
                            break;
                        default:
                            break;
                    }
                }
                partyGirlSendBeanFailedVO.setReason(reason);
                target.add(partyGirlSendBeanFailedVO);
            }
        }
        return target;
    }


    /**
     * 获取用户重复数据
     *
     * @param ridList 用户rid
     * @param start   开始时间戳
     * @return
     */
    public ApiResult<List<RepeatAccountsVO>> getRepeatAccounts(List<String> ridList, Integer start) {
        ApiResult<List<RepeatAccountsVO>> result = new ApiResult<>();
        List<RepeatAccountsVO> lists = new LinkedList<>();
        for (String fromRid : ridList) {
            RepeatAccountsVO repeatAccountsVO = new RepeatAccountsVO();
            repeatAccountsVO.setFromRid(fromRid);
            // 通过字符串fromRid获取actor
            Actor actor = ridExistActor(fromRid);
            // 如果actor是空的，则跳过此次循环
            if (null == actor || null == actor.get_id()) continue;
            repeatAccountsVO.setAfterRid(actor.getRid());//返回用户最新rid
            //set 重复账号数
            repeatAccountsVO.setAccountCount(actorStatDao.findAccounts(actor, start));
            lists.add(repeatAccountsVO);
        }
        return result.ok(lists);
    }

    public ApiResult<List<PartyGirlSendBeanVO>> getPartyGirlList(Integer start, Integer end, List<String> ridList, List<SendBeansStatData> sendBeansList) {
        ApiResult<List<PartyGirlSendBeanVO>> result = new ApiResult<>();
        List<PartyGirlSendBeanVO> lists = new LinkedList<>();

        Map<String, SendBeansStatData> sendBeanMap = new HashMap<>();
        for (SendBeansStatData sendBeansStatData : sendBeansList) {
            String uid = sendBeansStatData.getUid();
            sendBeanMap.put(uid, sendBeansStatData);
        }

        Set<Integer> ridSet = new HashSet<>();
        for (String s : ridList) {
            int rid = Integer.parseInt(s);
            ridSet.add(rid);
        }
        List<Actor> actorList = actorStatDao.listByRid(ridSet);
        Set<String> uidSet = new HashSet<>();
        Map<String, Actor> actorMap = new HashMap<>();
        for (Actor actor : actorList) {
            String rid = actor.getRid() + "";
            uidSet.add(actor.get_id().toString());
            actorMap.put(rid, actor);
        }
        Map<String, TotalVO> payChargeTotalMap = new HashMap<>();
        Map<String, TotalVO> getRedTotalMap = new HashMap<>();
        Map<String, TotalVO> sendGiftTotalMap = new HashMap<>();
        Map<String, TotalVO> receiveGiftTotalMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(uidSet)) {
            //分组查询充值钻石数据
            int halfYearStart = end - HALF_YEAR_TIME;
            List<TotalVO> payChargeTotalVOS = moneyDetailStatDao.listStatByATypeAndUid(halfYearStart, end, uidSet, MoneyDetailATypeConsts.PAY_CHARGE);

            for (TotalVO totalVO : payChargeTotalVOS) {
                payChargeTotalMap.put(totalVO.getUid(), totalVO);
            }
            //分组查询收到红包钻石数
            List<TotalVO> getRedTotalVOS = moneyDetailStatDao.listStatByATypeAndUid(start, end, uidSet, MoneyDetailATypeConsts.GET_LUCKY_BOX);

            for (TotalVO getRedTotalVO : getRedTotalVOS) {
                getRedTotalMap.put(getRedTotalVO.getUid(), getRedTotalVO);
            }
            //分组查询发送礼物钻石数
            List<TotalVO> sendGiftTotalVOS = moneyDetailStatDao.listStatByATypeAndUid(start, end, uidSet, MoneyDetailATypeConsts.SEND_GIFT);
            for (TotalVO sendGiftTotalVO : sendGiftTotalVOS) {
                sendGiftTotalMap.put(sendGiftTotalVO.getUid(), sendGiftTotalVO);
            }

            //分组查询收到礼物钻石数
            List<TotalVO> receiveGiftTotalVOS = moneyDetailStatDao.listStatByATypeAndUid(start, end, uidSet, MoneyDetailATypeConsts.RECEIVE_GIFT);

            for (TotalVO receiveGiftTotalVO : receiveGiftTotalVOS) {
                receiveGiftTotalMap.put(receiveGiftTotalVO.getUid(), receiveGiftTotalVO);
            }
        }

        // 获取时间段内的日期数组
        String[] dateDiffArray = DateHelper.ARABIAN.getDateDiffArray(start, end);
//        for (String s : dateDiffArray) {
//            logger.info("dateDiffArray={}", s);
//        }
        Query query;
        Map<Integer, Integer> repeatAfterRidMap = new HashMap<>();
        Set<String> resultUidSet = new HashSet<>();
        for (String fromRid : ridList) {
            PartyGirlSendBeanVO partyGirlSendBeanVo = new PartyGirlSendBeanVO();
            partyGirlSendBeanVo.setFromRid(fromRid);
            Actor actor = actorMap.get(fromRid);
            // 通过字符串fromRid获取actor
            if (actor == null) {
                actor = ridExistActor(fromRid);
            }
            // 如果actor是空的，则跳过此次循环
            if (null == actor || null == actor.get_id()) continue;

            String uid = actor.get_id().toString();

            SendBeansStatData sendBeansStatData = sendBeanMap.get(uid);
            if (sendBeansStatData == null) {
                continue;
            }

            partyGirlSendBeanVo.setAfterRid(actor.getRid());// 返回用户最新rid
            Integer repeatNum = repeatAfterRidMap.get(actor.getRid());
            if (repeatNum == null) {
                repeatNum = 1;
                repeatAfterRidMap.put(actor.getRid(), repeatNum);
            } else {
                repeatNum = repeatNum + 1;
                repeatAfterRidMap.put(actor.getRid(), repeatNum);
            }

            resultUidSet.add(uid);
            // 获取用户等级
            query = new Query(Criteria.where("uid").is(uid));
            UserLevel uLevel = mongoTemp1.findOne(query, UserLevel.class);
            String level = null == uLevel || 1 > uLevel.getLevel() ? "-1" : uLevel.getLevel().toString();
            //是否是安卓用户
            String isAndroid = StringUtil.isEmptyOrBlank(actor.getOs()) ? "Yes" : "no";

            // 是mongo数据生成的时间视为注册时间
            int registerTime = actor.get_id().getTimestamp();
            if (0 != registerTime) {
                partyGirlSendBeanVo.setRegisteredTime(DateHelper.ARABIAN.dateToStr(new Date(registerTime * 1000L)));
            }

            // 最后一次登录时间
            LastLogin lastLogin = actor.getLastLogin();
            if (null != lastLogin && null != lastLogin.getLogoutTime() && 0 != lastLogin.getLogoutTime()) {
                partyGirlSendBeanVo.setLogoutTime(DateHelper.ARABIAN.dateToStr(new Date(lastLogin.getLogoutTime() * 1000L)));
            }

            partyGirlSendBeanVo.setUserLevel(level);
            partyGirlSendBeanVo.setIsAndroid(isAndroid);
            partyGirlSendBeanVo.setCountry(actor.getCountry());
            partyGirlSendBeanVo.setFbGender(actor.getFbGender());
            partyGirlSendBeanVo.setNowBeans(actor.getBeans());
            partyGirlSendBeanVo.setIp(actor.getIp());
            CountryData countryData = countryQuery.find(actor.getIp());
            if (countryData != null) {
                partyGirlSendBeanVo.setIpCountry(countryData.getCountry());// IP国家
            }
            partyGirlSendBeanVo.setImei(actor.getImei());
            partyGirlSendBeanVo.setAndroidId(actor.getAndroidId());
            //set 过去48小时内发钻在房间时长
            partyGirlSendBeanVo.setInRoomTime(sendBeansStatData.getInRoomTime());
            //set 过去48小时内发钻在麦时长
            partyGirlSendBeanVo.setOnMicTime(sendBeansStatData.getOnMicTime());
            //set 发送红包数
            partyGirlSendBeanVo.setSendRedBoxNum(sendBeansStatData.getSendRedNum());
            //set 系统发送钻石数
            partyGirlSendBeanVo.setAdminSendBeans(sendBeansStatData.getSendBeanNum());
            //set 系统发钻次数
            partyGirlSendBeanVo.setAdminSendBeanNum(sendBeansStatData.getSendCount());
            //set 半年内充值数据
            TotalVO payChargeTotalVO = payChargeTotalMap.get(uid);
            if (payChargeTotalVO != null) {
                //set 半年内充值钻石数、次数
                partyGirlSendBeanVo.setHalfYearPayBeans(payChargeTotalVO.getSumNum() == null ? "0" : payChargeTotalVO.getSumNum() + "");
                partyGirlSendBeanVo.setHalfYearPayBeanNum(payChargeTotalVO.getCountNum() == null ? 0 : payChargeTotalVO.getCountNum().intValue());
            }
            //set 收到红包钻石数
            TotalVO getRedTotalVO = getRedTotalMap.get(uid);
            if (getRedTotalVO != null) {
                partyGirlSendBeanVo.setReceiveRedBeans(getRedTotalVO.getSumNum() == null ? "0" : getRedTotalVO.getSumNum() + "");
            }
            //set 发送礼物钻石数
            TotalVO sendGiftTotalVO = sendGiftTotalMap.get(uid);
            if (sendGiftTotalVO != null) {
                partyGirlSendBeanVo.setSendGiftBeans(sendGiftTotalVO.getSumNum() == null ? "0" : sendGiftTotalVO.getSumNum() + "");
            }
            //set 收到礼物钻石数
            TotalVO receiveGiftTotalVO = receiveGiftTotalMap.get(uid);
            if (receiveGiftTotalVO != null) {
                partyGirlSendBeanVo.setReceiveGiftBeans(receiveGiftTotalVO.getSumNum() == null ? "0" : receiveGiftTotalVO.getSumNum() + "");
            }
            lists.add(partyGirlSendBeanVo);
        }
//        Set<String> sendBeanUisSet = sendBeanMap.keySet();
//        for (String uid : sendBeanUisSet) {
//            if(!resultUidSet.contains(uid)){
//                Actor actor = actorDao.getActor(uid);
//                logger.info("wrong send bean uid={} rid={}",uid,actor.getRid());
//            }
//        }
        List<Integer> repeatAfterRids = new ArrayList<>();
        for (Integer afterRid : repeatAfterRidMap.keySet()) {
            Integer num = repeatAfterRidMap.get(afterRid);
            if (num > 1) {
                repeatAfterRids.add(afterRid);
            }
        }
        List<PartyGirlSendBeanVO> resultList = new ArrayList<>();
        for (PartyGirlSendBeanVO sendBeanVO : lists) {
            int afterRid = sendBeanVO.getAfterRid();
            int fromRid = Integer.parseInt(sendBeanVO.getFromRid());
            if (repeatAfterRids.contains(afterRid) && fromRid != afterRid) {
                continue;
            }
            resultList.add(sendBeanVO);
        }
        return result.ok(resultList);
    }

    /**
     * 获取用户一段时间内的总流水，收入流水，消费流水
     *
     * @param start   开始时间
     * @param end     结束时间
     * @param ridList 哪些用户
     * @return 总数据
     */
    public ApiResult<MoneyDetailVO> moneyDetailTotal(Integer start, Integer end, List<String> ridList) {
        ApiResult<MoneyDetailVO> result = new ApiResult<>();
        // 批量用户的流水统计
        List<AllMoneyDetailVO> allList = new ArrayList<>();
        // 批量用户的收入明细统计
        List<InMoneyDetailVO> inList = new ArrayList<>();
        // 批量用户的消费明细统计
        List<ConsumeMoneyDetailVO> outList = new ArrayList<>();
        // 不存在的rid用户
        List<String> notActorList = new LinkedList<>();
        for (String rid : ridList) {
            ApiResult<Actor> apiResult = actorDao.getActorByRidOrBeforeRid(Integer.parseInt(rid));
            if (!apiResult.isOK() || null == apiResult.getData()) {
                notActorList.add(rid);
                logger.info("rid={} actor not exist", rid);
                continue;
            }
            Actor actor = apiResult.getData();

            // 查询es数据库
            SortBuilder sort = SortBuilders.fieldSort("mtime").order(SortOrder.DESC); // 排序
            QueryBuilder query = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("uid", actor.get_id().toString())).must(QueryBuilders.rangeQuery("mtime").gte(start).lt(end));

            Map<String, Integer> map = new HashMap<>();
            map.put("fromRid", Integer.parseInt(rid));
            map.put("afterRid", actor.getRid());

            int esResultListSize;
            int size = 10000; // 分页大小
            Pageable page = PageRequest.of(0, size);
            ApiResult<List<MoneyDetailES>> listResult;
            while (true) {
                listResult = esServer.getESDataAndSortPage(query, sort, page, MoneyDetailES.class);
                logger.info("getESDataList result size={} message={}", listResult.getData().size(), listResult.getMsg());
                if (!listResult.isOK()) {
                    logger.error("get es data error page={} errorInfo={}", page.getPageNumber(), listResult.getMsg());
                    break;
                }
                List<MoneyDetailES> moneyDetailList = listResult.getData();
                esResultListSize = moneyDetailList.size(); // 赋值es返回的数据条数
                // 合并同类费用
                map = FeeMergeUtil.mergeFee(moneyDetailList, map);

                logger.info("mergeFee map:{}", JSONObject.toJSONString(map));
                if (size == esResultListSize) page = page.next();
                else break;
            }

            // 将某个用户的收入/消费明细统计
            MoneyDetailTypeVO moneyDetailTypeVO = getCountDetailData(map);
            allList.add(moneyDetailTypeVO.getAllMoneyDetailVO());
            inList.add(moneyDetailTypeVO.getInMoneyDetailVO());
            outList.add(moneyDetailTypeVO.getConsumeMoneyDetailVO());
        }
        logger.info("not check rid actor list:{}", JSON.toJSONString(notActorList));
        // 设置返参
        MoneyDetailVO moneyDetailVO = new MoneyDetailVO();
        moneyDetailVO.setAllMoneyDetailVoList(allList);
        moneyDetailVO.setInMoneyDetailVoList(inList);
        moneyDetailVO.setConsumeMoneyDetailVoList(outList);
        return result.ok(moneyDetailVO);
    }

    /**
     * 获取用户一段时间的流水
     *
     * @param start 开始时间
     * @param end   结束时间
     * @param rid   用户
     * @return 流水
     */
    public ApiResult<List<UserBeansWaterVO>> moneyDetail(Integer start, Integer end, Integer rid) {
        ApiResult<List<UserBeansWaterVO>> result = new ApiResult<>();
        List<UserBeansWaterVO> list = new ArrayList<>();
        ApiResult<Actor> apiResult = actorDao.getActorByRidOrBeforeRid(rid);
        if (!apiResult.isOK()) {
            logger.info("rid={} actor not exist", rid);
            return result.error(apiResult.getMsg());
        }
        Actor actor = apiResult.getData();
        // 查询es数据库
        SortBuilder sort = SortBuilders.fieldSort("mtime").order(SortOrder.DESC); // 排序
        QueryBuilder query = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("uid", actor.get_id().toString())).must(QueryBuilders.rangeQuery("mtime").gte(start).lt(end));
        Integer esResultListSize = 0;
        Integer size = 10000; // 一页大小
        Pageable page = PageRequest.of(0, size);
        while (true) {
            ApiResult<List<MoneyDetailES>> moneyResult = esServer.getESDataAndSortPage(query, sort, page, MoneyDetailES.class);
            logger.info("moneyDetail getESDataAndSortPage result message={}", moneyResult.getMsg());
            if (!moneyResult.isOK() || null == moneyResult.getData()) {
                logger.error("get es data error page={} errorInfo={}", page.getPageNumber(), moneyResult.getMsg());
                break;
            }
            List<MoneyDetailES> detailList = moneyResult.getData();
            esResultListSize = detailList.size();

            List<MoneyDetailES> mList = moneyResult.getData();
            if (CollectionUtils.isEmpty(mList)) {
                return result.ok(list);
            }

            List<UserBeansWaterVO> lists = new ArrayList<>();
            for (MoneyDetailES m : mList) {
                UserBeansWaterVO userBeansWaterVo = new UserBeansWaterVO();
                userBeansWaterVo.setRid(rid);
                userBeansWaterVo.setChanged(m.getChanged());
                userBeansWaterVo.setBalance(m.getBalance());
                userBeansWaterVo.setTitle(m.getTitle());
                userBeansWaterVo.setDesc(m.getDesc());
                userBeansWaterVo.setaType(m.getAtype());
                userBeansWaterVo.setMtime(DateHelper.ARABIAN.datetimeToStr(new Date(m.getMtime() * 1000L)));
                lists.add(userBeansWaterVo);
            }
            list.addAll(lists);
            if (esResultListSize == size) {
                page = page.next();
            } else {
                break;
            }
        }

        return result.ok(list);
    }

    /**
     * 房间活跃信息
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 报表数据
     */
    public List<CompereRoomInfoVO> roomActiveInfo(Integer startTime, Integer endTime) {
        List<CompereRoomInfoVO> list = new ArrayList<>();
        // 获取迎新房主持人列表
        Query query = new Query(Criteria.where("status").is(1));
        query.fields().include("room_id");
        List<RookieRoom> rookieRooms = mongoTemp1.find(query, RookieRoom.class);
        for (RookieRoom rookieRoom : rookieRooms) {
            if (null == rookieRoom || StringUtil.isEmptyOrBlank(rookieRoom.getRoomId())) {
                continue;
            }
            String roomId = rookieRoom.getRoomId();
            String uid = StringUtil.getUidByRoomId(roomId);
            list.add(getCompereRoomInfo(startTime, endTime, roomId, uid));
        }
        return list;
    }

    /**
     * 房间内用户的行为情况
     *
     * @param rid       迎新房主持人rid
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 行为情况
     */
    public ApiResult<List<ActorActivityVO>> roomActorAction(Integer rid, Integer startTime, Integer endTime) {
        ApiResult<List<ActorActivityVO>> result = new ApiResult<>();
        Query query = new Query(Criteria.where("rid").is(rid));
        query.fields().include("_id");
        Actor actor = mongoTemp1.findOne(query, Actor.class);
        if (null == actor || null == actor.get_id()) {
            logger.info("roomActorAction rid={} actor not exist!", rid);
            return result.error("rid=" + rid + " actor not exist!");
        }
        String rooId = StringUtil.getRoomIdByUid(actor.get_id().toString());
        ApiResult<List<String>> apiResult = enterRoomServer.getRoomActorList(startTime, endTime, rooId);
        if (!apiResult.isOK() || CollectionUtils.isEmpty(apiResult.getData())) {
            logger.info("roomActorAction get room actor error roomId={} {}", rooId, apiResult.getMsg());
        }
        List<ActorActivityVO> lists = new ArrayList<>();
        for (String uid : apiResult.getData()) {
            ActorActivityVO actorActivityVO = actorActionTotal(startTime, endTime, uid);
            lists.add(actorActivityVO);
        }
        logger.info("room rid={}  roomActorAction count={}", rid, lists.size());

        return result.ok(lists);
    }

    /**
     * 将某actor用户在es消费明细区分统计
     *
     * @param map 某个actor用户es查出来的数据
     * @return
     */
    private MoneyDetailTypeVO getCountDetailData(Map<String, Integer> map) {
        MoneyDetailTypeVO moneyDetailVo = new MoneyDetailTypeVO();
        AllMoneyDetailVO all = new AllMoneyDetailVO();
        all.setFromRid(map.get("fromRid").toString());
        all.setAfterRid(map.get("afterRid"));
        InMoneyDetailVO in = new InMoneyDetailVO();
        in.setFromRid(map.get("fromRid").toString());
        in.setAfterRid(map.get("afterRid"));
        ConsumeMoneyDetailVO consume = new ConsumeMoneyDetailVO();
        consume.setFromRid(map.get("fromRid").toString());
        consume.setAfterRid(map.get("afterRid"));

        Iterator<String> iterator = map.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Integer value = map.get(key);
            if (null == value) value = 0;
            switch (key) {
                case "1":
                case "5":
                case "700":
                    // 充值
                    all.setTotalRecharge(all.getTotalRecharge() + value);
                    break;
                case "2":
                    // admin 减钻
                    all.setAdminSubBeans(all.getAdminSubBeans() + value);
                    consume.setAdminSubBeans(consume.getAdminSubBeans() + value);
                    break;
                case "40":
                    // 转盘游戏
                    all.setDialGame(all.getDialGame() + value);
                    break;
                case "51":
                case "52":
                case "53":
                case "101":
                    // 购买消费
                    all.setBuyConsume(all.getBuyConsume() + value);
                    consume.setBuyConsume(consume.getBuyConsume() + value);
                    break;
                case "61":
                case "62":
                    // pk游戏
                    all.setPkGame(all.getPkGame() + value);
                    break;
                case "67":
                case "68":
                case "69":
                case "70":
                case "71":
                    // 猜拳游戏
                    all.setFingerGame(all.getFingerGame() + value);
                    if ("67".equals(key) || "68".equals(key)) consume.setFingerGame(consume.getFingerGame() + value);
                    else in.setFingerGame(in.getFingerGame() + value);
                    break;
                case "105":
                    // 签到
                    all.setSignIn(all.getSignIn() + value);
                    in.setSignIn(in.getSignIn() + value);
                    break;
                case "100":
                    // vip 充值
                    all.setVipRecharge(all.getVipRecharge() + value);
                    consume.setVipRecharge(consume.getVipRecharge() + value);
                    break;
                case "104":
                    // vip奖励
                    all.setVipAward(all.getVipAward() + value);
                    in.setVipAward(in.getVipAward() + value);
                    break;
                case "200":
                    // 个人消费返钻
                    all.setConsumeFeedback(all.getConsumeFeedback() + value);
                    in.setConsumeFeedback(in.getConsumeFeedback() + value);
                    break;
                case "202":
                    // 房间消费返钻
                    all.setRoomConsumeFeedback(all.getRoomConsumeFeedback() + value);
                    in.setRoomConsumeFeedback(in.getRoomConsumeFeedback() + value);
                    break;
                case "204":
                case "205":
                    // 老虎机
                    all.setTigerMachineGame(all.getTigerMachineGame() + value);
                    if ("204".equals(key)) consume.setTigerMachineGame(consume.getTigerMachineGame() + value);
                    else in.setTigerMachineGame(in.getTigerMachineGame() + value);
                    break;
                case "206":
                    // 新色子游戏
                    all.setNewDiceGame(all.getNewDiceGame() + value);
                    consume.setNewDiceGame(consume.getNewDiceGame() + value);
                    break;
                case "207":
                    consume.setSudoKuGame(consume.getSudoKuGame() + value);
                    break;
                case "208":
                    in.setSudoKuGame(consume.getSudoKuGame() + value);
                    break;
                case "301":
                case "302":
                    // 收发礼物
                    all.setGetSendGift(all.getGetSendGift() + value);
                    if ("301".equals(key)) consume.setSendGift(consume.getSendGift() + value);
                    else in.setIncomeGift(in.getIncomeGift() + value);
                    break;
                case "800":
                case "801":
                case "802":
                    // 收发红包
                    all.setGetSendRedPacket(all.getGetSendRedPacket() + value);
                    if ("800".equals(key)) consume.setSendRedPacket(consume.getSendRedPacket() + value);
                    else in.setIncomeRedPacket(in.getIncomeRedPacket() + value);
                    break;
                case "901":
                case "902":
                case "903":
                case "904":
                case "905":
                case "906":
                case "907":
                    // 活动奖励收入
                    all.setActivityAward(all.getActivityAward() + value);
                    in.setActivityAward(in.getActivityAward() + value);
                    break;
                case "admin honor charge":
                    in.setAdminHonorCharge(in.getAdminHonorCharge() + value);
                    break;
                case "admin non honor charge":
                    in.setAdminNonHonorCharge(in.getAdminNonHonorCharge() + value);
                    break;
                case "fromRid":
                case "afterRid":
                    break;
                default:
                    all.setOther(all.getOther() + value);
                    break;
            }
        }

        all.setTotalConsume(all.sum());
        in.setTotalConsume(in.sum());
        consume.setTotalConsume(consume.sum());

        moneyDetailVo.setAllMoneyDetailVO(all);
        moneyDetailVo.setInMoneyDetailVO(in);
        moneyDetailVo.setConsumeMoneyDetailVO(consume);

        return moneyDetailVo;
    }

    /**
     * 通过rid获取用户信息
     * rid在actor中没有找到会去靓号中找
     *
     * @param fromRid rid
     * @return actor
     */
    private Actor ridExistActor(String fromRid) {
        try {
            int rid = Integer.parseInt(fromRid);
            ApiResult<Actor> apiResult = actorDao.getActorByRidOrBeforeRid(rid);
            if (!apiResult.isOK() || null == apiResult.getData()) {
                logger.info("actor not exist rid={} ", fromRid);
                return null;
            }
            return apiResult.getData();
        } catch (Exception e) {
            logger.info("fromRid transform rid error fromRid={} not is number", fromRid);
            return null;
        }
    }


    /**
     * 对象转固定顺序的list
     *
     * @param mList 对象集合
     * @param rid   用户rid
     * @return 返参
     */
    private List<UserBeansWaterVO> moneyToList(List<MoneyDetailES> mList, int rid) {
        if (CollectionUtils.isEmpty(mList)) return new LinkedList<>();
        List<UserBeansWaterVO> lists = new ArrayList<>();
        for (MoneyDetailES m : mList) {
            UserBeansWaterVO userBeansWaterVo = new UserBeansWaterVO();
            userBeansWaterVo.setRid(rid);
            userBeansWaterVo.setChanged(m.getChanged());
            userBeansWaterVo.setBalance(m.getBalance());
            userBeansWaterVo.setTitle(m.getTitle());
            userBeansWaterVo.setDesc(m.getDesc());
            userBeansWaterVo.setaType(m.getAtype());
            userBeansWaterVo.setMtime(DateHelper.ARABIAN.datetimeToStr(new Date(m.getMtime() * 1000L)));
            lists.add(userBeansWaterVo);
        }
        return lists;
    }

    /**
     * 获取用户在房消费合计
     *
     * @param uidSet uidSet
     * @param start  开始合计时间
     * @param end    结束合计时间
     * @return 合计
     */
    private Map<String, Integer> roomDevoteSum(Set<String> uidSet, Integer start, Integer end) {
        return new HashMap<>();
    }

    public static Set<String> formatRoomIdSet(Set<String> uidSet) {
        Set<String> result = new HashSet<>(uidSet.size());
        for (String uid : uidSet) {
            result.add("r:" + uid);
        }
        return result;
    }

    /**
     * 获取统计在房内时长
     *
     * @param uidSet        用户uidSet
     * @param dateDiffArray 时间段
     * @return 时长
     */
    private Map<String, Integer> totalInRoomTime(Set<String> uidSet, String[] dateDiffArray) {
        return roomTimeDao.totalInRoomTimeByUidSet(uidSet, Arrays.asList(dateDiffArray));
    }

    /**
     * 用户在某时间段内充值次数
     *
     * @param uidSet uidSet
     * @param start  开始时间
     * @param end    结束时间
     * @return 次数
     */
    private Map<String, Integer> rechargeCount(Set<String> uidSet, Integer start, Integer end) {
        Map<String, Integer> result = new HashMap<>();
        QueryBuilder query = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("uid", uidSet)).must(QueryBuilders.termQuery("atype", 1)).must(QueryBuilders.rangeQuery("mtime").gte(start).lt(end));

        TermsAggregationBuilder group = AggregationBuilders.terms("group").field("uid.keyword").size(ES_PAGE_SIZE).subAggregation(AggregationBuilders.count("countNum").field("uid.keyword"));
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withFields("uid").withQuery(query).addAggregation(group).build();
        AggregatedPage<MoneyDetailES> moneyDetails = readEsTemplate.queryForPage(searchQuery, MoneyDetailES.class);
        Terms terms = (Terms) moneyDetails.getAggregation("group");
        for (Terms.Bucket bucket : terms.getBuckets()) {
            InternalValueCount countNum = bucket.getAggregations().get("countNum");
            result.put(String.valueOf(bucket.getKey()), null == countNum ? 0 : (int) countNum.getValue());
        }
        return result;
    }

    private CompereRoomInfoVO getCompereRoomInfo(Integer startTime, Integer endTime, String roomId, String userId) {
        CompereRoomInfoVO vo = new CompereRoomInfoVO();

        // 主持人在麦时长
        ApiResult<Integer> result = roomMicDao.compereInMicTime(startTime, endTime, userId, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setMicTime(result.getData());
        }

        // 新用户进房人数
        result = enterRoomServer.newActorInPerson(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorJoinRoomPerson(result.getData());
        }

        // 新用户进房次数
        result = enterRoomServer.newActorInCount(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorJoinRoomCount(result.getData());
        }

        // 聊天人数
        result = roomMsgDao.roomChatPerson(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setChatPerson(result.getData());
        }

        // 聊天次数
        result = roomMsgDao.roomChatCount(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setChatCount(result.getData());
        }

        // 新用户上麦人数
        result = roomMicDao.newActorInMicPerson(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorMicPerson(result.getData());
        }

        // 新用户上麦次数
        result = roomMicDao.newActorInMicCount(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorMicCount(result.getData());
        }

        // 新用户平均在房时间
        ApiResult<Double> doubleApiResult = roomMicDao.newActorInMicTimeAvg(startTime, endTime, roomId);
        if (doubleApiResult.isOK() && null != doubleApiResult.getData()) {
            vo.setNewActorMicAvg(doubleApiResult.getData());
        }

        // 新用户在麦5分钟以上
        result = roomMicDao.newActorInMicTime5Minute(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorMic5Minute(result.getData());
        }

        // 房间关注人数
        result = followRoomOpDao.roomFollowActor(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setFollowRoomPerson(result.getData());
        }

        // 房间关注次数
//        result = roomFollowApi.roomFollowCount(startTime, endTime, roomId);
//        if (result.isOK() && null != result.getData()) {
//            vo.setFollowRoomCount(result.getData());
        vo.setFollowRoomCount(0);
//        }

        // 用户平均停留时长
        doubleApiResult = enterRoomServer.roomActorOnlineAvgTime(startTime, endTime, roomId);
        if (doubleApiResult.isOK() && null != doubleApiResult.getData()) {
            vo.setAvgInRoomTime(doubleApiResult.getData());
        }

        return vo;
    }

    private ActorActivityVO actorActionTotal(Integer startTime, Integer endTime, String userId) {
        ActorActivityVO actorActivityVO = new ActorActivityVO();

        // 进房间数量
        ApiResult<Integer> result = enterRoomServer.actorJoinRoomNum(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setJoinRoomNum(result.getData());
        }


        // 进房间次数
        result = enterRoomServer.actorJoinRoomCount(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setJoinRoomCount(result.getData());
        }

        // 累计房间停留时长
        ApiResult<TotalVO> voResult = roomTimeDao.actorInRoomTime(startTime, endTime, userId);
        if (voResult.isOK()) {
            actorActivityVO.setStayRoomDuration(voResult.getData().getSumNum().toString());// 累计在房时长
        }

        // 文字聊天次数
        result = roomMsgDao.actorChatCount(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setWordyChatCount(result.getData());
        }

        // 上麦次数
        result = roomMicDao.actorMicCount(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setOnMicCount(result.getData());
        }

        // 累计上麦时长
        result = roomMicDao.actorAddUpMicTime(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setOnMicDuration(result.getData().toString());
        }

        // 送礼人数和送礼次数
        ApiResult<Integer[]> apiResult = giftRecordMgDao.sendGiftCount(startTime, endTime, userId);
        if (apiResult.isOK()) {
            actorActivityVO.setSendGiftUserCount(apiResult.getData()[0]);
            actorActivityVO.setSendGiftCount(apiResult.getData()[1]);
        }

        // 收礼次数
        result = giftRecordMgDao.receivedGiftCount(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setReceiveGiftCount(result.getData());
        }

        // 房间关注次数
//        result = roomFollowApi.actorFollowRoomCount(startTime, endTime, userId);
//        if (result.isOK() && null != result.getData())
//            list.add(result.getData().toString());
//        else

        // 好友数
        actorActivityVO.setFriends(friendsNumDao.getUserFriendsNum(userId));

        // 充值金额
        result = esServer.actorChargeBeans(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setPayMoney(result.getData().toString());
        }
        return actorActivityVO;
    }

    /**
     * 获取用户一段时间里消费的信息
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param uid       用户user_id
     * @return 消费信息
     */
    public ApiResult<ConsumeMoneyDetailVO> outConsumeInfo(Integer startTime, Integer endTime, String uid) {
        ApiResult<ConsumeMoneyDetailVO> result = new ApiResult<>();

        // 查询es数据库
        SortBuilder sort = SortBuilders.fieldSort("mtime").order(SortOrder.DESC); // 排序
        QueryBuilder query = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("uid", uid)).must(QueryBuilders.rangeQuery("mtime").gte(startTime).lt(endTime));

        Map<String, Integer> map = new HashMap<>();
        int esResultSize;
        int size = 10000; // 分页大小
        Pageable page = PageRequest.of(0, size);
        ApiResult<List<MoneyDetailES>> apiResult;
        while (true) {
            apiResult = esServer.getESDataAndSortPage(query, sort, page, MoneyDetailES.class);
//            logger.info("outConsumeInfo result size={} message={}", apiResult.getData().size(), apiResult.getMsg());
            if (!apiResult.isOK()) {
                logger.error("get es data error page={} errorInfo={}", page.getPageNumber(), apiResult.getMsg());
                break;
            }
            List<MoneyDetailES> moneyDetailList = apiResult.getData();
            esResultSize = moneyDetailList.size(); // 赋值es返回的数据条数
            // 合并同类费用
            map = FeeMergeUtil.mergeFee(moneyDetailList, map);

            if (size == esResultSize) page = page.next();
            else break;
        }

        // 将某个用户的收入/消费明细统计

        map.put("fromRid", 123);
        map.put("afterRid", 123);
        MoneyDetailTypeVO moneyDetailTypeVO = getCountDetailData(map);
        ConsumeMoneyDetailVO consumeMoneyDetailVO = moneyDetailTypeVO.getConsumeMoneyDetailVO();
        return result.ok(consumeMoneyDetailVO);
    }

    /**
     * 获取日期时间段内的总迎新房数据报表
     *
     * @param dateArr 日期数组
     * @return 数据报表
     */
    public ApiResult<List<WelcomeNewRoomVO>> welcomeNewRoomDataInfo(String[] dateArr) {
        ApiResult<List<WelcomeNewRoomVO>> result = new ApiResult<>();
        List<WelcomeNewRoomVO> lists = new ArrayList<>();
        for (String date : dateArr) {
            WelcomeNewRoomVO vo = getDayWelcomeRoomDataInfo(date);

            if (null != vo) lists.add(vo);
        }

        return result.ok(lists);
    }

    /**
     * 获取某天的数据报表
     *
     * @param dateStr 某天
     * @return 数据报表
     */
    private WelcomeNewRoomVO getDayWelcomeRoomDataInfo(String dateStr) {
        if (StringUtil.isEmptyOrBlank(dateStr)) return null;

        List<EnterRoom> erList = getWelcomeNewRoomActorUid(dateStr);
        if (CollectionUtils.isEmpty(erList)) {
            return null;
        }
        WelcomeNewRoomVO vo = new WelcomeNewRoomVO();
        vo.setDate(dateStr);

        List<String> uidList = fillNewActorInfo(vo, erList);
        fillNewActorInRoomInfo(vo, dateStr, uidList);

        ApiResult<String> strResult = roomTimeDao.personAvgInRoomTime(dateStr, uidList);
        if (strResult.isOK()) {
            vo.setPerAvgRoomTime(strResult.getData());
        } else {
            logger.error("date={} personAvgInRoomTime error {}", dateStr, strResult.getMsg());
        }

        ApiResult<Integer> result = roomMsgDao.chatPersonNum(dateStr, uidList);
        if (result.isOK()) {
            vo.setChatPerNum(result.getData());
        } else {
            logger.error("date={} chatPersonNum error {}", dateStr, result.getMsg());
        }

        fillUpMicInfo(vo, dateStr, uidList);

        ApiResult<Integer[]> apiResult = giftRecordMgDao.sendGiftTotal(dateStr, uidList);
        if (!apiResult.isOK()) {
            logger.error("date={} sendGiftTotal error {}", dateStr, apiResult.getMsg());
        } else {
            vo.setSendGiftPerNum(apiResult.getData()[0]);
            vo.setSendGiftCount(apiResult.getData()[1]);
        }

        result = followRoomOpDao.partyActorFollowNum(dateStr, uidList);
        if (!result.isOK()) {
            logger.error("date={} partyActorFollowNum error {}", dateStr, result.getMsg());
        } else {
            vo.setFollowRoomPer(result.getData());
        }

        ApiResult<TotalVO> voResult = esServer.totalChargeInfoByUidList(dateStr, uidList);
        if (voResult.isOK()) {
            vo.setChargePerNum(voResult.getData().getPersonNum().intValue());
            vo.setChargeCount(voResult.getData().getCountNum().intValue());
            vo.setChargeBeans(voResult.getData().getSumNum().intValue());
        }

        return vo;
    }

    /**
     * 获取注册当天进入迎新房的活跃用户
     *
     * @param dateStr 某天日期
     * @return 用户uid列表
     */
    public List<EnterRoom> getWelcomeNewRoomActorUid(String dateStr) {
        ApiResult<List<EnterRoom>> result = enterRoomServer.getEnterRoomUserId(dateStr, 1);
        if (!result.isOK()) {
            logger.error("get date={} welcome room uid list error msg={}", dateStr, result.getMsg());
        }
        if (null == result.getData()) {
            return new ArrayList<>();
        }
        return result.getData();
    }

    /**
     * 填充用户新增信息
     *
     * @param vo     待填充对象
     * @param erList 新增信息
     * @return 用户uid列表
     */
    private List<String> fillNewActorInfo(WelcomeNewRoomVO vo, List<EnterRoom> erList) {
        int ios = 0;
        int android = 0;
        List<String> uidList = new ArrayList<>();
        for (EnterRoom er : erList) {
            uidList.add(er.getUserId());
            if (1 == er.getOs()) {
                ios++;
            } else {
                android++;
            }
        }

        vo.setNewActor(uidList.size());
        vo.setIosNewActor(ios);
        vo.setAndroidNewActor(android);
        return uidList;
    }

    /**
     * 填充新用户进房间信息
     *
     * @param vo      待填充对象
     * @param dateStr 日期
     * @param uidList 一批用户
     */
    private void fillNewActorInRoomInfo(WelcomeNewRoomVO vo, String dateStr, List<String> uidList) {
        List<Integer> perList = personEnterRoom(dateStr, uidList);
        // 人均进入房间
        if (0 != perList.size()) {
            BigDecimal a = new BigDecimal(perList.size());
            BigDecimal personNum = new BigDecimal(uidList.size());
            double avg = a.divide(personNum, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
            vo.setPerAvgInRoomNum(avg); // 人均进入房间数

            int totalNum = 0;
            for (Integer num : perList) {
                totalNum += num;
            }
            a = new BigDecimal(totalNum);
            double count = a.divide(personNum, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
            vo.setPerAvgInRoomCount(count); // 人均进房次数
        }
    }

    /**
     * 填充上麦信息
     *
     * @param vo      待填充对象
     * @param dateStr 日期
     * @param uidList 批量用户
     */
    private void fillUpMicInfo(WelcomeNewRoomVO vo, String dateStr, List<String> uidList) {
        ApiResult<List<TotalVO>> result = roomMicDao.actorUpMicTotal(dateStr, uidList);
        if (!result.isOK() || CollectionUtils.isEmpty(result.getData())) {
            logger.error("get actor up mic total error {}", result.getMsg());
            return;
        }
        Long countNum = 0L;
        BigDecimal sumNum = BigDecimal.ZERO;
        for (TotalVO total : result.getData()) {
            countNum += total.getCountNum();
            sumNum = sumNum.add(total.getSumNum());
        }

        BigDecimal personNum = new BigDecimal(result.getData().size());
        vo.setUpMicPer(personNum.intValue());
        if (0 < countNum)
            vo.setPerAvgUpMicCount(new BigDecimal(countNum).divide(personNum, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue());

        if (!BigDecimal.ZERO.equals(sumNum)) {
            Integer avgTime = sumNum.divide(personNum, 2, BigDecimal.ROUND_HALF_DOWN).intValue();
            vo.setPerAvgUpMicTime(DateHelper.ARABIAN.intToTimeString(avgTime));
        }
    }

    /**
     * 某天用户进迎新房的信息
     *
     * @param dateStr 日期
     * @param uidList 一批用户
     * @return 数据
     */
    private List<Integer> personEnterRoom(String dateStr, List<String> uidList) {
        ApiResult<List<Integer>> result = enterRoomServer.personEnterRoom(dateStr, uidList);
        if (!result.isOK()) {
            logger.error("date={} personEnterRoom error, info : {}", dateStr, result.getMsg());
            return new ArrayList<>();
        }
        return result.getData();
    }


    /**
     * 获取那些天的进入迎新房的用户的次留
     *
     * @param dateArr 那些天
     * @return 次留
     */
    public ApiResult<List<WelcomeNewRoomKeepVO>> welcomeNewRoomKeep(String[] dateArr) {
        ApiResult<List<WelcomeNewRoomKeepVO>> result = new ApiResult<>();
        List<WelcomeNewRoomKeepVO> lists = new ArrayList<>();
        for (String date : dateArr) {
            WelcomeNewRoomKeepVO welcomeNewRoomKeepVO = new WelcomeNewRoomKeepVO();
            welcomeNewRoomKeepVO.setDate(date);
            Double[] doubles = totalSomedayInRoomNum(date, -1);
            welcomeNewRoomKeepVO.setIosYesterdayKeep(doubles[0]);
            welcomeNewRoomKeepVO.setAndroidYesterdayKeep(doubles[1]);
            welcomeNewRoomKeepVO.setYesterdayKeep(doubles[2]);
            doubles = totalSomedayInRoomNum(date, -7);
            welcomeNewRoomKeepVO.setIosSevenKeep(doubles[0]);
            welcomeNewRoomKeepVO.setAndroidSevenKeep(doubles[1]);
            welcomeNewRoomKeepVO.setSevenKeep(doubles[2]);
            doubles = totalSomedayInRoomNum(date, -30);
            welcomeNewRoomKeepVO.setIosThirtyKeep(doubles[0]);
            welcomeNewRoomKeepVO.setAndroidThirdKeep(doubles[1]);
            welcomeNewRoomKeepVO.setThirtyKeep(doubles[2]);
            lists.add(welcomeNewRoomKeepVO);
        }
        return result.ok(lists);
    }

    public ApiResult<Map<String, Map<String, Object>>> getRookieNewUserCount(String channel, String startDate, String endDate) {
        ApiResult<Map<String, Map<String, Object>>> result = new ApiResult<>();
        Integer[] times = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("start get rookie new user count start date = {},end date = {},start time = {},end time = {}", startDate, endDate, times[0], times[1]);
        Map<String, Map<String, Object>> rookieRoomUserCount = enterRoomServer.findRookieRoomUserCount(channel, times[0], times[1]);
        return result.ok(rookieRoomUserCount);
    }

    /**
     * 统计某天注册的现在进房数
     *
     * @param dateStr 现在日期
     * @param num     前某(N)天
     * @return 数组
     */
    private Double[] totalSomedayInRoomNum(String dateStr, int num) {
        Date date = DateHelper.ARABIAN.stringToDate(dateStr);
        Date keepDate = DateHelper.ARABIAN.dateAddDay(date, num);
        String keepDateStr = DateHelper.ARABIAN.dateToStr(keepDate);

        int newIos = 0;
        int newAndroid = 0;
        List<EnterRoom> erList = getWelcomeNewRoomActorUid(keepDateStr);
        List<String> uidList = new ArrayList<>();
        for (EnterRoom er : erList) {
            if (null == er) continue;
            if (1 == er.getOs()) {
                newIos++;
            } else {
                newAndroid++;
            }
            uidList.add(er.getUserId());
        }
        int newAll = newIos + newAndroid;


        int ios = 0;
        int android = 0;
        ApiResult<List<Dau>> result = dauDao.getActiveActorByUid(dateStr, uidList);
        if (!result.isOK()) {
            logger.error("totalSomedayInRoomNum getActiveActorByUid error msg {}", result.getMsg());
            // 防止下面那行报空指针
            result.ok(new ArrayList<>());
        }
        for (Dau dau : result.getData()) {
            if (null == dau) continue;
            if (1 == dau.getOs()) {
                ios++;
            } else {
                android++;
            }
        }
        int all = ios + android;
        logger.info("num {} {} {} new num {} {} {}", ios, android, all, newIos, newAndroid, newAll);
        return new Double[]{comparison(ios, newIos), comparison(android, newAndroid), comparison(all, newAll)};
    }

    /**
     * 计算两个数的比例
     *
     * @param a 被除数
     * @param b 除数
     * @return 比例%
     */
    private Double comparison(Integer a, Integer b) {
        if (a == 0 || b == 0) return 0.00;

        BigDecimal bigDecimal = new BigDecimal(a).divide(new BigDecimal(b), 4, BigDecimal.ROUND_DOWN);
        bigDecimal = bigDecimal.multiply(new BigDecimal(100));
        return bigDecimal.doubleValue();
    }

    /**
     * @param type 数据类型
     *             所有、0
     *             日活和新增、 1
     *             创建房间人数次数、2
     *             进房人数次数、3
     *             上麦人数次数、4
     *             发言人数次数、5
     *             送礼物人数次数、6
     *             停留时长和上麦时长、7
     */
    public List<RoomUserStatVO> roomUserStat(int startTime, int endTime, Integer os, int type) {
        List<RoomUserStatVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        RoomUserStatVO vo;
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            vo = new RoomUserStatVO();
            vo.setDate(dateStr);
            if (type == 0 || type == 1) {
                Set<String> dauUidSet = dauDao.getActiveActorByWhere(time[0], --time[1], os, null, null, -1).getData();
                vo.setDau(dauUidSet.size());
                Set<String> newUserUidSet = actorDao.totalNewActorUidSet(time[0], --time[1], null == os ? -1 : os, -1);
                vo.setNewUser(newUserUidSet.size());
            }
            if (type == 0 || type == 2) {
                CreateRoomStatVO createRoomStat = roomDao.getCreateRoomStat(time[0], --time[1], os);
                vo.setCreateRoomUser(createRoomStat.getCreateRoomUser());
                vo.setCreateRoomCount(createRoomStat.getCreateRoomCount());
            }
            if (type == 0 || type == 3 || type == 7) {
                EnterRoomStatVO enterRoomStat = enterRoomDao.getEnterRoomStat(time[0], --time[1], os);
                vo.setEnterRoomUser(enterRoomStat.getEnterRoomUser());
                vo.setEnterRoomCount(enterRoomStat.getEnterRoomCount());
                if (enterRoomStat.getEnterRoomUser() > 0 && enterRoomStat.getEnterRoomCount() > 0) {
                    vo.setPerCapitaEnterRoom(enterRoomStat.getEnterRoomCount() / enterRoomStat.getEnterRoomUser());
                }
                if (enterRoomStat.getTotalOnlineTime() > 0 && enterRoomStat.getEnterRoomUser() > 0) {
                    vo.setRoomStayMinute(enterRoomStat.getTotalOnlineTime() / enterRoomStat.getEnterRoomUser() / 60);
                }
            }
            if (type == 0 || type == 4 || type == 7) {
                RoomMicStatVO roomMicStat = roomMicDao.getRoomMicStat(time[0], --time[1], os);
                vo.setUpMicUser(roomMicStat.getUpMicUser());
                vo.setUpMicCount(roomMicStat.getUpMicCount());
                if (roomMicStat.getTotalMicTime() > 0 && roomMicStat.getUpMicUser() > 0) {
                    vo.setUpMicMinute(roomMicStat.getTotalMicTime() / roomMicStat.getUpMicUser() / 60);
                }
            }
            if (type == 0 || type == 5) {
                RoomMsgStatVO roomMsgStat = roomMsgDao.getRoomMsgStat(time[0], --time[1], os);
                vo.setChatUser(roomMsgStat.getChatUser());
                vo.setChatCount(roomMsgStat.getChatCount());
            }
            if (type == 0 || type == 6) {
                List<Integer> giftTotal = giftRecordMgDao.sendGiftTotal(time[0], --time[1], os);
                vo.setSentGiftUser(giftTotal.get(0));
                vo.setSentGiftCount(giftTotal.get(1));
            }
            list.add(vo);
        }
        return list;
    }

    public List<FirstChargeStatVO> firstChargeStat(int startTime, int endTime, Integer os) {
        List<FirstChargeStatVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        FirstChargeStatVO vo;
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            vo = moneyDetailStatDao.firstChargeStat(time[0], time[1], os);
            vo.setDate(dateStr);
            list.add(vo);
        }
        return list;
    }

    public RoomOnlineStatVO roomOnlineStat(Integer startTime, Integer endTime) {
        return roomOnlineDao.roomOnlineStat(startTime, endTime);
    }

    public List<RoomUserOnlineVO> roomUserOnlineByDay(Integer startTime, Integer endTime) {
        return roomUserStatDao.roomUserOnlineByDay(startTime, endTime);
    }

    public RoomOnlineStatVO roomUserOnlineStat(Integer startTime, Integer endTime, String os, String userType) {
        return roomUserOnlineSlaveDao.roomUserOnlineStat(startTime, endTime, os, userType);
    }

    public RoomOnlineStatVO userUpMicStat(Integer startTime, Integer endTime, String os, String userType) {
        return roomMicDao.userUpMicStat(startTime, endTime, os, userType);
    }

    public List<HuaWeiPayChargeVO> huaWeiPayChargeReports(Integer startTime, Integer endTime) {
        List<HuaWeiPayChargeVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            HuaWeiPayChargeVO vo = new HuaWeiPayChargeVO();
            vo.setDate(dateStr);
            Set<String> uidSet = actorDao.huaWeiNewActorUidSet(time[0], time[1], -1, -1);
            vo.setNewUserCount(String.valueOf(uidSet.size()));
            TotalVO totalVOMap = moneyDetailStatDao.statHuaWeiCharge(dateStr, uidSet);
            vo.setChargePerson(String.valueOf(totalVOMap.getPersonNum()));
            vo.setChargeSum(String.valueOf(totalVOMap.getSumNum()));
            list.add(vo);
        }
        return list;
    }

    /**
     * 通过rid获取靓号数据
     *
     * @param ridList
     * @return
     */
    private Map<Integer, Actor> listBeautifulActorMap(List<Integer> ridList) {
        List<BeautifulRidChangeLogOP> beautifulRidChangeLogOPS = beautifulRidChangeLogOPDao.listByBeforeRids(ridList);
        Set<String> beforeUid = new HashSet<>();
        Map<String, List<BeautifulRidChangeLogOP>> beautifulRidChangeLogMap = new HashMap<>();
        for (BeautifulRidChangeLogOP beautifulRidChangeLogOP : beautifulRidChangeLogOPS) {
            String uid = beautifulRidChangeLogOP.getUid();
            if (!StringUtils.isEmpty(uid)) {
                beforeUid.add(uid);
                List<BeautifulRidChangeLogOP> list = beautifulRidChangeLogMap.get(uid);
                if (list == null) {
                    list = new ArrayList<>();
                }
                list.add(beautifulRidChangeLogOP);
                beautifulRidChangeLogMap.put(uid, list);
            }
        }
        List<Actor> beautifulActorList = actorStatDao.listByUid(beforeUid);
        Map<Integer, Actor> beautifulActorMap = new HashMap<>();
        for (Actor actor : beautifulActorList) {
            String uid = actor.get_id().toString();
            List<BeautifulRidChangeLogOP> value = beautifulRidChangeLogMap.get(uid);
            if (!CollectionUtils.isEmpty(value)) {
                for (BeautifulRidChangeLogOP beautifulRidChangeLogOP : value) {
                    beautifulActorMap.put(beautifulRidChangeLogOP.getBeforeRid(), actor);
                }
            }
        }
        return beautifulActorMap;
    }

    public List<LudoGameCurrencyVO> ludoGameCurrency(Integer startTime, Integer endTime) {
        List<LudoGameCurrencyVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            List<LudoData> gameInfoList = ludoDao.getGameInfoList(time[0], --time[1]);
            LudoGameCurrencyVO vo = new LudoGameCurrencyVO();
            vo.setDate(dateStr);
            List<String> createGameCount = new ArrayList<>();
            List<String> joinGameCount = new ArrayList<>();
            List<String> quitGameCount = new ArrayList<>();
            for (LudoData ludoData : gameInfoList) {
                // 创建游戏人数次数
                createGameCount.add(ludoData.getSelfUid());
                // 游戏总消耗
                if (ludoData.getCurrencyType() == 1) {
                    vo.setHeartConsume(vo.getHeartConsume() + ludoData.getTotalCurrency());
                } else {
                    vo.setDiamondConsume(vo.getDiamondConsume() + ludoData.getTotalCurrency());
                }
                for (GamePlayerData gamePlayerData : ludoData.getPlayerList()) {
                    // 加入游戏人数次数
                    joinGameCount.add(gamePlayerData.getUid());
                    if (gamePlayerData.getStatus() == 2) {
                        if (ludoData.getCurrencyType() == 1) {
                            // 退出回收心心
                            vo.setHeartRecover(vo.getHeartRecover() + ludoData.getCurrency());
                        } else {
                            // 退出回收钻石
                            vo.setQuitDiaRecover(vo.getQuitDiaRecover() + ludoData.getCurrency());
                            vo.setDiamondRecover(vo.getDiamondRecover() + ludoData.getCurrency());
                        }
                        // 中途退出游戏人数次数
                        quitGameCount.add(gamePlayerData.getUid());
                    }
                }
                if (ludoData.getCurrencyType() == 1) {
                    vo.setHeartRecover(vo.getHeartRecover() + ludoData.getTax());
                } else {
                    vo.setDiamondRecover(vo.getDiamondRecover() + ludoData.getTax());
                }
            }
            vo.setCreateGameCount(createGameCount.size());
            vo.setCreateGamePerson(new HashSet<>(createGameCount).size());
            vo.setJoinGameCount(joinGameCount.size());
            vo.setJoinGamePerson(new HashSet<>(joinGameCount).size());
            vo.setQuitCount(quitGameCount.size());
            vo.setQuitPerson(new HashSet<>(quitGameCount).size());
            list.add(vo);
        }
        return list;
    }

    public int getRegDay(String uid) {
        int day = (int) ((System.currentTimeMillis() - new ObjectId(uid).getTimestamp() * 1000L) / (1000 * 3600 * 24));
        return day == 0 ? 1 : day;
    }

    public List<LudoRoomVO> ludoRoom(Integer startTime, Integer endTime) {
        List<LudoRoomVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            List<LudoData> gameInfoList = ludoDao.getGameInfoList(time[0], --time[1]);
            LudoRoomVO vo = new LudoRoomVO();
            vo.setDate(dateStr);
            List<String> enterRoomUid = new ArrayList<>();
            List<String> newUserEnterRoomUid = new ArrayList<>();
            for (LudoData ludoData : gameInfoList) {
                // 玩家总人数
                vo.setPlayerCount(vo.getPlayerCount() + ludoData.getPlayerList().size());
                for (GamePlayerData gamePlayerData : ludoData.getPlayerList()) {
                    if (getRegDay(gamePlayerData.getUid()) <= 1) {
                        // 新用户玩家总人数
                        vo.setNewPlayerCount(vo.getNewPlayerCount() + 1);
                        // 新用户玩总局数
                        vo.setNewPlayerGameCount(vo.getNewPlayerGameCount() + 1);
                    }
                }
                if (ludoData.getStartTime() > 0 && ludoData.getEndTime() > 0) {
                    List<String> uidList = enterRoomDao.getEnterRoomUid(ludoData.getRoomId(), ludoData.getStartTime(), ludoData.getEndTime());
                    // 进入数
                    enterRoomUid.addAll(uidList);
                    for (String uid : uidList) {
                        if (getRegDay(uid) <= 1) {
                            newUserEnterRoomUid.add(uid);
                        }
                    }
                }
            }
            // 游戏总局数
            vo.setGameCount(gameInfoList.size());
            // 人均玩局数
            if (vo.getGameCount() > 0) {
                vo.setGamePerPlayer(vo.getPlayerCount() / vo.getGameCount());
            }
            String ludoRoomStat = ludoRoomOnlineDao.getLudoRoomStat(time[0], --time[1]);
            // ludo房同时在线数量
            vo.setLudoRoomOnlineCount(ludoRoomStat);
            // 进入人数
            int enterRoomPerson = new HashSet<>(enterRoomUid).size();
            // ludo房进入总人数
            vo.setLudoRoomEnterPerson(vo.getLudoRoomEnterPerson() + enterRoomPerson);
            // ludo房人均进入次数
            if (enterRoomPerson > 0) {
                vo.setLudoRoomPerEnterCount(enterRoomUid.size() / enterRoomPerson);
            }
            // 新用户进入人数
            vo.setNewUserEnterCount(newUserEnterRoomUid.size());
            // 新用户进入次数
            vo.setNewUserEnterPerson(new HashSet<>(newUserEnterRoomUid).size());
            list.add(vo);
        }
        return list;
    }

    private String getRate(Integer divisor, Integer dividend) {
        if (dividend == null || divisor == null) return null;
        if (divisor == 0 || dividend == 0) {
            return "0%";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(2);
        String result = numberFormat.format((float) divisor / (float) dividend * 100);
        return result + "%";
    }

    public List<LudoRetentionVO> ludoRetention(Integer startTime, Integer endTime, Integer os, Integer user) {
        List<LudoRetentionVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            String yesterday = DateHelper.ARABIAN.getYesterdayStr(dateStr);
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(yesterday);
            List<LudoData> gameInfoList = ludoDao.getGameInfoList(time[0], --time[1]);
            LudoRetentionVO vo = new LudoRetentionVO();
            vo.setDate(yesterday);
            Map<String, Integer> playCountMap = new HashMap<>();
            for (LudoData ludoData : gameInfoList) {
                for (GamePlayerData gamePlayerData : ludoData.getPlayerList()) {
                    playCountMap.compute(gamePlayerData.getUid(), (k, v) -> {
                        if (null == v) {
                            v = 0;
                        }
                        return v + 1;
                    });
                }
            }
            Set<String> playOneSet = new HashSet<>();
            Set<String> playThreeSet = new HashSet<>();
            Set<String> playFiveSet = new HashSet<>();
            Set<String> playTenSet = new HashSet<>();
            Set<String> playOverTenSet = new HashSet<>();
            for (Map.Entry<String, Integer> entry : playCountMap.entrySet()) {
                String uid = entry.getKey();
                // 过滤当天注册的新用户
                int regDay = getRegDay(uid);
                if (1 == user && regDay > 1) {
                    continue;
                } else if (2 == user && regDay <= 1) {
                    continue;
                }
                // 过滤os
                if (-1 != os) {
                    Actor actor = actorDao.getActor(uid);
                    if (3 == os && !"huawei".equals(actor.getChannel())) {
                        continue;
                    } else if (1 == os && !"1".equals(actor.getOs())) {
                        continue;
                    } else if (2 == os && "1".equals(actor.getOs())) {
                        continue;
                    }
                }
                if (entry.getValue() == 1) {
                    playOneSet.add(uid);
                } else if (entry.getValue() >= 2 && entry.getValue() <= 3) {
                    playThreeSet.add(uid);
                } else if (entry.getValue() >= 4 && entry.getValue() <= 5) {
                    playFiveSet.add(uid);
                } else if (entry.getValue() >= 6 && entry.getValue() <= 10) {
                    playTenSet.add(uid);
                } else if (entry.getValue() > 10) {
                    playOverTenSet.add(uid);
                }
            }
            vo.setPlayOne(playOneSet.size());
            int dauCount = dauDao.getDauCount(dateStr, playOneSet);
            vo.setPlayOneRet(getRate(dauCount, vo.getPlayOne()));

            vo.setPlayThree(playThreeSet.size());
            dauCount = dauDao.getDauCount(dateStr, playThreeSet);
            vo.setPlayThreeRet(getRate(dauCount, vo.getPlayThree()));

            vo.setPlayFive(playFiveSet.size());
            dauCount = dauDao.getDauCount(dateStr, playFiveSet);
            vo.setPlayFiveRet(getRate(dauCount, vo.getPlayFive()));

            vo.setPlayTen(playTenSet.size());
            dauCount = dauDao.getDauCount(dateStr, playTenSet);
            vo.setPlayTenRet(getRate(dauCount, vo.getPlayTen()));

            vo.setPlayOverTen(playOverTenSet.size());
            dauCount = dauDao.getDauCount(dateStr, playOverTenSet);
            vo.setPlayOverTenRet(getRate(dauCount, vo.getPlayOverTen()));

            list.add(vo);
        }
        return list;
    }

    public List<LudoGameDetailVO> ludoGameDetail(Integer startTime, Integer endTime) {
        List<LudoGameDetailVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            List<LudoData> gameInfoList = ludoDao.getGameInfoList(time[0], --time[1]);
            LudoGameDetailVO vo = new LudoGameDetailVO();
            vo.setDate(dateStr);
            Set<String> fastPersonSet = new HashSet<>();
            Set<String> classicPersonSet = new HashSet<>();
            Set<String> heart20Set = new HashSet<>();
            Set<String> dia20Set = new HashSet<>();
            Set<String> dia100Set = new HashSet<>();
            Set<String> dia200Set = new HashSet<>();
            for (LudoData ludoData : gameInfoList) {
                List<String> playerList = ludoData.getPlayerList().stream().map(GamePlayerData::getUid).collect(Collectors.toList());
                if (ludoData.getGameType() == 1) {
                    vo.setClassicCount(vo.getClassicCount() + 1);
                    fastPersonSet.addAll(playerList);
                } else {
                    vo.setFastCount(vo.getFastCount() + 1);
                    classicPersonSet.addAll(playerList);
                }
                if (ludoData.getCurrencyType() == 1 && ludoData.getCurrency() == 20) {
                    vo.setHeart20Count(vo.getHeart20Count() + 1);
                    heart20Set.addAll(playerList);
                } else if (ludoData.getCurrencyType() == 2 && ludoData.getCurrency() == 20) {
                    vo.setDia20Count(vo.getDia20Count() + 1);
                    dia20Set.addAll(playerList);
                } else if (ludoData.getCurrencyType() == 2 && ludoData.getCurrency() == 100) {
                    vo.setDia100Count(vo.getDia100Count() + 1);
                    dia100Set.addAll(playerList);
                } else if (ludoData.getCurrencyType() == 2 && ludoData.getCurrency() == 200) {
                    vo.setDia200Count(vo.getDia200Count() + 1);
                    dia200Set.addAll(playerList);
                }
            }
            vo.setFastPerson(fastPersonSet.size());
            vo.setClassicPerson(classicPersonSet.size());
            vo.setHeart20Person(heart20Set.size());
            vo.setDia20Person(dia20Set.size());
            vo.setDia100Person(dia100Set.size());
            vo.setDia200Person(dia200Set.size());
            list.add(vo);
        }
        return list;
    }

    public List<CrushUserVO> crushUserStat(Integer startTime, Integer endTime, Integer gender, Integer user, Integer app, String versionCodeStr) {
        return Collections.emptyList();
    }

    /**
     * 过滤用户
     *
     * @param user 新1、老2、-1全部
     */
    private boolean filterUser(String uid, Integer user, String dateStr) {
        // 过滤新老用户
        if (-1 != user) {
            boolean newRegisterActor = com.quhong.utils.ActorUtils.isNewRegisterActor(uid, dateStr);
            if (1 == user && !newRegisterActor) {
                return false;
            } else return 2 != user || !newRegisterActor;
        }
        return true;
    }

    private List<String> getMatchSendMsgPerson(String dateStr, Integer user) {
        try {
            Set<String> uidSet = redisTemplate.opsForZSet().rangeByScore("match:msg:send:" + dateStr, 0, 10000);
            if (null == uidSet) {
                return new ArrayList<>();
            } else {
                List<String> list = new ArrayList<>();
                for (String uid : uidSet) {
                    if (filterUser(uid, user, dateStr)) {
                        list.add(uid);
                    }
                }
                return list;
            }
        } catch (Exception e) {
            logger.info("getMatchSendMsgPerson error msg={}", e.getMessage());
            return new ArrayList<>();
        }
    }

    private int getMatchReplyMsgPerson(String dateStr, Integer user, Integer gender, Integer app, String versioncodeStr) {
        try {
            Set<String> uidSet = redisTemplate.opsForSet().members("match:msg:back:posb:" + dateStr);
            if (null == uidSet) {
                return 0;
            }
            List<String> list = new ArrayList<>();
            for (String uid : uidSet) {
                uid = uid.split("-")[0];
                if (filterUser(uid, user, dateStr)) {
                    list.add(uid);
                }
            }
            List<Integer> versionCodeList = parseVersionCode(versioncodeStr);
            return filterUser(list, gender, app, versionCodeList).size();
        } catch (Exception e) {
            logger.info("getMatchReplyMsgPerson error msg={}", e.getMessage());
            return 0;
        }
    }

    private List<String> getRealReplyMsgPerson(String dateStr, Integer user) {
        try {
            Set<String> uidSet = redisTemplate.opsForSet().members("match:msg:back:ared:" + dateStr);
            if (null == uidSet) {
                return new ArrayList<>();
            }
            List<String> list = new ArrayList<>();
            for (String uid : uidSet) {
                uid = uid.split("-")[0];
                if (filterUser(uid, user, dateStr)) {
                    list.add(uid);
                }
            }
            return list;
        } catch (Exception e) {
            logger.info("getRealReplyMsgPerson error msg={}", e.getMessage());
            return new ArrayList<>();
        }
    }

    private Set<String> getPtgReplayNewUsers(String dateStr) {
        Set<String> uidSet = redisTemplate.opsForSet().members("match:msg:back:ared:" + dateStr);
        if (null == uidSet) {
            return new HashSet<>();
        }
        Set<String> newUidSet = new HashSet<>();
        for (String uidStr : uidSet) {
            String uid = uidStr.split("-")[0];
            String aid = uidStr.split("-")[1];
            if (partyGirlRedis.isPartyGirlByRedis(uid) && filterUser(aid, 1, dateStr)) {
                newUidSet.add(aid);
            } else if (partyGirlRedis.isPartyGirlByRedis(aid) && filterUser(uid, 1, dateStr)) {
                newUidSet.add(uid);
            }
        }
        if (CollectionUtils.isEmpty(newUidSet)) {
            return newUidSet;
        }
        newUidSet = actorDao.filterActorByUidSet("1", newUidSet);
        return newUidSet;
    }

    public List<CrushRetentionVO> crushRetention(Integer startTime, Integer endTime, Integer gender, Integer user, Integer app, String versionCode) {
        return Collections.emptyList();
    }

    /**
     * 1、安卓主包: Youstar-android
     * 2、安卓PRO包: Youstar-pro
     * 3、IOS包: Youstar-ios
     * 4、安卓主包和IOS包: Youstar
     */
    private String getAppPackage(Integer app) {
        if (AppPackageConstant.YOUSTAR_ANDROID == app) {
            return "in.dradhanus.liveher";
        } else if (AppPackageConstant.YOUSTAR_PRO == app) {
            return "com.youstar.android.lite";
        } else if (AppPackageConstant.YOUSTAR_IOS == app) {
            return "com.stonemobile.youstar";
        } else if (AppPackageConstant.YOUSTAR == app) {
            return "in.dradhanus.liveher,com.stonemobile.youstar";
        }
        return null;
    }

    /**
     * Crush功能留存率与产品留存率数据概况
     *
     * @param event  右滑1、超级喜欢2、匹配成功3、发消息4、回复消息5
     * @param count  event对应选择的次数
     * @param gender 男1、女2、PartyGirl3、-1全部
     * @param user   新1、老2、-1全部
     * @param app    1Youstar、2Youstar Pro、-1全部
     * @param hours
     */
    public List<CrushDetailVO> crushDetail(Integer startTime, Integer endTime, Integer event, int count, Integer gender, Integer user, Integer app, Integer remainType, Integer hours) {
        return Collections.emptyList();
    }

    private Set<String> getDayRemainPerson(Set<String> personSet, int day, String dateStr, Integer event, int count, Integer gender, Integer user, Integer app) {
        if (CollectionUtils.isEmpty(personSet)) {
            return new HashSet<>();
        }
        String dayDate = LocalDate.parse(dateStr).plusDays(day).toString();
        String today = com.quhong.core.utils.DateHelper.ARABIAN.formatDateInDay();
        if (today.compareTo(dayDate) < 0) {
            return new HashSet<>();
        }
        List<String> dayPersonList = reportsServer.getCrushUidByEvent(dayDate, event, user, gender, app, "");
        dayPersonList = filterPersonByCount(dayPersonList, count);
        if (CollectionUtils.isEmpty(dayPersonList)) {
            return new HashSet<>();
        }
        Set<String> uidSet = new HashSet<>();
        for (String uid : dayPersonList) {
            if (personSet.contains(uid)) {
                uidSet.add(uid);
            }
        }
        return uidSet;
    }

    private List<String> filterPersonByCount(List<String> personList, int count) {
        if (-1 == count || CollectionUtils.isEmpty(personList)) {
            return personList;
        }
        Map<String, Integer> countMap = new HashMap<>();
        for (String uid : personList) {
            countMap.compute(uid, (k, v) -> {
                if (null == v) {
                    v = 0;
                }
                return v + 1;
            });
        }
        personList = new ArrayList<>();
        for (String uid : countMap.keySet()) {
            if (countMap.get(uid) >= count) {
                personList.add(uid);
            }
        }
        return personList;
    }

    /**
     * @param event 右滑(喜欢)1、超级喜欢2、匹配成功3、发消息4、回复消息5、不喜欢6
     */
    public List<String> getCrushUidByEvent(String dateStr, Integer event, Integer user, Integer gender, Integer app, String versioncode) {
        logger.info("getCrushUidByEvent dateStr={} event={} user={} gender={} app={} versioncode={}", dateStr, event, user, gender, app, versioncode);
        return Collections.emptyList();
    }

    private List<Integer> parseVersionCode(String versioncode) {
        List<Integer> versionCodeList = null;
        if (!StringUtils.isEmpty(versioncode)) {
            versionCodeList = new ArrayList<>();
            String[] split = versioncode.split(",");
            for (String version : split) {
                versionCodeList.add(Integer.parseInt(version));
            }
        }
        return versionCodeList;
    }

    // 过滤uid列表
    private List<String> filterUser(List<String> uidList, int gender, int app, List<Integer> versionCode) {
        if (-1 == gender && -1 == app && CollectionUtils.isEmpty(versionCode)) {
            return uidList;
        }
        Set<String> unqualifiedUser = new HashSet<>(uidList);
        unqualifiedUser.removeAll(actorDao.filterActors(new HashSet<>(uidList), gender, getAppPackage(app), -1, versionCode));
        // 剔除不符合条件的用户
        uidList.removeAll(unqualifiedUser);
        return uidList;
    }

    public List<CrushDashboardVO> crushDashboard(Integer startTime, Integer endTime, Integer event, Integer gender, Integer user, Integer app) {
        return Collections.emptyList();
    }

    private int getMatchSendMsgCount(String dateStr, Integer user) {
        int count = 0;
        try {
            ZSetOperations<String, String> zSet = redisTemplate.opsForZSet();
            Set<ZSetOperations.TypedTuple<String>> tuples = zSet.rangeWithScores("match:msg:send:" + dateStr, 0, 9999);
            if (null == tuples || tuples.isEmpty()) {
                return count;
            }
            for (ZSetOperations.TypedTuple<String> tuple : tuples) {
                if (filterUser(tuple.getValue(), user, dateStr)) {
                    if (null != tuple.getScore()) {
                        count = count + tuple.getScore().intValue();
                    }
                }
            }
            return count;
        } catch (Exception e) {
            logger.info("getMatchSendMsgCount error", e);
            return count;
        }
    }

    public List<FriendshipVO> friendship(Integer startTime, Integer endTime, Integer user) {
        List<FriendshipVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            FriendshipVO vo = new FriendshipVO();
            vo.setDate(dateStr);
            list.add(vo);
        }
        return list;
    }

    public List<RelationshipNewVO> relationshipNew(Integer startTime, Integer endTime) {
        return Collections.emptyList();
    }

    public List<RelationshipReturnVO> relationshipReturn(Integer startTime, Integer endTime) {
        return Collections.emptyList();
    }

    public MsgGiftVO getDayMsgGift(DayTimeData dayTimeData, int os, int isNew) {
        MsgGiftVO msgGiftVO = new MsgGiftVO();
        msgGiftVO.setDate(dayTimeData.getDate());
        Integer osType = null;
        Integer isNewType = null;
        if (os != -1) {
            osType = os;
        }
        if (isNew != -1) {
            isNewType = isNew;
        }
        //set dau
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dayTimeData.getDate());
        ApiResult<Set<String>> dauOnlineActor = dauDao.getActiveActorByWhere(time[0], --time[1], osType, isNewType, null, -1);
        Set<String> dauUidSet = dauOnlineActor.getData();
        if (CollectionUtils.isEmpty(dauUidSet)) {
            return msgGiftVO;
        }
        int dau = dauUidSet.size();
        msgGiftVO.setDau(dau);
        //set 新增用户数
        List<Actor> newActors = actorDao.totalNewActorList(dayTimeData.getTime(), dayTimeData.getEndTime(), os, -1);
        int addUserNum = newActors.size();
        msgGiftVO.setAddUserNum(addUserNum);
        //set 发送私信礼物相关数据
        List<MysqlMsgRecordData> beanGiftMsgList = msgRecordDao.listMsgRecord(dayTimeData.getTime() * 1000L, dayTimeData.getEndTime() * 1000L, MsgType.GIFT, dauUidSet);
        List<MysqlMsgRecordData> heartGiftMsgList = msgRecordDao.listMsgRecord(dayTimeData.getTime() * 1000L, dayTimeData.getEndTime() * 1000L, MsgType.HEART_GIFT, dauUidSet);
        List<MysqlMsgRecordData> totalGiftMsgList = new ArrayList<>();
        totalGiftMsgList.addAll(beanGiftMsgList);
        totalGiftMsgList.addAll(heartGiftMsgList);
        Set<String> totalGiftUidSet = new HashSet<>();
        for (MysqlMsgRecordData mysqlMsgRecordData : totalGiftMsgList) {
            totalGiftUidSet.add(mysqlMsgRecordData.getFromUid());
        }
        int totalGiftUserNum = totalGiftUidSet.size();
        int totalGiftCount = totalGiftMsgList.size();
        msgGiftVO.setSendMsgUserNum(totalGiftUserNum);
        msgGiftVO.setSendMsgCount(totalGiftCount);
        //set 心心礼物相关数据
        Set<String> heartUidSet = new HashSet<>();
        int heartNum = 0;
        for (MysqlMsgRecordData mysqlMsgRecordData : heartGiftMsgList) {
            heartUidSet.add(mysqlMsgRecordData.getFromUid());
            String msgInfo = mysqlMsgRecordData.getMsgInfo();
            JSONObject info = JSON.parseObject(msgInfo);
            heartNum = heartNum + info.getInteger("sendNum");
        }
        int heartUserNum = heartUidSet.size();
        msgGiftVO.setSendHeartUserNum(heartUserNum);
        msgGiftVO.setSendHeartCount(heartGiftMsgList.size());
        msgGiftVO.setSendHeartNum(heartNum);
        //set 普通钻石礼物
        List<GiftData> giftList = giftDao.listMsgGiftByType(1);
        Map<Integer, Integer> giftMap = new HashMap<>();
        for (GiftData giftData : giftList) {
            Integer rid = giftData.getRid();
            giftMap.put(rid, giftData.getPrice());
        }
        Set<String> beanUidSet = new HashSet<>();
        int giftBeans = 0;
        for (MysqlMsgRecordData mysqlMsgRecordData : beanGiftMsgList) {
            beanUidSet.add(mysqlMsgRecordData.getFromUid());
            String msgInfo = mysqlMsgRecordData.getMsgInfo();
            JSONObject info = JSON.parseObject(msgInfo);
            Integer sendNum = info.getInteger("sendNum");
            Integer giftId = info.getInteger("giftId");
            Integer price = giftMap.get(giftId);
            if (price == null) {
                continue;
            }
            int sendBean = sendNum * price;
            giftBeans = giftBeans + sendBean;
        }
        msgGiftVO.setSendBeanUserNum(beanUidSet.size());
        msgGiftVO.setSendBeanNum(beanGiftMsgList.size());
        msgGiftVO.setSendBeanNum(giftBeans);
        //平台消耗钻石总数
        int costBeans = moneyDetailStatDao.getCostBeans(dayTimeData.getTime(), dayTimeData.getEndTime(), null, null);
        msgGiftVO.setTotalCostBean(costBeans);
        return msgGiftVO;
    }

    public PageResultVO<FeedBackReportVO> getFeedbackReport(String start, String end, int page, int status, String username, int replyStatus) {
        Integer[] timeList = DateHelper.ARABIAN.getStartOrEndSeconds(start, end);
        PageResultVO<FeedBackReportVO> pageResultVO = new PageResultVO<>();
        int startTime = timeList[0];
        int endTime = timeList[1];
        List<String> reportIds = null;
        List<FeedbackHandleData> handleList = null;
        boolean fromHandle = false;
        if ((status != -1 && status != 0) || !StringUtils.isEmpty(username) || (replyStatus != -1 && replyStatus != 0)) {
            //查询处理记录
            fromHandle = true;
            if (page != -1) {
                PageResultVO<FeedbackHandleData> handlePage = feedbackHandleDao.pageHandle(startTime, endTime, username, status, replyStatus, page, 10);
                handleList = handlePage.getList();
                if (CollectionUtils.isEmpty(handleList)) {
                    return pageResultVO;
                }
                pageResultVO.setTotal(handlePage.getTotal());
            } else {
                handleList = feedbackHandleDao.listHandle(startTime, endTime, username, status, replyStatus);
                if (CollectionUtils.isEmpty(handleList)) {
                    return pageResultVO;
                }
                pageResultVO.setTotal(handleList.size());
            }
        } else {
            //直接查询report表
            if (page != -1) {
                reportIds = reportLogStatDao.listReportId(startTime, endTime, page);
                if (CollectionUtils.isEmpty(reportIds)) {
                    return pageResultVO;
                }
                PageInfo<String> pageInfo = new PageInfo<>(reportIds);
                pageResultVO.setTotal(pageInfo.getTotal());
            }
        }
        if (fromHandle) {
            List<FeedBackReportVO> feedBackReportVOS = fillFeedbackReportVO(null, handleList);
            for (FeedBackReportVO feedBackReportVO : feedBackReportVOS) {
                String reportId = feedBackReportVO.getReportId();
                List<ReportLogData> logList = reportLogStatDao.listReport(reportId);
                getFBKReportVOByReports(feedBackReportVO, logList);
            }
            pageResultVO.setList(feedBackReportVOS);
        } else {
            List<ReportLogData> reportList = reportLogStatDao.listReport(startTime, endTime, reportIds);
            List<FeedBackReportVO> feedBackReportVOS = fillFeedbackReportVO(reportList);
            handleList = feedbackHandleDao.listHandle(reportIds);
            feedBackReportVOS = fillFeedbackReportVO(feedBackReportVOS, handleList);
            pageResultVO.setList(feedBackReportVOS);
        }
        return pageResultVO;
    }

    public void updateFeedBackHandle(int status, String username, String remark, String reportId, int replyStatus) {
        FeedbackHandleData handleData = feedbackHandleDao.selectHandle(reportId);
        Manager manager = managerDao.getManager(username);
        if (manager == null) {
            logger.error("manager is empty. username={}", username);
            return;
        }
        if (handleData == null) {
            int currentTime = com.quhong.core.utils.DateHelper.getNowSeconds();
            handleData = new FeedbackHandleData();
            handleData.set_id(new ObjectId());
            handleData.setCtime(currentTime);
            handleData.setMtime(currentTime);
            handleData.setRemark(remark);
            handleData.setReplyStatus(replyStatus);
            handleData.setReportId(reportId);
            handleData.setStatus(status);
            handleData.setUid(manager.get_id().toString());
            handleData.setUsername(manager.getAccount());
            feedbackHandleDao.saveOne(handleData);
        } else {
            handleData.setRemark(remark);
            handleData.setReplyStatus(replyStatus);
            handleData.setReportId(reportId);
            handleData.setStatus(status);
            handleData.setUid(manager.get_id().toString());
            handleData.setUsername(manager.getAccount());
            feedbackHandleDao.updateById(handleData, handleData.get_id().toString());
        }
    }

    private List<FeedBackReportVO> fillFeedbackReportVO(List<FeedBackReportVO> feedBackReportVOS, List<FeedbackHandleData> handleList) {
        if (CollectionUtils.isEmpty(handleList)) {
            return feedBackReportVOS;
        }
        if (feedBackReportVOS == null) {
            feedBackReportVOS = new ArrayList<>();
            for (FeedbackHandleData feedbackHandleData : handleList) {
                FeedBackReportVO feedBackReportVO = new FeedBackReportVO();
                feedBackReportVO.setReportId(feedbackHandleData.getReportId());
                feedBackReportVO.setStatus(feedbackHandleData.getStatus());
                feedBackReportVO.setManageUser(feedbackHandleData.getUsername());
                feedBackReportVO.setRemark(feedbackHandleData.getRemark());
                feedBackReportVO.setReplayStatus(feedbackHandleData.getReplyStatus());
                feedBackReportVOS.add(feedBackReportVO);
            }
            return feedBackReportVOS;
        }
        Map<String, FeedbackHandleData> handleMap = new HashMap<>();
        for (FeedbackHandleData feedbackHandleData : handleList) {
            handleMap.put(feedbackHandleData.getReportId(), feedbackHandleData);
        }
        for (FeedBackReportVO feedBackReportVO : feedBackReportVOS) {
            FeedbackHandleData feedbackHandleData = handleMap.get(feedBackReportVO.getReportId());
            if (feedbackHandleData != null) {
                feedBackReportVO.setStatus(feedbackHandleData.getStatus());
                feedBackReportVO.setManageUser(feedbackHandleData.getUsername());
                feedBackReportVO.setRemark(feedbackHandleData.getRemark());
                feedBackReportVO.setReplayStatus(feedbackHandleData.getReplyStatus());
            }
        }
        return feedBackReportVOS;
    }

    private List<FeedBackReportVO> fillFeedbackReportVO(List<ReportLogData> reportList) {
        Map<String, List<ReportLogData>> reportMap = new HashMap<>();
        for (ReportLogData reportLogData : reportList) {
            String reportId = reportLogData.getReportId();
            List<ReportLogData> list = reportMap.get(reportId);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(reportLogData);
            reportMap.put(reportId, list);
        }
        List<FeedBackReportVO> voList = new ArrayList<>();
        for (String key : reportMap.keySet()) {
            List<ReportLogData> logList = reportMap.get(key);
            FeedBackReportVO feedBackReportVO = getFBKReportVOByReports(null, logList);
            voList.add(feedBackReportVO);
        }
        return voList;
    }

    private FeedBackReportVO getFBKReportVOByReports(FeedBackReportVO feedBackReportVO, List<ReportLogData> logList) {
        if (feedBackReportVO == null) {
            feedBackReportVO = new FeedBackReportVO();
        }
        if (CollectionUtils.isEmpty(logList)) {
            return feedBackReportVO;
        }
        List<String> urls = new ArrayList<>();
        for (ReportLogData reportLogData : logList) {
            int contentType = reportLogData.getContentType();
            if (contentType == ReportContentType.TEXT) {
                feedBackReportVO.setContent(reportLogData.getContent());
                String dateTime = com.quhong.core.utils.DateHelper.ARABIAN.formatDateTime(new Date(reportLogData.getCtime() * 1000L));
                feedBackReportVO.setCommitTime(dateTime);
                feedBackReportVO.setCreateTime(dateTime);
                feedBackReportVO.setInformation(reportLogData.getInformation());
                feedBackReportVO.setError(reportLogData.getError());
                feedBackReportVO.setReportId(reportLogData.getReportId());

                String reportUid = reportLogData.getUid();

                if (reportUid.length() == 24) {
                    ActorData actorData = actorCoreDao.getActorDataFromCache(reportUid);
                    feedBackReportVO.setReportRid(actorData == null ? "" : String.valueOf(actorData.getRid()));
                    feedBackReportVO.setUid(reportUid);
                    feedBackReportVO.setRegisterTime(new ObjectId(reportUid).getTimestamp());
                    feedBackReportVO.setRechargeMoney(actorPayExternalDao.getUserRechargeMoney(reportUid));
                }
                String osName = "Android";
                int os = reportLogData.getOs();
                if (os == ClientOS.IOS) {
                    osName = "ios";
                }
                feedBackReportVO.setOs(osName);
            }
            if (contentType == ReportContentType.IMG) {
                if (!StringUtils.isEmpty(reportLogData.getContent())) {
                    urls.add(reportLogData.getContent());
                }
            }
        }
        feedBackReportVO.setUrls(urls);
        return feedBackReportVO;
    }

    public NoviceReportVO getNoviceReport(DayTimeData dayTimeData, int os, int app, int isNew) {
        return new NoviceReportVO();
    }

    private Set<String> filterUsers(int os, int app, int isNew, String date, Set<String> uidSet) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return uidSet;
        }
        if (isNew == 1) {
            Set<String> newUidSet = new HashSet<>();
            for (String uid : uidSet) {
                if (filterUser(uid, isNew, date)) {
                    newUidSet.add(uid);
                }
            }
            if (CollectionUtils.isEmpty(newUidSet)) {
                return new HashSet<>();
            }
            return actorDao.filterActors(newUidSet, -1, getAppPackage(app), os, null);
        }
        return actorDao.filterActors(uidSet, -1, getAppPackage(app), os, null);
    }

    //    @PostConstruct
    private void downloadLoginExcel() {
        String date = "2021-10-25";
        DayTimeData daytimeData = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(date);
        List<String> uidList = dauDao.totalCount(daytimeData.getTime(), daytimeData.getEndTime(), null, null, null, -1);
        List<ActorLoginVO> loginList = new ArrayList<>();
        for (String uid : uidList) {
            Actor actor = actorDao.getActor(uid);
            ActorLoginVO loginVO = new ActorLoginVO();
            loginVO.setRid(actor.getRid());
            loginVO.setName(actor.getName());
            loginVO.setBeans(actor.getBeans());
            loginList.add(loginVO);
        }
        //下载报表
        ExcelUtils.exportExcel(loginList, ActorLoginVO.class, "login_" + date + ".xlsx", "user");
    }

    public List<StaffWelcomeVO> staffWelcome(Integer startTime, Integer endTime) {
        List<StaffWelcomeVO> result = new ArrayList<>();
        return result;
    }

    private static String getStatTime(int second) {
        if (second <= 0) {
            return "0";
        }
        float minute = second / 60f;
        DecimalFormat df = new DecimalFormat("##0.00");
        if (minute >= 60) {
            float hour = minute / 60;
            return df.format(hour) + "小时";
        } else {
            return df.format(minute) + "分钟";
        }
    }

    public JSONObject apiReportList(Integer startTime, Integer endTime, Integer rid, Integer type, Integer os, String name, Integer page, Integer pageSize) {
        JSONObject jsonObject = new JSONObject();
        List<ApiReportListVO> result = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        String uid = null;
        if (null != rid && 0 != rid) {
            ApiResult<Actor> actor = actorDao.getActorByRid(rid);
            if (actor.isOK()) {
                uid = actor.getData().get_id().toString();
            }
        }
        for (String dateStr : dateArr) {
            List<ApiReportListVO> statReportList = reportsServer.getStatReportList(dateStr, uid, type, os, name, page, pageSize);
            jsonObject.put("total", reportsServer.getStatReportListTotal(dateStr, uid, type, os, name));
            // 获取上一天的数据
            String yesterdayStr = com.quhong.core.utils.DateHelper.ARABIAN.getYesterdayStr(DateHelper.ARABIAN.stringToDate(dateStr));
            List<ApiReportListVO> yesterdayReportList = reportsServer.getStatReportList(yesterdayStr, uid, type, os, name, page, pageSize);
            Map<String, ApiReportListVO> map = CollectionUtil.listToKeyMap(yesterdayReportList, ApiReportListVO::getName);
            for (ApiReportListVO vo : statReportList) {
                ApiReportListVO reportListVO = map.getOrDefault(vo.getName(), null);
                if (null != reportListVO) {
                    vo.setChange(getRate((new BigDecimal(vo.getRespTime()).intValue() - new BigDecimal(reportListVO.getRespTime()).intValue()), new BigDecimal(vo.getRespTime()).intValue()));
                }
                // todo 转换场景
                vo.setScene(vo.getName());
                if (2 == type) {
                    vo.setScene(getMarsSceneName(vo.getName()));
                }
            }
            // 仅返回第一天数据
            jsonObject.put("result", statReportList);
            return jsonObject;
        }
        jsonObject.put("result", result);
        return jsonObject;
    }

    private String getMarsSceneName(String name) {
        if (name.contains("18.196.57.207")) {
            return "法兰克福-AWS";
        } else if (name.contains("13.248.153.103")) {
            return "泛播(GA)01-AWS";
        } else if (name.contains("15.184.30.63")) {
            return "巴林-AWS";
        } else if (name.contains("3.109.170.149")) {
            return "孟买(服务器01直连)-AWS";
        } else if (name.contains("3.6.233.113")) {
            return "孟买(服务器02直连)-AWS";
        } else if (name.contains("47.112.131.236")) {
            return "香港阿里云国内代理-ALI";
        } else if (name.contains("47.91.115.39")) {
            return "迪拜-ALI";
        } else if (name.contains("47.91.105.228")) {
            return "中东-FPA";
        } else if (name.contains("154.57.32.31")) {
            return "北非-FPA";
        }
        return name;
    }

    public List<ApiReportListVO> getStatReportList(String dateStr, String uid, Integer type, Integer os, String name, Integer page, Integer pageSize) {
        String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(dateStr));
        if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
            return reportSlaveMapper.getStatReportList(tableSuffix, uid, type, os, name, (page - 1) * pageSize, pageSize);
        }
        return new ArrayList<>();
    }

    public int getStatReportListTotal(String dateStr, String uid, Integer type, Integer os, String name) {
        String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(dateStr));
        if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
            return reportSlaveMapper.getStatReportListTotal(tableSuffix, uid, type, os, name);
        }
        return 0;
    }

    public ApiReportStatVO apiReportStat(Integer startTime, Integer endTime, Integer rid, Integer type, Integer os, String name, String countryCode) {
        ApiReportStatVO result = new ApiReportStatVO();
        List<ReportCountryVO> countryList = new ArrayList<>();
        List<ReportStatVO> statList = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        String uid = null;
        if (null != rid && 0 != rid) {
            ApiResult<Actor> actor = actorDao.getActorByRid(rid);
            if (actor.isOK()) {
                uid = actor.getData().get_id().toString();
            }
        }
        // 只有1天的数据时显示当天的实施数据
        if (dateArr.length == 1) {
            String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(dateArr[0]));
            if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
                statList = reportSlaveMapper.statInDay(tableSuffix, uid, type, os, name, countryCode);
                countryList = reportSlaveMapper.statCountry(tableSuffix, uid, type, os, name, countryCode);
            }
            fillPercentage(countryList);
        } else {
            for (String date : dateArr) {
                // 每天统计
                String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(date));
                ReportStatVO reportStatVO = new ReportStatVO();
                if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
                    reportStatVO = reportSlaveMapper.statByDay(tableSuffix, uid, type, os, name, countryCode);
                    if (null == reportStatVO) {
                        reportStatVO = new ReportStatVO();
                    }
                }
                reportStatVO.setStatTime(date);
                statList.add(reportStatVO);
            }
            if (dateArr.length > 0) {
                String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(dateArr[0]));
                if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
                    countryList = reportSlaveMapper.statCountry(tableSuffix, uid, type, os, name, countryCode);
                }
            }
            fillPercentage(countryList);
        }
        result.setStatList(statList);
        result.setCountryList(countryList);
        return result;
    }

    private void fillPercentage(List<ReportCountryVO> countryList) {
        int sum = countryList.stream().mapToInt(ReportCountryVO::getResultCount).sum();
        for (ReportCountryVO vo : countryList) {
            vo.setPercentage(getRate(vo.getResultCount(), sum).replace("%", ""));
            // 处理转换国家名字
            vo.setCountry(CountryCodeToName.MAP.getOrDefault(vo.getCountryCode(), vo.getCountryCode()));
            try {
                vo.setRespTime(new BigDecimal(vo.getRespTime()).intValue() + "");
            } catch (Exception ignored) {
            }
        }
    }

    @Resource
    private RoomMsgMapper roomMsgMapper;
    @Resource
    private ActorDao coreActorDao;

    public List<RoomMsgVO> roomMsgReports(int start, int end, String roomId) {
        List<RoomMsgVO> voList = new ArrayList<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(start, end);
        List<RoomMsg> roomMsgList = roomMsgMapper.selectByRoomId(tableSuffixArr.get(0), start, end, roomId);
        for (RoomMsg roomMsg : roomMsgList) {
            RoomMsgVO vo = new RoomMsgVO();
            ActorData actorData = coreActorDao.getActorDataFromCache(roomMsg.getFromUid());
            if (null == actorData) {
                logger.error("cannot find actor data uid={}", roomMsg.getFromUid());
                continue;
            }
            String dateTime = DateHelper.ARABIAN.datetimeToStr(new Date(roomMsg.getCtime() * 1000L));
            int rid = actorData.getRid();
            Integer atRid = null;
            String content = roomMsg.getMsgBody();
            if (roomMsg.getMsyType() == 222) {
                try {
                    JSONObject jsonObject = JSONObject.parseObject(roomMsg.getMsgBody());
                    String aid = jsonObject.getString("aid");
                    content = jsonObject.getString("content");
                    ActorData atActorData = coreActorDao.getActorDataFromCache(aid);
                    if (null == atActorData) {
                        logger.error("cannot find actor data uid={}", roomMsg.getFromUid());
                        continue;
                    }
                    atRid = atActorData.getRid();
                } catch (Exception e) {
                    logger.error("parse aid error, error msg={}", e.getMessage());
                }
            }
            vo.setDateTime(dateTime);
            vo.setRid(rid);
            vo.setAtRid(atRid);
            vo.setContent(content);
            voList.add(vo);
        }
        return voList;
    }

    @Resource
    private RechargeRedis rechargeRedis;

    public List<EnterRoomUserVO> enterRoomReports(int start, int end, String roomId) {
        List<EnterRoomUserVO> voList = new ArrayList<>();
        List<EnterRoom> enterRoomList = enterRoomDao.selectByRoomId(start, end, roomId);
        for (EnterRoom enterRoom : enterRoomList) {
            EnterRoomUserVO vo = new EnterRoomUserVO();
            ActorData actorData = coreActorDao.getActorDataFromCache(enterRoom.getUserId());
            if (null == actorData) {
                logger.error("cannot find actor data uid={}", enterRoom.getUserId());
                continue;
            }
            String dateTime = DateHelper.ARABIAN.datetimeToStr(new Date(enterRoom.getCtime() * 1000L));
            int rid = actorData.getRid();
            vo.setDateTime(dateTime);
            vo.setRid(rid);
            vo.setRmbUser(rechargeRedis.isRechargeUser(enterRoom.getUserId()));
            voList.add(vo);
        }
        return voList;
    }

    public JSONObject detectUserRecord(String rid, Integer detectType, Integer page, Integer pageSize) {
        JSONObject jsonObject = new JSONObject();

        List<DetectUserRecordVO> list = new ArrayList<>();
        String queryUid = null;
        if (!StringUtils.isEmpty(rid)) {
            ActorData actorData = actorCoreDao.getActorByRid(Integer.parseInt(rid));
            if (actorData != null) {
                queryUid = actorData.getUid();
            }
        }

        List<DetectUserRecordData> detectUserRecordDataList = detectUserRecordDao.getRecords(queryUid, detectType, page, pageSize);
        for (DetectUserRecordData recordData : detectUserRecordDataList) {
            DetectUserRecordVO vo = new DetectUserRecordVO();
            BeanUtils.copyProperties(recordData, vo);
            String recordUid = recordData.getUid();
            ActorData actorData = actorCoreDao.getActorDataFromCache(recordUid);
            vo.setRid(actorData.getRid());
            list.add(vo);
        }
        jsonObject.put("list", list);
        jsonObject.put("total", detectUserRecordDao.getTotalRecords(queryUid, detectType));

        return jsonObject;
    }

    public List<DetectUserRecordVO> detectUserRecordReports(String rid, Integer detectType) {
        List<DetectUserRecordVO> list = new ArrayList<>();
        String queryUid = null;
        if (!StringUtils.isEmpty(rid)) {
            ActorData actorData = actorCoreDao.getActorByRid(Integer.parseInt(rid));
            if (actorData != null) {
                queryUid = actorData.getUid();
            }
        }

        List<DetectUserRecordData> detectUserRecordDataList = detectUserRecordDao.getReportRecords(queryUid, detectType);
        for (DetectUserRecordData recordData : detectUserRecordDataList) {
            DetectUserRecordVO vo = new DetectUserRecordVO();
            BeanUtils.copyProperties(recordData, vo);
            String recordUid = recordData.getUid();
            ActorData actorData = actorCoreDao.getActorDataFromCache(recordUid);
            vo.setRid(actorData.getRid());
            if (detectType == 0) {
                vo.setStatus(-1);
            }
            list.add(vo);
        }
        return list;
    }

    public List<ThirdDetectLogData> thirdDetectLogReports(Integer startTime, Integer endTime) {
        return thirdDetectLogDao.getThirdLog(startTime, endTime, 1);
    }

    /**
     * 数美对比信息
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 报表数据
     */
    public List<CompereShuMeiTnInfoVO> shuMeiTnCompareInfo(Integer startTime, Integer endTime, Integer riskType
            , String searchKey, Integer page, Integer dTime) {
        page = page == null || page < 1 ? 1 : page;
        riskType = riskType == null ? 1 : riskType;

        List<CompereShuMeiTnInfoVO> list = new ArrayList<>();
        List<ShuMeiDeviceAccountData> accountDataList = shuMeiDeviceAccountDao.
                selectAll(startTime, endTime, page, 3000, searchKey);
        if (!CollectionUtils.isEmpty(accountDataList)) {
            for (ShuMeiDeviceAccountData item : accountDataList) {
                String jsonText = item.getShuMeiText();
                String riskListText = item.getTnRisk();
                String shuMeiId = item.getShuMeiId();
                String tnId = item.getTnId();
                Integer mtime = item.getMtime();

                SMOpDeviceRespondsData shuMeiData = JSON.parseObject(jsonText, SMOpDeviceRespondsData.class);
                List<Integer> riskList = JSON.parseArray(riskListText, Integer.class);
                if (shuMeiData == null || shuMeiData.getDeviceLabels() == null || mtime == null) {
                    continue;
                }

                String newShuMeiRisk = "";
                if (riskType == 1) {
                    if (!isTnRisk(riskList)) {
                        // 没有图灵风险
                        continue;
                    }
                    newShuMeiRisk = getNewShuMeiRisk(shuMeiData, dTime, mtime);
                } else {
                    newShuMeiRisk = getNewShuMeiRisk(shuMeiData, dTime, mtime);
                    if (StringUtils.isEmpty(newShuMeiRisk)) {
                        // 没有数美风险
                        continue;
                    }
                }

                CompereShuMeiTnInfoVO itemVO = new CompereShuMeiTnInfoVO();
                itemVO.setId(String.valueOf(item.getRid()));
                itemVO.setShuMeiReqId(shuMeiData.getRequestId());
                itemVO.setShuMeiId(shuMeiId);
                itemVO.setTnId(tnId);
                itemVO.setShuMeiRisk(newShuMeiRisk);
                itemVO.setTnRisk(riskListText);
                itemVO.setCtime(mtime);
                list.add(itemVO);
            }
        }
        return list;
    }

    private boolean isNowField(long shuMeiTime, Integer mTime, Integer dTime, long last_active_ts) {
        int stime = (int) (shuMeiTime / 1000);
        int lastTs = (int) (last_active_ts / 1000);
        if (Math.abs(lastTs - stime) < 5) {
            return true;
        } else {
            logger.info("old data field gt:5s stime:{} lastTs:{}", stime, lastTs);
        }
        return false;
    }

    private String getNewShuMeiRisk(SMOpDeviceRespondsData shuMeiData, Integer dTime, Integer mTime) {
        SMOpDeviceRespondsData.DeviceSuspiciousLabels deviceSuspiciousLabels = shuMeiData.getDeviceLabels().getDevice_suspicious_labels();
        SMOpDeviceRespondsData.FakeDevice fakeDevice = shuMeiData.getDeviceLabels().getFake_device();
        long last_active_ts = shuMeiData.getDeviceLabels().getLast_active_ts();

        int lastTs = (int) (last_active_ts / 1000);
        if (Math.abs(mTime - lastTs) >= dTime) {
            logger.info("old data mTime:{} lastTs:{}", mTime, lastTs);
            return "";
        }

        StringBuilder builder = new StringBuilder();
        if (deviceSuspiciousLabels.getB_acc() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_acc_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_acc");
            }
        }
        if (deviceSuspiciousLabels.getB_adb_enable() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_adb_enable_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_adb_enable");
            }
        }
        if (deviceSuspiciousLabels.getB_alter_apps() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_alter_apps_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_alter_apps");
            }
        }
        if (deviceSuspiciousLabels.getB_alter_loc() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_alter_loc_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_alter_loc");
            }
        }
        if (deviceSuspiciousLabels.getB_console() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_console_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_console");
            }
        }
        if (deviceSuspiciousLabels.getB_debuggable() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_debuggable_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_debuggable");
            }
        }
        if (deviceSuspiciousLabels.getB_device_proxy() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_device_proxy_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_device_proxy");
            }
        }
        if (deviceSuspiciousLabels.getB_hook() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_hook_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_hook");
            }
        }
        if (deviceSuspiciousLabels.getB_manufacture() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_manufacture_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_manufacture");
            }
        }
        if (deviceSuspiciousLabels.getB_monkey_apps() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_monkey_apps_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_monkey_apps");
            }
        }
        if (deviceSuspiciousLabels.getB_multi_boxing_apps() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_multi_boxing_apps_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_multi_boxing_apps");
            }
        }
        if (deviceSuspiciousLabels.getB_remote_control_apps() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_remote_control_apps_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_remote_control_apps");
            }
        }
        if (deviceSuspiciousLabels.getB_repackage() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_repackage_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_repackage");
            }
        }
        if (deviceSuspiciousLabels.getB_reset() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_reset_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_reset");
            }
        }
        if (deviceSuspiciousLabels.getB_root() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_root_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_root");
            }
        }
        if (deviceSuspiciousLabels.getB_sim() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_sim_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_sim");
            }
        }
        if (deviceSuspiciousLabels.getB_vpn() == 1) {
            if (isNowField(deviceSuspiciousLabels.getB_vpn_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_vpn");
            }
        }


        if (fakeDevice.getB_altered() == 1) {
            if (isNowField(fakeDevice.getB_altered_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_altered");
            }
        }
        if (fakeDevice.getB_cloud_device() == 1) {
            if (isNowField(fakeDevice.getB_cloud_device_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_cloud_device");
            }
        }
        if (fakeDevice.getB_faker() == 1) {
            if (isNowField(fakeDevice.getB_faker_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_faker");
            }
        }
        if (fakeDevice.getB_farmer() == 1) {
            if (isNowField(fakeDevice.getB_farmer_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_farmer");
            }
        }
        if (fakeDevice.getB_multi_boxing() == 1) {
            if (isNowField(fakeDevice.getB_multi_boxing_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_multi_boxing");
            }
        }
        if (fakeDevice.getB_multi_boxing_by_app() == 1) {
            if (isNowField(fakeDevice.getB_multi_boxing_by_app_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_multi_boxing_by_app");
            }
        }
        if (fakeDevice.getB_multi_boxing_by_os() == 1) {
            if (isNowField(fakeDevice.getB_multi_boxing_by_os_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_multi_boxing_by_os");
            }
        }
        if (fakeDevice.getB_offerwall() == 1) {
            if (isNowField(fakeDevice.getB_offerwall_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_offerwall");
            }
        }
        if (fakeDevice.getB_pc_emulator() == 1) {
            if (isNowField(fakeDevice.getB_pc_emulator_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_pc_emulator");
            }
        }
        if (fakeDevice.getB_phone_emulator() == 1) {
            if (isNowField(fakeDevice.getB_phone_emulator_last_ts(), dTime, mTime, last_active_ts)) {
                append(builder, "b_phone_emulator");
            }
        }

        return builder.toString();

    }

    private StringBuilder append(StringBuilder builder, String key) {
        return builder.append(key).append(",");
    }

    private boolean isTnRisk(List<Integer> fromRiskList) {
//        logger.info("fromRiskList:{}", fromRiskList);
        if (CollectionUtils.isEmpty(fromRiskList)) {
            return false;
        }
        for (Integer item : fromRiskList) {
            if (DEVICE_EMULATOR_TYPE.contains(item)) {
                return true;
            }
        }
        return false;
    }


    public List<UserResRecordVO> userResRecordReports(UserResCondition condition) {
        String queryUid = null;
        int queryResId;
        String queryStrRid = condition.getStrRid();

        if (!StringUtils.isEmpty(queryStrRid)) {
            ActorData actorData = actorCoreDao.getActorByStrRid(queryStrRid);
            if (actorData != null) {
                queryUid = actorData.getUid();
            }
        }

        queryResId = condition.getResId() != null ? condition.getResId() : 0;

        if (StringUtils.isEmpty(queryUid) && queryResId <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "查询用户id和资源id至少输入一项");
        }

        return resourceConfigService.userResRecordReports(condition.getResType(), queryUid, queryResId, condition.getFeature());
    }

    public HttpResult<Object> detectImgPass(Integer id, Integer ctime) {
        HttpResult<Object> result = new HttpResult<>();
        if (id == null || id <= 0 || ctime == null || ctime <= 0) {
            return result.error("param error");
        }
        DetectUserRecordData recordData = detectUserRecordDao.selectById(id, ctime);
        if (recordData == null || recordData.getDetectType() != 1) {
            return result.error("param error");
        }
        if (recordData.getStatus() != null && recordData.getStatus() == 1) {
            return result.error("已放行，请勿重复操作");
        }
        String imgUrl = recordData.getDetectInfo();
        String urlMD5 = DigestUtils.md5DigestAsHex(imgUrl.getBytes(StandardCharsets.UTF_8));
        DiscernImageData discernImageData = discernImageDao.getDiscernImageData(urlMD5);
        if (discernImageData == null) {
            return result.error("操作失败，请联系开发人员");
        }
        discernImageData.setIsSafe(1);
        discernImageDao.update(discernImageData);
        recordData.setStatus(1);
        detectUserRecordDao.update(recordData);
        return result.ok();
    }

    /**
     * 保存或更新投放消耗记录
     *
     * @param record 记录
     * @return 是否成功
     */
    public boolean saveOrUpdatePutInConsumeRecord(PutInConsumeRecord record, String opUser) {

        PutInConsumeRecord existingRecord = putInConsumeRecordDao.selectByUniqueKey(record.getMedium(), record.getCampaign(),
                record.getPkgOs(), record.getDate());
        boolean success = false;
        if (existingRecord != null) {
            // 更新现有记录
            record.setId(existingRecord.getId());
            success = putInConsumeRecordDao.updateByUniqueKey(record);
            doUpdateAdCostPutInEvent(record, opUser);
        } else {
            // 插入新记录
            success = putInConsumeRecordDao.insertRecord(record);
            doAdCostPutInEvent(record, opUser);
        }
        return success;
    }

    /**
     * 分页查询投放消耗记录
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param page      页码
     * @param pageSize  页大小
     * @return 分页结果
     */
    public PageResultVO<PutInConsumeRecordVO> getPutInConsumeRecordPage(String startDate, String endDate, Integer page, Integer pageSize) {
        PageResultVO<PutInConsumeRecordVO> pageVO = new PageResultVO<>();
        try {
            // 参数校验
            if (page == null || page < 1) {
                page = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }

            // 设置时间范围
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
            int startTime = timeArr[0];
            int endTime = timeArr[1];

            List<PutInConsumeRecord> records = putInConsumeRecordDao.selectByPage(startTime, endTime, page, pageSize);
            int total = putInConsumeRecordDao.countByTimeRange(startTime, endTime);

            List<PutInConsumeRecordVO> voList = new ArrayList<>();
            for (PutInConsumeRecord record : records) {
                PutInConsumeRecordVO vo = convertToVO(record);
                voList.add(vo);
            }
            pageVO.setList(voList);
            pageVO.setTotal(total);

        } catch (Exception e) {
            logger.error("getPutInConsumeRecordPage error. startTime={}, endTime={}, page={}, pageSize={}, error={}",
                    startDate, endDate, page, pageSize, e.getMessage(), e);
            pageVO.setList(new ArrayList<>());
            pageVO.setTotal(0);
        }
        return pageVO;
    }


    /**
     * 查询所有投放消耗记录（用于导出）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 记录列表
     */
    public List<PutInConsumeRecordVO> getAllPutInConsumeRecords(Integer startTime, Integer endTime) {
        try {
            List<PutInConsumeRecord> records = putInConsumeRecordDao.selectAllByTimeRange(startTime, endTime);
            List<PutInConsumeRecordVO> voList = new ArrayList<>();
            for (PutInConsumeRecord record : records) {
                PutInConsumeRecordVO vo = convertToVO(record);
                voList.add(vo);
            }
            return voList;
        } catch (Exception e) {
            logger.error("getAllPutInConsumeRecords error. startTime={}, endTime={}, error={}",
                    startTime, endTime, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 将PutInConsumeRecord转换为PutInConsumeRecordVO
     *
     * @param record 数据库记录
     * @return VO对象
     */
    private PutInConsumeRecordVO convertToVO(PutInConsumeRecord record) {
        PutInConsumeRecordVO vo = new PutInConsumeRecordVO();
        vo.setId(record.getId());
        vo.setMedium(record.getMedium());
        vo.setCampaign(record.getCampaign());
        vo.setAdCostMoney(record.getAdCostMoney());
        vo.setPkgOs(record.getPkgOs());
        vo.setCtime(record.getCtime());
        vo.setMtime(record.getMtime());
        vo.setDate(record.getDate());

        // 转换时间戳为字符串
        if (record.getCtime() != null) {
            vo.setCtimeStr(DateHelper.ARABIAN.dateToStr(new Date(record.getCtime() * 1000L)));
        }
        if (record.getMtime() != null) {
            vo.setMtimeStr(DateHelper.ARABIAN.dateToStr(new Date(record.getMtime() * 1000L)));
        }

        return vo;
    }

    private void doAdCostPutInEvent(PutInConsumeRecord record, String opUser) {
        AdCostPutInEvent event = new AdCostPutInEvent();
        event.setUid(event.getEventName());
        event.setCtime(record.getCtime());
        event.setAd_cost_money(record.getAdCostMoney());
        event.setMedium(record.getMedium());
        event.setCampaign(record.getCampaign());
        event.setPkg_os(record.getPkgOs());
        event.setDate(record.getDate());
        event.setTime_zone("+8");
        event.setOperator(opUser);
        EventDTO eventDTO = new EventDTO(event);
        String key = String.format("%s-%s-%s-%s", record.getDate(), record.getPkgOs(), record.getMedium(), record.getCampaign());
        eventDTO.setEventId(key);
        eventReport.track(eventDTO);
    }


    private void doUpdateAdCostPutInEvent(PutInConsumeRecord record, String opUser) {
        AdCostPutInEvent eventProperties = new AdCostPutInEvent();
        EventDTO eventDTO = new EventDTO(eventProperties.getEventName(), eventProperties.getEventName());
        String key = String.format("%s-%s-%s-%s", record.getDate(), record.getPkgOs(), record.getMedium(), record.getCampaign());
        eventDTO.setEventId(key);
        eventDTO.addProperties("ad_cost_money", record.getAdCostMoney());
        eventDTO.addProperties("operator", opUser);
        eventReport.trackUpdate(eventDTO);
    }

}
