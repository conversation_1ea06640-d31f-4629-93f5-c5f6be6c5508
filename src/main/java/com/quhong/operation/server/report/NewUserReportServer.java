package com.quhong.operation.server.report;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.datas.DayTimeData;
import com.quhong.operation.constant.MoneyDetailATypeConsts;
import com.quhong.operation.dao.*;
import com.quhong.operation.server.EnterRoomServer;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.CrushDetailVO;
import com.quhong.operation.share.vo.GiftAndPlayVO;
import com.quhong.operation.share.vo.HeartRecordStatVO;
import com.quhong.operation.share.vo.RoomCorePlayVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.MathUtils;
import com.quhong.operation.utils.PropertiesUtils;
import com.quhong.redis.NewRookieRoomRedis;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;


@Service
public class NewUserReportServer {

    private final static Logger logger = LoggerFactory.getLogger(NewUserReportServer.class);

    public static final int ALL = -1;
    public static final int ENTER_ROOM = 1; // 进迎新房新用户
    public static final int MIC_UP = 2; // 迎新房上麦新用户
    public static final int SEND_MSG = 3; // 迎新房发言新用户
    public static final int SEND_GIFT = 4; // 迎新房送迎新礼物的新用户
    public static final int ROOM_TIME_1B = 50; // < 1min
    public static final int ROOM_TIME_1 = 51; // 1-2
    public static final int ROOM_TIME_2 = 52; // 2-3
    public static final int ROOM_TIME_3 = 53; // 3-4
    public static final int ROOM_TIME_4 = 54; // 4-5
    public static final int ROOM_TIME_5 = 55; // 5-10 min
    public static final int ROOM_TIME_10A = 56; // >10 min
    public static final int MIC_TIME_1B = 60;
    public static final int MIC_TIME_1 = 61;
    public static final int MIC_TIME_2 = 62;
    public static final int MIC_TIME_3 = 63;
    public static final int MIC_TIME_4 = 64;
    public static final int MIC_TIME_5 = 65;
    public static final int MIC_TIME_10A = 66;

    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private AdminDauDao dauDao;
    @Autowired
    private EnterRoomServer enterRoomServer;
    @Autowired
    private MoneyDetailStatDao moneyDetailStatDao;
    @Autowired
    private HeartRecordOpDao heartRecordOpDao;
    @Resource
    private NewUserReportServer newUserReportServer;
    @Resource
    private ReportsServer reportsServer;
    @Autowired
    private EnterRoomDao enterRoomDao;
    @Autowired
    private NewRookieRoomRedis newRookieRoomRedis;
    @Autowired
    private RoomMicStatDao roomMicStatDao;
    @Autowired
    private RoomMsgDao roomMsgDao;
    @Autowired
    private GiftSendRecordDao giftSendRecordDao;
    @Autowired
    private RoomUserOnlineSlaveDao roomUserOnlineSlaveDao;

    /**
     * 获取一段时间内的礼物+充值报表数据
     *
     * @param startTime 开始时间
     * @param endTime   结尾时
     * @param gender    用户性别，1男，2女，null为所有用户
     * @return 一段时间内的礼物+充值报表数据
     */
    @Cacheable(value = "reports3", key = "targetClass + methodName + #p0 + #p1 + #p2 + #p3 + #p4",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<GiftAndPlayVO> getGiftAndPayList(Integer startTime, Integer endTime, int gender, int os, int app) {
        logger.info("getGiftAndPayList param s={}, e={}", startTime, endTime);
        List<GiftAndPlayVO> lists = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            lists.add(newUserReportServer.statGiftAndPayList(dateStr, gender, os, app));
        }
        logger.info("method getGiftAndPayList done");
        return lists;
    }

    @Cacheable(value = "reports3", key = "targetClass + methodName + #p0 + #p1 + #p2 + #p3",
            condition = "T(com.quhong.core.utils.DateHelper).DEFAULT.formatDateInDay().compareTo(#dateStr)>0", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public GiftAndPlayVO statGiftAndPayList(String dateStr, int gender, int os, int app) {
        GiftAndPlayVO vo = new GiftAndPlayVO();
        Set<String> uidSet = null;
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
        vo.setDate(dateStr); // 日期
        if (2 == gender) {
            uidSet = getUidSetByGender(time[0], time[1], gender, os);
            vo.setFemaleUserCount(uidSet.size() + ""); // 女用户数
        }
        Set<String> newUserUidSet = actorDao.totalNewActorUidSet(time[0], --time[1], os, 2 == gender ? gender : -1, app, null);
        Set<String> huwWeiUidSet = actorDao.huaWeiNewActorUidSet(time[0], time[1], os, 2 == gender ? gender : -1);
        vo.setNewHuaweiCount(huwWeiUidSet.size() + "");
        vo.setNewUserCount(newUserUidSet.size() + ""); // 新增用户数(新增女用户数)
        if (2 == gender) {
            newUserUidSet = uidSet;
        }
        //List<Integer> roomPersonCount = enterRoomServer.actorJoinRoomPersonCount(time[0], time[1], newUserUidSet);
        List<String> personList = reportsServer.getCrushUidByEvent(dateStr, ReportsServer.ENTER_ROOM, 1, gender, app, "");
        int enterRoomPerson = new HashSet<>(personList).size();
        int enterRoomCount = personList.size();
        vo.setEnterRoomPerson(enterRoomPerson + ""); // 进房人数
        vo.setEnterRoomCount(enterRoomCount + ""); // 进房次数
        if (enterRoomPerson > 0 && enterRoomCount > 0) {
            vo.setPerCapitaEnterRoom(enterRoomCount / enterRoomPerson + ""); // 人均进房次数
        } else {
            vo.setPerCapitaEnterRoom("0");
        }

        HeartRecordStatVO heartRecordStatVO = heartRecordOpDao.heartRecordStatMap(time[0], time[1], newUserUidSet);
        vo.setHeartReceivePerson(heartRecordStatVO.getReceivePerson() + ""); // 心心领取人数
        vo.setHeartReceiveCount(heartRecordStatVO.getReceiveCount() + ""); // 心心领取次数
        vo.setHeartSendPerson(heartRecordStatVO.getSendPerson() + ""); // 心心发送人数
        vo.setHeartSendCount(heartRecordStatVO.getSendCount() + ""); // 心心发送次数

        Set<Integer> aTypeSet = new HashSet<>(Arrays.asList(
                MoneyDetailATypeConsts.GET_LUCKY_BOX, MoneyDetailATypeConsts.SEND_GIFT,
                MoneyDetailATypeConsts.CHARGE_VIP, MoneyDetailATypeConsts.PAY_CHARGE,
                MoneyDetailATypeConsts.HUAWEI_PAY_CHARGE));
        Map<Integer, TotalVO> totalVOMap = moneyDetailStatDao.statByATypeSet(dateStr, aTypeSet, newUserUidSet);

        TotalVO total = totalVOMap.get(MoneyDetailATypeConsts.GET_LUCKY_BOX);
        vo.setGetLuckyBoxPerson(total.getPersonNum() + ""); // 红包领取人数
        vo.setGetLuckyBoxCount(total.getCountNum() + ""); // 红包领取次数
        vo.setGetLuckyBoxSum(total.getSumNum().abs() + ""); // 红包领取钻石数

        total = totalVOMap.get(MoneyDetailATypeConsts.SEND_GIFT);
        vo.setGiftSendPerson(total.getPersonNum() + ""); // 礼物发送人数
        vo.setGiftSendCount(total.getCountNum() + ""); // 礼物发送次数
        vo.setGiftSendSum(total.getSumNum().abs() + ""); // 礼物消耗钻石数

        total = totalVOMap.get(MoneyDetailATypeConsts.CHARGE_VIP);
        vo.setChargeVipPerson(total.getPersonNum() + ""); // 购买VIP人数

        total = totalVOMap.get(MoneyDetailATypeConsts.HUAWEI_PAY_CHARGE);
        vo.setHuaweiChargePerson(total.getPersonNum() + ""); // 华为充值人数
        vo.setHuaweiChargeCount(total.getCountNum() + ""); // 华为充值次数
        vo.setHuaweiChargeSum(total.getSumNum().abs() + ""); // 华为充值金额

        total = totalVOMap.get(MoneyDetailATypeConsts.PAY_CHARGE);
        vo.setChargePerson(total.getPersonNum() + ""); // 充值人数
        vo.setChargeCount(total.getCountNum() + ""); // 充值次数
        vo.setChargeSum(total.getSumNum().abs() + ""); // 充值金额
        return vo;
    }

    /**
     * 获取房间核心玩法报表数据
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param gender    用户性别，1男，2女，null为所有用户
     * @return 一段时间内的获取房间核心玩法报表数据
     */
    @Cacheable(value = "reports3", key = "targetClass + methodName + #p0 + #p1 + #p2 + #p3 + #p4",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<RoomCorePlayVO> getRoomCorePlayList(Integer startTime, Integer endTime, int gender, int os, int app) {
        logger.info("getRoomCorePlayList param s={}, e={}", startTime, endTime);
        List<RoomCorePlayVO> lists = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            lists.add(newUserReportServer.statRoomCorePlayList(dateStr, gender, os, app));
        }
        logger.info("method getRoomCorePlayList done");
        return lists;
    }

    @Cacheable(value = "reports3", key = "targetClass + methodName + #p0 + #p1 + #p2 + #p3",
            condition = "T(com.quhong.core.utils.DateHelper).DEFAULT.formatDateInDay().compareTo(#dateStr)>0", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public RoomCorePlayVO statRoomCorePlayList(String dateStr, int gender, int os, int app) {
        TotalVO total;
        Set<String> uidSet;
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
        RoomCorePlayVO vo = new RoomCorePlayVO();
        vo.setDate(dateStr); // 日期
        if (2 == gender) {
            uidSet = getUidSetByGender(time[0], time[1], gender, os);
        } else {
            uidSet = actorDao.totalNewActorUidSet(time[0], --time[1], os, -1, app, null);
        }
        vo.setUserCount(uidSet.size() + ""); // 新增用户数（女用户数）
        //List<Integer> roomPersonCount = enterRoomServer.actorJoinRoomPersonCount(time[0], time[1], uidSet);
        List<String> personList = reportsServer.getCrushUidByEvent(dateStr, ReportsServer.ENTER_ROOM, 1, gender, app, "");
        int enterRoomPerson = new HashSet<>(personList).size();
        int enterRoomCount = personList.size();
        vo.setEnterRoomPerson(enterRoomPerson + ""); // 进房人数
        vo.setEnterRoomCount(enterRoomCount + ""); // 进房次数
        if (enterRoomPerson > 0 && enterRoomCount > 0) {
            vo.setPerCapitaEnterRoom(enterRoomCount / enterRoomPerson + ""); // 人均进房次数
        } else {
            vo.setPerCapitaEnterRoom("0");
        }
        Set<Integer> aTypeSet = new HashSet<>(Arrays.asList(
                MoneyDetailATypeConsts.PLAY_TURNTABLE_GAME, MoneyDetailATypeConsts.FINGER_GUESSING_GAME,
                MoneyDetailATypeConsts.PLAY_DICE_GAME, MoneyDetailATypeConsts.PLAY_NEW_DICE_GAME,
                MoneyDetailATypeConsts.SEND_LUCK_BOX, MoneyDetailATypeConsts.GET_LUCKY_BOX,
                MoneyDetailATypeConsts.PLAY_PK_GAME, MoneyDetailATypeConsts.PLAY_SUDOKU_GAME,
                MoneyDetailATypeConsts.PLAY_LUCK_CARD_GAME, MoneyDetailATypeConsts.PLAY_ROOM_PK_GAME));
        Map<Integer, TotalVO> totalVOMap = moneyDetailStatDao.statByATypeSet(dateStr, aTypeSet, uidSet);

        total = totalVOMap.get(MoneyDetailATypeConsts.PLAY_TURNTABLE_GAME);
        vo.setTurntableGamePerson(total.getPersonNum() + ""); // 幸运转盘参与人数
        vo.setTurntableGameCount(total.getCountNum() + ""); // 幸运转盘参与次数
        vo.setTurntableGameSum(total.getSumNum().abs() + ""); // 幸运转盘消耗钻石数

        total = totalVOMap.get(MoneyDetailATypeConsts.FINGER_GUESSING_GAME);
        vo.setFingerGuessingPerson(total.getPersonNum() + ""); // 参与猜拳人数
        vo.setFingerGuessingCount(total.getCountNum() + ""); // 参与猜拳次数
        vo.setFingerGuessingSum(total.getSumNum().abs() + ""); // 猜拳消耗钻石数

        total = totalVOMap.get(MoneyDetailATypeConsts.PLAY_DICE_GAME);
        vo.setDiceGamePerson(total.getPersonNum() + ""); // 玩幸运数字人数
        vo.setDiceGameCount(total.getCountNum() + ""); // 玩幸运数字次数

        total = totalVOMap.get(MoneyDetailATypeConsts.PLAY_NEW_DICE_GAME);
        vo.setNewDiceGamePerson(total.getPersonNum() + ""); // 玩骰子人数
        vo.setNewDiceGameCount(total.getCountNum() + ""); // 玩骰子次数

        total = totalVOMap.get(MoneyDetailATypeConsts.SEND_LUCK_BOX);
        vo.setSendLuckyBoxPerson(total.getPersonNum() + ""); // 红包发送人数
        vo.setSendLuckyBoxCount(total.getCountNum() + ""); // 红包发送次数

        total = totalVOMap.get(MoneyDetailATypeConsts.GET_LUCKY_BOX);
        vo.setGetLuckyBoxPerson(total.getPersonNum() + ""); // 红包领取人数
        vo.setGetLuckyBoxCount(total.getCountNum() + ""); // 红包领取次数

        total = totalVOMap.get(MoneyDetailATypeConsts.PLAY_PK_GAME);
        TotalVO roomPkTotalVO = totalVOMap.get(MoneyDetailATypeConsts.PLAY_ROOM_PK_GAME);
        vo.setPkGamePerson(total.getPersonNum() + roomPkTotalVO.getPersonNum() + ""); // 参与PK人数
        vo.setPkGameCount(total.getCountNum() + roomPkTotalVO.getCountNum() + ""); // 参与PK次数

        total = totalVOMap.get(MoneyDetailATypeConsts.PLAY_SUDOKU_GAME);
        vo.setSudokuGamePerson(total.getPersonNum() + ""); // 玩九宫格抽奖人数
        vo.setSudokuGameCount(total.getCountNum() + ""); // 玩九宫格抽奖次数
        vo.setSudokuGameSum(total.getSumNum().abs() + ""); // 九宫格消耗钻石数

        total = totalVOMap.get(MoneyDetailATypeConsts.PLAY_LUCK_CARD_GAME);
        vo.setLuckCardPerson(total.getPersonNum() + ""); // 玩幸运卡牌人数
        vo.setLuckCardCount(total.getCountNum() + ""); // 玩幸运卡牌次数
        vo.setLuckCardSum(total.getSumNum().abs() + ""); // 幸运卡牌消耗钻石数
        return vo;
    }

    /**
     * 获取一段时间内的登录的所有用户uid
     *
     * @param startTime 开始时间
     * @param endTime   结尾人数
     * @param gender    性别，1男，2女
     * @param os        0安卓，1苹果，-1全部
     * @return uidSet
     */
    public Set<String> getUidSetByGender(Integer startTime, Integer endTime, int gender, int os) {
        List<String> uidList = dauDao.getUidListByGender(startTime, endTime, gender, os);
        return new HashSet<>(uidList);
    }

    /**
     * @param event
     * @param count  event对应选择的次数
     * @param app    1Youstar、2Youstar Pro、-1全部
     * @param gender 男1、女2、PartyGirl3、-1全部
     * @param user   新1、老2、-1全部
     */
    public List<CrushDetailVO> newRookieRoomDetail(Integer startTime, Integer endTime, Integer event, int count, Integer gender, Integer user, Integer app, String rookieRoomId) {
        List<CrushDetailVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            list.add(newUserReportServer.getRookieRoomDetail(dateStr, event, count, gender, user, app, rookieRoomId));
        }
        return list;
    }

    public CrushDetailVO getRookieRoomDetail(String dateStr, Integer event, int count, Integer gender, Integer user, Integer app, String rookieRoomId) {
        CrushDetailVO vo = new CrushDetailVO();
        vo.setDate(dateStr);
        Set<String> rookieRooms;
        if (StringUtils.hasLength(rookieRoomId)) {
            rookieRooms = new HashSet<>();
            rookieRooms.add(rookieRoomId);
        } else {
            rookieRooms = newRookieRoomRedis.getNewRookieRoomByRedis();
        }
        if (CollectionUtils.isEmpty(rookieRooms)) {
            vo.setPerson(0);
            return vo;
        }
        List<String> personList = newUserReportServer.getRookieRoomByEvent(dateStr, event, user, gender, app, "", rookieRooms);
//        personList = filterPersonByCount(personList, count);
        Set<String> personSet = new HashSet<>(personList);
        vo.setPerson(personSet.size());
        if (CollectionUtils.isEmpty(personSet)) {
            return vo;
        }
        int day1Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(1).toString(), personSet);
        vo.setDay1Num(day1Num);
        vo.setDay1(MathUtils.getTwoRate(day1Num, personSet.size()));
        int day2Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(2).toString(), personSet);
        vo.setDay2Num(day2Num);
        vo.setDay2(MathUtils.getTwoRate(day2Num, personSet.size()));
        int day3Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(3).toString(), personSet);
        vo.setDay3Num(day3Num);
        vo.setDay3(MathUtils.getTwoRate(day3Num, personSet.size()));
        int day4Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(4).toString(), personSet);
        vo.setDay4Num(day4Num);
        vo.setDay4(MathUtils.getTwoRate(day4Num, personSet.size()));
        int day5Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(5).toString(), personSet);
        vo.setDay5Num(day5Num);
        vo.setDay5(MathUtils.getTwoRate(day5Num, personSet.size()));
        int day6Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(6).toString(), personSet);
        vo.setDay6Num(day6Num);
        vo.setDay6(MathUtils.getTwoRate(day6Num, personSet.size()));
        int day7Num = dauDao.getDauCount(LocalDate.parse(dateStr).plusDays(7).toString(), personSet);
        vo.setDay7Num(day7Num);
        vo.setDay7(MathUtils.getTwoRate(day7Num, personSet.size()));
        return vo;
    }

    /**
     * @param event
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2 + #p3 + #p4 + #p5 + #p6.hashCode() + 'd'",
            condition = "T(com.quhong.core.utils.DateHelper).DEFAULT.formatDateInDay().compareTo(#dateStr)>0", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<String> getRookieRoomByEvent(String dateStr, Integer event, Integer user, Integer gender, Integer app, String versioncode, Set<String> rookieRooms) {
        logger.info("getRookieRoomByEvent dateStr={} event={} user={} gender={} app={} versioncode={}", dateStr, event, user, gender, app, versioncode);
        List<Integer> versionCodeList = PropertiesUtils.parseVersionCode(versioncode);
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
        List<String> uidList = new ArrayList<>();
        DayTimeData dayTimeData = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(dateStr);

        switch (event) {
            case ENTER_ROOM:
                Set<String> enterRoomSet = enterRoomDao.enterRookieRoomNewUsers(dayTimeData, rookieRooms);
                uidList = new ArrayList<>(enterRoomSet);
                break;
            case MIC_UP:
                Set<String> micRoomSet = roomMicStatDao.micRookieRoomNewUsers(dayTimeData, rookieRooms);
                uidList = new ArrayList<>(micRoomSet);
                break;
            case SEND_MSG:
                Set<String> msgRoomSet = roomMsgDao.msgRookieRoomNewUsers(dayTimeData, rookieRooms);
                uidList = new ArrayList<>(msgRoomSet);
                break;
            case SEND_GIFT:
                Set<String> giftRoomSet = giftSendRecordDao.giftRookieRoomNewUsers(dayTimeData, rookieRooms);
                uidList = new ArrayList<>(giftRoomSet);
                break;
            case ROOM_TIME_1B:
                uidList = roomUserOnlineSlaveDao.enterRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 0, 1);
                break;
            case ROOM_TIME_1:
                uidList = roomUserOnlineSlaveDao.enterRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 1, 2);
                break;
            case ROOM_TIME_2:
                uidList = roomUserOnlineSlaveDao.enterRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 2, 3);
                break;
            case ROOM_TIME_3:
                uidList = roomUserOnlineSlaveDao.enterRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 3, 4);
                break;
            case ROOM_TIME_4:
                uidList = roomUserOnlineSlaveDao.enterRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 4, 5);
                break;
            case ROOM_TIME_5:
                uidList = roomUserOnlineSlaveDao.enterRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 5, 10);
                break;
            case ROOM_TIME_10A:
                uidList = roomUserOnlineSlaveDao.enterRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 10, 2000);
                break;
            case MIC_TIME_1B:
                uidList = roomMicStatDao.micRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 0, 1);
                break;
            case MIC_TIME_1:
                uidList = roomMicStatDao.micRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 1, 2);
                break;
            case MIC_TIME_2:
                uidList = roomMicStatDao.micRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 2, 3);
                break;
            case MIC_TIME_3:
                uidList = roomMicStatDao.micRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 3, 4);
                break;
            case MIC_TIME_4:
                uidList = roomMicStatDao.micRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 4, 5);
                break;
            case MIC_TIME_5:
                uidList = roomMicStatDao.micRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 5, 10);
                break;
            case MIC_TIME_10A:
                uidList = roomMicStatDao.micRookieRoomNewUsersTime(dayTimeData.getTime(), dayTimeData.getEndTime(), rookieRooms, 10, 2000);
                break;
            default:
                break;
        }

        if (CollectionUtils.isEmpty(uidList)) {
            return new ArrayList<>();
        }
        List<String> newUidList = new ArrayList<>();
        for (String uid : uidList) {
            if (ActorUtils.isNewRegisterActor(uid, dateStr)) {
                newUidList.add(uid);
            }
        }
        return filterUser(newUidList, gender, app, versionCodeList);
    }


    // 过滤uid列表
    private List<String> filterUser(List<String> uidList, int gender, int app, List<Integer> versionCode) {
        if (-1 == gender && -1 == app && CollectionUtils.isEmpty(versionCode)) {
            return uidList;
        }
        Set<String> unqualifiedUser = new HashSet<>(uidList);
        unqualifiedUser.removeAll(actorDao.filterActors(new HashSet<>(uidList), gender, PropertiesUtils.getAppPackage(app), -1, versionCode));
        // 剔除不符合条件的用户
        uidList.removeAll(unqualifiedUser);
        return uidList;
    }

}
