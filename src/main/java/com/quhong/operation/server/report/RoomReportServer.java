package com.quhong.operation.server.report;

import com.quhong.datas.DayTimeData;
import com.quhong.operation.dao.DauStatDao;
import com.quhong.operation.dao.RoomReportLogDao;
import com.quhong.operation.dao.VideoStatDao;
import com.quhong.operation.share.data.MysqlStatData;
import com.quhong.operation.share.data.RoomReportStatData;
import com.quhong.operation.share.mysql.RoomReportLogData;
import com.quhong.operation.share.vo.reports.room.RoomVideoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RoomReportServer {

    private static final Logger logger = LoggerFactory.getLogger(RoomReportServer.class);

    @Autowired
    private DauStatDao dauStatDao;

    @Autowired
    private RoomReportLogDao roomReportLogDao;

    @Autowired
    private VideoStatDao videoStatDao;

    /**
     * 获取视屏房间上报数据
     *
     * @param dayTimeData
     * @return
     */
    public RoomVideoVO getRoomVideoReport(DayTimeData dayTimeData) {
        RoomVideoVO roomVideoVO = new RoomVideoVO();
        roomVideoVO.setDate(dayTimeData.getDate());
        int dau = dauStatDao.getDauByDay(-1, dayTimeData, null);
        roomVideoVO.setDau(dau);
        RoomReportStatData avgStatData = roomReportLogDao.getAvgStatData(dayTimeData, RoomReportLogData.ROOM_TYPE_VIDEO, 0);
        if (avgStatData != null) {
            Double roomAvgNum = avgStatData.getRoomAvgNum();
            roomVideoVO.setRoomAvgNum(roomAvgNum == null ? 0 : roomAvgNum.intValue());
            Double userAvgNum = avgStatData.getUserAvgNum();
            roomVideoVO.setRoomAvgOnlineUsers(userAvgNum == null ? 0 : userAvgNum.intValue());
        }
        MysqlStatData statData = videoStatDao.getStatData(dayTimeData);
        roomVideoVO.setNum(statData.getCountNum());
        roomVideoVO.setTime(statData.getSumNum());
        return roomVideoVO;
    }
}
