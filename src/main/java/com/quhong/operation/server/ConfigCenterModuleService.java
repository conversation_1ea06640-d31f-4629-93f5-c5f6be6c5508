package com.quhong.operation.server;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.ConfigCenterKeyDao;
import com.quhong.mysql.dao.ConfigCenterKeyLabelDao;
import com.quhong.mysql.dao.ConfigCenterLabelDao;
import com.quhong.mysql.dao.ConfigCenterValueDao;
import com.quhong.mysql.data.ConfigCenterKeyData;
import com.quhong.mysql.data.ConfigCenterKeyLabelData;
import com.quhong.mysql.data.ConfigCenterLabelData;
import com.quhong.mysql.data.ConfigCenterValueData;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.ConfigCenterCondition;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.ConfigCenterKeyLabelVO;
import com.quhong.operation.share.vo.ConfigCenterVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ConfigCenterModuleService {

    private static final Logger logger = LoggerFactory.getLogger(ConfigCenterModuleService.class);

    @Resource
    private ConfigCenterKeyDao configCenterKeyDao;
    @Resource
    private ConfigCenterValueDao configCenterValueDao;
    @Resource
    private ConfigCenterKeyLabelDao configCenterKeyLabelDao;
    @Resource
    private ConfigCenterLabelDao configCenterLabelDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ManagerDao managerDao;


    public PageResultVO<ConfigCenterVO> configCenterKeyList(BaseCondition condition) {
        PageResultVO<ConfigCenterVO> pageVO = new PageResultVO<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int status = condition.getStatus();
        String search = condition.getSearch();
        IPage<ConfigCenterKeyData> pageConfigKey = configCenterKeyDao.selectPageList(search, status, page, pageSize);
        List<ConfigCenterVO> voList = new ArrayList<>();

        for (ConfigCenterKeyData keyData : pageConfigKey.getRecords()) {
            ConfigCenterVO vo = new ConfigCenterVO();
            BeanUtils.copyProperties(keyData, vo);

            List<ConfigCenterLabelData> labelDataList = new ArrayList<>();
            List<ConfigCenterKeyLabelData> keyLabelDataList = configCenterKeyLabelDao.getKeyLabelListByConfigKey(keyData.getConfigKey());
            if(!keyLabelDataList.isEmpty()){
                List<String> keyLabelList = keyLabelDataList.stream().map(ConfigCenterKeyLabelData::getLabelKey).collect(Collectors.toList());
                labelDataList = configCenterLabelDao.getLabelListByLabelKey(keyLabelList);
            }
            vo.setLabelList(labelDataList);
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(pageConfigKey.getTotal());
        return pageVO;
    }


    public void addConfigCenterKeyData(String uid, ConfigCenterKeyData dto) {
        String configKey = dto.getConfigKey();
        ConfigCenterKeyData configCenterKeyData = configCenterKeyDao.getConfigCenterKeyByKey(configKey);
        if(configCenterKeyData != null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), String.format("该configKey: %s 已存在, 不能创建", configKey));
        }

        int currentTime = DateHelper.getNowSeconds();
        dto.setMtime(currentTime);
        dto.setCtime(currentTime);
        configCenterKeyDao.insert(dto);
    }


    public void updateConfigCenterKeyData(String uid, ConfigCenterKeyData dto) {
        String configKey = dto.getConfigKey();

        ConfigCenterKeyData data = configCenterKeyDao.getConfigCenterKeyById(dto.getId());
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String originData = JSONObject.toJSONString(data);

        BeanUtils.copyProperties(dto, data);
        int currentTime = DateHelper.getNowSeconds();
        data.setMtime(currentTime);
        configCenterKeyDao.update(data);
        if (dto.getWeChatSwitch() > 0){
            Manager manager = managerDao.getDataByUid(uid);
            String afterData = JSONObject.toJSONString(data);

            String warnDesc = String.format("【配置中心】用户修改配置key: %s", configKey);
            String warnDetail = String.format("操作者:%s \n 原KeyData: %s, \n 修改后KeyData: %s", manager.getAccount(), originData, afterData);
            logger.info("addConfigCenterValue: warnDesc:{}, warnDetail:{}", warnDesc, warnDetail);
            monitorSender.info("ustar_java_exception", warnDesc, warnDetail);
        }
    }

    public void deleteConfigCenterKeyData(String uid, ConfigCenterKeyData dto) {
        ConfigCenterKeyData data = configCenterKeyDao.getConfigCenterKeyById(dto.getId());
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String configKey = data.getConfigKey();
        String originData = JSONObject.toJSONString(data);
        configCenterKeyLabelDao.deleteByConfigKey(configKey);
        configCenterValueDao.deleteByConfigKey(configKey);
        configCenterKeyDao.delete(data.getId());
        Manager manager = managerDao.getDataByUid(uid);
        String warnDesc = String.format("【配置中心】用户删除配置key: %s", configKey);
        String warnDetail = String.format("操作者:%s \n 删除KeyData: %s", manager.getAccount(), originData);
        monitorSender.info("ustar_java_exception", warnDesc, warnDetail);
    }


    public PageResultVO<ConfigCenterValueData> configCenterValueList(ConfigCenterCondition condition) {
        PageResultVO<ConfigCenterValueData> pageVO = new PageResultVO<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int status = condition.getStatus();
        String configKey = condition.getConfigKey();
        String search = condition.getSearch();
        IPage<ConfigCenterValueData> pageGift = configCenterValueDao.selectPageList(configKey, search, status, page, pageSize);
        List<ConfigCenterValueData> voList = new ArrayList<>(pageGift.getRecords());
        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }

    public void addConfigCenterValueData(String uid, ConfigCenterValueData dto) {
        String configKey = dto.getConfigKey();
        ConfigCenterKeyData data = configCenterKeyDao.getConfigCenterKeyByKey(configKey);
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        int currentTime = DateHelper.getNowSeconds();
        dto.setMtime(currentTime);
        dto.setCtime(currentTime);
        configCenterValueDao.insert(dto);

        if (data.getWeChatSwitch() > 0){
            Manager manager = managerDao.getDataByUid(uid);
            String afterValueData = JSONObject.toJSONString(dto);
            String warnDesc = String.format("【配置中心】用户增加key: %s 的value值", configKey);
            String warnDetail = String.format("操作者:%s \n 新增valueData: %s", manager.getAccount(), afterValueData);
            logger.info("addConfigCenterValue: warnDesc:{}, warnDetail:{}", warnDesc, warnDetail);
            monitorSender.info("ustar_java_exception", warnDesc, warnDetail);
        }

    }

    public void updateConfigCenterValueData(String uid, ConfigCenterValueData dto) {
        String configKey = dto.getConfigKey();
        ConfigCenterKeyData data = configCenterKeyDao.getConfigCenterKeyByKey(configKey);
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ConfigCenterValueData valueData = configCenterValueDao.getConfigCenterKeyById(dto.getId());
        if(valueData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        String originValueData = JSONObject.toJSONString(valueData);
        int currentTime = DateHelper.getNowSeconds();
        BeanUtils.copyProperties(dto, valueData);
        valueData.setMtime(currentTime);
        configCenterValueDao.update(valueData);

        if (data.getWeChatSwitch() > 0){
            Manager manager = managerDao.getDataByUid(uid);
            String afterValueData = JSONObject.toJSONString(valueData);
            String warnDesc = String.format("【配置中心】用户修改key: %s 的value值", configKey);
            String warnDetail = String.format("操作者:%s \n 原valueData: %s, \n 修改后valueData: %s", manager.getAccount(), originValueData, afterValueData);
            logger.info("updateConfigCenterValue: warnDesc:{}, warnDetail:{}", warnDesc, warnDetail);
            monitorSender.info("ustar_java_exception", warnDesc, warnDetail);
        }
    }

    public void deleteConfigCenterValueData(String uid, ConfigCenterValueData dto) {

        String configKey = dto.getConfigKey();
        ConfigCenterKeyData data = configCenterKeyDao.getConfigCenterKeyByKey(configKey);
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ConfigCenterValueData valueData = configCenterValueDao.getConfigCenterKeyById(dto.getId());
        if(valueData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        configCenterValueDao.delete(valueData.getId());
        if (data.getWeChatSwitch() > 0){
            Manager manager = managerDao.getDataByUid(uid);
            String afterValueData = JSONObject.toJSONString(valueData);
            String warnDesc = String.format("【配置中心】用户删除key: %s 的value值", configKey);
            String warnDetail = String.format("操作者:%s \n 删除valueData: %s, ", manager.getAccount(), afterValueData);
            logger.info("updateConfigCenterValue: warnDesc:{}, warnDetail:{}", warnDesc, warnDetail);
            monitorSender.info("ustar_java_exception", warnDesc, warnDetail);
        }
    }


    public PageResultVO<ConfigCenterKeyLabelVO> configCenterLabelList(ConfigCenterCondition condition) {
        PageResultVO<ConfigCenterKeyLabelVO> pageVO = new PageResultVO<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        String search = condition.getSearch();
        String configKey = condition.getConfigKey();
        IPage<ConfigCenterLabelData> pageConfigCenterLabel = configCenterLabelDao.selectPageList(search, page, pageSize);
        List<ConfigCenterKeyLabelVO> voList = new ArrayList<>();
        List<String> labelKeyList = new ArrayList<>();
        if(!StringUtils.isEmpty(configKey)){
            List<ConfigCenterKeyLabelData> keyLabelDataList = configCenterKeyLabelDao.getKeyLabelListByConfigKey(configKey);
            labelKeyList = keyLabelDataList.stream().map(ConfigCenterKeyLabelData::getLabelKey).collect(Collectors.toList());
        }

        for (ConfigCenterLabelData labelData : pageConfigCenterLabel.getRecords()) {
            ConfigCenterKeyLabelVO vo = new ConfigCenterKeyLabelVO();
            BeanUtils.copyProperties(labelData, vo);
            if (!StringUtils.isEmpty(configKey) && labelKeyList.contains(labelData.getLabelKey())){
                vo.setChoose(1);
            }
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(pageConfigCenterLabel.getTotal());
        return pageVO;
    }

    public void addConfigCenterLabelData(String uid, ConfigCenterLabelData dto) {
        String labelKey = dto.getLabelKey();
        String labelName = dto.getLabelName();
        if(StringUtils.isEmpty(labelKey) || StringUtils.isEmpty(labelName)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ConfigCenterLabelData data = configCenterLabelDao.getConfigCenterLabelByLabelKey(labelKey);
        if(data != null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "labelKey 已存在");
        }

        int currentTime = DateHelper.getNowSeconds();
        dto.setMtime(currentTime);
        dto.setCtime(currentTime);
        configCenterLabelDao.insert(dto);

    }

    public void updateConfigCenterLabelData(String uid, ConfigCenterLabelData dto) {
        String labelKey = dto.getLabelKey();
        ConfigCenterLabelData data = configCenterLabelDao.getConfigCenterLabelByLabelKey(labelKey);
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        int currentTime = DateHelper.getNowSeconds();
        BeanUtils.copyProperties(dto, data);
        data.setMtime(currentTime);
        configCenterLabelDao.update(data);
    }



    // key-选择标签label
    public void addConfigCenterKeyLabelData(String uid, ConfigCenterKeyLabelData dto) {
        String labelKey = dto.getLabelKey();
        String configKey = dto.getConfigKey();
        if(StringUtils.isEmpty(labelKey) || StringUtils.isEmpty(configKey)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ConfigCenterKeyLabelData data = configCenterKeyLabelDao.getKeyLabelByConfigKeyLabelKey(configKey, labelKey);
        if(data != null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "已选择该标签");
        }

        int currentTime = DateHelper.getNowSeconds();
        dto.setMtime(currentTime);
        dto.setCtime(currentTime);
        configCenterKeyLabelDao.insert(dto);
    }


    public void deleteConfigCenterKeyLabelData(String uid, ConfigCenterKeyLabelData dto) {
        String labelKey = dto.getLabelKey();
        String configKey = dto.getConfigKey();
        if(StringUtils.isEmpty(labelKey) || StringUtils.isEmpty(configKey)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ConfigCenterKeyLabelData data = configCenterKeyLabelDao.getKeyLabelByConfigKeyLabelKey(configKey, labelKey);
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "该key-label标签不存在");
        }
        configCenterKeyLabelDao.deleteByConfigKeyLabelKey(configKey, labelKey);
    }

    public void deleteConfigCenterKeyLabelId(String uid, ConfigCenterKeyLabelData dto) {
        int keyLabelId = dto.getId();

        ConfigCenterKeyLabelData data = configCenterKeyLabelDao.getConfigCenterKeyLabelById(keyLabelId);
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "该key-label标签不存在");
        }
        configCenterKeyLabelDao.deleteById(keyLabelId);
    }


}
