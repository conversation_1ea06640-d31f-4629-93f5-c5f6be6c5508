package com.quhong.operation.constant;

import java.util.HashMap;
import java.util.Map;

public class CountryCodeToName {

    public static final Map<String, String> MAP = new HashMap<String, String>() {
        {
            put("AD",	"安道尔");
            put("AE",	"阿联酋");
            put("AF",	"阿富汗");
            put("AG",	"安提瓜和巴布达");
            put("AI",	"安圭拉");
            put("AL",	"阿尔巴尼亚");
            put("AM",	"亚美尼亚");
            put("AO",	"安哥拉");
            put("AQ",	"南极洲");
            put("AR",	"阿根廷");
            put("AS",	"美属萨摩亚");
            put("AT",	"奥地利");
            put("AU",	"澳大利亚");
            put("AW",	"阿鲁巴");
            put("AX",	"奥兰群岛");
            put("AZ",	"阿塞拜疆");
            put("BA",	"波黑");
            put("BB",	"巴巴多斯");
            put("BD",	"孟加拉");
            put("BE",	"比利时");
            put("BF",	"布基纳法索");
            put("BG",	"保加利亚");
            put("BH",	"巴林");
            put("BI",	"布隆迪");
            put("BJ",	"贝宁");
            put("BL",	"圣巴泰勒米岛");
            put("BM",	"百慕大");
            put("BN",	"文莱");
            put("BO",	"玻利维亚");
            put("BQ",	"荷兰加勒比区");
            put("BR",	"巴西");
            put("BS",	"巴哈马");
            put("BT",	"不丹");
            put("BV",	"布韦岛");
            put("BW",	"博茨瓦纳");
            put("BY",	"白俄罗斯");
            put("BZ",	"伯利兹");
            put("CA",	"加拿大");
            put("CC",	"科科斯群岛");
            put("CF",	"中非");
            put("CH",	"瑞士");
            put("CL",	"智利");
            put("CM",	"喀麦隆");
            put("CO",	"哥伦比亚");
            put("CR",	"哥斯达黎加");
            put("CU",	"古巴");
            put("CV",	"佛得角");
            put("CX",	"圣诞岛");
            put("CY",	"塞浦路斯");
            put("CZ",	"捷克");
            put("DE",	"德国");
            put("DJ",	"吉布提");
            put("DK",	"丹麦");
            put("DM",	"多米尼克");
            put("DO",	"多米尼加");
            put("DZ",	"阿尔及利亚");
            put("EC",	"厄瓜多尔");
            put("EE",	"爱沙尼亚");
            put("EG",	"埃及");
            put("EH",	"西撒哈拉");
            put("ER",	"厄立特里亚");
            put("ES",	"西班牙");
            put("FI",	"芬兰");
            put("FJ",	"斐济群岛");
            put("FK",	"马尔维纳斯群岛（福克兰）");
            put("FM",	"密克罗尼西亚联邦");
            put("FO",	"法罗群岛");
            put("FR",	"法国");
            put("GA",	"加蓬");
            put("GD",	"格林纳达");
            put("GE",	"格鲁吉亚");
            put("GF",	"法属圭亚那");
            put("GH",	"加纳");
            put("GI",	"直布罗陀");
            put("GL",	"格陵兰");
            put("GN",	"几内亚");
            put("GP",	"瓜德罗普");
            put("GQ",	"赤道几内亚");
            put("GR",	"希腊");
            put("GS",	"南乔治亚岛和南桑威奇群岛");
            put("GT",	"危地马拉");
            put("GU",	"关岛");
            put("GW",	"几内亚比绍");
            put("GY",	"圭亚那");
            put("HK",	"中国香港");
            put("HM",	"赫德岛和麦克唐纳群岛");
            put("HN",	"洪都拉斯");
            put("HR",	"克罗地亚");
            put("HT",	"海地");
            put("HU",	"匈牙利");
            put("ID",	"印尼");
            put("IE",	"爱尔兰");
            put("IL",	"以色列");
            put("IM",	"马恩岛");
            put("IN",	"印度");
            put("IO",	"英属印度洋领地");
            put("IQ",	"伊拉克");
            put("IR",	"伊朗");
            put("IS",	"冰岛");
            put("IT",	"意大利");
            put("JE",	"泽西岛");
            put("JM",	"牙买加");
            put("JO",	"约旦");
            put("JP",	"日本");
            put("KH",	"柬埔寨");
            put("KI",	"基里巴斯");
            put("KM",	"科摩罗");
            put("KW",	"科威特");
            put("KY",	"开曼群岛");
            put("LB",	"黎巴嫩");
            put("LI",	"列支敦士登");
            put("LK",	"斯里兰卡");
            put("LR",	"利比里亚");
            put("LS",	"莱索托");
            put("LT",	"立陶宛");
            put("LU",	"卢森堡");
            put("LV",	"拉脱维亚");
            put("LY",	"利比亚");
            put("MA",	"摩洛哥");
            put("MC",	"摩纳哥");
            put("MD",	"摩尔多瓦");
            put("ME",	"黑山");
            put("MF",	"法属圣马丁");
            put("MG",	"马达加斯加");
            put("MH",	"马绍尔群岛");
            put("MK",	"马其顿");
            put("ML",	"马里");
            put("MM",	"缅甸");
            put("MO",	"中国澳门");
            put("MQ",	"马提尼克");
            put("MR",	"毛里塔尼亚");
            put("MS",	"蒙塞拉特岛");
            put("MT",	"马耳他");
            put("MV",	"马尔代夫");
            put("MW",	"马拉维");
            put("MX",	"墨西哥");
            put("MY",	"马来西亚");
            put("NA",	"纳米比亚");
            put("NE",	"尼日尔");
            put("NF",	"诺福克岛");
            put("NG",	"尼日利亚");
            put("NI",	"尼加拉瓜");
            put("NL",	"荷兰");
            put("NO",	"挪威");
            put("NP",	"尼泊尔");
            put("NR",	"瑙鲁");
            put("OM",	"阿曼");
            put("PA",	"巴拿马");
            put("PE",	"秘鲁");
            put("PF",	"法属波利尼西亚");
            put("PG",	"巴布亚新几内亚");
            put("PH",	"菲律宾");
            put("PK",	"巴基斯坦");
            put("PL",	"波兰");
            put("PN",	"皮特凯恩群岛");
            put("PR",	"波多黎各");
            put("PS",	"巴勒斯坦");
            put("PW",	"帕劳");
            put("PY",	"巴拉圭");
            put("QA",	"卡塔尔");
            put("RE",	"留尼汪");
            put("RO",	"罗马尼亚");
            put("RS",	"塞尔维亚");
            put("RU",	"俄罗斯");
            put("RW",	"卢旺达");
            put("SB",	"所罗门群岛");
            put("SC",	"塞舌尔");
            put("SD",	"苏丹");
            put("SE",	"瑞典");
            put("SG",	"新加坡");
            put("SI",	"斯洛文尼亚");
            put("SJ",	"斯瓦尔巴群岛和扬马延岛");
            put("SK",	"斯洛伐克");
            put("SL",	"塞拉利昂");
            put("SM",	"圣马力诺");
            put("SN",	"塞内加尔");
            put("SO",	"索马里");
            put("SR",	"苏里南");
            put("SS",	"南苏丹");
            put("ST",	"圣多美和普林西比");
            put("SV",	"萨尔瓦多");
            put("SY",	"叙利亚");
            put("SZ",	"斯威士兰");
            put("TC",	"特克斯和凯科斯群岛");
            put("TD",	"乍得");
            put("TG",	"多哥");
            put("TH",	"泰国");
            put("TK",	"托克劳");
            put("TL",	"东帝汶");
            put("TN",	"突尼斯");
            put("TO",	"汤加");
            put("TR",	"土耳其");
            put("TV",	"图瓦卢");
            put("TZ",	"坦桑尼亚");
            put("UA",	"乌克兰");
            put("UG",	"乌干达");
            put("US",	"美国");
            put("UY",	"乌拉圭");
            put("VA",	"梵蒂冈");
            put("VE",	"委内瑞拉");
            put("VG",	"英属维尔京群岛");
            put("VI",	"美属维尔京群岛");
            put("VN",	"越南");
            put("WF",	"瓦利斯和富图纳");
            put("WS",	"萨摩亚");
            put("YE",	"也门");
            put("YT",	"马约特");
            put("ZA",	"南非");
            put("ZM",	"赞比亚");
            put("ZW",	"津巴布韦");
            put("CN",	"中国");
            put("CG",	"刚果（布）");
            put("CD",	"刚果（金）");
            put("MZ",	"莫桑比克");
            put("GG",	"根西岛");
            put("GM",	"冈比亚");
            put("MP",	"北马里亚纳群岛");
            put("ET",	"埃塞俄比亚");
            put("NC",	"新喀里多尼亚");
            put("VU",	"瓦努阿图");
            put("TF",	"法属南部领地");
            put("NU",	"纽埃");
            put("UM",	"美国本土外小岛屿");
            put("CK",	"库克群岛");
            put("GB",	"英国");
            put("TT",	"特立尼达和多巴哥");
            put("VC",	"圣文森特和格林纳丁斯");
            put("TW",	"中国台湾");
            put("NZ",	"新西兰");
            put("SA",	"沙特阿拉伯");
            put("LA",	"老挝");
            put("KP",	"朝鲜");
            put("KR",	"韩国");
            put("PT",	"葡萄牙");
            put("KG",	"吉尔吉斯斯坦");
            put("KZ",	"哈萨克斯坦");
            put("TJ",	"塔吉克斯坦");
            put("TM",	"土库曼斯坦");
            put("UZ",	"乌兹别克斯坦");
            put("KN",	"圣基茨和尼维斯");
            put("PM",	"圣皮埃尔和密克隆");
            put("SH",	"圣赫勒拿");
            put("LC",	"圣卢西亚");
            put("MU",	"毛里求斯");
            put("CI",	"科特迪瓦");
            put("KE",	"肯尼亚");
            put("MN",	"蒙古国");
        }
    };

}
