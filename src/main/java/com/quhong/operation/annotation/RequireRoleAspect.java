package com.quhong.operation.annotation;

import com.alibaba.fastjson.JSON;
import com.quhong.config.OperationRedisBean;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.utils.StringUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Component
@Aspect
public class RequireRoleAspect {

    private static final Logger logger = LoggerFactory.getLogger(RequireRoleAspect.class);

    @Resource(name = OperationRedisBean.MANAGE_ROLE)
    private StringRedisTemplate roleRedis;
    @Autowired
    private ManagerDao managerDao;

    @Pointcut("@annotation(com.quhong.operation.annotation.RequireRole)")
    private void pointcut() {
    }

    @Around("pointcut() && @annotation(requireRole)")
    public Object advice(ProceedingJoinPoint pjp, RequireRole requireRole) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.
                requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();

        String uid = request.getParameter("uid");
        if (StringUtil.isEmptyOrBlank(uid)) {
            uid = (String) request.getSession().getAttribute("uid");
            if (StringUtil.isEmptyOrBlank(uid)) {
                uid = request.getAttribute("uid").toString();
            }
        }
        int userRole = getUserRole(uid);
        if (userRole < requireRole.value()) {
//            String NO_ROLE = "/error/noRole";
//            request.getRequestDispatcher(NO_ROLE).forward(request, response);

            response.setStatus(HttpStatus.OK.value());
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=utf-8");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "*");
            response.setHeader("Access-Control-Max-Age", "4200");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            try (PrintWriter out = response.getWriter()) {
                out.append(JSON.toJSONString(new HttpResult<>().error(20, "权限不够联系管理员")));
            } catch (IOException e) {
                logger.info("token expire msg={}", e.getMessage());
            }
            logger.info("permission denied uid={} request path={} myRole={} destValue={}", uid, request.getRequestURL(), userRole, requireRole.value());
            return null;
        }
        return pjp.proceed();
    }

    private int getUserRole(String uid) {
        String key = "role_" + uid;
        Object o = roleRedis.opsForValue().get(key);
        if (Objects.isNull(o)) {
            Integer data = managerDao.verifyAdminLevelByUid(uid).getData();
            if (data > 0) {
                roleRedis.opsForValue().set(key, data + "", 4, TimeUnit.HOURS);
            }
            return data;
        }
        return Integer.parseInt(o.toString());
    }
}
