package com.quhong.operation.country;

import com.fasterxml.jackson.databind.JsonNode;
import com.maxmind.db.Reader;
import com.quhong.operation.share.data.CountryData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetAddress;

@Component
public class CountryQuery {
    private static final Logger logger = LoggerFactory.getLogger(CountryQuery.class);

    private volatile static Reader reader;

    public static Reader getReader() {
        if (reader == null) {
            synchronized (Reader.class) {
                if (reader == null) {
                    try {
                        reader = new Reader(new ClassPathResource("classpath:GeoIP2-Country.mmdb").getInputStream());
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        return reader;
    }

    public CountryData find(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            JsonNode node = getReader().get(address);
            if (node == null) {
                logger.error("can not find node.ip={}", ip);
                return null;
            }
            CountryData countryData = new CountryData();
            countryData.setIp(ip);
            String continent = getNode(node, "continent", "names", "en");
            countryData.setContinent(continent);
            String country = getNode(node, "country", "names", "en");
            countryData.setCountry(country);
            String code = getNode(node, "country", "iso_code");
            countryData.setCode(code);
            return countryData;
        } catch (Exception e) {
            logger.error("query country error. ip={} {}", ip, e.getMessage(), e);
        }
        return null;
    }

    private String getNode(JsonNode node, String... names) {
        JsonNode subNode = node;
        for (String name : names) {
            subNode = subNode.get(name);
            if (subNode == null) {
                return null;
            }
        }
        if (subNode != null) {
            return subNode.asText();
        }
        return null;
    }
}
