package com.quhong.operation.share.reportsdto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/18
 */
public class IncomeStatementDTO implements Serializable {
    private static final long serialVersionUID = -6784431911068083793L;

    // admin充值
    private Integer adminCharge = 0;
    //
    private Integer googlePayCharge = 0;
    //
    private Integer payPalCharge = 0;
    //
    private Integer applePayCharge = 0;
    //
    private Integer applePayChargeSubscription = 0;
    // 签到
    private Integer signIn = 0;
    // 礼物收入
    private Integer incomeGift = 0;
    // 红包收入
    private Integer incomeRedPacket = 0;
    // 房间消费反钻
    private Integer roomConsumeFeedback = 0;
    // vip奖励
    private Integer vipAward = 0;
    // 消费反钻
    private Integer consumeFeedback = 0;
    // 老虎机游戏赢钻
    private Integer tigerMachineGame = 0;
    // 活动奖励
    private Integer activityAward = 0;
    // 猜拳游戏
    private Integer fingerGame = 0;
    //
    private Integer adminHonorCharge = 0;
    //
    private Integer adminNonHonorCharge = 0;
    //
    private Integer adminDiamondsDaily = 0;
    //
    private Integer operatingRoomFridaySalary = 0;
    //
    private Integer adminChargePartyGirl = 0;
    //
    private Integer adminChargeParty = 0;
    // 九宫格游戏
    private Integer sudokuGame = 0;

    public Integer getAdminCharge() {
        return adminCharge;
    }

    public void setAdminCharge(Integer adminCharge) {
        this.adminCharge = adminCharge;
    }

    public Integer getGooglePayCharge() {
        return googlePayCharge;
    }

    public void setGooglePayCharge(Integer googlePayCharge) {
        this.googlePayCharge = googlePayCharge;
    }

    public Integer getPayPalCharge() {
        return payPalCharge;
    }

    public void setPayPalCharge(Integer payPalCharge) {
        this.payPalCharge = payPalCharge;
    }

    public Integer getApplePayCharge() {
        return applePayCharge;
    }

    public void setApplePayCharge(Integer applePayCharge) {
        this.applePayCharge = applePayCharge;
    }

    public Integer getApplePayChargeSubscription() {
        return applePayChargeSubscription;
    }

    public void setApplePayChargeSubscription(Integer applePayChargeSubscription) {
        this.applePayChargeSubscription = applePayChargeSubscription;
    }

    public Integer getSignIn() {
        return signIn;
    }

    public void setSignIn(Integer signIn) {
        this.signIn = signIn;
    }

    public Integer getIncomeGift() {
        return incomeGift;
    }

    public void setIncomeGift(Integer incomeGift) {
        this.incomeGift = incomeGift;
    }

    public Integer getIncomeRedPacket() {
        return incomeRedPacket;
    }

    public void setIncomeRedPacket(Integer incomeRedPacket) {
        this.incomeRedPacket = incomeRedPacket;
    }

    public Integer getRoomConsumeFeedback() {
        return roomConsumeFeedback;
    }

    public void setRoomConsumeFeedback(Integer roomConsumeFeedback) {
        this.roomConsumeFeedback = roomConsumeFeedback;
    }

    public Integer getVipAward() {
        return vipAward;
    }

    public void setVipAward(Integer vipAward) {
        this.vipAward = vipAward;
    }

    public Integer getConsumeFeedback() {
        return consumeFeedback;
    }

    public void setConsumeFeedback(Integer consumeFeedback) {
        this.consumeFeedback = consumeFeedback;
    }

    public Integer getTigerMachineGame() {
        return tigerMachineGame;
    }

    public void setTigerMachineGame(Integer tigerMachineGame) {
        this.tigerMachineGame = tigerMachineGame;
    }

    public Integer getActivityAward() {
        return activityAward;
    }

    public void setActivityAward(Integer activityAward) {
        this.activityAward = activityAward;
    }

    public Integer getFingerGame() {
        return fingerGame;
    }

    public void setFingerGame(Integer fingerGame) {
        this.fingerGame = fingerGame;
    }

    public Integer getAdminHonorCharge() {
        return adminHonorCharge;
    }

    public void setAdminHonorCharge(Integer adminHonorCharge) {
        this.adminHonorCharge = adminHonorCharge;
    }

    public Integer getAdminNonHonorCharge() {
        return adminNonHonorCharge;
    }

    public void setAdminNonHonorCharge(Integer adminNonHonorCharge) {
        this.adminNonHonorCharge = adminNonHonorCharge;
    }

    public Integer getAdminDiamondsDaily() {
        return adminDiamondsDaily;
    }

    public void setAdminDiamondsDaily(Integer adminDiamondsDaily) {
        this.adminDiamondsDaily = adminDiamondsDaily;
    }

    public Integer getOperatingRoomFridaySalary() {
        return operatingRoomFridaySalary;
    }

    public void setOperatingRoomFridaySalary(Integer operatingRoomFridaySalary) {
        this.operatingRoomFridaySalary = operatingRoomFridaySalary;
    }

    public Integer getAdminChargePartyGirl() {
        return adminChargePartyGirl;
    }

    public void setAdminChargePartyGirl(Integer adminChargePartyGirl) {
        this.adminChargePartyGirl = adminChargePartyGirl;
    }

    public Integer getAdminChargeParty() {
        return adminChargeParty;
    }

    public void setAdminChargeParty(Integer adminChargeParty) {
        this.adminChargeParty = adminChargeParty;
    }

    public Integer getSudokuGame() {
        return sudokuGame;
    }

    public void setSudokuGame(Integer sudokuGame) {
        this.sudokuGame = sudokuGame;
    }

    public Integer sum() {
        Integer sum = adminCharge + googlePayCharge + payPalCharge + applePayCharge
                + applePayChargeSubscription + signIn + incomeGift + incomeRedPacket + roomConsumeFeedback
                + vipAward + consumeFeedback + tigerMachineGame + activityAward + fingerGame + adminHonorCharge
                + adminNonHonorCharge + adminDiamondsDaily + operatingRoomFridaySalary + adminChargePartyGirl
                + adminChargeParty + sudokuGame;
        return sum;
    }

    @Override
    public String toString() {
        return "IncomeStatementDTO{" +
                "adminCharge=" + adminCharge +
                ", googlePayCharge=" + googlePayCharge +
                ", payPalCharge=" + payPalCharge +
                ", applePayCharge=" + applePayCharge +
                ", applePayChargeSubscription=" + applePayChargeSubscription +
                ", signIn=" + signIn +
                ", incomeGift=" + incomeGift +
                ", incomeRedPacket=" + incomeRedPacket +
                ", roomConsumeFeedback=" + roomConsumeFeedback +
                ", vipAward=" + vipAward +
                ", consumeFeedback=" + consumeFeedback +
                ", tigerMachineGame=" + tigerMachineGame +
                ", activityAward=" + activityAward +
                ", fingerGame=" + fingerGame +
                ", adminHonorCharge=" + adminHonorCharge +
                ", adminNonHonorCharge=" + adminNonHonorCharge +
                ", adminDiamondsDaily=" + adminDiamondsDaily +
                ", operatingRoomFridaySalary=" + operatingRoomFridaySalary +
                ", adminChargePartyGirl=" + adminChargePartyGirl +
                ", adminChargeParty=" + adminChargeParty +
                ", sudokuGame=" + sudokuGame +
                '}';
    }
}
