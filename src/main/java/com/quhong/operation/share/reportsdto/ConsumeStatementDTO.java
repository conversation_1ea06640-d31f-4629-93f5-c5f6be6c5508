package com.quhong.operation.share.reportsdto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/18
 */
public class ConsumeStatementDTO implements Serializable {

    private static final long serialVersionUID = -7727759947947590572L;
    // 购买消费
    private Integer buyConsume = 0;
    // 发送礼物
    private Integer sendGift = 0;
    // 发送红包
    private Integer sendRedPacket = 0;
    // vip充值
    private Integer vipRecharge = 0;
    // admin减钻
    private Integer adminSubBeans = 0;
    // 老虎机游戏
    private Integer tigerMachineGame = 0;
    // 猜拳游戏
    private Integer fingerGame = 0;
    // 新骰子游戏
    private Integer newDiceGame = 0;
    // 九宫格游戏
    private Integer sudokuGame = 0;

    public Integer getBuyConsume() {
        return buyConsume;
    }

    public void setBuyConsume(Integer buyConsume) {
        this.buyConsume = buyConsume;
    }

    public Integer getSendGift() {
        return sendGift;
    }

    public void setSendGift(Integer sendGift) {
        this.sendGift = sendGift;
    }

    public Integer getSendRedPacket() {
        return sendRedPacket;
    }

    public void setSendRedPacket(Integer sendRedPacket) {
        this.sendRedPacket = sendRedPacket;
    }

    public Integer getVipRecharge() {
        return vipRecharge;
    }

    public void setVipRecharge(Integer vipRecharge) {
        this.vipRecharge = vipRecharge;
    }

    public Integer getAdminSubBeans() {
        return adminSubBeans;
    }

    public void setAdminSubBeans(Integer adminSubBeans) {
        this.adminSubBeans = adminSubBeans;
    }

    public Integer getTigerMachineGame() {
        return tigerMachineGame;
    }

    public void setTigerMachineGame(Integer tigerMachineGame) {
        this.tigerMachineGame = tigerMachineGame;
    }

    public Integer getFingerGame() {
        return fingerGame;
    }

    public void setFingerGame(Integer fingerGame) {
        this.fingerGame = fingerGame;
    }

    public Integer getNewDiceGame() {
        return newDiceGame;
    }

    public void setNewDiceGame(Integer newDiceGame) {
        this.newDiceGame = newDiceGame;
    }

    public Integer getSudokuGame() {
        return sudokuGame;
    }

    public void setSudokuGame(Integer sudokuGame) {
        this.sudokuGame = sudokuGame;
    }

    public Integer sum() {
        Integer sum = buyConsume + sendGift + sendRedPacket + vipRecharge + adminSubBeans
                + tigerMachineGame + fingerGame + newDiceGame + sudokuGame;
        return sum;
    }

    @Override
    public String toString() {
        return "ConsumeStatementDTO{" +
                "buyConsume=" + buyConsume +
                ", sendGift=" + sendGift +
                ", sendRedPacket=" + sendRedPacket +
                ", vipRecharge=" + vipRecharge +
                ", adminSubBeans=" + adminSubBeans +
                ", tigerMachineGame=" + tigerMachineGame +
                ", fingerGame=" + fingerGame +
                ", newDiceGame=" + newDiceGame +
                ", sudokuGame=" + sudokuGame +
                '}';
    }
}
