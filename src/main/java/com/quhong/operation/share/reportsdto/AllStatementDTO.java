package com.quhong.operation.share.reportsdto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/18
 */
public class AllStatementDTO implements Serializable {

    private static final long serialVersionUID = -449550649233553905L;
    private Integer totalRecharge = 0;
    private Integer dialGame = 0;
    private Integer pkGame = 0;
    private Integer other = 0;
    private Integer tigerMachineGame = 0;
    private Integer roomConsumeFeedback = 0;
    private Integer fingerGame = 0;
    private Integer newDiceGame = 0;
    private Integer adminSubBeans = 0;
    private Integer buyConsume = 0;
    private Integer vipRecharge = 0;
    private Integer getSendGift = 0;
    private Integer getSendRedPacket = 0;
    private Integer vipAward = 0;
    private Integer signIn = 0;
    private Integer activityAward = 0;
    private Integer consumeFeedback = 0;

    public Integer getTotalRecharge() {
        return totalRecharge;
    }

    public void setTotalRecharge(Integer totalRecharge) {
        this.totalRecharge = totalRecharge;
    }

    public Integer getAdminSubBeans() {
        return adminSubBeans;
    }

    public void setAdminSubBeans(Integer adminSubBeans) {
        this.adminSubBeans = adminSubBeans;
    }

    public Integer getDialGame() {
        return dialGame;
    }

    public void setDialGame(Integer dialGame) {
        this.dialGame = dialGame;
    }

    public Integer getBuyConsume() {
        return buyConsume;
    }

    public void setBuyConsume(Integer buyConsume) {
        this.buyConsume = buyConsume;
    }

    public Integer getSignIn() {
        return signIn;
    }

    public void setSignIn(Integer signIn) {
        this.signIn = signIn;
    }

    public Integer getGetSendGift() {
        return getSendGift;
    }

    public void setGetSendGift(Integer getSendGift) {
        this.getSendGift = getSendGift;
    }

    public Integer getGetSendRedPacket() {
        return getSendRedPacket;
    }

    public void setGetSendRedPacket(Integer getSendRedPacket) {
        this.getSendRedPacket = getSendRedPacket;
    }

    public Integer getPkGame() {
        return pkGame;
    }

    public void setPkGame(Integer pkGame) {
        this.pkGame = pkGame;
    }

    public Integer getVipRecharge() {
        return vipRecharge;
    }

    public void setVipRecharge(Integer vipRecharge) {
        this.vipRecharge = vipRecharge;
    }

    public Integer getRoomConsumeFeedback() {
        return roomConsumeFeedback;
    }

    public void setRoomConsumeFeedback(Integer roomConsumeFeedback) {
        this.roomConsumeFeedback = roomConsumeFeedback;
    }

    public Integer getVipAward() {
        return vipAward;
    }

    public void setVipAward(Integer vipAward) {
        this.vipAward = vipAward;
    }

    public Integer getConsumeFeedback() {
        return consumeFeedback;
    }

    public void setConsumeFeedback(Integer consumeFeedback) {
        this.consumeFeedback = consumeFeedback;
    }

    public Integer getActivityAward() {
        return activityAward;
    }

    public void setActivityAward(Integer activityAward) {
        this.activityAward = activityAward;
    }

    public Integer getTigerMachineGame() {
        return tigerMachineGame;
    }

    public void setTigerMachineGame(Integer tigerMachineGame) {
        this.tigerMachineGame = tigerMachineGame;
    }

    public Integer getFingerGame() {
        return fingerGame;
    }

    public void setFingerGame(Integer fingerGame) {
        this.fingerGame = fingerGame;
    }

    public Integer getNewDiceGame() {
        return newDiceGame;
    }

    public void setNewDiceGame(Integer newDiceGame) {
        this.newDiceGame = newDiceGame;
    }

    public Integer getOther() {
        return other;
    }

    public void setOther(Integer other) {
        this.other = other;
    }

    public Integer sum() {
        Integer sum = totalRecharge + dialGame + pkGame + other + tigerMachineGame + roomConsumeFeedback + fingerGame
                + newDiceGame + adminSubBeans + buyConsume + vipRecharge + getSendGift + getSendRedPacket + vipAward
                + signIn + activityAward + consumeFeedback;
        return sum;

    }

    @Override
    public String toString() {
        return "AllStatementDTO{" +
                "totalRecharge=" + totalRecharge +
                ", dialGame=" + dialGame +
                ", pkGame=" + pkGame +
                ", other=" + other +
                ", tigerMachineGame=" + tigerMachineGame +
                ", roomConsumeFeedback=" + roomConsumeFeedback +
                ", fingerGame=" + fingerGame +
                ", newDiceGame=" + newDiceGame +
                ", adminSubBeans=" + adminSubBeans +
                ", buyConsume=" + buyConsume +
                ", vipRecharge=" + vipRecharge +
                ", getSendGift=" + getSendGift +
                ", getSendRedPacket=" + getSendRedPacket +
                ", vipAward=" + vipAward +
                ", signIn=" + signIn +
                ", activityAward=" + activityAward +
                ", consumeFeedback=" + consumeFeedback +
                '}';
    }
}
