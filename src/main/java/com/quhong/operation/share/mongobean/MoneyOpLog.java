package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/28
 */
@Document(collection = "money_op_log")
public class MoneyOpLog implements Serializable {

    private static final long serialVersionUID = -6475577766507328483L;
    private ObjectId _id;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    // 管理员uid
    @Field("uid")
    private String adminUid;
    // 普通用户uid
    @Field("aid")
    private String uid;
    // 钻石数
    private Integer beans;
    // 时间
    @Field("c_time")
    private Integer cTime;
    // 操作者的ip
    private String ip;
    // 操作者手机的imei
    private String imei;
    // 操作后的剩余钻石数
    private Integer left;
    // 操作描述
    @Field("desc")
    private String remark;
    //  充值类型 0 未知 1 荣誉加钻 2 非荣誉加钻 3减钻
    @Field("charge_type")
    private Integer chargeType;
    // 充值子类型
    @Field("s_type")
    private Integer sType;
    // 充值货币单位
    private String currency;
    // 充值金额
    private Integer money;

    public String getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(String adminUid) {
        this.adminUid = adminUid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Integer getcTime() {
        return cTime;
    }

    public void setcTime(Integer cTime) {
        this.cTime = cTime;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public Integer getLeft() {
        return left;
    }

    public void setLeft(Integer left) {
        this.left = left;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getChargeType() {
        return chargeType;
    }

    public void setChargeType(Integer chargeType) {
        this.chargeType = chargeType;
    }

    public Integer getsType() {
        return sType;
    }

    public void setsType(Integer sType) {
        this.sType = sType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getMoney() {
        return money;
    }

    public void setMoney(Integer money) {
        this.money = money;
    }

    @Override
    public String toString() {
        return "MoneyOpLog{" +
                "_id=" + _id +
                ", adminUid='" + adminUid + '\'' +
                ", uid='" + uid + '\'' +
                ", beans=" + beans +
                ", cTime=" + cTime +
                ", ip='" + ip + '\'' +
                ", imei='" + imei + '\'' +
                ", left=" + left +
                ", remark='" + remark + '\'' +
                ", chargeType=" + chargeType +
                ", sType=" + sType +
                ", currency='" + currency + '\'' +
                ", money=" + money +
                '}';
    }
}
