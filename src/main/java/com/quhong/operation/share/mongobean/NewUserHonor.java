package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

@Document(collection = "new_user_honor")
public class NewUserHonor{

    private String _id;

    @Field("honor_level")
    private Integer honorLevel;
    @Field("get_time")
    private Integer getTime;

    private Integer beans;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public Integer getHonorLevel() {
        return honorLevel;
    }

    public void setHonorLevel(Integer honorLevel) {
        this.honorLevel = honorLevel;
    }

    public Integer getGetTime() {
        return getTime;
    }

    public void setGetTime(Integer getTime) {
        this.getTime = getTime;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    @Override
    public String toString() {
        return "NewUserHonor{" +
                "_id='" + _id + '\'' +
                ", honorLevel=" + honorLevel +
                ", getTime=" + getTime +
                ", beans=" + beans +
                '}';
    }
}
