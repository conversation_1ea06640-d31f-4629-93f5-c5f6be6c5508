package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 迎新房
 * <AUTHOR>
 * @date 2020/8/3
 */
@Document(collection = "rookie_room")
public class RookieRoom implements Serializable {
    private static final long serialVersionUID = 6255404638966129711L;
    @Field("room_id")
    private String roomId;
    private Integer status;
    @Field("c_time")
    private Integer cTime;

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getcTime() {
        return cTime;
    }

    public void setcTime(Integer cTime) {
        this.cTime = cTime;
    }

    @Override
    public String toString() {
        return "RookieRoom{" +
                "roomId='" + roomId + '\'' +
                ", status=" + status +
                ", cTime=" + cTime +
                '}';
    }
}
