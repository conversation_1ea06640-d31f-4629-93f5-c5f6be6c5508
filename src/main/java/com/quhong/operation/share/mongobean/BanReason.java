package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Document(collection = "ban_reason")
public class BanReason implements Serializable {

    private static final long serialVersionUID = -3524510816106110643L;

    private String uid;
    // 封号理由
    private String reason;
    // 封号时间
    private Integer mtime;
    // 封号的人
    @Field("op_uid")
    private String opUid;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getOpUid() {
        return opUid;
    }

    public void setOpUid(String opUid) {
        this.opUid = opUid;
    }

    @Override
    public String toString() {
        return "BanReason{" +
                "uid='" + uid + '\'' +
                ", reason='" + reason + '\'' +
                ", mtime=" + mtime +
                ", opUid='" + opUid + '\'' +
                '}';
    }

}
