package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Document(collection = "user_monitor_log")
public class UserMonitorLog implements Serializable {

    private static final long serialVersionUID = 4970599282402664416L;
    // 操作人id
    private String operator;
    private String optUser;
    // 被操作人
    private String uid;
    private String name;
    // 操作编码
    private Integer code;
    // 操作
    private String operation;
    // 原因
    private String reason;
    // 封禁时长
    private String block_term;
    @Field("opt_time")
    private Integer optTime;

    public String getBlock_term() {
        return block_term;
    }

    public void setBlock_term(String block_term) {
        this.block_term = block_term;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOptUser() {
        return optUser;
    }

    public void setOptUser(String optUser) {
        this.optUser = optUser;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getOptTime() {
        return optTime;
    }

    public void setOptTime(Integer optTime) {
        this.optTime = optTime;
    }

    @Override
    public String toString() {
        return "UserMonitorLog{" +
                "operator='" + operator + '\'' +
                ", optUser='" + optUser + '\'' +
                ", uid='" + uid + '\'' +
                ", name='" + name + '\'' +
                ", code=" + code +
                ", operation=" + operation +
                ", reason='" + reason + '\'' +
                ", optTime=" + optTime +
                '}';
    }

}
