package com.quhong.operation.share.mongobean;

import com.quhong.mongo.dao.ActorDao;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 沙特用户注册BO，用于发送邮件
 *
 * <AUTHOR>
 * @date 2025/8/26 15:59
 */
@Lazy
@Document(collection = ActorDao.TABLE_NAME)
public class RegisterSendMailBO {
    /**
     * id
     */
    @Id
    private ObjectId _id;

    /**
     * rid
     */
    private Integer rid;

    /**
     * actor名称
     */
    private String name = "";


    /**
     * 国家
     */
    private String country = "";

    /**
     * facebook性别 1男2女
     */
    @Field("fb_gender")
    private String fbGender;

    /**
     * 新账号的 tn_id（仅新注册账号且为新设备时才会写入）
     */
    private String firstTnId;

    /**
     * 国家码及国家
     */
    private String ipCodeCountry;

    public String getIpCodeCountry() {
        return ipCodeCountry;
    }

    public void setIpCodeCountry(String ipCodeCountry) {
        this.ipCodeCountry = ipCodeCountry;
    }

    public String getFirstTnId() {
        return firstTnId;
    }

    public void setFirstTnId(String firstTnId) {
        this.firstTnId = firstTnId;
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getFbGender() {
        return fbGender;
    }

    public void setFbGender(String fbGender) {
        this.fbGender = fbGender;
    }
}
