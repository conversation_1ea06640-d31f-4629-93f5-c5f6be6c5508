package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 赊账列表
 * <AUTHOR>
 * @date 2020/6/28
 */
@Document(collection = "credit_charge_bill")
public class CreditChargeBill implements Serializable {

    private static final long serialVersionUID = -4105174927237103038L;

    private ObjectId _id;
    // 赊账用户uid
    private String uid;
    // 管理员uid
    @Field("admin_uid")
    private String adminUid;
    // 赊账砖石
    private Integer beans;
    // 赊账日期
    @Field("c_time")
    private Integer cTime;
    // 结清日期
    @Field("end_time")
    private Integer endTime;
    // 备注
    private String remark;
    // 0 未知 1 未结清 2已结清
    private Integer state;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(String adminUid) {
        this.adminUid = adminUid;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Integer getcTime() {
        return cTime;
    }

    public void setcTime(Integer cTime) {
        this.cTime = cTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "CreditChargeBill{" +
                "_id=" + _id +
                ", uid='" + uid + '\'' +
                ", adminUid='" + adminUid + '\'' +
                ", beans=" + beans +
                ", cTime=" + cTime +
                ", endTime=" + endTime +
                ", remark='" + remark + '\'' +
                ", state=" + state +
                '}';
    }

}
