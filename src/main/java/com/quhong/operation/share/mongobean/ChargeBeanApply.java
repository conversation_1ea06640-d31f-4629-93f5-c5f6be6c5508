package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Document(collection = "charge_bean_apply")
public class ChargeBeanApply {

    @Id
    private ObjectId _id;

    @Indexed
    private String uid;

    private String username;

    /**
     * 打钻desc
     */
    private String desc;

    /**
     * 打钻es_title
     */
    @Field("es_title")
    private String esTitle;

    /**
     *  打钻类型: 1 for activity room salary
     */
    @Field("charge_type")
    private Integer chargeType;

    /**
     * 0 待处理 1 驳回 2 同意申请
     */
    private Integer status;

    /**
     * 1 已读 0 未读
     */
    @Field("read_status")
    private Integer readStatus;

    @Field("apply_list")
    private List<UserChargeBean> applyList;


    private Integer ctime;

    private Integer mtime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<UserChargeBean> getApplyList() {
        return applyList;
    }

    public void setApplyList(List<UserChargeBean> applyList) {
        this.applyList = applyList;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getEsTitle() {
        return esTitle;
    }

    public void setEsTitle(String esTitle) {
        this.esTitle = esTitle;
    }

    public Integer getChargeType() {
        return chargeType;
    }

    public void setChargeType(Integer chargeType) {
        this.chargeType = chargeType;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getReadStatus() {
        return readStatus;
    }

    public void setReadStatus(Integer readStatus) {
        this.readStatus = readStatus;
    }
}
