package com.quhong.operation.share.mongobean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * ludo游戏
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Document(collection = "ludo")
public class LudoData {

    private String gameId; // 游戏id
    private String roomId; // 房间id
    private String selfUid; // 游戏创建者
    private int gameType; // 1 经典 2 快速
    private int currency; // 入场费
    private int currencyType; // 游戏币类型 1 心心 2 钻石
    private int totalCurrency; // 总入场费
    private int tax; // 平台扣除的手续费
    private int startTime; // 游戏开始时间
    private int endTime; // 游戏结束时间
    private List<GamePlayerData> playerList; // 玩家uid数据
    private List<WinnerPlayer> winnerPlayerList; // 游戏结果


    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getSelfUid() {
        return selfUid;
    }

    public void setSelfUid(String selfUid) {
        this.selfUid = selfUid;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getCurrency() {
        return currency;
    }

    public void setCurrency(int currency) {
        this.currency = currency;
    }

    public int getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(int currencyType) {
        this.currencyType = currencyType;
    }

    public int getTotalCurrency() {
        return totalCurrency;
    }

    public void setTotalCurrency(int totalCurrency) {
        this.totalCurrency = totalCurrency;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public int getTax() {
        return tax;
    }

    public void setTax(int tax) {
        this.tax = tax;
    }

    public List<GamePlayerData> getPlayerList() {
        if (CollectionUtils.isEmpty(playerList)) {
            return new ArrayList<>();
        }
        return playerList;
    }

    public void setPlayerList(List<GamePlayerData> playerList) {
        this.playerList = playerList;
    }

    public List<WinnerPlayer> getWinnerPlayerList() {
        if (CollectionUtils.isEmpty(winnerPlayerList)) {
            return new ArrayList<>();
        }
        return winnerPlayerList;
    }

    public void setWinnerPlayerList(List<WinnerPlayer> winnerPlayerList) {
        this.winnerPlayerList = winnerPlayerList;
    }

}
