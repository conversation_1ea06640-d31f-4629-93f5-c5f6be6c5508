package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "feedback_handle")
public class FeedbackHandleData {

    @Id
    private ObjectId _id;

    /**
     * 上报id
     */
    @Indexed
    @Field("report_id")
    private String reportId;

    /**
     * 处理用户id
     */
    @Indexed
    private String uid;

    /**
     * 处理用户名
     */
    @Indexed
    private String username;

    /**
     * 审核状态：0 待处理 1 处理中 2 已处理 3 垃圾箱
     */
    @Indexed
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 回复状态：0 未回复 1 已回复 2 无需回复
     */
    @Indexed
    @Field("reply_status")
    private Integer replyStatus;

    /**
     * 创建时间戳
     */
    private Integer ctime;

    /**
     * 更新时间戳
     */
    @Indexed
    private Integer mtime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getReplyStatus() {
        return replyStatus;
    }

    public void setReplyStatus(Integer replyStatus) {
        this.replyStatus = replyStatus;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
