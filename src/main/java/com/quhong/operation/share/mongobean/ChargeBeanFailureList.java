package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;


@Document(collection = "charge_bean_failure_list")
public class ChargeBeanFailureList {

    @Id
    private ObjectId _id;

    @Indexed
    private String uid;

    /**
     * 失败RID
     */
    @Field("failure_rid")
    private String failureRid;

    /**
     * 批次号
     */
    @Field("batch_no")
    private String batchNo;

    /**
     * 钻石数量
     */
    @Field("diamond_num")
    private Integer diamondNum;

    /**
     * 失败时间
     */
    @Field("failure_time")
    private String failureTime;

    /**
     * 失败原因
     */
    @Field("reason")
    private String reason;

    private Integer ctime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getFailureRid() {
        return failureRid;
    }

    public void setFailureRid(String failureRid) {
        this.failureRid = failureRid;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getDiamondNum() {
        return diamondNum;
    }

    public void setDiamondNum(Integer diamondNum) {
        this.diamondNum = diamondNum;
    }

    public String getFailureTime() {
        return failureTime;
    }

    public void setFailureTime(String failureTime) {
        this.failureTime = failureTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "ChargeBeanFailureList{" +
                "_id=" + _id +
                ", uid='" + uid + '\'' +
                ", failureRid='" + failureRid + '\'' +
                ", batchNo='" + batchNo + '\'' +
                ", diamondNum=" + diamondNum +
                ", failureTime='" + failureTime + '\'' +
                ", reason='" + reason + '\'' +
                ", ctime=" + ctime +
                '}';
    }
}
