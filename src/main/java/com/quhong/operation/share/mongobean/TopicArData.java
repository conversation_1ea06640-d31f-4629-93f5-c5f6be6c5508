package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

@Document(collection = "topic_ar")
public class TopicArData implements Serializable {

    private static final long serialVersionUID = -485361867327766638L;

    public static final int STATUS_ACTIVE = 1; //有效状态

    public static final int STATUS_STOP = 2; //无效状态；停止状态

    // id
    @Id
    private ObjectId _id;

    /**
     * 主题名
     */
    @Indexed
    @Field("topic_name")
    private String topicName;

    /**
     * 创建时间戳
     */
    @Field("c_time")
    private Integer ctime;

    /**
     * 话题状态：1表示可用状态，2表示停止
     */
    private Integer status;

    /**
     * 使用次数。默认0
     */
    private Integer opts;

    /**
     * 排序（置顶）
     */
    private Integer order;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOpts() {
        return opts;
    }

    public void setOpts(Integer opts) {
        this.opts = opts;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

}
