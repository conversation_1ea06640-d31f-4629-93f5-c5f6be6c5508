package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * youstar_admin账号对象
 * <AUTHOR>
 * @date 2020/6/22
 */
@Document(collection = "admin_user")
public class AdminUser implements Serializable {
    private static final long serialVersionUID = -3599052569820831366L;
    // uid 与 youstar的uid不对应
    private String uid;
    // 账号
    private String account;
    private String pwd;
    private String imei;
    // 权限等级
    private Integer level;
    @Field("android_id")
    private String androidId;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }
}
