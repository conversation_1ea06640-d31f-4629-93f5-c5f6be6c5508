package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

@Document(collection = "start_page")
public class StartPage implements Serializable {

    private static final long serialVersionUID = 5877879383130196229L;
    @Field("flash_id")
    private Integer flashId;
    private String title;

    @Field("title_ar")
    private String titleAr;

    private String url;

    @Field("url_ar")
    private String urlAr;

    @Field("iphonex_url")
    private String iphonexUrl;

    @Field("iphonex_urlar")
    private String iphonexUrlAr;

    private String link;
    private Integer skip;
    private Integer valid;
    private Integer atype;
    // atype 消息动作类型 用于是否跳转h5页面 0:跳转[如果url为空不跳转]  1:弹窗 2:跳转等级页面  3:Ride坐骑列表
    // 4:mic列表  5:bubble列表  6:voice声波  7:Unique ID  8:QUEEN 9:vip页面 10：个人主页  99:指定房间

    @Field("room_id")
    private String roomId;

    @Field("jump_aid")
    private String jumpAid;

    @Field("isdelete")
    private Integer isDelete;

    private Integer validTime;

    public Integer getFlashId() {
        return flashId;
    }

    public void setFlashId(Integer flashId) {
        this.flashId = flashId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrlAr() {
        return urlAr;
    }

    public void setUrlAr(String urlAr) {
        this.urlAr = urlAr;
    }

    public String getIphonexUrl() {
        return iphonexUrl;
    }

    public void setIphonexUrl(String iphonexUrl) {
        this.iphonexUrl = iphonexUrl;
    }

    public String getIphonexUrlAr() {
        return iphonexUrlAr;
    }

    public void setIphonexUrlAr(String iphonexUrlAr) {
        this.iphonexUrlAr = iphonexUrlAr;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Integer getSkip() {
        return skip;
    }

    public void setSkip(Integer skip) {
        this.skip = skip;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getAtype() {
        return atype;
    }

    public void setAtype(Integer atype) {
        this.atype = atype;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getJumpAid() {
        return jumpAid;
    }

    public void setJumpAid(String jumpAid) {
        this.jumpAid = jumpAid;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getValidTime() {
        return validTime;
    }

    public void setValidTime(Integer validTime) {
        this.validTime = validTime;
    }

    @Override
    public String toString() {
        return "StartPage{" +
                "flashId=" + flashId +
                ", title='" + title + '\'' +
                ", titleAr='" + titleAr + '\'' +
                ", url='" + url + '\'' +
                ", urlAr='" + urlAr + '\'' +
                ", link='" + link + '\'' +
                ", skip=" + skip +
                ", valid=" + valid +
                ", atype=" + atype +
                ", roomId='" + roomId + '\'' +
                ", jumpAid='" + jumpAid + '\'' +
                ", isdelete=" + isDelete +
                '}';
    }
}
