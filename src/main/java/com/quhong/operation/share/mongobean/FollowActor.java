package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 关注信息对象
 * <AUTHOR>
 * @date 2020/8/5
 */
@Document(collection = "follow")
public class FollowActor implements Serializable {
    private static final long serialVersionUID = -5371872382560191756L;
    // uid
    private String uid;
    // uid关注的用户uid
    private String aid;
    private Integer ctime;
    @Field("is_new")
    private Integer isNew;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    @Override
    public String toString() {
        return "FollowActor{" +
                "uid='" + uid + '\'' +
                ", aid='" + aid + '\'' +
                ", ctime=" + ctime +
                ", isNew=" + isNew +
                '}';
    }
}
