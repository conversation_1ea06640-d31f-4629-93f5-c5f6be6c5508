package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 关注房间对象
 * <AUTHOR>
 * @date 2020/8/5
 */
@Document(collection = "follow_room")
public class FollowRoom implements Serializable {
    private static final long serialVersionUID = 8686062696520746228L;
    private String uid;
    @Field("room_id")
    private String roomId;
    @Field("c_time")
    private Integer ctime;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FollowRoom{" +
                "uid='" + uid + '\'' +
                ", roomId='" + roomId + '\'' +
                ", ctime=" + ctime +
                '}';
    }
}
