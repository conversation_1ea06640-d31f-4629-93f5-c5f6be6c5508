package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Document(collection = "user_monitor")
public class UserMonitor implements Serializable {

    private static final long serialVersionUID = -1764301441626083897L;

    private String uid;
    private Integer code;
    @Field("opt_time")
    private Integer optTime;
    private String reason;
    @Field("release_at")
    private Long releaseAt;
    @Field("block_term")
    private String blockTerm;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getOptTime() {
        return optTime;
    }

    public void setOptTime(Integer optTime) {
        this.optTime = optTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getReleaseAt() {
        return releaseAt;
    }

    public void setReleaseAt(Long releaseAt) {
        this.releaseAt = releaseAt;
    }

    public String getBlockTerm() {
        return blockTerm;
    }

    public void setBlockTerm(String blockTerm) {
        this.blockTerm = blockTerm;
    }

    @Override
    public String toString() {
        return "UserMonitor{" +
                "uid='" + uid + '\'' +
                ", code=" + code +
                ", optTime=" + optTime +
                ", reason='" + reason + '\'' +
                ", releaseAt=" + releaseAt +
                ", blockTerm='" + blockTerm + '\'' +
                '}';
    }

}
