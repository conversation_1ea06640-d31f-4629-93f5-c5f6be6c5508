package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
public class LastLogin implements Serializable {
    private static final long serialVersionUID = -6502081326402350426L;

    private Integer status;
    @Field("login_time")
    private Long loginTime;
    @Field("logout_time")
    private Long logoutTime;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Long loginTime) {
        this.loginTime = loginTime;
    }

    public Long getLogoutTime() {
        return logoutTime;
    }

    public void setLogoutTime(Long logoutTime) {
        this.logoutTime = logoutTime;
    }

    @Override
    public String toString() {
        return "LastLogin{" +
                "status=" + status +
                ", loginTime=" + loginTime +
                ", logoutTime=" + logoutTime +
                '}';
    }

}
