package com.quhong.operation.share.mongobean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.List;

/**
 * 用户对象
 *
 * <AUTHOR>
 * @date 2020/4/22
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Document(collection = "actor")
public class Actor implements Serializable {

    private static final long serialVersionUID = 860169120664356301L;

    // id
    @Id
    private ObjectId _id;
    // 金币
    private Integer gold;
    // 钻石
    private Integer beans;
    // 房间id
    private String room;
    private String status;
    // actor名称
    private String name = "";
    // 头像
    private String head = "";
    //
    @Field("origin_head")
    private String originHead;
    //
    private String banner;
    // 历史遗留 gender为2表示1v1收费用户和主播
    private Integer gender = 1;
    // 1男2女
    @Field("fb_gender")
    private String fbGender;
    // 年龄
    private Integer age = 0;
    // 国家
    private String country = "";
    // 城市
    private String city;
    // 手机号
    private String phone;
    // 短id
    private Integer rid;
    //  用户有效性 0表示被封
    private Integer valid = 0;
    // 是否隐身
    private String display;
    // vip记录
    private String viprecord;
    // null
    @Field("actor_uid")
    private String uid;
    // 登录类型
    @Field("login_type")
    private String loginType;
    // 生日
    private String birthday;
    // fcm推送
    private String push;
    // 是否打开语言
    @Field("video_option")
    private String videoOption;
    // 语言首选项
    private String area;
    // 语言，英语， 阿语，土耳其语
    private String nlang;
    // 语言+国家吗
    private String lang;
    // 手机imsi
    private String imsi;
    // 手机imei
    private String imei;
    // ios唯一凭证
    private String ioskey;
    // andriod唯一凭证，防止多开
    @Field("android_id")
    private String androidId;
    // 是否ios用户,ios的版本号
    private String os;
    // ios马甲包
    private String mos;
    // null
    private String count;
    // null
    private String vnumber;
    // 被喜欢人数
    private Integer like;
    // 个人主页宣言
    private String desc = "";
    // follow人数限制
    @Field("follow_limit")
    private String followLimit;
    // 最后一次登陆信息
    @Field("last_login")
    private LastLogin lastLogin;
    //
    @Field("ride_option")
    private Integer rideOption = 0;

    // actor等级
    private Integer ulvl;
    // 勋章列表
    private List<String> badge;
    // vip等级
    private Integer vlvl;
    // 进房动画id
    @Field("join_carton_id")
    private Integer joinCartonId;
    //
    @Field("ripple_url")
    private String rippleUrl;
    //
    @Field("small_icon")
    private String smallIcon;
    //
    @Field("carton_times")
    private String cartonTimes;
    //
    @Field("source_type")
    private Integer sourceType;
    //
    @Field("join_carton_type")
    private Integer joinCartonType;
    //
    @Field("is_beautiful_rid")
    private Integer isBeautifulRid = 0;
    // 消费情况
    private String devote;

    private String ip;

    private String tn_id;

    private String oid;

    @Field("promotion_id")
    private String promotionId;
    private String channel;
    @Field("app_package_name")
    private String appPackageName;
    private String email; // 第三方的邮箱地址

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public Integer getGold() {
        return gold;
    }

    public void setGold(Integer gold) {
        this.gold = gold;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getOriginHead() {
        return originHead;
    }

    public void setOriginHead(String originHead) {
        this.originHead = originHead;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getFbGender() {
        return fbGender;
    }

    public void setFbGender(String fbGender) {
        this.fbGender = fbGender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public String getDisplay() {
        return display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

    public String getViprecord() {
        return viprecord;
    }

    public void setViprecord(String viprecord) {
        this.viprecord = viprecord;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getPush() {
        return push;
    }

    public void setPush(String push) {
        this.push = push;
    }

    public String getVideoOption() {
        return videoOption;
    }

    public void setVideoOption(String videoOption) {
        this.videoOption = videoOption;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getNlang() {
        return nlang;
    }

    public void setNlang(String nlang) {
        this.nlang = nlang;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getIoskey() {
        return ioskey;
    }

    public void setIoskey(String ioskey) {
        this.ioskey = ioskey;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getMos() {
        return mos;
    }

    public void setMos(String mos) {
        this.mos = mos;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getVnumber() {
        return vnumber;
    }

    public void setVnumber(String vnumber) {
        this.vnumber = vnumber;
    }

    public Integer getLike() {
        return like;
    }

    public void setLike(Integer like) {
        this.like = like;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getFollowLimit() {
        return followLimit;
    }

    public String getAppPackageName() {
        return appPackageName;
    }

    public void setAppPackageName(String appPackageName) {
        this.appPackageName = appPackageName;
    }

    public void setFollowLimit(String followLimit) {
        this.followLimit = followLimit;
    }

    public LastLogin getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(LastLogin lastLogin) {
        this.lastLogin = lastLogin;
    }

    public Integer getRideOption() {
        return rideOption;
    }

    public void setRideOption(Integer rideOption) {
        this.rideOption = rideOption;
    }

    public Integer getUlvl() {
        return ulvl;
    }

    public void setUlvl(Integer ulvl) {
        this.ulvl = ulvl;
    }

    public List<String> getBadge() {
        return badge;
    }

    public void setBadge(List<String> badge) {
        this.badge = badge;
    }

    public Integer getVlvl() {
        return vlvl;
    }

    public void setVlvl(Integer vlvl) {
        this.vlvl = vlvl;
    }

    public Integer getJoinCartonId() {
        return joinCartonId;
    }

    public void setJoinCartonId(Integer joinCartonId) {
        this.joinCartonId = joinCartonId;
    }

    public String getRippleUrl() {
        return rippleUrl;
    }

    public void setRippleUrl(String rippleUrl) {
        this.rippleUrl = rippleUrl;
    }

    public String getSmallIcon() {
        return smallIcon;
    }

    public void setSmallIcon(String smallIcon) {
        this.smallIcon = smallIcon;
    }

    public String getCartonTimes() {
        return cartonTimes;
    }

    public void setCartonTimes(String cartonTimes) {
        this.cartonTimes = cartonTimes;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getJoinCartonType() {
        return joinCartonType;
    }

    public void setJoinCartonType(Integer joinCartonType) {
        this.joinCartonType = joinCartonType;
    }

    public Integer getIsBeautifulRid() {
        return isBeautifulRid;
    }

    public void setIsBeautifulRid(Integer isBeautifulRid) {
        this.isBeautifulRid = isBeautifulRid;
    }

    public String getDevote() {
        return devote;
    }

    public void setDevote(String devote) {
        this.devote = devote;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(String promotionId) {
        this.promotionId = promotionId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getTn_id() {
        return tn_id;
    }

    public void setTn_id(String tn_id) {
        this.tn_id = tn_id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String toString() {
        return "Actor{" +
                "_id=" + _id +
                ", gold=" + gold +
                ", beans=" + beans +
                ", room='" + room + '\'' +
                ", status='" + status + '\'' +
                ", name='" + name + '\'' +
                ", head='" + head + '\'' +
                ", originHead='" + originHead + '\'' +
                ", banner='" + banner + '\'' +
                ", gender=" + gender +
                ", fbGender='" + fbGender + '\'' +
                ", age=" + age +
                ", country='" + country + '\'' +
                ", city='" + city + '\'' +
                ", phone='" + phone + '\'' +
                ", rid=" + rid +
                ", valid=" + valid +
                ", display='" + display + '\'' +
                ", viprecord='" + viprecord + '\'' +
                ", uid='" + uid + '\'' +
                ", loginType='" + loginType + '\'' +
                ", birthday='" + birthday + '\'' +
                ", push='" + push + '\'' +
                ", videoOption='" + videoOption + '\'' +
                ", area='" + area + '\'' +
                ", nlang='" + nlang + '\'' +
                ", lang='" + lang + '\'' +
                ", imsi='" + imsi + '\'' +
                ", imei='" + imei + '\'' +
                ", ioskey='" + ioskey + '\'' +
                ", androidId='" + androidId + '\'' +
                ", os='" + os + '\'' +
                ", mos='" + mos + '\'' +
                ", count='" + count + '\'' +
                ", vnumber='" + vnumber + '\'' +
                ", like=" + like +
                ", desc='" + desc + '\'' +
                ", followLimit='" + followLimit + '\'' +
                ", lastLogin=" + lastLogin +
                ", rideOption=" + rideOption +
                ", ulvl=" + ulvl +
                ", badge=" + badge +
                ", vlvl=" + vlvl +
                ", joinCartonId=" + joinCartonId +
                ", rippleUrl='" + rippleUrl + '\'' +
                ", smallIcon='" + smallIcon + '\'' +
                ", cartonTimes='" + cartonTimes + '\'' +
                ", sourceType=" + sourceType +
                ", joinCartonType=" + joinCartonType +
                ", isBeautifulRid=" + isBeautifulRid +
                ", devote='" + devote + '\'' +
                ", ip='" + ip + '\'' +
                ", tn_id='" + tn_id + '\'' +
                ", oid='" + oid + '\'' +
                ", promotionId='" + promotionId + '\'' +
                ", channel='" + channel + '\'' +
                ", appPackageName='" + appPackageName + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
