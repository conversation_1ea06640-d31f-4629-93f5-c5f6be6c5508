package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 朋友对象
 * <AUTHOR>
 * @date 2020/8/5
 */
@Document(collection = "friends")
public class Friends implements Serializable {

    private static final long serialVersionUID = -6205877885605031993L;
    @Field("uid_first")
    private String uidFirst;
    @Field("uid_second")
    private String uidSecond;
    private Integer source;
    @Field("friend_index")
    private String friendIndex;
    @Field("friend_relation")
    private Integer friendRelation;
    private Integer ctime;

    public String getUidFirst() {
        return uidFirst;
    }

    public void setUidFirst(String uidFirst) {
        this.uidFirst = uidFirst;
    }

    public String getUidSecond() {
        return uidSecond;
    }

    public void setUidSecond(String uidSecond) {
        this.uidSecond = uidSecond;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getFriendIndex() {
        return friendIndex;
    }

    public void setFriendIndex(String friendIndex) {
        this.friendIndex = friendIndex;
    }

    public Integer getFriendRelation() {
        return friendRelation;
    }

    public void setFriendRelation(Integer friendRelation) {
        this.friendRelation = friendRelation;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "Friends{" +
                "uidFirst='" + uidFirst + '\'' +
                ", uidSecond='" + uidSecond + '\'' +
                ", source=" + source +
                ", friendIndex='" + friendIndex + '\'' +
                ", friendRelation=" + friendRelation +
                ", ctime=" + ctime +
                '}';
    }
}
