package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.List;

/**
 * 礼物收发记录
 * <AUTHOR>
 * @date 2020/7/31
 */
@Document(collection = "gift_record_mg")
public class GiftRecordMg implements Serializable {

    private static final long serialVersionUID = 6944514348953550051L;
    // 礼物id
    @Field("gift_id")
    private Integer giftId;
    // 礼物名称
    @Field("gift_name")
    private String giftName;
    @Field("gift_number")
    private Integer giftNumber;
    // 礼物本金
    @Field("gift_price")
    private Integer giftPrice;
    @Field("from_uid")
    private String fromUid;
    private Integer os;
    @Field("to_uid")
    private String toUid;
    @Field("room_id")
    private String roomId;
    @Field("earn_beans")
    private Integer earnBeans;
    private Integer gtype;
    @Field("send_type")
    private Integer sendType;
    @Field("aid_list")
    private List<String> aidList;
    @Field("time_stamp")
    private Long timeStamp;

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public Integer getGiftNumber() {
        return giftNumber;
    }

    public void setGiftNumber(Integer giftNumber) {
        this.giftNumber = giftNumber;
    }

    public Integer getGiftPrice() {
        return giftPrice;
    }

    public void setGiftPrice(Integer giftPrice) {
        this.giftPrice = giftPrice;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public String getToUid() {
        return toUid;
    }

    public void setToUid(String toUid) {
        this.toUid = toUid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getEarnBeans() {
        return earnBeans;
    }

    public void setEarnBeans(Integer earnBeans) {
        this.earnBeans = earnBeans;
    }

    public Integer getGtype() {
        return gtype;
    }

    public void setGtype(Integer gtype) {
        this.gtype = gtype;
    }

    public Integer getSendType() {
        return sendType;
    }

    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }

    public List<String> getAidList() {
        return aidList;
    }

    public void setAidList(List<String> aidList) {
        this.aidList = aidList;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    @Override
    public String toString() {
        return "GiftRecordMg{" +
                "giftId=" + giftId +
                ", giftName=" + giftName +
                ", giftNumber=" + giftNumber +
                ", giftPrice=" + giftPrice +
                ", fromUid=" + fromUid +
                ", os=" + os +
                ", toUid=" + toUid +
                ", roomId=" + roomId +
                ", earnBeans=" + earnBeans +
                ", gtype=" + gtype +
                ", sendType=" + sendType +
                ", aidList=" + aidList +
                ", timeStamp=" + timeStamp +
                '}';
    }
}
