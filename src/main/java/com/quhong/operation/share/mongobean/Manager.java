package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
@Document(collection = "manager")
public class Manager implements Serializable {

    private static final long serialVersionUID = 3827586627674918752L;
    // 账号 必填
    private ObjectId _id;
    private String account = "";
    // 密码 必填
    private String pwd = "";
    private String icon = "";
    private Integer role = 0;
    @Field("last_login")
    private LastLogin lastLogin;
    private String ip = "";
    private Integer ctime = 0;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public LastLogin getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(LastLogin lastLogin) {
        this.lastLogin = lastLogin;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "Manager{" +
                "_id=" + _id +
                ", account='" + account + '\'' +
                ", pwd='" + pwd + '\'' +
                ", icon='" + icon + '\'' +
                ", role=" + role +
                ", lastLogin=" + lastLogin +
                ", ip='" + ip + '\'' +
                ", ctime=" + ctime +
                '}';
    }
}
