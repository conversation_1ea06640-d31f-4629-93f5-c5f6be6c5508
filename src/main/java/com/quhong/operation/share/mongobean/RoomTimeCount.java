package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/7/14
 */
@Document(collection = "room_time_count")
public class RoomTimeCount implements Serializable {

    private static final long serialVersionUID = 8124523212703311761L;

    // 用户id
    private String uid;

    // 当天时间 yyyy-MM-dd
    @Field("date_str")
    private String dateStr;

    // 当天在房时长统计秒数
    @Field("second_total")
    private Integer secondTotal;

    // 是否已经计算过经验值的标志位
    @Field("count_flag")
    private Integer countFlag;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public Integer getSecondTotal() {
        return secondTotal;
    }

    public void setSecondTotal(Integer secondTotal) {
        this.secondTotal = secondTotal;
    }

    public Integer getCountFlag() {
        return countFlag;
    }

    public void setCountFlag(Integer countFlag) {
        this.countFlag = countFlag;
    }

    @Override
    public String toString() {
        return "RoomTimeCount{" +
                "uid='" + uid + '\'' +
                ", dateStr='" + dateStr + '\'' +
                ", secondTotal=" + secondTotal +
                ", countFlag=" + countFlag +
                '}';
    }

}
