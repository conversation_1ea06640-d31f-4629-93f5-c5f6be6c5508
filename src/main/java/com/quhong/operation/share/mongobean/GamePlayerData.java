package com.quhong.operation.share.mongobean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GamePlayerData {
    private String uid;
    private int status; // 玩家状态 0 正常 1 托管 2 退出 3 所有棋子已到达终点
    private int hostingTime; // 托管时长
    private int side; // 1, 蓝 2 红 3 绿 4 黄

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getHostingTime() {
        return hostingTime;
    }

    public void setHostingTime(int hostingTime) {
        this.hostingTime = hostingTime;
    }

    public int getSide() {
        return side;
    }

    public void setSide(int side) {
        this.side = side;
    }
}
