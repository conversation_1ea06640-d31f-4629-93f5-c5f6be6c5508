package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/7/27
 */
@Document(collection = "room")
public class Room implements Serializable {

    private static final long serialVersionUID = -5956438439496896754L;
    // 房间id
    private String rid;
    // 房间名字
    private String name = "";
    // 房间头像
    private String head = "";
    // 房间主题
    private String topic = "";
    // 房间主题
    private Integer theme = 0;
    // 房间公告
    private String announce = "";
    // 所属区域
    private Integer area = 0;
    // 房间国家
    private String country = "";
    // 是否置顶房间
    private Integer top = 0;
    // 房主是否在线
    private Integer owner = 0;
    // 房主最近的开播时间
    @Field("btime")
    private Long bTime = 0L;
    // 是否展示在列表
    private Integer display = 0;
    // 房主下播时间
    private Integer etime = 0;
    // 房间会员人数
    @Field("memnum")
    private Integer memNum = 0;
    // 全局替换cdn_prefix，临时备份
    @Field("origin_head")
    private String originHead = "";
    // 1为默认开启状态，2为不开启状态
    @Field("room_pk")
    private Integer roomPk = 1;
    // 房间是否禁止公屏聊天1为禁止
    @Field("chat_locked")
    private Integer chatLocked = 0;
    // 房间是否禁止发图片1为禁止
    @Field("pic_locked")
    private Integer picLocked = 0;
    // 房间发消息，限制用户等级
    @Field("text_limit")
    private Integer textLimit = -1;
    // 房间信息修改时间
    private Integer mtime = 0;

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public Integer getTheme() {
        return theme;
    }

    public void setTheme(Integer theme) {
        this.theme = theme;
    }

    public String getAnnounce() {
        return announce;
    }

    public void setAnnounce(String announce) {
        this.announce = announce;
    }

    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getTop() {
        return top;
    }

    public void setTop(Integer top) {
        this.top = top;
    }

    public Integer getOwner() {
        return owner;
    }

    public void setOwner(Integer owner) {
        this.owner = owner;
    }

    public Long getbTime() {
        return bTime;
    }

    public void setbTime(Long bTime) {
        this.bTime = bTime;
    }

    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }

    public Integer getEtime() {
        return etime;
    }

    public void setEtime(Integer etime) {
        this.etime = etime;
    }

    public Integer getMemNum() {
        return memNum;
    }

    public void setMemNum(Integer memNum) {
        this.memNum = memNum;
    }

    public String getOriginHead() {
        return originHead;
    }

    public void setOriginHead(String originHead) {
        this.originHead = originHead;
    }

    public Integer getRoomPk() {
        return roomPk;
    }

    public void setRoomPk(Integer roomPk) {
        this.roomPk = roomPk;
    }

    public Integer getChatLocked() {
        return chatLocked;
    }

    public void setChatLocked(Integer chatLocked) {
        this.chatLocked = chatLocked;
    }

    public Integer getPicLocked() {
        return picLocked;
    }

    public void setPicLocked(Integer picLocked) {
        this.picLocked = picLocked;
    }

    public Integer getTextLimit() {
        return textLimit;
    }

    public void setTextLimit(Integer textLimit) {
        this.textLimit = textLimit;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "Room{" +
                "rid='" + rid + '\'' +
                ", name='" + name + '\'' +
                ", head='" + head + '\'' +
                ", topic='" + topic + '\'' +
                ", theme=" + theme +
                ", announce='" + announce + '\'' +
                ", area=" + area +
                ", country='" + country + '\'' +
                ", top=" + top +
                ", owner=" + owner +
                ", bTime=" + bTime +
                ", display=" + display +
                ", etime=" + etime +
                ", memNum=" + memNum +
                ", originHead='" + originHead + '\'' +
                ", roomPk=" + roomPk +
                ", chatLocked=" + chatLocked +
                ", picLocked=" + picLocked +
                ", textLimit=" + textLimit +
                ", mtime=" + mtime +
                '}';
    }
}
