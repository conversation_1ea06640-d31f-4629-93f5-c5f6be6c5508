package com.quhong.operation.share.condition;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
public class RechargeCouponCondition {
    /**
     * 用户ID
     */
    private String rid;
    /**
     * 优惠卷ID
     */
    private String couponId;
    /**
     * 额外奖励
     */
    private BigDecimal extraProp;
    /**
     * 来源
     */
    private String source;

    private Integer status;
    private String search;
    private Integer page;  //第几页
    private Integer pageSize;  // 每页数量

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public BigDecimal getExtraProp() {
        return extraProp;
    }

    public void setExtraProp(BigDecimal extraProp) {
        this.extraProp = extraProp;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
