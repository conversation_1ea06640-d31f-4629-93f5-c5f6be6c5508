package com.quhong.operation.share.condition;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
public class QuizActivityCondition extends HttpEnvData {

    private Integer id;
    private String gid;
    private Integer status;
    private Integer quizType;
    private String acName;
    private Integer page;
    private Integer pageSize;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAcName() {
        return acName;
    }

    public void setAcName(String acName) {
        this.acName = acName;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getQuizType() {
        return quizType;
    }

    public void setQuizType(Integer quizType) {
        this.quizType = quizType;
    }
}
