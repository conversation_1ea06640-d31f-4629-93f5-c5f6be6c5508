package com.quhong.operation.share.condition;

import java.util.List;

public class RookieRoomUserConfigCondition {

    private String search;
    private int type; // 1 迎新用户 2 迎新房间
    private Integer page;  //第几页
    private Integer pageSize;  // 每页数量
    private Integer cmdType; // 1 新增 2删除

    private String rids; // 增加的rid
    private List<String> ridList ; // 非接口字段

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getRids() {
        return rids;
    }

    public void setRids(String rids) {
        this.rids = rids;
    }

    public List<String> getRidList() {
        return ridList;
    }

    public void setRidList(List<String> ridList) {
        this.ridList = ridList;
    }

    public Integer getCmdType() {
        return cmdType;
    }

    public void setCmdType(Integer cmdType) {
        this.cmdType = cmdType;
    }
}
