package com.quhong.operation.share.condition;

import com.alibaba.fastjson.JSONObject;

public class EmojiCondition {

    private Integer emojiType;
    private String emojiConfigId;
    private Integer status;
    private String search;
    private Integer page;  //第几页
    private Integer pageSize;  // 每页数量

    public Integer getEmojiType() {
        return emojiType;
    }

    public void setEmojiType(Integer emojiType) {
        this.emojiType = emojiType;
    }

    public String getEmojiConfigId() {
        return emojiConfigId;
    }

    public void setEmojiConfigId(String emojiConfigId) {
        this.emojiConfigId = emojiConfigId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
