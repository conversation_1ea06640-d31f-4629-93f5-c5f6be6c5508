package com.quhong.operation.share.condition;

public class StaffBindUserCondition {

    private String startDate;
    private String endDate;
    private String search;
    private int staffId;
    private int status = -1;
    private int bindType;
    private int sortCtime = -1;
    private int last30DaySort = -1;
    private int last60DaySort = -1;
    private int last90DaySort = -1;
    private int totalDaySort = -1;
    private Integer page;  //第几页
    private Integer pageSize;  // 每页数量

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public int getStaffId() {
        return staffId;
    }

    public void setStaffId(int staffId) {
        this.staffId = staffId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getBindType() {
        return bindType;
    }

    public void setBindType(int bindType) {
        this.bindType = bindType;
    }

    public int getSortCtime() {
        return sortCtime;
    }

    public void setSortCtime(int sortCtime) {
        this.sortCtime = sortCtime;
    }

    public int getLast30DaySort() {
        return last30DaySort;
    }

    public void setLast30DaySort(int last30DaySort) {
        this.last30DaySort = last30DaySort;
    }

    public int getLast60DaySort() {
        return last60DaySort;
    }

    public void setLast60DaySort(int last60DaySort) {
        this.last60DaySort = last60DaySort;
    }

    public int getLast90DaySort() {
        return last90DaySort;
    }

    public void setLast90DaySort(int last90DaySort) {
        this.last90DaySort = last90DaySort;
    }

    public int getTotalDaySort() {
        return totalDaySort;
    }

    public void setTotalDaySort(int totalDaySort) {
        this.totalDaySort = totalDaySort;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
