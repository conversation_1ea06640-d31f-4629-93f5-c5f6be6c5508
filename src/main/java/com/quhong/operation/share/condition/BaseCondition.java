package com.quhong.operation.share.condition;

public class BaseCondition {

    protected int status;         // 状态 -1 全部 0:无效 1:有效
    protected String search;      // 搜索字段
    protected Integer page;       // 第几页
    protected Integer pageSize;   // 每页数量

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getPage() {
        return page != null && page > 0 ? page : 1;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize != null ? pageSize : 10;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "BaseCondition{" +
                "status=" + status +
                ", search='" + search + '\'' +
                ", page=" + page +
                ", pageSize=" + pageSize +
                '}';
    }
}
