package com.quhong.operation.share.condition;

public class FeedbackCondition extends BaseCondition {

    private String start;
    private String end;
    private String origin;
    private String feedbackId;
    private Integer userType;     // -1 全部  0: 非付费用户  1: 付费用户
    private String problemSelect;  // 反馈模块
    private Integer feedbackNum;    // 反馈次数

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getFeedbackId() {
        return feedbackId;
    }

    public void setFeedbackId(String feedbackId) {
        this.feedbackId = feedbackId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getProblemSelect() {
        return problemSelect;
    }

    public void setProblemSelect(String problemSelect) {
        this.problemSelect = problemSelect;
    }

    public Integer getFeedbackNum() {
        return feedbackNum;
    }

    public void setFeedbackNum(Integer feedbackNum) {
        this.feedbackNum = feedbackNum;
    }
}
