package com.quhong.operation.share.condition;


import java.util.List;

public class ReportCallBackUserCondition extends BaseCondition {
    private String rids; // 增加的rid
    private Integer staffId; // 员工id
    private String addSubmitId; // 提交的id，验证的时候生成
    private Integer bindType; // 1 召回 2 拓新
    private  List<String> ridList ; // 非接口字段
    private  String tnId ; //
    private  Integer itemId ; // 删除时使用

    public String getAddSubmitId() {
        return addSubmitId;
    }

    public void setAddSubmitId(String addSubmitId) {
        this.addSubmitId = addSubmitId;
    }

    public String getRids() {
        return rids;
    }

    public void setRids(String rids) {
        this.rids = rids;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public Integer getBindType() {
        return bindType;
    }

    public void setBindType(Integer bindType) {
        this.bindType = bindType;
    }

    public List<String> getRidList() {
        return ridList;
    }

    public void setRidList(List<String> ridList) {
        this.ridList = ridList;
    }

    public String getTnId() {
        return tnId;
    }

    public void setTnId(String tnId) {
        this.tnId = tnId;
    }

    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }
}
