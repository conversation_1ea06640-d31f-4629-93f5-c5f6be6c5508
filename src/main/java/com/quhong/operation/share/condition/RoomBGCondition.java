package com.quhong.operation.share.condition;

import com.alibaba.fastjson.annotation.JSONField;

public class RoomBGCondition {

    private Integer bgType;
    private Integer status;
    private String search;
    private Integer page;  //第几页

    private Integer page_size;  // 每页数量

    public Integer getBgType() {
        return bgType;
    }

    public void setBgType(Integer bgType) {
        this.bgType = bgType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }


    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }

    @Override
    public String toString() {
        return "RoomBGCondition{" +
                "bgType=" + bgType +
                ", status=" + status +
                ", search='" + search + '\'' +
                ", page=" + page +
                ", page_size=" + page_size +
                '}';
    }
}
