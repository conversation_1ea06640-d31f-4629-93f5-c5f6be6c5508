package com.quhong.operation.share.condition;

public class ItemCondition {

    private Integer resType;
    private Integer itemType;
    private Integer status;
    private String search;
    private Integer page;  //第几页
    private Integer pageSize;  // 每页数量
    private Integer hot = -1;  // 是否预加载
    private Integer fusion = -1;  // 是否融合动画

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public Integer getItemType() {
        return itemType;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getHot() {
        return hot;
    }

    public void setHot(Integer hot) {
        this.hot = hot;
    }

    public Integer getFusion() {
        return fusion;
    }

    public void setFusion(Integer fusion) {
        this.fusion = fusion;
    }

    @Override
    public String toString() {
        return "ItemCondition{" +
                "resType=" + resType +
                ", itemType=" + itemType +
                ", status=" + status +
                ", search='" + search + '\'' +
                ", page=" + page +
                ", pageSize=" + pageSize +
                ", hot=" + hot +
                ", fusion=" + fusion +
                '}';
    }
}
