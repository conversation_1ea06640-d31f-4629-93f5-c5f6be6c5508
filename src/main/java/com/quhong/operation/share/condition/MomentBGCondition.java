package com.quhong.operation.share.condition;

public class MomentBGCondition {
    private String search;
    private Integer status;
    private Integer backgroundType;
    private Integer costType;
    private Integer page;  //第几页
    private Integer pageSize;  // 每页数量

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getBackgroundType() {
        return backgroundType;
    }

    public void setBackgroundType(Integer backgroundType) {
        this.backgroundType = backgroundType;
    }

    public Integer getCostType() {
        return costType;
    }

    public void setCostType(Integer costType) {
        this.costType = costType;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "MomentBGCondition{" +
                "search='" + search + '\'' +
                ", status=" + status +
                ", backgroundType=" + backgroundType +
                ", costType=" + costType +
                ", page=" + page +
                ", pageSize=" + pageSize +
                '}';
    }
}
