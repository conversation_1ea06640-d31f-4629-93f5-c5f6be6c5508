package com.quhong.operation.share.condition;

public class ActivityTemplateCondition {
    private String activityId;
    private String acNameEn;
    private Integer page;
    private Integer pageSize;
    private int templateType;

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public int getTemplateType() {
        return templateType;
    }

    public void setTemplateType(int templateType) {
        this.templateType = templateType;
    }
}
