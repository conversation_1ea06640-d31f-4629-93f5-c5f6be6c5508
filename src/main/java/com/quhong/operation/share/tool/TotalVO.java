package com.quhong.operation.share.tool;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/8/3
 */
public class TotalVO implements Serializable {

    private static final long serialVersionUID = -1131968564974339298L;
    private String channel;
    private String uid;
    private Long personNum = 0L;
    private Long countNum = 0L;
    private BigDecimal sumNum = BigDecimal.ZERO;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Long getPersonNum() {
        return personNum;
    }

    public void setPersonNum(Long personNum) {
        this.personNum = personNum;
    }

    public Long getCountNum() {
        return countNum;
    }

    public void setCountNum(Long countNum) {
        this.countNum = countNum;
    }

    public BigDecimal getSumNum() {
        return sumNum;
    }

    public void setSumNum(BigDecimal sumNum) {
        this.sumNum = sumNum;
    }

    public void addCount () {
        this.countNum++;
    }

    @Override
    public String toString() {
        return "TotalVO{" +
                "channel='" + channel + '\'' +
                ", uid='" + uid + '\'' +
                ", personNum=" + personNum +
                ", countNum=" + countNum +
                ", sumNum=" + sumNum +
                '}';
    }
}
