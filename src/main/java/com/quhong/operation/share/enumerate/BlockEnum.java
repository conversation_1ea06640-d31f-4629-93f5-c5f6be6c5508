package com.quhong.operation.share.enumerate;

/**
 * 拉黑类型枚举
 */
public enum BlockEnum {
    RELEASE(0, "释放"),
    WARN(1, "警告"),
    FREEZE(2, "冻结"),
    BLOCKED(3, "封禁");

    private int code;
    private String name;

    BlockEnum(int code, String name){
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(int value) {
        switch (value) {
            case 0: return RELEASE.name;
            case 1: return WARN.name;
            case 2: return FREEZE.name;
            case 3: return BLOCKED.name;
            default: return null;
        }
    }

}
