package com.quhong.operation.share.data;

import com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 * https://help.ishumei.com/docs/tx/deviceRisk/newest/developDoc/
 */
public class SMOpDeviceRespondsData {

    private int code;
    private String message;
    private String requestId;
    private int profileExist;
    private String tokenLabels;
    private DeviceLabels deviceLabels;
    private String tokenProfileLabels;
    private String tokenRiskLabels;
    private String deviceRiskLabels;
    private String devicePrimayInfo;
    private String jsonText;

    public static class DeviceLabels {
        private String id;
        private long last_active_ts;
        private FakeDevice fake_device;
        private DeviceSuspiciousLabels device_suspicious_labels;
        private String device_active_info;
        private String monkey_device;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public long getLast_active_ts() {
            return last_active_ts;
        }

        public void setLast_active_ts(long last_active_ts) {
            this.last_active_ts = last_active_ts;
        }

        public FakeDevice getFake_device() {
            return fake_device;
        }

        public void setFake_device(FakeDevice fake_device) {
            this.fake_device = fake_device;
        }

        public DeviceSuspiciousLabels getDevice_suspicious_labels() {
            return device_suspicious_labels;
        }

        public void setDevice_suspicious_labels(DeviceSuspiciousLabels device_suspicious_labels) {
            this.device_suspicious_labels = device_suspicious_labels;
        }

        public String getDevice_active_info() {
            return device_active_info;
        }

        public void setDevice_active_info(String device_active_info) {
            this.device_active_info = device_active_info;
        }

        public String getMonkey_device() {
            return monkey_device;
        }

        public void setMonkey_device(String monkey_device) {
            this.monkey_device = monkey_device;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }


    public static class DeviceSuspiciousLabels {
        private int b_acc;
        private long b_acc_last_ts;
        private int b_adb_enable;
        private long b_adb_enable_last_ts;
        private int b_alter_apps;
        private long b_alter_apps_last_ts;
        private int b_alter_loc;
        private long b_alter_loc_last_ts;
        private int b_camera_hook;
        private int b_console;
        private long b_console_last_ts;
        private int b_debuggable;
        private long b_debuggable_last_ts;
        private int b_device_proxy;
        private long b_device_proxy_last_ts;
        private int b_game_cheat_apps;
        private int b_headless;
        private int b_hook;
        private long b_hook_last_ts;
        private int b_icloud;
        private int b_idle;
        private int b_low_active;
        private long b_low_active_last_ts;
        private int b_low_osver;
        private int b_manufacture;
        private long b_manufacture_last_ts;
        private int b_monkey_apps;
        private long b_monkey_apps_last_ts;
        private int b_multi_boxing_apps;
        private long b_multi_boxing_apps_last_ts;
        private int b_non_appstore;
        private int b_old_model;
        private int b_remote_control_apps;
        private long b_remote_control_apps_last_ts;
        private int b_repackage;
        private long b_repackage_last_ts;
        private int b_reset;
        private long b_reset_last_ts;
        private int b_root;
        private long b_sim_last_ts;
        private int b_sim;
        private long b_root_last_ts;
        private int b_sms_code;
        private int b_vpn;
        private long b_vpn_last_ts;
        private int b_vpn_apps;
        private int b_wx_code;
        private String b_malware_installed;

        public int getB_acc() {
            return b_acc;
        }

        public void setB_acc(int b_acc) {
            this.b_acc = b_acc;
        }

        public int getB_adb_enable() {
            return b_adb_enable;
        }

        public void setB_adb_enable(int b_adb_enable) {
            this.b_adb_enable = b_adb_enable;
        }

        public int getB_alter_apps() {
            return b_alter_apps;
        }

        public void setB_alter_apps(int b_alter_apps) {
            this.b_alter_apps = b_alter_apps;
        }

        public int getB_alter_loc() {
            return b_alter_loc;
        }

        public void setB_alter_loc(int b_alter_loc) {
            this.b_alter_loc = b_alter_loc;
        }

        public int getB_camera_hook() {
            return b_camera_hook;
        }

        public void setB_camera_hook(int b_camera_hook) {
            this.b_camera_hook = b_camera_hook;
        }

        public int getB_console() {
            return b_console;
        }

        public void setB_console(int b_console) {
            this.b_console = b_console;
        }

        public int getB_debuggable() {
            return b_debuggable;
        }

        public void setB_debuggable(int b_debuggable) {
            this.b_debuggable = b_debuggable;
        }

        public int getB_device_proxy() {
            return b_device_proxy;
        }

        public void setB_device_proxy(int b_device_proxy) {
            this.b_device_proxy = b_device_proxy;
        }

        public int getB_game_cheat_apps() {
            return b_game_cheat_apps;
        }

        public void setB_game_cheat_apps(int b_game_cheat_apps) {
            this.b_game_cheat_apps = b_game_cheat_apps;
        }

        public int getB_headless() {
            return b_headless;
        }

        public void setB_headless(int b_headless) {
            this.b_headless = b_headless;
        }

        public int getB_hook() {
            return b_hook;
        }

        public void setB_hook(int b_hook) {
            this.b_hook = b_hook;
        }

        public int getB_icloud() {
            return b_icloud;
        }

        public void setB_icloud(int b_icloud) {
            this.b_icloud = b_icloud;
        }

        public int getB_idle() {
            return b_idle;
        }

        public void setB_idle(int b_idle) {
            this.b_idle = b_idle;
        }

        public int getB_low_active() {
            return b_low_active;
        }

        public void setB_low_active(int b_low_active) {
            this.b_low_active = b_low_active;
        }

        public long getB_low_active_last_ts() {
            return b_low_active_last_ts;
        }

        public void setB_low_active_last_ts(long b_low_active_last_ts) {
            this.b_low_active_last_ts = b_low_active_last_ts;
        }

        public int getB_low_osver() {
            return b_low_osver;
        }

        public void setB_low_osver(int b_low_osver) {
            this.b_low_osver = b_low_osver;
        }

        public int getB_manufacture() {
            return b_manufacture;
        }

        public void setB_manufacture(int b_manufacture) {
            this.b_manufacture = b_manufacture;
        }

        public int getB_monkey_apps() {
            return b_monkey_apps;
        }

        public void setB_monkey_apps(int b_monkey_apps) {
            this.b_monkey_apps = b_monkey_apps;
        }

        public int getB_multi_boxing_apps() {
            return b_multi_boxing_apps;
        }

        public void setB_multi_boxing_apps(int b_multi_boxing_apps) {
            this.b_multi_boxing_apps = b_multi_boxing_apps;
        }

        public int getB_non_appstore() {
            return b_non_appstore;
        }

        public void setB_non_appstore(int b_non_appstore) {
            this.b_non_appstore = b_non_appstore;
        }

        public int getB_old_model() {
            return b_old_model;
        }

        public void setB_old_model(int b_old_model) {
            this.b_old_model = b_old_model;
        }

        public int getB_remote_control_apps() {
            return b_remote_control_apps;
        }

        public void setB_remote_control_apps(int b_remote_control_apps) {
            this.b_remote_control_apps = b_remote_control_apps;
        }

        public int getB_repackage() {
            return b_repackage;
        }

        public void setB_repackage(int b_repackage) {
            this.b_repackage = b_repackage;
        }

        public int getB_reset() {
            return b_reset;
        }

        public void setB_reset(int b_reset) {
            this.b_reset = b_reset;
        }

        public int getB_root() {
            return b_root;
        }

        public void setB_root(int b_root) {
            this.b_root = b_root;
        }

        public int getB_sim() {
            return b_sim;
        }

        public void setB_sim(int b_sim) {
            this.b_sim = b_sim;
        }

        public int getB_sms_code() {
            return b_sms_code;
        }

        public void setB_sms_code(int b_sms_code) {
            this.b_sms_code = b_sms_code;
        }

        public int getB_vpn() {
            return b_vpn;
        }

        public void setB_vpn(int b_vpn) {
            this.b_vpn = b_vpn;
        }

        public int getB_vpn_apps() {
            return b_vpn_apps;
        }

        public void setB_vpn_apps(int b_vpn_apps) {
            this.b_vpn_apps = b_vpn_apps;
        }

        public int getB_wx_code() {
            return b_wx_code;
        }

        public void setB_wx_code(int b_wx_code) {
            this.b_wx_code = b_wx_code;
        }

        public String getB_malware_installed() {
            return b_malware_installed;
        }

        public void setB_malware_installed(String b_malware_installed) {
            this.b_malware_installed = b_malware_installed;
        }


        public long getB_acc_last_ts() {
            return b_acc_last_ts;
        }

        public void setB_acc_last_ts(long b_acc_last_ts) {
            this.b_acc_last_ts = b_acc_last_ts;
        }

        public long getB_root_last_ts() {
            return b_root_last_ts;
        }

        public void setB_root_last_ts(long b_root_last_ts) {
            this.b_root_last_ts = b_root_last_ts;
        }

        public long getB_vpn_last_ts() {
            return b_vpn_last_ts;
        }

        public void setB_vpn_last_ts(long b_vpn_last_ts) {
            this.b_vpn_last_ts = b_vpn_last_ts;
        }

        public long getB_adb_enable_last_ts() {
            return b_adb_enable_last_ts;
        }

        public void setB_adb_enable_last_ts(long b_adb_enable_last_ts) {
            this.b_adb_enable_last_ts = b_adb_enable_last_ts;
        }

        public long getB_alter_apps_last_ts() {
            return b_alter_apps_last_ts;
        }

        public void setB_alter_apps_last_ts(long b_alter_apps_last_ts) {
            this.b_alter_apps_last_ts = b_alter_apps_last_ts;
        }

        public long getB_alter_loc_last_ts() {
            return b_alter_loc_last_ts;
        }

        public void setB_alter_loc_last_ts(long b_alter_loc_last_ts) {
            this.b_alter_loc_last_ts = b_alter_loc_last_ts;
        }

        public long getB_console_last_ts() {
            return b_console_last_ts;
        }

        public void setB_console_last_ts(long b_console_last_ts) {
            this.b_console_last_ts = b_console_last_ts;
        }

        public long getB_debuggable_last_ts() {
            return b_debuggable_last_ts;
        }

        public void setB_debuggable_last_ts(long b_debuggable_last_ts) {
            this.b_debuggable_last_ts = b_debuggable_last_ts;
        }

        public long getB_device_proxy_last_ts() {
            return b_device_proxy_last_ts;
        }

        public void setB_device_proxy_last_ts(long b_device_proxy_last_ts) {
            this.b_device_proxy_last_ts = b_device_proxy_last_ts;
        }

        public long getB_hook_last_ts() {
            return b_hook_last_ts;
        }

        public void setB_hook_last_ts(long b_hook_last_ts) {
            this.b_hook_last_ts = b_hook_last_ts;
        }

        public long getB_manufacture_last_ts() {
            return b_manufacture_last_ts;
        }

        public void setB_manufacture_last_ts(long b_manufacture_last_ts) {
            this.b_manufacture_last_ts = b_manufacture_last_ts;
        }

        public long getB_monkey_apps_last_ts() {
            return b_monkey_apps_last_ts;
        }

        public void setB_monkey_apps_last_ts(long b_monkey_apps_last_ts) {
            this.b_monkey_apps_last_ts = b_monkey_apps_last_ts;
        }

        public long getB_multi_boxing_apps_last_ts() {
            return b_multi_boxing_apps_last_ts;
        }

        public void setB_multi_boxing_apps_last_ts(long b_multi_boxing_apps_last_ts) {
            this.b_multi_boxing_apps_last_ts = b_multi_boxing_apps_last_ts;
        }

        public long getB_remote_control_apps_last_ts() {
            return b_remote_control_apps_last_ts;
        }

        public void setB_remote_control_apps_last_ts(long b_remote_control_apps_last_ts) {
            this.b_remote_control_apps_last_ts = b_remote_control_apps_last_ts;
        }

        public long getB_repackage_last_ts() {
            return b_repackage_last_ts;
        }

        public void setB_repackage_last_ts(long b_repackage_last_ts) {
            this.b_repackage_last_ts = b_repackage_last_ts;
        }

        public long getB_reset_last_ts() {
            return b_reset_last_ts;
        }

        public void setB_reset_last_ts(long b_reset_last_ts) {
            this.b_reset_last_ts = b_reset_last_ts;
        }

        public long getB_sim_last_ts() {
            return b_sim_last_ts;
        }

        public void setB_sim_last_ts(long b_sim_last_ts) {
            this.b_sim_last_ts = b_sim_last_ts;
        }
    }

    public static class FakeDevice {
        private int b_altered;
        private long b_altered_last_ts;
        private int b_cloud_device;
        private long b_cloud_device_last_ts;
        private int b_faker;
        private long b_faker_last_ts;
        private int b_farmer;
        private long b_farmer_last_ts;
        private int b_multi_boxing;
        private long b_multi_boxing_last_ts;
        private int b_multi_boxing_by_app;
        private long b_multi_boxing_by_app_last_ts;
        private int b_multi_boxing_by_os;
        private long b_multi_boxing_by_os_last_ts;
        private int b_offerwall;
        private long b_offerwall_last_ts;
        private int b_pc_emulator;
        private long b_pc_emulator_last_ts;
        private int b_phone_emulator;
        private long b_phone_emulator_last_ts;
        private String other;

        public int getB_altered() {
            return b_altered;
        }

        public void setB_altered(int b_altered) {
            this.b_altered = b_altered;
        }

        public int getB_cloud_device() {
            return b_cloud_device;
        }

        public void setB_cloud_device(int b_cloud_device) {
            this.b_cloud_device = b_cloud_device;
        }

        public int getB_faker() {
            return b_faker;
        }

        public void setB_faker(int b_faker) {
            this.b_faker = b_faker;
        }

        public int getB_farmer() {
            return b_farmer;
        }

        public void setB_farmer(int b_farmer) {
            this.b_farmer = b_farmer;
        }

        public int getB_multi_boxing() {
            return b_multi_boxing;
        }

        public void setB_multi_boxing(int b_multi_boxing) {
            this.b_multi_boxing = b_multi_boxing;
        }

        public int getB_multi_boxing_by_app() {
            return b_multi_boxing_by_app;
        }

        public void setB_multi_boxing_by_app(int b_multi_boxing_by_app) {
            this.b_multi_boxing_by_app = b_multi_boxing_by_app;
        }

        public int getB_multi_boxing_by_os() {
            return b_multi_boxing_by_os;
        }

        public void setB_multi_boxing_by_os(int b_multi_boxing_by_os) {
            this.b_multi_boxing_by_os = b_multi_boxing_by_os;
        }

        public int getB_offerwall() {
            return b_offerwall;
        }

        public void setB_offerwall(int b_offerwall) {
            this.b_offerwall = b_offerwall;
        }

        public int getB_pc_emulator() {
            return b_pc_emulator;
        }

        public void setB_pc_emulator(int b_pc_emulator) {
            this.b_pc_emulator = b_pc_emulator;
        }

        public int getB_phone_emulator() {
            return b_phone_emulator;
        }

        public void setB_phone_emulator(int b_phone_emulator) {
            this.b_phone_emulator = b_phone_emulator;
        }

        public String getOther() {
            return other;
        }

        public void setOther(String other) {
            this.other = other;
        }

        public long getB_altered_last_ts() {
            return b_altered_last_ts;
        }

        public void setB_altered_last_ts(long b_altered_last_ts) {
            this.b_altered_last_ts = b_altered_last_ts;
        }

        public long getB_cloud_device_last_ts() {
            return b_cloud_device_last_ts;
        }

        public void setB_cloud_device_last_ts(long b_cloud_device_last_ts) {
            this.b_cloud_device_last_ts = b_cloud_device_last_ts;
        }

        public long getB_faker_last_ts() {
            return b_faker_last_ts;
        }

        public void setB_faker_last_ts(long b_faker_last_ts) {
            this.b_faker_last_ts = b_faker_last_ts;
        }

        public long getB_farmer_last_ts() {
            return b_farmer_last_ts;
        }

        public void setB_farmer_last_ts(long b_farmer_last_ts) {
            this.b_farmer_last_ts = b_farmer_last_ts;
        }

        public long getB_multi_boxing_last_ts() {
            return b_multi_boxing_last_ts;
        }

        public void setB_multi_boxing_last_ts(long b_multi_boxing_last_ts) {
            this.b_multi_boxing_last_ts = b_multi_boxing_last_ts;
        }

        public long getB_multi_boxing_by_app_last_ts() {
            return b_multi_boxing_by_app_last_ts;
        }

        public void setB_multi_boxing_by_app_last_ts(long b_multi_boxing_by_app_last_ts) {
            this.b_multi_boxing_by_app_last_ts = b_multi_boxing_by_app_last_ts;
        }

        public long getB_multi_boxing_by_os_last_ts() {
            return b_multi_boxing_by_os_last_ts;
        }

        public void setB_multi_boxing_by_os_last_ts(long b_multi_boxing_by_os_last_ts) {
            this.b_multi_boxing_by_os_last_ts = b_multi_boxing_by_os_last_ts;
        }

        public long getB_offerwall_last_ts() {
            return b_offerwall_last_ts;
        }

        public void setB_offerwall_last_ts(long b_offerwall_last_ts) {
            this.b_offerwall_last_ts = b_offerwall_last_ts;
        }

        public long getB_pc_emulator_last_ts() {
            return b_pc_emulator_last_ts;
        }

        public void setB_pc_emulator_last_ts(long b_pc_emulator_last_ts) {
            this.b_pc_emulator_last_ts = b_pc_emulator_last_ts;
        }

        public long getB_phone_emulator_last_ts() {
            return b_phone_emulator_last_ts;
        }

        public void setB_phone_emulator_last_ts(long b_phone_emulator_last_ts) {
            this.b_phone_emulator_last_ts = b_phone_emulator_last_ts;
        }
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public int getProfileExist() {
        return profileExist;
    }

    public void setProfileExist(int profileExist) {
        this.profileExist = profileExist;
    }

    public String getTokenLabels() {
        return tokenLabels;
    }

    public void setTokenLabels(String tokenLabels) {
        this.tokenLabels = tokenLabels;
    }

    public DeviceLabels getDeviceLabels() {
        return deviceLabels;
    }

    public void setDeviceLabels(DeviceLabels deviceLabels) {
        this.deviceLabels = deviceLabels;
    }

    public String getTokenProfileLabels() {
        return tokenProfileLabels;
    }

    public void setTokenProfileLabels(String tokenProfileLabels) {
        this.tokenProfileLabels = tokenProfileLabels;
    }

    public String getTokenRiskLabels() {
        return tokenRiskLabels;
    }

    public void setTokenRiskLabels(String tokenRiskLabels) {
        this.tokenRiskLabels = tokenRiskLabels;
    }

    public String getDeviceRiskLabels() {
        return deviceRiskLabels;
    }

    public void setDeviceRiskLabels(String deviceRiskLabels) {
        this.deviceRiskLabels = deviceRiskLabels;
    }

    public String getDevicePrimayInfo() {
        return devicePrimayInfo;
    }

    public void setDevicePrimayInfo(String devicePrimayInfo) {
        this.devicePrimayInfo = devicePrimayInfo;
    }

    public String getJsonText() {
        return jsonText;
    }

    public void setJsonText(String jsonText) {
        this.jsonText = jsonText;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
