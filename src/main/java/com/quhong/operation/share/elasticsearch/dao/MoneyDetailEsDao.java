package com.quhong.operation.share.elasticsearch.dao;

import com.quhong.operation.share.elasticsearch.data.MoneyDetailEs;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
@Component
public class MoneyDetailEsDao extends EsMonthShardingDao<MoneyDetailEs> {

    private final static Logger logger = LoggerFactory.getLogger(MoneyDetailEsDao.class);

    public MoneyDetailEsDao() {
        super("money_detail2_es", MoneyDetailEs.class);
    }

    public List<MoneyDetailEs> getMyMoneyDetails(String uid, int page, int size) {
        try {
            int sPage = page > 0 ? page - 1 : 0;
            int sSize = size > 0 ? size : 20;
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("uid", uid));
            SearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withIndices(getIndexNames(-3))
                    .withPageable(PageRequest.of(sPage, sSize, Sort.Direction.DESC, "mtime"))
                    .build();
            return readEsTemplate.queryForList(searchQuery, MoneyDetailEs.class);
        } catch (Exception e) {
            logger.error("getMyVisitDetails, uid={} msg={}", uid, e.getMessage(), e);
            return null;
        }
    }

    public List<MoneyDetailEs> searchUserMoneyDetails(String uid, Integer filterBeans, Integer filterType, int page, int size) {
        try {
            int sPage = page > 0 ? page - 1 : 0;
            int sSize = size > 0 ? size : 20;
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("uid", uid));
            if (null != filterBeans && filterBeans != 0) {
                if (filterBeans > 0) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("changed").gte(filterBeans));
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("changed").lte(filterBeans));
                }
            } else {
                if (filterType == 1) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("changed").gt(0));
                } else if (filterType == 2) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("changed").lt(0));
                }
            }
            SearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withIndices(getIndexNames(-3))
                    .withPageable(PageRequest.of(sPage, sSize, Sort.Direction.DESC, "mtime"))
                    .build();
            return readEsTemplate.queryForList(searchQuery, MoneyDetailEs.class);
        } catch (Exception e) {
            logger.error("searchUserMoneyDetails, uid={} {}", uid, e.getMessage(), e);
            return null;
        }
    }

    public String[] getMoneyDetailIndexNames(int offset) {
        return getIndexNames(-3);
    }
}
