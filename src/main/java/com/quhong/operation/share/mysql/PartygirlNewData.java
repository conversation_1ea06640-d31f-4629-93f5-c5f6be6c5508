package com.quhong.operation.share.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_partygirl_new")
public class PartygirlNewData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String uid;

    /**
     * 1有效0无效
     */
    private Integer status;

    @TableField("task_head")
    private Integer taskHead;

    @TableField("task_notice")
    private Integer taskNotice;

    @TableField("notice_status")
    private Integer noticeStatus;

    @TableField("c_time")
    private Integer ctime;

    @TableField("m_time")
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getTaskHead() {
        return taskHead;
    }

    public void setTaskHead(Integer taskHead) {
        this.taskHead = taskHead;
    }

    public Integer getTaskNotice() {
        return taskNotice;
    }

    public void setTaskNotice(Integer taskNotice) {
        this.taskNotice = taskNotice;
    }

    public Integer getNoticeStatus() {
        return noticeStatus;
    }

    public void setNoticeStatus(Integer noticeStatus) {
        this.noticeStatus = noticeStatus;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
