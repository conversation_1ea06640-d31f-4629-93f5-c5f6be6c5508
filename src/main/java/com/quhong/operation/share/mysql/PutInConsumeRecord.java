package com.quhong.operation.share.mysql;

import java.io.Serializable;

public class PutInConsumeRecord implements Serializable {
    // 自增id
    private Long id;
    // 推广渠道
    private String medium;
    // 广告系列名称
    private String campaign;
    // 花费金额
    private Float adCostMoney;
    // 推广包的系统
    private String pkgOs;
    // 创建时间
    private Integer ctime;
    // 更新时间
    private Integer mtime;
    // 日期
    private String date; // yyyy-MM-dd


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMedium() {
        return medium;
    }

    public void setMedium(String medium) {
        this.medium = medium;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public Float getAdCostMoney() {
        return adCostMoney;
    }

    public void setAdCostMoney(Float adCostMoney) {
        this.adCostMoney = adCostMoney;
    }

    public String getPkgOs() {
        return pkgOs;
    }

    public void setPkgOs(String pkgOs) {
        this.pkgOs = pkgOs;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}
