package com.quhong.operation.share.mysql;

public class VideoLogData {

    private Integer id;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 视频id
     */
    private String videoId;

    /**
     * 视频标题
     */
    private String videoTitle;

    /**
     * 视屏时长
     */
    private Integer duration;

    /**
     * 播放时长
     */
    private Integer playTime;

    /**
     * 创建时间
     */
    private Long ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getVideoTitle() {
        return videoTitle;
    }

    public void setVideoTitle(String videoTitle) {
        this.videoTitle = videoTitle;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getPlayTime() {
        return playTime;
    }

    public void setPlayTime(Integer playTime) {
        this.playTime = playTime;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "VideoLogData{" +
                "id=" + id +
                ", roomId='" + roomId + '\'' +
                ", videoId='" + videoId + '\'' +
                ", videoTitle='" + videoTitle + '\'' +
                ", duration=" + duration +
                ", playTime=" + playTime +
                ", ctime=" + ctime +
                '}';
    }
}
