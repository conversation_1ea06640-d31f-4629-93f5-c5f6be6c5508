package com.quhong.operation.share.mysql;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/21
 */
public class UserMoney implements Serializable {

    private static final long serialVersionUID = -6585509532024219252L;

    private Integer rid;
    private String userId;
    private Integer status;
    private String detailTable;
    private Long mTotal;
    private Long mLeft;
    private Long mCost;
    private Long mCash;
    private Long mCharge;
    private Date cTime;
    private Date mTime;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDetailTable() {
        return detailTable;
    }

    public void setDetailTable(String detailTable) {
        this.detailTable = detailTable;
    }

    public Long getmTotal() {
        return mTotal;
    }

    public void setmTotal(Long mTotal) {
        this.mTotal = mTotal;
    }

    public Long getmLeft() {
        return mLeft;
    }

    public void setmLeft(Long mLeft) {
        this.mLeft = mLeft;
    }

    public Long getmCost() {
        return mCost;
    }

    public void setmCost(Long mCost) {
        this.mCost = mCost;
    }

    public Long getmCash() {
        return mCash;
    }

    public void setmCash(Long mCash) {
        this.mCash = mCash;
    }

    public Long getmCharge() {
        return mCharge;
    }

    public void setmCharge(Long mCharge) {
        this.mCharge = mCharge;
    }

    public Date getcTime() {
        return cTime;
    }

    public void setcTime(Date cTime) {
        this.cTime = cTime;
    }

    public Date getmTime() {
        return mTime;
    }

    public void setmTime(Date mTime) {
        this.mTime = mTime;
    }

    @Override
    public String toString() {
        return "UserMoney{" +
                "rid=" + rid +
                ", userId='" + userId + '\'' +
                ", status=" + status +
                ", detailTable='" + detailTable + '\'' +
                ", mTotal=" + mTotal +
                ", mLeft=" + mLeft +
                ", mCost=" + mCost +
                ", mCash=" + mCash +
                ", mCharge=" + mCharge +
                ", cTime=" + cTime +
                ", mTime=" + mTime +
                '}';
    }

}
