package com.quhong.operation.share.mysql;

import java.io.Serializable;

public class CreateRoom implements Serializable {
    private static final long serialVersionUID = 2806438029390531338L;
    private Integer id;

    private String roomId;

    private String userId;

    /**
     * 0 no 1 迎新房
     */
    private Integer rookieStatus;

    /**
     * 0 andorid 1 ios
     */
    private Integer os;

    private Long ctime;

    private Integer versionCode;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return room_id
     */
    public String getRoomId() {
        return roomId;
    }

    /**
     * @param roomId
     */
    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    /**
     * @return user_id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * @param userId
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 获取0 no 1 迎新房
     *
     * @return rookie_status - 0 no 1 迎新房
     */
    public Integer getRookieStatus() {
        return rookieStatus;
    }

    /**
     * 设置0 no 1 迎新房
     *
     * @param rookieStatus 0 no 1 迎新房
     */
    public void setRookieStatus(Integer rookieStatus) {
        this.rookieStatus = rookieStatus;
    }

    /**
     * 获取0 andorid 1 ios
     *
     * @return os - 0 andorid 1 ios
     */
    public Integer getOs() {
        return os;
    }

    /**
     * 设置0 andorid 1 ios
     *
     * @param os 0 andorid 1 ios
     */
    public void setOs(Integer os) {
        this.os = os;
    }

    /**
     * @return ctime
     */
    public Long getCtime() {
        return ctime;
    }

    /**
     * @param ctime
     */
    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    /**
     * @return version_code
     */
    public Integer getVersionCode() {
        return versionCode;
    }

    /**
     * @param versionCode
     */
    public void setVersionCode(Integer versionCode) {
        this.versionCode = versionCode;
    }

    @Override
    public String toString() {
        return "room{" +
                "id=" + id +
                ", roomId='" + roomId + '\'' +
                ", userId='" + userId + '\'' +
                ", rookieStatus=" + rookieStatus +
                ", os=" + os +
                ", ctime=" + ctime +
                ", versionCode=" + versionCode +
                '}';
    }
}
