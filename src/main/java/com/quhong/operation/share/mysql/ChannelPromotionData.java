package com.quhong.operation.share.mysql;

import java.io.Serializable;

public class ChannelPromotionData implements Serializable {

    private static final long serialVersionUID = 7089308428846712325L;

    public static final int STATUS_ACTIVE = 1; //有效状态

    public static final int STATUS_STOP = 2; //无效状态

    private Integer id;

    private Integer sourceId;

    private String channelName;

    private String sourceName;

    private String url;

    private String shortUrl;

    private String opUser;

    private Integer status;

    private Integer del;

    private String channelId;

    private Integer ctime;

    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getShortUrl() {
        return shortUrl;
    }

    public void setShortUrl(String shortUrl) {
        this.shortUrl = shortUrl;
    }

    public String getOpUser() {
        return opUser;
    }

    public void setOpUser(String opUser) {
        this.opUser = opUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getDel() {
        return del;
    }

    public void setDel(Integer del) {
        this.del = del;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    @Override
    public String toString() {
        return "ChannelPromotionData{" +
                "id=" + id +
                ", sourceId=" + sourceId +
                ", channelName='" + channelName + '\'' +
                ", sourceName='" + sourceName + '\'' +
                ", url='" + url + '\'' +
                ", shortUrl='" + shortUrl + '\'' +
                ", opUser='" + opUser + '\'' +
                ", status=" + status +
                ", del=" + del +
                ", channelId='" + channelId + '\'' +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
