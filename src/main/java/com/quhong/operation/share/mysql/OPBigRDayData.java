package com.quhong.operation.share.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("s_op_big_r_day")
public class OPBigRDayData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    @TableField("day_time")
    private Integer dayTime; // 格式如 20201201
    @TableField("r1_count")
    private Integer r1Count = 0; // 历史充值用户统计
    @TableField("r2_count")
    private Integer r2Count = 0; // 小r数量统计
    @TableField("r3_count")
    private Integer r3Count = 0; // 中r数量统计
    @TableField("r4_count")
    private Integer r4Count = 0; // 大r数量统计,只按30天内充值，不按荣誉积分大于1m
    @TableField("r2_l2_count")
    private Integer r2l2Count = 0; // 小r准备流失用户统计数
    @TableField("r3_l2_count")
    private Integer r3l2Count = 0; // 中r准备流失用户统计数
    @TableField("r4_l2_count")
    private Integer r4l2Count = 0; // 大r准备流失用户统计数
    @TableField("r2_l3_count")
    private Integer r2l3Count = 0; // 小r半流失用户统计数
    @TableField("r3_l3_count")
    private Integer r3l3Count = 0; // 中r半流失用户统计数
    @TableField("r4_l3_count")
    private Integer r4l3Count = 0; // 大r半流失用户统计数
    @TableField("r2_l4_count")
    private Integer r2l4Count = 0; // 小r重度流失用户统计数
    @TableField("r3_l4_count")
    private Integer r3l4Count = 0; // 中r重度流失用户统计数
    @TableField("r4_l4_count")
    private Integer r4l4Count = 0; // 大r重度流失用户统计数
    @TableField("r2_l5_count")
    private Integer r2l5Count = 0; // 小r完全流失用户统计数
    @TableField("r3_l5_count")
    private Integer r3l5Count = 0; // 中r完全流失用户统计数
    @TableField("r4_l5_count")
    private Integer r4l5Count = 0; // 大r完全流失用户统计数
    @TableField("r2_f1_count")
    private Integer r2F1Count = 0; // 小r新充值用户统计数
    @TableField("r3_f1_count")
    private Integer r3F1Count = 0; // 中r新充值用户统计数
    @TableField("r4_f1_count")
    private Integer r4F1Count = 0; // 大r新充值用户统计数
    private Long ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDayTime() {
        return dayTime;
    }

    public void setDayTime(Integer dayTime) {
        this.dayTime = dayTime;
    }

    public Integer getR1Count() {
        return r1Count;
    }

    public void setR1Count(Integer r1Count) {
        this.r1Count = r1Count;
    }

    public Integer getR2Count() {
        return r2Count;
    }

    public void setR2Count(Integer r2Count) {
        this.r2Count = r2Count;
    }

    public Integer getR3Count() {
        return r3Count;
    }

    public void setR3Count(Integer r3Count) {
        this.r3Count = r3Count;
    }

    public Integer getR4Count() {
        return r4Count;
    }

    public void setR4Count(Integer r4Count) {
        this.r4Count = r4Count;
    }

    public Integer getR2l2Count() {
        return r2l2Count;
    }

    public void setR2l2Count(Integer r2l2Count) {
        this.r2l2Count = r2l2Count;
    }

    public Integer getR3l2Count() {
        return r3l2Count;
    }

    public void setR3l2Count(Integer r3l2Count) {
        this.r3l2Count = r3l2Count;
    }

    public Integer getR4l2Count() {
        return r4l2Count;
    }

    public void setR4l2Count(Integer r4l2Count) {
        this.r4l2Count = r4l2Count;
    }

    public Integer getR2l3Count() {
        return r2l3Count;
    }

    public void setR2l3Count(Integer r2l3Count) {
        this.r2l3Count = r2l3Count;
    }

    public Integer getR3l3Count() {
        return r3l3Count;
    }

    public void setR3l3Count(Integer r3l3Count) {
        this.r3l3Count = r3l3Count;
    }

    public Integer getR4l3Count() {
        return r4l3Count;
    }

    public void setR4l3Count(Integer r4l3Count) {
        this.r4l3Count = r4l3Count;
    }

    public Integer getR2l4Count() {
        return r2l4Count;
    }

    public void setR2l4Count(Integer r2l4Count) {
        this.r2l4Count = r2l4Count;
    }

    public Integer getR3l4Count() {
        return r3l4Count;
    }

    public void setR3l4Count(Integer r3l4Count) {
        this.r3l4Count = r3l4Count;
    }

    public Integer getR4l4Count() {
        return r4l4Count;
    }

    public void setR4l4Count(Integer r4l4Count) {
        this.r4l4Count = r4l4Count;
    }

    public Integer getR2l5Count() {
        return r2l5Count;
    }

    public void setR2l5Count(Integer r2l5Count) {
        this.r2l5Count = r2l5Count;
    }

    public Integer getR3l5Count() {
        return r3l5Count;
    }

    public void setR3l5Count(Integer r3l5Count) {
        this.r3l5Count = r3l5Count;
    }

    public Integer getR4l5Count() {
        return r4l5Count;
    }

    public void setR4l5Count(Integer r4l5Count) {
        this.r4l5Count = r4l5Count;
    }

    public Integer getR2F1Count() {
        return r2F1Count;
    }

    public void setR2F1Count(Integer r2F1Count) {
        this.r2F1Count = r2F1Count;
    }

    public Integer getR3F1Count() {
        return r3F1Count;
    }

    public void setR3F1Count(Integer r3F1Count) {
        this.r3F1Count = r3F1Count;
    }

    public Integer getR4F1Count() {
        return r4F1Count;
    }

    public void setR4F1Count(Integer r4F1Count) {
        this.r4F1Count = r4F1Count;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }
}
