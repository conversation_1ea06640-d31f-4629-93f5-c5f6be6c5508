package com.quhong.operation.share.mysql;

public class  ChannelBeansLogData {

    public static final int STATUS_DRILL = 1;
    public static final int STATUS_RESET_BEAN = 2;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 被打钻id
     */
    private String aid;

    /**
     * 打钻之前钻石额度
     */
    private Integer beforeBeans;

    /**
     * 打钻消耗钻石额度
     */
    private Integer costBeans;

    /**
     * 打钻之后钻石额度
     */
    private Integer afterBeans;

    /**
     * 打钻备注
     */
    private String remark;

    /**
     * 打钻状态：1打钻 2重置额度
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 更新时间
     */
    private Integer mtime;

    public Integer getAfterBeans() {
        return afterBeans;
    }

    public void setAfterBeans(Integer afterBeans) {
        this.afterBeans = afterBeans;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getBeforeBeans() {
        return beforeBeans;
    }

    public void setBeforeBeans(Integer beforeBeans) {
        this.beforeBeans = beforeBeans;
    }

    public Integer getCostBeans() {
        return costBeans;
    }

    public void setCostBeans(Integer costBeans) {
        this.costBeans = costBeans;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "ChannelBeansLogData{" +
                "id=" + id +
                ", channelId='" + channelId + '\'' +
                ", aid='" + aid + '\'' +
                ", beforeBeans=" + beforeBeans +
                ", costBeans=" + costBeans +
                ", afterBeans=" + afterBeans +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
