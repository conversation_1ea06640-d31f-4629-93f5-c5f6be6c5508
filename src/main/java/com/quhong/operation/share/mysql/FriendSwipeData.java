package com.quhong.operation.share.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quhong.core.utils.DateHelper;

@TableName("t_friend_swipe")
public class FriendSwipeData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid;  // 操作方
    private String aid; // 被操作方
    private int type; //  喜欢类型，1喜欢，2超级喜欢,3不喜欢
    private boolean isUnlock; //  是否已解锁
    private long ctime; //  操作时间

    public FriendSwipeData() {
    }

    public FriendSwipeData(String uid, String aid, int type) {
        this.uid = uid;
        this.aid = aid;
        this.type = type;
        this.isUnlock = false;
        this.ctime = DateHelper.getNowSeconds();
    }

    public Integer getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isUnlock() {
        return isUnlock;
    }

    public void setUnlock(boolean unlock) {
        isUnlock = unlock;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public long getCtime() {
        return ctime;
    }

    public void setCtime(long ctime) {
        this.ctime = ctime;
    }
}
