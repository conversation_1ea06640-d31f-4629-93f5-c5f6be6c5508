package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * 房间关注表对象
 * <AUTHOR>
 * @date 2020/8/4
 */
public class RoomFollow implements Serializable {

    private static final long serialVersionUID = -5485680276557533536L;
    private Integer id;
    private String roomId;
    private String userId;
    // 1 关注 2 取消关注
    private Integer action;
    // 0 android 1 ios
    private Integer os;
    private Long ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "RoomFollow{" +
                "id=" + id +
                ", roomId='" + roomId + '\'' +
                ", userId='" + userId + '\'' +
                ", action=" + action +
                ", os=" + os +
                ", ctime=" + ctime +
                '}';
    }
}
