package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * 心心记录表对象
 */
public class HeartRecord implements Serializable {

    private static final long serialVersionUID = 8927205556327828947L;


    private Integer id;
    private String uid;
    private Integer changed;
    private Integer gBalance;
    private Integer rBalance;
    private String roomId;
    private String aid;
    private String title;
    private String remark;
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getChanged() {
        return changed;
    }

    public void setChanged(Integer changed) {
        this.changed = changed;
    }

    public Integer getgBalance() {
        return gBalance;
    }

    public void setgBalance(Integer gBalance) {
        this.gBalance = gBalance;
    }

    public Integer getrBalance() {
        return rBalance;
    }

    public void setrBalance(Integer rBalance) {
        this.rBalance = rBalance;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "HeatRecord{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", changed=" + changed +
                ", gBalance=" + gBalance +
                ", rBalance=" + rBalance +
                ", roomId='" + roomId + '\'' +
                ", aid='" + aid + '\'' +
                ", title='" + title + '\'' +
                ", remark='" + remark + '\'' +
                ", ctime=" + ctime +
                '}';
    }
}
