package com.quhong.operation.share.mysql;


public class Music {

    private Integer id;

    /**
     * 封面图
     */
    private String album;

    private String name;

    private String artist;

    /**
     * 音乐链接
     */
    private String url;

    /**
     * 歌词链接
     */
    private String lrc;

    /**
     * 上传者uid
     */
    private String upUid;

    /**
     * 上传者name
     */
    private String upUser;

    /**
     * 音乐文件md5
     */
    private String md5;

    /**
     * 音乐文件大小kb
     */
    private Integer size;

    /**
     * 创建时间
     */
    private Integer cTime;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取封面图
     *
     * @return album - 封面图
     */
    public String getAlbum() {
        return album;
    }

    /**
     * 设置封面图
     *
     * @param album 封面图
     */
    public void setAlbum(String album) {
        this.album = album;
    }

    /**
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return artist
     */
    public String getArtist() {
        return artist;
    }

    /**
     * @param artist
     */
    public void setArtist(String artist) {
        this.artist = artist;
    }

    /**
     * 获取音乐链接
     *
     * @return url - 音乐链接
     */
    public String getUrl() {
        return url;
    }

    /**
     * 设置音乐链接
     *
     * @param url 音乐链接
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 获取歌词链接
     *
     * @return lrc - 歌词链接
     */
    public String getLrc() {
        return lrc;
    }

    /**
     * 设置歌词链接
     *
     * @param lrc 歌词链接
     */
    public void setLrc(String lrc) {
        this.lrc = lrc;
    }

    /**
     * 获取上传者uid
     *
     * @return up_uid - 上传者uid
     */
    public String getUpUid() {
        return upUid;
    }

    /**
     * 设置上传者uid
     *
     * @param upUid 上传者uid
     */
    public void setUpUid(String upUid) {
        this.upUid = upUid;
    }

    /**
     * 获取上传者name
     *
     * @return up_user - 上传者name
     */
    public String getUpUser() {
        return upUser;
    }

    /**
     * 设置上传者name
     *
     * @param upUser 上传者name
     */
    public void setUpUser(String upUser) {
        this.upUser = upUser;
    }

    /**
     * 获取音乐文件md5
     *
     * @return md5 - 音乐文件md5
     */
    public String getMd5() {
        return md5;
    }

    /**
     * 设置音乐文件md5
     *
     * @param md5 音乐文件md5
     */
    public void setMd5(String md5) {
        this.md5 = md5;
    }

    /**
     * 获取音乐文件大小kb
     *
     * @return size - 音乐文件大小kb
     */
    public Integer getSize() {
        return size;
    }

    /**
     * 设置音乐文件大小kb
     *
     * @param size 音乐文件大小kb
     */
    public void setSize(Integer size) {
        this.size = size;
    }

    /**
     * 获取创建时间
     *
     * @return c_time - 创建时间
     */
    public Integer getcTime() {
        return cTime;
    }

    /**
     * 设置创建时间
     *
     * @param cTime 创建时间
     */
    public void setcTime(Integer cTime) {
        this.cTime = cTime;
    }

    @Override
    public String toString() {
        return "music{" +
                "id=" + id +
                ", album='" + album + '\'' +
                ", name='" + name + '\'' +
                ", artist='" + artist + '\'' +
                ", url='" + url + '\'' +
                ", lrc='" + lrc + '\'' +
                ", upUid='" + upUid + '\'' +
                ", upUser='" + upUser + '\'' +
                ", md5='" + md5 + '\'' +
                ", size=" + size +
                ", cTime=" + cTime +
                '}';
    }
}
