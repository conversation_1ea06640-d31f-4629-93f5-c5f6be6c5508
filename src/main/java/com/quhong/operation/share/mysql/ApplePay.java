package com.quhong.operation.share.mysql;

import java.io.Serializable;

public class ApplePay implements Serializable {

    private static final long serialVersionUID = -7440935381366656614L;

    private Integer rid;

    private Integer fstatus;

    private String receiptDataMd5;

    private Long ctime;

    private Long mtime;

    private String userId;

    private String productId;

    private String receiptData;

    private String transactionId;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getFstatus() {
        return fstatus;
    }

    public void setFstatus(Integer fstatus) {
        this.fstatus = fstatus;
    }

    public String getReceiptDataMd5() {
        return receiptDataMd5;
    }

    public void setReceiptDataMd5(String receiptDataMd5) {
        this.receiptDataMd5 = receiptDataMd5;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getMtime() {
        return mtime;
    }

    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getReceiptData() {
        return receiptData;
    }

    public void setReceiptData(String receiptData) {
        this.receiptData = receiptData;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @Override
    public String toString() {
        return "applePay{" +
                "rid=" + rid +
                ", fstatus=" + fstatus +
                ", receiptDataMd5='" + receiptDataMd5 + '\'' +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                ", userId='" + userId + '\'' +
                ", productId='" + productId + '\'' +
                ", receiptData='" + receiptData + '\'' +
                ", transactionId='" + transactionId + '\'' +
                '}';
    }
}
