package com.quhong.operation.share.mysql;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/23
 */
public class VipInfo implements Serializable {

    private static final long serialVersionUID = -3122141537690648656L;

    private Integer rid;
    private String uid;
    // vip购买时间
    private Date vipBuyTime;
    // vip结束时间
    private Date vipEndTime;
    private Integer vipTimeTotal = 1;
    private Date lastBenefitGetTime;
    private Integer vipGoldTotal = 0;
    private Integer vipGoldUnget = 0;
    private Integer vipLevel;
    private Integer freeCallTimeRemain = 0;
    private Integer freeCallTimeUsed = 0;
    private Integer vipGiftRemain = 0;
    private Integer vipGiftUsed = 0;
    private Integer vipGiftUnget = 0;
    private Date ctime;
    private Date mtime;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Date getVipBuyTime() {
        return vipBuyTime;
    }

    public void setVipBuyTime(Date vipBuyTime) {
        this.vipBuyTime = vipBuyTime;
    }

    public Date getVipEndTime() {
        return vipEndTime;
    }

    public void setVipEndTime(Date vipEndTime) {
        this.vipEndTime = vipEndTime;
    }

    public Integer getVipTimeTotal() {
        return vipTimeTotal;
    }

    public void setVipTimeTotal(Integer vipTimeTotal) {
        this.vipTimeTotal = vipTimeTotal;
    }

    public Date getLastBenefitGetTime() {
        return lastBenefitGetTime;
    }

    public void setLastBenefitGetTime(Date lastBenefitGetTime) {
        this.lastBenefitGetTime = lastBenefitGetTime;
    }

    public Integer getVipGoldTotal() {
        return vipGoldTotal;
    }

    public void setVipGoldTotal(Integer vipGoldTotal) {
        this.vipGoldTotal = vipGoldTotal;
    }

    public Integer getVipGoldUnget() {
        return vipGoldUnget;
    }

    public void setVipGoldUnget(Integer vipGoldUnget) {
        this.vipGoldUnget = vipGoldUnget;
    }

    public Integer getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(Integer vipLevel) {
        this.vipLevel = vipLevel;
    }

    public Integer getFreeCallTimeRemain() {
        return freeCallTimeRemain;
    }

    public void setFreeCallTimeRemain(Integer freeCallTimeRemain) {
        this.freeCallTimeRemain = freeCallTimeRemain;
    }

    public Integer getFreeCallTimeUsed() {
        return freeCallTimeUsed;
    }

    public void setFreeCallTimeUsed(Integer freeCallTimeUsed) {
        this.freeCallTimeUsed = freeCallTimeUsed;
    }

    public Integer getVipGiftRemain() {
        return vipGiftRemain;
    }

    public void setVipGiftRemain(Integer vipGiftRemain) {
        this.vipGiftRemain = vipGiftRemain;
    }

    public Integer getVipGiftUsed() {
        return vipGiftUsed;
    }

    public void setVipGiftUsed(Integer vipGiftUsed) {
        this.vipGiftUsed = vipGiftUsed;
    }

    public Integer getVipGiftUnget() {
        return vipGiftUnget;
    }

    public void setVipGiftUnget(Integer vipGiftUnget) {
        this.vipGiftUnget = vipGiftUnget;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "VipInfo{" +
                "rid=" + rid +
                ", uid='" + uid + '\'' +
                ", vipBuyTime=" + vipBuyTime +
                ", vipEndTime=" + vipEndTime +
                ", vipTimeTotal=" + vipTimeTotal +
                ", lastBenefitGetTime=" + lastBenefitGetTime +
                ", vipGoldTotal=" + vipGoldTotal +
                ", vipGoldUnget=" + vipGoldUnget +
                ", vipLevel=" + vipLevel +
                ", freeCallTimeRemain=" + freeCallTimeRemain +
                ", freeCallTimeUsed=" + freeCallTimeUsed +
                ", vipGiftRemain=" + vipGiftRemain +
                ", vipGiftUsed=" + vipGiftUsed +
                ", vipGiftUnget=" + vipGiftUnget +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }

}
