package com.quhong.operation.share.mysql;

public class UserOnlineData {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 用户在线人数
     */
    private Integer userOnlineCount;

    /**
     * 机器人在线人数
     */
    private Integer robotOnlineCount;

    /**
     * 活跃房间数
     */
    private Integer roomActiveCount;

    /**
     * 在线男用户数
     */
    private int manOnlineCount;
    private int newManOnlineCount;

    /**
     * 在线女用户数
     */
    private int girlOnlineCount;
    private int newGirlOnlineCount;

    /**
     * 在线ptg人数
     */
    private int ptgOnlineCount;
    private int rechargeOnlineCount; // 在线充值用户人数
    private int liveRoomCount; // 直播房间数
    private int voiceRoomCount; // 语音房间数
    private int ludoRoomCount; // 正在玩ludo的房间数
    private int videoRoomCount; // 正在放视频的房间数
    private int turntableRoomCount; // 正在玩转盘的房间数
    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * os -1代表全部0代表安卓1代表ios
     */
    private Integer os;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserOnlineCount() {
        return userOnlineCount;
    }

    public void setUserOnlineCount(Integer userOnlineCount) {
        this.userOnlineCount = userOnlineCount;
    }

    public Integer getRoomActiveCount() {
        return roomActiveCount;
    }

    public void setRoomActiveCount(Integer roomActiveCount) {
        this.roomActiveCount = roomActiveCount;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public Integer getRobotOnlineCount() {
        return robotOnlineCount;
    }

    public void setRobotOnlineCount(Integer robotOnlineCount) {
        this.robotOnlineCount = robotOnlineCount;
    }

    public int getManOnlineCount() {
        return manOnlineCount;
    }

    public void setManOnlineCount(int manOnlineCount) {
        this.manOnlineCount = manOnlineCount;
    }

    public int getGirlOnlineCount() {
        return girlOnlineCount;
    }

    public void setGirlOnlineCount(int girlOnlineCount) {
        this.girlOnlineCount = girlOnlineCount;
    }

    public int getPtgOnlineCount() {
        return ptgOnlineCount;
    }

    public void setPtgOnlineCount(int ptgOnlineCount) {
        this.ptgOnlineCount = ptgOnlineCount;
    }

    public int getNewManOnlineCount() {
        return newManOnlineCount;
    }

    public void setNewManOnlineCount(int newManOnlineCount) {
        this.newManOnlineCount = newManOnlineCount;
    }

    public int getNewGirlOnlineCount() {
        return newGirlOnlineCount;
    }

    public void setNewGirlOnlineCount(int newGirlOnlineCount) {
        this.newGirlOnlineCount = newGirlOnlineCount;
    }

    public int getRechargeOnlineCount() {
        return rechargeOnlineCount;
    }

    public void setRechargeOnlineCount(int rechargeOnlineCount) {
        this.rechargeOnlineCount = rechargeOnlineCount;
    }

    public int getLiveRoomCount() {
        return liveRoomCount;
    }

    public void setLiveRoomCount(int liveRoomCount) {
        this.liveRoomCount = liveRoomCount;
    }

    public int getVoiceRoomCount() {
        return voiceRoomCount;
    }

    public void setVoiceRoomCount(int voiceRoomCount) {
        this.voiceRoomCount = voiceRoomCount;
    }

    public int getLudoRoomCount() {
        return ludoRoomCount;
    }

    public void setLudoRoomCount(int ludoRoomCount) {
        this.ludoRoomCount = ludoRoomCount;
    }

    public int getVideoRoomCount() {
        return videoRoomCount;
    }

    public void setVideoRoomCount(int videoRoomCount) {
        this.videoRoomCount = videoRoomCount;
    }

    public int getTurntableRoomCount() {
        return turntableRoomCount;
    }

    public void setTurntableRoomCount(int turntableRoomCount) {
        this.turntableRoomCount = turntableRoomCount;
    }
}
