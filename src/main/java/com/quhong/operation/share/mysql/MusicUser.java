package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * 音乐平台用户对象
 * <AUTHOR>
 * @date 2020/8/28
 */
public class MusicUser implements Serializable {
    private static final long serialVersionUID = 378419583475408331L;
    /**
     * id
     */
    private Integer id;

    /**
     * 登录的账号
     */
    private String account;

    /**
     * MD5密码
     */
    private String pwd;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 设备信息
     */
    private String device;

    /**
     * 账号创建时间
     */
    private Long cTime;

    /**
     * 最近一次登陆时间
     */
    private Long lastLogin;

    /**
     * 获取id
     *
     * @return id - id
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取登录的账号
     *
     * @return account - 登录的账号
     */
    public String getAccount() {
        return account;
    }

    /**
     * 设置登录的账号
     *
     * @param account 登录的账号
     */
    public void setAccount(String account) {
        this.account = account;
    }

    /**
     * 获取MD5密码
     *
     * @return pwd - MD5密码
     */
    public String getPwd() {
        return pwd;
    }

    /**
     * 设置MD5密码
     *
     * @param pwd MD5密码
     */
    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    /**
     * 获取用户名称
     *
     * @return userName - 用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置用户名称
     *
     * @param userName 用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 获取设备信息
     *
     * @return device - 设备信息
     */
    public String getDevice() {
        return device;
    }

    /**
     * 设置设备信息
     *
     * @param device 设备信息
     */
    public void setDevice(String device) {
        this.device = device;
    }

    /**
     * 获取账号创建时间
     *
     * @return time - 账号创建时间
     */
    public Long getcTime() {
        return cTime;
    }

    /**
     * 设置账号创建时间
     *
     * @param cTime 账号创建时间
     */
    public void setcTime(Long cTime) {
        this.cTime = cTime;
    }

    /**
     * 获取最近一次登陆时间
     *
     * @return last_login - 最近一次登陆时间
     */
    public Long getLastLogin() {
        return lastLogin;
    }

    /**
     * 设置最近一次登陆时间
     *
     * @param lastLogin 最近一次登陆时间
     */
    public void setLastLogin(Long lastLogin) {
        this.lastLogin = lastLogin;
    }

    @Override
    public String toString() {
        return "MusicUser{" +
                "id=" + id +
                ", account='" + account + '\'' +
                ", pwd='" + pwd + '\'' +
                ", userName='" + userName + '\'' +
                ", device='" + device + '\'' +
                ", cTime=" + cTime +
                ", lastLogin=" + lastLogin +
                '}';
    }
}
