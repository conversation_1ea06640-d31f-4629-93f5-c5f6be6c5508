package com.quhong.operation.share.mysql;

import java.io.Serializable;
import java.util.Date;

public class GooglePay implements Serializable {

    private static final long serialVersionUID = 2065281672773584975L;
    private Integer rid;

    /**
     * 1 subscription; 2 product
     */
    private Byte gkind;

    private String userId;

    private String orderId;

    /**
     * 0 wait check; 1 check succeed; 2 check failed
     */
    private Byte fstatus;

    private String productId;

    private String purchaseToken;

    private Long purchaseTimeMillis;

    /**
     * from client, 0 paied
     */
    private Byte purchaseState;

    /**
     * pay load
     */
    private String payload;

    /**
     * json data,content like consumptionState, autoRenewing
     */
    private String fothers;

    private Date ctime;

    private Date mtime;

    /**
     * @return rid
     */
    public Integer getRid() {
        return rid;
    }

    /**
     * @param rid
     */
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    /**
     * 获取1 subscription; 2 product
     *
     * @return gkind - 1 subscription; 2 product
     */
    public Byte getGkind() {
        return gkind;
    }

    /**
     * 设置1 subscription; 2 product
     *
     * @param gkind 1 subscription; 2 product
     */
    public void setGkind(Byte gkind) {
        this.gkind = gkind;
    }

    /**
     * @return userId
     */
    public String getUserId() {
        return userId;
    }

    /**
     * @param userId
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * @return orderId
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * @param orderId
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取0 wait check; 1 check succeed; 2 check failed
     *
     * @return fstatus - 0 wait check; 1 check succeed; 2 check failed
     */
    public Byte getFstatus() {
        return fstatus;
    }

    /**
     * 设置0 wait check; 1 check succeed; 2 check failed
     *
     * @param fstatus 0 wait check; 1 check succeed; 2 check failed
     */
    public void setFstatus(Byte fstatus) {
        this.fstatus = fstatus;
    }

    /**
     * @return productId
     */
    public String getProductId() {
        return productId;
    }

    /**
     * @param productId
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * @return purchaseToken
     */
    public String getPurchaseToken() {
        return purchaseToken;
    }

    /**
     * @param purchaseToken
     */
    public void setPurchaseToken(String purchaseToken) {
        this.purchaseToken = purchaseToken;
    }

    /**
     * @return purchaseTimeMillis
     */
    public Long getPurchaseTimeMillis() {
        return purchaseTimeMillis;
    }

    /**
     * @param purchaseTimeMillis
     */
    public void setPurchaseTimeMillis(Long purchaseTimeMillis) {
        this.purchaseTimeMillis = purchaseTimeMillis;
    }

    /**
     * 获取from client, 0 paied
     *
     * @return purchaseState - from client, 0 paied
     */
    public Byte getPurchaseState() {
        return purchaseState;
    }

    /**
     * 设置from client, 0 paied
     *
     * @param purchaseState from client, 0 paied
     */
    public void setPurchaseState(Byte purchaseState) {
        this.purchaseState = purchaseState;
    }

    /**
     * 获取pay load
     *
     * @return payload - pay load
     */
    public String getPayload() {
        return payload;
    }

    /**
     * 设置pay load
     *
     * @param payload pay load
     */
    public void setPayload(String payload) {
        this.payload = payload;
    }

    /**
     * 获取json data,content like consumptionState, autoRenewing
     *
     * @return fothers - json data,content like consumptionState, autoRenewing
     */
    public String getFothers() {
        return fothers;
    }

    /**
     * 设置json data,content like consumptionState, autoRenewing
     *
     * @param fothers json data,content like consumptionState, autoRenewing
     */
    public void setFothers(String fothers) {
        this.fothers = fothers;
    }

    /**
     * @return ctime
     */
    public Date getCtime() {
        return ctime;
    }

    /**
     * @param ctime
     */
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    /**
     * @return mtime
     */
    public Date getMtime() {
        return mtime;
    }

    /**
     * @param mtime
     */
    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "googlePay{" +
                "rid=" + rid +
                ", gkind=" + gkind +
                ", userId='" + userId + '\'' +
                ", orderId='" + orderId + '\'' +
                ", fstatus=" + fstatus +
                ", productId='" + productId + '\'' +
                ", purchaseToken='" + purchaseToken + '\'' +
                ", purchaseTimeMillis=" + purchaseTimeMillis +
                ", purchaseState=" + purchaseState +
                ", payload='" + payload + '\'' +
                ", fothers='" + fothers + '\'' +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
