package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * 迎新房每天数据
 * <AUTHOR>
 * @date 2020/8/25
 */
public class WelcomeNewRoom implements Serializable {

    private static final long serialVersionUID = 3278348729830198512L;
    private String date;
    private Integer newActor = 0;
    private Integer iosNewActor = 0;
    private Integer androidNewActor = 0;
    // 人均进入房间数
    private Double perAvgInRoomNum = 0.00;
    // 人均进房次数
    private Double perAvgInRoomCount = 0.00;
    private String perAvgRoomTime = "00:00:00";
    private Integer chatPerNum = 0;
    private Integer upMicPer = 0;
    private Double perAvgUpMicCount = 0.00;
    private String perAvgUpMicTime = "00:00:00";
    private Integer sendGiftPerNum = 0;
    private Integer sendGiftCount = 0;
    private Integer followRoomPer = 0;
    private Integer friendNum = 0;
    private Integer chargePerNum = 0;
    private Integer chargeCount = 0;
    private Integer chargeBeans = 0;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Integer getNewActor() {
        return newActor;
    }

    public void setNewActor(Integer newActor) {
        this.newActor = newActor;
    }

    public Integer getIosNewActor() {
        return iosNewActor;
    }

    public void setIosNewActor(Integer iosNewActor) {
        this.iosNewActor = iosNewActor;
    }

    public Integer getAndroidNewActor() {
        return androidNewActor;
    }

    public void setAndroidNewActor(Integer androidNewActor) {
        this.androidNewActor = androidNewActor;
    }

    public Double getPerAvgInRoomNum() {
        return perAvgInRoomNum;
    }

    public void setPerAvgInRoomNum(Double perAvgInRoomNum) {
        this.perAvgInRoomNum = perAvgInRoomNum;
    }

    public Double getPerAvgInRoomCount() {
        return perAvgInRoomCount;
    }

    public void setPerAvgInRoomCount(Double perAvgInRoomCount) {
        this.perAvgInRoomCount = perAvgInRoomCount;
    }

    public String getPerAvgRoomTime() {
        return perAvgRoomTime;
    }

    public void setPerAvgRoomTime(String perAvgRoomTime) {
        this.perAvgRoomTime = perAvgRoomTime;
    }

    public Integer getChatPerNum() {
        return chatPerNum;
    }

    public void setChatPerNum(Integer chatPerNum) {
        this.chatPerNum = chatPerNum;
    }

    public Integer getUpMicPer() {
        return upMicPer;
    }

    public void setUpMicPer(Integer upMicPer) {
        this.upMicPer = upMicPer;
    }

    public Double getPerAvgUpMicCount() {
        return perAvgUpMicCount;
    }

    public void setPerAvgUpMicCount(Double perAvgUpMicCount) {
        this.perAvgUpMicCount = perAvgUpMicCount;
    }

    public String getPerAvgUpMicTime() {
        return perAvgUpMicTime;
    }

    public void setPerAvgUpMicTime(String perAvgUpMicTime) {
        this.perAvgUpMicTime = perAvgUpMicTime;
    }

    public Integer getSendGiftPerNum() {
        return sendGiftPerNum;
    }

    public void setSendGiftPerNum(Integer sendGiftPerNum) {
        this.sendGiftPerNum = sendGiftPerNum;
    }

    public Integer getSendGiftCount() {
        return sendGiftCount;
    }

    public void setSendGiftCount(Integer sendGiftCount) {
        this.sendGiftCount = sendGiftCount;
    }

    public Integer getFollowRoomPer() {
        return followRoomPer;
    }

    public void setFollowRoomPer(Integer followRoomPer) {
        this.followRoomPer = followRoomPer;
    }

    public Integer getFriendNum() {
        return friendNum;
    }

    public void setFriendNum(Integer friendNum) {
        this.friendNum = friendNum;
    }

    public Integer getChargePerNum() {
        return chargePerNum;
    }

    public void setChargePerNum(Integer chargePerNum) {
        this.chargePerNum = chargePerNum;
    }

    public Integer getChargeCount() {
        return chargeCount;
    }

    public void setChargeCount(Integer chargeCount) {
        this.chargeCount = chargeCount;
    }

    public Integer getChargeBeans() {
        return chargeBeans;
    }

    public void setChargeBeans(Integer chargeBeans) {
        this.chargeBeans = chargeBeans;
    }

    @Override
    public String toString() {
        return "WelcomeNewRoomVO{" +
                "date='" + date + '\'' +
                ", newActor=" + newActor +
                ", iosNewActor=" + iosNewActor +
                ", androidNewActor=" + androidNewActor +
                ", perAvgInRoomNum=" + perAvgInRoomNum +
                ", perAvgInRoomCount=" + perAvgInRoomCount +
                ", perAvgRoomTime=" + perAvgRoomTime +
                ", chatPerNum=" + chatPerNum +
                ", upMicPer=" + upMicPer +
                ", perAvgUpMicCount=" + perAvgUpMicCount +
                ", perAvgUpMicTime='" + perAvgUpMicTime + '\'' +
                ", sendGiftPerNum=" + sendGiftPerNum +
                ", sendGiftCount=" + sendGiftCount +
                ", followRoomPer=" + followRoomPer +
                ", friendNum=" + friendNum +
                ", chargePerNum=" + chargePerNum +
                ", chargeCount=" + chargeCount +
                ", chargeBeans=" + chargeBeans +
                '}';
    }
}
