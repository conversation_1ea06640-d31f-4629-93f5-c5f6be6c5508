package com.quhong.operation.share.mysql;

import java.io.Serializable;

public class ChannelSourceData implements Serializable {

    private static final long serialVersionUID = -5006346958794546249L;

    private Integer id;

    /**
     * 渠道来源id
     */
    private Integer sourceId;

    /**
     * 渠道来源名称
     */
    private String sourceName;

    /**
     * 删除标识: 0 正常 1 已删除
     */
    private Integer del;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 更新时间
     */
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public Integer getDel() {
        return del;
    }

    public void setDel(Integer del) {
        this.del = del;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "ChannelSourceData{" +
                "id=" + id +
                ", sourceId=" + sourceId +
                ", sourceName='" + sourceName + '\'' +
                ", del=" + del +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
