package com.quhong.operation.share.mysql;

public class RoomReportLogData {

    public static final int ROOM_TYPE_VIDEO = 1; //视频房

    public static final int ROOM_TYPE_TOPIC = 2; //话题房

    public static final int ROOM_TYPE_MUSIC = 3; //音乐房

    private Integer id;

    /**
     * 房间类型:1视屏房2话题房3音乐房
     */
    private Integer roomType;

    /**
     * 二级分类:没有二级分类的二级分类默认为0
     */
    private Integer childType;

    /**
     * 上报日期
     */
    private String date;

    /**
     * 房间数
     */
    private Integer num;

    /**
     * 房间内在线人数
     */
    private Integer userNum;

    /**
     * 内容数量：如视频数量、话题数量、音乐数量
     */
    private Integer contentNum;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 更新时间
     */
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRoomType() {
        return roomType;
    }

    public void setRoomType(Integer roomType) {
        this.roomType = roomType;
    }

    public Integer getChildType() {
        return childType;
    }

    public void setChildType(Integer childType) {
        this.childType = childType;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getUserNum() {
        return userNum;
    }

    public void setUserNum(Integer userNum) {
        this.userNum = userNum;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getContentNum() {
        return contentNum;
    }

    public void setContentNum(Integer contentNum) {
        this.contentNum = contentNum;
    }

    @Override
    public String toString() {
        return "RoomReportLogData{" +
                "id=" + id +
                ", roomType=" + roomType +
                ", childType=" + childType +
                ", date='" + date + '\'' +
                ", num=" + num +
                ", userNum=" + userNum +
                ", contentNum=" + contentNum +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
