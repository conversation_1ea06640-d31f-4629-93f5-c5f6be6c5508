package com.quhong.operation.share.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_relationship_stat")
public class RelationshipStatData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String date;
    private int newStatPerson; // 新用户一键关注和喜欢人数
    private int newStatCount; // 新用户一键关注和喜欢用户数
    private int returnStatPerson; // 回访用户一键关注和喜欢人数
    private int returnStatCount; // 回访用户一键关注和喜欢用户数
    private int returnUser; // 当天的回流用户人数
    private int ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getNewStatPerson() {
        return newStatPerson;
    }

    public void setNewStatPerson(int newStatPerson) {
        this.newStatPerson = newStatPerson;
    }

    public int getNewStatCount() {
        return newStatCount;
    }

    public void setNewStatCount(int newStatCount) {
        this.newStatCount = newStatCount;
    }

    public int getReturnStatPerson() {
        return returnStatPerson;
    }

    public void setReturnStatPerson(int returnStatPerson) {
        this.returnStatPerson = returnStatPerson;
    }

    public int getReturnStatCount() {
        return returnStatCount;
    }

    public void setReturnStatCount(int returnStatCount) {
        this.returnStatCount = returnStatCount;
    }

    public int getReturnUser() {
        return returnUser;
    }

    public void setReturnUser(int returnUser) {
        this.returnUser = returnUser;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
