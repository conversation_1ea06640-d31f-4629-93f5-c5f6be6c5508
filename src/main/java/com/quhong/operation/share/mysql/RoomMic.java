package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * 上麦记录
 * <AUTHOR>
 * @date 2020/8/3
 */
public class RoomMic implements Serializable {

    private static final long serialVersionUID = -3080549408297054747L;
    private Integer id;
    private String roomId;
    private String userId;
    // 麦位
    private Integer micPosition;
    // 在麦时长，秒
    private Integer micTime;
    // 数据创建时间
    private Integer ctime;
    // 更新时间
    private Integer mtime;
    // 0安卓  1ios
    private Integer os;
    // 是否新用户
    private Integer rookieStatus;
    private Integer versionCode;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getMicPosition() {
        return micPosition;
    }

    public void setMicPosition(Integer micPosition) {
        this.micPosition = micPosition;
    }

    public Integer getMicTime() {
        return micTime;
    }

    public void setMicTime(Integer micTime) {
        this.micTime = micTime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public Integer getRookieStatus() {
        return rookieStatus;
    }

    public void setRookieStatus(Integer rookieStatus) {
        this.rookieStatus = rookieStatus;
    }

    public Integer getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(Integer versionCode) {
        this.versionCode = versionCode;
    }

    @Override
    public String toString() {
        return "RoomMic{" +
                "id=" + id +
                ", roomId='" + roomId + '\'' +
                ", userId='" + userId + '\'' +
                ", micPosition=" + micPosition +
                ", micTime=" + micTime +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                ", os=" + os +
                ", rookieStatus=" + rookieStatus +
                ", versionCode=" + versionCode +
                '}';
    }
}
