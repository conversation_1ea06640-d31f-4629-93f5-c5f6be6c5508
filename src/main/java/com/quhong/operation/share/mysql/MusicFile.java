package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * 音乐文件列表
 * <AUTHOR>
 * @date 2020/8/28
 */
public class MusicFile implements Serializable {

    private static final long serialVersionUID = -3573722375445599402L;
    public static final Byte DELETE = 0; // 已删除
    public static final Byte AWAIT_AUDIT = 1; // 待审批
    public static final Byte AUDIT_PASS = 2; // 审批通过
    public static final Byte NOT_AUDIT = 3; // 审批拒绝
    public static final Byte ALBUM_FILE = 1; // 音乐封面
    public static final Byte MUSIC_FILE = 2; // 音乐
    public static final Byte LRC_FILE = 3; // 歌词

    // id
    private Integer id;

    // 歌曲封面
    private String album;

    // 文件上传的名称
    private String name;

    // 歌手
    private String artist;

    // 访问文件url
    private String url;

    // 歌词url
    private String lrc;

    // 上传人账号
    private String upUid;

    // 上传的用户名
    private String upUser;

    // 0:原唱;1:伴奏;
    private Byte musicType;

    // 音乐时长
    private Integer musicTime;

    // 文件大小
    private Integer size;

    // 音乐文件md5
    private String md5;

    // 0:文件已删除;1:审批中;2:文件上传成功;3:文件审核不通过;
    private Byte status;

    // t_music表中的id
    private Integer musicId;

    // 文件上传时间
    private Long cTime;

    /**
     * 获取id
     *
     * @return id - id
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取歌曲封面
     *
     * @return album - 歌曲封面
     */
    public String getAlbum() {
        return album;
    }

    /**
     * 设置歌曲封面
     *
     * @param album 歌曲封面
     */
    public void setAlbum(String album) {
        this.album = album;
    }

    /**
     * 获取文件上传的名称
     *
     * @return name - 文件上传的名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置文件上传的名称
     *
     * @param name 文件上传的名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取歌手
     *
     * @return artist - 歌手
     */
    public String getArtist() {
        return artist;
    }

    /**
     * 设置歌手
     *
     * @param artist 歌手
     */
    public void setArtist(String artist) {
        this.artist = artist;
    }

    /**
     * 获取访问文件url
     *
     * @return url - 访问文件url
     */
    public String getUrl() {
        return url;
    }

    /**
     * 设置访问文件url
     *
     * @param url 访问文件url
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 获取歌词url
     *
     * @return lrc - 歌词url
     */
    public String getLrc() {
        return lrc;
    }

    /**
     * 设置歌词url
     *
     * @param lrc 歌词url
     */
    public void setLrc(String lrc) {
        this.lrc = lrc;
    }

    /**
     * 获取更新人账号
     *
     * @return up_uid - 更新人账号
     */
    public String getUpUid() {
        return upUid;
    }

    /**
     * 设置更新人账号
     *
     * @param upUid 更新人账号
     */
    public void setUpUid(String upUid) {
        this.upUid = upUid;
    }

    /**
     * 获取更新的用户名
     *
     * @return up_user - 更新的用户名
     */
    public String getUpUser() {
        return upUser;
    }

    /**
     * 设置更新的用户名
     *
     * @param upUser 更新的用户名
     */
    public void setUpUser(String upUser) {
        this.upUser = upUser;
    }

    /**
     * 获取0:原唱;1:伴奏;
     *
     * @return music_type - 0:原唱;1:伴奏;
     */
    public Byte getMusicType() {
        return musicType;
    }

    /**
     * 设置0:原唱;1:伴奏;
     *
     * @param musicType 0:原唱;1:伴奏;
     */
    public void setMusicType(Byte musicType) {
        this.musicType = musicType;
    }

    public Integer getMusicId() {
        return musicId;
    }

    public void setMusicId(Integer musicId) {
        this.musicId = musicId;
    }

    /**
     * 获取音乐时长
     *
     * @return music_time - 音乐时长
     */
    public Integer getMusicTime() {
        return musicTime;
    }

    /**
     * 设置音乐时长
     *
     * @param musicTime 音乐时长
     */
    public void setMusicTime(Integer musicTime) {
        this.musicTime = musicTime;
    }

    /**
     * 获取文件大小
     *
     * @return size - 文件大小
     */
    public Integer getSize() {
        return size;
    }

    /**
     * 设置文件大小
     *
     * @param size 文件大小
     */
    public void setSize(Integer size) {
        this.size = size;
    }

    /**
     * 获取音乐文件md5
     *
     * @return md5 - 音乐文件md5
     */
    public String getMd5() {
        return md5;
    }

    /**
     * 设置音乐文件md5
     *
     * @param md5 音乐文件md5
     */
    public void setMd5(String md5) {
        this.md5 = md5;
    }

    /**
     * 获取0:文件已删除;1:审批中;2:文件上传成功;3:文件审核不通过;
     *
     * @return status - 0:文件已删除;1:审批中;2:文件上传成功;3:文件审核不通过;
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置0:文件已删除;1:审批中;2:文件上传成功;3:文件审核不通过;
     *
     * @param status 0:文件已删除;1:审批中;2:文件上传成功;3:文件审核不通过;
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取文件上传时间
     *
     * @return c_time - 文件上传时间
     */
    public Long getcTime() {
        return cTime;
    }

    /**
     * 设置文件上传时间
     *
     * @param cTime 文件上传时间
     */
    public void setcTime(Long cTime) {
        this.cTime = cTime;
    }

    @Override
    public String toString() {
        return "MusicFile{" +
                "id=" + id +
                ", album='" + album + '\'' +
                ", name='" + name + '\'' +
                ", artist='" + artist + '\'' +
                ", url='" + url + '\'' +
                ", lrc=" + lrc +
                ", upUid='" + upUid + '\'' +
                ", upUser='" + upUser + '\'' +
                ", musicType=" + musicType +
                ", musicTime=" + musicTime +
                ", size=" + size +
                ", md5='" + md5 + '\'' +
                ", status=" + status +
                ", musicId=" + musicId +
                ", cTime=" + cTime +
                '}';
    }
}
