package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/7/29
 */
public class Dau implements Serializable {
    private static final long serialVersionUID = 1173260739644900692L;
    private Integer id;
    // 用户uid
    private String userId;
    //性别 1男 2女
    private String fbGender;
    // 是否新用户 1是0否
    private Integer isNew;
    // 是否ios 1是0否
    private Integer os;
    // 创建时间
    private Integer ctime;
    // 更新时间
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getFbGender() {
        return fbGender;
    }

    public void setFbGender(String fbGender) {
        this.fbGender = fbGender;
    }

    @Override
    public String toString() {
        return "Dau{" +
                "id=" + id +
                ", userId='" + userId + '\'' +
                ", fbGender='" + fbGender + '\'' +
                ", isNew=" + isNew +
                ", os=" + os +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
