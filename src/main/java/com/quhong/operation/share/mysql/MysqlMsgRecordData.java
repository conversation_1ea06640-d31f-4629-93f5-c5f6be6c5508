package com.quhong.operation.share.mysql;

public class MysqlMsgRecordData {
    private int id;
    private String msgId;
    private String msgIndex; // 用于关联两个用户间的所有会话
    private String fromUid; //  发送方
    private String toUid; //  接收方
    private String msg; // 消息内容
    private String msgInfo; //  语音消息特有字段，文字消息为一个空字典
    private int msgType; //  消息类型，1为文字，2为语音
    private int fromDelete; //  1表示a删除了消息
    private int toDelete; // 表示b删除了消息
    private long timestamp; //  消息发出时间
    private int status; // 0 无 1 撤回
    private String fromLike;
    private String toLike;

    public MysqlMsgRecordData(){

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getMsgIndex() {
        return msgIndex;
    }

    public void setMsgIndex(String msgIndex) {
        this.msgIndex = msgIndex;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getToUid() {
        return toUid;
    }

    public void setToUid(String toUid) {
        this.toUid = toUid;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public int getFromDelete() {
        return fromDelete;
    }

    public void setFromDelete(int fromDelete) {
        this.fromDelete = fromDelete;
    }

    public int getToDelete() {
        return toDelete;
    }

    public void setToDelete(int toDelete) {
        this.toDelete = toDelete;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getFromLike() {
        return fromLike;
    }

    public void setFromLike(String fromLike) {
        this.fromLike = fromLike;
    }

    public String getToLike() {
        return toLike;
    }

    public void setToLike(String toLike) {
        this.toLike = toLike;
    }

}
