package com.quhong.operation.share.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("s_op_big_r")
public class OpBigRData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    @TableField("user_id")
    private String userId;
    @TableField("r_level")
    private Integer rLevel; //用户价值等级 0-4，0未知，1为历史充值用户，4为大R,大R按30天内充值或荣誉积分大于1m统计
    @TableField("honor_exp")
    private Long honorExp; //最后更新时的荣誉积分
    @TableField("loss_level")
    private Integer lossLevel; // 流失等级0-5，0未知，1为正常用户，5为完全流失,-1为7天内注册还不能评定等级的新用户
    @TableField("last_30_charge")
    private Float last30Charge; // 最近30天的内的充值，单位$
    @TableField("last_li_time")
    private Long lastLiTime; // 最后一次登入或者登出im时间
    @TableField("last_cg_time")
    private Long lastCgTime; // 最后一次充值时间
    @TableField("last_15_rtime")
    private Integer last15Rtime; // 最近15天内在房间的时长
    @TableField("last_7_send")
    private Long last7Send; // 最近7天发礼物钻石
    @TableField("last_7_recv")
    private Long last7Recv; // 最近7天收礼物钻石
    @TableField("ctime")
    private Long ctime; // 数据最后更新时间
    @TableField("become_day")
    private Integer becomeDay; // 成为充值用户的时间,格式如20201201
    @TableField("expand_origin")
    private Integer expandOrigin; // 拓展来源  # 0未知  1:线下拓展 2:推广

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getrLevel() {
        return rLevel;
    }

    public void setrLevel(Integer rLevel) {
        this.rLevel = rLevel;
    }

    public Long getHonorExp() {
        return honorExp;
    }

    public void setHonorExp(Long honorExp) {
        this.honorExp = honorExp;
    }

    public Integer getLossLevel() {
        return lossLevel;
    }

    public void setLossLevel(Integer lossLevel) {
        this.lossLevel = lossLevel;
    }

    public Float getLast30Charge() {
        return last30Charge;
    }

    public void setLast30Charge(Float last30Charge) {
        this.last30Charge = last30Charge;
    }

    public Long getLastLiTime() {
        return lastLiTime;
    }

    public void setLastLiTime(Long lastLiTime) {
        this.lastLiTime = lastLiTime;
    }

    public Long getLastCgTime() {
        return lastCgTime;
    }

    public void setLastCgTime(Long lastCgTime) {
        this.lastCgTime = lastCgTime;
    }

    public Integer getLast15Rtime() {
        return last15Rtime;
    }

    public void setLast15Rtime(Integer last15Rtime) {
        this.last15Rtime = last15Rtime;
    }

    public Long getLast7Send() {
        return last7Send;
    }

    public void setLast7Send(Long last7Send) {
        this.last7Send = last7Send;
    }

    public Long getLast7Recv() {
        return last7Recv;
    }

    public void setLast7Recv(Long last7Recv) {
        this.last7Recv = last7Recv;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Integer getBecomeDay() {
        return becomeDay;
    }

    public void setBecomeDay(Integer becomeDay) {
        this.becomeDay = becomeDay;
    }

    public Integer getExpandOrigin() {
        return expandOrigin;
    }

    public void setExpandOrigin(Integer expandOrigin) {
        this.expandOrigin = expandOrigin;
    }
}
