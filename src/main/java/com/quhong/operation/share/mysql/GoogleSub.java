package com.quhong.operation.share.mysql;

import java.io.Serializable;

public class GoogleSub implements Serializable {
    private static final long serialVersionUID = -6197607773945051813L;

    public static final byte DATA_INITIAL = 0;
    public static final byte SUB_SVIP = 1;
    public static final byte CANCEL_SVIP_SUB = 2;
    public static final byte VERIFY_NOT_PASS = 3;
    public static final byte RENEW_SVIP = 4;

    /**
     * 自增长id
     */
    private Integer rid;

    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 订单id
     */
    private String orderId;

    private String packageName;

    private String purchaseToken;

    /**
     * 支付取消了还是支付了
     */
    private Integer purchaseState;
    /**
     * svip开始时间
     */
    private Long purchaseTime;

    /**
     * svip过期时间
     */
    private Long expiresTimestamp;

    /**
     * 是否自动续费
     */
    private Byte autoRenewStatus;

    /**
     * 是否体验期
     */
    private Byte isTrialPeriod;

    /**
     * 渠道
     */
    private String channel;

    // 谷歌回调的类型通知
    private Integer notType;

    /**
     * 设置失败状态
     * 0 数据存入状态
     * 1 订阅中/订阅成功状态
     * 2 取消订阅状态
     */
    private Byte fstatus;

    /**
     * 创建时间
     */
    private Long ctime;

    /**
     * 更新时间
     */
    private Long mtime;

    /**
     * 获取自增长id
     *
     * @return rid - 自增长id
     */
    public Integer getRid() {
        return rid;
    }

    /**
     * 设置自增长id
     *
     * @param rid 自增长id
     */
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    /**
     * 获取用户唯一标识
     *
     * @return user_id - 用户唯一标识
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置用户唯一标识
     *
     * @param userId 用户唯一标识
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 获取商品id
     *
     * @return product_id - 商品id
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 设置商品id
     *
     * @param productId 商品id
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * 获取订单id
     *
     * @return order_id - 订单id
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置订单id
     *
     * @param orderId 订单id
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * @return package_name
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * @param packageName
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * @return purchase_token
     */
    public String getPurchaseToken() {
        return purchaseToken;
    }

    /**
     * @param purchaseToken
     */
    public void setPurchaseToken(String purchaseToken) {
        this.purchaseToken = purchaseToken;
    }

    public Integer getPurchaseState() {
        return purchaseState;
    }

    public void setPurchaseState(Integer purchaseState) {
        this.purchaseState = purchaseState;
    }

    /**
     * 获取svip开始时间
     *
     * @return purchase_timestamp - svip开始时间
     */
    public Long getPurchaseTime() {
        return purchaseTime;
    }

    /**
     * 设置svip开始时间
     *
     * @param purchaseTime svip开始时间
     */
    public void setPurchaseTime(Long purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    /**
     * 获取svip过期时间
     *
     * @return expires_timestamp - svip过期时间
     */
    public Long getExpiresTimestamp() {
        return expiresTimestamp;
    }

    /**
     * 设置svip过期时间
     *
     * @param expiresTimestamp svip过期时间
     */
    public void setExpiresTimestamp(Long expiresTimestamp) {
        this.expiresTimestamp = expiresTimestamp;
    }

    /**
     * 获取是否自动续费
     *
     * @return auto_renew_status - 是否自动续费
     */
    public Byte getAutoRenewStatus() {
        return autoRenewStatus;
    }

    /**
     * 设置是否自动续费
     *
     * @param autoRenewStatus 是否自动续费
     */
    public void setAutoRenewStatus(Byte autoRenewStatus) {
        this.autoRenewStatus = autoRenewStatus;
    }

    /**
     * 获取是否体验期
     *
     * @return is_trial_period - 是否体验期
     */
    public Byte getIsTrialPeriod() {
        return isTrialPeriod;
    }

    /**
     * 设置是否体验期
     *
     * @param isTrialPeriod 是否体验期
     */
    public void setIsTrialPeriod(Byte isTrialPeriod) {
        this.isTrialPeriod = isTrialPeriod;
    }

    /**
     * 获取渠道
     *
     * @return channel - 渠道
     */
    public String getChannel() {
        return channel;
    }

    /**
     * 设置渠道
     *
     * @param channel 渠道
     */
    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Integer getNotType() {
        return notType;
    }

    public void setNotType(Integer notType) {
        this.notType = notType;
    }

    /**
     * 获取失败状态
     *
     * @return fstatus - 失败状态
     */
    public Byte getFstatus() {
        return fstatus;
    }

    /**
     * @param fstatus 失败状态
     */
    public void setFstatus(Byte fstatus) {
        this.fstatus = fstatus;
    }

    /**
     * 获取创建时间
     *
     * @return ctime - 创建时间
     */
    public Long getCtime() {
        return ctime;
    }

    /**
     * 设置创建时间
     *
     * @param ctime 创建时间
     */
    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    /**
     * 获取更新时间
     *
     * @return mtime - 更新时间
     */
    public Long getMtime() {
        return mtime;
    }

    /**
     * 设置更新时间
     *
     * @param mtime 更新时间
     */
    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "GoogleSubscription{" +
                "rid=" + rid +
                ", userId='" + userId + '\'' +
                ", productId='" + productId + '\'' +
                ", orderId='" + orderId + '\'' +
                ", packageName='" + packageName + '\'' +
                ", purchaseToken='" + purchaseToken + '\'' +
                ", purchaseState=" + purchaseState +
                ", purchaseTime=" + purchaseTime +
                ", expiresTimestamp=" + expiresTimestamp +
                ", autoRenewStatus=" + autoRenewStatus +
                ", isTrialPeriod=" + isTrialPeriod +
                ", channel='" + channel + '\'' +
                ", notType=" + notType +
                ", fstatus=" + fstatus +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
