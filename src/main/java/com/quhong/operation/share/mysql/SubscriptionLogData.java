package com.quhong.operation.share.mysql;

public class SubscriptionLogData {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 原始订单号
     */
    private String originalOrderId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 0 android, 1 ios
     */
    private Integer os;

    /**
     * 国家编码
     */
    private String priceCurrencyCode;

    /**
     * 价格 * 1000000
     */
    private Long priceAmountMicros;

    /**
     * 1 正常订阅 2 试用期订阅 3 测试订阅 4 续订
     */
    private Integer type;

    /**
     * 订阅类型 1 恢复订阅 2 续订 3 取消订阅 4 订阅 12 撤销订阅
     */
    private Integer subscriptionType;

    /**
     * vip 开始时间
     */
    private Long startAt;

    /**
     * vip 结束时间
     */
    private Long endAt;

    /**
     * 创建时间
     */
    private Long ctime;

    /**
     * 更新时间
     */
    private Long mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOriginalOrderId() {
        return originalOrderId;
    }

    public void setOriginalOrderId(String originalOrderId) {
        this.originalOrderId = originalOrderId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public String getPriceCurrencyCode() {
        return priceCurrencyCode;
    }

    public void setPriceCurrencyCode(String priceCurrencyCode) {
        this.priceCurrencyCode = priceCurrencyCode;
    }

    public Long getPriceAmountMicros() {
        return priceAmountMicros;
    }

    public void setPriceAmountMicros(Long priceAmountMicros) {
        this.priceAmountMicros = priceAmountMicros;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSubscriptionType() {
        return subscriptionType;
    }

    public void setSubscriptionType(Integer subscriptionType) {
        this.subscriptionType = subscriptionType;
    }

    public Long getStartAt() {
        return startAt;
    }

    public void setStartAt(Long startAt) {
        this.startAt = startAt;
    }

    public Long getEndAt() {
        return endAt;
    }

    public void setEndAt(Long endAt) {
        this.endAt = endAt;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getMtime() {
        return mtime;
    }

    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }
}
