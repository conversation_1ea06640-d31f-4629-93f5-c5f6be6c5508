package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/9/18
 */
public class AppleSub implements Serializable {
    private static final long serialVersionUID = 8863921554150876221L;

    public static final int DATA_INITIAL = 0; // 数据入库
    public static final int SUB_SVIP = 1; // svip
    public static final int RENEW_SVIP = 7; // 续订svip
    public static final int CANCEL_SUB = 2; // 取消订阅
    public static final int VERIFY_NOT_PASS = 3; // apple服务器校验不通过
    public static final int AGAIN_APPLE_VERIFY = 4; // 可再次查询结果
    public static final int RECEIPT_OVERDUE = 5; // 收据有效，但是过期了
    public static final int SVIP_TRANSFER = 6; // svip转移了
    public static final int SVIP_RESTORE = 8; // svip还原恢复

    /**
     * 自增长id
     */
    private Integer rid;

    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 商品id
     */
    private String productId;

    private String orderId;

    private String originalTransactionId;

    /**
     * 交易id
     */
    private String transactionId;

    private String receiptData;

    private String receiptDataMd5;

    /**
     * 购买时间
     */
    private Long purchaseTimestamp;

    /**
     * 到期时间
     */
    private Long expiresTimestamp;

    /**
     * 是否自动续费
     */
    private Byte autoRenewStatus;

    /**
     * 是否体验期
     */
    private Integer isTrialPeriod;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 0=验证中的订单
     * 1=已经成功
     * 2=t_apple_pay 验证失败
     * 3=apple服务器连接异常
     * 4=apple内部异常
     * 5=apple服务器校验不同
     *
     *
     * 0入库
     * 1校验通过
     * 2取消订阅
     * 3校验不通过
     * 4可重复校验
     */
    private Integer fstatus;

    // apple返回的收据状态码
    private Integer notType;

    /**
     * 记录创建时间
     */
    private Long ctime;

    /**
     * 更新时间
     */
    private Long mtime;

    /**
     * 获取自增长id
     *
     * @return rid - 自增长id
     */
    public Integer getRid() {
        return rid;
    }

    /**
     * 设置自增长id
     *
     * @param rid 自增长id
     */
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    /**
     * 获取用户唯一标识
     *
     * @return user_id - 用户唯一标识
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置用户唯一标识
     *
     * @param userId 用户唯一标识
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 获取商品id
     *
     * @return product_id - 商品id
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 设置商品id
     *
     * @param productId 商品id
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * @return original_transaction_id
     */
    public String getOriginalTransactionId() {
        return originalTransactionId;
    }

    /**
     * @param originalTransactionId
     */
    public void setOriginalTransactionId(String originalTransactionId) {
        this.originalTransactionId = originalTransactionId;
    }

    /**
     * 获取交易id
     *
     * @return transaction_id - 交易id
     */
    public String getTransactionId() {
        return transactionId;
    }

    /**
     * 设置交易id
     *
     * @param transactionId 交易id
     */
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    /**
     * @return receipt_data_md5
     */
    public String getReceiptDataMd5() {
        return receiptDataMd5;
    }

    /**
     * @param receiptDataMd5
     */
    public void setReceiptDataMd5(String receiptDataMd5) {
        this.receiptDataMd5 = receiptDataMd5;
    }

    /**
     * 获取购买时间
     *
     * @return purchase_timestamp - 购买时间
     */
    public Long getPurchaseTimestamp() {
        return purchaseTimestamp;
    }

    /**
     * 设置购买时间
     *
     * @param purchaseTimestamp 购买时间
     */
    public void setPurchaseTimestamp(Long purchaseTimestamp) {
        this.purchaseTimestamp = purchaseTimestamp;
    }

    /**
     * 获取到期时间
     *
     * @return expires_timestamp - 到期时间
     */
    public Long getExpiresTimestamp() {
        return expiresTimestamp;
    }

    /**
     * 设置到期时间
     *
     * @param expiresTimestamp 到期时间
     */
    public void setExpiresTimestamp(Long expiresTimestamp) {
        this.expiresTimestamp = expiresTimestamp;
    }

    /**
     * 获取是否自动续费
     *
     * @return auto_renew_status - 是否自动续费
     */
    public Byte getAutoRenewStatus() {
        return autoRenewStatus;
    }

    /**
     * 设置是否自动续费
     *
     * @param autoRenewStatus 是否自动续费
     */
    public void setAutoRenewStatus(Byte autoRenewStatus) {
        this.autoRenewStatus = autoRenewStatus;
    }

    /**
     * 获取是否体验期
     *
     * @return is_trial_period - 是否体验期
     */
    public Integer getIsTrialPeriod() {
        return isTrialPeriod;
    }

    /**
     * 设置是否体验期
     *
     * @param isTrialPeriod 是否体验期
     */
    public void setIsTrialPeriod(Integer isTrialPeriod) {
        this.isTrialPeriod = isTrialPeriod;
    }

    /**
     * 获取渠道
     *
     * @return channel - 渠道
     */
    public String getChannel() {
        return channel;
    }

    /**
     * 设置渠道
     *
     * @param channel 渠道
     */
    public void setChannel(String channel) {
        this.channel = channel;
    }

    /**
     * 获取失败状态
     *
     * @return fstatus - 失败状态
     */
    public Integer getFstatus() {
        return fstatus;
    }

    /**
     * 设置失败状态
     *
     * @param fstatus 失败状态
     */
    public void setFstatus(Integer fstatus) {
        this.fstatus = fstatus;
    }

    public Integer getNotType() {
        return notType;
    }

    public void setNotType(Integer notType) {
        this.notType = notType;
    }

    /**
     * 获取记录创建时间
     *
     * @return ctime - 记录创建时间
     */
    public Long getCtime() {
        return ctime;
    }

    /**
     * 设置记录创建时间
     *
     * @param ctime 记录创建时间
     */
    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    /**
     * 获取更新时间
     *
     * @return mtime - 更新时间
     */
    public Long getMtime() {
        return mtime;
    }

    /**
     * 设置更新时间
     *
     * @param mtime 更新时间
     */
    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    /**
     * @return receipt_data
     */
    public String getReceiptData() {
        return receiptData;
    }

    /**
     * @param receiptData
     */
    public void setReceiptData(String receiptData) {
        this.receiptData = receiptData;
    }

    @Override
    public String toString() {
        return "Subscription{" +
                "rid=" + rid +
                ", userId='" + userId + '\'' +
                ", productId='" + productId + '\'' +
                ", orderId='" + orderId + '\'' +
                ", originalTransactionId='" + originalTransactionId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", receiptData='" + receiptData + '\'' +
                ", receiptDataMd5='" + receiptDataMd5 + '\'' +
                ", purchaseTimestamp=" + purchaseTimestamp +
                ", expiresTimestamp=" + expiresTimestamp +
                ", autoRenewStatus=" + autoRenewStatus +
                ", isTrialPeriod=" + isTrialPeriod +
                ", channel='" + channel + '\'' +
                ", fstatus=" + fstatus +
                ", notType=" + notType +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }

    public String toString2() {
        return "Subscription{" +
                "rid=" + rid +
                ", userId='" + userId + '\'' +
                ", productId='" + productId + '\'' +
                ", orderId='" + orderId + '\'' +
                ", originalTransactionId='" + originalTransactionId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", receiptDataMd5='" + receiptDataMd5 + '\'' +
                ", purchaseTimestamp=" + purchaseTimestamp +
                ", expiresTimestamp=" + expiresTimestamp +
                ", autoRenewStatus=" + autoRenewStatus +
                ", isTrialPeriod=" + isTrialPeriod +
                ", channel='" + channel + '\'' +
                ", fstatus=" + fstatus +
                ", notType=" + notType +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }

}
