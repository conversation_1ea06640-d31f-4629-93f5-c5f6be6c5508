package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * operation后台打钻记录表
 *
 * <AUTHOR>
 * @date 2020/8/17
 */
public class SendBeanLog implements Serializable {

    private static final long serialVersionUID = 5348812334390012216L;

    private Integer id;
    private String uid;
    private Integer inRoomTime = 0;
    private Integer sendBean = 0;
    private Integer status = 1;
    private Long ctime;
    private Integer micTime;
    private Integer sendRedNumber;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getInRoomTime() {
        return inRoomTime;
    }

    public void setInRoomTime(Integer inRoomTime) {
        this.inRoomTime = inRoomTime;
    }

    public Integer getSendBean() {
        return sendBean;
    }

    public void setSendBean(Integer sendBean) {
        this.sendBean = sendBean;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Integer getMicTime() {
        return micTime;
    }

    public void setMicTime(Integer micTime) {
        this.micTime = micTime;
    }

    public Integer getSendRedNumber() {
        return sendRedNumber;
    }

    public void setSendRedNumber(Integer sendRedNumber) {
        this.sendRedNumber = sendRedNumber;
    }

    @Override
    public String toString() {
        return "SendBeanLog{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", inRoomTime=" + inRoomTime +
                ", sendBean=" + sendBean +
                ", status=" + status +
                ", ctime=" + ctime +
                ", micTime=" + micTime +
                ", sendRedNumber=" + sendRedNumber +
                '}';
    }
}
