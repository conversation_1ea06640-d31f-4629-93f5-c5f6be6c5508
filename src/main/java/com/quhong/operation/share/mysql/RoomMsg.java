package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * 房间内消息记录
 *
 * <AUTHOR>
 * @date 2020/7/31
 */
public class RoomMsg implements Serializable {
    private static final long serialVersionUID = 5982566545234186729L;

    private Integer id;
    // 房间id
    private String roomId;
    // 消息分类
    private String msgCategory;
    // 消息类型
    private Integer msyType;
    // 消息体
    private String msgBody;
    // 发送人数
    private Integer sendUserCount;
    // 房间当前人数
    private Integer userCount;
    // 消息id
    private String msgId;
    // 创建时间
    private Integer ctime;
    // 0 android；1 ios
    private String fromOs;
    // 发送者uid
    private String fromUid;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getMsgCategory() {
        return msgCategory;
    }

    public void setMsgCategory(String msgCategory) {
        this.msgCategory = msgCategory;
    }

    public Integer getMsyType() {
        return msyType;
    }

    public void setMsyType(Integer msyType) {
        this.msyType = msyType;
    }

    public String getMsgBody() {
        return msgBody;
    }

    public void setMsgBody(String msgBody) {
        this.msgBody = msgBody;
    }

    public Integer getSendUserCount() {
        return sendUserCount;
    }

    public void setSendUserCount(Integer sendUserCount) {
        this.sendUserCount = sendUserCount;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public String getFromOs() {
        return fromOs;
    }

    public void setFromOs(String fromOs) {
        this.fromOs = fromOs;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }
}
