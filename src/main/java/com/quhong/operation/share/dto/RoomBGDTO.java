package com.quhong.operation.share.dto;

import java.util.Map;

/**
 * 房间背景的配置
 * @date 2022/10/19
 */
public class RoomBGDTO {

    private String docId;  // 更新时用到
    private int bgType;
    private String preview;
    private String background;
    private String title;
    private String titleAr;
    private int status;
    private int newtheme;
    private int currencyType;
    private int price;
    private int validDate;

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public int getBgType() {
        return bgType;
    }

    public void setBgType(int bgType) {
        this.bgType = bgType;
    }

    public String getPreview() {
        return preview;
    }

    public void setPreview(String preview) {
        this.preview = preview;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getNewtheme() {
        return newtheme;
    }

    public void setNewtheme(int newtheme) {
        this.newtheme = newtheme;
    }

    public int getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(int currencyType) {
        this.currencyType = currencyType;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getValidDate() {
        return validDate;
    }

    public void setValidDate(int validDate) {
        this.validDate = validDate;
    }

    @Override
    public String toString() {
        return "RoomBGDTO{" +
                "docId='" + docId + '\'' +
                ", bgType=" + bgType +
                ", preview='" + preview + '\'' +
                ", background='" + background + '\'' +
                ", title='" + title + '\'' +
                ", titleAr='" + titleAr + '\'' +
                ", status=" + status +
                ", newtheme=" + newtheme +
                ", currencyType=" + currencyType +
                ", price=" + price +
                ", validDate=" + validDate +
                '}';
    }
}
