package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.quhong.data.ActorData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.service.ZegoRecordService;
import com.quhong.operation.clients.zego.DTO.CallbackDetail;
import com.quhong.operation.clients.zego.DTO.RecordCallback;
import com.quhong.operation.clients.zego.DTO.StartRecordResp;
import com.quhong.operation.clients.zego.ZegoClient;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.vo.PageVo;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;


@RestController
@RequestMapping("/api/zego")
public class ZegoApiController {
    private static final Logger logger = LoggerFactory.getLogger(ZegoApiController.class);
    private static final Set<String> ADMIN_SET = new HashSet<>(Arrays.asList("5ed9b3311e23cd01bb74dd60"));

    @Value("${online:true}")
    private boolean online;
    @Resource
    private ZegoClient zegoClient;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ZegoRecordService zegoRecordService;


    private boolean checkPrivilege(String uid) {
        if (!online) {
            return false;
        }
        return !ADMIN_SET.contains(uid);
    }

    /**
     * 开始录制
     * <a href="https://doc-zh.zego.im/article/12322">https://doc-zh.zego.im/article/12322</a>
     */
    @RequestMapping("/startRecord")
    public HttpResult<Object> startRecord(HttpServletRequest request, Integer rid) {
        logger.info("startRecord rid={}", rid);
        HttpResult<Object> result = new HttpResult<>();
        try {
            if (checkPrivilege(request.getParameter("uid"))) {
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (null == rid) {
                return result.error("rid不能为空。");
            }
            ActorData actorData = actorDao.getActorByRid(rid);
            String roomId = RoomUtils.formatRoomId(actorData.getUid());
            if (zegoRecordService.isRecording(roomId)) {
                return result.error("该房间正在录制中，请先停止后再录制。");
            }
            StartRecordResp resp = zegoClient.startRecord(roomId, rid);
            if (null == resp || 0 != resp.getCode()) {
                return result.error("zego响应错误: " + (null != resp ? resp.getMessage() : ""));
            }
            zegoRecordService.startRecord(rid, roomId, resp.getData().getTaskId());
            return result.ok();
        } catch (Exception e) {
            logger.error("startRecord error {}", e.getMessage());
            return result.error(e.getMessage());
        }
    }

    /**
     * 结束录制
     * <a href="https://doc-zh.zego.im/article/12350">https://doc-zh.zego.im/article/12350</a>
     */
    @RequestMapping("/stopRecord")
    public HttpResult<Object> stopRecord(HttpServletRequest request, String taskId) {
        logger.info("stopRecord taskId={}", taskId);
        HttpResult<Object> result = new HttpResult<>();
        try {
            if (checkPrivilege(request.getParameter("uid"))) {
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            zegoRecordService.endRecord(taskId);
            StartRecordResp resp = zegoClient.stopRecord(taskId);
            if (null == resp || 0 != resp.getCode()) {
                return result.error("zego响应错误: " + (null != resp ? resp.getMessage() : ""));
            }
            return result.ok();
        } catch (Exception e) {
            logger.error("stopRecord error {}", e.getMessage());
            return result.error(e.getMessage());
        }
    }

    /**
     * 获取录制列表
     */
    @RequestMapping("/recordList")
    public HttpResult<PageVo> recordList(HttpServletRequest request, int page) {
        logger.info("recordList page={}", page);
        HttpResult<PageVo> result = new HttpResult<>();
        if (checkPrivilege(request.getParameter("uid"))) {
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        try {
            return result.ok(zegoRecordService.getPageRecord(page));
        } catch (Exception e) {
            logger.error("recordList error {}", e.getMessage());
            return result.error(e.getMessage());
        }
    }

    /**
     * <a href="https://doc-zh.zego.im/article/12324">https://doc-zh.zego.im/article/12324</a>
     * 事件通知类型。
     * 1：录制文件上传状态通知，详细信息请查看 detail 参数。
     * 2：录制任务异常结束状态通知，详细信息请查看 detail 参数。
     * 3：录制过程中自定义背景图/水印图下载失败通知，详细信息请查看 detail 参数。
     * 4：录制过程中房间内流数量为 0 通知。
     * 房间内流的数量变为 0 后的 30 秒内会触发此事件。首次触发后，若房间内一直无流，会每隔 30 秒触发一次此事件直到有流或者任务因无流超时异常结束。
     * 5：录制正常退出通知。
     * 6：录制的流不存在，详细信息请查看 detail 参数。
     */
    @PostMapping("/callback")
    public void callback(@RequestBody String json) {
        try {
            logger.info("RecordCallback data={}", json);
            RecordCallback callback = JSON.parseObject(json, RecordCallback.class);
            if (null == callback) {
                logger.error("callback data is null. body={}", json);
                return;
            }
            if (callback.getEvent_type() == 1) {
                CallbackDetail detail = callback.getDetail();
                if (null != detail && !CollectionUtils.isEmpty(detail.getFile_info())) {
                    logger.info("RecordCallback fileSize={}", detail.getFile_info().size());
                    zegoRecordService.finishRecord(callback.getTask_id(), detail.getFile_info().get(0));
                }
            } else if (callback.getEvent_type() != 4) {
                zegoRecordService.endRecord(callback.getTask_id());
            }
        } catch (Exception e) {
            logger.error("callback error {}", e.getMessage());
        }
    }
}
