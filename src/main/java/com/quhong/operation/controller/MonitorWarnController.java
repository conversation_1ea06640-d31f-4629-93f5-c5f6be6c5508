package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.MonitorWarnService;
import com.quhong.operation.share.condition.MonitorWarnCondition;
import com.quhong.operation.share.vo.MonitorWarnVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 测试白名单配置
 */

@RestController
@RequestMapping(value ="/monitorWarn", produces = MediaType.APPLICATION_JSON_VALUE)
public class MonitorWarnController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(MonitorWarnController.class);

    @Resource
    private MonitorWarnService monitorWarnService;


    @RequireRole
    @RequestMapping("/list")
    public String getDataList(@RequestBody MonitorWarnCondition condition) {
        logger.info("getDataList {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, monitorWarnService.getDataList(condition));
    }


    @RequireRole
    @PostMapping("/addData")
    public String addData(@RequestBody MonitorWarnVO dto) {
        try {
            logger.info("addData MonitorWarnVO Data {}", JSONObject.toJSONString(dto));
            monitorWarnService.addData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    @RequireRole
    @PostMapping("/updateData")
    public String updateData(@RequestBody MonitorWarnVO dto) {
        try {
            logger.info("update WhiteTestVO Data {}", JSONObject.toJSONString(dto));
            monitorWarnService.updateData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    @RequireRole
    @PostMapping("/deleteData")
    public String deleteData(@RequestBody MonitorWarnVO dto) {
        try {
            logger.info("deleteData MonitorWarnVO Data {}", JSONObject.toJSONString(dto));
            monitorWarnService.deleteData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }
}
