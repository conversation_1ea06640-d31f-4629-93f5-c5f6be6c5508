package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.SmashEggConfigData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.SmashEggService;
import com.quhong.operation.share.condition.SmashEggCondition;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 砸蛋配置
 */

@RestController
@RequestMapping(value ="/smashEgg", produces = MediaType.APPLICATION_JSON_VALUE)
public class SmashEggController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(SmashEggController.class);
    private final static String filePath = "common/";

    @Resource
    private SmashEggService smashEggService;


    @RequireRole
    @RequestMapping("/list")
    public String getDataList(@RequestBody SmashEggCondition condition) {
        logger.info("get emojiList {}", condition);
        return createResult(HttpCode.SUCCESS, smashEggService.getDataList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public String addData(@RequestBody SmashEggConfigData dto) {
        try {
            logger.info("addEmojiData {}", dto);
            smashEggService.addData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateData(@RequestBody SmashEggConfigData dto) {
        try {
            logger.info("updateEmojiData {}", dto);
            if (StringUtils.isEmpty(dto.getId())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }

            smashEggService.updateData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return AWSUploadUtils.upload(file, filePath);
    }

}
