package com.quhong.operation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mysql.dao.ActivityShareConfigDao;
import com.quhong.mysql.data.ActivityShareConfigData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.dto.ShareConfigDTO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4
 */
@RestController
@RequestMapping("/shareConfig")
public class ActivityShareController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(ActivityShareController.class);

    @Resource
    private ActivityShareConfigDao activityShareConfigDao;

    /**
     * 获取分享配置列表
     */
    @RequireRole(3)
    @RequestMapping("list")
    public HttpResult<PageResultVO<ActivityShareConfigData>> getList(@RequestBody ShareConfigDTO dto) {
        logger.info("get share config list. name={}, page={}, pageSize={}", dto.getName(), dto.getPage(), dto.getPageSize());
        HttpResult<PageResultVO<ActivityShareConfigData>> result = new HttpResult<>();
        PageResultVO<ActivityShareConfigData> pageVO = new PageResultVO<>();
        int page = dto.getPage() != null && dto.getPage() != 0 ? dto.getPage() : 1;
        int pageSize = dto.getPageSize() != null && dto.getPageSize() != 0 ? dto.getPageSize() : 30;
        IPage<ActivityShareConfigData> iPage = activityShareConfigDao.selectPage(dto.getName(), page, pageSize);
        pageVO.setList(iPage.getRecords());
        pageVO.setTotal(iPage.getTotal());
        return result.ok(pageVO);
    }

    /**
     * 保存分享配置
     */
    @RequireRole(3)
    @RequestMapping("save")
    public HttpResult save(@RequestBody ShareConfigDTO dto) {
        logger.info("save share config. name={}", dto.getName());
        HttpResult result = new HttpResult();
        try {
            ActivityShareConfigData data = new ActivityShareConfigData();
            BeanUtils.copyProperties(dto, data);
            data.setCtime(DateHelper.getNowSeconds());
            data.setCreator(dto.getUid());
            if (dto.getId() == null || dto.getId() == 0) {
                activityShareConfigDao.insert(data);
                data.setUrl(getActivityUrl(data.getUrl(), data.getId(), data.getActivityId()));
            }
            if (!data.getUrl().contains("shareId=")) {
                data.setUrl(getActivityUrl(data.getUrl(), data.getId(), null));
            }
            activityShareConfigDao.update(data);
        } catch (Exception e) {
            logger.error("save share config error. {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok();
    }

    /**
     * 删除分享配置
     */
    @RequireRole(3)
    @RequestMapping("delete")
    public HttpResult delete(@RequestBody ShareConfigDTO dto) {
        logger.info("delete share config. id={}", dto.getId());
        HttpResult result = new HttpResult();
        try {
            activityShareConfigDao.delete(dto.getId());
        } catch (Exception e) {
            logger.error("delete share config error. {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok();
    }

    private String getActivityUrl(String url, int shareId, String activityId) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        if (!StringUtils.isEmpty(activityId)) {
            urlBuilder.queryParam("activityId", activityId);
        }
        urlBuilder.queryParam("shareId", shareId);
        return urlBuilder.build(false).encode().toUriString();
    }
}
