package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.MomentTopicCategoryData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.MomentTopicService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.MomentTopicCondition;
import com.quhong.operation.share.dto.MomentTopicCategoryLabelDTO;
import com.quhong.operation.share.vo.MomentTopicVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value ="/momentTopic", produces = MediaType.APPLICATION_JSON_VALUE)
public class MomentTopicController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(MomentTopicController.class);

    @Resource
    private MomentTopicService momentTopicService;

    @RequireRole
    @RequestMapping("/categoryList")
    public String categoryList(@RequestBody BaseCondition condition) {
        logger.info("get categoryList {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, momentTopicService.categoryList(condition));
    }

    @RequireRole
    @RequestMapping("/categoryAdd")
    public String categoryAdd(@RequestBody MomentTopicCategoryData data) {
        logger.info("categoryAdd data:{}", JSONObject.toJSONString(data));
        momentTopicService.categoryAdd(data);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequireRole
    @RequestMapping("/categoryUpdate")
    public String categoryUpdate(@RequestBody MomentTopicCategoryData data) {
        logger.info("categoryUpdate data:{}", JSONObject.toJSONString(data));
        momentTopicService.categoryUpdate(data);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 话题分类与标签关联关系【根据分类id查找设置的兴趣标签】
    @RequireRole
    @RequestMapping("/categoryLabelList")
    public String categoryLabelList(@RequestParam int id, @RequestParam(defaultValue = "") String search) {
        logger.info("categoryLabelList id {} String search:{}", id, search);
        return createResult(HttpCode.SUCCESS, momentTopicService.categoryLabelList(id, search));
    }

    @RequireRole
    @RequestMapping("/categoryLabelSet")
    public String categoryLabelSet(@RequestBody MomentTopicCategoryLabelDTO dto) {
        logger.info("MomentTopicCategoryLabelDTO dto {}", JSONObject.toJSONString(dto));
        momentTopicService.categoryLabelSet(dto);
        return createResult(HttpCode.SUCCESS, null);
    }


    // 话题管理
    @RequireRole
    @RequestMapping("/momentTopicList")
    public String momentTopicList(@RequestBody MomentTopicCondition condition) {
        logger.info("get momentTopicList {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, momentTopicService.momentTopicList(condition));
    }

    @RequireRole
    @RequestMapping("/momentTopicAdd")
    public String momentTopicAdd(@RequestBody MomentTopicVO data) {
        logger.info("momentTopicAdd data:{}", JSONObject.toJSONString(data));
        momentTopicService.momentTopicAdd(data);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequireRole
    @RequestMapping("/momentTopicUpdate")
    public String momentTopicUpdate(@RequestParam String uid, @RequestBody MomentTopicVO data) {
        logger.info("momentTopicUpdate data:{}", JSONObject.toJSONString(data));
        momentTopicService.momentTopicUpdate(uid, data);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequireRole
    @RequestMapping("/momentTopicTopDelete")
    public String momentTopicTopDelete(@RequestParam String uid, @RequestBody MomentTopicVO data) {
        logger.info("momentTopicTopDelete data:{}", JSONObject.toJSONString(data));
        momentTopicService.momentTopicTopDelete(uid, data);
        return createResult(HttpCode.SUCCESS, null);
    }

}
