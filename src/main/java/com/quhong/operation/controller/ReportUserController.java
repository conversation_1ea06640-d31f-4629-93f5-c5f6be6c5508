package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.ReportUserService;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.ReportUserCondition;
import com.quhong.operation.share.dto.BlockUserMsgDTO;
import com.quhong.operation.share.dto.FloatScreenSourceDTO;
import com.quhong.operation.share.dto.MReportDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户举报列表
 *
 * <AUTHOR>
 * @date 2023/02/20
 */

@RestController
@RequestMapping(value ="/reportUser", produces = MediaType.APPLICATION_JSON_VALUE)
public class ReportUserController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(ReportUserController.class);

    @Resource
    private ReportUserService reportUserService;



    @RequireRole
    @RequestMapping("/list")
    public String reportUserList(@RequestBody ReportUserCondition condition) {

        logger.info("get reportUserList {}", condition);
        return createResult(HttpCode.SUCCESS, reportUserService.reportUserList(condition));

    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateReportUserData(@RequestBody MReportDTO dto) {
        logger.info("updateReportUserData {}", dto);
        if (StringUtils.isEmpty(dto.getDocId())) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }
        reportUserService.updateMReporteData(dto);
        return createResult(HttpCode.SUCCESS, "");
    }


    /**
     * 新版举报
     */
    @RequireRole
    @RequestMapping("/recordList")
    public String recordList(@RequestBody ReportUserCondition condition) {
        logger.info("get reportUserList {}", condition);
        return createResult(HttpCode.SUCCESS, reportUserService.reportUserRecordList(condition));
    }

    @RequireRole
    @RequestMapping("/reportUserRecordDetailList")
    public String reportUserRecordDetailList(@RequestBody ReportUserCondition condition) {
        logger.info("get reportUserRecordDetailList {}", condition);
        return createResult(HttpCode.SUCCESS, reportUserService.reportUserRecordDetailList(condition));
    }

    /**
     * 封禁用户聊天
     */
    @RequireRole
    @RequestMapping("/blockUserMsg")
    public String blockUserMsg(@RequestBody BlockUserMsgDTO dto) {
        logger.info("get BlockUserMsgDTO {}", dto);
        reportUserService.blockUserMsg(dto);
        return createResult(HttpCode.SUCCESS, null);
    }



}
