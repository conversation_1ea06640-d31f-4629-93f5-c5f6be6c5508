package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.ConfigCenterKeyData;
import com.quhong.mysql.data.ConfigCenterKeyLabelData;
import com.quhong.mysql.data.ConfigCenterLabelData;
import com.quhong.mysql.data.ConfigCenterValueData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.ConfigCenterModuleService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.ConfigCenterCondition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 浮萍图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/configCenter", produces = MediaType.APPLICATION_JSON_VALUE)
public class ConfigCenterController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(ConfigCenterController.class);
    private final static String filePath = "gift/";

    @Resource
    private ConfigCenterModuleService configCenterModuleService;

    @RequireRole
    @RequestMapping("/keyList")
    public String configKeyList(@RequestBody BaseCondition condition) {

        logger.info("get configKeyList {}", condition);
        return createResult(HttpCode.SUCCESS, configCenterModuleService.configCenterKeyList(condition));

    }

    @RequireRole
    @PostMapping("/addKeyData")
    public String addKeyData(@RequestParam String uid, @RequestBody ConfigCenterKeyData dto) {
        logger.info("uid: {}, addGiftData {}", uid, dto);
        try {
            configCenterModuleService.addConfigCenterKeyData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateKeyData")
    public String updateKeyData(@RequestParam String uid, @RequestBody ConfigCenterKeyData dto) {
        logger.info("updateKeyData {}", dto);
        try {
            configCenterModuleService.updateConfigCenterKeyData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/deleteKeyData")
    public String deleteKeyData(@RequestParam String uid, @RequestBody ConfigCenterKeyData dto) {
        logger.info("deleteKeyData {}", dto);
        try {
            configCenterModuleService.deleteConfigCenterKeyData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    @RequireRole
    @RequestMapping("/valueList")
    public String configValueList(@RequestBody ConfigCenterCondition condition) {
        logger.info("get configValueList {}", condition);
        return createResult(HttpCode.SUCCESS, configCenterModuleService.configCenterValueList(condition));

    }

    @RequireRole
    @PostMapping("/addValueData")
    public String addValueData(@RequestParam String uid, @RequestBody ConfigCenterValueData dto) {
        logger.info("uid: {}, ConfigCenterValueData {}", uid, dto);
        try {
            configCenterModuleService.addConfigCenterValueData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateValueData")
    public String updateValueData(@RequestParam String uid, @RequestBody ConfigCenterValueData dto) {
        logger.info("updateValueData {}", dto);
        try {
            configCenterModuleService.updateConfigCenterValueData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/deleteValueData")
    public String deleteValueData(@RequestParam String uid, @RequestBody ConfigCenterValueData dto) {
        logger.info("deleteValueData  uid: {}, ConfigCenterValueData {}", uid, dto);
        try {
            configCenterModuleService.deleteConfigCenterValueData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    @RequireRole
    @RequestMapping("/labelList")
    public String configLabelList(@RequestBody ConfigCenterCondition condition) {
        logger.info("get configValueList {}", condition);
        return createResult(HttpCode.SUCCESS, configCenterModuleService.configCenterLabelList(condition));

    }

    @RequireRole
    @PostMapping("/addLabelData")
    public String addLabelData(@RequestParam String uid, @RequestBody ConfigCenterLabelData dto) {
        logger.info("uid: {}, ConfigCenterLabelData {}", uid, dto);
        try {
            configCenterModuleService.addConfigCenterLabelData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateLabelData")
    public String updateLabelData(@RequestParam String uid, @RequestBody ConfigCenterLabelData dto) {
        logger.info("ConfigCenterLabelData {}", dto);
        try {
            configCenterModuleService.updateConfigCenterLabelData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/addKeyLabelData")
    public String addKeyLabelData(@RequestParam String uid, @RequestBody ConfigCenterKeyLabelData dto) {
        logger.info("uid: {}, ConfigCenterKeyLabelData {}", uid, dto);
        try {
            configCenterModuleService.addConfigCenterKeyLabelData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/deleteKeyLabelDataByKey")
    public String deleteKeyLabelDataByKey(@RequestParam String uid, @RequestBody ConfigCenterKeyLabelData dto) {
        logger.info("uid: {}, ConfigCenterKeyLabelData {}", uid, dto);
        try {
            configCenterModuleService.deleteConfigCenterKeyLabelData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    @RequireRole
    @PostMapping("/deleteKeyLabelDataById")
    public String deleteKeyLabelDataById(@RequestParam String uid, @RequestBody ConfigCenterKeyLabelData dto) {
        logger.info("uid: {}, ConfigCenterKeyLabelData {}", uid, dto);
        try {
            configCenterModuleService.deleteConfigCenterKeyLabelId(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

}
