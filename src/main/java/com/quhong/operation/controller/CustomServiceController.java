package com.quhong.operation.controller;

import com.quhong.data.dto.MsgRecordDTO;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.MsgRecordListVO;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.CustomerServiceService;
import com.quhong.operation.share.dto.CustomerServiceMsgListDTO;
import com.quhong.operation.share.dto.cutomerService.ServiceInfoDTO;
import com.quhong.operation.share.vo.UserOnlineVo;
import com.quhong.operation.share.vo.customerService.CustomerServiceVO;
import com.quhong.operation.share.vo.customerService.RechargeActorListVO;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/customer_service")
public class CustomServiceController {
    private static final Logger logger = LoggerFactory.getLogger(CustomServiceController.class);

    @Autowired
    private CustomerServiceService service;

    /**
     * 获取客服信息
     * @param request
     * @return
     */
    @RequireRole
    @RequestMapping("/service_info")
    public HttpResult<CustomerServiceVO> getServiceInfo(HttpServletRequest request, @RequestBody(required = false) ServiceInfoDTO dto) {
        String userId = RequestUtils.getParam(request, "uid");
        logger.info("get customer service user. {} userId={}", dto, userId);
        return service.getServiceInfo(dto);
    }

    /**
     * 获取最近充值用户登录列表
     * @param request
     * @return
     */
    @RequireRole
    @RequestMapping("/latest_list")
    public HttpResult<RechargeActorListVO> getLatestRechargeList(HttpServletRequest request, @RequestBody(required = false) CustomerServiceMsgListDTO dto) {
        String userId = RequestUtils.getParam(request, "uid");
        logger.info("get recharge actor list. userId={}", userId);
        return service.getUserList(dto);
    }

    @RequireRole
    @RequestMapping("/send_msg")
    public HttpResult<Object> sendMsg(@RequestBody SendChatMsgDTO dto){
        logger.info("send msg. {}", dto);
        return service.sendMsg(dto);
    }

    @RequireRole
    @RequestMapping("/msg_records")
    public HttpResult<MsgRecordListVO> getRecordList(@RequestBody MsgRecordDTO dto){
        logger.info("get record list. {}", dto);
        return service.getRecordList(dto);
    }
}
