package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.OfficialWelcomeService;
import com.quhong.operation.share.condition.OfficialWelcomeCondition;
import com.quhong.operation.share.dto.OfficialWelcomeDTO;
import com.quhong.operation.share.vo.OfficialWelcomeVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 官方欢迎房间-国家映射接口
 */
@RestController
@RequestMapping(value = "/officialWelcome", produces = MediaType.APPLICATION_JSON_VALUE)
public class OfficialWelcomeController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(OfficialWelcomeController.class);

    @Resource
    private OfficialWelcomeService officialWelcomeService;

    /**
     * 映射关系列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<OfficialWelcomeVO>> list(@RequestBody OfficialWelcomeCondition condition) {
        logger.info("查询官方欢迎映射列表 condition={}", JSONObject.toJSONString(condition));
        return HttpResult.getOk(officialWelcomeService.list(condition));
    }

    /**
     * 设置房间对应的国家（主要操作）
     */
    @RequireRole
    @RequestMapping("/setRoomCountries")
    public HttpResult<?> setRoomCountries(@RequestBody OfficialWelcomeDTO dto) {
        logger.info("设置房间国家映射 dto={}", JSONObject.toJSONString(dto));
        officialWelcomeService.setRoomCountries(dto);
        return HttpResult.getOk();
    }


}
