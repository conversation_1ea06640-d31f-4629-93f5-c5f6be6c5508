package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.result.UpdateResult;
import com.quhong.core.config.ServerConfig;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.enums.RocketRewardTypeEnum;
import com.quhong.operation.share.dto.RocketRewardConfigDTO;
import com.quhong.operation.share.vo.RocketRewardConfigVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@RestController
@RequestMapping("/rocketConfig")
public class RocketConfigController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(RocketConfigController.class);

    @Resource
    private MonitorSender monitorSender;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private RocketRewardConfigDao rocketRewardConfigDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private MicFrameSourceDao micFrameSourceDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private BuddleSourceDao buddleSourceDao;
    @Resource
    private FloatScreenSourceDao floatScreenSourceDao;

    @RequireRole(3)
    @RequestMapping("save")
    public HttpResult saveRocketConfig(@RequestBody RocketRewardConfigDTO dto) {
        logger.info("save rocket config. dto={}", JSONObject.toJSONString(dto));
        HttpResult result = new HttpResult();
        String errorMsg = checkParam(dto);
        if (!StringUtils.isEmpty(errorMsg)) {
            return result.error(errorMsg);
        }
        try {
            RocketRewardConfigData data = rocketRewardConfigDao.findData(dto.getRocketLevel());
            if (data == null) {
                data = new RocketRewardConfigData();
            }
            data.setRocketLevel(dto.getRocketLevel());
            data.setRocketLaunchLimit(dto.getRocketLaunchLimit());
            data.setDetailList(dto.getDetailList());
            if (!CollectionUtils.isEmpty(data.getDetailList())) {
                data.getDetailList().sort(Comparator.comparing(RocketRewardConfigData.RewardConfigDetail::getLowerLimit).reversed());
                data.getDetailList().forEach(k -> {
                    RocketRewardTypeEnum typeEnum = RocketRewardTypeEnum.getByCode(k.getRewardType());
                    k.setRewardNameEn(typeEnum.getNameEn());
                    k.setRewardNameAr(typeEnum.getNameAr());
                    if ("gift".equals(k.getRewardType())) {

                        GiftData extraGift = giftDao.getGiftFromDb(k.getSourceId());
                        if(extraGift == null){
                            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "礼物不存在");
                        }
                        if (extraGift.getBagGift() <= 0) {
                            extraGift.setBagGift(1);
                            giftDao.updateOne(extraGift);
                        }
                    }
                });
            }
            rocketRewardConfigDao.save(data);
        } catch (Exception e) {
            logger.error("save rocket config error. {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok();
    }

    private boolean setRewardIcon(RocketRewardConfigData.RewardConfigDetail detail) {
        RocketRewardTypeEnum rewardTypeEnum = RocketRewardTypeEnum.getByCode(detail.getRewardType());
        if (rewardTypeEnum == RocketRewardTypeEnum.BAG_GIFT) {
            GiftData giftData = giftDao.getGiftFromCache(detail.getSourceId());
            if (giftData != null) {
                detail.setRewardIcon(giftData.getGicon());
                return true;
            }
        } else if (rewardTypeEnum == RocketRewardTypeEnum.MIC) {
            MicFrameSourceData micFrameSourceData = micFrameSourceDao.findData(detail.getSourceId());
            if (micFrameSourceData != null) {
                detail.setRewardIcon(micFrameSourceData.getMic_icon());
                return true;
            }
        } else if (rewardTypeEnum == RocketRewardTypeEnum.RIDE) {
            JoinSourceData joinSourceData = joinSourceDao.findSourceData(detail.getSourceId());
            if (joinSourceData != null) {
                detail.setRewardIcon(joinSourceData.getJoin_icon());
                return true;
            }
        } else if (rewardTypeEnum == RocketRewardTypeEnum.BUDDLE) {
            BuddleSourceData buddleSourceData = buddleSourceDao.findData(detail.getSourceId());
            if (buddleSourceData != null) {
                detail.setRewardIcon(buddleSourceData.getBuddle_icon());
                return true;
            }
        } else if (rewardTypeEnum == RocketRewardTypeEnum.FLOAT_SCREEN) {
            FloatScreenSourceData screenSourceData = floatScreenSourceDao.findData(detail.getSourceId());
            if (screenSourceData != null) {
                detail.setRewardIcon(screenSourceData.getScreen_icon());
                return true;
            }
        }
        return false;
    }

    private String checkParam(RocketRewardConfigDTO dto) {
        if (dto.getRocketLevel() == null) {
            return "火箭等级不能为空";
        }
        List<Integer> levels = Arrays.asList(1, 2, 3, 4, 5, 6);
        if (!levels.contains(dto.getRocketLevel())) {
            return "火箭等级只能是123456";
        }
        if (dto.getRocketLaunchLimit() == null || dto.getRocketLaunchLimit() <= 0) {
            return "火箭发射的能量不能设置为空,或者0和负数";
        }
        if (CollectionUtils.isEmpty(dto.getDetailList())) {
            return "奖励配置不能为空";
        }
        int count = 0;
        for (RocketRewardConfigData.RewardConfigDetail detail : dto.getDetailList()) {
            if (detail.getRate() == 0) {
                return "领取概率不能为空或0";
            }
            if (StringUtils.isEmpty(detail.getRewardType())) {
                return "奖励类型不能为空";
            }
            if ("diamond".equals(detail.getRewardType()) || "coin".equals(detail.getRewardType()) ) {
                if (detail.getRewardNum() == null || detail.getRewardNum() == 0) {
                    return "钻石金币奖励数量不能为空或者0";
                }
            } else if ("gift".equals(detail.getRewardType())) {
                if (detail.getSourceId() == null) {
                    return "非钻石金币奖励，资源id不能为空";
                }
                if (detail.getRewardNum() == null || detail.getRewardNum() == 0) {
                    return "礼物奖励数量不能为空或者0";
                }
            } else {
                if (detail.getSourceId() == null) {
                    return "非钻石金币奖励，资源id不能为空";
                }
                if (detail.getRewardTime() == null) {
                    return "非钻石金币礼物奖励，奖励有效时间不能为空";
                }
            }
            if (count != 0 && detail.getUpperLimit() == null) {
                return "领取条件上限不能为空";
            }
            if (detail.getLowerLimit() == null) {
                return "领取条件下限不能为空";
            }
            if (count != 0 && detail.getLowerLimit() > detail.getUpperLimit()) {
                return "领取条件下限不能大于领取条件上限";
            }
            count ++;
            if (StringUtils.isEmpty(detail.getRewardIcon())) {
                if(!setRewardIcon(detail)) {
                    return String.format("资源%d的图标不能为空", detail.getSourceId());
                }
            }
        }
        return null;
    }

    @RequireRole(3)
    @RequestMapping("find")
    public HttpResult<RocketRewardConfigVO> findRocketConfig(@RequestBody RocketRewardConfigDTO dto) {
        logger.info("find rocket config data. rocketLevel={}", dto.getRocketLevel());
        HttpResult<RocketRewardConfigVO> result = new HttpResult<>();
        RocketRewardConfigVO vo = new RocketRewardConfigVO();
        try {
            RocketRewardConfigData data = rocketRewardConfigDao.findData(dto.getRocketLevel());
            if (data != null) {
                vo.setStatus(sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_KEY, false));
                vo.setRocketLevel(data.getRocketLevel());
                vo.setRocketLaunchLimit(data.getRocketLaunchLimit());
                vo.setDetailList(data.getDetailList());
            }
        } catch (Exception e) {
            logger.error("find rocket config error. {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok(vo);
    }

    @RequireRole(3)
    @RequestMapping("updateStatus")
    public HttpResult updateStatus(@RequestBody RocketRewardConfigDTO dto) {
        logger.info("updateStatus. status={}", dto.getStatus());
        HttpResult result = new HttpResult();
        String errorMsg = checkRocketConfig();
        if (!StringUtils.isEmpty(errorMsg)) {
            return result.error(errorMsg);
        }
        try {
            UpdateResult updateResult = sysConfigDao.updateMapValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_KEY, dto.getStatus());
            if (null == updateResult || updateResult.getModifiedCount() == 0) {
                if (ServerConfig.isProduct()) {
                    monitorSender.info("activity", "操作房间火箭开关异常", dto.getStatus() == 1 ? "房间火箭活动开启失败" : "房间火箭活动关闭失败");
                    return result.error(HttpCode.SERVER_ERROR.getMsg());
                }
            }
        } catch (Exception e) {
            logger.error("updateStatus error. {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok();
    }




    private String checkRocketConfig() {
        for (int i = 1; i <= 6; i++) {
            String errorMsg = String.format("Lv%s火箭奖励设置", i);
            RocketRewardConfigData data = rocketRewardConfigDao.findData(i);
            if (data == null) {
                return errorMsg + "为空";
            }
            errorMsg = errorMsg + "中,";
            if (data.getRocketLaunchLimit() <= 0) {
                return errorMsg + "火箭发射所需的能量为0或者负数";
            }
            if (CollectionUtils.isEmpty(data.getDetailList())) {
                return errorMsg + "奖励配置为空";
            }
            int count = 0;
            for (RocketRewardConfigData.RewardConfigDetail detail : data.getDetailList()) {
                if (StringUtils.isEmpty(detail.getRewardIcon())) {
                    return errorMsg + "奖励图标为空";
                }
                if (detail.getRate() == 0) {
                    return errorMsg + "领取概率为0";
                }
                if (StringUtils.isEmpty(detail.getRewardType())) {
                    return errorMsg + "奖励类型为空";
                }
                if ("diamond".equals(detail.getRewardType()) || "coin".equals(detail.getRewardType())) {
                    if (detail.getRewardNum() == null || detail.getRewardNum() == 0) {
                        return errorMsg + "钻石金币奖励数量为空或者0";
                    }
                } else if ("gift".equals(detail.getRewardType())) {
                    if (detail.getSourceId() == null) {
                        return errorMsg + "非钻石金币奖励，资源id为空";
                    }
                    if (detail.getRewardNum() == null || detail.getRewardNum() == 0) {
                        return errorMsg + "礼物奖励数量为空或者0";
                    }
                } else {
                    if (detail.getSourceId() == null) {
                        return errorMsg + "非钻石金币奖励，资源id为空";
                    }
                    if (detail.getRewardTime() == null) {
                        return errorMsg + "非钻石金币礼物奖励，奖励有效时间为空";
                    }
                }
                if (count != 0 && detail.getUpperLimit() == null) {
                    return errorMsg + "领取条件上限为空";
                }
                count++;
                if (detail.getLowerLimit() == null) {
                    return errorMsg + "领取条件下限为空";
                }
            }
        }
        return null;
    }
}
