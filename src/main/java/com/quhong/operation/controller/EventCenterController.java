package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.BubbleSourceService;
import com.quhong.operation.server.EventCenterService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.dto.BubbleSourceDTO;
import com.quhong.operation.share.vo.EventCenterVO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 活动中心配置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/eventCenter", produces = MediaType.APPLICATION_JSON_VALUE)
public class EventCenterController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(EventCenterController.class);

    @Resource
    private EventCenterService eventCenterService;


    @RequireRole
    @RequestMapping("/list")
    public String eventCenterList(@RequestBody BaseCondition condition) {
        logger.info("get eventCenterList {}", condition);
        return createResult(HttpCode.SUCCESS, eventCenterService.eventCenterList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public String addEventCenterData(@RequestBody EventCenterVO dto) {

        logger.info("addEventCenterData {}", JSON.toJSONString(dto));
        eventCenterService.addEventCenterData(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateEventCenterData(@RequestBody EventCenterVO dto) {
        logger.info("updateEventCenterData {}", dto);
        if (StringUtils.isEmpty(dto.getDocId())) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }
        eventCenterService.updateEventCenterData(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

}
