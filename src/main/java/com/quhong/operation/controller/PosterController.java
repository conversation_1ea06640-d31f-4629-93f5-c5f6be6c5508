package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.OfficialWelcomeService;
import com.quhong.operation.server.PosterService;
import com.quhong.operation.share.condition.OfficialWelcomeCondition;
import com.quhong.operation.share.condition.PageCondition;
import com.quhong.operation.share.dto.NationalDayV3DTO;
import com.quhong.operation.share.dto.OfficialWelcomeDTO;
import com.quhong.operation.share.dto.PosterDTO;
import com.quhong.operation.share.vo.OfficialWelcomeVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 海报管理
 */
@RestController
@RequestMapping(value = "/poster", produces = MediaType.APPLICATION_JSON_VALUE)
public class PosterController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(PosterController.class);

    @Resource
    private PosterService posterService;

    /**
     * 海报列表
     * @param page
     * @param pageSize
     * @param status -1 全部 0 无效 1 有效
     * @return
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<PosterDTO>> list(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                                    @RequestParam(value = "page_size", defaultValue = "12") Integer pageSize,
                                                    @RequestParam(value = "status", defaultValue = "") Integer status) {
        return HttpResult.getOk(posterService.list(status, page, pageSize));
    }

    /**
     * 增加海报
     */
    @RequireRole
    @RequestMapping("/add")
    public HttpResult<?> add(@RequestBody PosterDTO dto) {
        posterService.add(dto);
        return HttpResult.getOk();
    }

    /**
     * 更新海报
     */
    @RequireRole
    @RequestMapping("/update")
    public HttpResult<?> update(@RequestBody PosterDTO dto) {
        posterService.update(dto);
        return HttpResult.getOk();
    }

}
