package com.quhong.operation.controller;

import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mysql.dao.BadWordDao;
import com.quhong.mysql.data.BadWordData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.BadWordCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 脏词词库
 *
 * <AUTHOR>
 * @date 2022/05/16
 */

@RestController
@RequestMapping("/badWord")
public class BadWordController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(BadWordController.class);

    @Autowired
    private BadWordDao badWordDao;

    /**
     * 获取脏词词库列表
     *
     * @param condition
     * @return
     */
    @RequireRole(3)
    @RequestMapping("get/badWordList")
    public HttpResult<PageResultVO<BadWordData>> getBadWordList(@RequestBody BadWordCondition condition) {
        logger.info("get bad word list.word={}, page={}, pageSize={}", condition.getWord(), condition.getPage(), condition.getPageSize());
        HttpResult<PageResultVO<BadWordData>> result = new HttpResult<>();
        PageResultVO<BadWordData> pageVO = new PageResultVO<>();
        List<BadWordData> dataList = badWordDao.selectList(condition.getWord(), condition.getPage(), condition.getPageSize());
        pageVO.setList(dataList);
        pageVO.setTotal(badWordDao.selectCount(condition.getWord()));
        return result.ok(pageVO);
    }

    /**
     * 新增脏词
     *
     * @param condition
     * @return
     */
    @RequireRole(3)
    @RequestMapping("set/badWord")
    public HttpResult insertBadWord(@RequestBody BadWordCondition condition) {
        logger.info("batch insert bad word.word={}", condition.getWord());
        List<String> wordList = Arrays.asList(condition.getWord().split("\\r?\\n"));
        HttpResult result = new HttpResult();
        List<BadWordData> dataList = new ArrayList<>();
        for (String word : wordList) {
            if(StringUtils.isEmpty(word)) {
                continue;
            }
            BadWordData data = new BadWordData();
            data.setWord(word);
            data.setCtime(DateHelper.getNowSeconds());
            dataList.add(data);
        }
        try {
            badWordDao.batchInsert(dataList);
        } catch (Exception e) {
            logger.error("save bad word error. {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok();
    }

    /**
     * 修改脏词
     *
     * @param condition
     * @return
     */
    @RequireRole(3)
    @RequestMapping("update/badWord")
    public HttpResult updateBadWord(@RequestBody BadWordCondition condition) {
        logger.info("update bad word. id={} word={}", condition.getId(), condition.getWord());
        HttpResult result = new HttpResult();
        try {
            BadWordData data = new BadWordData();
            data.setId(condition.getId());
            data.setWord(condition.getWord());
            badWordDao.update(data);
        } catch (Exception e) {
            logger.error("update bad word error. {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok();
    }

    /**
     * 删除脏词
     *
     * @param condition
     * @return
     */
    @RequireRole(3)
    @RequestMapping("delete/badWord")
    public HttpResult deleteBadWord(@RequestBody BadWordCondition condition) {
        logger.info("delete bad word. id={}", condition.getId());
        HttpResult result = new HttpResult();
        try {
            badWordDao.delete(condition.getId());
        } catch (Exception e) {
            logger.error("delete bad word error. {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok();
    }
}
