package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.AdCampaignGameData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.AdCampaignGameService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.RoomTypeGameListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 广告系列游戏映射配置接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-30
 */
@RestController
@RequestMapping(value = "/adCampaignGame", produces = MediaType.APPLICATION_JSON_VALUE)
public class AdCampaignGameController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(AdCampaignGameController.class);

    @Resource
    private AdCampaignGameService adCampaignGameService;

    /**
     * 房间TO游戏映射列表
     */
    @RequireRole
    @RequestMapping("/roomTypeGameList")
    public HttpResult<RoomTypeGameListVO> roomTypeGameList() {
        return HttpResult.getOk(adCampaignGameService.roomTypeGameList());
    }

    /**
     * 广告系列游戏映射列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<AdCampaignGameData>> list(@RequestBody BaseCondition condition) {
        logger.info("get adCampaignGame list={}", JSONObject.toJSONString(condition));
        return HttpResult.getOk(adCampaignGameService.list(condition));
    }

    /**
     * 新增广告系列游戏映射
     */
    @RequireRole
    @RequestMapping("/add")
    public HttpResult<?> addData(@RequestBody AdCampaignGameData dto) {
        logger.info("addData adCampaignGame {}", JSONObject.toJSONString(dto));
        adCampaignGameService.addData(dto);
        return HttpResult.getOk();
    }

    /**
     * 更新广告系列游戏映射
     */
    @RequireRole
    @RequestMapping("/update")
    public HttpResult<?> updateData(@RequestBody AdCampaignGameData dto) {
        logger.info("updateData adCampaignGame {}", JSONObject.toJSONString(dto));
        adCampaignGameService.updateData(dto);
        return HttpResult.getOk();
    }
}
