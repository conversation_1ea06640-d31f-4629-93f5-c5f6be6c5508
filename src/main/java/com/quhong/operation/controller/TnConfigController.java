package com.quhong.operation.controller;

import com.quhong.data.dto.RegisterPhoneAccountDTO;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.TnConfigServer;
import com.quhong.operation.share.dto.user.TnConfigDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Set;

@RestController
@RequestMapping("/operation")
public class TnConfigController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(TnConfigController.class);

    @Resource
    private TnConfigServer tnConfigServer;


    @RequireRole
    @RequestMapping("/addTnWhite")
    public HttpResult<?> addTnWhite(@RequestBody TnConfigDTO tnConfigDTO) {
        return tnConfigServer.addList(tnConfigDTO.getSourceList(), tnConfigDTO.getTnId());
    }

    @RequireRole
    @RequestMapping("/delTnWhite")
    public HttpResult<?> delTnWhite(@RequestBody TnConfigDTO tnConfigDTO) {
        return tnConfigServer.deleteItem(tnConfigDTO.getRid(), tnConfigDTO.getTnId());
    }

    @RequireRole
    @RequestMapping("/listTnWhite")
    public HttpResult<?> listTnWhite(@RequestBody TnConfigDTO tnConfigDTO) {
        return tnConfigServer.listTnWhite(tnConfigDTO.getRid(), tnConfigDTO.getTnId(), tnConfigDTO.getPage());
    }

    @RequireRole
    @RequestMapping("/registerHumanAccount")
    public HttpResult<?> registerHumanAccount(HttpServletRequest request,@RequestBody RegisterPhoneAccountDTO dto) {
        String optUid = request.getParameter("uid");
        dto.setOptUid(optUid);
        return tnConfigServer.registerHumanAccount(dto);
    }

    @RequireRole
    @RequestMapping("/getHumanAccountList")
    public HttpResult<?> getHumanAccountList(HttpServletRequest request,@RequestBody RegisterPhoneAccountDTO dto) {
//        String optUid = request.getParameter("uid");
//        dto.setOptUid(optUid);
        return tnConfigServer.getHumanAccountList(dto);
    }

    @RequireRole
    @RequestMapping("/updateHumanAccount")
    public HttpResult<?> updateHumanAccount(HttpServletRequest request,@RequestBody RegisterPhoneAccountDTO dto) {
        String optUid = request.getParameter("uid");
        dto.setOptUid(optUid);
        return tnConfigServer.updateHumanAccount(dto);
    }

    @GetMapping("/wahoQuery")
    public HttpResult<?> wahoQuery(HttpServletRequest request) {
        String wahoTnId = request.getParameter("wahoTnId");
        return tnConfigServer.wahoQuery(wahoTnId);
    }

    @RequireRole
    @RequestMapping("/updateRoomScore")
    public HttpResult<?> updateRoomScore(HttpServletRequest request,@RequestBody RegisterPhoneAccountDTO dto) {
        String optUid = request.getParameter("uid");
        dto.setOptUid(optUid);
        return tnConfigServer.updateRoomScore(dto);
    }

    @RequireRole
    @RequestMapping("/getAllMangerList")
    public HttpResult<?> getAllMangerList(HttpServletRequest request,@RequestBody RegisterPhoneAccountDTO dto) {
        String optUid = request.getParameter("uid");
        dto.setOptUid(optUid);
        return tnConfigServer.getMangerList(dto);
    }

    @RequireRole
    @RequestMapping("/testPhoneWarning")
    public HttpResult<?> testPhoneWarning(HttpServletRequest request,@RequestBody RegisterPhoneAccountDTO dto) {
        String optUid = request.getParameter("uid");
        dto.setOptUid(optUid);
        return tnConfigServer.testPhoneWarning(dto);
    }
}
