package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.MicFrameService;
import com.quhong.operation.server.RoomBannerService;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.dto.MicFrameDTO;
import com.quhong.operation.share.dto.RoomBannerDTO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 房间背景图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/micFrame", produces = MediaType.APPLICATION_JSON_VALUE)
public class MicFrameController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(MicFrameController.class);
    private final static String filePath = "mic/";

    @Resource
    private MicFrameService micFrameService;

    @RequestMapping("/notLoginList")
    public String micNotLoginList(@RequestBody ItemCondition condition) {
        logger.info("get micNotLoginList {}", condition);
        return createResult(HttpCode.SUCCESS, micFrameService.micFrameList(condition));
    }

    @RequireRole
    @RequestMapping("/list")
    public String micFrameList(@RequestBody ItemCondition condition) {

        logger.info("get micFrameList {}", condition);
        return createResult(HttpCode.SUCCESS, micFrameService.micFrameList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public String addMicFrameData(@RequestBody MicFrameDTO dto) {
        try {
            logger.info("addMicFrameData {}", dto);
            micFrameService.addMicFrameData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }




    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateMicFrameData(@RequestBody MicFrameDTO dto) {
        try {
            logger.info("updateMicFrameData {}", dto);
            if (StringUtils.isEmpty(dto.getDocId())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }

            micFrameService.updateMicFrameData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }



    }



    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return AWSUploadUtils.upload(file, filePath);
    }

}
