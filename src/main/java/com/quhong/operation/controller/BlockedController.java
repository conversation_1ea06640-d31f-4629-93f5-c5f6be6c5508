package com.quhong.operation.controller;

import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.BlockedServer;
import com.quhong.operation.share.mongobean.UserMonitorLog;
import com.quhong.operation.share.vo.BlockUserVO;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@RestController
@RequestMapping("/blocked")
public class BlockedController {

    private static final Logger logger = LoggerFactory.getLogger(BlockedController.class);
    @Autowired
    private BlockedServer blockedServer;

    /**
     * 获取封禁列表
     * @param startDate 开始时间
     * @param endDate 结尾时间
     * @param rid 用户rid 可以为空
     * @return 列表
     */
    @RequestMapping("/getBlockedList")
    public HttpResult<List<BlockUserVO>> getBlockedList (String startDate, String endDate, Integer rid) {
        HttpResult<List<BlockUserVO>> result = new HttpResult<>();
        logger.info("getBlockedList param startDate={} endDate={} rid={}", startDate, endDate, rid);

        Integer endTime = DateHelper.ARABIAN.getEndSeconds(endDate);
        Integer startTime = DateHelper.ARABIAN.getStartSeconds(startDate, endTime);

        ApiResult<List<BlockUserVO>> apiResult = blockedServer.blockedList(startTime, endTime, rid);
        if (!apiResult.isOK()) {
            logger.error("get block account fail : {}", apiResult.getMsg());
            return result.error(apiResult.getMsg());
        }

        return result.ok(apiResult.getData());
    }

    @RequestMapping("/getUserMonitorLog")
    public HttpResult<List<UserMonitorLog>> getUserMonitorLog (String aid) {
        HttpResult<List<UserMonitorLog>> result = new HttpResult<>();
        logger.info("getUserMonitorLog param aid={}", aid);

        ApiResult<List<UserMonitorLog>> apiResult = blockedServer.getUserMonitorLog(aid);
        if (!apiResult.isOK()) {
            logger.error("get user monitor log fail, cause of failure: {}", apiResult.getMsg());
            return result.error(apiResult.getMsg());
        }
        logger.info("getUserMonitorLog param aid={} result {}", aid, apiResult.getData());
        return result.ok(apiResult.getData());
    }

}
