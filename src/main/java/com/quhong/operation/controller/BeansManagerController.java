package com.quhong.operation.controller;

import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.BeansManagerServer;
import com.quhong.operation.share.vo.ChargeTotalVOs;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/7/7
 */

@RestController
@RequestMapping("/beansManager")
public class BeansManagerController {

    private final static Logger logger = LoggerFactory.getLogger(BeansManagerController.class);

    @Autowired
    private BeansManagerServer beansManagerServer;

    @RequireRole(3)
    @RequestMapping("/getChargeTotal")
    public HttpResult<ChargeTotalVOs> getChargeTotalList (String startDate, String endDate) {
        logger.info("method getChargeTotalList param startDate={}, endDate={}", startDate, endDate);
        HttpResult<ChargeTotalVOs> result = new HttpResult<>();
        Integer endSeconds = DateHelper.ARABIAN.getEndSeconds(endDate);
        Integer startSeconds = DateHelper.ARABIAN.getStartSeconds(startDate, endSeconds);
        ApiResult<ChargeTotalVOs> apiResult = beansManagerServer.getChargeTotal(startSeconds, endSeconds);
        logger.info("method getChargeTotal result {}", apiResult);
        return result.ok(apiResult.getData());
    }

}
