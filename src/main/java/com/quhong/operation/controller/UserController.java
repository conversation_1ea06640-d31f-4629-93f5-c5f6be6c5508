package com.quhong.operation.controller;

import com.alibaba.druid.sql.visitor.functions.If;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.concurrency.tree.TreeTask;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.dao.UserOnlineDao;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.common.OsConstant;
import com.quhong.operation.server.UserServer;
import com.quhong.operation.share.dto.user.NfRetainedInfoDTO;
import com.quhong.operation.share.mysql.UserOnlineData;
import com.quhong.operation.share.vo.UserOnlineVo;
import com.quhong.operation.share.vo.reports.NoviceReportVO;
import com.quhong.operation.share.vo.user.NfRetainedInfoVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@RestController
@RequestMapping("/user")
public class UserController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserServer userServer;

    /**
     * 用户在线数据查看
     *
     * @param request
     * @return
     */
    @RequireRole
    @RequestMapping("/online")
    public HttpResult<List<UserOnlineVo>> online(HttpServletRequest request) {
        HttpResult<List<UserOnlineVo>> result = new HttpResult();
        return result.ok(userServer.getOnlineData(request.getParameter("date")));
    }

    /**
     * 新用户，女用户留存报表展示
     *
     * @param cType
     * @param start
     * @param end
     * @param qType
     * @param platform
     * @param pkgType
     * @return
     */
    @RequestMapping("/nf_retained_info")
    @RequireRole(3)
    public String nfRetainedInfo(@RequestParam(value = "c_type", defaultValue = "total") String cType,
                                 String start, String end,
                                 @RequestParam(value = "q_type", defaultValue = "0") Integer qType,
                                 @RequestParam(value = "platform", defaultValue = "2") Integer platform,
                                 @RequestParam(value = "pkg_type", defaultValue = "2") Integer pkgType) {
        try {
            logger.info("begin get user nf_retained_info. c_type={} start={} end={} q_type={} platform={} pkg_type={}", cType, start, end, qType, platform, pkgType);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.info("get user nf_retained_info param error. c_type={} start={} end={} q_type={} platform={} pkg_type={}", cType, start, end, qType, platform, pkgType);
                return createResult(HttpCode.PARAM_ERROR, null);
            }
            //2、业务调用
            List<DayTimeData> dayTimeList = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(start, end);
            List<NfRetainedInfoVO> retList = new ArrayList<>();
            for (DayTimeData dayTimeData : dayTimeList) {
                NfRetainedInfoDTO nfRetainedInfoDTO = new NfRetainedInfoDTO();
                nfRetainedInfoDTO.setcType(cType);
                nfRetainedInfoDTO.setqType(qType);
                nfRetainedInfoDTO.setDate(dayTimeData);
                nfRetainedInfoDTO.setPlatform(platform);
                nfRetainedInfoDTO.setPkgType(pkgType);
                NfRetainedInfoVO nfRetainedInfo = userServer.getNfRetainedInfo(nfRetainedInfoDTO);
                retList.add(nfRetainedInfo);
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("total_revenue", retList);
            return createResult(HttpCode.SUCCESS, jsonObject);
        } catch (Exception e) {
            logger.error("get user nf_retained_info error. {}", e.getMessage(), e);
        }
        return createResult(HttpCode.SERVER_ERROR, null);
    }

    @RequestMapping("/nf_retained_info/download")
    @RequireRole(3)
    public void downloadNfRetainedInfo(HttpServletResponse response, @RequestParam(value = "c_type", defaultValue = "total") String cType,
                                       String start, String end,
                                       @RequestParam(value = "q_type", defaultValue = "0") Integer qType,
                                       @RequestParam(value = "platform", defaultValue = "2") Integer platform,
                                       @RequestParam(value = "pkg_type", defaultValue = "2") Integer pkgType) {
        try {
            logger.info("begin download user nf_retained_info. c_type={} start={} end={} q_type={} platform={} pkg_type={}", cType, start, end, qType, platform, pkgType);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.info("download user nf_retained_info param error. c_type={} start={} end={} q_type={} platform={} pkg_type={}", cType, start, end, qType, platform, pkgType);
                response.getWriter().write(createResult(HttpCode.PARAM_ERROR, null));
                return;
            }
            //2、业务调用
            List<DayTimeData> dayTimeList = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(start, end);
            List<NfRetainedInfoVO> retList = new ArrayList<>();
            for (DayTimeData dayTimeData : dayTimeList) {
                NfRetainedInfoDTO nfRetainedInfoDTO = new NfRetainedInfoDTO();
                nfRetainedInfoDTO.setcType(cType);
                nfRetainedInfoDTO.setqType(qType);
                nfRetainedInfoDTO.setDate(dayTimeData);
                nfRetainedInfoDTO.setPlatform(platform);
                nfRetainedInfoDTO.setPkgType(pkgType);
                NfRetainedInfoVO nfRetainedInfo = userServer.getNfRetainedInfo(nfRetainedInfoDTO);
                retList.add(nfRetainedInfo);
            }
            //3、下载报表
            ExcelUtils.exportExcel(response, retList, NfRetainedInfoVO.class, start + "_" + end + "-nf_retained_info", "Sheet");
        } catch (IOException e) {
            logger.error("download user nf_retained_info error. {}", e.getMessage(), e);
        }
    }

}
