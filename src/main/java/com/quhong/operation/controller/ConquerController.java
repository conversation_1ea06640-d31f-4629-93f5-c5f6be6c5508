package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.ConquerActivityDao;
import com.quhong.mongo.dao.LuckyLotteryActivityDao;
import com.quhong.mongo.data.ConquerActivity;
import com.quhong.mongo.data.LuckyLotteryActivity;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.ActivityTemplateCondition;
import com.quhong.operation.share.dto.ConquerActivityDTO;
import com.quhong.operation.share.dto.LotteryTemplateDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.ConquerRedis;
import com.quhong.redis.LuckyLotteryRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/conquer")
public class ConquerController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ConquerController.class);
    private static final Set<String> SUPPORT_SET = new HashSet<>(Arrays.asList("gift", "mic", "buddle", "ride",
            "ripple", "diamond", "badge", "float_screen", "heart", "once_again", "thanks"));
    // wenmiaofang、yangml、yufengxia
    private static final Set<String> ADMIN_SET = new HashSet<>(Arrays.asList("5dba92651e23cd00c80b32bc", "61b1fa404cf5f82dff19a63e", "62b9780a1fb34e1c3520cb90"));

    // @Value("${online:true}")
    // private boolean online;


    @Resource
    private ConquerActivityDao conquerActivityDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;

    @Resource
    private ConquerRedis conquerRedis;

    /**
     * 保存活动模板
     */
    @RequireRole(2)
    @RequestMapping("/save_template")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody ConquerActivity template) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(template));
            // String uid = request.getParameter("uid");
            // if (online && (!ADMIN_SET.contains(uid))) {
            //     logger.error("save_template no right to operate uid={}", uid);
            //     return result.error("您无操作权限，如有需求请联系技术人员!");
            // }
            if(template.getStatus() == 1 && conquerActivityDao.findOne() != null){
                return result.error("存在一个有效的征服活动");
            }

            List<Integer> conquerNumList = new ArrayList<>();
            List<Integer> conquerLevelNumsList = new ArrayList<>();


            if (null == template.getConfigList()
                    || null == template.getStartTime()
                    || null == template.getEndTime()
                    || null == template.getAcNameAr()
                    || null == template.getAcNameEn()){
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            int defaultNum = 0;
            conquerLevelNumsList.add(defaultNum);

            for (ConquerActivity.ConfigDetail config : template.getConfigList()) {
                conquerNumList.add(config.getConquerNum());

                defaultNum += config.getConquerNum();
                conquerLevelNumsList.add(defaultNum);
            }


            if (template.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }
            template.setJoinUrl(template.getJoinUrl());
            template.setConquerNumList(conquerNumList);
            template.setConquerLevelNumsList(conquerLevelNumsList);

            conquerActivityDao.save(template);
        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

   /**
    * 更新活动模板
    */
   @RequireRole(2)
   @RequestMapping("/update_template")
   public HttpResult updateTemplate(HttpServletRequest request, @RequestBody ConquerActivityDTO dto) {
       HttpResult result = new HttpResult();
       try {
           // String uid = request.getParameter("uid");
           // if (online && (!ADMIN_SET.contains(uid))) {
           //     logger.error("save_template no right to operate uid={}", uid);
           //     return result.error("您无操作权限，如有需求请联系技术人员!");
           // }
           if (Objects.isNull(dto.getActivityId())) {
               logger.error("The activityId cannot be empty.");
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }

           if (null == dto.getConfigList() || null == dto.getStartTime()
                   || null == dto.getEndTime() || null == dto.getAcNameAr()
                   || null == dto.getAcNameEn()) {
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }

           List<Integer> conquerNumList = new ArrayList<>();
           List<Integer> conquerLevelNumsList = new ArrayList<>();


           int defaultNum = 0;
           conquerLevelNumsList.add(defaultNum);

           for (ConquerActivity.ConfigDetail config : dto.getConfigList()) {

               conquerNumList.add(config.getConquerNum());

               defaultNum += config.getConquerNum();
               conquerLevelNumsList.add(defaultNum);

           }


           logger.info("update template activityId={} data={}", dto.getActivityId(), JSON.toJSONString(dto));

           ConquerActivity templateToUpdate = conquerActivityDao.findData(dto.getActivityId());
           if (Objects.isNull(templateToUpdate)) {
               logger.error("templateToUpdate is empty. id={}", dto.getActivityId());
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }


           ConquerActivity conquerActivity = conquerActivityDao.findOne();
           if(dto.getStatus() == 1 && conquerActivity != null && !dto.getActivityId().equals(conquerActivity.get_id().toString())){
               return result.error("存在一个有效的征服活动");
           }

           // if (templateToUpdate.getStatus() == 1 && ServerConfig.isProduct()) {
           //     return result.error("活动已结束，无法更新");
           // }


           // if (dto.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
           //     return result.error("活动结束时间距离现在太近，请检查");
           // }

           Update update = new Update();
           update.set("acNameEn", dto.getAcNameEn());
           update.set("acNameAr", dto.getAcNameAr());
           update.set("startTime", dto.getStartTime());
           update.set("endTime", dto.getEndTime());
           update.set("status", dto.getStatus());
           update.set("joinUrl", dto.getJoinUrl());

           update.set("configList", dto.getConfigList());
           update.set("conquerLevelNumsList", conquerLevelNumsList);
           update.set("conquerNumList", conquerNumList);

           update.set("mtime", DateHelper.getNowSeconds());
           conquerActivityDao.updateData(templateToUpdate, update);
       } catch (Exception e) {
           logger.error("update template error. {}", e.getMessage(), e);
           return result.error();
       }
       return result.ok();
   }

    /**
     * 活动模板分页查询
     */
    @RequireRole(2)
    @RequestMapping("/page")
    public String selectPage(@RequestBody ActivityTemplateCondition condition) {


        logger.info("luckyLottery selectPage condition={}", JSON.toJSONString(condition));
        HttpResult<PageResultVO<ConquerActivityDTO>> result = new HttpResult<>();
        PageResultVO<ConquerActivityDTO> pageVO = new PageResultVO<>();
        try {
            List<ConquerActivity> rankingActivities = conquerActivityDao
                    .selectPage(condition.getAcNameEn(), (condition.getPage() - 1) * 10, 10);
            List<ConquerActivityDTO> dtoList = new ArrayList<>();
            String token = basePlayerRedis.getToken(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");

            rankingActivities.forEach(a -> {
                ConquerActivityDTO dto = new ConquerActivityDTO();
                dto.setActivityId(a.get_id().toString());
                BeanUtils.copyProperties(a, dto);
                dto.setUid(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
                dto.setToken(token);
                dtoList.add(dto);
            });
            pageVO.setList(dtoList);
            pageVO.setTotal(conquerActivityDao.selectCount());
        } catch (Exception e) {
            logger.info("conquerActivity selectPage error", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }


}
