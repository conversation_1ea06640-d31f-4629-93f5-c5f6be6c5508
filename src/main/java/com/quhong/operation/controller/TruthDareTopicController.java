package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.TruthDareTopicData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.TruthDareTopicService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.TruthDareCondition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 运营平台真心话大冒险话题
 */
@RestController
@RequestMapping(value = "/truthDareTopic", produces = MediaType.APPLICATION_JSON_VALUE)
public class TruthDareTopicController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(TruthDareTopicController.class);

    @Resource
    private TruthDareTopicService truthDareTopicService;

    @RequireRole
    @RequestMapping("/list")
    public String truthDareTopicList(@RequestBody TruthDareCondition condition) {
        logger.info("get truthDareTopicList {}", condition);
        return createResult(HttpCode.SUCCESS, truthDareTopicService.truthDareTopicList(condition));
    }

    @RequireRole
    @RequestMapping("/add")
    public String addTruthDareTopic(@RequestBody TruthDareTopicData dto) {
        logger.info("addTruthDareTopic {}", JSONObject.toJSONString(dto));
        truthDareTopicService.addTruthDareTopic(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @RequestMapping("/update")
    public String updateTruthDareTopic(@RequestBody TruthDareTopicData dto) {
        logger.info("updateRoomMicTheme {}", JSONObject.toJSONString(dto));
        truthDareTopicService.updateTruthDareTopic(dto);
        return createResult(HttpCode.SUCCESS, "");
    }
}
