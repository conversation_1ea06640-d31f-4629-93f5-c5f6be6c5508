package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.FcmPushService;
import com.quhong.operation.server.GeneralService;
import com.quhong.operation.server.ScheduledServer;
import com.quhong.operation.server.report.ReportsServer;
import com.quhong.operation.share.condition.*;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 通用配置
 */
@RestController
@RequestMapping(value = "/general", produces = MediaType.APPLICATION_JSON_VALUE)
public class GeneralController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(GeneralController.class);

    @Resource
    private GeneralService generalService;
    @Resource
    private ScheduledServer scheduledServer;
    @Resource
    private FcmPushService fcmPushService;
    @Resource
    private ReportsServer reportsServer;

    @RequireRole
    @RequestMapping("/itemList")
    public String itemList() {
        return createResult(HttpCode.SUCCESS, generalService.itemList());
    }


    @RequireRole
    @RequestMapping("/bannerSwitch")
    public String bannerSwitch() {
        return createResult(HttpCode.SUCCESS, generalService.bannerSwitch());
    }

    @RequireRole
    @RequestMapping("/bannerSwitchSet")
    public String bannerSwitchSet(@RequestParam String uid, @RequestBody BannerSwitchVO dto) {
        logger.info("uid: {}, bannerSwitchSet {}", uid, dto);
        generalService.bannerSwitchSet(uid, dto);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequireRole
    @RequestMapping("/getMomentInfo")
    public String getMomentInfo(@RequestParam String uid, @RequestParam String momentId) {
        logger.info("uid: {}, momentId {}", uid, momentId);
        return createResult(HttpCode.SUCCESS, generalService.getMomentInfo(momentId));
    }

    @RequireRole
    @RequestMapping("/roomScrollPush")
    public String roomScrollPush(@RequestParam String roomId) {
        logger.info("roomId: {}", roomId);
        generalService.roomScrollPush(roomId);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequireRole
    @RequestMapping("/userCommonPopup")
    public String userCommonPopup(@RequestParam String userId, @RequestParam int actionType) {
        logger.info("userId: {}, actionType:{}", userId, actionType);
        generalService.userCommonPopup(userId, actionType);
        return createResult(HttpCode.SUCCESS, null);
    }


    /**
     * 资源key配置中心
     */
    @RequireRole
    @RequestMapping("/getResourcePage")
    public String getResourcePage(@RequestBody ResourceCondition condition) {
        return createResult(HttpCode.SUCCESS, generalService.getResourcePage(condition));
    }

    @RequireRole
    @RequestMapping("/resourceKeyPage")
    public String resourceKeyPage(@RequestBody ResourceKeyCondition condition) {
        return createResult(HttpCode.SUCCESS, generalService.resourceKeyPage(condition));
    }

    @RequireRole
    @PostMapping("/addResourceKey")
    public String addResourceKey(@RequestParam String uid, @RequestBody ResourceKeyVO dto) {
        logger.info("uid: {}, addResourceKey {}", uid, JSONObject.toJSONString(dto));
        generalService.addResourceKey(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/updateResourceKey")
    public String updateResourceKey(@RequestParam String uid, @RequestBody ResourceKeyVO dto) {
        logger.info("uid: {}, updateResourceKey {}", uid, JSONObject.toJSONString(dto));
        generalService.updateResourceKey(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/sendResourceKey")
    public String sendResourceKey(@RequestParam String uid, @RequestBody ResourceKeyVO dto) {
        logger.info("uid: {}, updateResourceKey {}", uid, JSONObject.toJSONString(dto));
        generalService.sendResourceKey(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole(2)
    @RequestMapping("/uploadOSS")
    public String uploadOSSResource(@RequestParam String folder, MultipartFile file) {
        folder = folder + "/";
        return OSSUploadUtils.upload(file, folder);
    }


    /**
     * 火箭V2配置
     */
    @RequireRole
    @RequestMapping("/rocketRewardConfigListV2")
    public String rocketRewardConfigListV2(@RequestBody ResourceKeyCondition condition) {
        return createResult(HttpCode.SUCCESS, generalService.rocketRewardConfigListV2(condition));
    }

    @RequireRole(3)
    @PostMapping("/addRocketRewardConfigV2")
    public String addRocketRewardConfigV2(@RequestParam String uid, @RequestBody RocketConfigV2VO dto) {
        logger.info("uid: {}, addRocketRewardConfigV2 {}", uid, JSONObject.toJSONString(dto));
        generalService.addRocketRewardConfigV2(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole(3)
    @PostMapping("/updateRocketRewardConfigV2")
    public String updateRocketRewardConfigV2(@RequestParam String uid, @RequestBody RocketConfigV2VO dto) {
        logger.info("uid:{}, updateRocketRewardConfigV2 {}", uid, JSONObject.toJSONString(dto));
        generalService.updateRocketRewardConfigV2(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole(3)
    @PostMapping("/updateRocketSwitchV2")
    public String updateRocketSwitchV2(@RequestParam String uid, @RequestBody RocketV2PageResultVO<Object> dto) {
        logger.info("uid: {}, updateRocketSwitchV2 {}", uid, JSONObject.toJSONString(dto));
        RocketV2PageResultVO<Object> vo = generalService.updateRocketSwitchV2(dto);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * fcm测试推送
     */
    @RequireRole
    @RequestMapping("/fcmMessagePush")
    public String fcmMessagePush(@RequestParam String userId,
                                 @RequestParam(defaultValue = "0") int actionType,
                                 @RequestParam(defaultValue = "") String actionValue) {
        generalService.fcmMessagePush(userId, actionType, actionValue);
        return createResult(HttpCode.SUCCESS, "");
    }

    /**
     * fcm消息推送
     */
    @RequireRole
    @RequestMapping("/fcmPushConfigList")
    public String fcmPushConfigList(@RequestBody ResourceKeyCondition condition) {
        return createResult(HttpCode.SUCCESS, fcmPushService.fcmPushConfigList(condition));
    }

    @RequireRole
    @PostMapping("/addFcmPushConfig")
    public String addFcmPushConfig(@RequestParam String uid, @RequestBody FcmPushConfigDataVO dto) {
        logger.info("uid: {}, addFcmPushConfig {}", uid, JSONObject.toJSONString(dto));
        fcmPushService.addFcmPushConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/updateFcmPushConfig")
    public String updateFcmPushConfig(@RequestParam String uid, @RequestBody FcmPushConfigDataVO dto) {
        logger.info("uid: {}, updateFcmPushConfig {}", uid, JSONObject.toJSONString(dto));
        fcmPushService.updateFcmPushConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/deleteFcmPushConfig")
    public String deleteFcmPushConfig(@RequestParam String uid, @RequestBody FcmPushConfigDataVO dto) {
        logger.info("uid: {}, deleteFcmPushConfig {}", uid, JSONObject.toJSONString(dto));
        fcmPushService.removeFcmPushConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }


    /**
     * 8.60房间引导弹窗配置
     */
    @RequireRole
    @RequestMapping("/recommendGuidConfigList")
    public String recommendGuidConfigList(@RequestBody ResourceKeyCondition condition) {
        return createResult(HttpCode.SUCCESS, generalService.recommendGuidConfigList(condition));
    }

    @RequireRole(3)
    @PostMapping("/addRecommendGuidConfig")
    public String addRocketRewardConfigV2(@RequestParam String uid, @RequestBody RecommendGuidConfigVO dto) {
        logger.info("uid: {}, addRecommendGuidConfig {}", uid, JSONObject.toJSONString(dto));
        generalService.addRecommendGuidConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole(3)
    @PostMapping("/updateRecommendGuidConfig")
    public String updateRecommendGuidConfig(@RequestParam String uid, @RequestBody RecommendGuidConfigVO dto) {
        logger.info("uid:{}, updateRecommendGuidConfig {}", uid, JSONObject.toJSONString(dto));
        generalService.updateRecommendGuidConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole(3)
    @PostMapping("/removeRecommendGuidConfig")
    public String removeRecommendGuidConfig(@RequestParam String uid, @RequestBody RecommendGuidConfigVO dto) {
        logger.info("uid:{}, docId {}", uid, dto.getDocId());
        generalService.removeRecommendGuidConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/sendMailTest")
    public String sendMailTest(@RequestParam String uid) {
        logger.info("uid: {}, sendMailTest", uid);
//        scheduledServer.sendFirstRechargeAccountEmail();
//        scheduledServer.sendBlockAccountEmail();
        scheduledServer.sendUnblockAccountEmail();
        return createResult(HttpCode.SUCCESS, "");
    }

    /**
     * 获取new列表推荐排序参数配置
     */
    @RequireRole()
    @PostMapping("/listNewRoomConfig")
    public HttpResult<NewRoomConfigVO> listNewRoomConfig() {
        return new HttpResult<NewRoomConfigVO>().ok(generalService.listNewRoomConfig());
    }

    /**
     * 保存new列表推荐排序参数配置
     */
    @RequireRole(3)
    @PostMapping("/updateNewRoomConfig")
    public HttpResult<Object> updateNewRoomConfig(@RequestParam String uid, @RequestBody NewRoomConfigVO dto) {
        logger.info("uid:{}, updateNewRoomConfig {}", uid, JSONObject.toJSONString(dto));
        generalService.updateNewRoomConfig(dto);
        return new HttpResult<>().ok();
    }

    /**
     * 优质房间列表
     */
    @RequireRole
    @PostMapping("/highQualityRoomList")
    public HttpResult<PageResultVO<HighQualityRoomVO>> highQualityRoomList(@RequestBody PageCondition condition) {
        logger.info("get highQualityRoomList {}", JSONObject.toJSONString(condition));
        return new HttpResult<PageResultVO<HighQualityRoomVO>>().ok(generalService.highQualityRoomList(condition));
    }

    /**
     * 优质房间列表导出
     */
    @RequireRole
    @GetMapping("/highQualityRoomExport")
    public void highQualityRoomExport(HttpServletResponse response) {
        PageCondition condition = new PageCondition();
        condition.setPage(1);
        condition.setPageSize(10000);
        PageResultVO<HighQualityRoomVO> pageResultVO = generalService.highQualityRoomList(condition);
        ExcelUtils.exportExcel(response, pageResultVO.getList(), HighQualityRoomVO.class, "highQualityRoom", "优质房间列表");
    }

    /**
     * 优质房间操作
     */
    @RequireRole(3)
    @PostMapping("/highQualityRoomOpt")
    public HttpResult<Object> highQualityRoomOpt(@RequestParam String uid, @RequestBody RoomOptCondition dto) {
        logger.info("uid:{}, highQualityRoomOpt {}", uid, JSONObject.toJSONString(dto));
        generalService.highQualityRoomOpt(dto);
        return new HttpResult<>().ok();
    }

    /**
     * 大R房间列表
     */
    @RequireRole
    @PostMapping("/bigRRoomList")
    public HttpResult<PageResultVO<HighQualityRoomVO>> bigRRoomList(@RequestBody PageCondition condition) {
        logger.info("get bigRRoomList {}", JSONObject.toJSONString(condition));
        return new HttpResult<PageResultVO<HighQualityRoomVO>>().ok(generalService.bigRRoomList(condition));
    }

    /**
     * 大R房间列表导出
     */
    @RequireRole
    @GetMapping("/bigRRoomExport")
    public void bigRRoomExport(HttpServletResponse response) {
        PageCondition condition = new PageCondition();
        condition.setPage(1);
        condition.setPageSize(10000);
        PageResultVO<HighQualityRoomVO> pageResultVO = generalService.bigRRoomList(condition);
        ExcelUtils.exportExcel(response, pageResultVO.getList(), HighQualityRoomVO.class, "bigRRoom", "大R房间列表");
    }

    /**
     * 大R房间操作
     */
    @RequireRole(3)
    @PostMapping("/bigRRoomListOpt")
    public HttpResult<Object> bigRRoomListOpt(@RequestParam String uid, @RequestBody RoomOptCondition dto) {
        logger.info("uid:{}, bigRRoomListOpt {}", uid, JSONObject.toJSONString(dto));
        generalService.bigRRoomListOpt(dto);
        return new HttpResult<>().ok();
    }

    /**
     * 分页查询投放消耗记录
     */
    @RequireRole
    @RequestMapping("/putInConsumeList")
    public HttpResult<PageResultVO<PutInConsumeRecordVO>> list(@RequestParam String startDate, @RequestParam String endDate,
                                                               @RequestParam Integer page, @RequestParam Integer pageSize) {
        logger.info("get list startDate={}, endDate={}, page={}, pageSize={}", startDate, endDate, page, pageSize);
        return new HttpResult<PageResultVO<PutInConsumeRecordVO>>().ok(reportsServer.getPutInConsumeRecordPage(startDate, endDate, page, pageSize));
    }


    @RequireRole
    @RequestMapping("/huaweiCheckSwitchSet")
    public HttpResult<Object> huaweiCheckSwitchSet(@RequestParam String uid, @RequestBody HuaWeiCheckVO dto) {
        logger.info("uid: {}, huaweiCheckSwitchSet {}", uid, dto);
        generalService.huaweiCheckSwitchSet(uid, dto);
        return new HttpResult<>().ok();
    }

    @RequireRole
    @RequestMapping("/huaweiCheckSwitchList")
    public HttpResult<HuaWeiCheckVO> huaweiCheckSwitchList(@RequestParam String uid) {
        logger.info("uid: {}, huaweiCheckSwitchList", uid);
        HuaWeiCheckVO vo =  generalService.huaweiCheckSwitchList(uid);
        return new HttpResult<HuaWeiCheckVO>().ok(vo);
    }
}
