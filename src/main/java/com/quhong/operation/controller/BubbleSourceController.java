package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.BubbleSourceService;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.dto.BubbleSourceDTO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 房间背景图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/bubble", produces = MediaType.APPLICATION_JSON_VALUE)
public class BubbleSourceController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(BubbleSourceController.class);
    private final static String filePath = "buddle/";

    @Resource
    private BubbleSourceService bubbleSourceService;


    @RequestMapping("/notLoginList")
    public String bubbleNotLoginList(@RequestBody ItemCondition condition) {
        logger.info("get bubbleNotLoginList {}", condition);
        return createResult(HttpCode.SUCCESS, bubbleSourceService.bubbleList(condition));
    }


    @RequireRole
    @RequestMapping("/list")
    public String bubbleList(@RequestBody ItemCondition condition) {

        logger.info("get bubbleList {}", condition);
        return createResult(HttpCode.SUCCESS, bubbleSourceService.bubbleList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public String addBubbleData(@RequestBody BubbleSourceDTO dto) {

        try {
            logger.info("addBubbleData {}", dto);
            bubbleSourceService.addBubbleSourceData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateBubbleData(@RequestBody BubbleSourceDTO dto) {
        logger.info("updateBubbleData {}", dto);

        try {
            if (StringUtils.isEmpty(dto.getDocId())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }
            bubbleSourceService.updateBubbleSourceData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }
    }



    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return AWSUploadUtils.upload(file, filePath);
    }

}
