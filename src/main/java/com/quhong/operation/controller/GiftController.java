package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.condition.GiftCondition;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.httpResult.OperationHttpCode;
import com.quhong.operation.server.GiftService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.dto.BlindBoxDTO;
import com.quhong.operation.share.dto.GiftDTO;
import com.quhong.operation.share.dto.GiftPanelDTO;
import com.quhong.operation.share.dto.InsertGiftDesDTO;
import com.quhong.operation.share.vo.GiftPanelVO;
import com.quhong.operation.utils.AWSUploadUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 浮萍图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/gift", produces = MediaType.APPLICATION_JSON_VALUE)
public class GiftController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(GiftController.class);
    private final static String filePath = "gift/";

    @Resource
    private GiftService giftService;

    /**
     * 新增或修改礼物说明
     * @param dto
     * @return void
     */
    @RequireRole
    @PostMapping("/gift_introduce_update")
    public HttpResult<Object> addOrUpdateGiftDesc(@RequestBody InsertGiftDesDTO dto) {
        logger.info("addOrUpdateGiftDesc InsertGiftDesDTO {}", JSONObject.toJSONString(dto));
        giftService.addOrUpdateGiftDesc(dto);
        return HttpResult.getOk();
    }

    /**
     * 删除礼物说明
     * @param rid
     * @return void
     */
    @RequireRole
    @PostMapping("/gift_introduce_remove")
    public HttpResult<Object> deleteGiftDesc(@RequestParam int rid) {
        giftService.deleteGiftDesc(rid);
        return HttpResult.getOk();
    }

    @RequireRole
    @RequestMapping("/list")
    public String giftList(@RequestBody GiftCondition condition) {

        logger.info("get giftList {}", condition);
        return createResult(HttpCode.SUCCESS, giftService.giftList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public String addGiftData(@RequestParam String uid, @RequestBody GiftDTO dto) {
        logger.info("uid: {}, addGiftData {}", uid, dto);
        try {
            return createResult(HttpCode.SUCCESS,giftService.addGiftData(uid, dto));
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateGiftData(@RequestBody GiftDTO dto) {
        logger.info("updateGiftData {}", dto);
        try {
            if (StringUtils.isEmpty(dto.getRid())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }
            if(dto.getGatype() == 7  && dto.getStatus() == 1){
                return createResult(OperationHttpCode.GIFT_SVG_STATUS, "");
            }

            giftService.updateGiftData(dto);
            return createResult(HttpCode.SUCCESS, "");

        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }
    }

    /**
     * 盲盒礼物配置
     */
    @RequireRole
    @RequestMapping("blindBoxList")
    public String blindBoxList(@RequestBody BaseCondition condition) {
        logger.info("get blindBoxList {}", condition);
        return createResult(HttpCode.SUCCESS, giftService.blindBoxList(condition));

    }

    @RequireRole
    @PostMapping("/blindBoxAddOrUpdate")
    public String blindBoxAdd( @RequestBody BlindBoxDTO dto) {
        logger.info("blindBoxAdd {}", dto);
        try {
            giftService.blindBoxAdd(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/blindBoxDelete")
    public String blindBoxDelete( @RequestBody BlindBoxDTO dto) {
        logger.info("blindBoxDelete {}", dto);
        try {
            giftService.blindBoxDelete(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    /**
     * 发送礼物面板显示
     */
    @RequireRole
    @RequestMapping("/panelShow")
    public HttpResult<PageVO<GiftPanelVO>> panelShow(@RequestBody GiftPanelDTO dto) {
        logger.info("panelShow. {}", dto);
        return HttpResult.getOk(giftService.panelShow(dto.getShowType(), dto.getGpType()));
    }

    /**
     * 修改礼物面板排序
     */
    @RequireRole
    @RequestMapping("/updatePanelOrder")
    public HttpResult<Object> updatePanelOrder(@RequestBody GiftPanelDTO dto) {
        logger.info("updatePanelOrder. {}", dto);
        giftService.updatePanelOrder(dto);
        return HttpResult.getOk();
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return AWSUploadUtils.upload(file, filePath);
    }
}
