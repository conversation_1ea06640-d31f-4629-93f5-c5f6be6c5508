package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.TopicServer;
import com.quhong.operation.share.dto.FriendTopicAddDTO;
import com.quhong.operation.share.dto.FriendTopicUpdateDTO;
import com.quhong.operation.share.vo.FriendTopicVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/topic")
public class TopicController {

    private static final Logger logger = LoggerFactory.getLogger(TopicController.class);

    @Autowired
    private TopicServer topicServer;


    /**
     * 朋友圈新增话题
     *
     * @param addTopic
     * @return
     */
    @PostMapping("/friend/add")
    public HttpResult<String> addFriendTopic(FriendTopicAddDTO addTopic) {
        HttpResult<String> result = new HttpResult<>();
        try {
            logger.info("method add friend topic. param data = {}", addTopic.toString());
            //参数校验
            String topicArName = addTopic.getTopicArName();
            String topicEnName = addTopic.getTopicEnName();
            Integer status = addTopic.getStatus();
            Integer enOrder = addTopic.getEnOrder();
            Integer arOrder = addTopic.getArOrder();
            if (StringUtils.isEmpty(topicArName) || StringUtils.isEmpty(topicEnName) || status == null || enOrder == null || arOrder == null) {
                logger.error("add friend topic data param error. param = {}", addTopic.toString());
                return result.error(HttpCode.PARAM_ERROR.getCode(), HttpCode.PARAM_ERROR.getMsg());
            }
            //业务调用
            ApiResult<String> apiResult = topicServer.addFriendTopic(addTopic);
            if (apiResult.isOK()) {
                return result.ok();
            }
            return result.error(apiResult.getMsg());
        } catch (Exception e) {
            logger.error("topic friend add error. msg = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getCode(), HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 朋友圈更新话题
     *
     * @param updateDTO
     * @return
     */
    @PostMapping("/friend/update")
    public HttpResult<String> updateFriendTopic(FriendTopicUpdateDTO updateDTO) {
        HttpResult<String> result = new HttpResult<>();
        try {
            logger.info("method update friend topic. param data = {}", updateDTO.toString());
            String topicId = updateDTO.getTopicId();
            if (StringUtils.isEmpty(topicId)) {
                logger.error("update friend topic data param error. param = {}", updateDTO.toString());
                return result.error(HttpCode.PARAM_ERROR.getCode(), HttpCode.PARAM_ERROR.getMsg());
            }
            //业务调用
            ApiResult<String> apiResult = topicServer.updateFriendTopic(updateDTO);
            if (apiResult.isOK()) {
                return result.ok();
            }
            return result.error(apiResult.getMsg());
        } catch (Exception e) {
            logger.error("topic friend update error. msg = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getCode(), HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 朋友圈获取话题列表
     *
     * @param status
     * @param lang
     * @param topic
     * @param page
     * @return
     */
    @RequireRole
    @GetMapping("/friend/list")
    public HttpResult<PageResultVO> listFriendTopic(@RequestParam("status") Integer status, @RequestParam("lang") Integer lang,
                                                    @RequestParam(value = "topic", required = false) String topic, @RequestParam("page") Integer page) {
        HttpResult<PageResultVO> result = new HttpResult<>();
        try {
            //参数校验
            logger.info("method list friend topic. status = {} lang = {} topic = {}", status, lang, topic);
            if (status == null || lang == null) {
                logger.error("list friend topic param error. status = {} lang = {} topic = {}", status, lang, topic);
                return result.error(HttpCode.PARAM_ERROR.getCode(), HttpCode.PARAM_ERROR.getMsg());
            }
            if (page == null) {
                page = 1;
            }
            //业务调用
            ApiResult<PageResultVO<FriendTopicVO>> apiResult = topicServer.listFriendTopic(status, lang, topic, page);
            if (apiResult.isOK()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getMsg());
        } catch (Exception e) {
            logger.error("list topic friend error. msg = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getCode(), HttpCode.SERVER_ERROR.getMsg());
    }

}
