package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.JoinSourceService;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.dto.JoinSourceDTO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 浮萍图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/joinSource", produces = MediaType.APPLICATION_JSON_VALUE)
public class JoinSourceController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(JoinSourceController.class);

    private final static String filePath = "join/";

    @Resource
    private JoinSourceService joinSourceService;

    @RequireRole
    @RequestMapping("/list")
    public String joinSourceList(@RequestBody ItemCondition condition) {
        logger.info("get joinSource {}", condition);
        return createResult(HttpCode.SUCCESS, joinSourceService.joinSourceList(condition));
    }

    @RequireRole
    @PostMapping("/addData")
    public String addJoinSourceData(@RequestParam String uid, @RequestBody JoinSourceDTO dto) {
        logger.info("uid: {}, addJoinSourceData {}", uid, dto);
        try {
            joinSourceService.addJoinSourceData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateJoinSourceData(@RequestBody JoinSourceDTO dto) {
        logger.info("updateJoinSourceData {}", dto);
        try {
            if (StringUtils.isEmpty(dto.getDocId())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }
            joinSourceService.updateJoinSourceData(dto);
            return createResult(HttpCode.SUCCESS, "");

        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return AWSUploadUtils.upload(file, filePath);
    }

}
