package com.quhong.operation.controller;

import com.alibaba.fastjson2.JSON;
import com.quhong.exception.CommonException;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.LuckyGiftConfigService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.LuckyGiftConfigVO;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


/**
 * 运营平台/幸运礼物配置new
 */
@RestController
@RequestMapping(value = "/luckyGiftConfig", produces = MediaType.APPLICATION_JSON_VALUE)
public class LuckyGiftConfigController {
    private final static Logger logger = LoggerFactory.getLogger(LuckyGiftConfigController.class);
    private final static String filePath = "common/";

    @Resource
    private LuckyGiftConfigService luckyGiftConfigService;


    /**
     * 获取幸运礼物列表
     * 如果列表中有钻石版幸运礼物，则钻石版幸运礼物始终为列表第一个
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<Object> getDataList(@RequestBody BaseCondition condition) {
        logger.info("getDataList {}", condition);
        return new HttpResult<>().ok(luckyGiftConfigService.getDataList(condition));
    }

    /**
     * 新增幸运礼物
     */
    @RequireRole
    @PostMapping("/addData")
    public HttpResult<Object> addData(@RequestBody LuckyGiftConfigVO dto) {
        try {
            logger.info("add luckyGift Data {}", JSON.toJSONString(dto));
            luckyGiftConfigService.addData(dto);
            return new HttpResult<>().ok();
        } catch (CommonException e) {
            return new HttpResult<>().error(1, e.getHttpCode().getMsg());
        }
    }

    /**
     * 更新幸运礼物
     */
    @RequireRole
    @PostMapping("/updateData")
    public HttpResult<Object> updateData(@RequestBody LuckyGiftConfigVO dto) {
        try {
            logger.info("update luckyGift Data {}", JSON.toJSONString(dto));
            luckyGiftConfigService.updateData(dto);
            return new HttpResult<>().ok();
        } catch (CommonException e) {
            return new HttpResult<>().error(1, e.getHttpCode().getMsg());
        }
    }

    /**
     * 删除幸运礼物
     * giftList、zipInfo需要传
     */
    @RequireRole
    @PostMapping("/deleteData")
    public HttpResult<Object> deleteData(@RequestBody LuckyGiftConfigVO dto) {
        try {
            logger.info("delete  luckyGift Data {}", JSON.toJSONString(dto));
            luckyGiftConfigService.deleteData(dto);
            return new HttpResult<>().ok();
        } catch (CommonException e) {
            return new HttpResult<>().error(1, e.getHttpCode().getMsg());
        }
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return OSSUploadUtils.upload(file, filePath);
    }
}
