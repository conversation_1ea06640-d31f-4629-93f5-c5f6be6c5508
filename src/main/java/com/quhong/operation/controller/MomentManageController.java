package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.GeneralService;
import com.quhong.operation.share.dto.MomentDTO;
import com.quhong.operation.share.vo.MomentVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 运营平台/朋友圈管理
 */
@RestController
@RequestMapping(value = "/moment")
public class MomentManageController {

    private final static Logger logger = LoggerFactory.getLogger(MomentManageController.class);

    @Resource
    private GeneralService generalService;


    /**
     * 获取朋友圈列表
     */
    @RequestMapping("/getMomentList")
    public HttpResult<PageVO<MomentVO.MomentDetailVO>> momentList(@RequestBody MomentDTO.MomentListDTO dto) {
        logger.info("moment list body={}", JSON.toJSONString(dto));
        return HttpResult.getOk(generalService.getMomentList(dto));
    }


    /**
     * 朋友圈置顶
     *
     * @param mid     朋友圈id
     * @param opType  1置顶 2取消
     * @param endTime 置顶结束时间戳
     */
    @RequireRole(3)
    @PostMapping("pinMoment")
    public HttpResult<Object> pinMoment(@RequestParam(value = "uid") String uid, @RequestParam(value = "mid") String mid,
                                        @RequestParam(value = "opType") Integer opType, @RequestParam(value = "endTime", required = false) Integer endTime) {
        logger.info("pinMoment mid={} opType={} endTime={}", mid, opType, endTime);
        generalService.pinMoment(uid, mid, opType, endTime);
        return HttpResult.getOk();
    }

}
