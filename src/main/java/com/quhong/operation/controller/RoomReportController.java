package com.quhong.operation.controller;

import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tree.TreeTask;
import com.quhong.core.utils.DateHelper;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.server.report.RoomReportServer;
import com.quhong.operation.share.vo.reports.room.RoomVideoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


@RestController
@RequestMapping("/room")
public class RoomReportController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RoomReportController.class);

    @Autowired
    private RoomReportServer roomReportServer;

    private Map<String, TaskQueue> cacheMap = new ConcurrentHashMap<>();

    private static final Comparator<RoomVideoVO> COMPARATOR_VIDEO = new Comparator<RoomVideoVO>() {
        //根据通话时间降序，使得前端数据更具有可视化
        @Override
        public int compare(RoomVideoVO o1, RoomVideoVO o2) {
            DayTimeData o1Time = DateHelper.ARABIAN.getContinuesDays(o1.getDate());
            DayTimeData o2Time = DateHelper.ARABIAN.getContinuesDays(o2.getDate());
            return -o1Time.getTime() + o2Time.getTime();
        }
    };

    /**
     * 获取视屏房数据报表
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return
     */
    @GetMapping("/video/report")
    public DeferredResult<String> videoRoomData(String start, String end) {
        DeferredResult<String> result = new DeferredResult<>();
        try {
            logger.info("begin get video room report data. start = {} end = {}");
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.error("get video room report data error. param error. start = {} end = {}", start, end);
                result.setResult(createResult(HttpCode.PARAM_ERROR, null));
                return result;
            }
            //2、业务调用
            TreeTask parentTask = new TreeTask() {
                @Override
                protected void doComplete(Object data) {
                    List<RoomVideoVO> retList = (List<RoomVideoVO>) getData();
                    retList.sort(COMPARATOR_VIDEO);
                    result.setResult(createResult(HttpCode.SUCCESS, retList));
                }

                @Override
                protected void execute() {
                    //do nothing
                }
            };
            List<RoomVideoVO> retList = Collections.synchronizedList(new ArrayList<>());
            parentTask.setData(retList);
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            for (DayTimeData timeData : dayTimeList) {
                TreeTask subTask = new TreeTask(parentTask) {
                    @Override
                    protected void execute() {
                        int tomorrowStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                        if (timeData.getTime() >= tomorrowStartTime) {
                            RoomVideoVO statData = new RoomVideoVO();
                            statData.setDate(timeData.getDate());
                            retList.add(statData);
                        } else {
                            RoomVideoVO statData = roomReportServer.getRoomVideoReport(timeData);
                            retList.add(statData);
                        }
                    }

                    @Override
                    protected void doComplete(Object data) {

                    }
                };
                addTask(timeData, subTask, "room_video");
            }
            parentTask.start();
            return result;
        } catch (Exception e) {
            logger.error("get room report error. msg = {}", e.getMessage(), e);
        }
        result.setResult(createResult(HttpCode.SERVER_ERROR, null));
        return result;
    }

    /**
     * 获取主题房数据报表
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return
     */
    @GetMapping("/topic/report")
    public DeferredResult<String> topicRoomData(String start, String end) {
        DeferredResult<String> result = new DeferredResult<>();
        result.setResult(createResult(HttpCode.SUCCESS, null));
        return result;
    }

    /**
     * 获取音乐房数据报表
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return
     */
    @GetMapping("/music/report")
    public DeferredResult<String> musicRoomData(String start, String end) {
        DeferredResult<String> result = new DeferredResult<>();
        result.setResult(createResult(HttpCode.SUCCESS, null));
        return result;
    }

    private void addTask(DayTimeData dayTimeData, TreeTask subTask, String pre) {
        String key = pre + "_" + dayTimeData.getDate();
        TaskQueue queue = cacheMap.get(key);
        if (queue == null) {
            synchronized (this) {
                queue = cacheMap.get(key);
                if (queue == null) {
                    queue = new TaskQueue();
                    cacheMap.put(key, queue);
                }
            }
        }
        queue.add(subTask);
    }

    private String getKey(DayTimeData dayTimeData, String channelId, int os) {
        return dayTimeData.getDate() + "_" + channelId + "_" + os;
    }

}
