package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.StaffBindUserRechargeJoinData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.server.ReportCallBackUserService;
import com.quhong.operation.share.condition.ReportCallBackUserCondition;
import com.quhong.operation.share.condition.ReportUserCondition;
import com.quhong.operation.share.condition.RookieRoomUserConfigCondition;
import com.quhong.operation.share.condition.StaffBindUserCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ReportCallBackUserVO;
import com.quhong.operation.share.vo.RookieRoomConfigVO;
import com.quhong.operation.share.vo.reports.RepeatAccountsVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 拓新
 * 召回
 *
 * @date 2023/4/24
 */
@RestController
@RequestMapping(value = "/reportCallBack", produces = MediaType.APPLICATION_JSON_VALUE)
public class ReportCallBackUserController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ReportCallBackUserController.class);


    @Resource
    private ReportCallBackUserService reportCallBackUserService;

    @RequireRole
    @PostMapping("/staffBindUserRechargeList")
    public String staffBindUserRechargeList(@RequestBody StaffBindUserCondition condition) {
        logger.info("get staffBindUserRechargeList {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, reportCallBackUserService.staffBindUserRechargeList(condition));
    }

    @RequireRole
    @PostMapping("/staffBindUserSummaryList")
    public String staffBindUserSummaryList(@RequestBody StaffBindUserCondition condition) {
        logger.info("get staffBindUserSummaryList {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, reportCallBackUserService.staffBindUserSummaryList(condition));
    }


    @RequireRole(3)
    @PostMapping("/preAdd")
    public HttpResult<ReportCallBackUserVO> preAdd(@RequestParam String uid, @RequestBody ReportCallBackUserCondition dto) {
        logger.info("preAdd. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        ReportCallBackUserVO vo = reportCallBackUserService.preAdd(dto);
        return HttpResult.getOk(vo);
    }

    @RequireRole(3)
    @RequestMapping("/preAddByExcel")
    public HttpResult<ReportCallBackUserVO> preAddByExcel(@RequestParam String uid,
                                                   @RequestParam Integer bindType,
                                                   @RequestParam Integer staffId,
                                                   MultipartFile file) {
        List<String> ridList = getWorkbookData(file);
        if (ridList == null) {
            return new HttpResult<>(HttpCode.PARAM_ERROR.getCode(), "上传文件为空", null);
        }
        ReportCallBackUserCondition dto = new ReportCallBackUserCondition();
        dto.setBindType(bindType);
        dto.setStaffId(staffId);
        dto.setRidList(ridList);
        logger.info("preAddByExcel. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        ReportCallBackUserVO vo = reportCallBackUserService.preAdd(dto);
        return HttpResult.getOk(vo);
    }

    @RequireRole(3)
    @PostMapping("/addSubmit")
    public HttpResult<?> addSubmit(@RequestParam String uid, @RequestBody ReportCallBackUserCondition dto) {
        logger.info("addSubmit. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        reportCallBackUserService.add(dto);
        return HttpResult.getOk();
    }

    @RequireRole(3)
    @PostMapping("/deleteBindUser")
    public HttpResult<?> deleteBindUser(@RequestParam String uid, @RequestBody ReportCallBackUserCondition dto) {
        logger.info("deleteBindUser. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        reportCallBackUserService.deleteBindUser(dto);
        return HttpResult.getOk();
    }

    @RequireRole(1)
    @PostMapping("/getSameByDevice")
    public HttpResult<ReportCallBackUserVO> getSameByDevice(@RequestParam String uid, @RequestBody ReportCallBackUserCondition dto) {
        logger.info("getSameByDevice. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        ReportCallBackUserVO vo = reportCallBackUserService.getSameByDevice(dto);
        return HttpResult.getOk(vo);
    }

    @RequireRole(1)
    @PostMapping("/getAllStaff")
    public HttpResult<ReportCallBackUserVO> getAllStaff(@RequestParam String uid, @RequestBody ReportCallBackUserCondition dto) {
        logger.info("getAllStaff. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        ReportCallBackUserVO vo = reportCallBackUserService.getAllStaff(dto);
        return HttpResult.getOk(vo);
    }


    /**
     * 获取workbook第一sheet第一列，去掉第一行数据
     *
     * @param file workbook文件
     * @return 返回数据
     */
    private List<String> getWorkbookData(MultipartFile file) {
        if (null == file) {
            return null;
        }
        try {
            List<String> result = new ArrayList<>();
            Map<String, List<String>> listMap = ExcelUtils.readExcel(file.getInputStream());
            //返回第一列数据
            for (List<String> list : listMap.values()) {
                return list;
            }
        } catch (IOException e) {
            logger.error("获取文件内容失败");
        }
        return Collections.emptyList();
    }



    @RequireRole(3)
    @PostMapping("/addOrDeleteRookieRoomUser")
    public HttpResult<RookieRoomConfigVO> addOrDeleteRookieRoomUser(@RequestParam String uid, @RequestBody RookieRoomUserConfigCondition dto) {
        logger.info("addOrDeleteRookieRoomUser. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        RookieRoomConfigVO vo = reportCallBackUserService.addOrDeleteRookieRoomUser(dto);
        return HttpResult.getOk(vo);
    }



    @RequireRole(3)
    @RequestMapping("/addOrDeleteRookieRoomUserByExcel")
    public HttpResult<RookieRoomConfigVO> addOrDeleteRookieRoomUserByExcel(@RequestParam String uid,
                                                          @RequestParam Integer type,
                                                          @RequestParam Integer cmdType,
                                                          MultipartFile file) {
        List<String> ridList = getWorkbookData(file);
        if (ridList == null) {
            return new HttpResult<>(HttpCode.PARAM_ERROR.getCode(), "上传文件为空", null);
        }
        RookieRoomUserConfigCondition dto = new RookieRoomUserConfigCondition();
        dto.setType(type);
        dto.setCmdType(cmdType);
        dto.setRidList(ridList);
        logger.info("addOrDeleteRookieRoomUserByExcel. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        RookieRoomConfigVO vo = reportCallBackUserService.addOrDeleteRookieRoomUser(dto);
        return HttpResult.getOk(vo);
    }

    @RequireRole
    @PostMapping("/rookieRoomUserConfigList")
    public String rookieRoomUserConfigList(@RequestBody RookieRoomUserConfigCondition condition) {
        logger.info("get RookieRoomUserConfigCondition {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, reportCallBackUserService.rookieRoomUserConfigList(condition));
    }



}
