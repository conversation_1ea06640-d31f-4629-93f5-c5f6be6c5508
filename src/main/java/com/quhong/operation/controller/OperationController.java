package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.GameExtraConfig;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.BizRobotConfigDao;
import com.quhong.mysql.data.BizRobotConfigData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.dao.MomentOpDao;
import com.quhong.operation.server.StartPageServer;
import com.quhong.operation.share.vo.OperationRoomVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.StartPageVO;
import com.quhong.redis.LuckyDiceRedis;
import com.quhong.redis.OperationConfigRedis;
import com.quhong.service.mysql.ClientLogService;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 运营平台/通用配置
 */
@RestController
@RequestMapping("/operation")
public class OperationController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(OperationController.class);

    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private OperationConfigRedis operationConfigRedis;
    @Resource
    private StartPageServer startPageServer;
    @Resource
    private MomentOpDao momentOpDao;
    @Resource
    private BizRobotConfigDao bizRobotConfigDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private ClientLogService clientLogService;
    @Resource
    private LuckyDiceRedis luckyDiceRedis;

    @RequireRole(3)
    @RequestMapping("/addTopMomentUser")
    public HttpResult<?> addTopMomentUser(@RequestParam int rid) {
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            return new HttpResult<>().error("未找到用户: " + rid);
        }
        operationConfigRedis.addTopMomentUser(actorData.getUid());
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/delTopMomentUser")
    public HttpResult<?> delTopMomentUser(@RequestParam int rid) {
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            return new HttpResult<>().error("未找到用户: " + rid);
        }
        operationConfigRedis.delTopMomentUser(actorData.getUid());
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/listTopMomentUser")
    public HttpResult<?> listTopMomentUser() {
        Set<String> topMomentUser = operationConfigRedis.listTopMomentUser();
        List<OperationRoomVO> operationRoomVOList = new ArrayList<>();
        for (String uid : topMomentUser) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            operationRoomVOList.add(new OperationRoomVO(actorData.getRid(), actorData.getName()));
        }
        return new HttpResult<>().ok(operationRoomVOList);
    }

    @RequireRole(3)
    @RequestMapping("/setMomentScoreWeight")
    public HttpResult<?> setMomentScoreWeight(@RequestBody JSONObject jsonObject) {
        if (null == jsonObject) {
            return new HttpResult<>().error("参数为空");
        }
        operationConfigRedis.setMomentScoreWeight(jsonObject);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/getMomentScoreWeight")
    public HttpResult<?> getMomentScoreWeight() {
        return new HttpResult<>().ok(operationConfigRedis.getMomentScoreWeight());
    }

    @RequireRole(3)
    @RequestMapping("/setForYouScoreWeight")
    public HttpResult<?> setForYouScoreWeight(@RequestBody JSONObject jsonObject) {
        if (null == jsonObject) {
            return new HttpResult<>().error("参数为空");
        }
        operationConfigRedis.setForYouScoreWeight(jsonObject);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/getForYouScoreWeight")
    public HttpResult<?> getForYouScoreWeight() {
        return new HttpResult<>().ok(operationConfigRedis.getForYouScoreWeight());
    }

    @RequireRole(3)
    @RequestMapping("/setRobotConfig")
    public HttpResult<?> setRobotConfig(@RequestBody JSONObject jsonObject) {
        if (null == jsonObject) {
            return new HttpResult<>().error("参数为空");
        }
        operationConfigRedis.setRobotConfig(jsonObject);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/getRobotConfig")
    public HttpResult<?> getRobotConfig() {
        return new HttpResult<>().ok(operationConfigRedis.getRobotConfig());
    }

    @RequireRole(3)
    @RequestMapping("/addVideoOperationRoom")
    public HttpResult<?> addVideoOperationRoom(@RequestParam int rid) {
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            return new HttpResult<>().error("未找到用户: " + rid);
        }
        MongoRoomData roomData = mongoRoomDao.findData(RoomUtils.formatRoomId(actorData.getUid()));
        if (null == roomData) {
            return new HttpResult<>().error("未找到房间: " + rid);
        }
        operationConfigRedis.addVideoOperationRoom(roomData.getRid());
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/delVideoOperationRoom")
    public HttpResult<?> delVideoOperationRoom(@RequestParam int rid) {
        ActorData actorData = actorDao.getActorByRid(rid);
        operationConfigRedis.delVideoOperationRoom(RoomUtils.formatRoomId(actorData.getUid()));
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/listVideoOperationRoom")
    public HttpResult<?> listVideoOperationRoom() {
        Set<String> allVideoOperationRoom = operationConfigRedis.getAllVideoOperationRoom();
        List<OperationRoomVO> operationRoomVOList = new ArrayList<>();
        for (String roomId : allVideoOperationRoom) {
            MongoRoomData roomData = mongoRoomDao.findData(roomId);
            ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomData.getRid()));
            operationRoomVOList.add(new OperationRoomVO(actorData.getRid(), roomData.getName()));
        }
        return new HttpResult<>().ok(operationRoomVOList);
    }


    @RequireRole
    @RequestMapping("/flashList")
    public HttpResult<PageResultVO> flashList(
            @RequestParam(value = "valid") Integer valid,
            @RequestParam(value = "page") Integer page,
            @RequestParam(value = "page_size") Integer pageSize,
            @RequestParam(value = "search", defaultValue = "") String search) {
        logger.info("get flashList valid={}, page={}, pageSize={}", valid, page, pageSize);

        HttpResult<PageResultVO> result = new HttpResult<>();

        try {
            //参数校验
            logger.info("method flashList valid = {} page = {} page_size = {}, search= {}", valid, page, pageSize, search);
            if (page == null) {
                page = 1;
            }

            if (pageSize == null) {
                pageSize = 12;
            }

            //业务调用
            ApiResult<PageResultVO<StartPageVO>> apiResult = startPageServer.listStartPage(valid, page, pageSize, search);
            if (apiResult.isOK()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getMsg());

        } catch (Exception e) {
            logger.error("list topic friend error. msg = {}", e.getMessage(), e);
        }

        return result.error(HttpCode.SERVER_ERROR.getCode(), HttpCode.SERVER_ERROR.getMsg());

    }

    /**
     * 通过rid获取用户发送的朋友圈信息，用于选择朋友圈id
     *
     * @param rid 用户rid
     */
    @RequireRole(3)
    @RequestMapping("/momentList")
    public HttpResult<?> momentList(@RequestParam int rid, @RequestParam(required = false) int page) {
        logger.info("moment list rid={} page={}", rid, page);
        return new HttpResult<>().ok(momentOpDao.getMomentList(actorDao.getActorByRid(rid).getUid(), page));
    }

    @RequireRole(3)
    @RequestMapping("/createBizRobotConfig")
    public HttpResult<?> createBizRobotConfig(HttpServletRequest request, @RequestBody BizRobotConfigData configData) {
        String uid = request.getParameter("uid");
        logger.info("createBizRobotConfig data={} uid={}", JSON.toJSONString(configData), uid);
        if (null == configData) {
            return new HttpResult<>().error("参数为空");
        }
        if (configData.getEnterRoomMode() == 2) {
            if (configData.getActivateEnterRoomTime() != 0 && configData.getActivateEnterRoomTime() >= configData.getLoseEnterRoomTime()) {
                return new HttpResult<>().error("时间范围配置不对");
            }
            String[] roomArr = configData.getEnterRoomRids().split(";");
            String[] roomIdsArr = new String[roomArr.length];
            for (int i = 0; i < roomArr.length; i++) {
                ActorData actor = actorDao.getActorByRid(Integer.parseInt(roomArr[i]));
                if (null == actor) {
                    return new HttpResult<>().error(roomArr[i] + "不存在");
                }
                roomIdsArr[i] = RoomUtils.formatRoomId(actor.getUid());
            }
            configData.setEnterRoomIds(StringUtils.arrayToDelimitedString(roomIdsArr, ";"));
        } else if (configData.getEnterRoomMode() == 3) {
            // 参数校验
            configData.setEnterRoomIds(String.valueOf(Integer.parseInt(configData.getEnterRoomRids())));
        }
        configData.setCreator(managerDao.getDataByUid(uid).getAccount());
        int nowSeconds = DateHelper.getNowSeconds();
        configData.setCtime(nowSeconds);
        configData.setMtime(nowSeconds);
        configData.setStatus(0);
        bizRobotConfigDao.saveConfig(configData);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/updateBizRobotConfig")
    public HttpResult<?> updateBizRobotConfig(HttpServletRequest request, @RequestBody BizRobotConfigData configData) {
        String uid = request.getParameter("uid");
        logger.info("updateBizRobotConfig data={} uid={}", JSON.toJSONString(configData), uid);
        if (null == configData) {
            return new HttpResult<>().error("参数为空");
        }
        if (configData.getEnterRoomMode() == 2) {
            if (configData.getActivateEnterRoomTime() != 0 && configData.getActivateEnterRoomTime() >= configData.getLoseEnterRoomTime()) {
                return new HttpResult<>().error("时间范围配置不对");
            }
            String[] roomArr = configData.getEnterRoomRids().split(";");
            String[] roomIdsArr = new String[roomArr.length];
            for (int i = 0; i < roomArr.length; i++) {
                ActorData actor = actorDao.getActorByRid(Integer.parseInt(roomArr[i]));
                if (null == actor) {
                    return new HttpResult<>().error(roomArr[i] + "不存在");
                }
                roomIdsArr[i] = RoomUtils.formatRoomId(actor.getUid());
            }
            configData.setEnterRoomIds(StringUtils.arrayToDelimitedString(roomIdsArr, ";"));
        } else if (configData.getEnterRoomMode() == 3) {
            // 参数校验
            configData.setEnterRoomIds(String.valueOf(Integer.parseInt(configData.getEnterRoomRids())));
        }
        bizRobotConfigDao.updateConfig(configData);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/updateBizRobotConfigStatus")
    public HttpResult<?> updateBizRobotConfigStatus(HttpServletRequest request, @RequestBody BizRobotConfigData configData) {
        String uid = request.getParameter("uid");
        logger.info("updateBizRobotConfigStatus id={} status={} uid={}", configData.getId(), configData.getStatus(), uid);
        if (configData.getStatus() != 1 && configData.getStatus() != 2) {
            return new HttpResult<>().error("参数错误");
        }
        bizRobotConfigDao.updateStatus(configData.getId(), configData.getStatus());
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/getBizRobotConfig")
    public HttpResult<?> getBizRobotConfig() {
        return new HttpResult<>().ok(bizRobotConfigDao.getConfigList());
    }

    @RequireRole(3)
    @RequestMapping("/delBizRobotConfig")
    public HttpResult<?> delBizRobotConfig(HttpServletRequest request, @RequestBody BizRobotConfigData configData) {
        String uid = request.getParameter("uid");
        logger.info("delBizRobotConfig id={} uid={}", configData.getId(), uid);
        bizRobotConfigDao.delConfig(configData.getId());
        return new HttpResult<>().ok();
    }

    /**
     * 获取客户端日志
     *
     * @param rid 用户rid
     */
    @RequireRole(3)
    @RequestMapping("/listClientLog")
    public HttpResult<?> listTopMomentUser(@RequestParam int rid) {
        logger.info("listClientLog rid={}", rid);
        return new HttpResult<>().ok(clientLogService.clientLogList(rid > 0 ? actorDao.getActorByRid(rid).getUid() : null));
    }

    /**
     * 发送客户端日志请求
     *
     * @param rid        用户rid
     * @param logEndTime 日志截止时间
     */
    @RequireRole(3)
    @RequestMapping("/addClientLogReq")
    public HttpResult<?> addClientLogReq(@RequestParam int rid, @RequestParam int logEndTime) {
        logger.info("addClientLogReq rid={} logEndTime={}", rid, logEndTime);
        int nowSeconds = DateHelper.getNowSeconds();
        if (logEndTime > nowSeconds || logEndTime < nowSeconds - TimeUnit.DAYS.toSeconds(7)) {
            return new HttpResult<>().error("参数错误");
        }
        clientLogService.addClientLogReqFlag(actorDao.getActorByRid(rid).getUid(), logEndTime);
        return new HttpResult<>().ok();
    }

    /**
     * 获取游戏额外奖励配置列表
     * 不分页
     */
    @RequireRole(3)
    @RequestMapping("/listGameExtraConfig")
    public HttpResult<List<GameExtraConfig>> listGameExtraConfig() {
        return new HttpResult<List<GameExtraConfig>>().ok(operationConfigRedis.listGameExtraConfig());
    }

    /**
     * 新增或修改游戏额外奖励配置
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    @RequireRole(3)
    @RequestMapping("/addGameExtraConfig")
    public HttpResult<?> addGameExtraConfig(@RequestBody GameExtraConfig config) {
        logger.info("addGameExtraConfig configData={}", JSON.toJSONString(config));
        if (null == config || 0 >= config.getGameType() || config.getProbability() <= 0
                || config.getProbability() > 100 || config.getExtraTimes() > 10 || config.getExtraTimes() < 0) {
            return new HttpResult<>().error("参数错误");
        }
        if (!ObjectUtils.isEmpty(config.getStartTime()) && !ObjectUtils.isEmpty(config.getEndTime())) {
            try {
                LocalTime.parse(config.getStartTime());
                LocalTime.parse(config.getEndTime());
            } catch (Exception e) {
                return new HttpResult<>().error("开始结束时间参数错误");
            }
        }
        operationConfigRedis.addGameExtraConfig(config);
        return new HttpResult<>().ok();
    }

    /**
     * 获取本期幸运数字
     *
     * @return List 内部0,1,2为幸运数字，3为倍数
     */
    @RequireRole(3)
    @RequestMapping("/getLuckyNumber")
    public HttpResult<List<Integer>> getLuckyNumber() {
        return new HttpResult<List<Integer>>().ok(luckyDiceRedis.getLuckyNumber(true));
    }

    /**
     * 设置本期幸运数字
     *
     * @param dice1     第一个幸运骰子
     * @param dice2     第二个幸运骰子
     * @param dice3     第三个幸运骰子
     * @param times     幸运倍数
     * @param startTime 幸运数有效期，时间戳
     * @param endTime   幸运数有效期，时间戳
     */
    @RequireRole(3)
    @RequestMapping("/setLuckyNumber")
    public HttpResult<?> setLuckyNumber(@RequestParam int dice1, @RequestParam int dice2, @RequestParam int dice3,
                                        @RequestParam int times, @RequestParam int startTime, @RequestParam int endTime) {
        if (dice1 < 1 || dice1 > 6 || dice2 < 1 || dice2 > 6 || dice3 < 1 || dice3 > 6 || times < 1 || times > 300 || startTime < 0 || endTime < 0) {
            return new HttpResult<>().error("参数错误");
        }
        luckyDiceRedis.setLuckyNumber(Arrays.asList(dice1, dice2, dice3, times, startTime, endTime));
        return new HttpResult<>().ok();
    }
}
