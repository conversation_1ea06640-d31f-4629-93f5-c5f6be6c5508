package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.mongo.data.ActivityComponentTemplate;
import com.quhong.mysql.data.ActivityComponent;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.ActivityComponentService;
import com.quhong.operation.share.condition.ActivityComponentCondition;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 活动组件化接口
 */
@RestController
@RequestMapping(value = "/activityComponent", produces = MediaType.APPLICATION_JSON_VALUE)
public class ActivityComponentController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(ActivityComponentController.class);

    @Resource
    private ActivityComponentService activityComponentService;

    /**
     * 组件列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<ActivityComponent>> list(@RequestBody ActivityComponentCondition condition) {
        logger.info("get ActivityComponentCondition={}", JSONObject.toJSONString(condition));
        return HttpResult.getOk(activityComponentService.list(condition));
    }

    /**
     * 组件新增
     */
    @RequireRole
    @RequestMapping("/add")
    public HttpResult<?> addData(@RequestBody ActivityComponent dto) {
        logger.info("activityComponentDTO {}", JSONObject.toJSONString(dto));
        activityComponentService.addData(dto);
        return HttpResult.getOk();
    }

    /**
     * 组件更新
     */
    @RequireRole
    @RequestMapping("/update")
    public HttpResult<?> updateData(@RequestBody ActivityComponent dto) {
        logger.info("updateData {}", JSONObject.toJSONString(dto));
        activityComponentService.updateData(dto);
        return HttpResult.getOk();
    }

    /**
     * 组件活动模板列表
     */
    @RequireRole
    @RequestMapping("/templateList")
    public HttpResult<PageResultVO<ActivityComponentTemplate>> templateList(@RequestBody BaseCondition condition) {
        logger.info("get templateList={}", JSONObject.toJSONString(condition));
        return HttpResult.getOk(activityComponentService.templateList(condition));
    }

    /**
     * 新增组件活动
     */
    @RequireRole
    @RequestMapping("/addTemplate")
    public HttpResult<?> addTemplate(@RequestBody ActivityComponentTemplate dto) {
        logger.info("addTemplate {}", JSONObject.toJSONString(dto));
        activityComponentService.addTemplate(dto);
        return HttpResult.getOk();
    }

    /**
     * 更新组件活动
     */
    @RequireRole
    @RequestMapping("/updateTemplate")
    public HttpResult<?> updateTemplate(@RequestBody ActivityComponentTemplate dto) {
        logger.info("updateTemplate {}", JSONObject.toJSONString(dto));
        activityComponentService.updateTemplate(dto);
        return HttpResult.getOk();
    }


}
