package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.WhiteTestService;
import com.quhong.operation.share.condition.WhiteTestCondition;
import com.quhong.operation.share.vo.WhiteTestVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 测试白名单配置
 */

@RestController
@RequestMapping(value ="/whiteTest", produces = MediaType.APPLICATION_JSON_VALUE)
public class WhiteTestController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(WhiteTestController.class);

    @Resource
    private WhiteTestService whiteTestService;


    @RequireRole
    @RequestMapping("/list")
    public String getDataList(@RequestBody WhiteTestCondition condition) {
        logger.info("getDataList {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, whiteTestService.getDataList(condition));
    }


    @RequireRole
    @PostMapping("/addData")
    public String addData(@RequestBody WhiteTestVO dto) {
        try {
            logger.info("addData WhiteTestVO Data {}", JSONObject.toJSONString(dto));
            whiteTestService.addData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    @RequireRole
    @PostMapping("/updateData")
    public String updateData(@RequestBody WhiteTestVO dto) {
        try {
            logger.info("update WhiteTestVO Data {}", JSONObject.toJSONString(dto));
            whiteTestService.updateData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    @RequireRole
    @PostMapping("/deleteData")
    public String deleteData(@RequestBody WhiteTestVO dto) {
        try {
            logger.info("deleteData WhiteTestVO Data {}", JSONObject.toJSONString(dto));
            whiteTestService.deleteData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }
}
