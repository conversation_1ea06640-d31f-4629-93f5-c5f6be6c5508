package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.WebClient;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.AppThemeDao;
import com.quhong.mongo.dao.AppThemeV2Dao;
import com.quhong.mongo.data.AppThemeData;
import com.quhong.mongo.data.AppThemeV2Data;
import com.quhong.monitor.MonitorSender;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.AppThemeCondition;
import com.quhong.operation.share.dto.AppThemeConfigDTO;
import com.quhong.operation.share.dto.AppThemeDTO;
import com.quhong.operation.share.dto.AppThemeV2ConfigDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.AWSUploadUtils;
import com.quhong.operation.utils.Md5Utils;
import com.quhong.operation.utils.ZipUtil;
import com.quhong.redis.SourceDuplicateRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/appThemeV2")
public class AppThemeV2Controller extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AppThemeV2Controller.class);
    private static final String FILE_BUKET_PATH = "appTheme/";  // AWS目录名
    private static final String fileBasePath = "/opt";
    private final static String DIR_ANDROID = "android";
    private final static String DIR_IOS = "ios";
    private final static String DIR_IOS_X = "ios_x";
    private final static Pattern pattern = Pattern.compile("\\S*[?]\\S*");
    private final static Map<String, String> JSON_FILE_NAME = new HashMap<String, String>() {
        {
            put("light", "light.json");
            put("night", "night.json");
        }
    };

    @Resource
    private AppThemeV2Dao appThemeV2Dao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private SourceDuplicateRedis sourceDuplicateRedis;

    /**
     * 活动模板分页查询
     */
    @RequireRole(2)
    @RequestMapping("/page")
    public String selectPage(@RequestBody AppThemeCondition condition) {

        logger.info("AppTheme selectPage condition={}", JSON.toJSONString(condition));
        HttpResult<PageResultVO<AppThemeV2Data>> result = new HttpResult<>();
        PageResultVO<AppThemeV2Data> pageVO = new PageResultVO<>();

        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<AppThemeV2Data> appThemeV2DataList = appThemeV2Dao.selectPage(condition.getStatus(), condition.getSearch(), start, pageSize);
        List<AppThemeV2Data> dtoList = new ArrayList<>();
        for (AppThemeV2Data appThemeV2Data : appThemeV2DataList) {
            appThemeV2Data.setDocId(appThemeV2Data.get_id().toString());
            dtoList.add(appThemeV2Data);
        }
        pageVO.setList(dtoList);
        pageVO.setTotal(appThemeV2Dao.selectCount());
        return createResult(HttpCode.SUCCESS, pageVO);

    }

    /**
     * 保存主题
     */
    @RequireRole(2)
    @RequestMapping("/save")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody AppThemeV2Data template) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(template));
            if (StringUtils.isEmpty(template.getThemeName())) {
                return result.error("主题名不能为空");
            }
            List<AppThemeV2Data.ThemeConfig> themeConfigList = template.getThemeConfigList();
            Map<String, Integer> themeMap = new HashMap<>();
            if (themeConfigList != null && !themeConfigList.isEmpty()) {
                for (AppThemeV2Data.ThemeConfig config : themeConfigList) {
                    String duplicate = "" + config.getThemePlatform() + config.getThemeType();
                    if (themeMap.get(duplicate) != null) {
                        return result.error("主题类型重复");
                    }
                    themeMap.put(duplicate, 1);
                }
            }

            if (template.getStatus() == 1) {
                if (appThemeV2Dao.findValidData() != null) {
                    return result.error("存在有效的主题, 只能存在一个有效的主题, 请先将旧主题改为无效");
                }
            }

            appThemeV2Dao.save(template);
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "正式服-用户成功创建appTheme", "app主题名：" + template.getThemeName());
            }
        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 更新主题
     */
    @RequireRole(2)
    @RequestMapping("/update")
    public HttpResult updateTemplate(HttpServletRequest request, @RequestBody AppThemeV2Data dto) {
        HttpResult result = new HttpResult();
        try {

            if (StringUtils.isEmpty(dto.getDocId())) {
                logger.error("The activityId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("update templateV2 activityId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
            AppThemeV2Data template = appThemeV2Dao.findData(dto.getDocId());
            if (Objects.isNull(template)) {
                logger.error("AppThemeData templateV2 is empty. id={}", dto.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }


            if (dto.getStatus() == 1) {
                AppThemeV2Data themeData = appThemeV2Dao.findValidData();
                if (themeData != null && !themeData.get_id().toString().equals(dto.getDocId())) {
                    return result.error("存在有效的主题, 只能存在一个有效的主题, 请先将旧主题改为无效");
                }
            }

            Update update = new Update();
            update.set("startTime", dto.getStartTime());
            update.set("endTime", dto.getEndTime());
            update.set("themeName", dto.getThemeName());
            update.set("status", dto.getStatus());

            // android zip包设置
            if (!StringUtils.isEmpty(dto.getAndroidZipUrl())) {
                update.set("androidZipUrl", dto.getAndroidZipUrl().trim());
            }

            if (!StringUtils.isEmpty(dto.getAndroidZipMd5())) {
                update.set("androidZipMd5", dto.getAndroidZipMd5().trim());
            }

            // ios zip包设置
            if (!StringUtils.isEmpty(dto.getIosZipUrl())) {
                update.set("iosZipUrl", dto.getIosZipUrl().trim());
            }

            if (!StringUtils.isEmpty(dto.getIosZipMd5())) {
                update.set("iosZipMd5", dto.getIosZipMd5().trim());
            }

            // ios x zip包设置
            if (!StringUtils.isEmpty(dto.getIosXZipUrl())) {
                update.set("iosXZipUrl", dto.getIosXZipUrl().trim());
            }
            if (!StringUtils.isEmpty(dto.getIosXZipMd5())) {
                update.set("iosXZipMd5", dto.getIosXZipMd5().trim());
            }

            if (!CollectionUtils.isEmpty(dto.getThemeConfigList())) {
                update.set("themeConfigList", dto.getThemeConfigList());
            }
            update.set("mtime", DateHelper.getNowSeconds());
            appThemeV2Dao.updateData(template, update);
        } catch (Exception e) {
            logger.error("update template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }


    /**
     * 增加单个主题类型
     */
    @RequireRole(2)
    @RequestMapping("/addThemeConfig")
    public HttpResult addThemeConfig(HttpServletRequest request, @RequestBody AppThemeV2ConfigDTO dto) {
        HttpResult result = new HttpResult();
        try {

            if (StringUtils.isEmpty(dto.getDocId()) || StringUtils.isEmpty(dto.getThemeType())) {
                logger.error("The getDocId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("addThemeConfig docId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
            AppThemeV2Data template = appThemeV2Dao.findData(dto.getDocId());
            if (Objects.isNull(template)) {
                logger.error("AppThemeData template is empty. id={}", dto.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            List<AppThemeV2Data.ThemeConfig> themeConfigList = template.getThemeConfigList();
            Map<String, Integer> themeMap = new HashMap<>();
            if (themeConfigList != null && !themeConfigList.isEmpty()) {
                for (AppThemeV2Data.ThemeConfig config : themeConfigList) {
                    String duplicate = "" + config.getThemePlatform() + config.getThemeType();
                    themeMap.put(duplicate, 1);
                }
            } else {
                themeConfigList = new ArrayList<>();
            }

            String newDuplicate = "" + dto.getThemePlatform() + dto.getThemeType();
            if (themeMap.get(newDuplicate) != null) {
                return result.error("主题类型重复");
            }

            AppThemeV2Data.ThemeConfig tempConfig = new AppThemeV2Data.ThemeConfig();
            BeanUtils.copyProperties(dto, tempConfig);
            themeConfigList.add(tempConfig);

            Update update = new Update();
            update.set("themeConfigList", themeConfigList);
            update.set("mtime", DateHelper.getNowSeconds());
            appThemeV2Dao.updateData(template, update);

        } catch (Exception e) {
            logger.error("addThemeConfig error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }


    /**
     * 修改单个主题类型
     */
    @RequireRole(2)
    @RequestMapping("/updateThemeConfig")
    public HttpResult updateThemeConfig(HttpServletRequest request, @RequestBody AppThemeV2ConfigDTO dto) {
        HttpResult result = new HttpResult();
        try {

            if (StringUtils.isEmpty(dto.getDocId()) || StringUtils.isEmpty(dto.getThemeType())) {
                logger.error("The getDocId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("addThemeConfig docId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
            AppThemeV2Data template = appThemeV2Dao.findData(dto.getDocId());
            if (Objects.isNull(template)) {
                logger.error("AppThemeData template is empty. id={}", dto.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            String dtoKey = "" + dto.getThemePlatform() + dto.getThemeType();
            List<AppThemeV2Data.ThemeConfig> themeConfigList = template.getThemeConfigList();
            if (themeConfigList != null && !themeConfigList.isEmpty()) {
                for (AppThemeV2Data.ThemeConfig config : themeConfigList) {
                    String dbKey = "" + config.getThemePlatform() + config.getThemeType();
                    if (dbKey.equals(dtoKey)) {
                        BeanUtils.copyProperties(dto, config);
                    }
                }
            } else {
                return result.error("param is error");
            }

            Update update = new Update();
            update.set("themeConfigList", themeConfigList);
            update.set("mtime", DateHelper.getNowSeconds());
            appThemeV2Dao.updateData(template, update);
        } catch (Exception e) {
            logger.error("updateThemeConfig error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 删除单个主题类型
     */
    @RequireRole(2)
    @RequestMapping("/deleteThemeConfig")
    public HttpResult deleteThemeConfig(HttpServletRequest request, @RequestBody AppThemeV2ConfigDTO dto) {
        HttpResult result = new HttpResult();
        try {

            if (StringUtils.isEmpty(dto.getDocId()) || StringUtils.isEmpty(dto.getThemeType())) {
                logger.error("The getDocId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("addThemeConfig docId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
            AppThemeV2Data template = appThemeV2Dao.findData(dto.getDocId());
            if (Objects.isNull(template)) {
                logger.error("AppThemeData template is empty. id={}", dto.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            String dtoKey = "" + dto.getThemePlatform() + dto.getThemeType();
            List<AppThemeV2Data.ThemeConfig> themeConfigList = template.getThemeConfigList();

            Iterator<AppThemeV2Data.ThemeConfig> iterator = themeConfigList.iterator();
            while (iterator.hasNext()) {
                AppThemeV2Data.ThemeConfig next = iterator.next();
                String dbKey = "" + next.getThemePlatform() + next.getThemeType();
                if (dbKey.equals(dtoKey)) {
                    iterator.remove();
                }
            }

            Update update = new Update();
            update.set("themeConfigList", themeConfigList);
            update.set("mtime", DateHelper.getNowSeconds());
            appThemeV2Dao.updateData(template, update);
        } catch (Exception e) {
            logger.error("updateThemeV2Config error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }


    /**
     * 生成zipUrl包
     */
    private static String parseSuffix(String url) {

        Matcher matcher = pattern.matcher(url);
        String[] spUrl = url.split("/");
        int len = spUrl.length;
        String endUrl = spUrl[len - 1];
        if (matcher.find()) {
            String[] spEndUrl = endUrl.split("\\?");
            return spEndUrl[0].split("\\.")[1];
        }
        return endUrl;
    }


    public String loadFileFromUrl(String rootDir, String fileUrl) {

        if (StringUtils.isEmpty(fileUrl)) {
            return "";
        }

        WebClient webClient = new WebClient();

        String iconDir = rootDir + "icon/";
        OutputStream outputStream = null;
        try {
            Path path = Paths.get(iconDir);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
            String fileName = parseSuffix(fileUrl);
            String filePath = iconDir + fileName;
            File file = new File(filePath);
            if (!file.exists()) {
                byte[] resp = webClient.getBytes(fileUrl);
                outputStream = new BufferedOutputStream(Files.newOutputStream(Paths.get(filePath)));
                outputStream.write(resp);
                outputStream.close();
            }
            return fileName;

        } catch (Exception e) {
            logger.error("loadFileFromUrl, msg={}", e.getMessage(), e);
        } finally {
            try {
                outputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return "";
    }


    private void fileOutPutStreamContent(String rootDir, String fileName, String content) {
        String filePath = rootDir + fileName;
        File file;
        FileOutputStream fos;
        try {
            file = new File(filePath);
            if (!file.exists()) {
                boolean newFile = file.createNewFile();
                if (!newFile) {
                    logger.info("fail to create new file, please check!");
                    return;
                }
            }
            byte[] bytes = content.getBytes();
            int b = bytes.length;   // 是字节的长度，不是字符串的长度
            fos = new FileOutputStream(file); // 如果已存在，以覆盖的方式写文件
            // fos = new FileOutputStream(txt, true); // 如果已存在，以追加的方式写文件
            fos.write(bytes, 0, b); // 写指定长度的内容
            // fos.write(bytes); // 写全文
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private boolean themeTypeZip(String rootDir, AppThemeV2Data.ThemeConfig themeConfig) {

        // 1、顶部导航栏配置
        themeConfig.getTopNavConfig().setTopIcon(loadFileFromUrl(rootDir, themeConfig.getTopNavConfig().getTopIcon()));
        themeConfig.getTopNavConfig().setTopIcon2x(loadFileFromUrl(rootDir, themeConfig.getTopNavConfig().getTopIcon2x()));
        // 2、底部Tab栏配置图片转换
        themeConfig.getBottomTabbar().setBackgroundIcon(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getBackgroundIcon()));
        themeConfig.getBottomTabbar().setBackgroundIcon2x(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getBackgroundIcon2x()));
        themeConfig.getBottomTabbar().setBackgroundNavIcon(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getBackgroundNavIcon()));
        themeConfig.getBottomTabbar().setNavSelectedBgIcon(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getNavSelectedBgIcon()));
        themeConfig.getBottomTabbar().setNavSelectedBgIcon2x(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getNavSelectedBgIcon2x()));
        List<AppThemeV2Data.NavBarConfig> navBarConfigList = themeConfig.getBottomTabbar().getNavList();
        for (AppThemeV2Data.NavBarConfig navBarConfig : navBarConfigList) {
            navBarConfig.setNavIcon(loadFileFromUrl(rootDir, navBarConfig.getNavIcon()));
            navBarConfig.setNavIcon2x(loadFileFromUrl(rootDir, navBarConfig.getNavIcon2x()));
            navBarConfig.setNavSelectedIcon(loadFileFromUrl(rootDir, navBarConfig.getNavSelectedIcon()));
            navBarConfig.setNavSelectedIcon2x(loadFileFromUrl(rootDir, navBarConfig.getNavSelectedIcon2x()));
            navBarConfig.setNavIconJson(loadFileFromUrl(rootDir, navBarConfig.getNavIconJson()));
            navBarConfig.setNavSelectedIconJson(loadFileFromUrl(rootDir, navBarConfig.getNavSelectedIconJson()));
        }
        themeConfig.getBottomTabbar().setNavList(navBarConfigList);

        // 3、home栏细节图片转换
        themeConfig.getRoom().setSearchIcon(loadFileFromUrl(rootDir, themeConfig.getRoom().getSearchIcon()));
        themeConfig.getRoom().setSearchIcon2x(loadFileFromUrl(rootDir, themeConfig.getRoom().getSearchIcon2x()));
        themeConfig.getRoom().setCreateRoomIcon(loadFileFromUrl(rootDir, themeConfig.getRoom().getCreateRoomIcon()));
        themeConfig.getRoom().setCreateRoomIcon2x(loadFileFromUrl(rootDir, themeConfig.getRoom().getCreateRoomIcon2x()));

        // 4、Moment栏细节配置
        themeConfig.getMoment().setTopicIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getTopicIcon()));
        themeConfig.getMoment().setTopicIcon2x(loadFileFromUrl(rootDir, themeConfig.getMoment().getTopicIcon2x()));
        themeConfig.getMoment().setNoticeIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getNoticeIcon()));
        themeConfig.getMoment().setNoticeIcon2x(loadFileFromUrl(rootDir, themeConfig.getMoment().getNoticeIcon2x()));
        themeConfig.getMoment().setPostIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getPostIcon()));
        themeConfig.getMoment().setPostIcon2x(loadFileFromUrl(rootDir, themeConfig.getMoment().getPostIcon2x()));
        themeConfig.getMoment().setPenIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getPenIcon()));
        themeConfig.getMoment().setPenIcon2x(loadFileFromUrl(rootDir, themeConfig.getMoment().getPenIcon2x()));

        List<String> newFloatScreen = new ArrayList<>();
        for (String floatUrl : themeConfig.getFloatScreen()) {
            newFloatScreen.add(loadFileFromUrl(rootDir, floatUrl));
        }

        themeConfig.setFloatScreen(newFloatScreen);

        String themeConfigString = JSONObject.toJSONString(themeConfig);
        String jsonFileName = JSON_FILE_NAME.get(themeConfig.getThemeType());
        fileOutPutStreamContent(rootDir, jsonFileName, themeConfigString);
        return true;
    }

    private void deleteFolder(File file) {
        for (File subFile : file.listFiles()) {
            if (subFile.isDirectory()) {
                deleteFolder(subFile);
            } else {
                subFile.delete();
            }
        }
        file.delete();
    }


    private void asyncGenZipUrl(String docId) {
        AppThemeV2Data themeData = appThemeV2Dao.findData(docId);
        if (themeData == null) {
            return;
        }

        boolean androidFlag = false;
        String androidRootDir = fileBasePath + "/" + DIR_ANDROID + "/";

        boolean iosFlag = false;
        String iosRootDir = fileBasePath + "/" + DIR_IOS + "/";

        boolean iosXFlag = false;
        String iosXRootDir = fileBasePath + "/" + DIR_IOS_X + "/";

        for (AppThemeV2Data.ThemeConfig themeConfig : themeData.getThemeConfigList()) {
            if (themeConfig.getThemePlatform() == 0) {
                androidFlag = themeTypeZip(androidRootDir, themeConfig);
            } else if (themeConfig.getThemePlatform() == 1) {
                iosFlag = themeTypeZip(iosRootDir, themeConfig);
            } else if (themeConfig.getThemePlatform() == 2) {
                iosXFlag = themeTypeZip(iosXRootDir, themeConfig);
            }
        }
        Update update = new Update();
        if (androidFlag) {
            String androidZipPath = ZipUtil.zipFileWithPath(androidRootDir, DIR_ANDROID + "_" + DateHelper.getNowSeconds() + ".zip");
            String androidUrl = ZipUtil.uploadZipFile(androidZipPath, FILE_BUKET_PATH);
            Map<String, Object> androidZipFileMeta = ZipUtil.calculateZipFileMD5(androidZipPath);

            update.set("androidZipUrl", androidUrl);
            update.set("androidZipMd5", androidZipFileMeta.get("fileMd5"));
            deleteFolder(new File(androidRootDir));
        }

        if (iosFlag) {
            String iosZipPath = ZipUtil.zipFileWithPath(iosRootDir, DIR_IOS + "_" + DateHelper.getNowSeconds() + ".zip");
            String iosUrl = ZipUtil.uploadZipFile(iosZipPath, FILE_BUKET_PATH);
            Map<String, Object> iosZipFileMeta = ZipUtil.calculateZipFileMD5(iosZipPath);
            update.set("iosZipUrl", iosUrl);
            update.set("iosZipMd5", iosZipFileMeta.get("fileMd5"));
            deleteFolder(new File(iosRootDir));
        }

        if (iosXFlag) {
            String iosXZipPath = ZipUtil.zipFileWithPath(iosXRootDir, DIR_IOS_X + "_" + DateHelper.getNowSeconds() + ".zip");
            String iosXUrl = ZipUtil.uploadZipFile(iosXZipPath, FILE_BUKET_PATH);
            Map<String, Object> iosXZipFileMeta = ZipUtil.calculateZipFileMD5(iosXZipPath);
            update.set("iosXZipUrl", iosXUrl);
            update.set("iosXZipMd5", iosXZipFileMeta.get("fileMd5"));
            deleteFolder(new File(iosXRootDir));
        }

        update.set("zipTime", DateHelper.getNowSeconds());
        appThemeV2Dao.updateData(themeData, update);

    }


    @RequireRole(2)
    @RequestMapping("/genZipUrl")
    public HttpResult genZipUrl(@RequestBody AppThemeCondition condition) {
        HttpResult result = new HttpResult();
        try {

            if (ServerConfig.isProduct()) {
                return result.error("正式服不能直接生成, 请在测试服测好再更新zipUrl");
            }

            if (StringUtils.isEmpty(condition.getDocId())) {
                logger.error("The getDocId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("genZipUrl docId={} data={}", condition.getDocId(), JSON.toJSONString(condition));
            AppThemeV2Data themeData = appThemeV2Dao.findData(condition.getDocId());
            if (Objects.isNull(themeData)) {
                logger.error("AppThemeData template is empty. id={}", condition.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }


            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    asyncGenZipUrl(condition.getDocId());
                }
            });


        } catch (Exception e) {
            logger.error("genZipUrl error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        try {
            //
            String fileMd5 = Md5Utils.getISMD5String(file.getInputStream());
            String fileUrl = sourceDuplicateRedis.getSourceStrData(fileMd5);
            if (!StringUtils.isEmpty(fileUrl)) {
                return fileUrl;
            }

            fileUrl = AWSUploadUtils.upload(file, FILE_BUKET_PATH);
            sourceDuplicateRedis.setSourceStrData(fileMd5, fileUrl);
            return fileUrl;
        } catch (IOException e) {
            logger.error("uploading failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }


}
