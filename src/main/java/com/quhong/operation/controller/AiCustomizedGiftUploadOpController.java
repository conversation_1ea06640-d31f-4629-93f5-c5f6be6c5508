package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.AiCustomizedGiftUploadOpService;
import com.quhong.operation.share.dto.AiCustomizedGiftUploadDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * AI定制礼物上传管理
 */
@RestController
@RequestMapping(value = "/aiCustomizedGiftUpload", produces = MediaType.APPLICATION_JSON_VALUE)
public class AiCustomizedGiftUploadOpController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AiCustomizedGiftUploadOpController.class);

    @Resource
    private AiCustomizedGiftUploadOpService aiCustomizedGiftUploadOpService;

    /**
     * 分页查询AI定制礼物上传记录
     */
    @RequireRole
    @RequestMapping("/list")
    public String list(@RequestBody AiCustomizedGiftUploadDTO dto) {
        return createResult(HttpCode.SUCCESS, aiCustomizedGiftUploadOpService.aiCustomizedGiftUploadList(dto));
    }

    /**
     * 新增AI定制礼物上传记录
     */
    @RequireRole
    @RequestMapping("/add")
    public String add(@RequestBody AiCustomizedGiftUploadDTO dto) {
        aiCustomizedGiftUploadOpService.aiCustomizedGiftUploadAdd(dto);
        return createResult(HttpCode.SUCCESS, new Object());
    }

    /**
     * 更新AI定制礼物上传记录状态
     */
    @RequireRole
    @RequestMapping("/update")
    public String update(@RequestParam String uid, @RequestBody AiCustomizedGiftUploadDTO dto) {
        if (!CollectionUtils.isEmpty(dto.getRidList())) {
            aiCustomizedGiftUploadOpService.aiCustomizedGiftUploadUpdateAll(uid,dto);
        }else {
            aiCustomizedGiftUploadOpService.aiCustomizedGiftUploadUpdate(uid, dto);
        }
        return createResult(HttpCode.SUCCESS, new Object());
    }

    /**
     * 根据ID查询单条记录
     */
    @RequireRole
    @RequestMapping("/detail")
    public String detail(@RequestParam Integer rid) {
        return createResult(HttpCode.SUCCESS, aiCustomizedGiftUploadOpService.getAiCustomizedGiftUploadById(rid));
    }
}
