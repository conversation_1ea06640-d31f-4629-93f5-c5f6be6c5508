package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.operation.common.HttpResult;
import org.apache.ibatis.ognl.ASTList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/sys_config")
public class SystemConfigController {

    private static final Logger logger = LoggerFactory.getLogger(SystemConfigController.class);

    @GetMapping("/versioncode_list")
    public HttpResult<List<JSONObject>> listVersionCode() {
        HttpResult<List<JSONObject>> httpResult = new HttpResult<>();
        try {
            logger.info("begin get versioncode list.");
            List<JSONObject> list = new ArrayList<>();
            //插入安卓814版本号
            JSONObject android814 = new JSONObject();
            android814.put("versionName", "YouStar V8.14.3.release");
            android814.put("versionCode", Arrays.asList("330", "331"));
            //插入安卓815版本号
            JSONObject Android815 = new JSONObject();
            Android815.put("versionName", "YouStar V8.15.3.release");
            Android815.put("versionCode", Arrays.asList("332", "333", "334", "335", "336", "337", "338", "339", "340"));
            //插入ios814版本号
            JSONObject ios814 = new JSONObject();
            ios814.put("versionName", "YouStar V7.3.8");
            ios814.put("versionCode", Arrays.asList("737", "738"));
            //插入ios815版本号
            JSONObject ios815 = new JSONObject();
            ios815.put("versionName", "YouStar V7.3.9");
            ios815.put("versionCode", Arrays.asList("739", "740"));
            //插入ios816版本号
            JSONObject ios816 = new JSONObject();
            ios816.put("versionName", "YouStar V8.4.1");
            ios816.put("versionCode", Arrays.asList("741"));
            //初始化list
            list.add(android814);
            list.add(Android815);
            list.add(ios814);
            list.add(ios815);
            list.add(ios816);
            return httpResult.ok(list);
        } catch (Exception e) {
            logger.error("get versioncode list error. {}", e.getMessage(), e);
        }
        return httpResult.error(HttpCode.SERVER_ERROR.getMsg());
    }

}
