package com.quhong.operation.controller;

import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tree.TreeTask;
import com.quhong.core.utils.DateHelper;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.ChannelPromotionServer;
import com.quhong.operation.share.dto.ChannelPromotionAddDTO;
import com.quhong.operation.share.dto.ChannelPromotionListDTO;
import com.quhong.operation.share.dto.ChannelPromotionUpdateDTO;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping("/channelPromotion")
public class ChannelPromotionController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ChannelPromotionController.class);

    @Autowired
    private ChannelPromotionServer channelPromotionServer;

    private Map<String, TaskQueue> cacheMap = new ConcurrentHashMap<>();

    private static final Comparator<ChannelPromotionDayVO> COMPARATOR = new Comparator<ChannelPromotionDayVO>() {
        //根据通话时间降序，使得前端数据更具有可视化
        @Override
        public int compare(ChannelPromotionDayVO o1, ChannelPromotionDayVO o2) {
            DayTimeData o1Time = DateHelper.ARABIAN.getContinuesDays(o1.getDate());
            DayTimeData o2Time = DateHelper.ARABIAN.getContinuesDays(o2.getDate());
            return - o1Time.getTime() + o2Time.getTime();
        }
    };

    /**
     * 新增渠道
     * @param addDTO
     * @return
     */
    @RequireRole
    @PostMapping("/add")
    public HttpResult<String> addChannel(HttpServletRequest request,ChannelPromotionAddDTO addDTO){
        HttpResult<String> result = new HttpResult<>();
        try {
            String uid = request.getParameter("uid");
            logger.info("begin add promotion channel. param = {} uid = {}",addDTO.toString(),uid);
            //参数校验
            String channelName = addDTO.getChannelName();
            Integer sourceId = addDTO.getSourceId();
            Integer status = addDTO.getStatus();
            if(StringUtils.isEmpty(channelName) || sourceId == null || status == null){
                logger.error("add promotion channel param error. param = {}",addDTO.toString());
                return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
            }
            //业务调用
            ApiResult<String> apiResult = channelPromotionServer.addChannel(addDTO,uid);
            if(apiResult.isOK()){
                return result.ok();
            }
            return result.error(apiResult.getMsg());
        } catch (Exception e) {
            logger.error("add promotion channel error. msg = {}",e.getMessage(),e);
        }
        return result.error(HttpCode.SERVER_ERROR.getCode(), HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 获取所有渠道来源
     * @return
     */
    @RequireRole
    @GetMapping("/source/list")
    public HttpResult<List<ChannelSourceVO>> listAllSource(){
        HttpResult<List<ChannelSourceVO>> result = new HttpResult<>();
        try {
            logger.info("begin get channel source list");
            //业务调用
            ApiResult<List<ChannelSourceVO>> apiResult = channelPromotionServer.listSource();
            if(apiResult.isOK()){
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getMsg());
        } catch (Exception e) {
            logger.error("list channel source error. msg = {}",e.getMessage(),e);
        }
        return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
    }

    /**
     * 获取所有渠道id
     * @return
     */
    @RequireRole
    @GetMapping("/channelId/list")
    public HttpResult<List<ChannelIdVO>> listAllChannelId(){
        HttpResult<List<ChannelIdVO>> result = new HttpResult<>();
        try {
            logger.info("begin get channel id list");
            //业务调用
            ApiResult<List<ChannelIdVO>> apiResult = channelPromotionServer.listChannelId();
            if(apiResult.isOK()){
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getMsg());
        } catch (Exception e) {
            logger.error("list channel id data error. msg = {}",e.getMessage(),e);
        }
        return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
    }

    /**
     * 获取渠道推广列表
     * @return
     */
    @RequireRole
    @GetMapping("/list")
    public HttpResult<PageResultVO> listChannel(String opUser,Integer status,String start,String end,Integer page){
        HttpResult<PageResultVO> result = new HttpResult<>();
        try {
            logger.info("begin get channel promotion list. opUser = {} status = {} start = {} end = {} page = {} ",opUser,status,start,end,page);
            //参数校验
            if(StringUtils.isEmpty(opUser) || status == null || StringUtils.isEmpty(start) || StringUtils.isEmpty(end)){
                logger.error("get channel promotion list param error. opUser = {} status = {} start = {} end = {} page = {}",opUser,status,start,end,page);
                return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
            }
            if(page == null){
                page = 1;
            }
            ChannelPromotionListDTO listDTO = new ChannelPromotionListDTO();
            listDTO.setOpUser(opUser);
            listDTO.setStatus(status);
            listDTO.setStart(start);
            listDTO.setEnd(end);
            listDTO.setPage(page);
            //业务调用
            ApiResult<PageResultVO<ChannelPromotionVO>> apiresult = channelPromotionServer.listChannel(listDTO);
            if(apiresult.isOK()){
                return result.ok(apiresult.getData());
            }
            return result.error(apiresult.getMsg());
        } catch (Exception e) {
            logger.error("list channel promotion error. msg = {}",e.getMessage(),e);
        }
        return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
    }

    /**
     * 更新渠道
     * @param updateDTO
     * @return
     */
    @RequireRole
    @PostMapping("/update")
    public HttpResult<String> updateChannel(HttpServletRequest request,ChannelPromotionUpdateDTO updateDTO){
        HttpResult<String> result = new HttpResult<>();
        try {
            String uid = request.getParameter("uid");
            logger.info("begin update channel promotion data. param = {} uid = {}",updateDTO.toString(),uid);
            //参数校验
            Integer channelId = updateDTO.getChannelId();
            Integer sourceId = updateDTO.getSourceId();
            if(channelId == null || sourceId == null){
                logger.error("update channel promotion param error. param = {}",updateDTO.toString());
                return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
            }
            //业务调用
            ApiResult<String> apiResult = channelPromotionServer.updateChannel(updateDTO, uid);
            if(apiResult.isOK()){
                return result.ok();
            }
            return result.error(apiResult.getMsg());
        } catch (Exception e) {
            logger.error("update channel promotion error. msg = {} ",e.getMessage(),e);
        }
        return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
    }

    /**
     * 删除渠道
     * @param request
     * @param channelId
     * @return
     */
    @RequireRole
    @PostMapping("/remove")
    public HttpResult<String> removeChannel(HttpServletRequest request,Integer channelId){
        HttpResult<String> result = new HttpResult<>();
        try {
            String uid = request.getParameter("uid");
            logger.info("begin delete channel promotion data. channelId = {} uid = {}",channelId,uid);
            //参数校验
            if(channelId == null){
                logger.error("delete channel promotion param error. channelId = {} uid = {}",channelId,uid);
                return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
            }
            //业务调用
            ApiResult<String> apiResult = channelPromotionServer.removeChannel(channelId, uid);
            if(apiResult.isOK()){
                return result.ok();
            }
            return result.error(apiResult.getMsg());
        } catch (Exception e) {
            logger.error("delete channel promotion error. msg = {} ",e.getMessage(),e);
        }
        return result.error(HttpCode.PARAM_ERROR.getCode(),HttpCode.PARAM_ERROR.getMsg());
    }

    /**
     * 获取渠道实时数据
     * @param channelId 渠道id
     * @param start 开始时间戳
     * @param end 结束时间戳
     * @param os 平台
     * @return
     */
    @RequireRole
    @GetMapping("/dayData")
    public DeferredResult<String> listDayData(Integer sourceId,String channelId,String start,String end,Integer os){
        DeferredResult<String> result = new DeferredResult<>();
        try {
            logger.info("begin get channel promotion day data. sourceId = {} channelId = {} start = {} end = {} os = {}",sourceId,channelId,start,end,os);
            //参数校验
            if(sourceId == null || StringUtils.isEmpty(channelId) || StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || os == null){
                logger.error("get channel promotion day data error.sourceId = {} channelId = {} start = {} end = {} os = {}",sourceId,channelId,start,end,os);
                result.setResult(createResult(HttpCode.PARAM_ERROR,null));
                return result;
            }
            //业务调用
            TreeTask parentTask = new TreeTask() {
                @Override
                protected void doComplete(Object data) {
                    List<ChannelPromotionDayVO> retList = (List<ChannelPromotionDayVO>)getData();
                    retList.sort(COMPARATOR);
                    result.setResult(createResult(HttpCode.SUCCESS,retList));
                }
                @Override
                protected void execute() {
                    //do nothing
                }
            };
            List<ChannelPromotionDayVO> retList = Collections.synchronizedList(new ArrayList<>());
            parentTask.setData(retList);
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            for (DayTimeData timeData : dayTimeList) {
                TreeTask subTask = new TreeTask(parentTask) {
                    @Override
                    protected void execute() {
                        int tomorrowStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                        if (timeData.getTime() >= tomorrowStartTime) {
                            ChannelPromotionDayVO statData = new ChannelPromotionDayVO();
                            statData.setDate(timeData.getDate());
                            statData.setChannelPayMoney("0");
                            retList.add(statData);
                        } else {
                            ChannelPromotionDayVO statData = channelPromotionServer.listChannelDayData(sourceId,channelId, timeData, os);
                            retList.add(statData);
                        }
                    }

                    @Override
                    protected void doComplete(Object data) {

                    }
                };
                addTask(timeData, channelId, subTask,os);
            }
            parentTask.start();
            return result;
        } catch (Exception e) {
            logger.error("get channel promotion day data error. msg = {}",e.getMessage(),e);
        }
        result.setResult(createResult(HttpCode.SERVER_ERROR,null));
        return result;
    }

    /**
     * 下载每日日活数据
     * @param channelId
     * @param start
     * @param end
     * @param os
     */
    @RequireRole
    @GetMapping("/dayData/download")
    public void downloadDayData(HttpServletResponse response,Integer sourceId,String channelId, String start, String end, Integer os){
        try {
            logger.info("begin download channel promotion day data. sourceId = {} channelId = {} start = {} end = {} os = {}",sourceId,channelId,start,end,os);
            //参数校验
            if(sourceId == null || StringUtils.isEmpty(channelId) || StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || os == null){
                logger.error("get channel promotion day data error.sourceId = {} channelId = {} start = {} end = {} os = {}",sourceId,channelId,start,end,os);
                String result = createResult(HttpCode.PARAM_ERROR, null);
                response.addHeader("Content-Type", "text/html; charset=utf-8");
                response.getOutputStream().write(result.getBytes(StandardCharsets.UTF_8));
                return;
            }
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            List<ChannelPromotionDayVO> retList = new ArrayList<>();
            for (DayTimeData dayTimeData : dayTimeList) {
                int tomorrowStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                if (dayTimeData.getTime() >= tomorrowStartTime) {
                    ChannelPromotionDayVO statData = new ChannelPromotionDayVO();
                    statData.setDate(dayTimeData.getDate());
                    statData.setChannelPayMoney("0");
                    retList.add(statData);
                } else {
                    ChannelPromotionDayVO statData = channelPromotionServer.listChannelDayData(sourceId,channelId, dayTimeData, os);
                    retList.add(statData);
                }
            }
            //下载报表
            ExcelUtils.exportExcel(response, retList, ChannelPromotionDayVO.class, "channel_promotion_day_info", "推广渠道每日实时数据");
            logger.info("finish begin download channel promotion day data report. ");
        } catch (IOException e) {
            logger.error("download channel promotion day data error. msg = {}",e.getMessage(),e);
        }
    }

    private void addTask(DayTimeData timeData, String channelId, TreeTask subTask,int os){
        String key = getKey(timeData, channelId,os);
        TaskQueue queue = cacheMap.get(key);
        if(queue == null){
            synchronized (this){
                queue = cacheMap.get(key);
                if(queue == null){
                    queue = new TaskQueue();
                    cacheMap.put(key, queue);
                }
            }
        }
        queue.add(subTask);
    }

    private String getKey(DayTimeData dayTimeData, String channelId,int os){
        return dayTimeData.getDate() + "_" + channelId + "_" + os;
    }

}
