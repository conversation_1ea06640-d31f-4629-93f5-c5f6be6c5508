package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.NationalDayV2Dao;
import com.quhong.mongo.data.NationalDayV2Data;
import com.quhong.monitor.MonitorSender;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.ActivityTemplateCondition;
import com.quhong.operation.share.dto.NationalDayV2DTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.NationalDayV2Redis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/nationalDayV2")
public class NationalDayV2Controller extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(NationalDayV2Controller.class);
    private static final Set<String> SUPPORT_SET = new HashSet<>(Arrays.asList("gift", "mic", "buddle", "ride",
            "ripple", "diamond", "badge", "float_screen", "heart", "background", "honor_title", "entry_effect", "thanks"));
    // wenmiaofang、yangml、yufengxia
    private static final Set<String> ADMIN_SET = new HashSet<>(Arrays.asList("5dba92651e23cd00c80b32bc", "61b1fa404cf5f82dff19a63e", "62b9780a1fb34e1c3520cb90"));

    @Value("${online:true}")
    private boolean online;


    @Resource
    private NationalDayV2Dao nationalDayV2Dao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private NationalDayV2Redis nationalDayV2Redis;
    @Resource
    private MonitorSender monitorSender;

    /**
     * 保存活动模板
     */
    @RequireRole(2)
    @RequestMapping("/save_template")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody NationalDayV2Data template) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(template));
            // String uid = request.getParameter("uid");
            // if (online && (!ADMIN_SET.contains(uid))) {
            //     logger.error("save_template no right to operate uid={}", uid);
            //     return result.error("您无操作权限，如有需求请联系技术人员!");
            // }
            // Map<String, LuckyLotteryActivity.RewardConfigDetail> rewardConfigMap = new HashMap<>();

                if (null == template.getGiftId()
                        || null == template.getAdvanceConfig()
                        || null == template.getStartTime()
                        || null == template.getEndTime()
                        || null == template.getAcNameAr()
                        || null == template.getPoolSize()
                        || null == template.getAcNameEn()) {
                    return result.error(ApiCode.PARAM_ERROR.getMsg());
                }

                if (online && template.getPoolSize() < 100) {
                    return result.error("抽奖次数配置错误");
                }

                if (template.getGiftId() <= 0) {
                    return result.error("礼物id配置错误");
                }

                for (NationalDayV2Data.DrawRewardConfig reward : template.getDrawRewardConfigList()) {
                    if (!SUPPORT_SET.contains(reward.getRewardType())) {
                        return result.error("不支持的礼物资源");
                    }

                    if (ResourceConstant.ONCE_AGAIN.equals(reward.getRewardType())
                            || ResourceConstant.THANKS.equals(reward.getRewardType())) {
                        continue;
                    }

                    if (ResourceConstant.DIAMOND.equals(reward.getRewardType())
                            || ResourceConstant.HEART.equals(reward.getRewardType())) {
                        if (null == reward.getRewardNum()) {
                            return result.error("钻石/心心数量不能为空");
                        }
                    } else {
                        if (null == reward.getSourceId()) {
                            return result.error("资源id不能为空");
                        }
                        if (null == reward.getRewardTime()) {
                            return result.error("资源时长不能为空");
                        }
                    }
                }

            if (template.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }
            if (template.getTemplateType() == NationalDayV2Dao.NATIONAL_DAY_TEMPLATE_2023) {
                template.setAcUrl(ServerConfig.isProduct() ? "https://static.youstar.live/national_2023/" : "https://test2.qmovies.tv/national_2023/");
            } else if (template.getTemplateType() == NationalDayV2Dao.NATIONAL_DAY_TEMPLATE_2024) {
                template.setAcUrl(ServerConfig.isProduct() ? "https://static.youstar.live/national_2024/" : "https://test2.qmovies.tv/national_2024/");
            }
            nationalDayV2Dao.save(template);
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "正式服-用户成功创建国庆节活动", "活动名：" + template.getAcNameEn());
            }

        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 更新活动模板
     */
    @RequireRole(2)
    @RequestMapping("/update_template")
    public HttpResult updateTemplate(HttpServletRequest request, @RequestBody NationalDayV2DTO dto) {
        HttpResult result = new HttpResult();
        try {
            // String uid = request.getParameter("uid");
            // if (online && (!ADMIN_SET.contains(uid))) {
            //     logger.error("save_template no right to operate uid={}", uid);
            //     return result.error("您无操作权限，如有需求请联系技术人员!");
            // }
            if (Objects.isNull(dto.getActivityId())) {
                logger.error("The activityId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            if (null == dto.getActivityId() || null == dto.getStartTime()
                    || null == dto.getEndTime() || null == dto.getAcNameAr()
                    || null == dto.getAcNameEn()
                    || null == dto.getGiftId()
                    || null == dto.getPoolSize()
                    || null == dto.getRewardConfig()
                    || null == dto.getAdvanceConfig()
                    || null == dto.getDrawRewardConfigList()) {
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }


            if (online && dto.getPoolSize() < 100) {
                return result.error("抽奖次数配置错误");
            }

            if (dto.getGiftId() <= 0) {
                return result.error("礼物id配置错误");
            }


            for (NationalDayV2Data.DrawRewardConfig reward : dto.getDrawRewardConfigList()) {
                if (!SUPPORT_SET.contains(reward.getRewardType())) {
                    return result.error("不支持的礼物资源");
                }

                if (ResourceConstant.ONCE_AGAIN.equals(reward.getRewardType())
                        || ResourceConstant.THANKS.equals(reward.getRewardType())) {
                    continue;
                }

                if (ResourceConstant.DIAMOND.equals(reward.getRewardType())
                        || ResourceConstant.HEART.equals(reward.getRewardType())) {
                    if (null == reward.getRewardNum()) {
                        return result.error("钻石/心心数量不能为空");
                    }
                } else {
                    if (null == reward.getSourceId()) {
                        return result.error("资源id不能为空");
                    }
                    if (null == reward.getRewardTime()) {
                        return result.error("资源时长不能为空");
                    }
                }
            }


            logger.info("update template activityId={} data={}", dto.getActivityId(), JSON.toJSONString(dto));

            NationalDayV2Data templateToUpdate = nationalDayV2Dao.findData(dto.getActivityId());
            if (Objects.isNull(templateToUpdate)) {
                logger.error("templateToUpdate is empty. id={}", dto.getActivityId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            if (templateToUpdate.getStatus() == 1 && ServerConfig.isProduct()) {
                return result.error("活动已结束，无法更新");
            }


            if (dto.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }

            Update update = new Update();
            update.set("acNameEn", dto.getAcNameEn());
            update.set("acNameAr", dto.getAcNameAr());
            update.set("startTime", dto.getStartTime());
            update.set("endTime", dto.getEndTime());
            update.set("homePageType", dto.getHomePageType());
            update.set("homePagePicture", dto.getHomePagePicture());
            update.set("homePagePictureAr", dto.getHomePagePictureAr());
            update.set("homePagePictureDt", dto.getHomePagePictureDt());
            update.set("homePagePictureDtAr", dto.getHomePagePictureDtAr());
            update.set("backgroundHome", dto.getBackgroundHome());
            update.set("backgroundHelp", dto.getBackgroundHelp());
            update.set("backgroundDraw", dto.getBackgroundDraw());
            update.set("backgroundRule", dto.getBackgroundRule());
            update.set("soundPlayIcon", dto.getSoundPlayIcon());
            update.set("soundPauseIcon", dto.getSoundPauseIcon());
            update.set("soundUrl", dto.getSoundUrl());
            update.set("gamePlayButtonEn", dto.getGamePlayButtonEn());
            update.set("gamePlayButtonAr", dto.getGamePlayButtonAr());
            update.set("questionButton", dto.getQuestionButton());
            update.set("questionUrl", dto.getQuestionUrl());
            update.set("drawButtonEn", dto.getDrawButtonEn());
            update.set("drawButtonAr", dto.getDrawButtonAr());
            update.set("pageHomeColor", dto.getPageHomeColor());
            update.set("joinButton", dto.getJoinButton());
            update.set("joinButtonBlack", dto.getJoinButtonBlack());
            update.set("flagIcon", dto.getFlagIcon());
            update.set("processIcon", dto.getProcessIcon());
            update.set("fontColor", dto.getFontColor());

            update.set("textColor", dto.getTextColor());
            update.set("countDownColor", dto.getCountDownColor());
            update.set("ruleColor", dto.getRuleColor());
            update.set("noticeBarColor", dto.getNoticeBarColor());

            update.set("giftId", dto.getGiftId());
            update.set("poolSize", dto.getPoolSize());
            update.set("advanceConfig", dto.getAdvanceConfig());
            update.set("rewardConfig", dto.getRewardConfig());
            update.set("drawRewardConfigList", dto.getDrawRewardConfigList());
            update.set("mtime", DateHelper.getNowSeconds());
            nationalDayV2Dao.updateData(templateToUpdate, update);

            // 更新需要删除奖池
            nationalDayV2Redis.deletePoolSize(dto.getActivityId());
        } catch (Exception e) {
            logger.error("update template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 活动模板分页查询
     */
    @RequireRole(2)
    @RequestMapping("/page")
    public String selectPage(@RequestBody ActivityTemplateCondition condition) {


        logger.info("luckyLottery selectPage condition={}", JSON.toJSONString(condition));
        HttpResult<PageResultVO<NationalDayV2DTO>> result = new HttpResult<>();
        PageResultVO<NationalDayV2DTO> pageVO = new PageResultVO<>();
        try {
            List<NationalDayV2Data> rankingActivities = nationalDayV2Dao
                    .selectPage(condition.getAcNameEn(), (condition.getPage() - 1) * 10, 10, condition.getTemplateType());
            List<NationalDayV2DTO> dtoList = new ArrayList<>();
            String token = basePlayerRedis.getToken(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
            rankingActivities.forEach(a -> {
                NationalDayV2DTO dto = new NationalDayV2DTO();
                dto.setActivityId(a.get_id().toString());
                BeanUtils.copyProperties(a, dto);
                dto.setUid(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
                dto.setToken(token);
                dtoList.add(dto);
            });
            pageVO.setList(dtoList);
            pageVO.setTotal(nationalDayV2Dao.selectCount(null));
        } catch (Exception e) {
            logger.info("luckyLottery selectPage error", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 活动模板详情
     */
    @RequireRole(2)
    @RequestMapping("/select_one")
    public String selectOne(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<NationalDayV2DTO> result = new HttpResult<>();
        if (Objects.isNull(condition.getActivityId())) {
            logger.error("The activityId cannot be empty.");
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        NationalDayV2Data data = nationalDayV2Dao.findData(condition.getActivityId());
        NationalDayV2DTO dto = new NationalDayV2DTO();
        dto.setActivityId(data.get_id().toString());
        BeanUtils.copyProperties(data, dto);
        return JSON.toJSONString(result.ok(dto), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }


}
