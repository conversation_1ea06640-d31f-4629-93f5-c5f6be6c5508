package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.RechargeCouponData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.RechargeCouponService;
import com.quhong.operation.share.condition.RechargeCouponCondition;
import com.quhong.operation.share.dto.CouponDTO;
import com.quhong.operation.share.vo.CouponOptionListVO;
import com.quhong.operation.share.vo.CouponUseRecordVO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 充值优惠卷
 *
 * <AUTHOR>
 * @date 2024/3/15
 */
@RestController
@RequestMapping("/recharge_coupon")
public class RechargeCouponController extends BaseController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final List<BigDecimal> VALID_EXTRA_PROP_LIST = Arrays.asList(
            new BigDecimal("0.1"),
            new BigDecimal("0.2"),
            new BigDecimal("0.3"),
            new BigDecimal("0.4"),
            new BigDecimal("0.5"));

    private static final List<Integer> VALID_DAY_LIST = Arrays.asList(3, 7, 15, 30);

    @Resource
    private RechargeCouponService rechargeCouponService;

    /**
     * 优惠卷配置列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<RechargeCouponData>> selectPage(@RequestParam String uid, @RequestBody RechargeCouponCondition condition) {
        logger.info("select page. uid={} condition={}", uid, JSON.toJSONString(condition));
        return HttpResult.getOk(rechargeCouponService.selectPage(condition));
    }

    /**
     * 优惠卷配置保存
     */
    @RequireRole(5)
    @RequestMapping("/save")
    public HttpResult<Object> save(@RequestParam String uid, @RequestBody RechargeCouponData dto) {
        logger.info("save recharge coupon config. uid={} dto={}", uid, JSON.toJSONString(dto));
        checkParam(dto);
        rechargeCouponService.save(uid, dto);
        return HttpResult.getOk();
    }

    /**
     * 赠送优惠卷
     */
    @RequireRole(5)
    @RequestMapping("/sendCoupon")
    public HttpResult<Object> sendCoupon(@RequestParam String uid, @RequestBody CouponDTO dto) {
        logger.info("send recharge coupon. uid={} strRid={} couponId={}", uid, dto.getStrRids(), dto.getCouponId());
        if (StringUtils.isEmpty(dto.getStrRids()) || dto.getCouponId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        rechargeCouponService.sendCoupon(uid, dto.getStrRids(), dto.getCouponId());
        return HttpResult.getOk();
    }

    /**
     * 移除用户优惠卷
     */
    @RequireRole(5)
    @RequestMapping("/removeCoupon")
    public HttpResult<Object> removeCoupon(@RequestParam String uid, @RequestBody CouponDTO dto) {
        logger.info("remove recharge coupon. uid={} aid={} couponId={}", uid, dto.getAid(), dto.getCouponId());
        if (StringUtils.isEmpty(dto.getAid()) || dto.getCouponId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        rechargeCouponService.removeCoupon(dto.getAid(), dto.getCouponId());
        return HttpResult.getOk();
    }

    /**
     * 优惠卷使用记录
     */
    @RequireRole()
    @RequestMapping("/useRecord")
    public HttpResult<PageResultVO<CouponUseRecordVO>> send(@RequestParam String uid, @RequestBody RechargeCouponCondition condition) {
        logger.info("recharge coupon use record. uid={} condition={}", uid, JSON.toJSONString(condition));
        return HttpResult.getOk(rechargeCouponService.useRecord(condition));
    }

    /**
     * 下拉框选项
     */
    @GetMapping("/optionList")
    public HttpResult<CouponOptionListVO> optionList(@RequestParam String uid) {
        logger.info("optionList. uid={} ", uid);
        return HttpResult.getOk(rechargeCouponService.optionList());
    }

    private void checkParam(RechargeCouponData dto) {
        if (StringUtils.isEmpty(dto.getTitle()) || StringUtils.isEmpty(dto.getTitleAr())) {
            throw new CommonH5Exception(new HttpCode(1, "标题不能为空"));
        }
        if (dto.getType() == null || (dto.getType() != 0 && dto.getType() != 1)) {
            throw new CommonH5Exception(new HttpCode(1, "显示规则参数错误"));
        }
        if (dto.getType() == 1) {
            if (dto.getStartTime() == null || dto.getStartTime() == 0 ||
                    dto.getEndTime() == null || dto.getEndTime() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "有效期参数错误"));
            }
        } else {
            if (!VALID_DAY_LIST.contains(dto.getValidDay())) {
                throw new CommonH5Exception(new HttpCode(1, "有效期参数错误"));
            }
        }
        if (!VALID_EXTRA_PROP_LIST.contains(dto.getExtraProp())) {
            throw new CommonH5Exception(new HttpCode(1, "额外奖励参数错误"));
        }
        if (dto.getStatus() == null || (dto.getStatus() != 0 && dto.getStatus() != 1)) {
            throw new CommonH5Exception(new HttpCode(1, "状态参数错误"));
        }
    }
}
