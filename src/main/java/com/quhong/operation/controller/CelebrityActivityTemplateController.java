package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.CelebrityActivityDao;
import com.quhong.mongo.data.CelebrityActivity;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.ActivityTemplateCondition;
import com.quhong.operation.share.dto.CelebrityTemplateDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/celebrity_template")
public class CelebrityActivityTemplateController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CelebrityActivityTemplateController.class);
    // wenmiaofang、yangml
    private static final Set<String> ADMIN_SET = new HashSet<>(Arrays.asList("5dba92651e23cd00c80b32bc", "61b1fa404cf5f82dff19a63e"));
    @Value("${online:true}")
    private boolean online;
    @Resource
    private CelebrityActivityDao celebrityActivityDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;

    /**
     * 保存活动模板
     */
    @RequireRole(3)
    @RequestMapping("/save_template")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody CelebrityActivity template) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(template));
            String uid = request.getParameter("uid");
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (null == template.getActivityGiftList() || null == template.getConfig() || null == template.getStartTime()
                    || null == template.getEndTime() || null == template.getAcNameAr()
                    || null == template.getAcNameEn()) {
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            for (CelebrityActivity.ActivityGift activityGift : template.getActivityGiftList()) {
                if (null == activityGift.getGiftId()) {
                    return result.error("活动礼物不能为空");
                }
            }
            if (template.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }
            template.setAcUrl(ServerConfig.isProduct() ? "https://static.youstar.live/celebrity_popularity/" : "https://test2.qmovies.tv/celebrity_popularity/");
            celebrityActivityDao.save(template);
        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 更新活动模板
     */
    @RequireRole(3)
    @RequestMapping("/update_template")
    public HttpResult updateTemplate(HttpServletRequest request, @RequestBody CelebrityTemplateDTO dto) {
        HttpResult result = new HttpResult();
        try {
            String uid = request.getParameter("uid");
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (Objects.isNull(dto.getActivityId())) {
                logger.error("The activityId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            if (null == dto.getActivityGiftList()
                    || null == dto.getConfig() || null == dto.getStartTime()
                    || null == dto.getEndTime() || null == dto.getAcNameAr()
                    || null == dto.getAcNameEn()) {
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            for (CelebrityActivity.ActivityGift activityGift : dto.getActivityGiftList()) {
                if (null == activityGift.getGiftId()) {
                    return result.error("活动礼物不能为空");
                }
            }
            logger.info("update template activityId={} data={}", dto.getActivityId(), JSON.toJSONString(dto));
            CelebrityActivity templateToUpdate = celebrityActivityDao.findData(dto.getActivityId());
            if (Objects.isNull(templateToUpdate)) {
                logger.error("templateToUpdate is empty. id={}", dto.getActivityId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            if (templateToUpdate.getStatus() == 1 && ServerConfig.isProduct()) {
                return result.error("活动已结束，无法更新");
            }
            if (dto.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }
            Update update = new Update();
            update.set("acNameEn", dto.getAcNameEn());
            update.set("acNameAr", dto.getAcNameAr());
            update.set("startTime", dto.getStartTime());
            update.set("endTime", dto.getEndTime());
            update.set("config", dto.getConfig());
            update.set("activityGiftList", dto.getActivityGiftList());
            update.set("mtime", DateHelper.getNowSeconds());
            celebrityActivityDao.updateData(templateToUpdate, update);
        } catch (Exception e) {
            logger.error("update template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 活动模板分页查询
     */
    @RequireRole(3)
    @RequestMapping("/page")
    public String selectPage(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<PageResultVO<CelebrityTemplateDTO>> result = new HttpResult<>();
        PageResultVO<CelebrityTemplateDTO> pageVO = new PageResultVO<>();
        try {
            List<CelebrityActivity> rankingActivities = celebrityActivityDao
                    .selectPage(condition.getAcNameEn(), (condition.getPage() - 1) * 10, 10);
            List<CelebrityTemplateDTO> dtoList = new ArrayList<>();
            String token = basePlayerRedis.getToken(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
            rankingActivities.forEach(a -> {
                CelebrityTemplateDTO dto = new CelebrityTemplateDTO();
                dto.setActivityId(a.get_id().toString());
                BeanUtils.copyProperties(a, dto);
                dto.setUid(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
                dto.setToken(token);
                dtoList.add(dto);
            });
            pageVO.setList(dtoList);
            pageVO.setTotal(celebrityActivityDao.selectCount());
        } catch (Exception e) {
            logger.info("e", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 活动模板详情
     */
    @RequireRole(3)
    @RequestMapping("/select_one")
    public String selectOne(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<CelebrityTemplateDTO> result = new HttpResult<>();
        if (Objects.isNull(condition.getActivityId())) {
            logger.error("The activityId cannot be empty.");
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        CelebrityActivity data = celebrityActivityDao.findData(condition.getActivityId());
        CelebrityTemplateDTO dto = new CelebrityTemplateDTO();
        dto.setActivityId(data.get_id().toString());
        BeanUtils.copyProperties(data, dto);
        return JSON.toJSONString(result.ok(dto), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 图片上传
     */
    @RequireRole(3)
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return AWSUploadUtils.upload(file);
    }
}
