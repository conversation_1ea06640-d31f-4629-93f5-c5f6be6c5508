package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.PayServer;
import com.quhong.operation.share.dto.pay.PayUserListDTO;
import com.quhong.operation.share.vo.pay.BeautifulRidLogVO;
import com.quhong.operation.share.vo.pay.PayUserInfoReportVO;
import com.quhong.operation.share.vo.pay.PayUserInfoVO;
import com.quhong.operation.share.vo.pay.PayUserListVO;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/pay")
public class PayController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PayController.class);

    @Resource
    private PayServer payServer;

    /**
     * 获取付费用户报表
     *
     * @param date
     */
    @RequestMapping("/userinfo")
    @RequireRole(4)
    public String userInfo(@RequestParam("date_str") String date) {
        try {
            logger.info("begin get pay user_info. date={}", date);
            //1、参数校验
            if (StringUtils.isEmpty(date)) {
                logger.info("get pay user_info param error. date={}", date);
                return createResult(HttpCode.PARAM_ERROR, null);
            }
            //2、业务调用
            PayUserInfoVO resultVO = payServer.userInfo(date);
            return createResult(HttpCode.SUCCESS, resultVO);
        } catch (Exception e) {
            logger.error("get pay user_info error. {}", e.getMessage(), e);
        }
        return createResult(HttpCode.SERVER_ERROR, null);
    }

    /**
     * 下载付费用户报表
     *
     * @param response
     */
    @RequestMapping("/userinfo/download")
    @RequireRole(4)
    public void downloadUserInfo(HttpServletResponse response) {
        try {
            logger.info("download pay user_info. ");
            //业务调用
            List<PayUserInfoReportVO> allPayUser = payServer.getAllPayUser();
            //报表下载
            ExcelUtils.exportExcel(response, allPayUser, PayUserInfoReportVO.class, "daily_data_change_report", "Sheet");
        } catch (Exception e) {
            logger.error("download pay user_info error. {}", e.getMessage(), e);
        }
    }

    /**
     * 付费用户列表
     *
     * @param userValue
     * @param loseLevel
     * @param search
     * @param page
     * @param pageSize
     * @param aType
     * @return
     */
    @RequestMapping("/userlist")
    @RequireRole(4)
    public String userList(@RequestParam(value = "user_value", defaultValue = "-1") Integer userValue,
                           @RequestParam(value = "lose_level", defaultValue = "-1") Integer loseLevel,
                           @RequestParam(value = "search", defaultValue = "-1") Integer search,
                           @RequestParam(value = "page", defaultValue = "1") Integer page,
                           @RequestParam(value = "page_size", defaultValue = "30") Integer pageSize,
                           @RequestParam(value = "atype", defaultValue = "data") String aType) {
        try {
            logger.info("begin get pay user_list. userValue={} loseLevel={} search={} page={} pageSize={} atype={}",
                    userValue, loseLevel, search, page, pageSize, aType);
            JSONObject jsonObject = new JSONObject();
            if ("data".equals(aType)) {
                PayUserListDTO dto = new PayUserListDTO();
                dto.setUserValue(userValue);
                dto.setLoseLevel(loseLevel);
                dto.setSearch(search);
                dto.setPage(page);
                dto.setPageSize(pageSize);
                dto.setaType(aType);
                jsonObject = payServer.payUserList(dto);
            }
            return createResult(HttpCode.SUCCESS, jsonObject);
        } catch (Exception e) {
            logger.error("get pay user_list error. {}", e.getMessage(), e);
        }
        return createResult(HttpCode.SERVER_ERROR, null);
    }

    /**
     * 付费用户列表 - 下载
     *
     * @param response
     * @param userValue
     * @param loseLevel
     * @param search
     */
    @RequestMapping("/userlist/download")
    @RequireRole(4)
    public void downloadUserList(HttpServletResponse response,
                                 @RequestParam(value = "user_value", defaultValue = "-1") Integer userValue,
                                 @RequestParam(value = "lose_level", defaultValue = "-1") Integer loseLevel,
                                 @RequestParam(value = "search", defaultValue = "-1") Integer search) {
        try {
            logger.info("begin download pay user_list. userValue={} loseLevel={} search={}",
                    userValue, loseLevel, search);
            PayUserListDTO dto = new PayUserListDTO();
            dto.setUserValue(userValue);
            dto.setLoseLevel(loseLevel);
            dto.setSearch(search);
            dto.setPage(-1);
            dto.setPageSize(0);
            dto.setaType("data");
            JSONObject result = payServer.payUserList(dto);
            Object itemsObj = result.get("items");
            List<PayUserListVO> userLists = new ArrayList<>();
            if (itemsObj != null) {
                List<JSONObject> items = JSON.parseArray(itemsObj.toString(), JSONObject.class);
                for (int i = 0; i < items.size(); i++) {
                    JSONObject item = items.get(i);
                    PayUserListVO payUserListVO = new PayUserListVO();
                    payUserListVO.setOrder(i + 1);
                    int user_value = item.getInteger("user_value");
                    String userCn = "unknow";
                    switch (user_value) {
                        case 4:
                            userCn = "大R";
                            break;
                        case 3:
                            userCn = "中R";
                            break;
                        case 2:
                            userCn = "小R";
                            break;
                        case 1:
                            userCn = "HPU";
                            break;
                        default:
                            break;
                    }
                    payUserListVO.setUserCn(userCn);
                    payUserListVO.setUid(item.getString("uid") == null ? "" : item.getString("uid"));
                    payUserListVO.setRid(item.getInteger("rid") == null ? 0 : item.getInteger("rid"));
                    payUserListVO.setBeans(item.getInteger("beans") == null ? 0 : item.getInteger("beans"));
                    payUserListVO.setBeautifulNum("详情");
                    DecimalFormat decimalFormat = new DecimalFormat("0.00");
                    payUserListVO.setLast30Charge("$" + decimalFormat.format(item.getFloat("last_30_charge") == null ? 0.00 : item.getFloat("last_30_charge")));
                    payUserListVO.setHonorExp(item.getLong("honor_exp") == null ? 0 : item.getLong("honor_exp"));
                    int lose_level = item.getInteger("lose_level");
                    String lossCn = "unknow";
                    switch (lose_level) {
                        case 1:
                            lossCn = "正常用户";
                            break;
                        case 2:
                            lossCn = "准备流失";
                            break;
                        case 3:
                            lossCn = "半流失";
                            break;
                        case 4:
                            lossCn = "重度流失";
                            break;
                        case 5:
                            lossCn = "完全流失";
                            break;
                        default:
                            break;
                    }
                    payUserListVO.setLossCn(lossCn);
                    String performValue = item.getString("lose_perform");
                    JSONObject perform = JSON.parseObject(performValue);
                    payUserListVO.setLastLiTime(perform.getString("last_li_time") == null ? "" : perform.getString("last_li_time"));
                    payUserListVO.setLast15RTime(perform.getInteger("last_15_rtime") == null ? 0 : perform.getInteger("last_15_rtime"));
                    payUserListVO.setLast7Send(perform.getLong("last_7_send") == null ? 0 : perform.getLong("last_7_send"));
                    payUserListVO.setLast7Recv(perform.getLong("last_7_recv") == null ? 0 : perform.getLong("last_7_recv"));
                    payUserListVO.setLastCgTime(perform.getString("last_cg_time") == null ? "" : perform.getString("last_cg_time"));
                    userLists.add(payUserListVO);
                }
            }
            //下载报表
            ExcelUtils.exportExcel(response, userLists, PayUserListVO.class, "user_pay_list2", "Sheet");
            logger.info("finish download pay user_list. userValue={} loseLevel={} search={}", userValue, loseLevel, search);
        } catch (Exception e) {
            logger.error("download pay user_list error. {}", e.getMessage(), e);
        }
    }

    /**
     * 获取靓号记录
     *
     * @param aid
     * @param atype
     * @return
     */
    @RequestMapping("beautiful_rid")
    @RequireRole(4)
    public String beautifulRid(String aid, String atype) {
        try {
            logger.info("begin get beautiful_rid. aid={} atype={}", aid, atype);
            if (StringUtils.isEmpty(aid)) {
                return createResult(HttpCode.PARAM_ERROR, null);
            }
            if (StringUtils.isEmpty(atype)) {
                atype = "beaut";
            }
            List<BeautifulRidLogVO> list = payServer.beautifulRidLog(aid, atype);
            return createResult(HttpCode.SUCCESS, list);
        } catch (Exception e) {
            logger.error("get beautiful_rid error. {}", e.getMessage(), e);
        }
        return createResult(HttpCode.SERVER_ERROR, null);
    }

}
