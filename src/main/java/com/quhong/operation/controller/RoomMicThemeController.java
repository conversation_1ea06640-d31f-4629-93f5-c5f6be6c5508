package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.RoomMicThemeData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.RoomMicThemeService;
import com.quhong.operation.share.condition.BaseCondition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 运营平台麦位主题
 */
@RestController
@RequestMapping(value = "/roomMicTheme", produces = MediaType.APPLICATION_JSON_VALUE)
public class RoomMicThemeController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(RoomMicThemeController.class);

    @Resource
    private RoomMicThemeService roomMicThemeService;

    /**
     * 麦位主题配置
     */
    @RequireRole
    @RequestMapping("/list")
    public String roomMicThemeList(@RequestBody BaseCondition condition) {
        logger.info("get roomMicThemeList {}", condition);
        return createResult(HttpCode.SUCCESS, roomMicThemeService.roomMicThemeConfigList(condition));
    }

    @RequireRole
    @RequestMapping("/add")
    public String addRoomMicTheme(@RequestBody RoomMicThemeData dto) {
        logger.info("addRoomMicTheme {}", JSONObject.toJSONString(dto));
        roomMicThemeService.addRoomMicTheme(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @RequestMapping("/update")
    public String updateRoomMicTheme(@RequestBody RoomMicThemeData dto) {
        logger.info("updateRoomMicTheme {}", JSONObject.toJSONString(dto));
        roomMicThemeService.updateRoomMicTheme(dto);
        return createResult(HttpCode.SUCCESS, "");
    }
}
