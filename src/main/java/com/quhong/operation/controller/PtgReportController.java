package com.quhong.operation.controller;

import com.quhong.core.utils.DateHelper;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.PtgReportServer;
import com.quhong.operation.share.vo.PtgCrushDayVO;
import com.quhong.operation.share.vo.PtgCrushNUDayVO;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/ptg_reports")
public class PtgReportController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PtgReportController.class);

    @Resource
    private PtgReportServer ptgReportServer;

    /**
     * 获取ptg快捷匹配ptg端报表数据
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return
     */
    @RequireRole(3)
    @RequestMapping("ptg_crush")
    public String ptgCrushReport(String start, String end) {
        try {
            logger.info("begin get ptg crush report. start={} end={}", start, end);
            //1.参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                return createResult(HttpCode.PARAM_ERROR, null);
            }
            //2.业务调用
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            List<PtgCrushDayVO> list = new ArrayList<>();
            for (DayTimeData dayTimeData : dayTimeList) {
                PtgCrushDayVO ptgCrushData = ptgReportServer.getPtgCrushData(dayTimeData);
                list.add(ptgCrushData);
            }
            return createResult(HttpCode.SUCCESS, list);
        } catch (Exception e) {
            logger.error("get ptg crush report error. {}", e.getMessage(), e);
        }
        return createResult(HttpCode.SERVER_ERROR, null);
    }

    /**
     * ptg快捷匹配ptg端报表 - 下载
     *
     * @param response
     * @param start
     * @param end
     */
    @RequireRole(3)
    @RequestMapping("ptg_crush/download")
    public void ptgCrushReportDownload(HttpServletResponse response, String start, String end) {
        try {
            logger.info("begin download ptg crush report. start={} end={}", start, end);
            //1.参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                response.getWriter().write(createResult(HttpCode.PARAM_ERROR, null));
                return;
            }
            //2.业务调用
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            List<PtgCrushDayVO> list = new ArrayList<>();
            for (DayTimeData dayTimeData : dayTimeList) {
                PtgCrushDayVO ptgCrushData = ptgReportServer.getPtgCrushData(dayTimeData);
                list.add(ptgCrushData);
            }
            //3.报表下载
            ExcelUtils.exportExcel(response, list, PtgCrushDayVO.class, "ptg_crush_report", "ptg_crush");
            logger.info("download ptg crush report ok !!!");
        } catch (Exception e) {
            logger.error("download ptg crush report error. {}", e.getMessage(), e);
        }
    }

    /**
     * 获取ptg快捷匹配数据：新用户端
     *
     * @param start  开始日期
     * @param end    结束日期
     * @param gender 1男用户2女用户 -1全部
     * @return
     */
    @RequireRole(3)
    @RequestMapping("newly_user_crush")
    public String newlyUserCrushReport(String start, String end, Integer gender, Integer app) {
        try {
            logger.info("begin get newly user crush report. start={} end={} gender={} app={}",
                    start, end, gender, app);
            //1.参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                return createResult(HttpCode.PARAM_ERROR, null);
            }
            if (gender == null) {
                gender = -1;
            }
            if (app == null) {
                app = -1;
            }
            //2.业务调用
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            List<PtgCrushNUDayVO> list = new ArrayList<>();
            for (DayTimeData dayTimeData : dayTimeList) {
                PtgCrushNUDayVO userCrushData = ptgReportServer.getNewlyUserCrushData(dayTimeData, gender, app);
                list.add(userCrushData);
            }
            return createResult(HttpCode.SUCCESS, list);
        } catch (Exception e) {
            logger.error("get newly user crush report error. {}", e.getMessage(), e);
        }
        return createResult(HttpCode.SERVER_ERROR, null);
    }

    /**
     * ptg快捷匹配数据：新用户端报表下载
     *
     * @param response
     * @param start
     * @param end
     * @param gender
     * @param app
     */
    @RequireRole(3)
    @RequestMapping("newly_user_crush/download")
    public void downloadNewlyUserCrush(HttpServletResponse response, String start, String end, Integer gender, Integer app) {
        try {
            logger.info("begin download newly user crush report. start={} end={} gender={} app={}",
                    start, end, gender, app);
            //1.参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                response.getWriter().write(createResult(HttpCode.PARAM_ERROR, null));
                return;
            }
            if (gender == null) {
                gender = -1;
            }
            if (app == null) {
                app = -1;
            }
            //2.业务调用
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            List<PtgCrushNUDayVO> list = new ArrayList<>();
            for (DayTimeData dayTimeData : dayTimeList) {
                PtgCrushNUDayVO userCrushData = ptgReportServer.getNewlyUserCrushData(dayTimeData, gender, app);
                list.add(userCrushData);
            }
            //3.下载报表
            ExcelUtils.exportExcel(response, list, PtgCrushNUDayVO.class, "newly_user_crush", "newly_user");
            logger.info("finish download newly user crush report !!!");
        } catch (Exception e) {
            logger.error("download newly user crush report error. {}", e.getMessage(), e);
        }
    }

}
