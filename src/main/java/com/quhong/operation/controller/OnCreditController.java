package com.quhong.operation.controller;

import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.CreditChargeServer;
import com.quhong.operation.share.vo.CreditTotalVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/6/28
 */
@RestController
@RequestMapping("/onCredit")
public class OnCreditController {

    private final static Logger logger = LoggerFactory.getLogger(OnCreditController.class);

    @Autowired
    private CreditChargeServer creditChargeServer;

    @RequestMapping("/onCreditList")
    public HttpResult<CreditTotalVO> getOnCreditList(Integer rid, String startTime, String endTime, Integer state) {
        logger.info("method getOnCreditList param rid={}, startTime={}, endTime={}, state={}",
                rid, startTime, endTime, state);

        HttpResult<CreditTotalVO> result = new HttpResult<>();
        Integer endSeconds = DateHelper.ARABIAN.getEndSeconds(endTime);
        Integer startSeconds = DateHelper.ARABIAN.getStartSeconds(startTime, endSeconds);

        // 只查询2020年04月01日开始的数据 1585670400表示这个时间的时间戳
        if (startSeconds < 1585670400) {
            startSeconds = 1585670400;
        }
        ApiResult<CreditTotalVO> apiResult =
                creditChargeServer.getOnCreditList(startSeconds, endSeconds, rid, state);
        logger.info("getOnCreditList apiResult {}", apiResult);
        if (!apiResult.isOK()) {
            return result.error(apiResult.getMsg());
        }
        return result.ok(apiResult.getData());
    }

    /**
     * 批量更新赊账还款状态
     *
     * @param ids
     * @param uid
     * @param state
     * @return
     */
    @RequestMapping("/updateCreditState")
    public HttpResult<String> updateCreditState(String[] ids, String uid, Integer state) {
        logger.info("method updateCreditState param ids={}, uid={}, state={}", ids, uid, state);
        HttpResult<String> result = new HttpResult<>();
        if (null == ids || ids.length < 1) {
            return result.error("ids not null!");
        }
        if (StringUtil.isEmptyOrBlank(uid)) {
            return result.error("uid not null!");
        }
        if (null == state || (1 != state && 2 != state)) {
            return result.error("state not null! And state it should be 1 or 2");
        }
        ApiResult<Integer> apiResult = creditChargeServer.updateCreditChargeState(ids, state, uid);
        if (apiResult.isOK()) {
            if (apiResult.getData() == ids.length)
                return result.ok("SUCCESS");
            return result.ok("predict update " + ids.length + " item, reality update " + apiResult.getData() + " item");
        }
        return result.error(apiResult.getMsg());
    }

}
