package com.quhong.operation.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.dao.ElasticsearchDao;
import com.quhong.operation.server.report.MoneyReportServer;
import com.quhong.operation.server.report.WelcomeRoomReportServer;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.MonthlyRechargeVO;
import com.quhong.operation.share.vo.reports.money.WelcomeRoomVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/19
 */
@RestController
@RequestMapping("/reports")
public class MoneyReportsController {

    private static final Logger logger = LoggerFactory.getLogger(MoneyReportsController.class);

    @Autowired
    private ElasticsearchDao elasticsearchDao;
    @Autowired
    private WelcomeRoomReportServer welcomeRoomReportServer;
    @Autowired
    private MoneyReportServer moneyReportServer;

    /**
     * 迎新房上麦五分钟、迎新房新用户充值报表下载
     *
     * @param response  response
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequestMapping("/getWelcomeRoomReports")
    public void getWelcomeRoomReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("getWelcomeRoomReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List<WelcomeRoomVO>> result = welcomeRoomReportServer.everydayWelcomeRoomNewActorChargeInfo(timeArr[0], timeArr[1]);
        ApiResult<List<WelcomeRoomVO>> result1 = welcomeRoomReportServer.mic5MinutesActorInfo(timeArr[0], timeArr[1]);
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "welcome_room_reports");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(result.getData(), EasyExcel.writerSheet(0, "注册当天进入迎新房且充值用户信息").head(WelcomeRoomVO.class).build());
            excelWriter.write(result1.getData(), EasyExcel.writerSheet(1, "迎新房上麦5分钟的新用户").head(WelcomeRoomVO.class).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method getWelcomeRoomReports 执行完毕");
    }

    /**
     * 获取一段时间内首充用户充值信息
     *
     * @param response  response
     * @param startDate 开始时间
     * @param endDate   结尾时间
     */
    @RequestMapping("/chargeActorInfo")
    public void generateChargeActorInfo(HttpServletResponse response, String startDate, String endDate) {
        logger.info("start generate charge actor info startDate = {} endDate = {} ", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<MonthlyRechargeVO> lists = moneyReportServer.getMonthlyRechargeInfo(timeArr[0], timeArr[1]);
        ExcelUtils.exportExcel(response, lists, MonthlyRechargeVO.class, "MonthlyRechargeInfo", "月份线上充值用户详情数据");
        logger.info("method generateChargeActorInfo 执行完毕");
    }

    /**
     * 获取某段时间内新增并且充值的用户充值信息
     *
     * @param request  request
     * @param response response
     * @param start    开始
     * @param end      结尾
     */
    @RequestMapping("/getDateChargeNewActor")
    public void getDateChargeNewActor(HttpServletRequest request,
                                      HttpServletResponse response, String start, String end) {
        Integer endTime = DateHelper.ARABIAN.getEndSeconds(end);
        Integer startTime = DateHelper.ARABIAN.getStartSeconds(start, endTime);
        ApiResult<List<WelcomeRoomVO>> result =
                welcomeRoomReportServer.everydayWelcomeRoomNewActorChargeInfo(startTime, endTime);
        if (!result.isOK()) {
            logger.error("getDateChargeNewActor everydayWelcomeRoomNewActorChargeInfo msg = {}", result.getMsg());
            return;
        }
        String[] titleArr = {"用户ID", "昵称", "性别", "国家", "平台（安卓/iOS）", "注册日期", "最后登陆时间",
                "用户等级", "vip等级", "房间停留时长", "上麦时长", "送礼人数", "送礼次数", "充值次数", "充值金额"};

        ApiResult<List<TotalVO>> result2 = elasticsearchDao.getDateChargeNewActor(startTime, endTime);
        List<List<String>> lists = new ArrayList<>();
        if (result.isOK()) {
            List<TotalVO> data = result2.getData();
            for (TotalVO vo : data) {
                ApiResult<List<String>> apiResult =
                        welcomeRoomReportServer.fillDateChargeNewActor(vo, startTime, endTime);
                if (!apiResult.isOK() || CollectionUtils.isEmpty(apiResult.getData())) {
                    logger.error("fillDateChargeNewActor error msg={} or list is null", apiResult.getMsg());
                    continue;
                }
                lists.add(apiResult.getData());
            }
        }

        List<List<String>> head = ExcelUtils.buildHead(titleArr);
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "inWelcomeRoomChargeActor");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(result.getData(), EasyExcel.writerSheet(0, "迎新房的充值用户").head(head).build());
            excelWriter.write(lists, EasyExcel.writerSheet(1, "迎新房的充值用户2").head(head).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method getDateChargeNewActor 执行完毕");
    }

}
