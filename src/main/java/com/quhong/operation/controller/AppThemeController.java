package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.WebClient;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.AppThemeDao;
import com.quhong.mongo.data.AppThemeData;
import com.quhong.monitor.MonitorSender;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.AppThemeCondition;
import com.quhong.operation.share.dto.AppThemeConfigDTO;
import com.quhong.operation.share.dto.AppThemeDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.AWSUploadUtils;
import com.quhong.operation.utils.Md5Utils;
import com.quhong.operation.utils.ZipUtil;
import com.quhong.redis.SourceDuplicateRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/appTheme")
public class AppThemeController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AppThemeController.class);
    private static final  String FILE_BUKET_PATH = "appTheme/";  // AWS目录名
    private static final String fileBasePath = "/opt";
    private final static String DIR_ANDROID = "android";
    private final static String DIR_IOS = "ios";
    private final static String DIR_IOS_X = "ios_x";
    private final static Pattern pattern = Pattern.compile("\\S*[?]\\S*");
    private final static Map<String, String> JSON_FILE_NAME = new HashMap<String, String>() {
        {
            put("lightEn", "light_ltr_%s.json");
            put("lightAr", "light_rtl_%s.json");
            put("nightEn", "night_ltr_%s.json");
            put("nightAr", "night_rtl_%s.json");
        }
    };


    @Resource
    private AppThemeDao appThemeDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private SourceDuplicateRedis sourceDuplicateRedis;

    /**
     * 活动模板分页查询
     */
    @RequireRole(2)
    @RequestMapping("/page")
    public String selectPage(@RequestBody AppThemeCondition condition) {


        logger.info("AppTheme selectPage condition={}", JSON.toJSONString(condition));
        HttpResult<PageResultVO<AppThemeDTO>> result = new HttpResult<>();
        PageResultVO<AppThemeDTO> pageVO = new PageResultVO<>();

        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        try {
            List<AppThemeData> appThemeDatas = appThemeDao.selectPage(condition.getStatus(), condition.getSearch(), start, pageSize);
            List<AppThemeDTO> dtoList = new ArrayList<>();
            appThemeDatas.forEach(a -> {
                AppThemeDTO dto = new AppThemeDTO();
                dto.setDocId(a.get_id().toString());
                BeanUtils.copyProperties(a, dto);
                dtoList.add(dto);
            });
            pageVO.setList(dtoList);
            pageVO.setTotal(appThemeDao.selectCount());
        } catch (Exception e) {
            logger.info("AppTheme selectPage error", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 保存主题
     */
    @RequireRole(2)
    @RequestMapping("/save")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody AppThemeData template) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(template));
            if(StringUtils.isEmpty(template.getThemeName())){
                return result.error("主题名不能为空");
            }
            List<AppThemeData.ThemeConfig> themeConfigList = template.getThemeConfigList();
            Map<String, Integer> themeMap = new HashMap<>();
            if(themeConfigList != null && !themeConfigList.isEmpty()){
                for (AppThemeData.ThemeConfig config: themeConfigList) {

                    String duplicate = "" + config.getThemePlatform() + config.getThemeDpi() + config.getThemeType();

                    if(themeMap.get(duplicate) != null){
                        return result.error("主题类型重复");
                    }
                    themeMap.put(duplicate, 1);
                }
            }

            if(template.getStatus() == 1){
                if(appThemeDao.findValidData() != null){
                    return result.error("存在有效的主题, 只能存在一个有效的主题, 请先将旧主题改为无效");
                }
            }


            appThemeDao.save(template);
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "正式服-用户成功创建appTheme", "app主题名：" + template.getThemeName());
            }

        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

   /**
    * 更新主题
    */
   @RequireRole(2)
   @RequestMapping("/update")
   public HttpResult updateTemplate(HttpServletRequest request, @RequestBody AppThemeDTO dto) {
       HttpResult result = new HttpResult();
       try {

           if (StringUtils.isEmpty(dto.getDocId())) {
               logger.error("The activityId cannot be empty.");
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }

           logger.info("update template activityId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
           AppThemeData template = appThemeDao.findData(dto.getDocId());
           if (Objects.isNull(template)) {
               logger.error("AppThemeData template is empty. id={}", dto.getDocId());
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }


           if(dto.getStatus() == 1){
               AppThemeData themeData = appThemeDao.findValidData();
               if( themeData != null && !themeData.get_id().toString().equals(dto.getDocId())){
                   return result.error("存在有效的主题, 只能存在一个有效的主题, 请先将旧主题改为无效");
               }
           }

           Update update = new Update();
           update.set("startTime", dto.getStartTime());
           update.set("endTime", dto.getEndTime());
           update.set("themeName", dto.getThemeName());
           update.set("status", dto.getStatus());

           // android zip包设置
           if(!StringUtils.isEmpty(dto.getAndroidZipUrl())){
               update.set("androidZipUrl", dto.getAndroidZipUrl().trim());
           }

           if(!StringUtils.isEmpty(dto.getAndroidZipMd5())){
               update.set("androidZipMd5", dto.getAndroidZipMd5().trim());
           }

           // ios zip包设置
           if(!StringUtils.isEmpty(dto.getIosZipUrl())){
               update.set("iosZipUrl", dto.getIosZipUrl().trim());
           }

           if(!StringUtils.isEmpty(dto.getIosZipMd5())){
               update.set("iosZipMd5", dto.getIosZipMd5().trim());
           }

           // ios x zip包设置
           if(!StringUtils.isEmpty(dto.getIosXZipUrl())){
               update.set("iosXZipUrl", dto.getIosXZipUrl().trim());
           }
           if(!StringUtils.isEmpty(dto.getIosXZipMd5())){
               update.set("iosXZipMd5", dto.getIosXZipMd5().trim());
           }


           if(!CollectionUtils.isEmpty(dto.getThemeConfigList())){
               update.set("themeConfigList", dto.getThemeConfigList());
           }
           update.set("mtime", DateHelper.getNowSeconds());
           appThemeDao.updateData(template, update);
       } catch (Exception e) {
           logger.error("update template error. {}", e.getMessage(), e);
           return result.error();
       }
       return result.ok();
   }


    /**
     * 增加单个主题类型
     */
    @RequireRole(2)
    @RequestMapping("/addThemeConfig")
    public HttpResult addThemeConfig(HttpServletRequest request, @RequestBody AppThemeConfigDTO dto) {
        HttpResult result = new HttpResult();
        try {

            if (StringUtils.isEmpty(dto.getDocId()) || StringUtils.isEmpty(dto.getThemeType())) {
                logger.error("The getDocId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("addThemeConfig docId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
            AppThemeData template = appThemeDao.findData(dto.getDocId());
            if (Objects.isNull(template)) {
                logger.error("AppThemeData template is empty. id={}", dto.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            List<AppThemeData.ThemeConfig> themeConfigList = template.getThemeConfigList();
            Map<String, Integer> themeMap = new HashMap<>();
            if(themeConfigList != null && !themeConfigList.isEmpty()){
                for (AppThemeData.ThemeConfig config: themeConfigList) {
                    String duplicate = "" + config.getThemePlatform() + config.getThemeDpi() + config.getThemeType();
                    themeMap.put(duplicate, 1);
                }
            } else{
               themeConfigList = new ArrayList<>();
            }

            String newDuplicate = "" + dto.getThemePlatform() + dto.getThemeDpi() + dto.getThemeType();
            if(themeMap.get(newDuplicate) != null){
                return result.error("主题类型重复");
            }

            AppThemeData.ThemeConfig tempConfig = new  AppThemeData.ThemeConfig();
            BeanUtils.copyProperties(dto, tempConfig);
            themeConfigList.add(tempConfig);

            Update update = new Update();
            update.set("themeConfigList", themeConfigList);
            update.set("mtime", DateHelper.getNowSeconds());
            appThemeDao.updateData(template, update);

        } catch (Exception e) {
            logger.error("addThemeConfig error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }


    /**
     * 修改单个主题类型
     */
    @RequireRole(2)
    @RequestMapping("/updateThemeConfig")
    public HttpResult updateThemeConfig(HttpServletRequest request, @RequestBody AppThemeConfigDTO dto) {
        HttpResult result = new HttpResult();
        try {

            if (StringUtils.isEmpty(dto.getDocId()) || StringUtils.isEmpty(dto.getThemeType())) {
                logger.error("The getDocId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("addThemeConfig docId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
            AppThemeData template = appThemeDao.findData(dto.getDocId());
            if (Objects.isNull(template)) {
                logger.error("AppThemeData template is empty. id={}", dto.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            String dtoKey = "" + dto.getThemePlatform() + dto.getThemeType() + dto.getThemeDpi();

            List<AppThemeData.ThemeConfig> themeConfigList = template.getThemeConfigList();
            if(themeConfigList != null && !themeConfigList.isEmpty()){
                for (AppThemeData.ThemeConfig config: themeConfigList) {
                    String dbKey = "" + config.getThemePlatform() + config.getThemeType() + config.getThemeDpi();
                    if(dbKey.equals(dtoKey)){
                        BeanUtils.copyProperties(dto, config);
                    }
                }
            }else {
                return result.error("param is error");
            }

            Update update = new Update();
            update.set("themeConfigList", themeConfigList);
            update.set("mtime", DateHelper.getNowSeconds());
            appThemeDao.updateData(template, update);
        } catch (Exception e) {
            logger.error("updateThemeConfig error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 删除单个主题类型
     */
    @RequireRole(2)
    @RequestMapping("/deleteThemeConfig")
    public HttpResult deleteThemeConfig(HttpServletRequest request, @RequestBody AppThemeConfigDTO dto) {
        HttpResult result = new HttpResult();
        try {

            if (StringUtils.isEmpty(dto.getDocId()) || StringUtils.isEmpty(dto.getThemeType())) {
                logger.error("The getDocId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("addThemeConfig docId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
            AppThemeData template = appThemeDao.findData(dto.getDocId());
            if (Objects.isNull(template)) {
                logger.error("AppThemeData template is empty. id={}", dto.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            String dtoKey = "" + dto.getThemePlatform() + dto.getThemeType() + dto.getThemeDpi();
            List<AppThemeData.ThemeConfig> themeConfigList = template.getThemeConfigList();

            Iterator<AppThemeData.ThemeConfig> iterator = themeConfigList.iterator();
            while(iterator.hasNext()) {
                AppThemeData.ThemeConfig next = iterator.next();
                String dbKey = "" + next.getThemePlatform() + next.getThemeType() + next.getThemeDpi();
                if (dbKey.equals(dtoKey)) {
                    iterator.remove();
                }
            }

            Update update = new Update();
            update.set("themeConfigList", themeConfigList);
            update.set("mtime", DateHelper.getNowSeconds());
            appThemeDao.updateData(template, update);
        } catch (Exception e) {
            logger.error("updateThemeConfig error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }


    /**
     * 生成zipUrl包
     */
    private static String parseSuffix(String url) {

        Matcher matcher = pattern.matcher(url);
        String[] spUrl = url.split("/");
        int len = spUrl.length;
        String endUrl = spUrl[len - 1];
        if(matcher.find()) {
            String[] spEndUrl = endUrl.split("\\?");
            return spEndUrl[0].split("\\.")[1];
        }
        return endUrl;
    }


    public String loadFileFromUrl(String rootDir, String fileUrl){

        if(StringUtils.isEmpty(fileUrl)){
            return "";
        }

        WebClient webClient = new WebClient();

        String iconDir = rootDir + "icon/";
        OutputStream outputStream = null;
        try{
            Path path = Paths.get(iconDir);
            if(!Files.exists(path)){
                Files.createDirectories(path);
            }
            String fileName = parseSuffix(fileUrl);
            String filePath = iconDir + fileName;
            File file = new File(filePath);
            if(!file.exists()){
                byte[] resp = webClient.getBytes(fileUrl);
                outputStream = new BufferedOutputStream(Files.newOutputStream(Paths.get(filePath)));
                outputStream.write(resp);
                outputStream.close();
            }
            return fileName;

        }catch (Exception e) {
            logger.error("loadFileFromUrl, msg={}" , e.getMessage(), e);
        } finally {
            try {
                outputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return "";
    }


    private void fileOutPutStreamContent(String rootDir, String fileName, String content) {
        String filePath = rootDir + fileName;
        File file;
        FileOutputStream fos;
        try {
            file = new File(filePath);
            if(!file.exists()) {
                boolean newFile = file.createNewFile();
                if (!newFile) {
                    logger.info("fail to create new file, please check!");
                    return;
                }
            }
            byte[] bytes = content.getBytes();
            int b = bytes.length;   //是字节的长度，不是字符串的长度
            fos = new FileOutputStream(file); // 如果已存在，以覆盖的方式写文件
            // fos = new FileOutputStream(txt, true); // 如果已存在，以追加的方式写文件
            fos.write(bytes,0, b); // 写指定长度的内容
            // fos.write(bytes); // 写全文
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private boolean themeTypeZip(String rootDir, AppThemeData.ThemeConfig themeConfig) {

        // 1、顶部导航栏配置
        themeConfig.getHomeNavBar().setTopIcon(loadFileFromUrl(rootDir, themeConfig.getHomeNavBar().getTopIcon()));


        // 2、底部Tab栏配置图片转换
        themeConfig.getBottomTabbar().setBackgroundIcon(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getBackgroundIcon()));
        themeConfig.getBottomTabbar().setBackgroundNavIcon(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getBackgroundNavIcon()));
        themeConfig.getBottomTabbar().setPlusIcon(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getPlusIcon()));
        themeConfig.getBottomTabbar().setPlusBgIcon(loadFileFromUrl(rootDir, themeConfig.getBottomTabbar().getPlusBgIcon()));
        List<AppThemeData.NavBarConfig> navBarConfigList = themeConfig.getBottomTabbar().getNavList();
        for (AppThemeData.NavBarConfig navBarConfig: navBarConfigList) {
            navBarConfig.setNavIcon(loadFileFromUrl(rootDir, navBarConfig.getNavIcon()));
            navBarConfig.setNavSelectedIcon(loadFileFromUrl(rootDir, navBarConfig.getNavSelectedIcon()));
            navBarConfig.setNavSelectedBgIcon(loadFileFromUrl(rootDir, navBarConfig.getNavSelectedBgIcon()));
            navBarConfig.setNavIconJson(loadFileFromUrl(rootDir, navBarConfig.getNavIconJson()));
            navBarConfig.setNavSelectedIconJson(loadFileFromUrl(rootDir, navBarConfig.getNavSelectedIconJson()));
        }
        themeConfig.getBottomTabbar().setNavList(navBarConfigList);

        // 3、home栏细节图片转换
        themeConfig.getHome().setMoreIcon(loadFileFromUrl(rootDir, themeConfig.getHome().getMoreIcon()));
        themeConfig.getHome().setChatHallIcon(loadFileFromUrl(rootDir, themeConfig.getHome().getChatHallIcon()));
        themeConfig.getHome().setOnlineIcon(loadFileFromUrl(rootDir, themeConfig.getHome().getOnlineIcon()));
        themeConfig.getHome().setSearchIcon(loadFileFromUrl(rootDir, themeConfig.getHome().getSearchIcon()));


        // 4、Moment栏细节配置
        themeConfig.getMoment().setTopicIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getTopicIcon()));
        themeConfig.getMoment().setTopicFollowIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getTopicFollowIcon()));
        themeConfig.getMoment().setTopicMomentIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getTopicMomentIcon()));
        themeConfig.getMoment().setTopicGiftIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getTopicGiftIcon()));
        themeConfig.getMoment().setCellDecorationIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getCellDecorationIcon()));
        themeConfig.getMoment().setNoticeIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getNoticeIcon()));
        themeConfig.getMoment().setPostIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getPostIcon()));
        themeConfig.getMoment().setPenIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getPenIcon()));
        themeConfig.getMoment().setMenuIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getMenuIcon()));
        themeConfig.getMoment().setCommentIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getCommentIcon()));
        themeConfig.getMoment().setLikeIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getLikeIcon()));
        themeConfig.getMoment().setLikeSelectedIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getLikeSelectedIcon()));
        themeConfig.getMoment().setLikeAnimateIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getLikeAnimateIcon()));
        themeConfig.getMoment().setShareIcon(loadFileFromUrl(rootDir, themeConfig.getMoment().getShareIcon()));

        // 5、chat栏细节配置
        themeConfig.getChat().setCellDecorationIcon(loadFileFromUrl(rootDir, themeConfig.getChat().getCellDecorationIcon()));

        // 6、 Me栏细节配置
        themeConfig.getMe().setCopyIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getCopyIcon()));
        themeConfig.getMe().setEditIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getEditIcon()));
        themeConfig.getMe().setTaskIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getTaskIcon()));
        themeConfig.getMe().setTycoonsIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getTycoonsIcon()));
        themeConfig.getMe().setStoreIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getStoreIcon()));
        themeConfig.getMe().setMyDecorationIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getMyDecorationIcon()));
        themeConfig.getMe().setLevelIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getLevelIcon()));
        themeConfig.getMe().setBadgeIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getBadgeIcon()));
        themeConfig.getMe().setRechargeStarIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getRechargeStarIcon()));
        themeConfig.getMe().setQueenIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getQueenIcon()));
        themeConfig.getMe().setFamilyIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getFamilyIcon()));
        themeConfig.getMe().setFeedbackIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getFeedbackIcon()));
        themeConfig.getMe().setSettingIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getSettingIcon()));
        themeConfig.getMe().setFbSettingNavIcon(loadFileFromUrl(rootDir, themeConfig.getMe().getFbSettingNavIcon()));

        List<String> newFloatScreen = new ArrayList<>();
        for (String floatUrl : themeConfig.getFloatScreen()) {
            newFloatScreen.add(loadFileFromUrl(rootDir, floatUrl));
        }

        themeConfig.setFloatScreen(newFloatScreen);

        String themeConfigString = JSONObject.toJSONString(themeConfig);
        String jsonFileName = String.format(JSON_FILE_NAME.get(themeConfig.getThemeType()), themeConfig.getThemeDpi());

        fileOutPutStreamContent(rootDir, jsonFileName, themeConfigString);

        return true;
    }

    private void deleteFolder(File file){
        for (File subFile : file.listFiles()) {
            if(subFile.isDirectory()) {
                deleteFolder(subFile);
            } else {
                subFile.delete();
            }
        }
        file.delete();
    }



    private void asyncGenZipUrl(String docId){
        AppThemeData themeData = appThemeDao.findData(docId);
        if(themeData == null){
            return;
        }

        boolean androidFlag = false;
        String androidRootDir = fileBasePath + "/" + DIR_ANDROID + "/";

        boolean iosFlag = false;
        String iosRootDir = fileBasePath + "/" + DIR_IOS + "/";

        boolean iosXFlag = false;
        String iosXRootDir = fileBasePath + "/" + DIR_IOS_X + "/";

        for (AppThemeData.ThemeConfig themeConfig : themeData.getThemeConfigList()) {

            if(themeConfig.getThemePlatform() == 0){
                androidFlag = themeTypeZip(androidRootDir, themeConfig);
            } else if (themeConfig.getThemePlatform() == 1) {
                iosFlag = themeTypeZip(iosRootDir, themeConfig);
            } else if (themeConfig.getThemePlatform() == 2) {
                iosXFlag = themeTypeZip(iosXRootDir, themeConfig);
            }
        }

        Update update = new Update();

        if(androidFlag){
            String androidZipPath = ZipUtil.zipFileWithPath(androidRootDir, DIR_ANDROID + "_" + DateHelper.getNowSeconds() + ".zip");
            String androidUrl = ZipUtil.uploadZipFile(androidZipPath, FILE_BUKET_PATH);
            Map<String, Object> androidZipFileMeta = ZipUtil.calculateZipFileMD5(androidZipPath);

            update.set("androidZipUrl", androidUrl);
            update.set("androidZipMd5", androidZipFileMeta.get("fileMd5"));
            deleteFolder(new File(androidRootDir));
        }

        if (iosFlag) {
            String iosZipPath = ZipUtil.zipFileWithPath(iosRootDir, DIR_IOS + "_" + DateHelper.getNowSeconds() + ".zip");
            String iosUrl = ZipUtil.uploadZipFile(iosZipPath, FILE_BUKET_PATH);
            Map<String, Object> iosZipFileMeta = ZipUtil.calculateZipFileMD5(iosZipPath);
            update.set("iosZipUrl", iosUrl);
            update.set("iosZipMd5", iosZipFileMeta.get("fileMd5"));
            deleteFolder(new File(iosRootDir));
        }

        if (iosXFlag) {
            String iosXZipPath = ZipUtil.zipFileWithPath(iosXRootDir, DIR_IOS_X + "_" + DateHelper.getNowSeconds() + ".zip");
            String iosXUrl = ZipUtil.uploadZipFile(iosXZipPath, FILE_BUKET_PATH);
            Map<String, Object> iosXZipFileMeta = ZipUtil.calculateZipFileMD5(iosXZipPath);
            update.set("iosXZipUrl", iosXUrl);
            update.set("iosXZipMd5", iosXZipFileMeta.get("fileMd5"));
            deleteFolder(new File(iosXRootDir));
        }

        update.set("zipTime", DateHelper.getNowSeconds());
        appThemeDao.updateData(themeData, update);

    }


    @RequireRole(2)
    @RequestMapping("/genZipUrl")
    public HttpResult genZipUrl(@RequestBody AppThemeCondition condition) {
        HttpResult result = new HttpResult();
        try {

            if(ServerConfig.isProduct()){
                return result.error("正式服不能直接生成, 请在测试服测好再更新zipUrl");
            }

            if (StringUtils.isEmpty(condition.getDocId())) {
                logger.error("The getDocId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            logger.info("genZipUrl docId={} data={}", condition.getDocId(), JSON.toJSONString(condition));
            AppThemeData themeData = appThemeDao.findData(condition.getDocId());
            if (Objects.isNull(themeData)) {
                logger.error("AppThemeData template is empty. id={}", condition.getDocId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }


            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    asyncGenZipUrl(condition.getDocId());
                }
            });


        } catch (Exception e) {
            logger.error("genZipUrl error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        try {
            //
            String fileMd5 = Md5Utils.getISMD5String(file.getInputStream());
            String fileUrl = sourceDuplicateRedis.getSourceStrData(fileMd5);
            if(!StringUtils.isEmpty(fileUrl)){
                return fileUrl;
            }

            fileUrl = AWSUploadUtils.upload(file, FILE_BUKET_PATH);
            sourceDuplicateRedis.setSourceStrData(fileMd5, fileUrl);
            return fileUrl;
        }catch (IOException e){
            logger.error("uploading failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }


}
