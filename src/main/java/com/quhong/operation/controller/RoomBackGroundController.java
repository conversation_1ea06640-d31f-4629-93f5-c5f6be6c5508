package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.BaseController;
import com.quhong.handler.HttpEnvData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.RoomBackGroundService;
import com.quhong.operation.share.condition.RoomBGCondition;
import com.quhong.operation.share.dto.RoomBGDTO;
import com.quhong.operation.utils.AWSUploadUtils;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 房间背景图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/roomBackGround", produces = MediaType.APPLICATION_JSON_VALUE)
public class RoomBackGroundController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(RoomBackGroundController.class);
    private final static String filePath = "theme/";

    @Resource
    private RoomBackGroundService roomBackGroundService;

    @RequireRole
    @RequestMapping("/configType")
    public String backGroundType(HttpEnvData envData){

        return createResult(HttpCode.SUCCESS, roomBackGroundService.backGroundType());
    }


    @RequireRole
    @RequestMapping("/page")
    public String RoomBGPageList(@RequestBody RoomBGCondition roomBGCondition) {

        logger.info("get RoomBGPageList {}", roomBGCondition);

        return createResult(HttpCode.SUCCESS, roomBackGroundService.roomBGPageList(roomBGCondition));

    }

    @RequireRole
    @PostMapping("/addRoomBG")
    public String addRoomBG(@RequestBody RoomBGDTO roomBGDTO) {
        logger.info("addRoomBG {}", roomBGDTO);

        if (StringUtils.isEmpty(roomBGDTO.getTitle()) || StringUtils.isEmpty(roomBGDTO.getTitleAr()) ||
                StringUtils.isEmpty(roomBGDTO.getPreview()) || StringUtils.isEmpty(roomBGDTO.getBackground())) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }


        try {
            roomBackGroundService.addRoomBG(roomBGDTO);
            return createResult(HttpCode.SUCCESS, "");

        } catch (Exception e) {
            logger.error("addRoomBG error. msg = {}",e.getMessage(),e);
        }

        return createResult(HttpCode.SERVER_ERROR, "");
    }

    @RequireRole
    @PostMapping("/updateRoomBG")
    public String updateStartPage(@RequestBody RoomBGDTO roomBGDTO) {
        logger.info("updateRoomBG {}", roomBGDTO);
        if (StringUtils.isEmpty(roomBGDTO.getDocId())) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }


        try {
            roomBackGroundService.updateRoomBG(roomBGDTO);
            return createResult(HttpCode.SUCCESS, "");

        } catch (Exception e) {
            logger.error("updateRoomBG error. msg = {}",e.getMessage(),e);

        }
        return createResult(HttpCode.SERVER_ERROR, "");
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return OSSUploadUtils.upload(file, filePath);
    }

}
