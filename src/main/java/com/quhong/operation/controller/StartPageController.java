package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.StartPageServer;
import com.quhong.operation.share.dto.StartPageDto;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.StartPageVO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 闪屏配置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping("/startPage")
public class StartPageController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(StartPageController.class);
    private final static String filePath = "flash/";

    @Autowired
    private StartPageServer startPageServer;

    @RequireRole
    @RequestMapping("/page")
    public HttpResult<PageResultVO> startPageList(@RequestBody StartPageDto startPageDto) {
        Integer valid = startPageDto.getValid();
        Integer page = startPageDto.getPage();
        Integer pageSize = startPageDto.getPageSize();
        String search = startPageDto.getSearch();

        logger.info("get flashList valid={}, page={}, pageSize={}", valid, page, pageSize);

        HttpResult<PageResultVO> result = new HttpResult<>();

        try {

            //业务调用
            ApiResult<PageResultVO<StartPageVO>> apiResult = startPageServer.listStartPage(valid, page, pageSize, search);
            if(apiResult.isOK()){
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getMsg());

        } catch (Exception e) {
            logger.error("list topic friend error. msg = {}",e.getMessage(),e);
        }

        return result.error(HttpCode.SERVER_ERROR.getCode(),HttpCode.SERVER_ERROR.getMsg());

    }

    @RequireRole
    @PostMapping("/addStartPage")
    public HttpResult addStartPage(@RequestBody StartPageDto startPageDto) {
        logger.info("addStartPage {}", startPageDto);

        HttpResult result = new HttpResult();

        try {
            ApiResult apiResult  = startPageServer.addStartPage(startPageDto);
            if (apiResult.isOK()){
                return result.ok();
            }
            return result.error(apiResult.getMsg());

        } catch (Exception e) {
            logger.error("list topic friend error. msg = {}",e.getMessage(),e);

        }
        return result.error(HttpCode.SERVER_ERROR.getMsg());
    }

    @RequireRole
    @PostMapping("/updateStartPage")
    public HttpResult updateStartPage(@RequestBody StartPageDto startPageDto) {
        logger.info("updateStartPage {}", startPageDto);

        HttpResult result = new HttpResult();

        try {
            ApiResult apiResult  = startPageServer.updateStartPage(startPageDto);
            if (apiResult.isOK()){
                return result.ok();
            }
            return result.error(apiResult.getMsg());

        } catch (Exception e) {
            logger.error("list topic friend error. msg = {}",e.getMessage(),e);

        }
        return result.error(HttpCode.SERVER_ERROR.getMsg());
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return AWSUploadUtils.upload(file, filePath);
    }

}
