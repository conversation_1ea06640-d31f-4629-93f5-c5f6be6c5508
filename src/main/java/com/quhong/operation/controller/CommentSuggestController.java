package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.CommentSuggestData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.CommentSuggestService;
import com.quhong.operation.share.condition.ActivityComponentCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 朋友圈评论推荐接口
 */
@RestController
@RequestMapping(value = "/commentSuggest", produces = MediaType.APPLICATION_JSON_VALUE)
public class CommentSuggestController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(CommentSuggestController.class);

    @Resource
    private CommentSuggestService commentSuggestService;

    /**
     * 列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<CommentSuggestData>> list(@RequestBody ActivityComponentCondition condition) {
        logger.info("get list={}", JSONObject.toJSONString(condition));
        return HttpResult.getOk(commentSuggestService.list(condition));
    }

    /**
     * 新增
     */
    @RequireRole
    @RequestMapping("/add")
    public HttpResult<?> addData(@RequestBody CommentSuggestData dto) {
        logger.info("addData {}", JSONObject.toJSONString(dto));
        commentSuggestService.addData(dto);
        return HttpResult.getOk();
    }

    /**
     * 更新
     */
    @RequireRole
    @RequestMapping("/update")
    public HttpResult<?> updateData(@RequestBody CommentSuggestData dto) {
        logger.info("updateData {}", JSONObject.toJSONString(dto));
        commentSuggestService.updateData(dto);
        return HttpResult.getOk();
    }
}
