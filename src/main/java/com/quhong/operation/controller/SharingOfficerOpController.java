package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.SharingOfficerOpService;
import com.quhong.operation.server.WorldCupService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.dto.SharingOfficerDTO;
import com.quhong.operation.share.vo.WorldCupMatchVO;
import com.quhong.operation.share.vo.WorldCupTeamVO;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 分享官
 */

@RestController
@RequestMapping(value = "/sharingOfficer", produces = MediaType.APPLICATION_JSON_VALUE)
public class SharingOfficerOpController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(SharingOfficerOpController.class);
    private final static String filePath = "sharingOfficer/";

    @Resource
    private SharingOfficerOpService sharingOfficerOpService;

    @RequireRole
    @RequestMapping("/uploadLogList")
    public String uploadLogList(@RequestBody SharingOfficerDTO dto) {
        return createResult(HttpCode.SUCCESS, sharingOfficerOpService.sharingOfficerUploadLogList(dto));
    }

    @RequireRole
    @RequestMapping("/uploadLogUpdate")
    public String uploadLogUpdate(@RequestParam String uid, @RequestBody SharingOfficerDTO dto) {
        if (!CollectionUtils.isEmpty(dto.getRidList())) {
            sharingOfficerOpService.sharingOfficerUploadLogUpdateAll(uid, dto);
        } else {
            sharingOfficerOpService.sharingOfficerUploadLogUpdate(uid, dto);
        }
        return createResult(HttpCode.SUCCESS, new Object());
    }

    @RequireRole
    @RequestMapping("/lingGanList")
    public String lingGanList(@RequestBody SharingOfficerDTO dto) {
        return createResult(HttpCode.SUCCESS, sharingOfficerOpService.sharingOfficerLingGanList(dto));
    }

    @RequireRole
    @RequestMapping("/lingGanUpdate")
    public String lingGanUpdate(@RequestBody SharingOfficerDTO dto) {
        sharingOfficerOpService.sharingOfficerLingGanUpdate(dto);
        return createResult(HttpCode.SUCCESS, new Object());
    }

    @RequireRole
    @RequestMapping("/lingGanAdd")
    public String lingGanAdd(@RequestBody SharingOfficerDTO dto) {
        sharingOfficerOpService.sharingOfficerLingGanAdd(dto);
        return createResult(HttpCode.SUCCESS, new Object());
    }

    /**
     * 图片,视频上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return OSSUploadUtils.upload(file, filePath);
    }

}
