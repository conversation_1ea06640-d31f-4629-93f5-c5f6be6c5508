package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.MomentBackGroundService;
import com.quhong.operation.share.condition.MomentBGCondition;
import com.quhong.operation.share.dto.MomentBGDTO;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * moment背景图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/momentBackGround", produces = MediaType.APPLICATION_JSON_VALUE)
public class MomentBackGroundController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(MomentBackGroundController.class);
    private final static String filePath = "theme/";

    @Resource
    private MomentBackGroundService momentBackGroundService;



    @RequireRole
    @RequestMapping("/page")
    public String MomentBGPageList(@RequestBody MomentBGCondition condition) {

        logger.info("get MomentBGCondition {}", condition);

        return createResult(HttpCode.SUCCESS, momentBackGroundService.momentBGPageList(condition));

    }

    @RequireRole
    @PostMapping("/add")
    public String addMomentBG(HttpServletRequest request, @RequestBody MomentBGDTO dto) {
        String uid = request.getParameter("uid");
        logger.info("uid: {} addMomentBG {}", uid, dto);

        if (StringUtils.isEmpty(dto.getNameEn()) || StringUtils.isEmpty(dto.getNameAr()) ||
                dto.getBackgroundType() == null|| dto.getCostType() == null) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }


        try {
            momentBackGroundService.addMomentBG(uid, dto);
            return createResult(HttpCode.SUCCESS, "");

        } catch (Exception e) {
            logger.error("addRoomBG error. msg = {}",e.getMessage(),e);
        }

        return createResult(HttpCode.SERVER_ERROR, "");
    }

    @RequireRole
    @PostMapping("/update")
    public String updateMomentBG(@RequestBody MomentBGDTO dto) {
        logger.info("updateMomentBG {}", dto);
        if (dto.getId() == null) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }


        try {
            momentBackGroundService.updateMomentBG(dto);
            return createResult(HttpCode.SUCCESS, "");

        } catch (Exception e) {
            logger.error("updateRoomBG error. msg = {}",e.getMessage(),e);

        }
        return createResult(HttpCode.SERVER_ERROR, "");
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return OSSUploadUtils.upload(file, filePath);
    }

}
