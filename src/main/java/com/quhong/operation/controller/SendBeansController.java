package com.quhong.operation.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ServerType;
import com.quhong.mysql.data.PtgSendBeansData;
import com.quhong.mysql.data.PtgSendBeansFailedData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.constant.ChargeBeanApplyConstant;
import com.quhong.operation.dao.*;
import com.quhong.operation.enums.PtgSendBeansWeekEnum;
import com.quhong.operation.server.BeansManagerServer;
import com.quhong.operation.server.FileServer;
import com.quhong.operation.share.dto.RollCallDto;
import com.quhong.operation.share.mongobean.ChargeBeanApply;
import com.quhong.operation.share.mongobean.ChargeBeanFailureList;
import com.quhong.operation.share.mongobean.UserChargeBean;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.operation.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/15
 */
@RestController
@RequestMapping("/sendBeans")
public class SendBeansController {

    private static final Logger logger = LoggerFactory.getLogger(SendBeansController.class);
    private final String ES_FRIDAY_SALARY_CHARGE_TITLE = "operating room friday salary"; // 周五运营房薪资打钻
    private final String ES_SALARY_TITLE = "admin charge for sundry salary"; // 批量打钻
    private final List<String> EXAMINE_OPERATION_USER = Arrays.asList("5dd234301e23cd011be9e3e2", "65e6ed49c0781dcc6fd96c25", "62b9780a1fb34e1c3520cb90"); // 二次审核运营人员  alizhong baijun, yufengxia

    private static final List<String> WEEK_DAYS = Arrays.asList("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
    private static final Pattern intPattern = Pattern.compile("[^0-9]");

    @Value("${online:true}")
    private boolean online;

    @Autowired
    private BeansManagerServer beansManagerServer;
    @Autowired
    private FileServer fileServer;
    @Autowired
    private ChargeBeanApplyDao chargeBeanApplyDao;
    @Autowired
    private ChargeBeanFailureListDao chargeBeanFailureListDao;
    @Autowired
    private ManagerDao managerDao;
    @Autowired
    private PtgSendBeansFailedDao ptgSendBeansFailedDao;
    @Autowired
    private PtgSendBeansDao ptgSendBeansDao;

    /**
     * 周五运营房薪资打钻
     *
     * @param file 打钻名单
     * @param type 打钻类型
     * @param desc 打钻备注
     * @return 未打钻的错误信息
     */
    @RequireRole(3)
    @RequestMapping("/fridaySalaryBeans")
    public HttpResult<List<String>> fridaySalaryBeans(HttpServletRequest request, MultipartFile file, Integer type, String desc) {
        HttpResult<List<String>> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml,其余用户上传提示：您无操作权限，如有需求请联系技术人员
        Set<String> adminUidSet = getAdminUser(null);
        if (online && !adminUidSet.contains(uid)) {
            logger.error("fridaySalaryBeans no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        logger.info(" begin send friday salary beans. type = {} param desc={} ", type, desc);
        if (StringUtil.isEmptyOrBlank(desc)) {
            logger.error("send friday salary beans error.type = {} desc = {}", type, desc);
            return result.error("desc not is empty!");
        }
        if (null == file) {
            logger.info("upload file is empty");
            return result.error("upload file is empty");
        }

        ApiResult<List<String>> apiResult = beansManagerServer.fridaySendBeans(file, type, desc, ES_FRIDAY_SALARY_CHARGE_TITLE, uid);
        logger.info("fridaySendBeans result {}", apiResult);
        if (apiResult.isOK()) {
            return result.ok(apiResult.getData());
        } else {
            return result.error(apiResult.getMsg());
        }
    }

    /**
     * 获取周五打钻申请列表
     */
    @RequireRole(1)
    @RequestMapping("/friday_apply_list")
    public HttpResult<List<ChargeBeanApplyVO>> fridayApplyList(HttpServletRequest request) {
        HttpResult<List<ChargeBeanApplyVO>> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        logger.info("begin get friday apply list. uid={}", uid);
        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml 其余用户上传提示：您无操作权限，如有需求请联系技术人员
        Set<String> adminUidSet = getAdminUser(null);
        if (online && !adminUidSet.contains(uid)) {
            logger.error("friday apply list no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        List<ChargeBeanApplyVO> list = beansManagerServer.getChargeApplyData(null);
        return result.ok(list);
    }

    /**
     * 获取更新打钻名单申请列表
     */
    @RequireRole(1)
    @RequestMapping("/updateRollCallApplyList")
    public HttpResult<List<ChargeBeanApplyVO>> updateRollCallApplyList(HttpServletRequest request) {
        HttpResult<List<ChargeBeanApplyVO>> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        logger.info("begin get friday apply list. uid={}", uid);
        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml 其余用户上传提示：您无操作权限，如有需求请联系技术人员
        Set<String> adminUidSet = getAdminUser(null);
        if (online && !adminUidSet.contains(uid)) {
            logger.error("friday apply list no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        List<ChargeBeanApplyVO> list = beansManagerServer.getUpdateRollCallApplyList();
        return result.ok(list);
    }

    /**
     * 申请已读
     */
    @RequireRole(3)
    @RequestMapping("/read_apply")
    public HttpResult<Object> readApply(HttpServletRequest request, String applyId) {
        HttpResult<Object> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        logger.info("begin read apply. uid={} apply_id={}", uid, applyId);
        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml 其余用户上传提示：您无操作权限，如有需求请联系技术人员
        Set<String> adminUidSet = getAdminUser(null);
        if (online && !adminUidSet.contains(uid)) {
            logger.error("read apply no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        chargeBeanApplyDao.updateRead(applyId, ChargeBeanApplyConstant.READ);
        return result.ok();
    }

    /**
     * 获取周五打钻申请失败列表
     */
    @RequireRole(3)
    @RequestMapping("/friday_apply_reject")
    public HttpResult<List<ChargeBeanApplyVO>> fridayApplyReject(HttpServletRequest request) {
        HttpResult<List<ChargeBeanApplyVO>> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        logger.info("begin get friday apply reject data. uid={}", uid);
        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml 其余用户上传提示：您无操作权限，如有需求请联系技术人员
        Set<String> adminUidSet = getAdminUser(null);
        if (online && !adminUidSet.contains(uid)) {
            logger.error("friday apply reject no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        List<ChargeBeanApplyVO> list = beansManagerServer.getChargeApplyData(uid);
        return result.ok(list);
    }

    /**
     * 获取批量打钻申请列表
     */
    @RequireRole(3)
    @RequestMapping("/monitoring_apply_list")
    public HttpResult<List<ChargeBeanApplyVO>> monitoringApplyList(HttpServletRequest request) {
        HttpResult<List<ChargeBeanApplyVO>> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        logger.info("begin get friday apply reject data. uid={}", uid);
        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml 其余用户上传提示：您无操作权限，如有需求请联系技术人员
        Set<String> adminUidSet = getAdminUser(null);
        if (online && !adminUidSet.contains(uid)) {
            logger.error("friday apply reject no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        List<ChargeBeanApplyVO> list = beansManagerServer.getChargeApplyData();
        return result.ok(list);
    }

    /**
     * 通过打钻二次确认申请
     */
    @RequireRole(3)
    @RequestMapping("/allow_friday_apply")
    public HttpResult<List<String>> handlerAllowFridayApply(HttpServletRequest request, String applyId) {
        HttpResult<List<String>> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        logger.info("begin allow friday apply. uid={} apply_id={}", uid, applyId);
        //只能zhonglizhong、白俊进行操作
        if (online && !EXAMINE_OPERATION_USER.contains(uid)) {
            logger.error("allow_friday_apply no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        if (StringUtils.isEmpty(applyId)) {
            logger.error("allow_friday_apply param error. uid={} applyId={}", uid, applyId);
            return result.error("param error");
        }
        ApiResult<List<String>> apiResult = beansManagerServer.handlerFridaySendBeans(applyId, ChargeBeanApplyConstant.STATUS_AGREE);
        if (apiResult.isOK()) {
            return result.ok(apiResult.getData());
        } else {
            return result.error(apiResult.getMsg());
        }
    }

    /**
     * 驳回打钻二次确认申请
     */
    @RequireRole(3)
    @RequestMapping("/reject_friday_apply")
    public HttpResult<List<String>> handlerRejectFridayApply(HttpServletRequest request, String applyId) {
        HttpResult<List<String>> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        logger.info("begin reject friday apply. uid={} apply_id={}", uid, applyId);
        //只能zhonglizhong进行操作
        if (online && !EXAMINE_OPERATION_USER.contains(uid)) {
            logger.error("reject_friday_apply no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        if (StringUtils.isEmpty(applyId)) {
            logger.error("reject_friday_apply param error. uid={} applyId={}", uid, applyId);
            return result.error("param error");
        }
        ApiResult<List<String>> apiResult = beansManagerServer.handlerFridaySendBeans(applyId, ChargeBeanApplyConstant.STATUS_REJECT);
        if (apiResult.isOK()) {
            return result.ok(apiResult.getData());
        } else {
            return result.error(apiResult.getMsg());
        }
    }

    private Set<String> getAdminUser(String username) {
        Set<String> adminUidSet = new HashSet<>();
        String wenmiaofangUid = "5dba92651e23cd00c80b32bc";
        String zhonglizhongUid = "5dd234301e23cd011be9e3e2";
        String yufengxia = "62b9780a1fb34e1c3520cb90";
        String tuoheti = "636c9ab349fe791003d316ba";
        String baijun = "65e6ed49c0781dcc6fd96c25";
        String chenluyao = "667a3cccd530ac0dd025e113";
        String xumenghua = "67288d1b8bfeda82d33a4993";
        String zhangmengxuan = "682c4e3fc991ede3e27c4c05"; // 运营-张梦璇
        boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
        if (test) {
            wenmiaofangUid = "5e86cfc3eea604296b98a539";
            zhonglizhongUid = "623bdc51595e7bf13f730e88";
        }
        if (StringUtils.isEmpty(username)) {
            adminUidSet.add(wenmiaofangUid);
            adminUidSet.add(zhonglizhongUid);
            adminUidSet.add(yufengxia);
            adminUidSet.add(tuoheti);
            adminUidSet.add(baijun);
            adminUidSet.add(chenluyao);
            adminUidSet.add(xumenghua);
            adminUidSet.add(zhangmengxuan);
        } else if ("wenmiaofang".equals(username)) {
            adminUidSet.add(wenmiaofangUid);
        } else if ("zhonglizhong".equals(username)) {
            adminUidSet.add(zhonglizhongUid);
        } else if ("yufengxia".equals(username)) {
            adminUidSet.add(yufengxia);
        } else if ("tuoheti".equals(username)) {
            adminUidSet.add(tuoheti);
        } else if ("baijun".equals(username)) {
            adminUidSet.add(baijun);
        }else if ("chenluyao".equals(username)) {
            adminUidSet.add(chenluyao);
        }
        return adminUidSet;
    }

    /**
     * 批量打钻
     *
     * @param file 打钻名单
     * @param type 打钻类型
     * @param desc 打钻备注
     * @return 未打钻的错误信息
     */
    @RequireRole(3)
    @RequestMapping("/salaryBeans")
    public HttpResult<String> salaryBeans(HttpServletRequest request, MultipartFile file, Integer type, String desc) {
        logger.info("salaryBeans param desc={} ", desc);
        HttpResult<String> result = new HttpResult<>();
        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml 其余用户上传提示：您无操作权限，如有需求请联系技术人员
        String uid = request.getParameter("uid");
        Set<String> adminUidSet = getAdminUser(null);
        if (online && !adminUidSet.contains(uid)) {
            logger.error("friday apply reject no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        if (StringUtil.isEmptyOrBlank(desc)) {
            return result.error("desc not is empty!");
        }
        if (null == file) {
            logger.info("upload file is empty");
            return result.error("upload file is empty");
        }
        ApiResult<String> apiResult = beansManagerServer.salaryBeans(file, type, desc, ES_SALARY_TITLE, uid);
        logger.info("salaryBeans result {}", apiResult);
        if (apiResult.isOK()) {
            return result.ok(apiResult.getData());
        } else {
            return result.error(apiResult.getCode(), apiResult.getMsg());
        }
    }

    /**
     * 更新每天管理打钻名单
     *
     * @param file 打钻名单
     * @return 校验名单错误信息
     */
    @RequireRole(3)
    @RequestMapping("/updateEverydayFile")
    public HttpResult<List<String>> updateEverydaySendBeanFile(HttpServletRequest request, MultipartFile file) {
        HttpResult<List<String>> result = new HttpResult<>();
        return result.error("您无操作权限，如有需求请联系技术人员!");
//
//        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml 其余用户上传提示：您无操作权限，如有需求请联系技术人员
//        String uid = request.getParameter("uid");
//        Set<String> adminUidSet = getAdminUser(null);
//        if (online && !adminUidSet.contains(uid)) {
//            logger.error("friday apply reject no right to operate uid={}", uid);
//            return result.error("您无操作权限，如有需求请联系技术人员!");
//        }
//        if (null == file || examineName(file.getOriginalFilename())) {
//            return result.error("file is null or not what is “.xlsx” suffix");
//        }
//        InputStream is;
//        try {
//            List<String> errorList = examineEverydayFile(file.getInputStream());
//            if (!CollectionUtils.isEmpty(errorList)) {
//                return result.ok(errorList);
//            }
//            is = file.getInputStream();
//        } catch (IOException e) {
//            logger.error("{}", e.getMessage(), e);
//            return result.error("upload fail " + e.getMessage());
//        }
//        ApiResult<Boolean> apiResult = fileServer.updateFile(is, everydayBakPath);
//        logger.info("update everyday roll call result ：{}", apiResult);
//        String ADMIN_DIAMONDS_DAILY_TITLE = "admin diamonds daily";
//        String ADMIN_DIAMONDS_DAILY_DESC = "admin charge for everyday";
//        //插入数据apply
//        saveChargeBeanApply(uid, ADMIN_DIAMONDS_DAILY_TITLE, ADMIN_DIAMONDS_DAILY_DESC);
//        if (!apiResult.isOK()) {
//            return result.error(apiResult.getMsg());
//        }
//        return result.ok();
    }

    /**
     * 更新party girl打钻名单
     *
     * @param file 名单
     * @return 校验名单错误信息
     */
    @RequireRole(3)
    @RequestMapping("/updatePartyGirlFile")
    public HttpResult<List<String>> updatePartyGirlSendBeanFile(HttpServletRequest request, MultipartFile file) {
        HttpResult<List<String>> result = new HttpResult<>();
        // 仅限专人操作，运营系统账号：wenmiaofang / zhonglizhong / guoyanshan / yangml 其余用户上传提示：您无操作权限，如有需求请联系技术人员
        String uid = request.getParameter("uid");
        logger.info("update party girl file . uid={}", uid);
        Set<String> adminUidSet = getAdminUser(null);
        if (online && !adminUidSet.contains(uid)) {
            logger.error("friday apply reject no right to operate uid={}", uid);
            return result.error("您无操作权限，如有需求请联系技术人员!");
        }
        if (null == file || examineName(file.getOriginalFilename())) {
            return result.error("file is null or not what is “.xlsx” suffix");
        }
        try {
            List<String> errorList = examinePartyGirlFile(file);
            if (!CollectionUtils.isEmpty(errorList)) {
                return result.ok(errorList);
            }
            int nowTime = DateHelper.getNowSeconds();
            String PARTY_GIRL_TITLE = "admin charge for party girl";
            String PARTY_GIRL_DESC = "Party Girl support";
            //插入数据apply
            String applyId = saveChargeBeanApply(uid, PARTY_GIRL_TITLE, PARTY_GIRL_DESC);
            for (String weekDay : WEEK_DAYS) {
                Map<String, List<String>> listMap = ExcelUtils.readExcel(file.getInputStream(), weekDay);
                List<PtgSendBeansData> list = new ArrayList<>();
                for (String header : listMap.keySet()) {
                    if (!header.startsWith("rid")) {
                        logger.error("header not start with 'rid' header={}", header);
                        continue;
                    }
                    List<String> ridList = listMap.get(header);
                    if (CollectionUtils.isEmpty(ridList)) {
                        logger.error("{} {} send bean fail, data is empty", weekDay, PARTY_GIRL_TITLE);
                        continue;
                    } else {
                        // 去重
                        ridList = ridList.stream().distinct().collect(Collectors.toList());
                    }
                    for (String strRid : ridList) {
                        //去掉非数字的所有字符串
                        strRid = intPattern.matcher(strRid.trim()).replaceAll("");
                        if (StringUtils.isEmpty(strRid)) {
                            continue;
                        }
                        int rid;
                        try {
                            rid = Integer.parseInt(strRid);
                        } catch (Exception e) {
                            logger.error("strRid from string to integer error. strRid = {} {}", strRid, e.getMessage(), e);
                            continue;
                        }
                        list.add(new PtgSendBeansData(applyId, PtgSendBeansWeekEnum.getCodeByName(weekDay), rid, 0, 0, uid, nowTime));
                    }
                    ptgSendBeansDao.batchInsert(list);
                }
            }
        } catch (IOException e) {
            logger.error("update party girl fail {}", e.getMessage(), e);
            return result.error("update party girl fail " + e.getMessage());
        }
        return result.ok();
    }

    private String saveChargeBeanApply(String uid, String title, String desc) {
        int nowTime = DateHelper.getNowSeconds();
        ChargeBeanApply chargeBeanApply = new ChargeBeanApply();
        chargeBeanApply.setEsTitle(title);
        chargeBeanApply.setDesc(desc);
        chargeBeanApply.setUid(uid);
        chargeBeanApply.setCtime(nowTime);
        chargeBeanApply.setMtime(nowTime);
        chargeBeanApply.setStatus(ChargeBeanApplyConstant.STATUS_PENDING);
        chargeBeanApply.setChargeType(1);
        chargeBeanApply.setReadStatus(ChargeBeanApplyConstant.UN_READ);
        chargeBeanApply.setUsername(managerDao.getDataByUid(uid).getAccount());
        return chargeBeanApplyDao.saveAndReturnId(chargeBeanApply);
    }

    @RequireRole(1)
    @RequestMapping("/downloadEverydayFile")
    public void downloadEverydayFile(HttpServletResponse response) {
//        downloadExcel(response, everydayPath);
    }

    @RequireRole(1)
    @RequestMapping("/downloadEverydayBakFile")
    public void downloadEverydayBakFile(HttpServletResponse response) {
//        downloadExcel(response, everydayBakPath);
    }


    /**
     * 批量打钻模板下载
     */
    @RequireRole(1)
    @RequestMapping("/downloadBulkDiamondsFile")
    public void downloadBulkDiamondsFile(HttpServletResponse response) {
        ExcelUtils.exportExcel(response, null, IncreaseDiamondVo.class, "bulk_diamonds_file", "批量打钻模板");
    }

    /**
     * 批量打钻申请详情下载
     */
    @RequireRole(1)
    @RequestMapping("/downloadBulkDiamondsApply")
    public void downloadBulkDiamondsFile(HttpServletResponse response, @RequestBody RollCallDto rollCallDto) {
        String uid = rollCallDto.getUid();
        String applyId = rollCallDto.getApplyId();
        logger.info("uid={},applyId={}", uid, applyId);
        ChargeBeanApply apply = chargeBeanApplyDao.getApply(applyId);
        List<IncreaseDiamondVo> diamondVos = new ArrayList<>();
        if (Objects.nonNull(apply) && !CollectionUtils.isEmpty(apply.getApplyList())) {
            List<UserChargeBean> applyList = apply.getApplyList();
            for (UserChargeBean chargeBean : applyList) {
                IncreaseDiamondVo diamondVo = new IncreaseDiamondVo();
                diamondVo.setRid(chargeBean.getRid());
                diamondVo.setBean(chargeBean.getNum());
                diamondVos.add(diamondVo);
            }
        } else {
            logger.info("要导出的数据为空");
        }
        ExcelUtils.exportExcel(response, diamondVos, IncreaseDiamondVo.class, "bulk_diamonds_apply_roll_call", "批量打钻申请名单");
    }

    /**
     * ptg停止打钻名单下载
     */
    @RequireRole(1)
    @RequestMapping("/downloadPtgSendBeansFailed")
    public void downloadPtgSendBeansFailed(HttpServletResponse response, @RequestBody RollCallDto rollCallDto) {
        String uid = rollCallDto.getUid();
        logger.info("download ptg send beans failed list. uid={}", uid);
        List<PtgSendBeansFailedData> list = ptgSendBeansFailedDao.findList(getWeekStartTime(), getWeekEndTime());
        List<PtgSendBeansFailedVO> failedVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (PtgSendBeansFailedData data : list) {
                PtgSendBeansFailedVO vo = new PtgSendBeansFailedVO();
                vo.setRid(data.getRid());
                vo.setType(data.getType() == 0 ? "暂停打钻" : "移除PTG");
                vo.setRemark(data.getRemark());
                vo.setCtime(com.quhong.operation.utils.DateHelper.ARABIAN.dateToStr(new Date(data.getCtime() * 1000L)));
                failedVOList.add(vo);
            }
        } else {
            logger.info("要导出的数据为空");
        }
        ExcelUtils.exportExcel(response, failedVOList, PtgSendBeansFailedVO.class, "ptg_send_beans_failed", "本周暂停打钻和移除PTG的名单");
    }

    /**
     * 获取本周的第一天的时间戳
     */
    private Integer getWeekStartTime() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        Date time = cal.getTime();
        return stringDateToStampSecond(new SimpleDateFormat("yyyy-MM-dd").format(time) + " 00:00:00");
    }

    /**
     * 获取本周的最后一天的时间戳
     */
    private Integer getWeekEndTime() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        Date time = cal.getTime();
        return stringDateToStampSecond(new SimpleDateFormat("yyyy-MM-dd").format(time) + " 23:59:59");
    }

    private Integer stringDateToStampSecond(String strDate) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = format.parse(strDate);
            return (int) (date.getTime() / 1000) - 8 * 60 * 60;
        } catch (Exception e) {
            logger.error("string date to stamp second error. strDate={}", strDate);
            return 0;
        }
    }

    /**
     * 名单下载
     */
    @RequireRole(1)
    @RequestMapping("/downloadLatestFile")
    public void downloadLatestFile(HttpServletResponse response) {
        List<IncreaseDiamondVo> diamondVoList = new ArrayList<>();
        ChargeBeanApply latestOne = chargeBeanApplyDao.getLatestOne(ES_SALARY_TITLE);
        if (Objects.nonNull(latestOne) && !CollectionUtils.isEmpty(latestOne.getApplyList())) {
            List<UserChargeBean> applyList = latestOne.getApplyList();
            for (UserChargeBean bean : applyList) {
                IncreaseDiamondVo diamondVo = new IncreaseDiamondVo();
                diamondVo.setRid(bean.getRid());
                diamondVo.setBean(bean.getNum());
                diamondVoList.add(diamondVo);
            }
        }
        ExcelUtils.exportExcel(response, diamondVoList, IncreaseDiamondVo.class, "latest_roll_call", "名单下载");
    }

    /**
     * 周五薪资打钻名单下载
     */
    @RequireRole(1)
    @RequestMapping("/downloadFridayLatestFile")
    public void downloadFridayLatestFile(HttpServletResponse response) {
        List<FridaySendBeansVO> voList = new ArrayList<>();
        ChargeBeanApply latestOne = chargeBeanApplyDao.getLatestOne(ES_FRIDAY_SALARY_CHARGE_TITLE);
        List<String> twoThousandBeansRids = new ArrayList<>();
        List<String> fourThousandBeansRids = new ArrayList<>();
        List<String> sevenThousandBeansRids = new ArrayList<>();
        List<String> tenThousandBeansRids = new ArrayList<>();
        if (Objects.nonNull(latestOne) && !CollectionUtils.isEmpty(latestOne.getApplyList())) {
            List<UserChargeBean> applyList = latestOne.getApplyList();
            for (UserChargeBean bean : applyList) {
                if (Integer.valueOf(2000).equals(bean.getNum())) {
                    twoThousandBeansRids.add(bean.getRid());
                    continue;
                }
                if (Integer.valueOf(4000).equals(bean.getNum())) {
                    fourThousandBeansRids.add(bean.getRid());
                    continue;
                }
                if (Integer.valueOf(7000).equals(bean.getNum())) {
                    sevenThousandBeansRids.add(bean.getRid());
                    continue;
                }
                if (Integer.valueOf(10000).equals(bean.getNum())) {
                    tenThousandBeansRids.add(bean.getRid());
                }
            }

            int maxLength = Collections.max(Arrays.asList(twoThousandBeansRids.size(), fourThousandBeansRids.size(), sevenThousandBeansRids.size(), tenThousandBeansRids.size()));
            for (int i = 0; i < maxLength; i++) {
                FridaySendBeansVO vo = new FridaySendBeansVO();
                vo.setTwoThousandBeansRid(i < twoThousandBeansRids.size() ? twoThousandBeansRids.get(i) : null);
                vo.setFourThousandBeansRid(i < fourThousandBeansRids.size() ? fourThousandBeansRids.get(i) : null);
                vo.setSevenThousandBeansRid(i < sevenThousandBeansRids.size() ? sevenThousandBeansRids.get(i) : null);
                vo.setTenThousandBeansRid(i < tenThousandBeansRids.size() ? tenThousandBeansRids.get(i) : null);
                voList.add(vo);
            }
        }
        ExcelUtils.exportExcel(response, voList, FridaySendBeansVO.class, "FridaySalarySendBeansForm", "名单下载");
    }

    @RequireRole(1)
    @RequestMapping("/downloadBulkDiamondsFailureFile")
    public void downloadBulkDiamondsFailureFile(HttpServletResponse response, @RequestBody ChargeBeanFailureList failureList) {

        logger.info("downloadBulkDiamondsFailureFile param batchNo={},uid={}", failureList.getBatchNo(), failureList.getUid());
        List<ChargeBeanFailureList> failureLists = chargeBeanFailureListDao.getFailureList(failureList.getBatchNo(), failureList.getUid());
        List<ChargeBeanFailureListVo> failureListVos = new ArrayList<>();
        for (ChargeBeanFailureList chargeBeanFailureList : failureLists) {
            ChargeBeanFailureListVo chargeBeanFailureListVo = new ChargeBeanFailureListVo();
            BeanUtils.copyProperties(chargeBeanFailureList, chargeBeanFailureListVo);
            failureListVos.add(chargeBeanFailureListVo);
        }
        logger.info(failureListVos.toString());
        ExcelUtils.exportExcel(response, failureListVos, ChargeBeanFailureListVo.class, "bulk_diamonds_failure_file", "打钻失败名单");
        logger.info("method downloadBulkDiamondsFailureFile done");
    }

    @RequireRole(1)
    @RequestMapping("/downloadPartyGirlFile")
    public void downloadPartyGirlFile(HttpServletResponse response) {
        Map<String, List<PtgSendBeansVO>> map = new HashMap<>();
        for (String strWeek : WEEK_DAYS) {
            Integer week = PtgSendBeansWeekEnum.getCodeByName(strWeek);
            List<PtgSendBeansData> list = ptgSendBeansDao.findList(week, 1);
            List<PtgSendBeansVO> voList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(k -> {
                    PtgSendBeansVO vo = new PtgSendBeansVO();
                    vo.setRid(k.getRid());
                    voList.add(vo);
                });
            }
            map.put(strWeek, voList);
        }
        exportExcel(response, map, PtgSendBeansVO.class, "ptg_send_beans_list", WEEK_DAYS);
    }

    public <T> void exportExcel(HttpServletResponse response, Map<String, List<PtgSendBeansVO>> dataMap, Class<T> entityClass, String fileName, List<String> sheetNames) {
        try {
            ExcelUtils.encodeFilename(response, fileName);
            int index = 0;
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
            for (String sheetName : sheetNames) {
                WriteSheet sheet = EasyExcel.writerSheet(index, sheetName).head(entityClass).build();
                excelWriter.write(dataMap.get(sheetName), sheet);
                index++;
            }
            excelWriter.finish();
        } catch (IOException e) {
            logger.error("export excel error, error message={}", e.getMessage());
            e.printStackTrace();
        }
    }

    @RequireRole(1)
    @RequestMapping("/downloadPartyGirlRejectFile")
    public void downloadPartyGirlRejectFile(HttpServletResponse response, String applyId) {
        Map<String, List<PtgSendBeansVO>> map = new HashMap<>();
        for (String strWeek : WEEK_DAYS) {
            Integer week = PtgSendBeansWeekEnum.getCodeByName(strWeek);
            List<PtgSendBeansData> list = ptgSendBeansDao.findList(applyId, week, 2);
            List<PtgSendBeansVO> voList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(k -> {
                    PtgSendBeansVO vo = new PtgSendBeansVO();
                    vo.setRid(k.getRid());
                    voList.add(vo);
                });
            }
            map.put(strWeek, voList);
        }
        exportExcel(response, map, PtgSendBeansVO.class, "ptg_send_beans_reject_list", WEEK_DAYS);
    }

    @RequireRole(1)
    @RequestMapping("/downloadPartyGirlBakFile")
    public void downloadPartyGirlBakFile(HttpServletResponse response, String applyId) {
        Map<String, List<PtgSendBeansVO>> map = new HashMap<>(8);
        for (String strWeek : WEEK_DAYS) {
            Integer week = PtgSendBeansWeekEnum.getCodeByName(strWeek);
            List<PtgSendBeansData> list = ptgSendBeansDao.findList(applyId, week, 0);
            List<PtgSendBeansVO> voList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(k -> {
                    PtgSendBeansVO vo = new PtgSendBeansVO();
                    vo.setRid(k.getRid());
                    voList.add(vo);
                });
            }
            map.put(strWeek, voList);
        }
        exportExcel(response, map, PtgSendBeansVO.class, "ptg_send_beans_need_confirmed_list", WEEK_DAYS);
    }

    /**
     * 下载本地文件
     *
     * @param response 请求的响应
     * @param path     文件路径
     */
    private void downloadExcel(HttpServletResponse response, String path) {
        OutputStream servletOS = null;
        ByteArrayOutputStream outStream = null;
        FileInputStream fis = null;
        try {
            File file = new File(path);
            System.out.println(file.getName());
            response.setContentType("application/octet-stream");
            response.setHeader("name", file.getName());
            response.addHeader("Content-Disposition", "inline; filename=\"" + URLEncoder.encode(file.getName(), "UTF-8") + "\"");
            servletOS = response.getOutputStream();
            outStream = new ByteArrayOutputStream();
            fis = new FileInputStream(file);
            byte[] b = new byte[1024];
            int len;
            while ((len = fis.read(b)) != -1) {
                outStream.write(b, 0, len);
            }
            servletOS.write(outStream.toByteArray());
            servletOS.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != servletOS) {
                    servletOS.close();
                }
                if (null != outStream) {
                    outStream.close();
                }
                if (null != fis) {
                    fis.close();
                }
            } catch (IOException e) {
                logger.info("{}", e.getMessage(), e);
            }
        }

    }

    /**
     * 检查文件内容格式
     *
     * @return 错误信息
     */
    private List<String> examinePartyGirlFile(MultipartFile file) throws IOException {
        List<String> errorList = new ArrayList<>();
        String[] weekDays = new String[]{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"};
        for (String weekDay : weekDays) {
            Map<String, List<String>> listMap = ExcelUtils.readExcel(file.getInputStream(), weekDay);
            if (CollectionUtils.isEmpty(listMap)) {
                errorList.add(weekDay + "sheet name not found");
                continue;
            }
            for (String header : listMap.keySet()) {
                if (!header.startsWith("rid")) {
                    logger.error("header not start with 'rid' header={}", header);
                    errorList.add("header not start with 'rid' header=" + header);
                    continue;
                }
                List<String> ridList = listMap.get(header);
                for (String rid : ridList) {
                    try {
                        Integer.parseInt(rid);
                    } catch (Exception e) {
                        errorList.add("rid is not number, rid=" + rid);
                    }
                }
            }
        }
        return errorList;
    }

    /**
     * 检查每天管理打钻名单格式
     *
     * @return 错误信息
     */
    private List<String> examineEverydayFile(InputStream inputStream) {
        List<String> errorList = new ArrayList<>();
        Map<String, List<String>> listMap = ExcelUtils.readExcel(inputStream);
        for (String header : listMap.keySet()) {
            List<String> ridList = listMap.get(header);
            if (CollectionUtils.isEmpty(ridList)) {
                break;
            }
            if (!header.startsWith("rids:")) {
                errorList.add("heard not is 'rids:' prefix header=" + header);
                continue;
            }
            String beanNum = header.substring("rids:".length());
            try {
                Integer.parseInt(beanNum.trim());
            } catch (Exception e) {
                errorList.add("column bean value exception value=" + header);
            }
            for (String rid : ridList) {
                try {
                    Integer.parseInt(rid);
                } catch (Exception e) {
                    errorList.add("rid is not number, rid=" + rid);
                }
            }
        }
        return errorList;
    }

    /**
     * 判断文件名是否是xlsx为后缀的
     *
     * @param originalName 原文件名
     * @return 是否
     */
    private boolean examineName(String originalName) {
        if (null == originalName) {
            return false;
        }
        return !originalName.endsWith(".xlsx");
    }

}
