package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.AdvanceGiftService;
import com.quhong.operation.server.LuckyGiftService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.dto.AdvanceGiftDTO;
import com.quhong.operation.share.vo.LuckyGiftVO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 进阶解锁礼物配置
 */

@RestController
@RequestMapping(value = "/advanceGift", produces = MediaType.APPLICATION_JSON_VALUE)
public class AdvanceGiftController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(AdvanceGiftController.class);

    @Resource
    private AdvanceGiftService advanceGiftService;


    @RequireRole
    @RequestMapping("/list")
    public String getDataList(@RequestBody AdvanceGiftDTO dto) {
        logger.info("getDataList {}", dto);
        return createResult(HttpCode.SUCCESS, advanceGiftService.getDataList(dto));
    }

    @RequireRole
    @PostMapping("/addData")
    public String addData(@RequestBody AdvanceGiftDTO dto) {
        try {
            dto.setUpdate(false);
            logger.info("add advanceGift Data {}", dto);
            advanceGiftService.addOrUpdateData(dto);
            return createResult(HttpCode.SUCCESS, "");
        } catch (CommonException e) {
            return createResult(e.getHttpCode(), null);
        } catch (Exception e) {
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateData(@RequestBody AdvanceGiftDTO dto) {
        try {
            dto.setUpdate(true);
            logger.info("updateData advanceGift Data {}", dto);
            advanceGiftService.addOrUpdateData(dto);
            return createResult(HttpCode.SUCCESS, "");
        } catch (CommonException e) {
            return createResult(e.getHttpCode(), null);
        } catch (Exception e) {
            throw e;
        }
    }


    @RequireRole
    @PostMapping("/deleteData")
    public String deleteData(@RequestBody AdvanceGiftDTO dto) {
        try {
            logger.info("delete advanceGift Data {}", dto);
            advanceGiftService.deleteData(dto);
            return createResult(HttpCode.SUCCESS, "");
        } catch (CommonException e) {
            return createResult(e.getHttpCode(), null);
        } catch (Exception e) {
            throw e;
        }
    }



}
