package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.GiftRecommendService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.dto.FloatScreenSourceDTO;
import com.quhong.operation.share.dto.GiftRecommendDTO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 浮萍图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/giftRecommend", produces = MediaType.APPLICATION_JSON_VALUE)
public class GiftRecommendController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(GiftRecommendController.class);
    private final static String filePath = "gift/";

    @Resource
    private GiftRecommendService giftRecommendService;



    @RequireRole
    @RequestMapping("/list")
    public String giftRecommendList(@RequestBody BaseCondition condition) {

        logger.info("get giftRecommendList {}", condition);
        return createResult(HttpCode.SUCCESS, giftRecommendService.giftRecommendList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public String addGiftRecommendData(@RequestBody GiftRecommendDTO dto) {
        logger.info("addGiftRecommendData {}", dto);
        try {
            giftRecommendService.addGiftRecommendData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateGiftRecommendData(@RequestBody GiftRecommendDTO dto) {
        logger.info("updateGiftRecommendData {}", dto);
        try {
            if (StringUtils.isEmpty(dto.getDocId())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }
            giftRecommendService.updateGiftRecommendData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return AWSUploadUtils.upload(file, filePath);
    }

}
