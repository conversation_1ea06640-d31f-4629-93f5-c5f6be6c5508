package com.quhong.operation.controller;

import com.quhong.operation.common.HttpResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/6/30
 */
@RestController
@RequestMapping("/error")
public class ErrorController {

    @RequestMapping("/notLogin")
    public HttpResult<Void> notLogin() {
        return new HttpResult<Void>().error(40, "Please login!");
    }

    @RequestMapping("/noRole")
    public HttpResult<Void> noRole() {
        return new HttpResult<Void>().error(40, "api permission denied!");
    }

}
