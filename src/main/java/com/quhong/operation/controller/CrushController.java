package com.quhong.operation.controller;

import com.quhong.core.utils.DateHelper;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.CrushReportServer;
import com.quhong.operation.share.vo.crush.SuperLikeVO;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/crush")
public class CrushController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CrushController.class);

    @Resource
    private CrushReportServer crushReportServer;

    /**
     * 超级喜欢报表
     *
     * @param start  开始日期
     * @param end    结束日期
     * @param app
     * @param user   1 新用户 0 老用户
     * @param gender 1 男用户 2 女用户
     * @return
     */
    @RequireRole(3)
    @RequestMapping("/super_like_report")
    public String superLikeReport(String start, String end, Integer app, Integer user, Integer gender) {
        try {
            logger.info("begin get crush super like report. start={} end={} app={} user={} gender={}",
                    start, end, app, user, gender);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.info("get crush super like report param error. start={} end={} app={} user={} gender={}",
                        start, end, app, user, gender);
                return createResult(HttpCode.PARAM_ERROR, null);
            }
            if (app == null) {
                app = -1;
            }
            if (user == null) {
                user = -1;
            }
            if (gender == null) {
                gender = -1;
            }
            //2、业务调用
            List<DayTimeData> dayList = DateHelper.ARABIAN.getContinuesDays(start, end);
            List<SuperLikeVO> likeList = new ArrayList<>();
            for (DayTimeData dayTimeData : dayList) {
                SuperLikeVO likeVO = crushReportServer.superLikeReport(dayTimeData, app, user, gender);
                likeList.add(likeVO);
            }
            return createResult(HttpCode.SUCCESS, likeList);
        } catch (Exception e) {
            logger.error("get crush super like report error. {}", e.getMessage(), e);
        }
        return createResult(HttpCode.SERVER_ERROR, null);
    }

    /**
     * 超级喜欢报表下载
     * @param response
     * @param start 开始日期
     * @param end 结束日期
     * @param app
     * @param user 1 新用户 0 老用户
     * @param gender 1 男用户 2 女用户
     */
    @RequireRole(3)
    @RequestMapping("/super_like_report/download")
    public void downloadSuperLike(HttpServletResponse response, String start, String end, Integer app, Integer user, Integer gender) {
        try {
            logger.info("begin download crush super like report. start={} end={} app={} user={} gender={}",
                    start, end, app, user, gender);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.info("download crush super like report param error. start={} end={} app={} user={} gender={}",
                        start, end, app, user, gender);
                response.getWriter().write(createResult(HttpCode.PARAM_ERROR, null));
                return;
            }
            if (app == null) {
                app = -1;
            }
            if (user == null) {
                user = -1;
            }
            if (gender == null) {
                gender = -1;
            }
            //2、业务调用
            List<DayTimeData> dayList = DateHelper.ARABIAN.getContinuesDays(start, end);
            List<SuperLikeVO> likeList = new ArrayList<>();
            for (DayTimeData dayTimeData : dayList) {
                SuperLikeVO likeVO = crushReportServer.superLikeReport(dayTimeData, app, user, gender);
                likeList.add(likeVO);
            }
            //3、报表下载
            ExcelUtils.exportExcel(response, likeList, SuperLikeVO.class, "super_like_report", "super_like");
            logger.info("finish download crush super like report !!!");
        } catch (IOException e) {
            logger.error("download crush super like report error. {}", e.getMessage(), e);
        }
    }

}
