package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.BadWordData;
import com.quhong.mysql.data.QueenDiscountData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.QueenDiscountServer;
import com.quhong.operation.share.condition.BadWordCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.QueenDiscountVO;
import com.quhong.service.mysql.BadWordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 脏词词库
 *
 * <AUTHOR>
 * @date 2022/08/01
 */

@RestController
@RequestMapping("/queen")
public class QueenDiscountController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(QueenDiscountController.class);

    @Resource
    private QueenDiscountServer queenDiscountServer;

    /**
     *
     * @param
     * @return
     */
    @RequireRole(2)
    @RequestMapping("discountList")
    public HttpResult<PageResultVO<QueenDiscountVO>> listQueenDiscount(@RequestParam int page, int pageSize, int search) {
        logger.info("get discountList page={}, pageSize={} search={}", page, pageSize, search);
        HttpResult<PageResultVO<QueenDiscountVO>> result = new HttpResult<>();
        PageResultVO<QueenDiscountVO> pageVO = new PageResultVO<>();
        List<QueenDiscountVO> dataList = queenDiscountServer.listQueenDiscount(page, pageSize, search);
        pageVO.setList(dataList);
        pageVO.setTotal(queenDiscountServer.countQueenDiscount(search));
        return result.ok(pageVO);
    }

    /**
     * 修改核销状态
     *
     * @param recordId id
     * @return
     */
    @RequireRole(2)
    @RequestMapping("updateDiscount")
    public HttpResult updateDiscount(@RequestParam int recordId, int writeOff) {
        logger.info("updateDiscount id={} word={}", recordId, writeOff);
        HttpResult result = new HttpResult();
        try {
            QueenDiscountData data = new QueenDiscountData();
            data.setId(recordId);
            data.setWriteOff(writeOff);
            queenDiscountServer.updateQueenDiscount(data);
        } catch (Exception e) {
            logger.error("updateDiscount {}", e.getMessage(), e);
            return result.error(HttpCode.SERVER_ERROR.getMsg());
        }
        return result.ok();
    }

}
