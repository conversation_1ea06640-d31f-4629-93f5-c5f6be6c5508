package com.quhong.operation.controller;

import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.DashboardServer;
import com.quhong.operation.share.vo.DashboardListVO;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/7/29
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    private final static Logger logger = LoggerFactory.getLogger(DashboardController.class);

    @Autowired
    private DashboardServer dashboardServer;

    @RequireRole(3)
    @RequestMapping("/statAllDashboardData")
    public HttpResult<DashboardListVO> statAllDashboardData (String startDate, String endDate) {
        logger.info("method getYesterdayOnlineInfo param startDate={}, endDate={}", startDate, endDate);
        HttpResult<DashboardListVO> result = new HttpResult<>();
        ApiResult<DashboardListVO> apiResult = new ApiResult<>();
        return result.error(apiResult.error("statAllDashboardData 功能已经废弃").getMsg());

//        Integer endSeconds = DateHelper.ARABIAN.getEndSeconds(endDate);
//        Integer startSeconds = DateHelper.ARABIAN.getStartSeconds(startDate, endSeconds);
//
//        ApiResult<DashboardListVO> apiResult = dashboardServer.statAllDashboardData(startSeconds, endSeconds);
//        logger.info("invoke statOnlineActiveCondition result {}", apiResult);
//        if (apiResult.isOK()) {
//            return result.ok(apiResult.getData());
//        }
//        return result.error(apiResult.getMsg());
    }



}
