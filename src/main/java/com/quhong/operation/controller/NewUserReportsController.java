package com.quhong.operation.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.quhong.data.ActorData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BeautifulRidDao;
import com.quhong.mongo.dao.CommonDao;
import com.quhong.mongo.data.BeautifulRidData;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.VipInfoData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.server.report.MoneyReportServer;
import com.quhong.operation.server.report.NewUserReportServer;
import com.quhong.operation.share.mongobean.LastLogin;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.Country;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;


/**
 * 新用户数据报表
 */
@RestController
@RequestMapping("/reports")
public class NewUserReportsController {

    private static final Logger logger = LoggerFactory.getLogger(NewUserReportsController.class);

    @Autowired
    private NewUserReportServer newUserReportServer;
    @Autowired
    private MoneyReportServer moneyReportServer;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Autowired
    private CommonDao commonDao;
    @Autowired
    private VipInfoDao vipInfoDao;

    /**
     * 礼物+充值报表下载
     *
     * @param response  response
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(4)
    @RequestMapping("/giftAndPayReports")
    public void giftAndPayReports(HttpServletResponse response,
                                  String startDate, String endDate, Integer os, Integer app) {
        logger.info("giftAndPayReports param s={}, e={}, os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);

        List<GiftAndPlayVO> result = newUserReportServer.getGiftAndPayList(timeArr[0], --timeArr[1], -1, null == os ? -1 : os, null == app ? -1 : app);
        List<MonthlyRechargeVO> lists = moneyReportServer.getMonthlyRechargeInfo(timeArr[0], timeArr[1]);

        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "gift_and_pay");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(result, EasyExcel.writerSheet(0, "礼物+充值").head(GiftAndPlayVO.class).build());
            excelWriter.write(lists, EasyExcel.writerSheet(1, "月份线上充值用户详情数据").head(MonthlyRechargeVO.class).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method giftAndPayReports done");
    }

    /**
     * 礼物+充值列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/giftAndPayList")
    public ApiResult<Object> giftAndPayList(String startDate, String endDate, Integer os, Integer app) {
        logger.info("giftAndPayList param s={}, e={}, os={}", startDate, endDate, os);
        String[] giftAndPayArrZh = {"日期", "新增用户数", "新增华为用户数", "进房人数", "进房次数", "人均进房次数", "心心领取人数", "心心领取次数", "心心发送人数",
                "心心发送次数", "红包领取人数", "红包领取次数", "红包领取钻石数", "礼物发送人数", "礼物发送次数", "礼物消耗钻石数",
                "购买VIP人数", "华为充值人数", "华为充值次数", "华为充值金额", "充值人数", "充值次数", "充值金额"};
        String[] giftAndPayArrEn = {"Date", "new users", "new huawei users", "enter room users", "per capita enter user", "enter room times", "receive heart users",
                "receive heart times", "send heart users", "send heart times", "receive lucky box users",
                "receive lucky box times", "receive diamonds from lucky box", "send gift users", "send gift times",
                "diamonds consumed from sending gift", "purchase VIP users", "huawei recharge users", "huawei recharge time", "huawei recharge amount",
                "recharge users", "recharge time", "recharge amount"};
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        List<GiftAndPlayVO> giftAndPayList = newUserReportServer.getGiftAndPayList(timeArr[0], --timeArr[1], -1, null == os ? -1 : os, null == app ? -1 : app);
        result.put("data", giftAndPayList);
        result.put("zh", giftAndPayArrZh);
        result.put("en", giftAndPayArrEn);
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 房间核心玩法数据报表下载
     *
     * @param response  response
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(4)
    @RequestMapping("/roomCorePlayReports")
    public void roomCorePlayReports(HttpServletResponse response,
                                    String startDate, String endDate, Integer os, Integer app) {
        logger.info("roomCorePlayReports param s={}, e={}, os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RoomCorePlayVO> result = newUserReportServer.getRoomCorePlayList(timeArr[0], --timeArr[1], -1, null == os ? -1 : os, null == app ? -1 : app);
        ExcelUtils.exportExcel(response, result, RoomCorePlayVO.class, "room_core_play", "房间核心玩法数据");
        logger.info("method roomCorePlayReports done");
    }

    @RequireRole()
    @GetMapping("/commonData/download")
    public void commonDataDownload(HttpServletResponse response) {
        Criteria criteria = Criteria.where("rid").lt(1000);
        List<MongoActorData> allList =  commonDao.findList(new Query(criteria), MongoActorData.class);
        logger.info("allList.size={}", allList.size());
        List<UserCommonInfoVO> result = new ArrayList<>();
        for (MongoActorData mongoActorData : allList) {
            ActorData actorData = new ActorData();
            mongoActorData.copyTo(actorData);
            VipInfoData vipInfo = vipInfoDao.findVipInfo(actorData.getUid());
            result.add(new UserCommonInfoVO(
                    actorData.getStrRid(),
                    actorData.getName(),
                    Country.getCountryName(actorData.getCountry()),
                    getActorRegisterDate(actorData.getUid()),
                    vipInfo != null ? DateHelper.ARABIAN.datetimeToStr(vipInfo.getVipBuyTime()) : "",
                    vipInfo != null ? vipInfo.getVipLevel() + "" : "",
                    vipInfo != null ? DateHelper.ARABIAN.datetimeToStr(vipInfo.getVipEndTime()) : "",
                    rechargeDailyInfoDao.getUserTotalRechargeBean(actorData.getUid(), 0, com.quhong.core.utils.DateHelper.getNowSeconds()) + "",
                    getActorLastLoginDate(actorData)));
        }
        ExcelUtils.exportExcel(response, result, UserCommonInfoVO.class, "user_info", "用户信息");
    }

    /**
     * 获取用户注册时间
     */
    private String getActorRegisterDate(String uid) {
        return DateHelper.ARABIAN.datetimeToStr(new Date(new ObjectId(uid).getTimestamp() * 1000L));
    }

    /**
     * 获取用户最近登录时间
     */
    private String getActorLastLoginDate(ActorData actorData) {
        if (null == actorData.getLastLogin()) {
            return "";
        }
        Long loginTime = actorData.getLastLogin().getLoginTime();
        if (null == loginTime) {
            return "";
        }
        return DateHelper.ARABIAN.datetimeToStr(new Date(loginTime * 1000L));
    }

    /**
     * 房间核心玩法数据列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/roomCorePlayList")
    public ApiResult<Object> roomCorePlayList(String startDate, String endDate, Integer os, Integer app) {
        logger.info("roomCorePlayList param s={}, e={}, os={}", startDate, endDate, os);
        String[] roomCorePlayArrZh = {"日期", "新增用户数", "进房人数", "进房次数", "人均进房次数", "幸运转盘参与人数", "幸运转盘参与次数", "幸运转盘消耗钻石数",
                "参与猜拳人数", "参与猜拳次数", "猜拳消耗钻石数", "玩幸运数字人数", "玩幸运数字次数", "玩骰子人数", "玩骰子次数",
                "红包发送人数", "红包发送次数", "红包领取人数", "红包领取次数", "参与PK人数", "参与PK次数", "玩九宫格抽奖人数",
                "玩九宫格抽奖次数", "九宫格消耗钻石数", "玩幸运卡牌人数", "玩幸运卡牌次数", "幸运卡牌消耗钻石数"};
        String[] roomCorePlayArrEn = {"Date", "new users", "enter room users", "enter room times", "join lucky wheel users",
                "join lucky wheel times", "diamonds consumed at lucky wheel", "join roshambo users", "join roshambo times",
                "diamonds consumed at roshambo", "play lucky number users", "play lucky number times", "play dice users",
                "play dice times", "send lucky box users", "send lucky box times", "receive lucky box users",
                "receive lucky box times", "join PK users", "join PK times", "play BIG WIN users", "play BIG WIN times",
                "diamonds consumed at BIG WIN", "play lucky card users", "play lucky card times", "diamonds consumed at lucky card"};
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        List<RoomCorePlayVO> roomCorePlayList = newUserReportServer.getRoomCorePlayList(timeArr[0], --timeArr[1], -1, null == os ? -1 : os, null == app ? -1 : app);
        result.put("data", roomCorePlayList);
        result.put("en", roomCorePlayArrEn);
        result.put("zh", roomCorePlayArrZh);
        return new ApiResult<>().ok(0, result);
    }


    /**
     * 迎新房新用户留存
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param event      1 进迎新房新用户 2 迎新房上麦新用户 3迎新房发言新用户 4 迎新房送迎新礼物的新用户,
     * 50-56(房间停留时长<1min~>10min),60-66(上麦时长<1min~>10min)
     * @param count     event对应选择的次数
     * @param app       1Youstar、2Youstar Pro、-1全部
     * @param gender    男1、女2、PartyGirl3、-1全部
     * @param user      新1、老2、-1全部
     */
    @RequireRole
    @RequestMapping("/newRookieRoomDetail")
    public ApiResult<Object> newRookieRoomDetail(String startDate, String endDate, Integer event, Integer count,
                                         Integer gender, Integer user, Integer app, String rookieRoomId) {
        logger.info("newRookieRoomDetail param s={}, e={} event={} count={} gender={} user={} app={}  rookieRoomId={}",
                startDate, endDate, event, count, gender, user, app, rookieRoomId);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        if (null == user) {
            user = -1;
        }
        if (null == gender) {
            gender = -1;
        }
        if (null == app) {
            app = -1;
        }
        if (null == event) {
            event = 1;
        }
        if (null == count) {
            count = 1;
        }
//        if (null == remainType) {
//            remainType = CrushReportConstant.REMAIN_TYPE_PRODUCT;
//        }
        if(StringUtils.hasLength(rookieRoomId)){
            int hostRid = Integer.parseInt(rookieRoomId);
            ActorData hostData = actorDao.getActorByRid(hostRid);
            rookieRoomId = RoomUtils.formatRoomId(hostData.getUid());
        }
        Map<String, Object> result = new HashMap<>();
        result.put("data", newUserReportServer.newRookieRoomDetail(timeArr[0], --timeArr[1], event, -1, gender, user, app, rookieRoomId));
        return new ApiResult<>().ok(0, result);
    }

    @RequireRole
    @RequestMapping("/newRookieRoomDetailReports")
    public void newRookieRoomDetailReports(HttpServletResponse response, String startDate, String endDate,
                                   Integer event, Integer count, Integer gender, Integer user, Integer app) {
        logger.info("newRookieRoomDetailReports param s={}, e={} event={} count={} gender={} user={} app={} ",
                startDate, endDate, event, count, gender, user, app);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        if (null == user) {
            user = -1;
        }
        if (null == gender) {
            gender = -1;
        }
        if (null == app) {
            app = -1;
        }
        if (null == event) {
            event = 1;
        }
        if (null == count) {
            count = 1;
        }
//        if (null == remainType) {
//            remainType = CrushReportConstant.REMAIN_TYPE_PRODUCT;
//        }
        List<CrushDetailVO> result = newUserReportServer.newRookieRoomDetail(timeArr[0], --timeArr[1], event, -1, gender, user, app, "");
        ExcelUtils.exportExcel(response, result, CrushDetailVO.class, "newUserReportDetailReports", "迎新房新用户留存");
    }



}
