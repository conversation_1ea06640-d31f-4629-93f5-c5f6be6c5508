package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.dto.QuestionGroupDTO;
import com.quhong.enums.ApiCode;
import com.quhong.handler.BaseController;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.QuestionGroupDao;
import com.quhong.mysql.dao.QuizActivityTemplateDao;
import com.quhong.mysql.data.QuestionAwardData;
import com.quhong.mysql.data.QuestionGroupData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.QuizActivityTemplateServer;
import com.quhong.operation.share.condition.QuizActivityCondition;
import com.quhong.operation.share.dto.CheckpointConfigDTO;
import com.quhong.operation.share.dto.QuestionDTO;
import com.quhong.operation.share.dto.QuizActivityTemplateDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.QuestionVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 答题活动模板
 *
 * <AUTHOR>
 * @date 2022/8/27
 */
@RestController
@RequestMapping("/question")
public class QuizActivityTemplateController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(QuizActivityTemplateController.class);

    private static final Set<String> SUPPORT_SET = new HashSet<>(Arrays.asList("gift", "mic", "buddle", "ride", "ripple", "diamond", "badge", "float_screen", "coin", "other"));
    // wenmiaofang、yangml、yufengxia
    private static final Set<String> ADMIN_SET = new HashSet<>(Arrays.asList("5dba92651e23cd00c80b32bc", "61b1fa404cf5f82dff19a63e", "62b9780a1fb34e1c3520cb90"));

    @Value("${online:true}")
    private boolean online;
    @Resource
    private QuizActivityTemplateServer templateServer;
    @Resource
    private QuestionGroupDao questionGroupDao;
    @Resource
    private QuizActivityTemplateDao quizActivityTemplateDao;
    @Resource
    private MonitorSender monitorSender;

    /**
     * 新增题库
     */
    @RequireRole(3)
    @RequestMapping("group/insert")
    public HttpResult insertGroup(@RequestBody QuestionGroupDTO dto) {
        HttpResult result = new HttpResult();
        logger.info("insert question group. uid={}", dto.getUid());
        if (dto.getUid() == null || StringUtils.isEmpty(dto.getGid().trim())) {
            return result.error("题库id不能为空");
        }
        try {
            templateServer.insertGroup(dto);
        } catch (Exception e) {
            logger.error("insert question group error. uid={} {}", dto.getUid(), e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 修改题库
     */
    @RequireRole(3)
    @RequestMapping("group/update")
    public HttpResult updateGroup(@RequestBody QuestionGroupDTO dto) {
        HttpResult result = new HttpResult();
        logger.info("update question group. uid={} id={}", dto.getUid(), dto.getId());
        try {
            templateServer.updateGroup(dto);
        } catch (Exception e) {
            logger.error("update question group error. uid={} {}", dto.getUid(), e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 删除题库
     */
    @RequireRole(3)
    @RequestMapping("group/delete")
    public HttpResult deleteGroup(@RequestBody QuestionGroupDTO dto) {
        HttpResult result = new HttpResult();
        logger.info("delete question group. uid={} id={}", dto.getUid(), dto.getId());
        try {
            templateServer.deleteGroup(dto);
        } catch (Exception e) {
            logger.error("delete question group error. uid={} {}", dto.getUid(), e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 获取题库列表
     *
     */
    @RequireRole(3)
    @RequestMapping("group/select_page")
    public HttpResult<PageResultVO<QuestionGroupData>> selectPage(@RequestBody QuestionGroupDTO dto) {
        logger.info("select question group page. name={}, type={}, status={}, page={}, pageSize={}", dto.getName(), dto.getType(), dto.getStatus(), dto.getPage(), dto.getPageSize());
        HttpResult<PageResultVO<QuestionGroupData>> result = new HttpResult<>();
        PageResultVO<QuestionGroupData> pageVO = new PageResultVO<>();
        List<QuestionGroupData> dataList = questionGroupDao.selectPage(dto);
        pageVO.setList(dataList);
        pageVO.setTotal(questionGroupDao.selectCount(dto));
        return result.ok(pageVO);
    }

    /**
     * 获取题库id列表
     */
    @RequireRole(3)
    @RequestMapping("group/gid_list")
    public HttpResult getGidList(@RequestBody QuestionGroupDTO dto) {
        HttpResult result = new HttpResult();
        if (dto.getLang() == null) {
            return result.error("param error.");
        }
        logger.info("get gid list. lang={}", dto.getLang());
        return result.ok(templateServer.getGidList(dto));
    }

    /**
     * 新增或修改题目
     */
    @RequireRole(3)
    @RequestMapping("save")
    public HttpResult save(@RequestBody QuestionDTO dto) {
        HttpResult result = new HttpResult();
        logger.info("insert or update question list. gid={}", dto.getGid());
        if (StringUtils.isEmpty(dto.getContent())) {
            return result.error("题目内容不能为空");
        }
        if (CollectionUtils.isEmpty(dto.getOptionContent())) {
            return result.error("题目选项不能为空");
        }
        if (StringUtils.isEmpty(dto.getCorrectOption())) {
            return result.error("题目正确选项不能为空");
        }
        try {
            templateServer.save(dto);
        } catch (Exception e) {
            logger.error("save question error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 删除题目
     */
    @RequireRole(3)
    @RequestMapping("delete")
    public HttpResult delete(@RequestBody QuestionDTO dto) {
        HttpResult result = new HttpResult();
        logger.info("delete question list. gid={}", dto.getGid());
        try {
            templateServer.delete(dto);
        } catch (Exception e) {
            logger.error("delete question error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 获取题目列表
     */
    @RequireRole(3)
    @RequestMapping("list")
    public HttpResult<QuestionVO> getQuestionList(@RequestBody QuestionDTO dto) {
        HttpResult<QuestionVO> result = new HttpResult<>();
        logger.info("get question list. gid={}", dto.getGid());
        QuestionVO vo = templateServer.getQuestionList(dto);
        return result.ok(vo);
    }

    /**
     * 新增或修改答题活动模板
     */
    @RequireRole(3)
    @RequestMapping("template/save")
    public HttpResult<JSONObject> saveTemplate(HttpServletRequest request, @RequestBody QuizActivityTemplateDTO dto) {
        HttpResult<JSONObject> result = new HttpResult<>();
        String uid = request.getParameter("uid");
        JSONObject object;
        try {
            logger.info("save quiz activity template. uid={}", uid);
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (dto.getFrontConfig() != null) {
                // 校验首页配置
                QuizActivityTemplateDTO.FrontConfig frontConfig = dto.getFrontConfig();
                if (StringUtils.isEmpty(frontConfig.getAcName()) || StringUtils.isEmpty(frontConfig.getAcNameAr()) ) {
                    return result.error("活动名称不能为空");
                }
                if (frontConfig.getFrontPageBgUrl() == null || frontConfig.getFrontPageBgUrlAr() == null) {
                    return result.error("首页背景不能为空");
                }
                if (StringUtils.isEmpty(frontConfig.getAcBeginTime()) || StringUtils.isEmpty(frontConfig.getAcEndTime()) ) {
                    return result.error("活动时间不能为空");
                }
                if (frontConfig.getJoinType() == null) {
                    return result.error("参与方式不能为空");
                }
                if (frontConfig.getBeginBtnUrl() == null || frontConfig.getBeginGreyBtnUrl() == null) {
                    return result.error("开始按钮图片不能为空");
                }
                if (StringUtils.isEmpty(frontConfig.getBgColor())) {
                    return result.error("背景颜色不能为空");
                }
                if (frontConfig.getBackBtnUrl() == null) {
                    return result.error("返回按钮不能为空");
                }
                if (frontConfig.getShowJoinNum() == null) {
                    return result.error("展示参与人数不能为空");
                }
                if (StringUtils.isEmpty(frontConfig.getRuleAr()) || StringUtils.isEmpty(frontConfig.getRuleEn())) {
                    return result.error("规则内容不能为空");
                }
                if (StringUtils.isEmpty(frontConfig.getRuleContent()) || StringUtils.isEmpty(frontConfig.getRuleContentAr())) {
                    return result.error("描述内容不能为空");
                }
                if (frontConfig.getExplainPageUrl() == null || frontConfig.getExplainPageArUrl() == null) {
                    return result.error("说明页面卡片不能为空");
                }
                if (frontConfig.getRankPageUrl() == null || frontConfig.getRankPageArUrl() == null) {
                    return result.error("排行榜页面卡不能为空");
                }
                if (frontConfig.getTopOneCss() == null || frontConfig.getTopTwoCss() == null || frontConfig.getTopThreeCss() == null) {
                    return result.error("前三名CSS不能为空");
                }
                if (dto.getQuizType() != 1) {
                    if (frontConfig.getAnswersNum() == null ) {
                        return result.error("答题次数不能为空");
                    }
                    if (frontConfig.getAnswersNum() <= 0) {
                        return result.error("答题次数必须大于0");
                    }
                }
            }
            if (dto.getCheckpointConfig() != null) {
                // 校验关卡配置
                QuizActivityTemplateDTO.CheckpointConfig checkpointConfig = dto.getCheckpointConfig();
                if (checkpointConfig.getCorrectCardUrl() == null || checkpointConfig.getErrorCardUrl() == null) {
                    return result.error("提示卡片不能为空");
                }
                // if (checkpointConfig.getCheckpointRewardCardUrl() == null) {
                //     return result.error("关卡奖励卡片不能为空");
                // }
                if (checkpointConfig.getGiveUpOrContinueBtn() == null) {
                    return result.error("放弃&继续按钮不能为空");
                }
                if (checkpointConfig.getGiveUpReviveBtn() == null) {
                    return result.error("放弃复活按钮不能为空");
                }
                if (checkpointConfig.getSuccessRewardCard() == null) {
                    return result.error("闯关成功奖励提示卡片不能为空");
                }
                if (checkpointConfig.getFailureRewardCard() == null) {
                    return result.error("闯关失败奖励提示卡片不能为空");
                }
                if (checkpointConfig.getCheckpointBackBtnUrl() == null) {
                    return result.error("返回按钮不能为空");
                }
                for (CheckpointConfigDTO checkpointConfigDTO : checkpointConfig.getCheckpointList()) {
                    if (checkpointConfigDTO.getName() == null || checkpointConfigDTO.getNameAr() == null) {
                        return result.error("关卡名称不能为空");
                    }
                    if (checkpointConfigDTO.getPictureUrl() == null || checkpointConfigDTO.getPictureUrlAr() == null) {
                        return result.error("关卡图片不能为空");
                    }
                    if (checkpointConfigDTO.getTimesLimit() == null || checkpointConfigDTO.getTimesLimit() == 0) {
                        return result.error("闯关次数不能为空或零");
                    }
                    if (checkpointConfigDTO.getQuestionNum() == null || checkpointConfigDTO.getQuestionNum() == 0) {
                        return result.error("最多出题数不能为空或零");
                    }
                    for (QuestionAwardData awardData: checkpointConfigDTO.getAwardList()) {
                        if (awardData.getName() == null || awardData.getNameAr() == null || awardData.getRewardLimit() == null
                                || awardData.getRewardIcon() == null || awardData.getRewardType() == null
                        ) {
                            return result.error("奖励配置不能为空");
                        }
                        if (!"diamond".equals(awardData.getRewardType()) && !"coin".equals(awardData.getRewardType()) && awardData.getSourceId() == null) {
                            return result.error("非钻石金币礼物资源id不能为空");
                        }
                        if (!"diamond".equals(awardData.getRewardType()) && !"coin".equals(awardData.getRewardType()) && !"gift".equals(awardData.getRewardType()) && awardData.getRewardTimes() == null) {
                            return result.error("非钻石金币礼物的有效天数不能为空");
                        }
                        if ("diamond".equals(awardData.getRewardType()) || "coin".equals(awardData.getRewardType()) || "gift".equals(awardData.getRewardType())) {
                            if (awardData.getRewardNum() == null) {
                                return result.error("钻石金币礼物的数量不能为空");
                            }
                        }
                        if (!SUPPORT_SET.contains(awardData.getRewardType())) {
                            return result.error("不支持的奖励类型");
                        }
                    }
                }
            }
            if (dto.getAnswerConfig() != null) {
                // 校验答题配置
                QuizActivityTemplateDTO.AnswerConfig answerConfig = dto.getAnswerConfig();
                if (answerConfig.getAnswerBgUrl() == null || answerConfig.getAnswerBgUrlAr() == null) {
                    return result.error("答题页背景不能为空");
                }
                if (answerConfig.getOptionBtn() == null || answerConfig.getCorrectOptionBtn() == null || answerConfig.getErrorOptionBtn() == null) {
                    return result.error("选项按钮不能为空");
                }
                if (answerConfig.getRewardBgUrl() == null) {
                    return result.error("答题页礼物背景不能为空");
                }
                if (answerConfig.getShowAnswer() == null) {
                    return result.error("正确答案显示不能为空");
                }
                if (answerConfig.getTimingMethod() == null) {
                    return result.error("是否正序计时不能为空");
                }
                if (answerConfig.getLimitTime() != null && answerConfig.getLimitTime() < 0) {
                    return result.error("是否正序计时不能为空");
                }
                if (answerConfig.getShowRate() == null) {
                    return result.error("答题进度显示不能为空");
                }
                if (answerConfig.getAnswerType() == null) {
                    return result.error("答题类型不能为空");
                }
                if (answerConfig.getAgainBtnUrl() == null || answerConfig.getAgainGreyBtnUrl() == null) {
                    return result.error("再次作答按钮不能为空");
                }
                if (answerConfig.getBackFrontPageBtnUrl() == null ) {
                    return result.error("返回首页按钮不能为空");
                }
                if (answerConfig.getEndPageRankBtnUrl() == null) {
                    return result.error("排行榜按钮不能为空");
                }
                if (answerConfig.getEndPageBgUrl() == null) {
                    return result.error("卡片背景不能为空");
                }
                if (dto.getQuizType() != 1) {
                    if (answerConfig.getGid() == null || answerConfig.getArGid() == null) {
                        return result.error("题库id不能为空");
                    }
                    if (answerConfig.getOnceNum() == null) {
                        return result.error("单轮出题数量不能为空");
                    }
                    if (answerConfig.getOnceNum() <= 0) {
                        return result.error("单轮出题数量必须大于0");
                    }
                }
            }
            if (dto.getRewardConfig() != null) {
                // 校验奖励配置
                QuizActivityTemplateDTO.RewardConfig rewardConfig = dto.getRewardConfig();
                if (rewardConfig.getCorrectCardUrl() == null || rewardConfig.getErrorCardUrl() == null) {
                    return result.error("提示卡片不能为空");
                }
                if (CollectionUtils.isEmpty(rewardConfig.getReward())) {
                    return result.error("活动奖励不能为空");
                }
                for (QuestionAwardData awardData: rewardConfig.getReward()) {
                    if (awardData.getName() == null || awardData.getNameAr() == null || awardData.getRewardLimit() == null
                            || awardData.getRewardIcon() == null || awardData.getRewardType() == null
                    ) {
                        return result.error("奖励配置不能为空");
                    }
                    if (!"diamond".equals(awardData.getRewardType()) && !"coin".equals(awardData.getRewardType()) && awardData.getSourceId() == null) {
                        return result.error("非钻石金币礼物资源id不能为空");
                    }
                    if (!"diamond".equals(awardData.getRewardType()) && !"coin".equals(awardData.getRewardType()) && !"gift".equals(awardData.getRewardType()) && awardData.getRewardTimes() == null) {
                        return result.error("非钻石金币礼物的有效天数不能为空");
                    }
                    if ("diamond".equals(awardData.getRewardType()) || "coin".equals(awardData.getRewardType()) || "gift".equals(awardData.getRewardType())) {
                        if (awardData.getRewardNum() == null) {
                            return result.error("钻石金币礼物的数量不能为空");
                        }
                    }
                    if (!SUPPORT_SET.contains(awardData.getRewardType())) {
                        return result.error("不支持的奖励类型");
                    }
                }
            }
            if (dto.getFontConfig() != null) {
                // 校验字体配置
                QuizActivityTemplateDTO.FontConfig fontConfig = dto.getFontConfig();
                if (StringUtils.isEmpty(fontConfig.getBtnTextColor())) {
                    return result.error("按钮文本颜色不能为空");
                }
                if (StringUtils.isEmpty(fontConfig.getOtherBtnTextColor())) {
                    return result.error("按钮文本颜色不能为空");
                }
                if (StringUtils.isEmpty(fontConfig.getExplainTextColor())) {
                    return result.error("规则文本颜色不能为空");
                }
                if (StringUtils.isEmpty(fontConfig.getQuestionTextColor())) {
                    return result.error("题目文本颜色不能为空");
                }
                if (StringUtils.isEmpty(fontConfig.getCardTextColor())) {
                    return result.error("说明文本颜色不能为空");
                }
            }
            object = templateServer.saveTemplate(dto);
        } catch (Exception e) {
            logger.error("save quiz activity template error. uid={} {}", uid, e.getMessage(), e);
            return result.error();
        }
        return result.ok(object);
    }

    /**
     * 删除活动模板
     */
    @RequireRole(3)
    @RequestMapping("template/delete")
    public HttpResult deleteTemplate(HttpServletRequest request, @RequestBody QuizActivityTemplateDTO dto) {
        HttpResult result = new HttpResult();
        logger.info("delete quiz activity template. id={}", dto.getId());
        String uid = request.getParameter("uid");
        try {
            logger.info("save quiz activity template. uid={}", uid);
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (dto.getId() == null) {
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            templateServer.deleteTemplate(dto);
        } catch (Exception e) {
            logger.error("delete quiz activity template error. uid={}, id={} {}", uid, dto.getId(), e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 活动模板列表
     */
    @RequireRole(3)
    @RequestMapping("template/list")
    public HttpResult<PageResultVO<QuizActivityTemplateDTO>> getTemplateList(@RequestBody QuizActivityCondition condition) {
        HttpResult<PageResultVO<QuizActivityTemplateDTO>> result = new HttpResult<>();
        logger.info("get activity template list. gid={}, acName={}, status={}, quizType={}, page={}, pageSize={}",
                condition.getGid(), condition.getAcName(), condition.getStatus(), condition.getQuizType(), condition.getPage(), condition.getPageSize());
        return result.ok(templateServer.getTemplateList(condition));
    }

    /**
     * 改变活动模板状态
     */
    @RequireRole(3)
    @RequestMapping("template/updateStatus")
    public HttpResult updateTemplateStatus(@RequestBody QuizActivityCondition condition) {
        HttpResult result = new HttpResult();
        logger.info("update activity template status. activityId={}, status={}", condition.getId(), condition.getStatus());
        try {
            quizActivityTemplateDao.updateStatus(condition.getId(), condition.getStatus());
            if (ServerConfig.isProduct() && condition.getStatus() == 1) {
                monitorSender.info("activity", "正式服-用户成功创建答题活动", "答题活动模板id" + condition.getId());
            }
        } catch (Exception e) {
            logger.error("update activity template status error. activityId={}, status={} {}", condition.getId(), condition.getStatus(), e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }
}
