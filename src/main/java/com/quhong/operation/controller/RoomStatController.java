package com.quhong.operation.controller;

import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tree.TreeTask;
import com.quhong.core.utils.DateHelper;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.server.report.RoomStatServer;
import com.quhong.operation.share.vo.AbstractDateVO;
import com.quhong.operation.share.vo.reports.NoviceReportVO;
import com.quhong.operation.share.vo.reports.room.*;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping("/room_stat")
public class RoomStatController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RoomStatController.class);

    @Autowired
    private RoomStatServer roomStatServer;

    private Map<String, TaskQueue> cacheMap = new ConcurrentHashMap<>();

    private static final Comparator<AbstractDateVO> COMPARATOR = new Comparator<AbstractDateVO>() {
        //根据通话时间降序，使得前端数据更具有可视化
        @Override
        public int compare(AbstractDateVO o1, AbstractDateVO o2) {
            DayTimeData o1Time = DateHelper.ARABIAN.getContinuesDays(o1.getDate());
            DayTimeData o2Time = DateHelper.ARABIAN.getContinuesDays(o2.getDate());
            return -o1Time.getTime() + o2Time.getTime();
        }
    };

    /**
     * 视屏房统计报表-查询
     *
     * @param start
     * @param end
     * @param os
     * @return
     */
    @GetMapping("video")
    public DeferredResult<String> listVideoStat(String start, String end, Integer os) {
        DeferredResult<String> result = new DeferredResult<>();
        try {
            logger.info("start get video room stat data. start={} end={} os={}", start, end, os);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || os == null) {
                logger.error("get video room stat param error. start={} end={} os={}", start, end, os);
                result.setResult(createResult(HttpCode.PARAM_ERROR, null));
                return result;
            }
            //2、业务调用
            TreeTask parentTask = new TreeTask() {
                @Override
                protected void doComplete(Object data) {
                    List<RoomVideoStatVO> retList = (List<RoomVideoStatVO>) getData();
                    retList.sort(COMPARATOR);
                    result.setResult(createResult(HttpCode.SUCCESS, retList));
                }

                @Override
                protected void execute() {
                    //do nothing
                }
            };
            List<RoomVideoStatVO> retList = Collections.synchronizedList(new ArrayList<>());
            parentTask.setData(retList);
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            for (DayTimeData timeData : dayTimeList) {
                TreeTask subTask = new TreeTask(parentTask) {
                    @Override
                    protected void execute() {
                        int tomorrowStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                        if (timeData.getTime() >= tomorrowStartTime) {
                            RoomVideoStatVO statData = new RoomVideoStatVO();
                            statData.setDate(timeData.getDate());
                            retList.add(statData);
                        } else {
                            RoomVideoStatVO statData = roomStatServer.getVideoRoomStat(timeData, os);
                            retList.add(statData);
                        }
                    }

                    @Override
                    protected void doComplete(Object data) {

                    }
                };
                addTask(timeData, os, subTask, "room_video_stat");
            }
            parentTask.start();
            return result;
        } catch (Exception e) {
            logger.error("get video room stat data error. {}", e.getMessage(), e);
        }
        result.setResult(createResult(HttpCode.SERVER_ERROR, null));
        return result;
    }

    /**
     * 新用户视屏房统计数据报表-查询
     *
     * @param start
     * @param end
     * @param os
     * @return
     */
    @GetMapping("video/new_user")
    public DeferredResult<String> listNewUserVideoStat(String start, String end, Integer os) {
        DeferredResult<String> result = new DeferredResult<>();
        try {
            logger.info("start get new user video room stat data. start={} end={} os={}", start, end, os);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || os == null) {
                logger.error("get new user video room stat param error. start={} end={} os={}", start, end, os);
                result.setResult(createResult(HttpCode.PARAM_ERROR, null));
                return result;
            }
            //2、业务调用
            TreeTask parentTask = new TreeTask() {
                @Override
                protected void doComplete(Object data) {
                    List<RoomVideoNewStatVO> retList = (List<RoomVideoNewStatVO>) getData();
                    Comparator<RoomVideoNewStatVO> desc = Comparator.comparing(RoomVideoNewStatVO::getDate).reversed();
                    retList.sort(desc);
                    result.setResult(createResult(HttpCode.SUCCESS, retList));
                }

                @Override
                protected void execute() {
                    //do nothing
                }
            };
            List<RoomVideoNewStatVO> retList = Collections.synchronizedList(new ArrayList<>());
            parentTask.setData(retList);
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            for (DayTimeData timeData : dayTimeList) {
                TreeTask subTask = new TreeTask(parentTask) {
                    @Override
                    protected void execute() {
                        int tomorrowStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                        if (timeData.getTime() >= tomorrowStartTime) {
                            RoomVideoNewStatVO statData = new RoomVideoNewStatVO();
                            statData.setDate(timeData.getDate());
                            retList.add(statData);
                        } else {
                            RoomVideoNewStatVO statData = roomStatServer.getNewUserVideoStat(timeData, os);
                            retList.add(statData);
                        }
                    }

                    @Override
                    protected void doComplete(Object data) {

                    }
                };
                addTask(timeData, os, subTask, "room_video_new_user_stat");
            }
            parentTask.start();
            return result;
        } catch (Exception e) {
            logger.error("get new user video room stat data error. {}", e.getMessage(), e);
        }
        result.setResult(createResult(HttpCode.SERVER_ERROR, null));
        return result;
    }

    /**
     * 新用户视屏房统计数据报表-下载
     *
     * @param response
     * @param start
     * @param end
     * @param os
     */
    @GetMapping("video/new_user/download")
    public void downloadNewUserVideoStat(HttpServletResponse response, String start, String end, Integer os) {
        try {
            logger.info("start download new user video room stat data. start={} end={} os={}", start, end, os);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || os == null) {
                logger.error("download new user video room stat param error. start={} end={} os={}", start, end, os);
                response.getWriter().write(createResult(HttpCode.PARAM_ERROR, null));
                return;
            }
            //2、业务调用
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            List<RoomVideoNewStatVO> retList = new ArrayList<>();
            for (DayTimeData timeData : dayTimeList) {
                int tomorrowStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                if (timeData.getTime() >= tomorrowStartTime) {
                    RoomVideoNewStatVO statData = new RoomVideoNewStatVO();
                    statData.setDate(timeData.getDate());
                    retList.add(statData);
                } else {
                    RoomVideoNewStatVO statData = roomStatServer.getNewUserVideoStat(timeData, os);
                    retList.add(statData);
                }
            }
            //3、下载报表
            ExcelUtils.exportExcel(response, retList, RoomVideoNewStatVO.class, "new_user_video", "video");
            logger.info("finish download new_user_video report. ");
        } catch (IOException e) {
            logger.error("download new user video room stat data error. {}", e.getMessage(), e);
        }
    }

    /**
     * 获取音乐房统计数据
     *
     * @param start
     * @param end
     * @param os
     * @return
     */
    @GetMapping("music")
    public DeferredResult<String> listMusicStat(String start, String end, Integer os) {
        DeferredResult<String> result = new DeferredResult<>();
        try {
            logger.info("begin get music room stat data. start={} end={} os={}", start, end, os);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || os == null) {
                logger.error("get music room stat param error. start={} end={} os={}", start, end, os);
                result.setResult(createResult(HttpCode.PARAM_ERROR, null));
                return result;
            }
            //2、业务调用
            TreeTask parentTask = new TreeTask() {
                @Override
                protected void doComplete(Object data) {
                    List<RoomMusicStatVO> retList = (List<RoomMusicStatVO>) getData();
                    retList.sort(COMPARATOR);
                    result.setResult(createResult(HttpCode.SUCCESS, retList));
                }

                @Override
                protected void execute() {
                    //do nothing
                }
            };
            List<RoomMusicStatVO> retList = Collections.synchronizedList(new ArrayList<>());
            parentTask.setData(retList);
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            for (DayTimeData timeData : dayTimeList) {
                TreeTask subTask = new TreeTask(parentTask) {
                    @Override
                    protected void execute() {
                        int tomorrowStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                        if (timeData.getTime() >= tomorrowStartTime) {
                            RoomMusicStatVO statData = new RoomMusicStatVO();
                            statData.setDate(timeData.getDate());
                            retList.add(statData);
                        } else {
                            //调用业务数据
                            RoomMusicStatVO statData = roomStatServer.getMusicStat(timeData, os);
                            retList.add(statData);
                        }
                    }

                    @Override
                    protected void doComplete(Object data) {

                    }
                };
                addTask(timeData, os, subTask, "room_music_stat");
            }
            parentTask.start();
            return result;
        } catch (Exception e) {
            logger.error("get music room stat data error. {}", e.getMessage(), e);
        }
        result.setResult(createResult(HttpCode.SERVER_ERROR, null));
        return result;
    }

    /**
     * 获取新用户音乐房统计数据
     *
     * @param start
     * @param end
     * @param os
     * @return
     */
    @GetMapping("music/new_user")
    public DeferredResult<String> listMusicNewStat(String start, String end, Integer os) {
        DeferredResult<String> result = new DeferredResult<>();
        try {
            logger.info("begin get new_user music room stat data. start={} end={} os={}", start, end, os);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || os == null) {
                logger.error("get new_user music room stat param error. start={} end={} os={}", start, end, os);
                result.setResult(createResult(HttpCode.PARAM_ERROR, null));
                return result;
            }
            //2、业务调用
            TreeTask parentTask = new TreeTask() {
                @Override
                protected void doComplete(Object data) {
                    List<RoomMusicNewStatVO> retList = (List<RoomMusicNewStatVO>) getData();
                    retList.sort(COMPARATOR);
                    result.setResult(createResult(HttpCode.SUCCESS, retList));
                }

                @Override
                protected void execute() {
                    //do nothing
                }
            };
            List<RoomMusicNewStatVO> retList = Collections.synchronizedList(new ArrayList<>());
            parentTask.setData(retList);
            List<DayTimeData> dayTimeList = DateHelper.ARABIAN.getContinuesDays(start, end);
            for (DayTimeData timeData : dayTimeList) {
                TreeTask subTask = new TreeTask(parentTask) {
                    @Override
                    protected void execute() {
                        int tomorrowStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                        if (timeData.getTime() >= tomorrowStartTime) {
                            RoomMusicNewStatVO statData = new RoomMusicNewStatVO();
                            statData.setDate(timeData.getDate());
                            retList.add(statData);
                        } else {
                            //调用业务数据
                            RoomMusicNewStatVO statData = roomStatServer.getNewUserMusicStat(timeData, os);
                            retList.add(statData);
                        }
                    }

                    @Override
                    protected void doComplete(Object data) {

                    }
                };
                addTask(timeData, os, subTask, "room_music_new_user_stat");
            }
            parentTask.start();
            return result;
        } catch (Exception e) {
            logger.error("get new_user music room stat data error. {}", e.getMessage(), e);
        }
        result.setResult(createResult(HttpCode.SERVER_ERROR, null));
        return result;
    }

    private void addTask(DayTimeData dayTimeData, int os, TreeTask subTask, String pre) {
        String key = pre + "_" + dayTimeData.getDate() + "_" + os;
        TaskQueue queue = cacheMap.get(key);
        if (queue == null) {
            synchronized (this) {
                queue = cacheMap.get(key);
                if (queue == null) {
                    queue = new TaskQueue();
                    cacheMap.put(key, queue);
                }
            }
        }
        queue.add(subTask);
    }

}
