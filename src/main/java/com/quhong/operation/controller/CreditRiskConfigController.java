package com.quhong.operation.controller;

import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.CreditRiskConfigServer;
import com.quhong.operation.share.dto.CreditRiskDTO;
import com.quhong.operation.share.vo.CreditRiskExcelVo;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 信誉风控体系配置
 */

@RestController
@RequestMapping(value = "/creditRisk", produces = MediaType.APPLICATION_JSON_VALUE)
public class CreditRiskConfigController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(CreditRiskConfigController.class);

    @Resource
    private CreditRiskConfigServer creditRiskConfigServer;


    @RequireRole(3)
    @RequestMapping("/query")
    public String query(HttpServletRequest request, @RequestBody CreditRiskDTO dto) {
        String uid = request.getParameter("uid");
        logger.info("uid={} query dto {}", uid, dto);
        try {
            return createResult(HttpCode.SUCCESS, creditRiskConfigServer.query(dto));
        } catch (CommonException e) {
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e) {
            throw e;
        }

    }

    @RequireRole(3)
    @PostMapping("/updateData")
    public String updateData(HttpServletRequest request, @RequestBody CreditRiskDTO dto) {
        String uid = request.getParameter("uid");
        logger.info("uid={} updateData dto {}", uid, dto);
        try {
            return createResult(HttpCode.SUCCESS, creditRiskConfigServer.updateData(dto));
        } catch (CommonException e) {
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e) {
            throw e;
        }
    }


    /**
     * 下载信用体系名单
     */
    @RequireRole
    @RequestMapping("/download")
    public void download(HttpServletResponse response, @RequestBody CreditRiskDTO dto) {
        logger.info("download credit_risk_report dto {}", dto);
        List<CreditRiskExcelVo> creditRiskExcelVoList = creditRiskConfigServer.getCreditRiskExcelVoList(dto);
        int userStatus = dto.getUserStatus() == null ? 1 : dto.getUserStatus();
        String day = DateHelper.ARABIAN.formatDateInDay();
        String fileName = userStatus == 1 ? "creaditNormal" : userStatus == 2 ? "creaditBlack" : "creaditWhite";
        if (CollectionUtils.isEmpty(creditRiskExcelVoList)) {
            logger.info("要导出的数据为空 dto={}", dto);
        }
        ExcelUtils.exportExcel(response, creditRiskExcelVoList, CreditRiskExcelVo.class, fileName + day, "名单");
    }
}
