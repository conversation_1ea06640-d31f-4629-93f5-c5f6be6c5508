package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.data.TaskRankTemplateData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.TaskRankTemplateService;
import com.quhong.operation.share.condition.BaseCondition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 活动组件
 */
@RestController
@RequestMapping(value = "/taskRankTemplate", produces = MediaType.APPLICATION_JSON_VALUE)
public class TaskRankTemplateController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(TaskRankTemplateController.class);

    @Resource
    private TaskRankTemplateService taskRankTemplateService;

    /**
     * 组件cru
     */
    @RequireRole
    @RequestMapping("/list")
    public String list(@RequestBody BaseCondition condition) {
        logger.info("get taskRankTemplateService condition={}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, taskRankTemplateService.list(condition));
    }

    @RequireRole
    @RequestMapping("/add")
    public String addData(@RequestBody TaskRankTemplateData dto) {
        logger.info("addData {}", JSONObject.toJSONString(dto));
        taskRankTemplateService.addData(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @RequestMapping("/update")
    public String updateData(@RequestBody TaskRankTemplateData dto) {
        logger.info("updateData {}", JSONObject.toJSONString(dto));
        taskRankTemplateService.updateData(dto);
        return createResult(HttpCode.SUCCESS, "");
    }
}
