package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.FloatScreenService;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.dto.FloatScreenSourceDTO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 浮萍图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/floatScreen", produces = MediaType.APPLICATION_JSON_VALUE)
public class FloatScreenController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(FloatScreenController.class);
    private final static String filePath = "screen/";

    @Resource
    private FloatScreenService floatScreenService;



    @RequireRole
    @RequestMapping("/list")
    public String floatScreenList(@RequestBody ItemCondition condition) {

        logger.info("get floatScreenList {}", condition);
        return createResult(HttpCode.SUCCESS, floatScreenService.floatScreenSourceList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public String addFloatScreenData(@RequestBody FloatScreenSourceDTO dto) {
        logger.info("addFloatScreenData {}", dto);
        try {
            floatScreenService.addFloatScreenSourceData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateFloatScreenData(@RequestBody FloatScreenSourceDTO dto) {
        logger.info("updateFloatScreenData {}", dto);
        try {
            if (StringUtils.isEmpty(dto.getDocId())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }
            floatScreenService.updateFloatScreenData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        } catch (Exception e){
            throw e;
        }
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return AWSUploadUtils.upload(file, filePath);
    }

}
