package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.IndexBannerService;
import com.quhong.operation.server.RoomBannerService;
import com.quhong.operation.share.condition.BannerCondition;
import com.quhong.operation.share.dto.RoomBannerDTO;
import com.quhong.operation.share.vo.IndexBannerVO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 主页banner设置
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping(value ="/indexBanner", produces = MediaType.APPLICATION_JSON_VALUE)
public class IndexBannerController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(IndexBannerController.class);
    private final static String filePath = "banner/";

    @Resource
    private IndexBannerService indexBannerService;



    @RequireRole
    @RequestMapping("/list")
    public String indexBannerList(@RequestBody BannerCondition condition) {

        logger.info("get roomBannerList {}", condition);
        return createResult(HttpCode.SUCCESS, indexBannerService.indexBannerList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public String addBannerData(@RequestBody IndexBannerVO dto) {
        logger.info("addBannerData {}", dto);
        indexBannerService.addIndexBanner(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateBannerData(@RequestBody IndexBannerVO dto) {
        logger.info("updateBannerData {}", dto);
        if (StringUtils.isEmpty(dto.getBanner_id())) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }

        indexBannerService.updateBannerData(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return AWSUploadUtils.upload(file, filePath);
    }

}
