package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.UserFeedbackData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.LuckyGiftService;
import com.quhong.operation.server.UserFeedbackService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.FeedbackCondition;
import com.quhong.operation.share.vo.LuckyGiftVO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 砸蛋配置
 */

@RestController
@RequestMapping(value ="/userFeedback", produces = MediaType.APPLICATION_JSON_VALUE)
public class UserFeedbackController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(UserFeedbackController.class);

    @Resource
    private UserFeedbackService userFeedbackService;


    @RequireRole
    @RequestMapping("/list")
    public String getDataList(@RequestBody FeedbackCondition condition) {
        logger.info("getDataList {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, userFeedbackService.getDataList(condition));
    }


    @RequireRole
    @PostMapping("/updateData")
    public String updateData(@RequestParam String uid, @RequestBody UserFeedbackData dto) {
        try {
            logger.info("update UserFeedbackData Data {}", JSONObject.toJSONString(dto));
            userFeedbackService.updateData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }
}
