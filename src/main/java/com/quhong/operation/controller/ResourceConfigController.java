package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.ResourceConfigService;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.UserResCondition;
import com.quhong.operation.share.dto.ResourceConfigDTO;
import com.quhong.operation.share.dto.UserResDTO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 进入特效资源
 *
 * <AUTHOR>
 * @date 2023/4/24
 */
@RestController
@RequestMapping(value ="/resource", produces = MediaType.APPLICATION_JSON_VALUE)
public class ResourceConfigController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ResourceConfigController.class);

    /**
     * 目前支持的资源类型 11入场通知 12荣誉称号
     */
    private static final List<Integer> SUPPORT_RES_TYPE = Arrays.asList(BaseDataResourcesConstant.TYPE_ENTRY_EFFECT, BaseDataResourcesConstant.TYPE_HONOR_TITLE, BaseDataResourcesConstant.TYPE_TICKET);

    private final static String FILE_PATH = "resource/";

    @Resource
    private ResourceConfigService resourceConfigService;

    /**
     * 资源列表
     */
    @RequireRole
    @RequestMapping("/list")
    public String joinSourceList(@RequestBody ItemCondition condition) {
        if (condition.getResType() == null || !SUPPORT_RES_TYPE.contains(condition.getResType())) {
            createResult(HttpCode.PARAM_ERROR, null);
        }
        logger.info("get resource list. {}", condition.toString());
        return createResult(HttpCode.SUCCESS, resourceConfigService.getSourceList(condition));
    }

    /**
     * 新增资源
     */
    @RequireRole
    @PostMapping("/addData")
    public String addEntryEffectSource(@RequestParam String uid, @RequestBody ResourceConfigDTO dto) {
        logger.info("uid: {}, addResourceData {}", uid, dto.toString());
        if (!SUPPORT_RES_TYPE.contains(dto.getResourceType())) {
            createResult(HttpCode.PARAM_ERROR, null);
        }
        try {
            resourceConfigService.addResourceData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        }
    }

    /**
     * 修改资源
     */
    @RequireRole
    @PostMapping("/updateData")
    public String updateEntryEffectSource(@RequestBody ResourceConfigDTO dto) {
        logger.info("updateJoinSourceData {}", dto.toString());
        try {
            resourceConfigService.updateResourceData(dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(e.getHttpCode(), null);
        }
    }

    @RequireRole(3)
    @PostMapping("/sendReward")
    public HttpResult<?> sendReward(@RequestParam String uid, @RequestBody UserResDTO dto) {
        logger.info("sendReward. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        resourceConfigService.sendReward(dto);
        return HttpResult.getOk();
    }

    /**
     * 用户资源列表
     */
    @RequireRole(3)
    @PostMapping("/userResRecord")
    public HttpResult<?> userResRecord(@RequestParam String uid, @RequestBody UserResCondition condition) {
        logger.info("userResRecord. uid={} dto={}", uid, JSONObject.toJSONString(condition));
        return HttpResult.getOk(resourceConfigService.userResRecord(condition));
    }


    @RequireRole(3)
    @PostMapping("/removeReward")
    public HttpResult<?> removeReward(@RequestParam String uid, @RequestBody UserResDTO dto) {
        logger.info("removeReward. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        resourceConfigService.removeReward(dto);
        return HttpResult.getOk();
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return AWSUploadUtils.upload(file, FILE_PATH);
    }

    @RequireRole(2)
    @RequestMapping("/uploadAWS")
    public String uploadAWSResource(@RequestParam String folder, MultipartFile file) {
        folder = folder + "/";
        return AWSUploadUtils.upload(file, folder);
    }
}
