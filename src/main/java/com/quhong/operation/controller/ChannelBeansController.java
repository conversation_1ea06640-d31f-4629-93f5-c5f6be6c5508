package com.quhong.operation.controller;

import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.handler.HttpEnvData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.ChannelBeansServer;
import com.quhong.operation.share.dto.ChannelDrillDto;
import com.quhong.operation.share.dto.ChannelDrillUserDto;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.share.vo.reports.ChannelBeansAmountLogVO;
import com.quhong.operation.share.vo.reports.ChannelBeansDayStatVO;
import com.quhong.operation.share.vo.reports.ChannelSendBeansLogVO;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 渠道id打钻controller
 */
@RestController
@RequestMapping("/channelBeans")
@CrossOrigin
public class ChannelBeansController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ChannelBeansController.class);

    @Autowired
    private ChannelBeansServer channelDrillService;

    /**
     * 打钻
     *
     * @param channelSendDto
     * @return
     */
    @PostMapping("/diamond")
    @RequireRole(0)
    public String addDiamonds(@RequestBody ChannelDrillDto channelSendDto) {
        HttpEnvData envData = new HttpEnvData();
        try {
            String channelId = channelSendDto.getChannelId();
            List<ChannelDrillUserDto> list = channelSendDto.getList();
            logger.info("start channel drill beans param = {}", channelSendDto.toString());
            //参数校验
            if (StringUtils.isEmpty(channelId) || CollectionUtils.isEmpty(list)) {
                logger.error("channel drill param error.channel id or list is null. params = {}", channelSendDto.toString());
                return createError(envData, HttpCode.PARAM_ERROR);
            }
            for (ChannelDrillUserDto channelDrillUserDto : list) {
                Integer beans = channelDrillUserDto.getBeans();
                Integer rid = channelDrillUserDto.getRid();
                if (beans == null || beans < 1 || rid == null || rid < 1) {
                    logger.error("channel drill param error;beans or rid invalid. beans = {} rid = {} channelId = {}", beans, rid, channelId);
                    return createError(envData, HttpCode.PARAM_ERROR);
                }
            }
            //业务调用
            ApiResult<ChannelDrillVO> result = channelDrillService.sendBeans(channelSendDto);
            if (result.isOk()) {
                return createResult(envData, HttpCode.SUCCESS, result.getData());
            }
            return createResult(envData, result.getCode(), result.getData());
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
        return createError(envData, HttpCode.SERVER_ERROR);
    }

    /**
     * 根据rid查询用户姓名
     *
     * @param agentName
     * @return
     */
    @GetMapping("/agent")
    @RequireRole(0)
    public String getAgent(String agentName) {
        HttpEnvData envData = new HttpEnvData();
        try {
            logger.info("get agent data. agentName = {}", agentName);
            //参数校验
            if (StringUtils.isEmpty(agentName)) {
                logger.error("get agent data param error. agentName = {}", agentName);
                return createError(envData, HttpCode.PARAM_ERROR);
            }
            //业务调用
            ApiResult<ChannelBeanVO> apiResult = channelDrillService.getAgentData(agentName);
            if (apiResult.isOk()) {
                return createResult(envData, HttpCode.SUCCESS, apiResult.getData());
            }
            return createResult(envData, apiResult.getCode(), apiResult.getData());
        } catch (Exception e) {
            logger.error("get agent data exception = {}", e.getMessage(), e);
        }
        return createError(envData, HttpCode.SERVER_ERROR);
    }

    /**
     * 获取打钻详情
     *
     * @param agentName
     * @return
     */
    @GetMapping("/record")
    @RequireRole(0)
    public String recordChannelBeans(String agentName, Integer page) {
        HttpEnvData envData = new HttpEnvData();
        try {
            logger.info("get channel_bean record data. agentName={} page={}", agentName, page);
            //参数校验
            if (StringUtils.isEmpty(agentName)) {
                logger.error("get channel_bean record param error. agentName = {}", agentName);
                return createError(envData, HttpCode.PARAM_ERROR);
            }
            if (page == null) {
                page = 1;
            }
            //业务调用
            ApiResult<PageResultVO<List<ChannelBeanRecordVO>>> result = channelDrillService.getChannelBeanRecord(agentName, page);
            if(result.isOk()){
                return createResult(envData,HttpCode.SUCCESS,result.getData());
            }
        } catch (Exception e) {
            logger.error("get channel_bean record exception = {}", e.getMessage(), e);
        }
        return createError(envData, HttpCode.SERVER_ERROR);
    }

    /**
     * 渠道添加额度
     *
     * @param channelId
     * @param beans
     * @return
     */
    @GetMapping("/amount/add")
    @RequireRole(2)
    public HttpResult<ChannelDrillVO> addAmount(HttpServletRequest request, @RequestParam("channelId") String channelId, @RequestParam("beans") Integer beans) {
        HttpResult<ChannelDrillVO> result = new HttpResult<>();
        try {
            String uid = request.getParameter("uid");
            logger.info("start add channel config limit diamonds channelId = {} beans = {} uid = {}", channelId, beans, uid);
            //参数校验
            if (StringUtils.isEmpty(channelId) || beans == null || beans < 1) {
                logger.error("add channel amount param error ");
                return result.error(HttpCode.PARAM_ERROR.getMsg());
            }
            //业务调用
            ApiResult<ChannelDrillVO> apiResult = channelDrillService.addAmount(channelId, beans, uid);
            if (apiResult.isOk()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getCode().getMsg());
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 清空渠道额度
     *
     * @param channelId
     * @return
     */
    @GetMapping("/amount/clear")
    @RequireRole(2)
    public HttpResult<ChannelDrillVO> clearAmount(HttpServletRequest request, @RequestParam("channelId") String channelId) {
        HttpResult<ChannelDrillVO> result = new HttpResult<>();
        try {
            String uid = request.getParameter("uid");
            logger.info("start clear channel config limit diamonds channelId = {} uid = {}", channelId, uid);
            //参数校验
            if (StringUtils.isEmpty(channelId)) {
                logger.error("clear channel amount param error ");
                return result.error(HttpCode.PARAM_ERROR.getMsg());
            }
            //业务调用
            ApiResult<ChannelDrillVO> apiResult = channelDrillService.clearAmount(channelId, uid);
            if (apiResult.isOk()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getCode().getCode(), apiResult.getCode().getMsg());
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 获取所有渠道号信息及其余额列表
     *
     * @return
     */
    @GetMapping("/list")
    @RequireRole(2)
    public HttpResult<List<ChannelBeansInfoVO>> listAll() {
        HttpResult<List<ChannelBeansInfoVO>> result = new HttpResult<>();
        try {
            logger.info("start get channel beans list");
            //业务调用
            ApiResult<List<ChannelBeansInfoVO>> apiResult = channelDrillService.listAllChannelBeans();
            if (apiResult.isOk()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getCode().getCode(), apiResult.getCode().getMsg());
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 渠道打钻记录报表
     *
     * @param date
     * @param channel
     * @return
     */
    @GetMapping("/send/log")
    @RequireRole(2)
    public HttpResult<List<ChannelSendBeansLogVO>> listSendLog(String date, String channel) {
        HttpResult<List<ChannelSendBeansLogVO>> result = new HttpResult<>();
        try {
            logger.info("start get channel send beans log data. date = {} channel = {}", date, channel);
            //参数校验
            if (StringUtils.isEmpty(date)) {
                return result.error(HttpCode.PARAM_ERROR.getMsg());
            }
            if (channel != null && channel.equals("all")) {
                channel = null;
            }
            //业务调用
            ApiResult<List<ChannelSendBeansLogVO>> apiResult = channelDrillService.listSendBeansLog(date, channel);
            if (apiResult.isOk()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getCode().getCode(), apiResult.getCode().getMsg());
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 下载 - 渠道打钻记录报表
     *
     * @param date
     * @param channel
     */
    @GetMapping("/send/log/download")
    @RequireRole(2)
    public void sendLogDownload(HttpServletResponse response, String date, String channel) {
        try {
            logger.info("start download channel send beans log data. date = {} channel = {}", date, channel);
            //参数校验
            if (StringUtils.isEmpty(date)) {
                logger.error("param error");
                return;
            }
            if (channel != null && channel.equals("all")) {
                channel = null;
            }
            //业务调用
            ApiResult<List<ChannelSendBeansLogVO>> apiResult = channelDrillService.listSendBeansLog(date, channel);
            if (!apiResult.isOk()) {
                logger.error("channel beans send log download error. msg = {}", apiResult.getCode().getMsg());
                return;
            }
            ExcelUtils.exportExcel(response, apiResult.getData(), ChannelSendBeansLogVO.class, "channelBeansSendLog", "渠道打钻记录表");
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
    }

    /**
     * 每日钻石总数统计报表
     *
     * @param start
     * @param end
     * @param channel
     * @return
     */
    @GetMapping("/day/stat")
    @RequireRole(2)
    public HttpResult<List<ChannelBeansDayStatVO>> listDayStat(String start, String end, String channel) {
        HttpResult<List<ChannelBeansDayStatVO>> result = new HttpResult<>();
        try {
            logger.info("start get channel send beans stat log data. start = {} end = {} channel = {}", start, end, channel);
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                return result.error(HttpCode.PARAM_ERROR.getMsg());
            }
            if (channel != null && channel.equals("all")) {
                channel = null;
            }
            //业务调用
            ApiResult<List<ChannelBeansDayStatVO>> apiResult = channelDrillService.listSendBeansStat(start, end, channel);
            if (apiResult.isOk()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getCode().getMsg());
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 下载：每日钻石总数统计报表
     *
     * @param response
     * @param start
     * @param end
     * @param channel
     */
    @GetMapping("/day/stat/download")
    @RequireRole(2)
    public void downloadDayStat(HttpServletResponse response, String start, String end, String channel) {
        try {
            logger.info("start download channel send beans stat log data. start = {} end = {} channel = {}", start, end, channel);
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.error("param error");
                return;
            }
            if (channel != null && channel.equals("all")) {
                channel = null;
            }
            //业务调用
            ApiResult<List<ChannelBeansDayStatVO>> apiResult = channelDrillService.listSendBeansStat(start, end, channel);
            if (!apiResult.isOk()) {
                logger.error("channel beans day stat log download error. msg = {}", apiResult.getCode().getMsg());
                return;
            }
            ExcelUtils.exportExcel(response, apiResult.getData(), ChannelBeansDayStatVO.class, "channelBeansDayStat", "渠道每日打钻数记录表");
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
    }

    /**
     * 渠道打钻额度调整日志
     *
     * @param channel
     * @return
     */
    @GetMapping("/amount/change")
    @RequireRole(2)
    public HttpResult<List<ChannelBeansAmountLogVO>> listAmountChange(String start, String end, String channel) {
        HttpResult<List<ChannelBeansAmountLogVO>> result = new HttpResult<>();
        try {
            logger.info("start get channel beans amount change log.start = {} end = {} channel = {}", start, end, channel);
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                return result.error(HttpCode.PARAM_ERROR.getMsg());
            }
            if (channel != null && channel.equals("all")) {
                channel = null;
            }
            //业务调用
            ApiResult<List<ChannelBeansAmountLogVO>> apiResult = channelDrillService.listChannelAmountChangeLog(start, end, channel);
            if (apiResult.isOk()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getCode().getMsg());
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
        return result.error(HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 下载：渠道打钻额度调整日志
     *
     * @param response
     * @param start
     * @param end
     * @param channel
     */
    @GetMapping("/amount/change/download")
    @RequireRole(2)
    public void downloadAmountChange(HttpServletResponse response, String start, String end, String channel) {
        try {
            logger.info("start download channel beans amount change log.start = {} end = {} channel = {}", start, end, channel);
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.error("param error");
                return;
            }
            if (channel != null && channel.equals("all")) {
                channel = null;
            }
            //业务调用
            ApiResult<List<ChannelBeansAmountLogVO>> apiResult = channelDrillService.listChannelAmountChangeLog(start, end, channel);
            if (!apiResult.isOk()) {
                logger.error("channel beans amount change log download error. msg = {}", apiResult.getCode().getMsg());
                return;
            }
            ExcelUtils.exportExcel(response, apiResult.getData(), ChannelBeansAmountLogVO.class, "channelBeansAmountChange", "渠道打钻额度记录表");
        } catch (Exception e) {
            logger.error("channel diamonds exception = {}", e.getMessage(), e);
        }
    }

}
