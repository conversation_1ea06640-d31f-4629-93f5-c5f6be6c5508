package com.quhong.operation.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tree.TreeTask;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mysql.data.ThirdDetectLogData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.constant.CrushReportConstant;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.dao.SendBeansStatLogDao;
import com.quhong.operation.server.OfficialWelcomeService;
import com.quhong.operation.server.ReportCallBackUserService;
import com.quhong.operation.server.report.MoneyReportServer;
import com.quhong.operation.server.report.ReportsServer;
import com.quhong.operation.share.condition.RookieRoomUserConfigCondition;
import com.quhong.operation.share.condition.UserResCondition;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.mysql.SendBeansStatData;
import com.quhong.operation.share.mysql.PutInConsumeRecord;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.share.vo.reports.*;
import com.quhong.operation.share.vo.reports.money.MoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.AllMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.ConsumeMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.InMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.UserBeansWaterVO;
import com.quhong.operation.share.vo.reports.msg.MsgGiftVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.operation.utils.MongoUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
@RestController
@RequestMapping("/reports")
public class ReportsController {

    private static final Logger logger = LoggerFactory.getLogger(ReportsController.class);

    @Autowired
    private ReportsServer reportsServer;
    @Autowired
    private MoneyReportServer moneyReportServer;
    @Autowired
    private SendBeansStatLogDao sendBeansLogDao;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource
    private ReportCallBackUserService reportCallBackUserService;
    @Resource
    private OfficialWelcomeService officialWelcomeService;
    @Resource
    private ManagerDao managerDao;

    private Map<String, TaskQueue> cacheMap = new ConcurrentHashMap<>();

    private static final int WEAK_SECOND = 7 * 24 * 60 * 60;
    private static final Pattern intPattern = Pattern.compile("[^0-9]");

    /**
     * 用户基本信息报表
     *
     * @param response  response
     * @param file      用户rid文件
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(2)
    @RequestMapping("/actorBaseInfoExcel")
    public void actorBaseInfoExcel(HttpServletResponse response, MultipartFile file, String uid, String startDate, String endDate) {
        try {
            logger.info("method actorBaseInfoExcel param uid={}, starDate={}, endDate={}", uid, startDate, endDate);
            if (null == file) {
                logger.info("上传文件为空");
                return;
            }
            List<String> ridList = getWorkbookData(file);
            // 设置时间
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
            //查询数据
            ApiResult<List<ActorBaseInfoVO>> apiResult = reportsServer.listActorInfoByRid(uid, timeArr[0], timeArr[1], ridList);
            if (!apiResult.isOK()) {
                logger.error("actorBaseInfoExcel two ok error info {}", apiResult.getMsg());
                return;
            }
            ExcelUtils.exportExcel(response, apiResult.getData(), ActorBaseInfoVO.class, "actorBaseInfo", "用户基础信息报表");
            logger.info("method actorBaseInfoExcel 执行完毕");
        } catch (Exception e) {
            logger.info("{}", e.getMessage(), e);
        }
    }

    /**
     * partyGirl打钻导出报表
     *
     * @param response  response
     * @param file      用户rid文件
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(2)
    @RequestMapping("/partyGirlSendBeanDownLoad")
    public void partyGirlSendBeanDownLoad(HttpServletResponse response, MultipartFile file, String startDate, String endDate) {
        if (null == file) {
            logger.info("上传文件为空");
            return;
        }
        List<String> ridList = getWorkbookData(file);
        logger.info("method partyGirlSendBeanDownLoad param starDate={}, endDate={} excel rid size = {} ", startDate, endDate, ridList.size());
        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        //获取发钻成功次数为2打钻数据
        List<SendBeansStatData> sendBeanOkTwoList = sendBeansLogDao.listStatBySuccessNum(timeArr[0], timeArr[1], 2);
        //获取发钻成功次数为1打钻数据
        List<SendBeansStatData> sendBeanOkOneList = sendBeansLogDao.listStatBySuccessNum(timeArr[0], timeArr[1], 1);
        //获取发钻失败的打钻数据
        List<SendBeansStatData> sendBeanErrorList = sendBeansLogDao.listFailStat(timeArr[0], timeArr[1], 2);
        //合并成三个表
        ApiResult<List<PartyGirlSendBeanVO>> sendBeanOkTwoResult = reportsServer.getPartyGirlList(timeArr[0], timeArr[1], ridList, sendBeanOkTwoList);
        if (!sendBeanOkTwoResult.isOK()) {
            logger.error("actorBaseInfoExcel two ok error info {}", sendBeanOkTwoResult.getMsg());
        }
        ApiResult<List<PartyGirlSendBeanVO>> sendBeanOkOneResult = reportsServer.getPartyGirlList(timeArr[0], timeArr[1], ridList, sendBeanOkOneList);
        if (!sendBeanOkOneResult.isOK()) {
            logger.error("actorBaseInfoExcel one ok error info {}", sendBeanOkOneResult.getMsg());
        }
        ApiResult<List<PartyGirlSendBeanVO>> sendBeanErrorResult = reportsServer.getPartyGirlList(timeArr[0], timeArr[1], ridList, sendBeanErrorList);
        if (!sendBeanErrorResult.isOK()) {
            logger.error("actorBaseInfoExcel failed error info {}", sendBeanErrorResult.getMsg());
        }
        logger.info("send bean log select;two ok user size = {} one ok user size = {} failed two user size = {}", sendBeanOkTwoList.size(), sendBeanOkOneList.size(), sendBeanErrorList.size());
        logger.info("send bean sever select; two ok user size = {} one ok user size = {} failed two user size = {}", sendBeanOkTwoResult.getData().size(), sendBeanOkOneResult.getData().size(), sendBeanErrorResult.getData().size());

        List<String> sendBeanOkTwoRidList = new ArrayList<>();
        List<String> sendBeanOkOneRidList = new ArrayList<>();
        List<String> sendBeanErrorRidList = new ArrayList<>();
        for (PartyGirlSendBeanVO sendBean : sendBeanOkTwoResult.getData()) {
            String rid = intPattern.matcher(sendBean.getFromRid().trim()).replaceAll("");
            sendBeanOkTwoRidList.add(rid);
        }
        for (PartyGirlSendBeanVO sendBean : sendBeanOkOneResult.getData()) {
            String rid = intPattern.matcher(sendBean.getFromRid().trim()).replaceAll("");
            sendBeanOkOneRidList.add(rid);
        }
        for (PartyGirlSendBeanVO sendBean : sendBeanErrorResult.getData()) {
            String rid = intPattern.matcher(sendBean.getFromRid().trim()).replaceAll("");
            sendBeanErrorRidList.add(rid);
        }
        List<String> failedSendBeanRidList = new ArrayList<>();
        for (String rid : ridList) {
            rid = intPattern.matcher(rid.trim()).replaceAll("");
            if (!sendBeanOkTwoRidList.contains(rid) && !sendBeanOkOneRidList.contains(rid) && !sendBeanErrorRidList.contains(rid)) {
                failedSendBeanRidList.add(rid);
            }
        }
        List<PartyGirlSendBeanFailedVO> failedSendBeanList = reportsServer.listPartyGirlBeanFailedReason(failedSendBeanRidList);
        logger.info("error send bean rid size = {} report data = {}", failedSendBeanRidList.size(), failedSendBeanList.size());

        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "party_girl_send_bean");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(sendBeanOkTwoResult.getData(), EasyExcel.writerSheet(0, "发钻成功两次用户信息表").head(PartyGirlSendBeanVO.class).build());
            excelWriter.write(sendBeanOkOneResult.getData(), EasyExcel.writerSheet(1, "打钻成功1次用户信息表").head(PartyGirlSendBeanVO.class).build());
            excelWriter.write(sendBeanErrorResult.getData(), EasyExcel.writerSheet(2, "打钻失败用户信息表").head(PartyGirlSendBeanVO.class).build());
            excelWriter.write(failedSendBeanList, EasyExcel.writerSheet(3, "没有成功进行打钻操作用户表").head(PartyGirlSendBeanFailedVO.class).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method partyGirlSendBeanDownLoad 执行完毕");
    }

    /**
     * 获取用户重复数据报表
     */
    @RequireRole(2)
    @RequestMapping("/actorRepeatAccountExcel")
    public void actorRepeatAccountExcel(HttpServletResponse response, MultipartFile file, String startDate) {
        logger.info("start get actor repeat accounts excel start = {}", startDate);
        if (null == file) {
            logger.error("上传文件为空");
            return;
        }
        List<String> ridList = getWorkbookData(file);
        Integer start = DateHelper.ARABIAN.stringDateToStampSecond(startDate);
        ApiResult<List<RepeatAccountsVO>> apiResult = reportsServer.getRepeatAccounts(ridList, start);
        if (!apiResult.isOK()) {
            logger.error("actorRepeatAccountExcel error info {}", apiResult.getMsg());
            return;
        }
        logger.info("request getActorByRidList api result list size={}", apiResult.getData().size());
        ExcelUtils.exportExcel(response, apiResult.getData(), RepeatAccountsVO.class, "actorRepeatAccount", "用户重复数据报表");
        logger.info("method actorRepeatAccountExcel 执行完毕");
    }

    /**
     * 查询每日明细
     *
     * @param response
     * @param file
     * @param startDate
     * @param endDate
     */
    @RequireRole(3)
    @RequestMapping("/moneyDetailTotal")
    public void moneyDetailTotal(HttpServletResponse response, MultipartFile file, String startDate, String endDate) {
        logger.info("method moneyDetailTotal param starDate={}, endDate={}", startDate, endDate);
        if (null == file) {
            logger.error("上传文件为空");
            return;
        }
        List<String> ridList = getWorkbookData(file);

        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<MoneyDetailVO> apiResult = reportsServer.moneyDetailTotal(timeArr[0], timeArr[1], ridList);
        if (!apiResult.isOK()) {
            logger.error("actorBaseInfoExcel error info {}", apiResult.getMsg());
            return;
        }

        MoneyDetailVO moneyDetailVO = apiResult.getData();
        List<AllMoneyDetailVO> allMoneyDetailVoList = moneyDetailVO.getAllMoneyDetailVoList();
        List<InMoneyDetailVO> inMoneyDetailVoList = moneyDetailVO.getInMoneyDetailVoList();
        List<ConsumeMoneyDetailVO> consumeMoneyDetailVoList = moneyDetailVO.getConsumeMoneyDetailVoList();
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "money_detail_total");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(allMoneyDetailVoList, EasyExcel.writerSheet(0, "总明细").head(AllMoneyDetailVO.class).build());
            excelWriter.write(inMoneyDetailVoList, EasyExcel.writerSheet(1, "收入明细").head(InMoneyDetailVO.class).build());
            excelWriter.write(consumeMoneyDetailVoList, EasyExcel.writerSheet(2, "支出明细").head(ConsumeMoneyDetailVO.class).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method moneyDetailTotal 执行完毕");
    }

    /**
     * 用户钻石流水明细报表
     *
     * @param response  response
     * @param file      用户rid文件
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(3)
    @RequestMapping("/moneyDetailExcel")
    public void moneyDetailExcel(HttpServletResponse response, MultipartFile file, String startDate, String endDate) {
        logger.info("method moneyDetailExcel param starDate={}, endDate={}", startDate, endDate);
        if (null == file) {
            logger.info("上传文件为空");
            return;
        }
        List<String> ridList = getWorkbookData(file);
        if (CollectionUtils.isEmpty(ridList)) {
            logger.info("upload file is null！");
            return;
        }

        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List<UserBeansWaterVO>> apiResult;
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "money_detail");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            for (int i = 0; i < ridList.size(); i++) {
                apiResult = reportsServer.moneyDetail(timeArr[0], timeArr[1], Integer.parseInt(ridList.get(i)));
                if (apiResult.isOK() && !CollectionUtils.isEmpty(apiResult.getData())) {
                    excelWriter.write(apiResult.getData(), EasyExcel.writerSheet(i, ridList.get(i)).head(UserBeansWaterVO.class).build());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method moneyDetailExcel 执行完毕");
    }

    /**
     * 迎新房报表数据
     *
     * @param response  response
     * @param startDate 开始时间
     * @param endDate   结尾时间
     */
    @RequireRole(2)
    @RequestMapping("/roomActiveInfo")
    public void roomActiveInfo(HttpServletResponse response, String startDate, String endDate) {
        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<CompereRoomInfoVO> result = reportsServer.roomActiveInfo(timeArr[0], timeArr[1]);
        ExcelUtils.exportExcel(response, result, CompereRoomInfoVO.class, "welcome_room_data", "迎新房报表");
        logger.info("method roomActiveInfo 执行完毕");
    }

    /**
     * 用户行为情况报表
     *
     * @param response  response
     * @param rid       房间rid
     * @param startDate 开始时间
     * @param endDate   结尾时间
     */
    @RequireRole(2)
    @RequestMapping("/actorActionCondition")
    public void actorActionCondition(HttpServletResponse response, Integer rid, String startDate, String endDate) {
        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List<ActorActivityVO>> result = reportsServer.roomActorAction(rid, timeArr[0], timeArr[1]);
        List<ActorActivityVO> data = new ArrayList<>();
        if (!result.isOK() || null == result.getData()) {
            logger.error("roomActorAction error info {}", result.getMsg());
        } else {
            data = result.getData();
        }
        ExcelUtils.exportExcel(response, data, ActorActivityVO.class, "actor_active", "用户行为情况报表");
        logger.info("method actorActionCondition 执行完毕");
    }

    /**
     * 迎新房每天信息
     *
     * @param startDate 开始时间
     * @param endDate   结尾时间
     * @return 每天数据
     */
    @RequireRole(2)
    @RequestMapping("/welcomeNewRoomDataInfo")
    public HttpResult<List<WelcomeNewRoomVO>> welcomeNewRoomDataInfo(String startDate, String endDate) {
        logger.info("welcomeNewRoomDataInfo param start={} end={}", startDate, endDate);
        HttpResult<List<WelcomeNewRoomVO>> result = new HttpResult<>();
        // 字符串转时间
        Integer[] time = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);

        // 时间段转每天日期数组
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(time[0], time[1]);
        ApiResult<List<WelcomeNewRoomVO>> apiResult = reportsServer.welcomeNewRoomDataInfo(dateArr);
        if (!apiResult.isOK()) {
            logger.error("get welcome room data info fail, msg={}", apiResult.getMsg());
            return result.error(apiResult.getMsg());
        }

        return result.ok(apiResult.getData());
    }

    /**
     * 迎新房用户活跃程度报表
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(2)
    @RequestMapping("/welcomeRoomReports")
    public void welcomeRoomReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("welcomeRoomReports param start={} end={}", startDate, endDate);
        // 字符串转时间
        Integer[] time = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        // 时间段转每天日期数组
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(time[0], time[1]);
        ApiResult<List<WelcomeNewRoomVO>> result = reportsServer.welcomeNewRoomDataInfo(dateArr);
        if (!result.isOK()) {
            logger.error("get welcome room data info fail, msg={}", result.getMsg());
            return;
        }
        ExcelUtils.exportExcel(response, result.getData(), WelcomeNewRoomVO.class, "welcome_room_data", "迎新房用户数据报表");
        logger.info("method welcomeRoomReports 执行完毕");
    }

    /**
     * 迎新房新用户留存报表
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(2)
    @RequestMapping("/welcomeNewRoomKeep")
    public void welcomeNewRoomKeep(HttpServletResponse response, String startDate, String endDate) {
        logger.info("welcomeNewRoomKeep param start={} end={}", startDate, endDate);
        Integer[] time = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        // 时间段转每天日期数组
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(time[0], time[1]);
        ApiResult<List<WelcomeNewRoomKeepVO>> apiResult = reportsServer.welcomeNewRoomKeep(dateArr);
        if (!apiResult.isOK()) {
            logger.error("welcomeNewRoomKeep param s={} e={} error msg={}", startDate, endDate, apiResult.getMsg());
            return;
        }
        ExcelUtils.exportExcel(response, apiResult.getData(), WelcomeNewRoomKeepVO.class, "welcome_room_keep", "迎新房用户次留报表");
        logger.info("method welcomeNewRoomKeep 执行完毕");

    }

    /**
     * tap支付的记录
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(3)
    @RequestMapping("/tapChargeLog")
    public void tapChargeLog(HttpServletResponse response, String startDate, String endDate) {
        logger.info("tapChargeLog param startDate={} endDate={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("method tapChargeLog 执行完毕");
    }

    /**
     * 获取workbook第一sheet第一列，去掉第一行数据
     *
     * @param file workbook文件
     * @return 返回数据
     */
    private List<String> getWorkbookData(MultipartFile file) {
        List<String> result = new ArrayList<>();
        if (null == file) {
            logger.info("上传文件为空");
            return result;
        }
        try {
            Map<String, List<String>> listMap = ExcelUtils.readExcel(file.getInputStream());
            //返回第一列数据
            for (List<String> list : listMap.values()) {
                return list;
            }
        } catch (IOException e) {
            logger.error("获取文件内容失败");
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 格式化日期字符串
     * 将 2025/5/26 格式转换为 2025-05-26 格式
     *
     * @param dateStr 原始日期字符串
     * @return 格式化后的日期字符串，格式错误时返回null
     */
    private String formatDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }

        try {
            // 处理 2025/5/26 格式
            if (dateStr.contains("/")) {
                String[] parts = dateStr.split("/");
                if (parts.length == 3) {
                    int year = Integer.parseInt(parts[0]);
                    int month = Integer.parseInt(parts[1]);
                    int day = Integer.parseInt(parts[2]);
                    return String.format("%04d-%02d-%02d", year, month, day);
                }
            }

            // 处理 2025-5-26 格式
            if (dateStr.contains("-")) {
                String[] parts = dateStr.split("-");
                if (parts.length == 3) {
                    int year = Integer.parseInt(parts[0]);
                    int month = Integer.parseInt(parts[1]);
                    int day = Integer.parseInt(parts[2]);
                    return String.format("%04d-%02d-%02d", year, month, day);
                }
            }

            // 如果已经是标准格式 2025-05-26，直接返回
            if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return dateStr;
            }

        } catch (NumberFormatException e) {
            logger.error("日期格式转换失败: {}", dateStr);
        }

        return null;
    }

    /**
     * 线上Google支付和Apple支付信息报表
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(3)
    @RequestMapping("/onlinePayLog")
    public void onlinePayLog(HttpServletResponse response, String startDate, String endDate) {
        logger.info("onlinePayLog param s={} e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List[]> result = moneyReportServer.getOnlineChargeInfo(timeArr[0], timeArr[1]);
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "online_pay_Info");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(result.getData()[0], EasyExcel.writerSheet(0, "付费情况").head(OnlinePayInfoVO.class).build());
            excelWriter.write(result.getData()[1], EasyExcel.writerSheet(1, "7天内注册的用户付费情况").head(OnlinePayInfoVO.class).build());
            excelWriter.write(result.getData()[2], EasyExcel.writerSheet(2, "商品销售情况").head(OnlinePayGoodsMarketVO.class).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method onlinePayLog 执行完毕");
    }

    /**
     * 获取迎新房新用户留存数据报表
     */
    @RequireRole(2)
    @RequestMapping("/rookie/remain")
    public HttpResult<List> rookieRemain(HttpServletRequest request) {
        HttpResult<List> result = new HttpResult<>();
        String channel = request.getParameter("channel");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        logger.info("start into rookie remain channel = {},start date = {},end date = {}", channel, startDate, endDate);
        ApiResult<Map<String, Map<String, Object>>> apiResult = reportsServer.getRookieNewUserCount(channel, startDate, endDate);
        if (!apiResult.isOK()) {
            return result.error(apiResult.getMsg());
        }
        //sort6.3
        Map<String, Map<String, Object>> data = apiResult.getData();
        TreeSet<String> dates = new TreeSet<>(data.keySet());
        List<Map> list = new ArrayList<>();
        for (String date : dates) {
            Map<String, Map<String, Object>> target = new HashMap<>();
            target.put(date, data.get(date));
            list.add(target);
        }
        return result.ok(list);
    }

    /**
     * 迎新房用户留存数据报表下载
     */
    @RequireRole(2)
    @RequestMapping("/rookie/remain/download")
    public void rookieRemainDownload(HttpServletRequest request, HttpServletResponse response) {
        String channel = request.getParameter("channel");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        logger.info("start into rookie remain download channel = {},start date = {},end date = {}", channel, startDate, endDate);
        ApiResult<Map<String, Map<String, Object>>> apiResult = reportsServer.getRookieNewUserCount(channel, startDate, endDate);
        if (!apiResult.isOK()) {
            return;
        }
        Map<String, Map<String, Object>> data = apiResult.getData();
        //拼接title list
        List<String> titleList = new ArrayList<>();
        titleList.add("日期");
        for (int i = 0; i < data.keySet().size() - 1; i++) {
            if (i == 0 || i == 6 || i == 29) {
                titleList.add("第 " + i + " 天");
                titleList.add("第 " + (i + 1) + " 天");
                titleList.add("留存率");
            } else {
                titleList.add("第 " + (i + 1) + " 天");
            }
        }
        //设置每一行的具体值
        List<List<String>> lists = new ArrayList<>();
        //sort 按照日期进行排序
        TreeSet<String> dates = new TreeSet<>(data.keySet());
        for (String date : dates) {
            List<String> rowList = new ArrayList<>();
            rowList.add(date);
            Map<String, Object> rowMap = data.get(date);
            for (int i = 0; i < rowMap.size() / 2; i++) {
                String countKey = "count_" + i;
                rowList.add(rowMap.get(countKey).toString());
                if (i == 1 || i == 7 || i == 30) {
                    rowList.add(rowMap.get("rate_" + i).toString());
                }
            }
            lists.add(rowList);
        }
        ExcelUtils.exportExcel(response, lists, titleList, "rookie_room_remain", "迎新房新用户留存数据报表");
        logger.info("method rookie remain download report finished");
    }

    /**
     * 最近一周报表
     */
    @RequireRole(2)
    @RequestMapping("/weak/newUser/download")
    public void downloadTest(HttpServletResponse response) {
        long currentStamp = new Date().getTime() / 1000;
        int end = (int) currentStamp;
        int start = end - WEAK_SECOND;
        String endUid = MongoUtils.create_idBySecond(end);
        String startUid = MongoUtils.create_idBySecond(start);
        Criteria criteria = Criteria.where("_id").gte(new ObjectId(startUid)).lte(new ObjectId(endUid));
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(criteria));
        logger.info("query sql = {}", aggregation.toString());
        List<Actor> actorList = mongoTemplate.aggregate(aggregation, "actor", Actor.class).getMappedResults();
        logger.info("find new user start = {} end = {} size = {}", start, end, actorList.size());
        List<String> titleList = new ArrayList<>();
        titleList.add("uid");
        titleList.add("rid");
        List<List<String>> lists = new ArrayList<>();
        for (Actor actor : actorList) {
            List<String> list = new ArrayList<>();
            list.add(actor.get_id().toString());
            list.add(String.valueOf(actor.getRid()));
            lists.add(list);
        }
        ExcelUtils.exportExcel(response, lists, titleList, "new_users", "新增用户id报表");
        logger.info("find new user report finished");
    }

    /**
     * 房间用户统计报表
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     * @param os        用户操作系统 0安卓 1苹果
     */
    @RequireRole(2)
    @RequestMapping("/roomUserStatReports")
    public void roomUserStatReports(HttpServletResponse response, String startDate, String endDate, Integer os) {
        logger.info("roomUserStatReports param start={} end={} os={}", startDate, endDate, os);
        if (os != null && (os < 0 || os > 1)) {
            os = null;
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RoomUserStatVO> result = reportsServer.roomUserStat(timeArr[0], --timeArr[1], os, 0);
        ExcelUtils.exportExcel(response, result, RoomUserStatVO.class, "room_user_stat", "房间用户统计报表");
        logger.info("method roomUserStatReports done");
    }

    /**
     * 房间用户统计报表
     *
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     * @param os        用户操作系统 0安卓 1苹果
     */
    @RequireRole(2)
    @RequestMapping("/roomUserStatList")
    public ApiResult<Object> roomUserStatList(String startDate, String endDate, Integer os) {
        if (os != null && (os < 0 || os > 1)) {
            os = null;
        }
        logger.info("roomUserStatList param start={} end={} os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        String[] titleZh = new String[]{"日期", "日活", "新增", "创建房间人数", "创建房间次数", "进房人数", "进房次数", "人均进房次数", "上麦人数", "上麦次数", "发言人数", "发言次数", "送礼人数", "送礼次数", "人均房间停留(min)", "人均上麦时长(min)"};
        List<RoomUserStatVO> roomUserStat = reportsServer.roomUserStat(timeArr[0], --timeArr[1], os, 0);
        Map<String, Object> result = new HashMap<>();
        result.put("data", roomUserStat);
        result.put("zh", titleZh);
        result.put("en", titleZh);
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 房间用户统计echarts调用
     *
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     * @param os        用户操作系统 0安卓 1苹果
     * @param type      数据类型 0所有
     */
    @RequireRole(2)
    @RequestMapping("/roomUserStat")
    public ApiResult<Object> roomUserStat(String startDate, String endDate, Integer os, int type) {
        if (os != null && (os < 0 || os > 1)) {
            os = null;
        }
        logger.info("roomUserStat param start={} end={} os={} type={}", startDate, endDate, os, type);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RoomUserStatVO> roomUserStat = reportsServer.roomUserStat(timeArr[0], --timeArr[1], os, type);
        return new ApiResult<>().ok(0, roomUserStat);
    }

    /**
     * 每日钻石商品销售报表下载
     */
    @RequireRole(4)
    @RequestMapping("/firstChargeReports")
    public void firstChargeReports(HttpServletResponse response, String startDate, String endDate, Integer os) {
        if (os != null && (os < 0 || os > 1)) {
            os = -1;
        }
        logger.info("firstChargeReports param start={} end={} os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<FirstChargeStatVO> firstChargeStat = reportsServer.firstChargeStat(timeArr[0], --timeArr[1], os);
        ExcelUtils.exportExcel(response, firstChargeStat, FirstChargeStatVO.class, "first_charge_stat", "每日钻石商品销售");
        logger.info("method firstChargeReports done");
    }

    /**
     * 每日钻石商品销售数据统计
     */
    @RequireRole(4)
    @RequestMapping("/firstChargeList")
    public ApiResult<Object> firstChargeList(String startDate, String endDate, Integer os) {
        if (os != null && (os < 0 || os > 1)) {
            os = -1;
        }
        logger.info("firstChargeList param start={} end={} os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<FirstChargeStatVO> firstChargeStat = reportsServer.firstChargeStat(timeArr[0], --timeArr[1], os);
        String[] titleZh = new String[]{"日期", "0.99购买人数", "0.99购买次数", "0.99购买金额", "4.99购买人数", "4.99购买次数", "4.99购买金额", "19.99购买人数", "19.99购买次数", "19.99购买金额", "49.99购买人数", "49.99购买次数", "49.99购买金额", "89.99购买人数", "89.99购买次数", "89.99购买金额", "199.99购买人数", "199.99购买次数", "199.99购买金额", "299.99购买人数", "299.99购买次数", "299.99购买金额", "总收入"};
        Map<String, Object> result = new HashMap<>();
        result.put("data", firstChargeStat);
        result.put("en", titleZh);
        result.put("zh", titleZh);
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 房间在线时长分布数据统计
     */
    @RequireRole(2)
    @RequestMapping("/roomOnlineStat")
    public ApiResult<Object> roomOnlineStat(String startDate, String endDate) {
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("roomOnlineStat param start={} end={} ctimeStart={} ctimeEnd={}", startDate, endDate, timeArr[0], timeArr[1]);
        RoomOnlineStatVO roomOnlineStatVO = reportsServer.roomOnlineStat(timeArr[0], --timeArr[1]);
        Map<String, Object> result = new HashMap<>();
        result.put("data", null == roomOnlineStatVO ? null : roomOnlineStatVO.convertToArray());
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 24小时房间在线人数
     */
    @RequireRole(2)
    @RequestMapping("/roomUserOnlineByDay")
    public ApiResult<Object> roomUserOnlineByDay(String date) {
        Integer[] timeArr = DateHelper.ARABIAN.getTimeWhereRange(date);
        logger.info("roomUserOnlineByDay param date={} ctimeStart={} ctimeEnd={}", date, timeArr[0], timeArr[1]);
        List<RoomUserOnlineVO> roomUserOnlineByDay = reportsServer.roomUserOnlineByDay(timeArr[0], --timeArr[1]);
        Map<String, Object> result = new HashMap<>();
        result.put("data", roomUserOnlineByDay);
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 用户在房间停留时长分布
     */
    @RequireRole(2)
    @RequestMapping("/roomUserOnlineStat")
    public ApiResult<Object> roomUserOnlineStat(String startDate, String endDate, String os, String userType) {
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("roomUserOnlineStat param start={} end={} os={} userType={}", startDate, endDate, os, userType);
        RoomOnlineStatVO roomOnlineStatVO = reportsServer.roomUserOnlineStat(timeArr[0], --timeArr[1], os, userType);
        Map<String, Object> result = new HashMap<>();
        result.put("data", null == roomOnlineStatVO ? null : roomOnlineStatVO.convertToArray());
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 用户上麦时长分布
     */
    @RequireRole(2)
    @RequestMapping("/userUpMicStat")
    public ApiResult<Object> userUpMicStat(String startDate, String endDate, String os, String userType) {
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("userUpMicStat param start={} end={} os={} userType={}", startDate, endDate, os, userType);
        RoomOnlineStatVO roomOnlineStatVO = reportsServer.userUpMicStat(timeArr[0], --timeArr[1], os, userType);
        Map<String, Object> result = new HashMap<>();
        result.put("data", null == roomOnlineStatVO ? null : roomOnlineStatVO.convertToArray());
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 华为渠道新增用户统计报表下载
     *
     * @param response  response
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(2)
    @RequestMapping("/huaWeiPayChargeReports")
    public void huaWeiPayChargeReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("huaWeiPayChargeReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<HuaWeiPayChargeVO> result = reportsServer.huaWeiPayChargeReports(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, HuaWeiPayChargeVO.class, "huaWeiPayChargeReports", "华为渠道新增用户统计");
        logger.info("method huaWeiPayChargeReports done");
    }

    /**
     * 华为渠道新增用户统计报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(2)
    @RequestMapping("/huaWeiPayChargeList")
    public ApiResult<Object> huaWeiPayChargeList(String startDate, String endDate) {
        logger.info("huaWeiPayChargeList param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.huaWeiPayChargeReports(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 游戏费用数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/ludoGameCurrency")
    public ApiResult<Object> ludoGameCurrency(String startDate, String endDate) {
        logger.info("ludoGameCurrency param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.ludoGameCurrency(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    @RequireRole
    @RequestMapping("/ludoGameCurrencyReports")
    public void ludoGameCurrencyReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("ludoGameCurrencyReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<LudoGameCurrencyVO> result = reportsServer.ludoGameCurrency(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, LudoGameCurrencyVO.class, "ludoGameCurrencyReports", "游戏费用数据");
    }

    /**
     * ludo房间数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/ludoRoom")
    public ApiResult<Object> ludoRoom(String startDate, String endDate) {
        logger.info("ludoRoom param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.ludoRoom(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    @RequireRole
    @RequestMapping("/ludoRoomReports")
    public void ludoRoomReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("ludoRoomReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<LudoRoomVO> result = reportsServer.ludoRoom(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, LudoRoomVO.class, "ludoRoomReports", "ludo房间数据");
    }

    /**
     * ludo留存
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param os        iOS1、Google2、华为3
     * @param user      新1、老2用户筛选。新用户以当天注册时间统计
     */
    @RequireRole
    @RequestMapping("/ludoRetention")
    public ApiResult<Object> ludoRetention(String startDate, String endDate, Integer os, Integer user) {
        logger.info("ludoRetention param s={} e={} os={} user={}", startDate, endDate, os, user);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        if (null == os) {
            os = -1;
        }
        if (null == user) {
            user = -1;
        }
        result.put("data", reportsServer.ludoRetention(timeArr[0], --timeArr[1], os, user));
        return new ApiResult<>().ok(0, result);
    }

    @RequireRole
    @RequestMapping("/ludoRetentionReports")
    public void ludoRetentionReports(HttpServletResponse response, String startDate, String endDate, Integer os, Integer user) {
        logger.info("ludoRetentionReports param s={} e={} os={} user={}", startDate, endDate, os, user);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        if (null == os) {
            os = -1;
        }
        if (null == user) {
            user = -1;
        }
        List<LudoRetentionVO> result = reportsServer.ludoRetention(timeArr[0], --timeArr[1], os, user);
        ExcelUtils.exportExcel(response, result, LudoRetentionVO.class, "ludoRetentionReports", "ludo留存");
    }

    /**
     * ludo游戏参数数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/ludoGameDetail")
    public ApiResult<Object> ludoGameDetail(String startDate, String endDate) {
        logger.info("ludoGameDetail param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.ludoGameDetail(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    @RequireRole
    @RequestMapping("/ludoGameDetailReports")
    public void ludoGameDetailReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("ludoGameDetailReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<LudoGameDetailVO> result = reportsServer.ludoGameDetail(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, LudoGameDetailVO.class, "ludoGameDetailReports", "ludo游戏参数数据");
    }

    /**
     * Crush用户层统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param gender    男1、女2、-1全部
     * @param user      新1、老2、-1全部
     * @param app       1Youstar-安卓、2Youstar Pro、-1total 3Youstar-ios 4Youstar
     */
    @RequireRole(3)
    @RequestMapping("/crushUserStat")
    public ApiResult<Object> crushUserStat(String startDate, String endDate, Integer gender, Integer user, Integer app, String versionCode) {
        logger.info("crushUserStat param s={}, e={} gender={} user={} versionCode={}", startDate, endDate, gender, user, versionCode);
        if (null == gender) {
            gender = -1;
        }
        if (null == user) {
            user = -1;
        }
        if (null == app) {
            app = -1;
        }
        if (!StringUtils.isEmpty(versionCode) && versionCode.contains("全部")) {
            versionCode = "";
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.crushUserStat(timeArr[0], --timeArr[1], gender, user, app, versionCode));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * Crush用户层统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param gender    男1、女2、-1全部
     * @param user      新1、老2、-1全部
     * @param app       1Youstar、2Youstar Pro、-1total
     */
    @RequireRole(3)
    @RequestMapping("/crushUserStatReports")
    public void crushUserStatReports(HttpServletResponse response, String startDate, String endDate, Integer gender, Integer user, Integer app, String versionCode) {
        logger.info("crushUserStatReports param s={}, e={} gender={} user={} versionCode={}", startDate, endDate, gender, user, versionCode);
        if (null == gender) {
            gender = -1;
        }
        if (null == user) {
            user = -1;
        }
        if (null == app) {
            app = -1;
        }
        if (!StringUtils.isEmpty(versionCode) && versionCode.contains("全部")) {
            versionCode = "";
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<CrushUserVO> result = reportsServer.crushUserStat(timeArr[0], --timeArr[1], gender, user, app, versionCode);
        ExcelUtils.exportExcel(response, result, CrushUserVO.class, "crushUserStatReports", "Crush用户层统计");
    }

    /**
     * Crush留存
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param gender    男1、女2、-1全部
     * @param user      新1、老2、-1全部
     * @param app       1Youstar、2Youstar Pro、-1total
     */
    @RequireRole(3)
    @RequestMapping("/crushRetention")
    public ApiResult<Object> crushRetention(String startDate, String endDate, Integer gender, Integer user, Integer app, String versionCode) {
        logger.info("crushRetention param s={}, e={} gender={} user={} version_code={}", startDate, endDate, gender, user, versionCode);
        endDate = LocalDate.parse(endDate).plusDays(1).toString();
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        if (null == user) {
            user = -1;
        }
        if (null == gender) {
            gender = -1;
        }
        if (null == app) {
            app = -1;
        }
        if (!StringUtils.isEmpty(versionCode) && versionCode.contains("全部")) {
            versionCode = "";
        }
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.crushRetention(timeArr[0], --timeArr[1], gender, user, app, versionCode));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * Crush留存
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param gender    男1、女2、-1全部
     * @param user      新1、老2、-1全部
     * @param app       1Youstar、2Youstar Pro、-1total
     */
    @RequireRole(3)
    @RequestMapping("/crushRetentionReports")
    public void crushRetentionReports(HttpServletResponse response, String startDate, String endDate, Integer gender, Integer user, Integer app, String versionCode) {
        logger.info("crushRetentionReports param s={}, e={} gender={} user={} versionCode={}", startDate, endDate, gender, user, versionCode);
        endDate = LocalDate.parse(endDate).plusDays(1).toString();
        if (null == gender) {
            gender = -1;
        }
        if (null == user) {
            user = -1;
        }
        if (null == app) {
            app = -1;
        }
        if (!StringUtils.isEmpty(versionCode) && versionCode.contains("全部")) {
            versionCode = "";
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<CrushRetentionVO> result = reportsServer.crushRetention(timeArr[0], --timeArr[1], gender, user, app, versionCode);
        ExcelUtils.exportExcel(response, result, CrushRetentionVO.class, "crushRetentionReports", "Crush留存");
    }

    /**
     * Crush功能留存率与产品留存率数据概况
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param event     右滑1、超级喜欢2、匹配成功3、发消息4、回复消息5、使用了匹配并且进房了 7、只使用匹配用户的留存 8、 只进了房间用户的留存 9
     * @param count     event对应选择的次数
     * @param app       1Youstar、2Youstar Pro、-1全部
     * @param gender    男1、女2、PartyGirl3、-1全部
     * @param user      新1、老2、-1全部
     */
    @RequireRole(3)
    @RequestMapping("/crushDetail")
    public ApiResult<Object> crushDetail(String startDate, String endDate, Integer event,
                                         Integer count, Integer gender, Integer user,
                                         Integer app, Integer remainType, Integer hours) {
        logger.info("crushDetail param s={}, e={} event={} count={} gender={} user={} app={} remainType={}", startDate, endDate, event, count, gender, user, app, remainType);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        if (null == user) {
            user = -1;
        }
        if (null == gender) {
            gender = -1;
        }
        if (null == app) {
            app = -1;
        }
        if (null == event) {
            event = 1;
        }
        if (null == count) {
            count = 1;
        }
        if (null == remainType) {
            remainType = CrushReportConstant.REMAIN_TYPE_PRODUCT;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.crushDetail(timeArr[0], --timeArr[1], event, -1, gender, user, app, remainType, hours));
        return new ApiResult<>().ok(0, result);
    }

    @RequireRole(3)
    @RequestMapping("/crushDetailReports")
    public void crushDetailReports(HttpServletResponse response, String startDate, String endDate,
                                   Integer event, Integer count, Integer gender, Integer user,
                                   Integer app, Integer remainType, Integer hours) {
        logger.info("crushDetailReports param s={}, e={} event={} count={} gender={} user={} app={} remainType={}", startDate, endDate, event, count, gender, user, app, remainType);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        if (null == user) {
            user = -1;
        }
        if (null == gender) {
            gender = -1;
        }
        if (null == app) {
            app = -1;
        }
        if (null == event) {
            event = 1;
        }
        if (null == count) {
            count = 1;
        }
        if (null == remainType) {
            remainType = CrushReportConstant.REMAIN_TYPE_PRODUCT;
        }
        List<CrushDetailVO> result = reportsServer.crushDetail(timeArr[0], --timeArr[1], event, -1, gender, user, app, remainType, hours);
        ExcelUtils.exportExcel(response, result, CrushDetailVO.class, "crushDetailReports", "Crush功能留存率与产品留存率数据概况");
    }

    /**
     * Crush大盘数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param event     右滑1、超级喜欢2、匹配成功3、发消息4、回复消息5、-1全部
     * @param app       1Youstar、2Youstar Pro、-1全部
     * @param gender    男1、女2、PartyGirl3、-1全部
     * @param user      新1、老2、-1全部
     */
    @RequireRole(3)
    @RequestMapping("/crushDashboard")
    public ApiResult<Object> crushDashboard(String startDate, String endDate, Integer event, Integer gender, Integer user, Integer app) {
        logger.info("crushDashboard param s={}, e={} event={} gender={} user={} app={}", startDate, endDate, event, gender, user, app);
        if (null == app) {
            app = -1;
        }
        if (null == gender) {
            gender = -1;
        }
        if (null == event) {
            event = 1;
        }
        if (null == user) {
            user = -1;
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.crushDashboard(timeArr[0], --timeArr[1], event, gender, user, app));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * Crush大盘数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param event     右滑(喜欢)1、超级喜欢2、匹配成功3、发消息4、回复消息5、不喜欢6、-1全部
     * @param app       1Youstar、2Youstar Pro、-1全部
     * @param gender    男1、女2、PartyGirl3、-1全部
     * @param user      新1、老2、-1全部
     */
    @RequireRole(3)
    @RequestMapping("/crushDashboardReports")
    public void crushDashboardReports(HttpServletResponse response, String startDate, String endDate, Integer event, Integer gender, Integer user, Integer app) {
        logger.info("crushDashboardReports param s={}, e={} event={} gender={} user={} app={}", startDate, endDate, event, gender, user, app);
        if (null == app) {
            app = -1;
        }
        if (null == gender) {
            gender = -1;
        }
        if (null == event) {
            event = 1;
        }
        if (null == user) {
            user = -1;
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<CrushDashboardVO> result = reportsServer.crushDashboard(timeArr[0], --timeArr[1], event, gender, user, app);
        ExcelUtils.exportExcel(response, result, CrushDashboardVO.class, "crushDashboardReports", "Crush大盘数据");
    }

    /**
     * 亲密度数据报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param user      新1、-1全部
     */
    @RequireRole
    @RequestMapping("/friendship")
    public ApiResult<Object> friendship(String startDate, String endDate, Integer user) {
        logger.info("friendship param s={}, e={} user={}", startDate, endDate, user);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        if (null == user) {
            user = -1;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.friendship(timeArr[0], --timeArr[1], user));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 亲密度数据报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param user      新1、-1全部
     */
    @RequireRole
    @RequestMapping("/friendshipReports")
    public void friendshipReports(HttpServletResponse response, String startDate, String endDate, Integer user) {
        logger.info("friendshipReports param s={}, e={} user={}", startDate, endDate, user);
        if (null == user) {
            user = -1;
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<FriendshipVO> result = reportsServer.friendship(timeArr[0], --timeArr[1], user);
        ExcelUtils.exportExcel(response, result, FriendshipVO.class, "friendshipReports", "亲密度数据报表");
    }

    /**
     * 新注册用户推荐数据统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/relationshipNew")
    public ApiResult<Object> relationshipNew(String startDate, String endDate) {
        logger.info("relationshipNew param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.relationshipNew(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 新注册用户推荐数据统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/relationshipNewReports")
    public void relationshipNewReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("relationshipReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RelationshipNewVO> result = reportsServer.relationshipNew(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, RelationshipNewVO.class, "relationshipNewReports", "新注册用户推荐数据统计");
    }

    /**
     * 回访用户推荐数据统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/relationshipReturn")
    public ApiResult<Object> relationshipReturn(String startDate, String endDate) {
        logger.info("relationshipReturn param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.relationshipReturn(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 回访用户推荐数据统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/relationshipReturnReports")
    public void relationshipReturnReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("relationshipReturnReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RelationshipReturnVO> result = reportsServer.relationshipReturn(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, RelationshipReturnVO.class, "relationshipReturnReports", "回访用户推荐数据统计");
    }

    /**
     * 获取私信发送礼物报表
     *
     * @param start
     * @param end
     * @param os
     * @param userType
     * @return
     */
    @RequestMapping("/msg_gift_report")
    public DeferredResult<HttpResult<Object>> getMsgGiftRecord(String start, String end, Integer os, Integer userType) {
        DeferredResult<HttpResult<Object>> result = new DeferredResult<>();
        try {
            logger.info("begin get msg_gift report. start={} end={} os={} userType={}", start, end, os, userType);
            Comparator<MsgGiftVO> COMPARATOR = new Comparator<MsgGiftVO>() {
                //根据通话时间降序，使得前端数据更具有可视化
                @Override
                public int compare(MsgGiftVO o1, MsgGiftVO o2) {
                    DayTimeData o1Time = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(o1.getDate());
                    DayTimeData o2Time = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(o2.getDate());
                    return -o1Time.getTime() + o2Time.getTime();
                }
            };
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.error("get msg_gift report param error. start={} end={} os={} userType={}", start, end, os, userType);
                result.setResult(new HttpResult<>().error("param error"));
                return result;
            }
            if (os == null) {
                os = -1;
            }
            if (userType == null) {
                userType = -1;
            }
            //业务调用
            TreeTask parentTask = new TreeTask() {
                @Override
                protected void doComplete(Object data) {
                    List<MsgGiftVO> retList = (List<MsgGiftVO>) getData();
                    retList.sort(COMPARATOR);
                    result.setResult(new HttpResult<>().ok(retList));
                }

                @Override
                protected void execute() {
                    //do nothing
                }
            };
            List<MsgGiftVO> retList = Collections.synchronizedList(new ArrayList<>());
            parentTask.setData(retList);
            List<DayTimeData> dayTimeList = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(start, end);
            Integer finalOs = os;
            Integer finalUserType = userType;
            for (DayTimeData timeData : dayTimeList) {
                TreeTask subTask = new TreeTask(parentTask) {
                    @Override
                    protected void execute() {
                        int tomorrowStartTime = (int) (com.quhong.core.utils.DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                        if (timeData.getTime() >= tomorrowStartTime) {
                            MsgGiftVO statData = new MsgGiftVO();
                            statData.setDate(timeData.getDate());
                            retList.add(statData);
                        } else {
                            MsgGiftVO statData = reportsServer.getDayMsgGift(timeData, finalOs, finalUserType);
                            retList.add(statData);
                        }
                    }

                    @Override
                    protected void doComplete(Object data) {

                    }
                };
                addTask(timeData, subTask, "msg_gift_report");
            }
            parentTask.start();
            return result;
        } catch (Exception e) {
            logger.error("get msg_gift report error. {}", e.getMessage(), e);
        }
        result.setResult(new HttpResult<>().error("server error"));
        return result;
    }

    /**
     * 私信发礼物报表下载
     *
     * @param response
     * @param start
     * @param end
     * @param os
     * @param userType
     */
    @RequestMapping("/msg_gift_report/download")
    public void downloadMsgGiftReport(HttpServletResponse response, String start, String end, Integer os, Integer userType) {
        try {
            logger.info("begin download msg_gift report. start={} end={} os={} userType={}", start, end, os, userType);
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.error("download msg_gift report param error. start={} end={} os={} userType={}", start, end, os, userType);
                response.getWriter().write(JSON.toJSONString(new ApiResult<>().error("param error")));
                return;
            }
            if (os == null) {
                os = -1;
            }
            if (userType == null) {
                userType = -1;
            }
            //下载操作
            List<MsgGiftVO> list = new ArrayList<>();
            List<DayTimeData> dayTimeList = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(start, end);
            for (DayTimeData dayTimeData : dayTimeList) {
                MsgGiftVO statData = reportsServer.getDayMsgGift(dayTimeData, os, userType);
                list.add(statData);
            }
            ExcelUtils.exportExcel(response, list, MsgGiftVO.class, "msg_gift_report", "gift");
            logger.info("finish download msg_gift_report. ");
        } catch (IOException e) {
            logger.error("download msg_gift report error. {}", e.getMessage(), e);
        }
    }

    @RequireRole
    @RequestMapping("/feedback")
    public HttpResult<PageResultVO> feedbackReport(String start, String end, Integer page, Integer status, String username, Integer replayStatus) {
        HttpResult<PageResultVO> httpResult = new HttpResult<>();
        try {
            logger.info("begin get feedback report. start={} end={} page={} status={} username={} replay_status={}", start, end, page, status, username, replayStatus);
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || status == null || StringUtils.isEmpty(username) || replayStatus == null) {
                logger.error("feed back report param error. start={} end={} page={} status={} username={} replay_status={}", start, end, page, status, username, replayStatus);
                return httpResult.error(HttpCode.PARAM_ERROR.getCode(), HttpCode.PARAM_ERROR.getMsg());
            }
            if ("全部".equals(username)) {
                username = null;
            }
            if (page == null) {
                page = 1;
            }
            PageResultVO<FeedBackReportVO> feedbackReport = reportsServer.getFeedbackReport(start, end, page, status, username, replayStatus);
            return httpResult.ok(feedbackReport);
        } catch (Exception e) {
            logger.error("report feedback error. {}", e.getMessage(), e);
        }
        return httpResult.error(HttpCode.SERVER_ERROR.getMsg());
    }

    @RequireRole(2)
    @RequestMapping("/feedback/update")
    public HttpResult<String> updateFBK(int status, String manager, String remark, String reportId, int replayStatus) {
        HttpResult<String> httpResult = new HttpResult<>();
        try {
            logger.info("begin update feedback data. status={} manager={} remark={} report_id={} reply_status={}", status, manager, remark, reportId, replayStatus);
            //参数校验
            if (StringUtils.isEmpty(reportId)) {
                logger.error("update feedback data param error. status={} manager={} remark={} report_id={} reply_status={}", status, manager, remark, reportId, replayStatus);
                return httpResult.error(HttpCode.PARAM_ERROR.getCode(), HttpCode.PARAM_ERROR.getMsg());
            }
            reportsServer.updateFeedBackHandle(status, manager, remark, reportId, replayStatus);
            return httpResult.ok(null);
        } catch (Exception e) {
            logger.error("update feedback data error. {}", e.getMessage(), e);
        }
        return httpResult.error(HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 获取新手任务报表
     *
     * @param start
     * @param end
     * @param app   1Youstar、2Youstar Pro、-1total
     * @param os    1 ios 0 安卓 -1 total
     * @param isNew 1 新用户 -1 total
     * @return
     */
    @RequestMapping("/novice")
    public DeferredResult<HttpResult<List<NoviceReportVO>>> reportNovice(String start, String end, Integer app, Integer os, Integer isNew) {
        DeferredResult<HttpResult<List<NoviceReportVO>>> result = new DeferredResult<>();
        try {
            logger.info("begin get novice_task report. start={} end={} app={} os={} isNew={}", start, end, app, os, isNew);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.error("get novice_task report param error. start={} end={} app={} os={} isNew={}", start, end, app, os, isNew);
                result.setResult(new HttpResult<List<NoviceReportVO>>().error(HttpCode.PARAM_ERROR.getMsg()));
                return result;
            }
            if (app == null) {
                app = -1;
            }
            if (os == null) {
                os = -1;
            }
            if (isNew == null) {
                isNew = -1;
            }
            //业务调用
            TreeTask parentTask = new TreeTask() {
                @Override
                protected void doComplete(Object data) {
                    List<NoviceReportVO> retList = (List<NoviceReportVO>) getData();
                    Comparator<NoviceReportVO> desc = Comparator.comparing(NoviceReportVO::getDate).reversed();
                    retList.sort(desc);
                    result.setResult(new HttpResult<List<NoviceReportVO>>().ok(retList));
                }

                @Override
                protected void execute() {
                    //do nothing
                }
            };
            List<NoviceReportVO> retList = Collections.synchronizedList(new ArrayList<>());
            parentTask.setData(retList);
            List<DayTimeData> dayTimeList = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(start, end);
            Integer finalOs = os;
            Integer finalApp = app;
            Integer finalIsNew = isNew;
            for (DayTimeData timeData : dayTimeList) {
                TreeTask subTask = new TreeTask(parentTask) {
                    @Override
                    protected void execute() {
                        int tomorrowStartTime = (int) (com.quhong.core.utils.DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                        if (timeData.getTime() >= tomorrowStartTime) {
                            NoviceReportVO statData = new NoviceReportVO();
                            statData.setDate(timeData.getDate());
                            retList.add(statData);
                        } else {
                            NoviceReportVO statData = reportsServer.getNoviceReport(timeData, finalOs, finalApp, finalIsNew);
                            retList.add(statData);
                        }
                    }

                    @Override
                    protected void doComplete(Object data) {

                    }
                };
                String taskPre = "novice_pre_os_" + finalOs + "_app" + finalApp + "_isNew_" + finalIsNew;
                addTask(timeData, subTask, taskPre);
            }
            parentTask.start();
            return result;
        } catch (Exception e) {
            logger.error("get novice_task report error. {}", e.getMessage(), e);
        }
        result.setResult(new HttpResult<List<NoviceReportVO>>().error(HttpCode.SERVER_ERROR.getMsg()));
        return result;
    }

    @RequestMapping("/novice/download")
    public void reportNoviceDownload(HttpServletResponse response, String start, String end, Integer app, Integer os, Integer isNew) {
        try {
            logger.info("begin download novice_task report. start={} end={} app={} os={} isNew={}", start, end, app, os, isNew);
            //1、参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.error("download novice_task report param error. start={} end={} app={} os={} isNew={}", start, end, app, os, isNew);
                response.getWriter().write(JSON.toJSONString(new ApiResult<>().error("param error")));
                return;
            }
            if (app == null) {
                app = -1;
            }
            if (os == null) {
                os = -1;
            }
            if (isNew == null) {
                isNew = -1;
            }
            //2、调用业务
            List<DayTimeData> dayTimeList = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(start, end);
            List<NoviceReportVO> reportVOS = new ArrayList<>();
            for (DayTimeData timeData : dayTimeList) {
                int tomorrowStartTime = (int) (com.quhong.core.utils.DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60;
                if (timeData.getTime() >= tomorrowStartTime) {
                    NoviceReportVO statData = new NoviceReportVO();
                    statData.setDate(timeData.getDate());
                    reportVOS.add(statData);
                } else {
                    NoviceReportVO statData = reportsServer.getNoviceReport(timeData, os, app, isNew);
                    reportVOS.add(statData);
                }
            }
            //3、下载报表
            ExcelUtils.exportExcel(response, reportVOS, NoviceReportVO.class, "novice_report", "novice");
            logger.info("finish download novice_report. ");
        } catch (IOException e) {
            logger.error("download novice_task report error. {}", e.getMessage(), e);
        }
    }

    private void addTask(DayTimeData dayTimeData, TreeTask subTask, String pre) {
        String key = pre + "_" + dayTimeData.getDate();
        TaskQueue queue = cacheMap.get(key);
        if (queue == null) {
            synchronized (this) {
                queue = cacheMap.get(key);
                if (queue == null) {
                    queue = new TaskQueue();
                    cacheMap.put(key, queue);
                }
            }
        }
        queue.add(subTask);
    }

    /**
     * 员工迎新数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/staffWelcome")
    public ApiResult<Object> staffWelcome(String startDate, String endDate) {
        logger.info("staffWelcome param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.staffWelcome(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 员工迎新数据报表下载
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/staffWelcomeReports")
    public void staffWelcomeReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("staffWelcomeReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<StaffWelcomeVO> result = reportsServer.staffWelcome(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, StaffWelcomeVO.class, "staffWelcome", "员工迎新数据");
    }

    /**
     * api接口列表数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param type      1=api,2=mars,3=resource,4=rtc
     * @param os        手机系统
     * @param name      接口名字
     */
    @RequireRole
    @RequestMapping("/apiReportList")
    public ApiResult<Object> apiReportList(String startDate, String endDate, Integer rid, Integer type, Integer os, String name, Integer page, Integer pageSize) {
        logger.info("apiReportList param s={}, e={} type={} os={} name={} page={} pageSize={}", type, startDate, endDate, os, name, page, pageSize);
        if (null == page) {
            page = 1;
        }
        if (null == pageSize) {
            pageSize = 20;
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.apiReportList(timeArr[0], --timeArr[1], rid, type, os, name, page, pageSize));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * api接口统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param type      1=api,2=mars,3=resource,4=rtc
     * @param os        手机系统
     * @param name      接口名字
     */
    @RequireRole
    @RequestMapping("/apiReportStat")
    public ApiResult<Object> apiReportStat(String startDate, String endDate, Integer rid, Integer type, Integer os, String name, String countryCode) {
        logger.info("apiReportStat param s={}, e={} os={} name={}", startDate, endDate, os, name);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.apiReportStat(timeArr[0], --timeArr[1], rid, type, os, name, countryCode));
        return new ApiResult<>().ok(0, result);
    }

    @RequestMapping("/roomMsgReports")
    public void roomMsgReports(HttpServletResponse response, int start, int end, String roomId) {
        logger.info("roomMsgReports param s={}, e={} roomId={}", start, end, roomId);
        List<RoomMsgVO> result = reportsServer.roomMsgReports(start, end, roomId);
        ExcelUtils.exportExcel(response, result, RoomMsgVO.class, "roomMsgReports", "房间公屏消息");
    }

    @RequestMapping("/enterRoomReports")
    public void enterRoomReports(HttpServletResponse response, int start, int end, String roomId) {
        logger.info("enterRoomReports param s={}, e={} roomId={}", start, end, roomId);
        List<EnterRoomUserVO> result = reportsServer.enterRoomReports(start, end, roomId);
        ExcelUtils.exportExcel(response, result, EnterRoomUserVO.class, "enterRoomReports", "进房间用户");
    }


    /**
     * bigo检测记录报表
     */
    @RequireRole
    @RequestMapping("/detectUserRecord")
    public ApiResult<Object> detectUserRecord(String rid, Integer detectType, Integer page, Integer pageSize) {
        logger.info("detectUserRecord param detectType={}, page={}, pageSize={}", detectType, page, pageSize);
        return new ApiResult<>().ok(0, reportsServer.detectUserRecord(rid, detectType, page, pageSize));
    }

    @RequireRole
    @RequestMapping("/detectUserRecordReports")
    public void detectUserRecordReports(HttpServletResponse response, String rid, Integer detectType) {
        logger.info("detectUserRecordReports param rid={} detectType={}", rid, detectType);
        List<DetectUserRecordVO> result = reportsServer.detectUserRecordReports(rid, detectType);
        ExcelUtils.exportExcel(response, result, DetectUserRecordVO.class, "detectUserRecordReports", "bigo检测记录数据");
    }

    /**
     * 检测图片放行
     */
    @RequireRole(3)
    @RequestMapping("/detectImgPass")
    public HttpResult<Object> detectImgPass(@RequestParam String uid, @RequestParam Integer id, @RequestParam Integer ctime) {
        logger.info("detectImgPass. uid={}, id={}, ctime={}", uid, id, ctime);
        return reportsServer.detectImgPass(id, ctime);
    }


    /**
     * 第三方识别日志报表
     */
    @RequireRole
    @PostMapping("/thirdDetectLogReports")
    // @RequestBody   private String startDate
    public void thirdDetectLogReports(HttpServletResponse response, @RequestBody DownLoadQueryVo vo) {
        String startDate = vo.getStartDate();
        String endDate = vo.getEndDate();
        logger.info("thirdDetectLogReports param start={}, end={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<ThirdDetectLogData> result = reportsServer.thirdDetectLogReports(timeArr[0], --timeArr[1]);
        String preName = startDate + "-" + endDate + "-" + com.quhong.core.utils.DateHelper.getNowSeconds();
        ExcelUtils.exportExcel(response, result, ThirdDetectLogData.class, preName + "-detectLog", "数美识别日志");
    }


    /**
     * 数美对比图灵顿报表
     *
     * @param response  response
     * @param startDate 开始时间
     * @param riskType  1 以图灵顿风控的导数据 2 以数美检测异常的导数据
     * @param searchKey 搜索的key,指定图灵顿风险标签，比如301，217,只能输入一个
     * @param page      过滤的源数据，每页3000条
     * @param dTime     上报时间与数美生成有效时间间隔，单位s
     */
    @RequireRole(2)
    @RequestMapping("/shuMeiTnCompareInfo")
    public void shuMeiTnCompareInfo(HttpServletResponse response, String startDate,
                                    Integer riskType, String searchKey, Integer page, Integer dTime) {
        // 设置时间
        String fileName = startDate + "_" + riskType + "_" + searchKey + "_" + page + "_" + dTime;
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, startDate);
        int start = timeArr[0] <= 1720008000 ? 1720008000 : timeArr[0];
        List<CompereShuMeiTnInfoVO> result = reportsServer.shuMeiTnCompareInfo(start, timeArr[1],
                riskType, searchKey, page, dTime);
        ExcelUtils.exportExcel(response, result, CompereShuMeiTnInfoVO.class, "shuMeiTnCompareInfo" + fileName, "数美图灵比较报表");
        logger.info("method shuMeiTnCompareInfo 执行完毕");
    }

    @RequireRole
    @RequestMapping("/userResRecordReports")
    public void userResRecordReports(HttpServletResponse response, @RequestBody UserResCondition condition) {
        logger.info("userResRecordReports param condition={}", JSONObject.toJSONString(condition));
        List<UserResRecordVO> result = reportsServer.userResRecordReports(condition);
        ExcelUtils.exportExcel(response, result, UserResRecordVO.class, "userResRecordReports", "用户资源记录数据");
    }


    /**
     * 迎新用户或者迎新房报表
     */
    @RequireRole
    @RequestMapping("/rookieRoomUserConfigListExcel")
    public void rookieRoomUserConfigListExcel(HttpServletResponse response, Integer type) {
        RookieRoomUserConfigCondition condition = new RookieRoomUserConfigCondition();
        condition.setType(type);
        condition.setPage(1);
        condition.setPageSize(5000);
        PageResultVO<RookieRoomConfigVO> pageData = reportCallBackUserService.rookieRoomUserConfigList(condition);
        List<RookieRoomConfigVO> dataList = pageData.getList();
        logger.info("RookieRoomConfigVO result type={} list size={}", type, dataList.size());
        if (dataList.size() > 0) {
            String fileName = type == 1 ? "rookieUserExcel" : "rookieRoomExcel";
            String sheetName = type == 1 ? "迎新用户报表" : "迎新房报表";
            ExcelUtils.exportExcel(response, dataList, RookieRoomConfigVO.class, fileName, sheetName);
        }
        logger.info("method rookieRoomUserConfigListExcel 执行完毕");
    }

    /**
     * 官方迎新房报表
     */
    @RequireRole
    @RequestMapping("/officialWelcomeListExcel")
    public void officialWelcomeListExcel(HttpServletResponse response) {
        List<OfficialWelcomeVO> dataList = new ArrayList<>();
        officialWelcomeService.fillAllOffcialList("", dataList);
        logger.info("OfficialWelcomeVO result list size={}", dataList.size());
        if (dataList.size() > 0) {
            String fileName = "officialWelcomeListExcel";
            String sheetName = "官方迎新房报表";
            ExcelUtils.exportExcel(response, dataList, OfficialWelcomeVO.class, fileName, sheetName);
        }
        logger.info("method officialWelcomeListExcel 执行完毕");
    }

    @RequireRole
    @RequestMapping("/putInConsumeRecordInfoExcel")
    public ApiResult<Object> putInConsumeRecordInfoExcel(MultipartFile file, String uid) {
        logger.info("putInConsumeRecordInfoExcel start uid={}", uid);
        if (file == null) {
            logger.error("上传文件为空");
            return new ApiResult<>().error("上传文件为空");
        }
        Manager manager = managerDao.getDataByUid(uid);
        String opUser = manager != null ? manager.getAccount() : "unkown";

        try {
            // 读取Excel文件数据
            Map<String, List<String>> excelData = ExcelUtils.readExcel(file.getInputStream());
            if (excelData.isEmpty()) {
                logger.error("Excel文件内容为空");
                return new ApiResult<>().error("Excel文件内容为空");
            }

            // 验证Excel表头格式
            String[] expectedHeaders = {"日期", "渠道", "广告系列名称", "包系统", "花费金额"};
            for (String header : expectedHeaders) {
                if (!excelData.containsKey(header)) {
                    logger.error("Excel表头缺少必要列: {}", header);
                    return new ApiResult<>().error("Excel表头格式不正确，缺少列: " + header);
                }
            }

            // 获取各列数据
            List<String> dateList = excelData.get("日期");
            List<String> mediumList = excelData.get("渠道");
            List<String> campaignList = excelData.get("广告系列名称");
            List<String> pkgOsList = excelData.get("包系统");
            List<String> costList = excelData.get("花费金额");

            // 检查数据行数是否一致
            int dataRowCount = dateList.size();
            if (mediumList.size() != dataRowCount || campaignList.size() != dataRowCount ||
                pkgOsList.size() != dataRowCount || costList.size() != dataRowCount) {
                logger.error("Excel各列数据行数不一致");
                return new ApiResult<>().error("Excel各列数据行数不一致");
            }

            if (dataRowCount == 0) {
                logger.error("Excel文件没有数据行");
                return new ApiResult<>().error("Excel文件没有数据行");
            }

            int successCount = 0;
            int failCount = 0;
            int currentTime = (int) (System.currentTimeMillis() / 1000);

            logger.info("开始处理Excel数据，共{}行", dataRowCount);

            // 逐行处理数据
            for (int i = 0; i < dataRowCount; i++) {
                try {
                    // 获取当前行各列数据
                    String dateStr = dateList.get(i);
                    String medium = mediumList.get(i);
                    String campaign = campaignList.get(i);
                    String pkgOs = pkgOsList.get(i);
                    String costStr = costList.get(i);

                    // 去除空白字符
                    dateStr = dateStr != null ? dateStr.trim() : "";
                    medium = medium != null ? medium.trim() : "";
                    campaign = campaign != null ? campaign.trim() : "";
                    pkgOs = pkgOs != null ? pkgOs.trim() : "";
                    costStr = costStr != null ? costStr.trim() : "";

                    // 验证必填字段
                    if (StringUtils.isEmpty(medium) || StringUtils.isEmpty(campaign) ||
                            StringUtils.isEmpty(pkgOs) || StringUtils.isEmpty(dateStr)) {
                        logger.info("第{}行必填字段为空，跳过: 日期={}, 渠道={}, 广告系列={}, 包系统={}",
                                i + 1, dateStr, medium, campaign, pkgOs);
                        failCount++;
                        continue;
                    }

                    // 转换日期格式 (2025/5/26 -> 2025-05-26)
                    String formattedDate = formatDate(dateStr);
                    if (formattedDate == null) {
                        logger.info("第{}行日期格式错误，跳过: {}", i + 1, dateStr);
                        failCount++;
                        continue;
                    }

                    // 解析花费金额
                    Float adCostMoney = null;
                    if (!StringUtils.isEmpty(costStr)) {
                        try {
                            adCostMoney = Float.parseFloat(costStr);
                        } catch (NumberFormatException e) {
                            logger.info("第{}行花费金额格式错误: {}", i + 1, costStr);
                            // 花费金额格式错误时设为0，不跳过整行
                            adCostMoney = 0.0f;
                        }
                    }

                    PutInConsumeRecord record = new PutInConsumeRecord();
                    record.setMedium(medium);
                    record.setCampaign(campaign);
                    record.setAdCostMoney(adCostMoney);
                    record.setPkgOs(pkgOs);
                    record.setDate(formattedDate);
                    record.setCtime(currentTime);
                    record.setMtime(currentTime);

                    boolean success = reportsServer.saveOrUpdatePutInConsumeRecord(record, opUser);
                    if (success) {
                        successCount++;
                        logger.info("成功处理第{}行数据: {}-{}", i + 1, medium, campaign);
                    } else {
                        failCount++;
                        logger.info("保存第{}行数据失败", i + 1);
                    }
                } catch (Exception e) {
                    logger.error("处理第{}行数据失败, error: {}", i + 1, e.getMessage());
                    failCount++;
                }
            }

            logger.info("putInConsumeRecordInfoExcel 完成，成功: {}, 失败: {}", successCount, failCount);
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("message", String.format("处理完成，成功: %d, 失败: %d", successCount, failCount));

            return new ApiResult<>().ok(result);
        } catch (Exception e) {
            logger.error("putInConsumeRecordInfoExcel error: {}", e.getMessage(), e);
            return new ApiResult<>().error("处理失败: " + e.getMessage());
        }
    }


    @RequireRole
    @RequestMapping("/putInConsumeRecordReports")
    public void putInConsumeRecordReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("putInConsumeRecordReports param startDate={}, endDate={}", startDate, endDate);

        try {
            // 设置时间范围
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);

            // 查询数据
            List<PutInConsumeRecordVO> result = reportsServer.getAllPutInConsumeRecords(timeArr[0], timeArr[1]);

            // 导出Excel
            ExcelUtils.exportExcel(response, result, PutInConsumeRecordVO.class, "put_in_consume_record", "put_in_consume_record");

            logger.info("putInConsumeRecordReports 执行完毕，导出记录数: {}", result.size());
        } catch (Exception e) {
            logger.error("putInConsumeRecordReports error: {}", e.getMessage(), e);
        }
    }

}
