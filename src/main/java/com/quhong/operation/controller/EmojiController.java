package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.EmojiService;
import com.quhong.operation.share.condition.EmojiCondition;
import com.quhong.operation.share.vo.EmojiConfigVO;
import com.quhong.operation.share.vo.EmojiVO;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


/**
 * 运营平台/表情管理
 */
@RestController
@RequestMapping(value = "/emoji", produces = MediaType.APPLICATION_JSON_VALUE)
public class EmojiController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(EmojiController.class);
    private final static String filePath = "emoji/";

    @Resource
    private EmojiService emojiService;

    /**
     * 表情分类配置
     */
    @RequireRole
    @RequestMapping("/configList")
    public String emojiConfigList(@RequestBody EmojiCondition condition) {
        logger.info("get emojiConfigList {}", condition);
        return createResult(HttpCode.SUCCESS, emojiService.emojiConfigList(condition));
    }

    @RequireRole
    @RequestMapping("/addConfig")
    public String addEmojiConfig(@RequestBody EmojiConfigVO dto) {
        logger.info("addEmojiConfig {}", JSONObject.toJSONString(dto));
        emojiService.addEmojiConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @RequestMapping("/updateConfig")
    public String updateEmojiConfig(@RequestBody EmojiConfigVO dto) {
        logger.info("updateConfig {}", JSONObject.toJSONString(dto));
        emojiService.updateEmojiConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    /**
     * 分页获取表情列表
     */
    @RequireRole
    @RequestMapping("/list")
    public String emojiList(@RequestBody EmojiCondition condition) {
        logger.info("get emojiList {}", condition);
        return createResult(HttpCode.SUCCESS, emojiService.emojiList(condition));
    }

    /**
     * 新增表情
     */
    @RequireRole
    @PostMapping("/addData")
    public String addEmojiData(@RequestBody EmojiVO dto) {
        try {
            logger.info("addEmojiData {}", dto);
            emojiService.addEmojiData(dto);
            return createResult(HttpCode.SUCCESS, "");
        } catch (CommonException e) {
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e) {
            return createResult(HttpCode.SERVER_ERROR, "");
        }
    }

    /**
     * 更新表情
     */
    @RequireRole
    @PostMapping("/updateData")
    public String updateEmojiData(@RequestBody EmojiVO dto) {
        try {
            logger.info("updateEmojiData {}", dto);
            if (ObjectUtils.isEmpty(dto.getDocId())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }
            emojiService.updateEmojiData(dto);
            return createResult(HttpCode.SUCCESS, "");
        } catch (CommonException e) {
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e) {
            return createResult(HttpCode.SERVER_ERROR, "");
        }
    }

    /**
     * 表情图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return OSSUploadUtils.upload(file, filePath);
    }
}
