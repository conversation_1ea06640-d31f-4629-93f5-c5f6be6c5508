package com.quhong.operation.controller;

import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.report.SvipReportsServer;
import com.quhong.operation.share.vo.SvipSubInfoVo;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/20
 */
@RestController
@RequestMapping("/svipReports")
public class SvipReportsController {

    private static final Logger logger = LoggerFactory.getLogger(SvipReportsController.class);
    @Autowired
    private SvipReportsServer svipReportsServer;

    /**
     * svip订阅展示列表
     *
     * @param startDate 开始时间
     * @param endDate   结尾时间
     * @return 列表数据
     */
    @RequestMapping("/svipSubList")
    public HttpResult<List<SvipSubInfoVo>> getSvipSubscriptionList(String startDate, String endDate) {
        logger.info("method getSvipSubscriptionList param starDate={}, endDate={}", startDate, endDate);
        HttpResult<List<SvipSubInfoVo>> result = new HttpResult<>();

        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List<SvipSubInfoVo>> apiResult = svipReportsServer.svipSubscriptionInfo(timeArr[0], timeArr[1]);
        if (apiResult.isOK()) {
            result.ok(apiResult.getData());
        } else {
            logger.error("method getSvipSubscriptionList error msg={}", apiResult.getMsg());
            result.error(apiResult.getMsg());
        }
        return result;
    }

    /**
     * svip报表下载列表
     *
     * @param startDate 开始时间
     * @param endDate   结尾时间
     */
    @RequestMapping("/svipSubReports")
    public void getSvipSubscriptionReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("method getSvipSubscriptionReports param starDate={}, endDate={}", startDate, endDate);

        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List<List<String>>> apiResult = svipReportsServer.svipSubscriptionInfo1(timeArr[0], timeArr[1]);
        if (!apiResult.isOK()) {
            logger.error("method getSvipSubscriptionReports error msg={}", apiResult.getMsg());
            return;
        }
        String[] titleArr = {"日期", "新增订阅人数", "续订人数", "取消订阅人数", "复购订阅人数", "订阅总人数", "总收入"};
        ExcelUtils.exportExcel(response, apiResult.getData(), titleArr, "svip_subscription_info", "svip订阅详情");
        logger.info("method getSvipSubscriptionReports 执行完毕");
    }

}
