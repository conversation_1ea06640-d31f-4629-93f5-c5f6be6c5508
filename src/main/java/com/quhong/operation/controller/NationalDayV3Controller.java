package com.quhong.operation.controller;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.NationalDayV2Dao;
import com.quhong.mongo.data.NationalDayV2Data;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.ActivityShareConfigDao;
import com.quhong.mysql.data.ActivityShareConfigData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.ActivityTemplateCondition;
import com.quhong.operation.share.dto.NationalDayV2DTO;
import com.quhong.operation.share.dto.NationalDayV3DTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.NationalDayV2Redis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/nationalDayV3")
public class NationalDayV3Controller extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(NationalDayV3Controller.class);

    @Resource
    private NationalDayV2Dao nationalDayV2Dao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private NationalDayV2Redis nationalDayV2Redis;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ActivityShareConfigDao activityShareConfigDao;

    /**
     * 保存活动模板
     */
    @RequireRole(2)
    @RequestMapping("/save_template")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody NationalDayV3DTO dto) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(dto));
            NationalDayV2Data template = new NationalDayV2Data();
            BeanUtils.copyProperties(dto, template);
            String errorMsg = checkDto(template);
            if (!StringUtils.isEmpty(errorMsg)) {
                return result.error(errorMsg);
            }
            ActivityShareConfigData shareConfigData = activityShareConfigDao.selectOne(template.getShareId());
            if (shareConfigData == null) {
                return result.error("分享id不合法 id:" + template.getShareId());
            }
            template.setTemplateType(NationalDayV2Dao.NATIONAL_DAY_TEMPLATE_2025);
            template.setAcUrl(ServerConfig.isProduct() ? "https://static.youstar.live/national_2025/" : "https://test2.qmovies.tv/national_2025/");

            if (dto.getIsCopyToGrayTest() == 1) {
                template.setIsGrayTest(1);
            }
            nationalDayV2Dao.save(template);

            // 更新分享配置
            shareConfigData.setUrl(getShareActivityUrl(template.getAcUrl(), template.getShareId(), template.get_id().toString()));
            activityShareConfigDao.update(shareConfigData);
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "正式服-用户成功创建2025国庆节活动", "活动名：" + template.getAcNameEn());
            }
            if (dto.getIsCopyToGrayTest() == 1) {
                if (!template.getAcNameEn().startsWith("test")) {
                    String acNameEn = String.format("test_%s_%s", template.get_id().getCounter(), template.getAcNameEn());
                    nationalDayV2Dao.updateData(template, new Update().set("acNameEn", acNameEn));
                }
            }

        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 更新活动模板
     */
    @RequireRole(2)
    @RequestMapping("/update_template")
    public HttpResult updateTemplate(HttpServletRequest request, @RequestBody NationalDayV3DTO dto) {
        HttpResult result = new HttpResult();
        try {
            if (Objects.isNull(dto.getActivityId())) {
                logger.error("The activityId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            String errorMsg = checkDto(dto);
            if (!StringUtils.isEmpty(errorMsg)) {
                return result.error(errorMsg);
            }

            logger.info("update template activityId={} data={}", dto.getActivityId(), JSON.toJSONString(dto));

            NationalDayV2Data templateToUpdate = nationalDayV2Dao.findData(dto.getActivityId());
            if (Objects.isNull(templateToUpdate)) {
                logger.error("templateToUpdate is empty. id={}", dto.getActivityId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            if (templateToUpdate.getStatus() == 1 && ServerConfig.isProduct()) {
                return result.error("活动已结束，无法更新");
            }


            if (dto.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }

            if (!Objects.equals(dto.getShareId(), templateToUpdate.getShareId())) {
                ActivityShareConfigData shareConfigData = activityShareConfigDao.selectOne(dto.getShareId());
                if (shareConfigData == null) {
                    return result.error("分享id不合法 id:" + dto.getShareId());
                }
                shareConfigData.setUrl(getShareActivityUrl(templateToUpdate.getAcUrl(), dto.getShareId(), templateToUpdate.get_id().toString()));
                activityShareConfigDao.update(shareConfigData);
            }

            Update update = new Update();
            update.set("acNameEn", dto.getAcNameEn());
            update.set("acNameAr", dto.getAcNameAr());
            update.set("startTime", dto.getStartTime());
            update.set("endTime", dto.getEndTime());
            update.set("homePageType", dto.getHomePageType());
            update.set("homePagePicture", dto.getHomePagePicture());
            update.set("homePagePictureAr", dto.getHomePagePictureAr());
            update.set("homePagePictureDt", dto.getHomePagePictureDt());
            update.set("homePagePictureDtAr", dto.getHomePagePictureDtAr());
            update.set("backgroundHome", dto.getBackgroundHome());
            update.set("templateStyle", dto.getTemplateStyle());
            update.set("flagIcon", dto.getFlagIcon());

            update.set("helpLevelList", dto.getHelpLevelList());
            update.set("levelDrawResKey1", dto.getLevelDrawResKey1());
            update.set("levelDrawResKey3", dto.getLevelDrawResKey3());
            update.set("levelDrawResKey2", dto.getLevelDrawResKey2());
            update.set("levelDrawResKey4", dto.getLevelDrawResKey4());
            update.set("levelDrawResKey5", dto.getLevelDrawResKey5());
            update.set("allLevelDrawResKey", dto.getAllLevelDrawResKey());
            update.set("helpUserResKey", dto.getHelpUserResKey());
            update.set("advanceResKey", dto.getAdvanceResKey());

            update.set("shareId", dto.getShareId());
            update.set("publish", dto.getPublish());

            update.set("countryMp3Url", dto.getCountryMp3Url());
            update.set("advanceVideoUrl", dto.getAdvanceVideoUrl());
            update.set("processFlag", dto.getProcessFlag());
            update.set("helpShowFlag", dto.getHelpShowFlag());
            update.set("backgroundInteract", dto.getBackgroundInteract());
            update.set("flagIconDt", dto.getFlagIconDt());
            update.set("helpBanner", dto.getHelpBanner());
            update.set("addFriendMsg", dto.getAddFriendMsg());

            update.set("giftId", dto.getGiftId());
            update.set("advanceConfig", dto.getAdvanceConfig());
            update.set("mtime", DateHelper.getNowSeconds());
            nationalDayV2Dao.updateData(templateToUpdate, update);

        } catch (Exception e) {
            logger.error("update template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 活动模板分页查询
     */
    @RequireRole(2)
    @RequestMapping("/page")
    public String selectPage(@RequestBody ActivityTemplateCondition condition) {


        logger.info("NationalDayV3 selectPage condition={}", JSON.toJSONString(condition));
        HttpResult<PageResultVO<NationalDayV3DTO>> result = new HttpResult<>();
        PageResultVO<NationalDayV3DTO> pageVO = new PageResultVO<>();
        try {
            List<NationalDayV2Data> rankingActivities = nationalDayV2Dao
                    .selectPage(condition.getAcNameEn(), (condition.getPage() - 1) * 10, 10, NationalDayV2Dao.NATIONAL_DAY_TEMPLATE_2025);
            List<NationalDayV3DTO> dtoList = new ArrayList<>();
            String token = basePlayerRedis.getToken(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
            rankingActivities.forEach(a -> {
                NationalDayV3DTO dto = new NationalDayV3DTO();
                dto.setActivityId(a.get_id().toString());
                BeanUtils.copyProperties(a, dto);
                dto.setUid(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
                dto.setToken(token);
                dtoList.add(dto);
            });
            pageVO.setList(dtoList);
            pageVO.setTotal(nationalDayV2Dao.selectCount(NationalDayV2Dao.NATIONAL_DAY_TEMPLATE_2025));
        } catch (Exception e) {
            logger.info("NationalDayV3 selectPage error", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 活动模板详情
     */
    @RequireRole(2)
    @RequestMapping("/select_one")
    public String selectOne(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<NationalDayV3DTO> result = new HttpResult<>();
        if (Objects.isNull(condition.getActivityId())) {
            logger.error("The activityId cannot be empty.");
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        NationalDayV2Data data = nationalDayV2Dao.findData(condition.getActivityId());
        NationalDayV3DTO dto = new NationalDayV3DTO();
        dto.setActivityId(data.get_id().toString());
        BeanUtils.copyProperties(data, dto);
        return JSON.toJSONString(result.ok(dto), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    private String checkDto(NationalDayV2Data template) {
        String errorMsg = "";
        if (null == template.getGiftId()
                || null == template.getAdvanceConfig()
                || null == template.getStartTime()
                || null == template.getEndTime()
                || null == template.getAcNameAr()
                || null == template.getAcNameEn()
                || null == template.getShareId()

        ) {
            return errorMsg = ApiCode.PARAM_ERROR.getMsg();
        }

        if (template.getGiftId() <= 0) {
            return errorMsg = "礼物id配置错误";
        }

//        if (null == template.getHomePagePicture()
//                || null == template.getHomePagePictureAr()
//                || null == template.getHomePagePictureDt()
//                || null == template.getHomePagePictureDtAr()
//                || null == template.getBackgroundHome()
//                || null == template.getFlagIcon()
//        || null == template.getTemplateStyle()
//        ) {
//            return errorMsg = "ui资源不能配置为空";
//        }

        if (null == template.getLevelDrawResKey3()
                || null == template.getLevelDrawResKey1()
                || null == template.getLevelDrawResKey2()
                || null == template.getLevelDrawResKey4()
                || null == template.getLevelDrawResKey5()
                || null == template.getAllLevelDrawResKey()
                || null == template.getHelpUserResKey()
                || null == template.getAdvanceResKey()
        ) {
            return errorMsg = "资源key不能配置为空";
        }

        if (CollectionUtils.isEmpty(template.getHelpLevelList())) {
            return errorMsg = "助力任务数值列表不能为空";
        }


        if (template.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
            return errorMsg = "活动结束时间距离现在太近，请检查";
        }
        return errorMsg;
    }


    private String getShareActivityUrl(String url, int shareId, String activityId) {
        if (org.springframework.util.StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        urlBuilder.replaceQueryParam("activityId", activityId);
        urlBuilder.replaceQueryParam("shareId", shareId);
        return urlBuilder.build(false).encode().toUriString();
    }
}
