package com.quhong.operation.controller;

import com.alibaba.fastjson2.JSON;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.LuckyGiftConfigData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.LuckyGiftService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.LuckyGiftVO;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 运营系统/幸运礼物配置
 */

@RestController
@RequestMapping(value = "/luckyGift", produces = MediaType.APPLICATION_JSON_VALUE)
public class LuckyGiftController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(LuckyGiftController.class);
    private final static String filePath = "common/";

    @Resource
    private LuckyGiftService luckyGiftService;


    @RequireRole
    @RequestMapping("/list")
    public String getDataList(@RequestBody BaseCondition condition) {
        logger.info("getDataList {}", condition);
        return createResult(HttpCode.SUCCESS, luckyGiftService.getDataList(condition));
    }

    @RequireRole
    @PostMapping("/addData")
    public String addData(@RequestBody LuckyGiftVO dto) {
        try {
            logger.info("add luckyGift Data {}", dto);
            luckyGiftService.addData(dto);
            return createResult(HttpCode.SUCCESS, "");
        } catch (CommonException e) {
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e) {
            throw e;
        }
    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateData(@RequestBody LuckyGiftVO dto) {
        try {
            logger.info("update luckyGift Data {}", dto);
            if (StringUtils.isEmpty(dto.getRid())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }

            luckyGiftService.updateData(dto);
            return createResult(HttpCode.SUCCESS, "");
        } catch (CommonException e) {
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e) {
            throw e;
        }
    }


    @RequireRole
    @PostMapping("/deleteData")
    public String deleteData(@RequestBody LuckyGiftVO dto) {
        try {
            logger.info("delete  luckyGift Data {}", dto);
            if (StringUtils.isEmpty(dto.getRid())) {
                return createResult(HttpCode.PARAM_ERROR, "");
            }

            luckyGiftService.deleteData(dto);
            return createResult(HttpCode.SUCCESS, "");
        } catch (CommonException e) {
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 幸运礼物奖池配置列表
     */
    @RequireRole
    @RequestMapping("/listJackpotConfig")
    public HttpResult<Object> listJackpotConfig() {
        logger.info("listJackpotConfig");
        return new HttpResult<>().ok(luckyGiftService.listJackpotConfig());
    }

    /**
     * 新增幸运礼物奖池配置
     * 每轮奖励数量=prize
     * 中奖倍数=rewardNum
     */
    @RequireRole
    @PostMapping("/addJackpotConfig")
    public HttpResult<Object> addJackpotConfig(@RequestBody LuckyGiftConfigData dto) {
        try {
            logger.info("addJackpotConfig Data {}", JSON.toJSONString(dto));
            if (dto.getPrize() <= 0 || dto.getRewardNum() <= 0 || dto.getRewardNum() > 5000 || dto.getPrize() > 5000) {
                return new HttpResult<>().error(1, HttpCode.PARAM_ERROR.getMsg());
            }
            luckyGiftService.addJackpotConfig(dto);
            return new HttpResult<>().ok();
        } catch (CommonException e) {
            return new HttpResult<>().error(1, e.getHttpCode().getMsg());
        }
    }

    /**
     * 更新幸运礼物奖池配置
     * 每轮奖励数量=prize
     * 中奖倍数=rewardNum
     */
    @RequireRole
    @PostMapping("/updateJackpotConfig")
    public HttpResult<Object> updateJackpotConfig(@RequestBody LuckyGiftConfigData dto) {
        try {
            logger.info("updateJackpotConfig Data {}", JSON.toJSONString(dto));
            if (dto.getPrize() <= 0 || dto.getRewardNum() <= 0 || dto.getRewardNum() > 5000 || dto.getPrize() > 5000) {
                return new HttpResult<>().error(1, HttpCode.PARAM_ERROR.getMsg());
            }
            luckyGiftService.updateJackpotConfig(dto);
            return new HttpResult<>().ok();
        } catch (CommonException e) {
            return new HttpResult<>().error(1, e.getHttpCode().getMsg());
        }
    }

    /**
     * 删除幸运礼物奖池配置
     */
    @RequireRole
    @GetMapping("/deleteJackpotConfig")
    public HttpResult<Object> deleteJackpotConfig(@RequestParam int id) {
        try {
            logger.info("deleteJackpotConfig Data {}", id);
            luckyGiftService.deleteJackpotConfig(id);
            return new HttpResult<>().ok();
        } catch (CommonException e) {
            return new HttpResult<>().error(1, e.getHttpCode().getMsg());
        }
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return AWSUploadUtils.upload(file, filePath);
    }

}
