package com.quhong.operation.controller;

import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.handler.HttpEnvData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.WorldCupService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.RoomBGCondition;
import com.quhong.operation.share.dto.RoomBGDTO;
import com.quhong.operation.share.vo.WorldCupMatchVO;
import com.quhong.operation.share.vo.WorldCupTeamVO;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 房间背景图设置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/worldCup", produces = MediaType.APPLICATION_JSON_VALUE)
public class WorldCupController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(WorldCupController.class);
    private final static String filePath = "common/";

    @Resource
    private WorldCupService worldCupService;



    @RequireRole
    @RequestMapping("/teamList")
    public String teamList(@RequestBody BaseCondition condition) {

        logger.info("get teamList {}", condition);
        return createResult(HttpCode.SUCCESS, worldCupService.teamPageList(condition));

    }

    // 冠军列表
    @RequireRole
    @RequestMapping("/championList")
    public String championList() {
        return createResult(HttpCode.SUCCESS, worldCupService.championList());
    }

    @RequireRole
    @PostMapping("/addTeamData")
    public String addTeamData(@RequestBody WorldCupTeamVO dto) {
        logger.info("addTeamData {}", dto);
        return createResult(HttpCode.SUCCESS, worldCupService.addTeamData(dto));
    }

    @RequireRole
    @PostMapping("/updateTeamData")
    public String updateTeamData(@RequestBody WorldCupTeamVO dto) {
        logger.info("updateTeamData {}", dto);
        if (StringUtils.isEmpty(dto.getId())) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }
        return createResult(HttpCode.SUCCESS, worldCupService.updateTeamData(dto));
    }

    // 设置竞赛
    @RequireRole
    @RequestMapping("/matchList")
    public String matchList(@RequestBody WorldCupMatchVO dto) {
        return createResult(HttpCode.SUCCESS, worldCupService.matchList(dto));

    }

    @RequireRole
    @PostMapping("/addMatchData")
    public String addMatchData(@RequestBody WorldCupMatchVO dto) {
        logger.info("addMatchData {}", dto);
        return createResult(HttpCode.SUCCESS, worldCupService.addMatchData(dto));
    }

    @RequireRole
    @PostMapping("/updateMatchData")
    public String updateMatchData(@RequestBody WorldCupMatchVO dto) {
        logger.info("updateRoomBG {}", dto);
        if (StringUtils.isEmpty(dto.getId())) {
            return createResult(HttpCode.PARAM_ERROR, "");
        }
        return createResult(HttpCode.SUCCESS, worldCupService.updateMatchData(dto));
    }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return OSSUploadUtils.upload(file, filePath);
    }

}
