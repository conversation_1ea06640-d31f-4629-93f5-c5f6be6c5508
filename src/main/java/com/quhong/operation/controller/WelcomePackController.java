package com.quhong.operation.controller;


import com.alibaba.fastjson.JSONObject;
import com.quhong.data.vo.WelcomePackReceiverVO;
import com.quhong.datas.HttpResult;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.WelcomePackService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.WelcomePackLogCondition;
import com.quhong.operation.share.dto.SendWelcomePackDTO;
import com.quhong.operation.share.dto.WelcomePackConfigDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.WelcomePackRecordVO;
import com.quhong.operation.share.vo.WelcomePackVO;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 礼包控制器
 *
 * <AUTHOR>
 * @date 2025/9/3 13:43
 */
@RestController
@RequestMapping("welcomePack")
public class WelcomePackController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private WelcomePackService welcomePackService;

    /**
     * 欢迎礼包配置列表
     */
    @RequireRole(3)
    @PostMapping("/list")
    public HttpResult<PageResultVO<WelcomePackVO>> selectList(@RequestParam String uid, @RequestBody BaseCondition condition) {
        logger.info("select welcome pack list. uid={} page={} pageSize={}", uid, condition.getPage(), condition.getPageSize());
        return HttpResult.getOk(welcomePackService.selectList(uid, condition));
    }

    /**
     * 欢迎礼包配置新增或修改
     */
    @RequireRole(5)
    @PostMapping("/save")
    public HttpResult<Object> saveOrUpdate(@RequestParam String uid, @RequestBody WelcomePackConfigDTO dto) {
        logger.info("saveOrUpdate welcome pack config. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        welcomePackService.saveOrUpdate(uid, dto);
        return HttpResult.getOk();
    }

    /**
     * 礼包删除
     *
     * @param uid 操作者uid
     * @param id  礼包id
     * @return com.quhong.datas.HttpResult<java.lang.Object>
     */
    @RequireRole(3)
    @PostMapping("/remove")
    public HttpResult<Object> remove(@RequestParam String uid, @RequestParam Integer id) {
        logger.info("remove welcome pack config. uid={} id={}", uid, id);
        welcomePackService.remove(uid, id);
        return HttpResult.getOk();
    }

    /**
     * rid校验
     * @param uid 操作者uid
     * @param ridsText rid列表用逗号隔开
     * @return com.quhong.datas.HttpResult<com.quhong.vo.PageVO<com.quhong.data.vo.WelcomePackReceiverVO>>
     */
    @RequireRole(3)
    @PostMapping("/check")
    public HttpResult<PageVO<WelcomePackReceiverVO>> check(@RequestParam String uid, @RequestParam String ridsText) {
        logger.info("check send welcome pack list. uid={} ridsText={}", uid, ridsText);
        return HttpResult.getOk(welcomePackService.check(ridsText));
    }

    /**
     * 递送欢迎礼包
     */
    @RequireRole(3)
    @PostMapping("/send")
    public HttpResult<Object> send(@RequestParam String uid, @RequestBody SendWelcomePackDTO dto) {
        logger.info("send welcome pack. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        welcomePackService.send(uid, dto);
        return HttpResult.getOk();
    }

    /**
     * 礼包递送记录列表
     */
    @RequireRole(3)
    @PostMapping("/record")
    public HttpResult<PageResultVO<WelcomePackRecordVO>> record(@RequestParam String uid, @RequestBody WelcomePackLogCondition condition) {
        logger.info("select welcome pack record list. uid={} page={} pageSize={}", uid, condition.getPage(), condition.getPageSize());
        return HttpResult.getOk(welcomePackService.record(condition));
    }

    /**
     * 撤回欢迎礼包
     */
    @RequireRole(3)
    @PostMapping("/withdraw")
    public HttpResult<Object> withdraw(@RequestParam String uid, @RequestBody SendWelcomePackDTO dto) {
        logger.info("withdraw welcome pack. uid={} dto{}", uid, JSONObject.toJSONString(dto));
        welcomePackService.withdraw(uid, dto);
        return HttpResult.getOk();
    }
}
