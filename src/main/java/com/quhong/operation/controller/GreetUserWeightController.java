package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.GreetWeightData;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.GreetUserWeightService;
import com.quhong.operation.share.vo.GreetUserWeightVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 社交推荐用户权重设置
 */
@RestController
@RequestMapping(value = "/greetUserWeight", produces = MediaType.APPLICATION_JSON_VALUE)
public class GreetUserWeightController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(GreetUserWeightController.class);

    @Resource
    private GreetUserWeightService greetUserWeightService;

    /**
     * 用户视角列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<GreetUserWeightVO> list() {
        logger.info("get GreetUserWeight ");
        return HttpResult.getOk(greetUserWeightService.list());
    }

    /**
     * 更新推荐用户权重设置
     */
    @RequireRole
    @RequestMapping("/update")
    public HttpResult<?> updateData(@RequestParam String uid, @RequestBody GreetWeightData dto) {
        logger.info("updateData GreetWeightData {}", JSONObject.toJSONString(dto));
        greetUserWeightService.updateData(uid, dto);
        return HttpResult.getOk();
    }
}
