package com.quhong.operation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.*;

/**
 * 时间时区管理类
 *
 * <AUTHOR>
 * @date 2020/7/4
 */
public class DateHelper {

    private static final Logger logger = LoggerFactory.getLogger(DateHelper.class);

    public static final DateHelper ARABIAN = new DateHelper(TimeZone.getTimeZone("GMT+3"));
    public static final DateHelper BEIJING = new DateHelper(TimeZone.getTimeZone("GMT+8"));
    public static final DateHelper INDIAN = new DateHelper(TimeZone.getTimeZone("GMT+5:30"));
    public static final String YYYY_MM = "yyyy_MM";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String HH_MM_SS = "HH:mm:ss";
    public static final String YYYYMMDD = "yyyyMMdd";

    private TimeZone timeZone;

    private ThreadLocalCalendar localCalendar;

    private ThreadLocalDateFormat tableSuffixFormat;
    private ThreadLocalDateFormat dateFormat;
    private ThreadLocalDateFormat dateFormat2;
    private ThreadLocalDateFormat datetimeFormat;
    private ThreadLocalDateFormat hourFormat;

    private DateHelper(TimeZone timeZone) {
        this.timeZone = timeZone;
        this.localCalendar = new ThreadLocalCalendar(this.timeZone);
        this.tableSuffixFormat = new ThreadLocalDateFormat(YYYY_MM, this.timeZone);
        this.dateFormat = new ThreadLocalDateFormat(YYYY_MM_DD, this.timeZone);
        this.dateFormat2 = new ThreadLocalDateFormat(YYYYMMDD, this.timeZone);
        this.datetimeFormat = new ThreadLocalDateFormat(YYYY_MM_DD_HH_MM_SS, this.timeZone);
        this.hourFormat = new ThreadLocalDateFormat(HH_MM_SS, this.timeZone);
    }

    public Date parseDay2(String day) {
        try {
            return dateFormat2.get().parse(day);
        } catch (ParseException e) {
            logger.error("parse day2 error. {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 返回时间戳 单位：秒
     *
     * @return 秒时间戳
     */
    public Integer currentTimeZoneSeconds() {
        Long currentTime = System.currentTimeMillis() / 1000;
        return currentTime.intValue();
    }

    /**
     * 给时间加减天数，如果date为空则视为new Date()
     *
     * @param date   时间
     * @param amount 加减天数
     * @return 处理后的时间
     */
    public Date dateAddDay(Date date, int amount) {
        Calendar calendar = this.localCalendar.get();
        if (null == date)
            date = new Date();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, amount);
        return calendar.getTime();
    }

    /**
     * 给时间加减天数
     *
     * @param time   时间戳 单位：毫秒
     * @param amount 加减的天数
     * @return 处理后的时间
     */
    public Date dateAddDay(long time, int amount) {
        return dateAddDay(new Date(time), amount);
    }

    /**
     * 加减月份
     *
     * @param date   时间
     * @param amount 加减的月份
     * @return 返回时间
     */
    public Date dateAddMonth(Date date, int amount) {
        Calendar calendar = this.localCalendar.get();
        if (null == date)
            date = new Date();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, amount);
        return calendar.getTime();
    }

    /**
     * 获取过滤的时间范围
     *
     * @param dateStr 日期
     * @return 时间范围
     */
    public Integer[] getTimeWhereRange(String dateStr) {
        Integer startTime = stringDateToStampSecond(dateStr);
        int endTime = startTime + (24 * 60 * 60);
        return new Integer[]{startTime, endTime};
    }

    /**
     * 获取搜索查询时间过滤字段的开始时间
     *
     * @param startDate  开始时间字符串
     * @param endSeconds 结束时间秒数时间戳
     * @return 开始时间秒数时间戳
     */
    public Integer getStartSeconds(String startDate, Integer endSeconds) {
        if (StringUtil.isEmptyOrBlank(startDate)) {
            if (endSeconds < 0) return -1;
            return endSeconds - (24 * 60 * 60);
        } else {
            return stringDateToStampSecond(startDate);
        }
    }

    /**
     * 获取搜索查询时间过滤字段的结束时间
     *
     * @param endDate 日期字符串
     * @return 结束时间秒数时间戳
     */
    public Integer getEndSeconds(String endDate) {
        Integer endTime = 0;
        if (StringUtil.isEmptyOrBlank(endDate)) {
            // 当前时间 （时分秒是00:00:00）
            Long endStamp = setStartTime(new Date());
            // 换成秒并
            endStamp = endStamp / 1000;
            endTime = endStamp.intValue();
        } else {
            endTime = stringDateToStampSecond(endDate);
        }
        if (endTime < 1) return -1;
        // 把时间设置为当天的结束时间
        return endTime + 24 * 60 * 60;
    }

    /**
     * 解析过滤的时间
     *
     * @param startDate 开始时间
     * @param endDate   结尾时间
     * @return 时间数组
     */
    public Integer[] getStartOrEndSeconds(String startDate, String endDate) {
        Integer endTime = DateHelper.ARABIAN.getEndSeconds(endDate);
        Integer startTime = DateHelper.ARABIAN.getStartSeconds(startDate, endTime);
        return new Integer[]{startTime, endTime};
    }

    /**
     * 设置某天的零时零分零秒的时间
     *
     * @param time
     * @return
     */
    public Long setStartTime(Long time) {
        return setStartTime(new Date(time));
    }

    /**
     * 设置某天的零时零分零秒的时间
     *
     * @param date
     * @return
     */
    public Long setStartTime(Date date) {
        Calendar calendar = localCalendar.get();
        if (null == date)
            date = new Date();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    public String getYesterdayStr(String strDate) {
        try {
            Date date = stringToDate(strDate);
            Date yesterday = new Date(date.getTime() - 86400000L);
            return dateFormat.get().format(yesterday);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 将字符串日期转换成秒数
     *
     * @param strDate “2020-03-23”
     * @return 秒时间戳
     */
    public Integer stringDateToStampSecond(String strDate) {
        try {
            long time = dateFormat.get().parse(strDate).getTime();
            return new Long(time / 1000).intValue();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return -1;
    }

    /**
     * 将字符串日期转换成时间
     *
     * @param strDate “2020-03-23”
     * @return Date
     */
    public Date stringToDate(String strDate) {
        try {
            return dateFormat.get().parse(strDate);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 时间转字符串
     *
     * @param date 时间
     * @return 字符串时间 "2020-07-12"
     */
    public String dateToStr(Date date) {
        try {
            String str = dateFormat.get().format(date);
            return str;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 时间转字符串
     *
     * @param date 时间
     * @return 字符串时间 "2020-07-12 12:32:59"
     */
    public String datetimeToStr(Date date) {
        try {
            return datetimeFormat.get().format(date);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 毫秒数时间戳转时间字符串
     *
     * @param time 毫秒数时间戳
     * @return 时间字符串
     */
    public String timestampToDatetimeStr(Long time) {
        Date date = new Date(time);
        return datetimeToStr(date);
    }

    /**
     * 通过时间获取表名后缀
     *
     * @param dateStr 时间
     * @return 表名后缀
     */
    public String getTableSuffixByString(String dateStr) {
        try {
            Date date = DateHelper.ARABIAN.stringToDate(dateStr);
            String tableSuffix = tableSuffixFormat.get().format(date);
            return tableSuffix;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 通过时间获取表名后缀
     *
     * @param date 时间
     * @return 表名后缀
     */
    public String getTableSuffixByDate(Date date) {
        try {
            String tableSuffix = tableSuffixFormat.get().format(date);
            return tableSuffix;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 通过时间获取表名后缀
     *
     * @param start 开始时间
     * @param end   结尾时间
     * @return 表名后缀
     */
    public List<String> getTableSuffixArr(Integer start, Integer end) {
        List<String> tableSuffixArr = new ArrayList<>();
        while (start < end) {
            // 获取表名后缀
            String tableSuffix = getTableSuffixByDate(new Date(start * 1000L));
            // 表名不为空则添加到集合中
            if (!StringUtil.isEmptyOrBlank(tableSuffix)) tableSuffixArr.add(tableSuffix);
            // 时间加一个月
            Date nextMonth = dateAddMonth(new Date(start * 1000L), 1);
            // 获取这个月月初的秒数时间戳
            start = getMonthFirstDay(nextMonth);

        }
        return tableSuffixArr;
    }

    /**
     * 获取下一个月的表名
     *
     * @param date 这次时间
     * @return 下个月表名后缀
     */
    public String getNextMonthTableSuffix(Date date) {
        date = dateAddMonth(date, 1);
        return getTableSuffixByDate(date);
    }

    /**
     * 获取上一个月的表名
     *
     * @param date 这次时间
     * @return 下个月表名后缀
     */
    public String getPreviousMonthTableSuffix(Date date) {
        date = dateAddMonth(date, -1);
        return getTableSuffixByDate(date);
    }

    /**
     * 获取某个月的第一天
     *
     * @param date 时间，如果为空则new Date()
     * @return 时间戳 单位：秒
     */
    public Integer getMonthFirstDay(Date date) {
        Calendar calendar = localCalendar.get();
        if (null == date)
            date = new Date();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Long time = setStartTime(calendar.getTime()) / 1000;
        return time.intValue();
    }

    /**
     * 返回两个日期之间的天数差
     * 天数向下取整
     *
     * @param end   第一个日期
     * @param start 第二个日期
     * @return 两个日期之间相差天数
     */
    public Integer dateDiff(Long end, Long start) {
        if (end == null || start == null) return 0;
        Long diff = (end - start) / 1000 / 60 / 60 / 24;
        return diff.intValue();
    }

    /**
     * 获取两个时间段的日期数组
     *
     * @param start 开始时间秒数
     * @param end   结束时间秒数
     * @return 返回区间天数
     */
    public String[] getDateDiffArray(Integer start, Integer end) {
        //todo 这个接口待完善
        if (end < start) return new String[0];

        Long dStart = setStartTime(start * 1000L);
        Long dEnd = setStartTime(end * 1000L);

        Calendar cStart = Calendar.getInstance();
        cStart.setTimeInMillis(dStart);

        int diff = dateDiff(dEnd, dStart) + 1;
        String[] dateArr = new String[diff];

        //别忘了，把起始日期加上
        int arrIndex = 0;
        dateArr[arrIndex++] = dateToStr(new Date(dStart));

        // 此日期是否在指定日期之后
        while (dEnd > cStart.getTimeInMillis()) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            cStart.add(Calendar.DAY_OF_MONTH, 1);
            dateArr[arrIndex++] = dateToStr(cStart.getTime());
        }
        return dateArr;
    }

    /**
     * 获取当前时间是周几
     * 星期一返回的是0，星期天返回的是6
     *
     * @param date 当前时间
     * @return 返回周几
     */
    public int getWeekOfDate(Date date) {
        if (null == date) date = new Date();
        int[] weekDays = {6, 0, 1, 2, 3, 4, 5};
        //String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar calendar = localCalendar.get();
        calendar.setTime(date);
        int w = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }

    /**
     * 将时分秒的时间戳(秒) 转成字符串 时:分:秒
     * <p>
     * 注意：此方法以utc的时间转换
     *
     * @param time 时分秒的时间戳
     * @return 时:分:秒的字符串
     */
    public String intToTimeString(Integer time) {
        // 一天的秒数
        int diff = 60 * 60 * 24;
        // 等到时分秒的时间戳
        time = time % diff;

        String hour = "0";
        String minute = "0";
        String seconds = "0";

        int m = time / 60;
        int h = m / 60;

        if (h < 10)
            hour += h;
        else
            hour = h + "";

        m = m % 60;
        if (m < 10)
            minute += m;
        else
            minute = m + "";

        long s = time % 60;
        if (s < 10)
            seconds += s;
        else
            seconds = s + "";

        return hour + ":" + minute + ":" + seconds;
    }

    /**
     * 获取小时格式的字符串
     *
     * @param date
     * @return
     */
    public String getHourTime(Date date) {
        return hourFormat.get().format(date);
    }

}
