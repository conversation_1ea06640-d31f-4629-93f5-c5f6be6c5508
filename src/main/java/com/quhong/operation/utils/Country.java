package com.quhong.operation.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
public class Country {
    public static Map<String, String> map = new HashMap<>();

    static {
        map.put("NR", "瑙鲁");
        map.put("NP", "尼泊尔");
        map.put("NO", "挪威");
        map.put("GD", "格林纳达");
        map.put("GE", "格鲁吉亚");
        map.put("NZ", "新西兰");
        map.put("GF", "法属圭亚那");
        map.put("GA", "加蓬");
        map.put("FJ", "斐济");
        map.put("FM", "密克罗尼西亚");
        map.put("FI", "芬兰");
        map.put("OM", "阿曼");
        map.put("FR", "法国");
        map.put("GY", "圭亚那");
        map.put("GN", "几内亚");
        map.put("GM", "冈比亚");
        map.put("PE", "秘鲁");
        map.put("PG", "巴布亚新几内亚");
        map.put("PA", "巴拿马");
        map.put("GH", "加纳");
        map.put("GG", "根西");
        map.put("PL", "波兰");
        map.put("GT", "危地马拉");
        map.put("PH", "菲律宾");
        map.put("GR", "希腊");
        map.put("PK", "巴基斯坦");
        map.put("LS", "莱索托");
        map.put("LR", "利比里亚");
        map.put("DZ", "阿尔及利亚");
        map.put("VU", "瓦努阿图");
        map.put("LV", "拉脱维亚");
        map.put("LU", "卢森堡");
        map.put("LT", "立陶宛");
        map.put("LY", "利比亚");
        map.put("EC", "厄瓜多尔");
        map.put("VN", "越南");
        map.put("DE", "德国");
        map.put("UZ", "乌兹别克斯坦");
        map.put("UY", "乌拉圭");
        map.put("MC", "摩纳哥");
        map.put("MD", "摩尔多瓦");
        map.put("MA", "摩洛哥");
        map.put("DK", "丹麦");
        map.put("DJ", "吉布提");
        map.put("MG", "马达加斯加");
        map.put("VE", "委内瑞拉");
        map.put("MH", "马绍尔群岛");
        map.put("VC", "格林纳丁斯");
        map.put("DO", "多米尼加");
        map.put("ME", "黑山");
        map.put("MK", "马其顿");
        map.put("VA", "梵蒂冈");
        map.put("ML", "马里");
        map.put("MN", "蒙古");
        map.put("MM", "缅甸");
        map.put("UK", "英国");
        map.put("EV", "萨尔瓦多");
        map.put("MR", "毛里塔尼亚");
        map.put("UG", "乌干达");
        map.put("MT", "马耳他");
        map.put("MV", "马尔代夫");
        map.put("MU", "毛里求斯");
        map.put("US", "美国");
        map.put("MX", "墨西哥");
        map.put("MW", "马拉维");
        map.put("MZ", "莫桑比克");
        map.put("MY", "马来西亚");
        map.put("EG", "埃及");
        map.put("TZ", "坦桑尼亚");
        map.put("NA", "纳米比亚");
        map.put("EE", "爱沙尼亚");
        map.put("TT", "特立尼达/多巴哥");
        map.put("TW", "中国台湾");
        map.put("NE", "尼日尔");
        map.put("TV", "图瓦卢");
        map.put("NG", "尼日利亚");
        map.put("UA", "乌克兰");
        map.put("NI", "尼加拉瓜");
        map.put("ET", "埃塞俄比亚");
        map.put("ES", "西班牙");
        map.put("ER", "厄立特里亚");
        map.put("NL", "荷兰");
        map.put("TO", "汤加");
        map.put("TN", "突尼斯");
        map.put("TM", "土库曼斯坦");
        map.put("CA", "加拿大");
        map.put("BZ", "伯利兹");
        map.put("TR", "土耳其");
        map.put("BW", "博茨瓦纳");
        map.put("TG", "多哥");
        map.put("JP", "日本");
        map.put("TD", "乍得");
        map.put("JO", "约旦");
        map.put("BS", "巴哈马");
        map.put("JM", "牙买加");
        map.put("BR", "巴西");
        map.put("BT", "不丹");
        map.put("TH", "泰国");
        map.put("KI", "基里巴斯");
        map.put("BO", "玻利维亚");
        map.put("KH", "柬埔寨");
        map.put("KG", "吉尔吉斯斯坦");
        map.put("BJ", "贝宁");
        map.put("KE", "肯尼亚");
        map.put("BF", "布基纳法索");
        map.put("BG", "保加利亚");
        map.put("BH", "巴林");
        map.put("ST", "圣多美和普林西比");
        map.put("BI", "布隆迪");
        map.put("BB", "巴巴多斯");
        map.put("SY", "叙利亚");
        map.put("SZ", "斯威士兰");
        map.put("BD", "孟加拉国");
        map.put("BE", "比利时");
        map.put("SL", "塞拉利昂");
        map.put("KW", "科威特");
        map.put("SK", "斯洛伐克");
        map.put("SN", "塞内加尔");
        map.put("KZ", "哈萨克斯坦");
        map.put("SM", "圣马力诺");
        map.put("SO", "索马里");
        map.put("SR", "苏里南");
        map.put("SD", "苏丹");
        map.put("CZ", "捷克");
        map.put("SC", "塞舌尔");
        map.put("CY", "塞浦路斯");
        map.put("KR", "韩国");
        map.put("SE", "瑞典");
        map.put("CV", "佛得角");
        map.put("SG", "新加坡");
        map.put("CU", "古巴");
        map.put("KM", "科摩罗");
        map.put("KN", "圣基茨和尼维斯");
        map.put("SI", "斯洛文尼亚");
        map.put("CQ", "赤道几内亚");
        map.put("LI", "列支敦士登");
        map.put("CR", "哥斯达黎加");
        map.put("CO", "哥伦比亚");
        map.put("LK", "斯里兰卡");
        map.put("CM", "喀麦隆");
        map.put("CN", "中国大陆");
        map.put("CK", "库克群岛");
        map.put("SB", "所罗门群岛");
        map.put("CL", "智利");
        map.put("LA", "老挝");
        map.put("RS", "塞尔维亚");
        map.put("LC", "圣卢西亚");
        map.put("LB", "黎巴嫩");
        map.put("CH", "瑞士");
        map.put("RU", "俄罗斯");
        map.put("CF", "中非");
        map.put("RW", "卢旺达");
        map.put("HR", "克罗地亚");
        map.put("RO", "罗马尼亚");
        map.put("HT", "海地");
        map.put("HU", "匈牙利");
        map.put("HK", "中国香港");
        map.put("ZA", "南非");
        map.put("HN", "洪都拉斯");
        map.put("ZW", "津巴布韦");
        map.put("ID", "印度尼西亚");
        map.put("ZR", "刚果");
        map.put("IE", "爱尔兰");
        map.put("ZM", "赞比亚");
        map.put("IQ", "伊拉克");
        map.put("IR", "伊朗");
        map.put("AZ", "阿塞拜疆");
        map.put("YE", "也门");
        map.put("IS", "冰岛");
        map.put("IT", "意大利");
        map.put("BA", "波斯尼亚");
        map.put("AT", "奥地利");
        map.put("AR", "阿根廷");
        map.put("IL", "以色列");
        map.put("QA", "卡塔尔");
        map.put("IN", "印度");
        map.put("AU", "澳大利亚");
        map.put("AL", "阿尔巴尼亚");
        map.put("AO", "安哥拉");
        map.put("PY", "巴拉圭");
        map.put("AN", "荷属安的列斯");
        map.put("PT", "葡萄牙");
        map.put("AD", "安道尔");
        map.put("AG", "安提瓜和巴布达");
        map.put("PR", "波多黎各");
        map.put("AE", "阿联酋");
        map.put("AF", "阿富汗");
    }

    public static String getCountryName(String code) {
        // code是空的或者小于两位则直接返回
        if (StringUtil.isEmptyOrBlank(code) || code.trim().length() < 2) {
            return code;
        }
        // 去前后空格
        code = code.trim();
        // 获取前两位并且转大写
        String countryCode = code.substring(0, 2).toUpperCase();

        // 通过国家编码获取国家中文名称
        String countryName = map.get(countryCode);

        // 如果没有获取到国家中文名称则返回传入的去前后空格的code
        return StringUtil.isEmptyOrBlank(countryName) ? code : countryName;
    }
}
