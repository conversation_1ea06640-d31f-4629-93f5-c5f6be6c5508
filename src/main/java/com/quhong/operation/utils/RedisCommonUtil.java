package com.quhong.operation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

public class RedisCommonUtil {

    private final static Logger logger = LoggerFactory.getLogger(RedisCommonUtil.class);

    /**
     * 获取对应的key的value值
     *
     * @param key key
     * @return
     */
    public static String get(RedisTemplate redisTemplate, String key) {
        Object o = redisTemplate.opsForValue().get(key);
        logger.info("get kye={}, value={}", key, o);
        if (null == o) return "";
        return (String) o;
    }

    /**
     * 往redis的集合中添加成员
     *
     * @param key key
     * @param member 成员
     * @return
     */
    public static boolean sadd(RedisTemplate redisTemplate, String key, String member) {
        Long add = redisTemplate.opsForSet().add(key, member);
        // TODO
        return true;
    }

    /**
     * 批量往redis的集合中添加val
     *
     * @param key key
     * @param member 成员
     * @return
     */
    public static boolean batchSetAdd(RedisTemplate redisTemplate, String key, String... member) {
        Long add = redisTemplate.opsForSet().add(key, member);
        logger.info("batchSetAdd key={}, add={}", key, add);
        return true;
    }

    /**
     * 获取hash对象中的对应key的value
     *
     * @param redisTemplate RedisTemplate
     * @param key key
     * @param item
     * @return
     */
    public static String hget(RedisTemplate redisTemplate, String key, String item) {
        Object o = redisTemplate.opsForHash().get(key, item);
        logger.info("hget key={}, item={}, value={}", key, item, o);
        if (null == o) return "";
        return (String) o;
    }

    /**
     * 往list里加入参数
     *
     * @param redisTemplate RedisTemplate
     * @param key key
     * @param val 成员
     * @return
     */
    public static boolean lpush(RedisTemplate redisTemplate, String key, String val) {
        Long l = redisTemplate.opsForList().leftPush(key, val);
        // TODO
        return true;
    }

    /**
     * 往list里批量加入参数
     *
     * @param redisTemplate RedisTemplate
     * @param key key
     * @param list
     * @return
     */
    public static Long rightPushAll(RedisTemplate redisTemplate, String key, List list) {
        Long num = redisTemplate.opsForList().rightPushAll(key, list);
        logger.info("redis rightPushAll list size={}, num={}", list.size(), num);
        return num;
    }


    /**
     * 设置过期时间
     *
     * @param key key
     * @param time 时间
     * @param unit 时间的单位
     * @return true成功 false失败
     */
    public static boolean expire(RedisTemplate redisTemplate, String key, Long time, TimeUnit unit) {
        if (null == unit) unit = TimeUnit.SECONDS;
        Boolean expire = redisTemplate.expire(key, time, unit);
        if (null == expire) expire = false;
        return expire;
    }

    /**
     * 查询key的过期时间
     *
     * @param redisTemplate RedisTemplate
     * @param key key
     * @param unit          时间单位：默认是秒
     * @return 过期时间
     */
    public static Long ttl(RedisTemplate redisTemplate, String key, TimeUnit unit) {
        if (unit == null) unit = TimeUnit.SECONDS;
        Long outTime = redisTemplate.getExpire(key, unit);
        if (null == outTime) outTime = 0L;
        return outTime;
    }

    /**
     * 删除key对应的键值对
     *
     * @param redisTemplate RedisTemplate
     * @param key key
     * @return true成功 false失败
     */
    public static boolean delete(RedisTemplate redisTemplate, String key) {
        Boolean delete = redisTemplate.delete(key);
        if (null == delete) delete = false;
        return delete;
    }

    /**
     * 是否存在这个成员
     *
     * @param key key
     * @param member 成员
     * @return true存在 false不存在
     */
    public static boolean isSetMember(RedisTemplate redisTemplate, String key, String member) {
        Boolean isMember = redisTemplate.opsForSet().isMember(key, member);
        logger.info("isSetMember: key={}, member={}", key, member);
        if (null == isMember) isMember = false;
        return isMember;
    }

    /**
     * key是否存在redis中
     *
     * @param key key
     * @return true存在 false不存在
     */
    public static boolean hashExists(RedisTemplate redisTemplate, String key) {
        Boolean exist = redisTemplate.hasKey(key);
        if (null == exist) exist = false;
        return exist;
    }

}
