package com.quhong.operation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;

public class MathUtils {

    private static final Logger logger = LoggerFactory.getLogger(MathUtils.class);

    public static BigDecimal Multiply(int num,double dou){
        BigDecimal numDecimal = new BigDecimal(Double.toString(num));
        BigDecimal douDecimal = new BigDecimal(Double.toString(dou));
        return numDecimal.multiply(douDecimal);
    }

    public static double round(double data){
        return round(data,null);
    }

    public static double round(double data,Integer scale){
        if(scale == null){
            scale = 2;
        }
        BigDecimal decimal = new BigDecimal(data);
        double db = decimal.setScale(scale,BigDecimal.ROUND_HALF_UP).doubleValue();
        return db;
    }

    public static double round(BigDecimal data){
        return round(data,null);
    }

    public static double round(BigDecimal data,Integer scale){
        if(scale == null){
            scale = 2;
        }
        double num = data.setScale(scale,BigDecimal.ROUND_HALF_UP).doubleValue();
        return num;
    }

    /**
     * 计算百分比
     * @param divisor 除数
     * @param dividend 被除数
     * @return
     */
    public static String getRate(int divisor,int dividend){
        if(dividend == 0){
            return "0%";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        String s = df.format((float)divisor/dividend * 100);
        return s + "%";
    }

    public static String getTwoRate(Integer divisor, Integer dividend) {
        if (dividend == null || divisor == null )
            return null;
        if (divisor == 0 || dividend == 0) {
            return "0%";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(2);
        String result = numberFormat.format((float) divisor / (float) dividend * 100);
        return result + "%";
    }

}
