package com.quhong.operation.utils;

import com.quhong.operation.share.el.MoneyDetailES;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/19
 */
public class FeeMergeUtil {

    private final static Logger logger = LoggerFactory.getLogger(FeeMergeUtil.class);
    /**
     * 合并费用同类项
     *
     * @param moneyDetailList
     * @return
     */
    public static Map<String, Integer> mergeFee(List<MoneyDetailES> moneyDetailList, Map<String, Integer> map) {
        if (CollectionUtils.isEmpty(moneyDetailList)) {
            return map;
        }
        for (MoneyDetailES moneyDetail : moneyDetailList) {
            Integer aType = moneyDetail.getAtype();
            String type = aType.toString();
            String title = moneyDetail.getTitle();
            Integer changed = moneyDetail.getChanged();
            // 将相同的atype的费用合计起来
            if (map.containsKey(type))
                map.put(type, map.get(type) + changed);
            else
                map.put(type, changed);

            // 统计admin charge 和 admin non honor charge的合计
            if (0 < changed && (1 == aType || 5 == aType || 700 == aType)) {
                if (1 == aType && "admin charge".equals(title))
                    title = "admin honor charge";
                else if (5 == aType && "admin charge".equals(title))
                    title = "admin non honor charge";

                if (map.containsKey(title))
                    map.put(title, map.get(title) + changed);
                else
                    map.put(title, changed);
            }
        }
        return map;
    }
}
