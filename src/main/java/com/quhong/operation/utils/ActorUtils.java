package com.quhong.operation.utils;

import com.quhong.data.ActorData;
import org.bson.types.ObjectId;

import java.util.Date;

public class ActorUtils {

    /**
     * 判断当前用户是否为新用户
     * @param actorData 用户对象
     * @param startTime 某一天的开始时间戳 毫秒
     * @return 新用户结果
     */
    public static boolean isNewRegisterActor(ActorData actorData, long startTime){
        long endTime = DateHelper.ARABIAN.dateAddDay(new Date(startTime), 1).getTime();
        long registerTime = new ObjectId(actorData.getUid()).getTimestamp() * 1000L;
        return (registerTime >= startTime && registerTime < endTime);
    }

    /**
     * 判断档期那用户是否为新用户
     * @param userId  用户id
     * @param startTime 某一天的开始时间戳 毫秒
     * @return
     */
    public static boolean isNewRegisterActor(String userId,long startTime){
        long endTime = DateHelper.ARABIAN.dateAddDay(new Date(startTime), 1).getTime();
        long registerTime = new ObjectId(userId).getTimestamp() * 1000L;
        return (registerTime >= startTime && registerTime < endTime);
    }

}
