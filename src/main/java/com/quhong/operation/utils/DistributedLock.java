package com.quhong.operation.utils;

import com.quhong.core.concurrency.lock.scripts.CADRedisScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/7/20
 */
public class DistributedLock {

    private static final Logger logger = LoggerFactory.getLogger(DistributedLock.class);

    /** 获取锁超时时间 单位：毫秒*/
    private static final int DEFAULT_OUT_TIME = 2000;
    /** 获取锁间隔时间 单位：毫秒 */
    private static final int DEFAULT_INTERVAL_TIME = 400;
    /** 分布式锁key前缀 */
    private static final String KEY_PREFIX = "DistributedLock:";
    private static final int EXIST_TIME = 30000;

    /** cad比较并删除 **/
    private static CADRedisScript cadRedisScript = new CADRedisScript();

    /**
     * 尝试获取锁
     * @param redisTemp redis
     * @param key 锁的key
     * @return value
     */
    public static String tryLock (RedisTemplate redisTemp, String key) {
        return tryLock(redisTemp, key, EXIST_TIME, DEFAULT_OUT_TIME);
    }

    /**
     * 尝试获取锁
     * @param redisTemp redis
     * @param key 锁的key
     * @param existTime 锁的默认时间
     * @param outTime 获取锁最长等待时间
     * @return value
     */
    public static String tryLock (RedisTemplate redisTemp, String key, int existTime, int outTime) {
        return tryLock(redisTemp, key, existTime, outTime, DEFAULT_INTERVAL_TIME);
    }

    /**
     * 尝试获取锁
     * @param redisTemp redis
     * @param key 锁的key
     * @param existTime 锁的默认时间
     * @param outTime 获取锁最长等待时间
     * @param intervalTime 获取锁的间隔时间
     * @return value
     */
    public static String tryLock (RedisTemplate redisTemp, String key, int existTime, int outTime, int intervalTime) {
        key = KEY_PREFIX + key;
        String v = StringUtil.getUUID();
        long startTime = System.currentTimeMillis();
        while (true) {
            Boolean flag = redisTemp.opsForValue().setIfAbsent(key, v, existTime, TimeUnit.MILLISECONDS);
            // 设置成功则获取锁成功
            if (null != flag && flag) {
                logger.info("key={} get lock succeed value={}", key, v);
                return v;
            }
            // 如果获取时间超过了设置的时间则结束循环
            if (startTime + outTime < System.currentTimeMillis()) {
                break;
            } else {
                try {
                    Thread.sleep(intervalTime);
                } catch (InterruptedException e) {
                    logger.error("method tryLock sleep error {}", e.getMessage(), e);
                }
            }
        }
        logger.error("method tryLock try lock key={}, error", key);
        return "";
    }

    /**
     * 释放锁
     * @param redisTemp redis
     * @param key 锁的key
     * @return 是否释放成功
     */
    public static boolean unLock (RedisTemplate redisTemp, String key, String value) {
        key = KEY_PREFIX + key;
        redisTemp.execute(cadRedisScript, Arrays.asList(key), value);
        return false;
    }

}
