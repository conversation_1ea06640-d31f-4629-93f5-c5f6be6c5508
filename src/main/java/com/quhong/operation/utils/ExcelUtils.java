package com.quhong.operation.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
public class ExcelUtils {

    private static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    /**
     * 导出Excel文件
     *
     * @param response  response
     * @param data      导出数据
     * @param head      Excel表头，支持Class及String数组
     * @param fileName  导出文件名
     * @param sheetName sheetName
     */
    @SuppressWarnings("rawtypes")
    public static void exportExcel(HttpServletResponse response, List data, Object head, String fileName, String sheetName) {
        try {
            encodeFilename(response, fileName);
            if (head instanceof Class) {
                EasyExcel.write(response.getOutputStream(), (Class) head)
                        .registerWriteHandler(simpleStyle()).sheet(sheetName).doWrite(data);
            } else if (head instanceof String[]) {
                EasyExcel.write(response.getOutputStream())
                        .registerWriteHandler(simpleStyle()).sheet(sheetName)
                        .head(buildHead((String[]) head)).doWrite(data);
            } else {
                logger.error("not support head type");
            }
        } catch (IOException e) {
            logger.error("export excel error, error message={}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 下载文件到本地
     * @param data
     * @param head
     * @param fileName
     * @param sheetName
     */
    public static void exportExcel(List data, Class head, String fileName, String sheetName) {
        ExcelWriter excelWriter = EasyExcel.write(fileName, head).build();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        excelWriter.write(data, writeSheet);
        excelWriter.finish();
    }

    public static void encodeFilename(HttpServletResponse response, String excelExportName) {
        String fileName;
        try {
            fileName = URLEncoder.encode(excelExportName + ".xlsx", "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            logger.error("encodeFilename method error param excelExportName={}", excelExportName);
            fileName = "export.xlsx";
        }
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        response.setHeader("Access-Control-Expose-Headers", "*");
    }

    /**
     * 使用EasyExcel按照表头读取，表头不存在时数据不读取
     *
     * @return key=表头，value=表头所在列的列表
     */
    private static Map<String, List<String>> convertData(List<Map<Integer, String>> listMap) {
        Map<String, List<String>> result = new LinkedHashMap<>(listMap.size());
        Map<Integer, String> headIndexMap = new HashMap<>();
        for (int i = 0; i < listMap.size(); i++) {
            for (Map.Entry<Integer, String> entry : listMap.get(i).entrySet()) {
                if (i == 0) {
                    if (null != entry.getValue()) {
                        result.put(entry.getValue(), new ArrayList<>());
                        headIndexMap.put(entry.getKey(), entry.getValue());
                    }
                } else {
                    result.computeIfPresent(headIndexMap.get(entry.getKey()), (k, v) -> {
                        if (null != entry.getValue()) {
                            v.add(entry.getValue());
                        }
                        return v;
                    });
                }
            }
        }
        return result;
    }

    /**
     * 基础样式
     */
    public static HorizontalCellStyleStrategy simpleStyle() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为白色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 11);
        headWriteFont.setBold(false);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 去除表头边框
        headWriteCellStyle.setBorderTop(BorderStyle.DASHED);
        headWriteCellStyle.setBorderBottom(BorderStyle.DASHED);
        headWriteCellStyle.setBorderLeft(BorderStyle.DASHED);
        headWriteCellStyle.setBorderRight(BorderStyle.DASHED);
        // 垂直居中
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.GENERAL);
        // 取消自动换行
        headWriteCellStyle.setWrapped(false);
        // 内容的策略
        WriteCellStyle writeCellStyle = new WriteCellStyle();
        writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return new HorizontalCellStyleStrategy(headWriteCellStyle, writeCellStyle);
    }

    public static Map<String, List<String>> readExcel(InputStream inputStream) {
        return convertData(EasyExcel.read(inputStream).sheet().headRowNumber(0).doReadSync());
    }

    public static Map<String, List<String>> readExcel(String filePath) {
        return convertData(EasyExcel.read(filePath).sheet().headRowNumber(0).doReadSync());
    }

    public static Map<String, List<String>> readExcel(String filePath, String sheetName) {
        return convertData(EasyExcel.read(filePath).sheet(sheetName).headRowNumber(0).doReadSync());
    }

    public static Map<String, List<String>> readExcel(InputStream inputStream, String sheetName) {
        return convertData(EasyExcel.read(inputStream).sheet(sheetName).headRowNumber(0).doReadSync());
    }

    public static List<List<String>> buildHead(String[] headList) {
        List<List<String>> list = new ArrayList<>();
        for (String head : headList) {
            list.add(Collections.singletonList(head));
        }
        return list;
    }

}
