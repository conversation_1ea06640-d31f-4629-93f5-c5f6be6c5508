package com.quhong.operation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/6/12
 */
public class FileUtils {

    private static final Logger logger = LoggerFactory.getLogger(FileUtils.class);

    public static void fileOutPutStreamContent(String filePath, String content) {
        File file;
        FileOutputStream fos;
        try {
            file = new File(filePath);
            if(!file.exists()) {
                boolean newFile = file.createNewFile();
                if (!newFile) {
                    logger.info("fail to create new file, please check!");
                    return;
                }
            }
            byte[] bytes = content.getBytes();
            int b = bytes.length;   //是字节的长度，不是字符串的长度
            fos = new FileOutputStream(file); // 如果已存在，以覆盖的方式写文件
            // fos = new FileOutputStream(txt, true); // 如果已存在，以追加的方式写文件
            fos.write(bytes,0, b); // 写指定长度的内容
            // fos.write(bytes); // 写全文
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
