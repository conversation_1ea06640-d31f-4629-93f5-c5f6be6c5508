package com.quhong.operation.utils;

import com.quhong.operation.constant.AppPackageConstant;

public class AppUtils {

    /**
     * 1:in.dradhanus.liveher
     * 2:com.youstar.android.lite
     * 3:com.stonemobile.youstar
     */
    public static String getAppPackage(Integer app) {
        if (AppPackageConstant.YOUSTAR_ANDROID == app) {
            return "in.dradhanus.liveher";
        } else if (AppPackageConstant.YOUSTAR_PRO == app) {
            return "com.youstar.android.lite";
        } else if (AppPackageConstant.YOUSTAR_IOS == app) {
            return "com.stonemobile.youstar";
        } else if (AppPackageConstant.YOUSTAR == app) {
            return "in.dradhanus.liveher,com.stonemobile.youstar";
        }
        return null;
    }
}
