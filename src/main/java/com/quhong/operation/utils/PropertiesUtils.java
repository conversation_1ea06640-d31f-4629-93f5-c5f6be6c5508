package com.quhong.operation.utils;

import com.quhong.operation.constant.AppPackageConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/24
 */
public class PropertiesUtils {

    private final static Logger logger = LoggerFactory.getLogger(PropertiesUtils.class);

    public static Map<String, Object> map = new HashMap<>();

    private static final String DEFAULT_PROPERTIES = "app_config/sys.properties";

    /** 默认编码 */
    private static final String DEFAULT_CHARSET = "UTF-8";

    /**
     * 读取文件　将内容保存到map
     * <AUTHOR>
     * @date 2020/8/24
     */
    private static synchronized void readProperties() {
        if (!map.isEmpty()) return;

        InputStream is = PropertiesUtils.class.getClassLoader().getResourceAsStream(DEFAULT_PROPERTIES);
        if (is == null) {
            logger.error("cannot find the file:" + DEFAULT_PROPERTIES);
        } else {
            try {
                Properties properties = new Properties();
                properties.load(new InputStreamReader(is, DEFAULT_CHARSET));
                Enumeration<?> e = properties.propertyNames();
                while (e.hasMoreElements()) {
                    String p_key = (String) e.nextElement();
                    map.put(p_key, properties.get(p_key));
                }
            } catch (Exception e) {
                logger.error("{}", e.getMessage(), e);
            } finally {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.error("{}", e.getMessage(), e);
                }
            }
        }

    }

    /**
     * 读取文件内容
     * @param fileName 文件名
     * @return Properties对象
     * <AUTHOR>
     * @since   2016年1月6日
     */
    public static Properties loadPropertyInstance(String fileName) {
        InputStream is = null;
        try {
            Properties p = new Properties();
            is = PropertiesUtils.class.getClassLoader().getResourceAsStream(fileName);
            p.load(is);
            return p;
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
            return null;
        } finally {
            try {
                if (is !=null) is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 通过key获取value
     * @param key key
     * @return value
     */
    public static String getProperty(String key) {
        if (map.isEmpty()) readProperties();
        return map.get(key).toString();
    }

    /**
     * 1:in.dradhanus.liveher
     * 2:com.youstar.android.lite
     * 3:com.stonemobile.youstar
     */
    public static String getAppPackage(Integer app) {
        if (AppPackageConstant.YOUSTAR_ANDROID == app) {
            return "in.dradhanus.liveher";
        } else if (AppPackageConstant.YOUSTAR_PRO == app) {
            return "com.youstar.android.lite";
        } else if (AppPackageConstant.YOUSTAR_IOS == app) {
            return "com.stonemobile.youstar";
        } else if (AppPackageConstant.YOUSTAR == app) {
            return "in.dradhanus.liveher,com.stonemobile.youstar";
        }
        return null;
    }

    public static List<Integer> parseVersionCode(String versioncode) {
        List<Integer> versionCodeList = null;
        if (!StringUtils.isEmpty(versioncode)) {
            versionCodeList = new ArrayList<>();
            String[] split = versioncode.split(",");
            for (String version : split) {
                versionCodeList.add(Integer.parseInt(version));
            }
        }
        return versionCodeList;
    }

}
