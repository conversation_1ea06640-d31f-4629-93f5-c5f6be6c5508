package com.quhong.operation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2020/8/31
 */
public class Md5Utils {

    private static final Logger logger = LoggerFactory.getLogger(Md5Utils.class);

    // 默认的密码字符串组合，用来将字节转换成 16 进制表示的字符,apache校验下载的文件的正确性用的就是默认的这个组合
    private final static char[] HEX_DIGITS = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };

    private static MessageDigest messagedigest = null;
    static {
        try {
            messagedigest = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            logger.error("初始化失败，MessageDigest不支持MD5Util。 {}", e.getMessage(), e);
        }
    }

    /**
     * 生成字符串的md5校验值
     * @param s 字符串
     * @return MD5
     */
    public static String getMD5String(String s) {
        return getMD5String(s.getBytes());
    }

    /**
     * 生成文件的md5校验值
     * @param file 文件
     * @return md5
     */
    public static String getFileMD5String(File file) {
        if (null == file) return null;
        InputStream fis = null;
        try {
            fis = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int numRead;
            while ((numRead = fis.read(buffer)) > 0) {
                messagedigest.update(buffer, 0, numRead);
            }
        } catch (IOException e) {
            logger.error("{}", e.getMessage(), e);
        } finally {
            try {
                if (null != fis) fis.close();
            } catch (IOException e) {
                logger.error("{}", e.getMessage(), e);
            }
        }

        return bufferToHex(messagedigest.digest());
    }

    public static String getISMD5String (InputStream fis) {
        try {
            byte[] buffer = new byte[1024];
            int numRead;
            while ((numRead = fis.read(buffer)) > 0) {
                messagedigest.update(buffer, 0, numRead);
            }
        } catch (IOException e) {
            logger.error("{}", e.getMessage(), e);
        } finally {
            try {
                if (null != fis) fis.close();
            } catch (IOException e) {
                logger.error("{}", e.getMessage(), e);
            }
        }

        return bufferToHex(messagedigest.digest());
    }

    /**
     * 判断字符串的md5校验码是否与一个已知的md5码相匹配
     * @param password 要校验的字符串
     * @param md5PwdStr 已知的md5校验码
     * @return 匹配返回true
     */
    public static boolean checkPassword(String password, String md5PwdStr) {
        String s = getMD5String(password);
        return s.equals(md5PwdStr);
    }

    /**
     * 将byte 数组转为md5
     * @param bytes 数组
     * @return md5
     */
    private static String getMD5String(byte[] bytes) {
        messagedigest.update(bytes);
        return bufferToHex(messagedigest.digest());
    }

    /**
     * 转十六进制字符串
     * @param bytes bytes数组
     * @return 十六进制字符串
     */
    private static String bufferToHex(byte[] bytes) {
        return bufferToHex(bytes, 0, bytes.length);
    }

    /**
     * 转十六进制字符串
     * @param bytes bytes数组
     * @param m 开始下标位置
     * @param n 结束下标位置
     * @return 十六进制字符串
     */
    private static String bufferToHex(byte[] bytes, int m, int n) {
        StringBuffer stringbuffer = new StringBuffer(2 * n);
        int k = m + n;
        for (int l = m; l < k; l++) {
            appendHexPair(bytes[l], stringbuffer);
        }
        return stringbuffer.toString();
    }

    private static void appendHexPair(byte bt, StringBuffer stringbuffer) {
        char c0 = HEX_DIGITS[(bt & 0xf0) >> 4];// 取字节中高 4 位的数字转换, >>>
        // 为逻辑右移，将符号位一起右移,此处未发现两种符号有何不同
        char c1 = HEX_DIGITS[bt & 0xf];// 取字节中低 4 位的数字转换
        stringbuffer.append(c0);
        stringbuffer.append(c1);
    }

    public static void main(String[] args) {
        long begin = System.currentTimeMillis();
        String path = "C:\\Users\\<USER>\\Downloads\\Ah_Mnk_melody4arab.com.mp3";

        File file = new File(path);
        if (!file.exists()) logger.info("文件 {} 不存在", path);

        String md5 = getFileMD5String(file);

//      String md5 = getMD5String("a");

        long end = System.currentTimeMillis();
        logger.info("md5: {} time: {}ms", md5, end - begin);
        logger.info(file.getPath());
    }

}
