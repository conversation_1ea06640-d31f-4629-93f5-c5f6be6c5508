package com.quhong.operation.utils;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

/**
 * <AUTHOR>
 * @date 2020/7/29
 */
public class MongoUtils {

    /**
     * 获取通过_id创建时间范围获取数据的query
     * @param startTime 开始时间
     * @param endTime 结尾时间
     * @return query
     */
    public static Query setQueryBy_idRange (Integer startTime, Integer endTime) {
        if (null == startTime || null == endTime) return null;

        String start = create_idBySecond(startTime);
        String end = create_idBySecond(endTime);
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").gte(new ObjectId(start)).lt(new ObjectId(end)));
        return query;
    }

    /**
     * 获取通过_id创建时间范围获取数据的 Criteria
     * @param startTime 开始时间
     * @param endTime 结尾时间
     * @return Criteria
     */
    public static Criteria setCriteriaBy_idRange (Integer startTime, Integer endTime) {
        if (null == startTime || null == endTime) return null;

        String start = create_idBySecond(startTime);
        String end = create_idBySecond(endTime);
        Criteria criteria = Criteria.where("_id").gte(new ObjectId(start)).lt(new ObjectId(end));
        return criteria;
    }

    public static String create_idBySecond (Integer time) {
        return Long.toHexString(time) + "0000000000000000";
    }

}
