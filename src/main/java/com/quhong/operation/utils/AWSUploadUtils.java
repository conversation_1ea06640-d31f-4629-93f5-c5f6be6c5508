package com.quhong.operation.utils;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.quhong.core.utils.DateHelper;
import com.quhong.exception.CommonH5Exception;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public class AWSUploadUtils {

    private static final Logger logger = LoggerFactory.getLogger(AWSUploadUtils.class);

    private static final AmazonS3 amazonS3;
    private static final String REGION = "ap-southeast-1";
    private static final String BUCKET_NAME = "qhcf";
    private static final String ACTIVITY_PATH = "activity/";
    private static final String REPLACE_STRING ="[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】|\\ ]";

    static {
        System.setProperty("aws.accessKeyId", "********************");
        System.setProperty("aws.secretKey", "LFhEf1fBkTGHsGKqTpmS0gswEKIsJk94skS0jn7W");
        amazonS3 = AmazonS3ClientBuilder.standard().withRegion(Regions.fromName(REGION)).build();
    }


    public static String upload(MultipartFile file) {
        return upload(file, ACTIVITY_PATH);
    }

    public static String upload(MultipartFile file,String path) {
        // 设置存储路径
        try {
            String originFileName = file.getOriginalFilename().replaceAll(REPLACE_STRING, "");
            String s3FilePath = path + "op_" + DateHelper.getNowSeconds() + "_" + originFileName;
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());
            metadata.setContentLength(file.getSize());
            metadata.setHeader("x-amz-acl", "public-read");
            // Upload Beginning
            amazonS3.putObject(BUCKET_NAME, s3FilePath, file.getInputStream(), metadata);
            // 获取一个requests
            GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(BUCKET_NAME, s3FilePath);
            // 生成公用的url
            String url = amazonS3.generatePresignedUrl(urlRequest).toString();
            if (url.contains("?")) {
                url = url.substring(0, url.indexOf("?"));
            }
            return createCDNUrl(url);
        } catch (Exception e) {
            logger.error("uploading failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }

    private static String createCDNUrl(String url) {
        if (ObjectUtils.isEmpty(url)) {
            return url;
        }
        url = url.replaceAll("https://qhcf.s3.ap-southeast-1.amazonaws.com", "https://cdn3.qmovies.tv");
        return url;
    }


    public static String uploadFromPath(String localPath, String bucketPath) {
        FileInputStream inputStream = null;

        try {

            File zipFile = new File(localPath);
            // 设置存储路径
            String s3FilePath = bucketPath + zipFile.getName();
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setHeader("x-amz-acl", "public-read");
            metadata.setContentLength(zipFile.length());
            inputStream = new FileInputStream(zipFile);

            amazonS3.putObject(BUCKET_NAME, s3FilePath, inputStream, metadata);
            GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(BUCKET_NAME, s3FilePath);   // 获取一个requests
            String url = amazonS3.generatePresignedUrl(urlRequest).toString();  // 生成公用的url

            if (url.contains("?")) {
                url = url.substring(0, url.indexOf("?"));
            }

            inputStream.close();
            return createCDNUrl(url);
        } catch (IOException e) {
            logger.error("uploading failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }
}
