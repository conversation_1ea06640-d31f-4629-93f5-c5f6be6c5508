package com.quhong.operation.utils;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.TimeZone;

public class ThreadLocalCalendar extends ThreadLocal<Calendar> {
    private TimeZone timeZone;

    public ThreadLocalCalendar(TimeZone timeZone) {
        this.timeZone = timeZone;
    }

    @Override
    protected Calendar initialValue() {
        Calendar calendar = new GregorianCalendar();
        if (this.timeZone != null) {
            calendar.setTimeZone(timeZone);
        }
        return calendar;
    }
}
