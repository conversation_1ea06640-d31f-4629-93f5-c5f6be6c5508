package com.quhong.operation.utils;

import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {

    public final static String ENCODING_FORMAT = "UTF-8";
    public static final String FOLDER_SEPARATOR = "/";
    private static final Pattern PATTERN = Pattern.compile("[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】]");

    /**
     * 判断字符串是否是空的
     *
     * @param str
     * @return
     */
    public static Boolean isEmpty(String str) {
        return null == str || "".equals(str);
    }

    /**
     * 判断字符串是否是空的 或者是只有空格的字符串
     *
     * @param str
     * @return
     */
    public static Boolean isEmptyOrBlank(String str) {
        if (null == str)
            return true;
        if ("".equals(str.trim()))
            return true;
        return false;
    }

    /**
     * 判断字符串是否不是空的
     *
     * @param str
     * @return
     */
    public static Boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean isNotEmptyOrBlank(String str) {
        return !isEmptyOrBlank(str);
    }

    /**
     * 通过房间rid获取到用户uid
     * @param roomId room的rid
     * @return uid
     */
    public static String getUidByRoomId (String roomId) {
        if (roomId.startsWith("r:")) {
            return roomId.substring(2);
        }
        return "";
    }

    /**
     * 通过uid生成roomId OR 通过uid获取roomId
     * @param uid
     * @return
     */
    public static String getRoomIdByUid (String uid) {
        return "r:" + uid;
    }
    /**
     * 去掉前后连续相同的字符串
     *
     * @param str     原字符串
     * @param element 前后需要去掉的字符串，如果连续相同则都去掉。
     * @return
     */
    public static String trimFirstAndLastStr(String str, String element) {
        if (str.startsWith(element) || str.endsWith(element)) {
            boolean beginIndexFlag = false;
            boolean endIndexFlag = false;
            int beginIndex = 0;
            int endIndex = str.length() + 1;
            do {
                beginIndex = str.indexOf(element) == 0 ? 1 : 0;
                endIndex = str.lastIndexOf(element) + 1 == str.length() ? str.lastIndexOf(element) : str.length();
                str = str.substring(beginIndex, endIndex);
                beginIndexFlag = (str.indexOf(element) == 0);
                endIndexFlag = (str.lastIndexOf(element) + 1 == str.length());
            } while (beginIndexFlag || endIndexFlag);

        }
        return str;
    }

    /**
     * 获取UUID -转空格的
     *
     * @return
     */
    public static String getUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "").toLowerCase();
    }

    public static String replaceBlank(String str) {
        String content = null;
        if (str != null) {
            content = str.replace("\\n", "").replace("\\t", "").replace("\\r", "").replace("|", "").replace("\u0001", "");
        }

        return content;
    }

    public static String replace(String inString, String oldPattern, String newPattern) {
        if (isNotEmptyOrBlank(inString) && isNotEmptyOrBlank(oldPattern) && null != newPattern) {
            int index = inString.indexOf(oldPattern);
            if (index == -1) {
                return inString;
            } else {
                int capacity = inString.length();
                if (newPattern.length() > oldPattern.length()) {
                    capacity += 16;
                }

                StringBuilder sb = new StringBuilder(capacity);
                int pos = 0;

                for (int patLen = oldPattern.length(); index >= 0; index = inString.indexOf(oldPattern, pos)) {
                    sb.append(inString, pos, index);
                    sb.append(newPattern);
                    pos = index + patLen;
                }

                sb.append(inString, pos, inString.length());
                return sb.toString();
            }
        } else {
            return inString;
        }
    }

    /**
     * 字符串是否包含中文
     *
     * @param str 待校验字符串
     * @return true 包含中文字符  false 不包含中文字符
     */
    public static boolean isContainChinese(String str){
        Matcher m = PATTERN.matcher(str);
        return m.find();
    }


}

