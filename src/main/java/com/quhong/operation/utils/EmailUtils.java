package com.quhong.operation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.mail.Address;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/17
 */
public class EmailUtils {

    private static final Logger logger = LoggerFactory.getLogger(EmailUtils.class);

    private final static String MAIL_TRANSPORT_PROTOCOL = "smtp";
    private final static String MAIL_SMTP_AUTH = "true";
    private final static String MAIL_SMTP_PORT = "465"; //587
    private final static String MAIL_SMTP_SOCKETFACTORY_CLASS = "javax.net.ssl.SSLSocketFactory";
    private final static String MAIL_SMTP_SOCKETFACTORY_FALLBACK = "false";
    private final static String CHARSET = "UTF-8";

//    private static final String sendAccount = "<EMAIL>";
//    private static final String accountPwd = "hiqrrfkzridkcadb"; //vzehupdflwtlcabe
//    private static final String emailSMTPHost = "smtp.qq.com";

    private static final String sendAccount = "<EMAIL>";
    private static final String accountPwd = "youstar814_2017"; //vzehupdflwtlcabe
    private static final String emailSMTPHost = "smtpout.secureserver.net";

    /**
     * 发送邮件
     *
     * @param pre 发送邮件类型
     * @return 发送是否成功
     */
    public static boolean sendMail(String pre, String content) {
        Transport transport = null;
        try {
            Session session = getSession(getEmailSMTPHost());
            // 查看详细log session.setDebug(true);

            // 收件人
            String receiveAccount = PropertiesUtils.getProperty(pre + ".receive.mail.account");
            // 主题
            String subject = PropertiesUtils.getProperty(pre + ".email.subject");
            // 创建一封邮件
            MimeMessage message = createMimeMessage(session, getSendAccount(), subject, receiveAccount, content);

            // 根据 Session 获取邮件传输对象
            transport = session.getTransport();

            // 通过 账号 密码 连接邮件服务器
            transport.connect(getSendAccount(), getAccountPwd());
            // 发送邮件, 发到所有的收件地址
            transport.sendMessage(message, message.getAllRecipients());
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
            return false;
        } finally {
            try {
                if (null != transport) transport.close();
            } catch (MessagingException e) {
                logger.error("close transport fail info : {}", e.getMessage(), e);
            }
        }
        return true;
    }

    /**
     * 发送邮件
     *
     * @param subject           邮件主题
     * @param content           邮件内容
     * @param receiveAccountMap 接受账号 key：邮箱地址 value：邮箱用户名
     * @return 发送是否成功
     */
    public static boolean sendMail(String subject, String content, Map<String, String> receiveAccountMap) {
        Transport transport = null;
        try {
            Session session = getSession(getEmailSMTPHost());
            // 创建一封邮件
            MimeMessage message = createMimeMessage(session, getSendAccount(), subject, receiveAccountMap, content);
            // 根据 Session 获取邮件传输对象
            transport = session.getTransport();
            // 通过 账号 密码 连接邮件服务器
            transport.connect(getSendAccount(), getAccountPwd());
            // 发送邮件, 发到所有的收件地址
            transport.sendMessage(message, message.getAllRecipients());
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
            return false;
        } finally {
            try {
                if (null != transport) transport.close();
            } catch (MessagingException e) {
                logger.error("close transport fail info : {}", e.getMessage(), e);
            }
        }
        return true;
    }

    /**
     * 获取邮件 Session
     *
     * @param emailSMTPHost 发送者的邮件服务器地址
     * @return Session
     */
    private static Session getSession(String emailSMTPHost) {
        // 创建参数配置, 用于连接邮件服务器的参数配置
        Properties props = new Properties();                    // 参数配置
        props.setProperty("mail.transport.protocol", MAIL_TRANSPORT_PROTOCOL); // 使用的协议（JavaMail规范要求）
        props.setProperty("mail.smtp.auth", MAIL_SMTP_AUTH); // 需要请求认证
        props.setProperty("mail.smtp.host", emailSMTPHost); // 发件人的邮箱的 SMTP 服务器地址

        props.setProperty("mail.smtp.ssl.enable", MAIL_SMTP_AUTH); // ssl传输
//        props.setProperty("mail.smtp.ssl.protocols", "TLSv1.2");
//        props.setProperty("mail.smtp.starttls.enable", MAIL_SMTP_AUTH); // 指定启用TLS加密

        // SMTP 服务器的端口
        props.setProperty("mail.smtp.port", MAIL_SMTP_PORT);
        props.setProperty("mail.smtp.socketFactory.class", MAIL_SMTP_SOCKETFACTORY_CLASS);
        props.setProperty("mail.smtp.socketFactory.fallback", MAIL_SMTP_SOCKETFACTORY_FALLBACK);
        props.setProperty("mail.smtp.socketFactory.port", MAIL_SMTP_PORT);

        // 根据配置创建会话对象, 用于和邮件服务器交互
        return Session.getDefaultInstance(props);
    }

    /**
     * 创建一封只包含文本的简单邮件
     *
     * @param session      和服务器交互的会话
     * @param sendMail     发件人邮箱
     * @param receiveMails 收件人邮箱
     * @return 邮件信息
     */
    private static MimeMessage createMimeMessage(Session session, String sendMail,
                                                 String subject, String receiveMails, String content) {
        // 创建一封邮件
        MimeMessage message = new MimeMessage(session);
        try {
            // 发件人
            message.setFrom(new InternetAddress(sendMail));

            // 收件人
            String[] receiveMail = receiveMails.split(";");
            Address[] addrArr = new Address[receiveMail.length];
            for (int i = 0; i < receiveMail.length; i++) {
                String[] split = receiveMail[i].split(":");
                InternetAddress addr = new InternetAddress(split[0], split[1], CHARSET);
                addrArr[i] = addr;
            }
            // MimeMessage.RecipientType.CC是抄送
            message.setRecipients(MimeMessage.RecipientType.TO, addrArr);

            message.setSubject(subject, CHARSET); // 邮箱主题

            // 邮件正文（可以使用html标签）
            message.setContent(content, "text/html;charset=UTF-8");

            message.setSentDate(new Date());
            // 保存设置
            message.saveChanges();
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
        return message;
    }

    /**
     * 创建一封只包含文本的简单邮件
     *
     * @param session      和服务器交互的会话
     * @param sendMail     发件人邮箱
     * @param receiveMails 收件人邮箱
     * @return 邮件信息
     */
    private static MimeMessage createMimeMessage(Session session, String sendMail,
                                                 String subject, Map<String, String> receiveMails, String content) {
        // 创建一封邮件
        MimeMessage message = new MimeMessage(session);
        try {
            // 发件人
            message.setFrom(new InternetAddress(sendMail));

            // 收件人
            Address[] addrArr = new Address[receiveMails.size()];
            int i = 0;
            for (Map.Entry<String, String> receiveMail : receiveMails.entrySet()) {
                InternetAddress addr = new InternetAddress(receiveMail.getKey(), receiveMail.getValue(), CHARSET);
                addrArr[i] = addr;
                i++;
            }
            // MimeMessage.RecipientType.CC是抄送
            message.setRecipients(MimeMessage.RecipientType.TO, addrArr);

            message.setSubject(subject, CHARSET); // 邮箱主题

            // 邮件正文（可以使用html标签）
            message.setContent(content, "text/html;charset=UTF-8");

            message.setSentDate(new Date());
            // 保存设置
            message.saveChanges();
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
        return message;
    }

    /**
     * 获取发送者账号
     *
     * @return 发送者账号
     */
    private static String getSendAccount() {
        return sendAccount;
    }

    /**
     * 获取发送者授权码
     *
     * @return 授权码
     */
    private static String getAccountPwd() {
        return accountPwd;
    }

    /**
     * 获取邮件服务器的地址
     *
     * @return 邮件服务器地址
     */
    private static String getEmailSMTPHost() {
        return emailSMTPHost;
    }

    /**
     * 通过list list对象填充成表格
     *
     * @param subject     主题
     * @param titleList   表格标题集合
     * @param contentList 内容集合
     * @return content字符串
     */
    public static String getEmailContent(String subject, List<String> titleList, List<List<String>> contentList) {
        StringBuilder content = new StringBuilder("<html><head></head><body>");
        if (null != subject) {
            content.append("<h2>").append(subject).append("</h2>");
        }
        content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;\">");

        // 设置标题行
        content.append("<tr style=\"background-color: #428BCA; color:#ffffff\">");
        for (String t : titleList) {
            content.append("<th>").append(t).append("</th>");
        }
        content.append("</tr>");

        // 内容填充
        for (List<String> list : contentList) {
            if (CollectionUtils.isEmpty(list)) continue;
            content.append("<tr>");
            for (String data : list) {
                content.append("<td>").append(data).append("</td>");
            }
            content.append("</tr>");
        }

        // 收尾
        content.append("</table>");
//        content.append("<h3>description</h3>");
        content.append("</body></html>");
        return content.toString();
    }

}
