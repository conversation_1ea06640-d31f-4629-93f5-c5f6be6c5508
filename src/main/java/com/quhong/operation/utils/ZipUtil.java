package com.quhong.operation.utils;

import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.WebClient;
import com.quhong.exception.CommonH5Exception;
import net.lingala.zip4j.ZipFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


public class ZipUtil {
    private static final Logger logger = LoggerFactory.getLogger(ZipUtil.class);

    public static String fileBasePath = "/opt";
    // public static String fileBasePath = "C:\\Ustar\\demo\\src\\main\\java\\com\\example\\demo";
    final static Pattern pattern = Pattern.compile("\\S*[?]\\S*");

    private static String parseSuffix(String url) {

        Matcher matcher = pattern.matcher(url);
        String[] spUrl = url.split("/");
        int len = spUrl.length;
        String endUrl = spUrl[len - 1];
        if(matcher.find()) {
            String[] spEndUrl = endUrl.split("\\?");
            return spEndUrl[0].split("\\.")[1];
        }
        return endUrl.split("\\.")[1];
    }

    public static String loadFileFromUrl(List<Map<String, String>> urlList){

        WebClient webClient = new WebClient();
        String fileDir = UUID.randomUUID().toString().replace("-", "").substring(0, 6);
        String rootDir = fileBasePath + "/" + fileDir + "/";
        logger.info("urlList: {}, fileBasePath: {}", urlList, rootDir);
        OutputStream outputStream = null;
        try{
            Path path = Paths.get(rootDir);
            if(!Files.exists(path)){
                Files.createDirectory(path);
            }

            for (Map<String, String> urlParam: urlList){
                String fileUrl = urlParam.get("fileUrl");
                String fileName = urlParam.get("fileName");
                String filePath = rootDir + fileName + "." + parseSuffix(fileUrl);
                byte[] resp = webClient.getBytes(fileUrl);
                outputStream = new BufferedOutputStream(new FileOutputStream(filePath));;
                outputStream.write(resp);
                outputStream.close();
            }

            return rootDir;

        }catch (Exception e) {
            logger.error("loadFileFromUrl, msg={}" , e.getMessage(), e);
        } finally {
            try {
                outputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return "";
    }

    // 压缩zip包
    public static String packDirToZip(String filePath, String zipFileName) {
        ZipOutputStream zipOut = null ;
        OutputStream out = null;
        String zipPath = filePath + "/" + zipFileName;

        try{
            File fileDir = new File(filePath);
            File[] fileArray = fileDir.listFiles();
            assert fileArray != null;
            List<File> fileList = Arrays.asList(fileArray);

            InputStream input = null;
            File zipFile = new File(zipPath);
            out = Files.newOutputStream(zipFile.toPath());
            zipOut = new ZipOutputStream(out);

            for (File file: fileList){
                input = Files.newInputStream(file.toPath());
                zipOut.putNextEntry(new ZipEntry(file.getName()));
                int temp = 0;
                while ((temp = input.read()) != -1){
                    zipOut.write(temp);
                }
                input.close();

            }
        }catch (Exception e) {
            logger.error("packDirToZip, msg={}" , e.getMessage(), e);
        } finally {
            try {
                zipOut.close();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return zipPath;
    }

    public static String loadUrlZipUploadFile(String zipPrefix, List<Map<String, String>> urlList){
        String fileDir = loadFileFromUrl(urlList);
        String zipFileName = zipPrefix + DateHelper.getNowSeconds() + ".zip";
        logger.info("loadUrlZipUploadFile fileDir {}, zipFileName: {}", fileDir, zipFileName);
        return  packDirToZip(fileDir, zipFileName);
    }

    /**
     * File的length()方法返回的类型为long
     * @param filePath 文件路径
     * @return map
     */
    public static Map<String, Object> calculateZipFileMD5(String filePath){
        try {
            Map<String, Object> fileMeta = new HashMap<>();
            File file = new File(filePath);

            InputStream inputStream = Files.newInputStream(file.toPath());
            String fileMd5 = DigestUtils.md5DigestAsHex(inputStream);
            fileMeta.put("fileMd5", fileMd5);
            fileMeta.put("fileSize", file.length());
            inputStream.close();
            return fileMeta;
        } catch (IOException e) {
            logger.error("calculateZipFileMD5 failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }


    public static String uploadZipFile(String filePath, String bucketPath){
        return AWSUploadUtils.uploadFromPath(filePath, bucketPath);
    }



    /**
     * 压缩文件夹加密
     * @param filePath 要打包的文件夹
     * @param zipFileName 生成的压缩包的名字
     */
    public static String zipFileWithPath(String filePath, String zipFileName) {
        // 生成的压缩文件

        String zipPath = filePath + zipFileName;
        try {
            ZipFile zipFile = new ZipFile(zipPath);
            // 要打包的文件夹
            File currentFile = new File(filePath);
            File[] fs = currentFile.listFiles();
            // 遍历test文件夹下所有的文件、文件夹
            for (File f : fs) {
                if (f.isDirectory()) {
                    zipFile.addFolder(f);
                } else {
                    zipFile.addFile(f);
                }
            }
            return  zipPath;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";

    }

    /**
     * 本地文件复制
     * @param sourcePath 文件原本地址
     * @param newPath 要复制的新地址
     */
    public static void copyFile(String sourcePath, String newPath) {
        File start = new File(sourcePath);
        File end = new File(newPath);
        try(BufferedInputStream bis=new BufferedInputStream(new FileInputStream(start));
            BufferedOutputStream bos=new BufferedOutputStream(new FileOutputStream(end))) {
            int len = 0;
            byte[] flush = new byte[1024];
            while((len=bis.read(flush)) != -1) {
                bos.write(flush, 0, len);
            }
            bos.flush();
        } catch(IOException e) {
            e.printStackTrace();
        }
    }
}
