package com.quhong.operation.httpResult;

import com.quhong.enums.HttpCode;

/**
 * <AUTHOR>
 * @date 2020/9/29
 */
public class OperationHttpCode extends HttpCode {

    public static final HttpCode CHANNEL_ID_NOT_EXIST = new HttpCode(10, "channelId not exist");
    public static final HttpCode CHANNEL_SIZE_EMPTY = new HttpCode(11,"channel size is zero in system");
    public static final HttpCode USER_NOT_EXIST = new HttpCode(1006, "user not exist");
    public static final HttpCode MANAGE_NOT_EXIST = new HttpCode(1007, "manage not exist");;
    public static final HttpCode GID_ALREADY_EXISTS = new HttpCode(1008, "Question group id already exists");
    public static final HttpCode GIFT_SVG_STATUS = new HttpCode(1008, "SVG gift 不能设置为有效");
}
