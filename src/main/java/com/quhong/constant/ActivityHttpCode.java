package com.quhong.constant;

import com.quhong.enums.HttpCode;

public class ActivityHttpCode extends HttpCode {
    public static final HttpCode NOT_ACTIVE_TIME = new HttpCode(5001, "not_active_time");
    public static final HttpCode INSUFFICIENT_NUMBER = new HttpCode(5002, "insufficient_number");
    public static final HttpCode INCORRECT_INPUT_ID = new HttpCode(5003, "incorrect_input_id");
    public static final HttpCode SUCCESS_RESULT = new HttpCode(0, "success_result");

    public static final HttpCode CELEBRITY_HAS_LIKE = new HttpCode(9001, "celebrity_has_like");
    public static final HttpCode THREE_TIMES_LIMIT = new HttpCode(9002, "three_times_limit");
    public static final HttpCode NOT_START = new HttpCode(9003, "The activity is not start.");
    public static final HttpCode SIGN_ALREADY = new HttpCode(9004, "You already signed.");
    public static final HttpCode CANNOT_SIGN = new HttpCode(9005, "You cannot sign.");
    public static final HttpCode ALREADY_SHARE = new HttpCode(9006, "already_share");
    public static final HttpCode LEVEL_LIMIT = new HttpCode(9007, "level_limit");
    public static final HttpCode DIRTY_WORD = new HttpCode(9008, "dirty_word");
    public static final HttpCode DIRTY_IMAGE = new HttpCode(9009, "dirty_image");


    public static final HttpCode QUEEN_NOT_DIAMONDS = new HttpCode(6001, "queen_not_diamonds");
    public static final HttpCode QUEEN_ALREADY_LIKE = new HttpCode(6002, "queen_already_like");
    public static final HttpCode QUEEN_NOT_QUEEN = new HttpCode(6003, "queen_not_queen");
    public static final HttpCode QUEEN_ALREADY_GET = new HttpCode(6004, "queen_already_get");
    public static final HttpCode QUEEN_CANNOT_SELF = new HttpCode(6005, "queen_cannot_self");
    public static final HttpCode QUEEN_ALREADY_GIVE = new HttpCode(6006, "queen_already_give");
    public static final HttpCode QUEEN_NUMS_ENOUGH = new HttpCode(6007, "queen_nums_enough");
    public static final HttpCode QUEEN_EXPRESS_SUCCESS = new HttpCode(0, "queen_express_success");
    public static final HttpCode QUEEN_GET_SUCCESS = new HttpCode(0, "queen_get_success");

    public static final HttpCode QUIZ_ACTIVITY_HAS_ENDED = new HttpCode(1001, "quiz_activity_has_ended");
    public static final HttpCode QUIZ_ACTIVITY_HAS_NOT_STARTED = new HttpCode(1002, "quiz_activity_has_not_started");
    public static final HttpCode NUM_OF_ANSWERS_HAS_BEEN_USED_UP = new HttpCode(1003, "num_of_answers_has_been_used_up");
    public static final HttpCode NOT_ENOUGH_COIN = new HttpCode(1004, "not_enough_coin");
    public static final HttpCode DIAMOND_NOT_ENOUGH = new HttpCode(1005, "diamond_not_enough");
    public static final HttpCode NOT_ANSWER_AGAIN = new HttpCode(1006, "not_answer_again");
    public static final HttpCode NOT_BEEN_UNLOCKED = new HttpCode(1007, "not_been_unlocked");

    public static final HttpCode BEAUTIFUL_LENGTH_NOY_ALLOW = new HttpCode(6001, "beautiful_length_not_allow");
    public static final HttpCode BEAUTIFUL_ENTER_VALID= new HttpCode(6001, "beautiful_enter_valid");
    public static final HttpCode BEAUTIFUL_USING_CODE= new HttpCode(6001, "beautiful_using_code");
    public static final HttpCode BEAUTIFUL_NOT_HONOR_USER= new HttpCode(6001, "beautiful_not_honor_user");
    public static final HttpCode BEAUTIFUL_RID_USED= new HttpCode(6001, "beautiful_rid_used");

    public static final HttpCode NO_TARGET_SPECIFIED = new HttpCode(20, "no_target_specified");
    public static final HttpCode IMAGE_UPLOAD_FAILED = new HttpCode(20, "image_upload_failed");
    public static final HttpCode ALREADY_REPORTED_THIS_USER = new HttpCode(41, "already_reported_this_user");
    public static final HttpCode ALREADY_REPORTED_THIS_FAMILY = new HttpCode(42, "already_reported_this_family");
    public static final HttpCode EXPLAIN_THE_REASON_FOR_THE_REPORT = new HttpCode(20, "explain_the_reason_for_the_report");
    public static final HttpCode TEXT_YOU_POSTED_IS_TOO_LONG = new HttpCode(20, "text_you_posted_is_too_long");

    public static final HttpCode NATIONAL_DAY_HELP_NOT_USER = new HttpCode(6001, "national_day_help_not_user");
    public static final HttpCode NATIONAL_DAY_HELP_DAILY_LIMIT = new HttpCode(6001, "national_day_help_daily_limit");
    public static final HttpCode NATIONAL_DAY_HELP_SUCCESS = new HttpCode(6001, "national_day_help_success");
    public static final HttpCode NATIONAL_DAY_HELP_ALREADY = new HttpCode(6001, "national_day_help_already");
    public static final HttpCode NATIONAL_DAY_HELP_SELF = new HttpCode(6001, "national_day_help_self");
    public static final HttpCode NATIONAL_DAY_HELP_SAME_DEVICE = new HttpCode(6001, "national_day_help_same_device");
    public static final HttpCode NATIONAL_DAY_HELP_NOT_DRAW = new HttpCode(6001, "national_day_help_not_draw");
    public static final HttpCode USER_NOT_EXIST = new HttpCode(10, "user_not_exist");


    public static final HttpCode NATIONAL3_DAY_HELP_ALREADY = new HttpCode(6001, "national3_day_help_already");
    public static final HttpCode NATIONAL3_DAY_24H_LIMIT = new HttpCode(6001, "national3_day_24h_limit");
    public static final HttpCode NATIONAL3_DAY_HELP_MAX_LIMIT = new HttpCode(6001, "national3_day_help_max_limit");


    public static final HttpCode OWNER_AND_ADMINS_CAN_CREATE_VOTE = new HttpCode(2001, "owner_and_admins_can_create_vote");
    public static final HttpCode EACH_PERSON_CAN_ONLY_VOTE_ONCE = new HttpCode(2002, "each_person_can_only_vote_once");
    public static final HttpCode VOTING_TIME_ENDS = new HttpCode(2003, "voting_time_ends");
    public static final HttpCode OWNER_AND_ADMIN_CAN_CLOSE_VOTING = new HttpCode(2004, "owner_and_admin_can_close_voting");
    public static final HttpCode VOTING_FUNCTION_HAS_BEEN_TURNED_ON = new HttpCode(2005, "voting_function_has_been_turned_on_in");
    public static final HttpCode THE_VOTE_ARE_NOT_ALLOWED_CONTAIN_DIRTY_WORDS = new HttpCode(2006, "the_vote_are_not_allowed_to_contain_dirty_words");
    public static final HttpCode COIN_NOT_ENOUGH = new HttpCode(55, "not_enough_coin");
    public static final HttpCode NOT_ENOUGH_DIAMOND = new HttpCode(50, "diamond_not_enough");

    public static final HttpCode PLANT_TREE_FULL_TREE = new HttpCode(6001, "plant_tree_full_tree");
    public static final HttpCode PLANT_TREE_NOT_ENOUGH_WATER = new HttpCode(6001, "plant_tree_not_enough_water");
    public static final HttpCode PANDA_CONTRACT_SIGN = new HttpCode(6001, "panda_contract_sign");

    public static final HttpCode FEED_BACK_PROBLEM_EMPTY = new HttpCode(6001, "feed_back_problem_empty");
    public static final HttpCode FEED_BACK_CONTACT_ERROR = new HttpCode(6002, "feed_back_contact_error");

    public static final HttpCode LOCK_GIFT_UNLOCK = new HttpCode(6002, "lock_gift_unlock");

    public static final HttpCode USER_NOT_FIND_EXIST = new HttpCode(6001, "user_not_exist");
    public static final HttpCode USER_NOT_DEFENSE_STATE = new HttpCode(6002, "user_not_defense_state");
    public static final HttpCode USER_IN_DEFENSE_STATE = new HttpCode(6003, "user_in_defense_state");
    public static final HttpCode INSUFFICIENT_NUMBER_POINT = new HttpCode(5002, "insufficient_number_point");
    public static final HttpCode CANNOT_YOUR_SELF = new HttpCode(5003, "cannot_your_self");

    public static final HttpCode SHINING_CONFIG_ERROR = new HttpCode(6100, "server config error");
    public static final HttpCode SHINING_NOT_JOIN = new HttpCode(6101, "shining_not_join");
    public static final HttpCode SHINING_NOT_ENOUGH_NUM = new HttpCode(6102, "shining_not_enough_num");
    public static final HttpCode SHINING_NOT_ADD_MYSELF = new HttpCode(6103, "shining_not_add_myself");
    public static final HttpCode SHINING_NOT_REAL_ADMIN = new HttpCode(6103, "shining_not_real_admin");
    public static final HttpCode SHINING_ADMIN_EXIST = new HttpCode(6103, "shining_admin_exist");
    public static final HttpCode SHINING_SUB_FAILED = new HttpCode(6103, "shining_sub_failed");

    public static final HttpCode CARROM_COLLECT_FAILED = new HttpCode(6104, "collect failed");
    public static final HttpCode ALREADY_LIKE = new HttpCode(6002, "already_like");
    public static final HttpCode LIKE_SUCCESS = new HttpCode(0, "like_success");

    public static final HttpCode USER_NOT_BACK= new HttpCode(6010, "not_returning_to_users");
    public static final HttpCode INVALID_BACK = new HttpCode(6010, "Invalid back activity");
    public static final HttpCode DAU_REWARD_FAIL = new HttpCode(6010, "dau reward fail");

    public static final HttpCode INSUFFICIENT_HOE_NUMBER = new HttpCode(6011, "insufficient_hoe_number");

    public static final HttpCode SUPER_QUEEN_ONLY_WOMEN = new HttpCode(6103, "super_queen_only_women");
    public static final HttpCode SUPER_QUEEN_JOIN_ALREADY = new HttpCode(6103, "super_queen_join_already");
    public static final HttpCode SUPER_QUEEN_NOT_JOIN = new HttpCode(6103, "super_queen_not_join");

    public static final HttpCode GAME_EVENT_GET_ALREADY = new HttpCode(6103, "game_event_get_already");
    public static final HttpCode GAME_EVENT_NOT_UNLOCK = new HttpCode(6103, "game_event_not_unlock");
    public static final HttpCode GAME_EVENT_ONLY_TO_ADMIN = new HttpCode(6103, "game_event_only_to_admin");
    public static final HttpCode GAME_EVENT_GET_MAX = new HttpCode(6103, "game_event_get_max");
    public static final HttpCode GAME_EVENT_QUESTION_FAILED = new HttpCode(6104, "game_event_question_failed");
    public static final HttpCode GAME_EVENT_QUESTION_ALREADY = new HttpCode(6105, "game_event_question_already");

    public static final HttpCode CARROM_MASTER_PLAYER_IN_GAME = new HttpCode(6103, "player_in_game");
    public static final HttpCode LUDO_MASTER_PLAYER_IN_GAME = new HttpCode(6103, "player_in_game_ludo");
    public static final HttpCode MONSTER_MASTER_PLAYER_IN_GAME = new HttpCode(6103, "player_in_game_monster");
    public static final HttpCode UNO_MASTER_PLAYER_IN_GAME = new HttpCode(6103, "player_in_game_uno");
    public static final HttpCode NOT_ENOUGH_CHANCES_TO_DRAW = new HttpCode(6011, "not_enough_chances_to_draw");
    public static final HttpCode NOT_ENOUGH_RING = new HttpCode(6010, "not_enough_ring");
    public static final HttpCode NOT_ENOUGH_ENVELOPE = new HttpCode(6009, "not_enough_envelope");

    public static final HttpCode SHARING_OFFICER_ALREADY_SUBMIT = new HttpCode(6001, "sharing_officer_already_submit");
    public static final HttpCode SHARING_OFFICER_DEVICE_MAX_NUM = new HttpCode(6001, "sharing_officer_device_max_num");
    public static final HttpCode SHARING_OFFICER_DEVICE_WEEK_MAX_NUM = new HttpCode(6001, "sharing_officer_device_week_max_num");

    public static final HttpCode ARABIC_PAINTER_ALREADY_JOIN = new HttpCode(6001, "arabic_painter_already_join");
    public static final HttpCode ARABIC_PAINTER_MAX_FILE_LIMIT = new HttpCode(6001, "arabic_painter_max_file_limit");

    public static final HttpCode CORRESPONDING_PATTERN_IS_MISSING = new HttpCode(6003, "corresponding_pattern_is_missing");

    public static final HttpCode TOMATO_IS_NOT_OVER= new HttpCode(6003, "tomato_is_not_over");
    public static final HttpCode TOMATO_IS_OUT_OF_RANK= new HttpCode(6003, "tomato_is_out_of_rank");
    public static final HttpCode TOMATO_IS_SUBMIT= new HttpCode(6003, "tomato_is_submit");
    public static final HttpCode TOMATO_IS_USER_EXIST = new HttpCode(6003, "tomato_is_user_exist");
    public static final HttpCode TOMATO_IS_MAX_NUM = new HttpCode(6003, "tomato_is_max_num");
    public static final HttpCode TOMATO_USER_NOT_EXIST = new HttpCode(6003, "tomato_user_not_exist");
    
    public static final HttpCode GIFT_SENDING_INSUFFICIENT = new HttpCode(6004, "gift_sending_insufficient");

    public static final HttpCode BACK_INVITE_CODE_NOT_ALLOW = new HttpCode(6010, "invitation_code_is_invalid");
    public static final HttpCode BACK_MAX_NUM_NOT_ALLOW = new HttpCode(6010, "max_num_not_allow") ;
    public static final HttpCode BACK_MYSELF_NOT_ALLOW = new HttpCode(6010, "cannot_invite_yourself") ;
    public static final HttpCode BACK_NOT_FRIEND = new HttpCode(6010, "back_not_friend") ;
    public static final HttpCode BACK_NOT_LONG_FRIEND = new HttpCode(6010, "back_not_long_friend") ;
    public static final HttpCode BACK_NOT_BACK_USER = new HttpCode(6010, "back_not_back_user") ;
    public static final HttpCode BACK_AID_DEV_NOT_ALLOW = new HttpCode(6010, "User or device has been invited","تمت دعوة المستخدم أو الجهاز");

    public static final HttpCode NOT_ENOUGH_SHEEP_CHANCE = new HttpCode(6010, "not_enough_sheep_chance");

    // 交友广场相关错误代码
    public static final HttpCode SEND_NOTE_COUNT_MAX = new HttpCode(6020, "send_note_count_max");
    public static final HttpCode SEND_GREET_COUNT_MAX = new HttpCode(6021, "send_greet_count_max");
    public static final HttpCode ALREADY_APPLIED = new HttpCode(6021, "already_applied");
    public static final HttpCode SET_REFUSE_TO_ADD_FRIENDS = new HttpCode(6021, "set_refuse_to_add_friends");
    public static final HttpCode SEND_NOTE_DEVICE_COUNT_MAX = new HttpCode(6020, "send_note_device_count_max");

    // crash活动相关错误代码
    public static final HttpCode CRASH_EXCHANGE_INSUFFICIENT_POINT = new HttpCode(6010, "crash_exchange_insufficient_point");
    public static final HttpCode CRASH_DRAW_INSUFFICIENT_NUMBER_POINT = new HttpCode(6010, "crash_draw_insufficient_number_point");

    // AI定制礼物相关错误代码
    public static final HttpCode ALREADY_PASS = new HttpCode(6022, "already_pass");
    public static final HttpCode ALREADY_PENDING = new HttpCode(6023, "already_pending");
    public static final HttpCode ALREADY_REJECTED = new HttpCode(6024, "already_rejected");

    // 默契挑战相关
    public static final HttpCode MATCH_QUIZ_ALREADY_GENERATE = new HttpCode(6025, "match_quiz_already_generate"); // 问卷已经生成
    public static final HttpCode MATCH_QUIZ_CANNOT_ANSWER_SELF = new HttpCode(6026, "match_quiz_cannot_answer_self"); // 不能回答自己的问卷
    public static final HttpCode MATCH_QUIZ_NOT_GENERATE = new HttpCode(6027, "match_quiz_not_generate"); // 该用户没有生成问卷
    public static final HttpCode MATCH_QUIZ_ALREADY_ANSWER = new HttpCode(6028, "match_quiz_already_answer"); // 已经回答过该问卷

    // happy trip 相关
    public static final HttpCode ROUND_NOT_MATCH = new HttpCode(81, "round not match"); // 已经回答过该问卷

}
