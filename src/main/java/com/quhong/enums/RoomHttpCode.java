package com.quhong.enums;

public class RoomHttpCode extends HttpCode {
    public static final HttpCode BE_KICKED_OUT_OF_THE_ROOM = new HttpCode(0, "You are kicked out of the room by room owner and will not be able to enter the room in 24hrs", "تم طردك من الغرفة بواسطة مالك الغرفة ولا يمكنك دخول الغرفة مرّة أخرى لمدة 24 ساعة");
    public static final HttpCode NEED_BECOME_MEMBER = new HttpCode(2002, "");
    public static final HttpCode KICK_OUt = new HttpCode(91, "'You are kicked out of the room by room owner and will not be able to enter the room in 24hrs", "تم طردك من الغرفة بواسطة مالك الغرفة ولا يمكنك دخول الغرفة مرّة أخرى لمدة 24 ساعة");
    public static final HttpCode ROOM_PARAM_ERROR = new HttpCode(91, "");
    public static final HttpCode PERMIT_ERROR = new HttpCode(2101, "error code:2101");
    public static final HttpCode DIRTY_WORD = new HttpCode(2104, "Contains sensitive content, failed to save.", "يحتوي على محتوى حساس، فشل في الحفظ.");
    public static final HttpCode NOT_NEW_USER = new HttpCode(3001, "Current user not is new.");
    public static final HttpCode VIDEO_ROOM_EMPTY = new HttpCode(3002, "video room is empty.");
    public static final HttpCode INCORRECT_INPUT_ID = new HttpCode(3003, "incorrect_input_id");
    public static final HttpCode CREATE_ROOM_LIMIT = new HttpCode(41, "create_room_limit");
    public static final HttpCode ROOM_NOT_EXIST = new HttpCode(44, "room_not_exist");
    public static final HttpCode LIVE_ROOM_LIMIT_NEW = new HttpCode(45, "live_room_limit_new");
    public static final HttpCode BLOCK_CREATE_ROOM = new HttpCode(72, "block_create_room");
    public static final HttpCode ROOM_BLOCK = new HttpCode(41, "room_block");
    public static final HttpCode ROOM_FULL = new HttpCode(56, "room_full");
    public static final HttpCode REPORTED = new HttpCode(42, "reported");
    public static final HttpCode USER_INVALID = new HttpCode(48, "user_invalid");
    public static final HttpCode BLOCKED = new HttpCode(47, "blocked");
    public static final HttpCode KICKED = new HttpCode(46, "kicked");
    public static final HttpCode DIRTY_WORD_TITLE = new HttpCode(41, "dirty_word_title");
    public static final HttpCode PASSWORD_ERROR = new HttpCode(122, "password_error");
    public static final HttpCode MIC_THEME_OPT_AUTH_ERROR = new HttpCode(123, "mic_theme_opt_auth_error");
    public static final HttpCode MIC_THEME_OPT_AUTH_QUEEN = new HttpCode(124, "mic_theme_opt_auth_queen");
    public static final HttpCode MIC_THEME_OPT_AUTH_3 = new HttpCode(123, "mic_theme_opt_auth_3");
    public static final HttpCode MIC_THEME_OPT_NOT_COMP = new HttpCode(124, "mic_theme_opt_not_comp");
    public static final HttpCode PLEASE_UPGRADE_VERSION = new HttpCode(126, "please_upgrade_version");
    public static final HttpCode PLEASE_CLOSE_VIDEO = new HttpCode(133, "please_close_video");
    public static final HttpCode PLEASE_CLOSE_TRUTH_DARE = new HttpCode(134, "please_close_truth_dare");
    public static final HttpCode ROOM_OWNER_AUTH_ERROR = new HttpCode(135, "room_owner_auth_error");
    public static final HttpCode PLAY_WHO_IS_SPY_NOT_CHANGE_THEME = new HttpCode(136, "play_who_is_spy_not_change_theme");
    public static final HttpCode UPLOAD_ROOM_THEME_ERROR = new HttpCode(41, "upload_room_theme_error");
    public static final HttpCode NEED_UPGRADE = new HttpCode(441, "need_upgrade");
    public static final HttpCode MAX_30 = new HttpCode(41, "max_30");
    public static final HttpCode MSG_TOO_LENGTH = new HttpCode(41, "The maximum allowed is 500 characters.", "يسمح ب 500 حرف كحد أقصى.");
    public static final HttpCode NOT_ROOM_OWNER_OR_VICE_OWNER = new HttpCode(41, "not_room_owner_or_vice_owner");
    public static final HttpCode NOT_ALLOW_VIP = new HttpCode(43, "not_allow_vip");
    public static final HttpCode VICE_OWNER_EXISTS = new HttpCode(44, "vice_owner_exists");
    public static final HttpCode ADMIN_NUM_REACHED_LIMIT = new HttpCode(45, "admin_num_reached_limit");
    public static final HttpCode ROOM_HAS_BEEN_CONQUERED = new HttpCode(60, "room_has_been_conquered");
    public static final HttpCode NOT_ROOM_PALACE = new HttpCode(41, "not_room_palace");
    public static final HttpCode THEME_NOT_VALID = new HttpCode(10, "theme_not_valid");
    public static final HttpCode ROOM_INFO_HAS_DIRTY_WORD = new HttpCode(41, "room_info_has_dirty_word");
    public static final HttpCode PERMISSION_DENIED_DELETE_MEMBER = new HttpCode(41, "permission_denied_delete_member");
    public static final HttpCode OVER_THE_LIMITATION = new HttpCode(45, "over_the_limitation");
    public static final HttpCode ALREADY_ROOM_MEMBER = new HttpCode(30, "already_room_member");
    public static final HttpCode KEY_WORD_CANNOT_BE_NULL = new HttpCode(20, "key_word_cannot_be_null");
    public static final HttpCode NO_PERMISSION_KICK_USER = new HttpCode(60, "no_permission_kick_user");
    public static final HttpCode NOT_KICK_ADMIN_USER = new HttpCode(61, "not_kick_admin_user");
    public static final HttpCode YOUSTAR_USER_CANNOT_BE_KICK = new HttpCode(62, "youstar_user_cannot_be_kick");
    public static final HttpCode TYCOON_4_CANNOT_BE_KICK = new HttpCode(63, "tycoon_4_cannot_be_kick");
    public static final HttpCode MESSAGE_EXPIRED = new HttpCode(64, "message_expired");
    public static final HttpCode NO_RECORD_OF_KICK = new HttpCode(65, "no_record_of_kick");
    public static final HttpCode ALREADY_IN_BLACKLIST = new HttpCode(30, "already_in_blacklist");
    public static final HttpCode UNION_NOT_ENOUGH_DIAMOND = new HttpCode(50, "not_enough_diamond");
    public static final HttpCode UPLOAD_ROOM_THEME_LIMIT = new HttpCode(41, "upload_room_theme_limit");
    public static final HttpCode NO_PERMISSION_TO_OPERATE = new HttpCode(41, "no_permission_to_operate");
    public static final HttpCode ACCOUNT_HAS_BEEN_FROZEN = new HttpCode(42, "account_has_been_frozen");
    public static final HttpCode SEND_PIC_TURNED_OFF = new HttpCode(43, "send_pic_turned_off");
    public static final HttpCode YOU_HAVE_BEEN_BANNED = new HttpCode(44, "you_have_been_banned");
    public static final HttpCode PLEASE_UPGRADE_LEVEL = new HttpCode(45, "please_upgrade_level");
    public static final HttpCode DURING_FUNCTION_OPTIMIZATION = new HttpCode(46, "during_function_optimization");
    public static final HttpCode NOT_FORBID_USER_TALK = new HttpCode(47, "not_forbid_user_talk");
    public static final HttpCode ALREADY_FORBID_USER_TALK = new HttpCode(48, "already_forbid_user_talk");
    public static final HttpCode NO_PERMISSION_CANCEL_BAN_TEXT = new HttpCode(49, "no_permission_cancel_ban_text");
    public static final HttpCode NOT_CANCEL_FORBID_USER_TALK = new HttpCode(50, "not_cancel_forbid_user_talk");
    public static final HttpCode NO_PERMISSION_FORBID_ALL_MIC = new HttpCode(51, "no_permission_forbid_all_mic");
    public static final HttpCode NO_PERMISSION_OPERATE_PK = new HttpCode(52, "no_permission_operate_pk");
    public static final HttpCode TYCOON4_OR_ABOVE_CAN_UPLOAD = new HttpCode(60, "tycoon4_or_above_can_upload");

    public static final HttpCode VIP2_EXPIRED = new HttpCode(61, "vip2_expired");
    public static final HttpCode VIP4_EXPIRED = new HttpCode(62, "vip4_expired");
    public static final HttpCode BACKGROUND_EXPIRED = new HttpCode(63, "background_expired");
    public static final HttpCode BACKGROUND_NOT_CHANGE = new HttpCode(63, "background_not_change");
    public static final HttpCode UPLOAD_BG_NO_MORE_THAN_10_PHOTOS = new HttpCode(64, "upload_bg_no_more_than_10_photos");
    public static final HttpCode NOT_HAVE_PERMISSION = new HttpCode(41, "not_have_permission");
    public static final HttpCode PLEASE_CHANGE_TIME = new HttpCode(42, "please_change_time");
    public static final HttpCode THE_SETTINGS_ARE_INVALID = new HttpCode(43, "the_settings_are_invalid");
    public static final HttpCode HAVE_ALREADY_BEEN_REWARDED = new HttpCode(44, "have_already_been_rewarded");
    public static final HttpCode NO_REWARDS_AVAILABLE = new HttpCode(45, "no_rewards_available");
    public static final HttpCode TIMED_OUT_AND_CANNOT_BE_CLAIMED = new HttpCode(46, "timed_out_and_cannot_be_claimed");
    public static final HttpCode EVENT_TIME_HAS_EXPIRED = new HttpCode(47, "event_time_has_expired");
    public static final HttpCode EVENT_HAS_STARTED = new HttpCode(48, "event_has_started");
    public static final HttpCode EVENT_HAS_BEEN_DELETED = new HttpCode(49, "event_has_been_deleted");
    public static final HttpCode NOT_HAVE_PERMISSION_DELETE_EVENT = new HttpCode(61, "not_have_permission_delete_event");
    public static final HttpCode HAVE_DIRTY_WORDS_OR_OFFENDING_IMAGES = new HttpCode(60, "have_dirty_words_or_offending_images");
    public static final HttpCode ACCOUNT_HAS_DISABLED = new HttpCode(62, "Your account has been disabled.","تم تعطيل حسابك.");
    public static final HttpCode EVENT_INCORRECT_INPUT_ORGANIZER = new HttpCode(63, "event_incorrect_input_organizer");
    public static final HttpCode EVENT_INPUT_ORGANIZER_LIMIT = new HttpCode(64, "event_input_organizer_limit");
    public static final HttpCode EVENT_SUPPORT_TIME_LIMIT = new HttpCode(65, "event_support_time_limit");
    public static final HttpCode EVENT_SUPPORT_NO_TIMES = new HttpCode(65, "event_support_no_times");
    public static final HttpCode EVENT_SUPPORT_NO_MOVE = new HttpCode(65, "event_support_no_move");
    public static final HttpCode EVENT_SUPPORT_INCLUDE_USER = new HttpCode(65, "event_support_include_user");
    public static final HttpCode VIP_CAN_NOT_BAN_MSG = new HttpCode(66, "vip_can_not_ban_msg");
    public static final HttpCode SEND_PICTURE_MALE_MSG = new HttpCode(67, "send_picture_male_msg");
    public static final HttpCode SEND_PICTURE_FEMALE_MSG = new HttpCode(68, "send_picture_female_msg");
    public static final HttpCode APPLICATION_REQUIRED = new HttpCode(666, "For better experience, please update YouStar in the store.", "من أجل الحصول على تجربة أفضل ، يرجى تحديث YouStar في المتجر");
    public static final HttpCode APPLICATION_CANCELED = new HttpCode(667, "Up mic application canceled.");
    public static final HttpCode APPLIED = new HttpCode(668, "You have applied for a mic.", "لقد تقدمت بطلب للحصول على ميكروفون");
    public static final HttpCode LIVE_ROOM_LIMIT_LEVEL = new HttpCode(669, "live_room_limit_level");
    public static final HttpCode ROOM_UPGRADED = new HttpCode(670, "The room function is being upgraded, please visit other rooms.", "تتم ترقية وظيفة الغرفة ، يرجى زيارة الغرف الأخرى.");
    public static final HttpCode UNMODIFIABLE_MIC_PERMISSION = new HttpCode(671, "In the live stream mode, it is not supported to modify the permission of taking mic.", "في وضع البث المباشر ، لا يتم دعم تعديل إذن أخذ الميكروفون");
    public static final HttpCode CHAT_HALL_LEVEL_LIMIT = new HttpCode(672, "chat_hall_level_limit");
    public static final HttpCode CHAT_HALL_VIP_LIMIT = new HttpCode(673, "chat_hall_vip_limit");
    public static final HttpCode CHAT_HALL_SHARE_LIMIT = new HttpCode(674, "The number of shares has been exhausted today.","لقد تم استنفاد عدد المشاركات اليوم");
    public static final HttpCode CAN_NOT_UP_MIC_LIMIT = new HttpCode(675, "can_not_up_mic_limit");
    public static final HttpCode CAN_NOT_DOWN_MIC_LIMIT = new HttpCode(676, "can_not_down_mic_limit");
    public static final HttpCode CAN_NOT_LOCK_MIC_LIMIT = new HttpCode(677, "can_not_lock_mic_limit");
    public static final HttpCode CAN_NOT_REMOVE_MIC_LIMIT = new HttpCode(678, "can_not_remove_mic_limit");
    public static final HttpCode CAN_NOT_CHANGE_ROOM_MODE = new HttpCode(679, "can_not_change_room_mode");
    public static final HttpCode WHO_IS_SPY_CAN_NOT_DOWN_MIC_LIMIT = new HttpCode(679, "who_is_spy_can_not_down_mic_limit");
    public static final HttpCode WHO_IS_SPY_CAN_NOT_LOCK_MIC_LIMIT = new HttpCode(679, "who_is_spy_can_not_lock_mic_limit");
    public static final HttpCode CAN_NOT_OPEN_VIDEO = new HttpCode(680, "can_not_open_video");
    public static final HttpCode CAN_NOT_SET_ROOM_LOCK = new HttpCode(681, "can_not_set_pwd_limit");
    public static final HttpCode WHO_IS_SPY_CAN_NOT_SET_ROOM_LOCK = new HttpCode(681, "who_is_spy_can_not_set_room_lock");
    public static final HttpCode CAN_NOT_KICK_USER = new HttpCode(682, "can_not_kick_user");
    public static final HttpCode CAN_NOT_CHANGE_ROOM = new HttpCode(683, "can_not_change_room");

    public static final HttpCode HAVE_REPEAT_MSG_TASK_IN_PROGRESS = new HttpCode(101, "have_repeat_msg_task_in_progress");
    public static final HttpCode ILLEGAL_CONTENT = new HttpCode(102, "illegal_content");
    public static final HttpCode PLEASE_JOIN_THE_ROOM_MEMBERS_FIRST = new HttpCode(103, "please_join_the_room_members_first");
    public static final HttpCode PLEASE_FOLLOWING_ROOM_FIRST = new HttpCode(104, "please_following_room_first");
    public static final HttpCode NO_BROADCASTABLE_USER = new HttpCode(105, "no_broadcastable_users");
    public static final HttpCode THE_PUBLIC_CHAT_AREA_HAS_CLOSED = new HttpCode(106, "the_public_chat_area_has_closed");
    public static final HttpCode YOU_ARE_BANNED_SEND_MSG = new HttpCode(107, "you_are_banned_send_msg");
    public static final HttpCode THE_PUBLIC_CHAT_AREA_HAS_BEEN_RESTRICTED = new HttpCode(108, "the_public_chat_area_has_been_restricted");

    public static final HttpCode CODE_ACTIVITY_NOT_START = new HttpCode(80, "activity not start yet");

    public static final HttpCode USER_RISK_INVALID_JOIN_ROOM = new HttpCode(48, "user_risk_invalid_join_room");
    public static final HttpCode USER_RISK_INVALID_ROOM_MSG = new HttpCode(48, "user_risk_invalid_room_msg");
    public static final HttpCode SEND_GIFT_FREEZE = new HttpCode(109, "send_gift_freeze");

    public static final HttpCode NOT_ENOUGH_COIN = new HttpCode(55, "not_enough_coin");
    public static final HttpCode NOT_SWITCH_GAME_RUNNING = new HttpCode(3002, "game_processing_not_switch");
    public static final HttpCode GAME_ROOM_NOT_HAVE_GAME = new HttpCode(3003, "game_room_not_have_game");
    public static final HttpCode NOT_KICK_GAME_PROCESSING = new HttpCode(3004, "game_processing_not_kick");
    public static final HttpCode GOING_ON_GAME_PROCESSING = new HttpCode(3005, "game_running");
    public static final HttpCode THE_WHO_IS_SPY_IS_RUNNING = new HttpCode(3006, "the_who_is_spy_is_running");


    /**
     * 消息内容不能为空
     */
    public static final HttpCode MESSAGE_CONTENT_EMPTY = new HttpCode(3002, "Message content cannot be empty", "لا يمكن أن يكون محتوى الرسالة فارغًا");

    /**
     * 房间ID不能为空
     */
    public static final HttpCode ROOM_ID_EMPTY = new HttpCode(3002, "Room ID cannot be empty", "لا يمكن أن يكون معرف الغرفة فارغًا");

    /**
     * 消息内容过长
     */
    public static final HttpCode MESSAGE_TOO_LONG = new HttpCode(3002, "Message content cannot exceed 200 characters", "لا يمكن أن يتجاوز محتوى الرسالة 200 حرف");

    /**
     * 欢迎消息不存在
     */
    public static final HttpCode MESSAGE_NOT_EXIST = new HttpCode(3002, "Welcome message does not exist", "رسالة الترحيب غير موجودة");

    /**
     * 审核状态无效
     */
    public static final HttpCode INVALID_REVIEW_STATUS = new HttpCode(3002, "Invalid review status", "حالة المراجعة غير صالحة");

    /**
     * 拒绝原因过长
     */
    public static final HttpCode REJECT_REASON_TOO_LONG = new HttpCode(3002, "Reject reason cannot exceed 50 characters", "لا يمكن أن يتجاوز سبب الرفض 50 حرفًا");



    public RoomHttpCode(int code, String... langMsg) {
        super(code, langMsg);
    }
}
