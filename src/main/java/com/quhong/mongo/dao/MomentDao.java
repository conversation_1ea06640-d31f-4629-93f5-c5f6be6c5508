package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.MomentConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.MomentData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@Component
public class MomentDao {

    private static final Logger logger = LoggerFactory.getLogger(MomentDao.class);

    public static final String CACHE_MANAGER = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE;
    public static final String TABLE_NAME = "moment";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public List<MomentData> getMomentList(String aid, List<Integer> show, Integer start, Integer size) {
        List<MomentData> dataList = new ArrayList<>();
        try {
            Criteria criteria = Criteria.where("uid").is(aid).and("is_blocked").is(0);
            if (!CollectionUtils.isEmpty(show)) {
                criteria.and("show").in(show);
            }
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort).skip(start).limit(size);
            dataList = mongoTemplate.find(query, MomentData.class);
        } catch (Exception e) {
            logger.error("get moment list error. {}", e.getMessage(), e);
        }
        return dataList;
    }

    public void save(MomentData momentData) {
        try {
            mongoTemplate.save(momentData);
        } catch (Exception e) {
            logger.error("save moment error uid={} {}", momentData.getUid(), e.getMessage(), e);
        }
    }

    @CacheEvict(value = "momentCache", key = "#p1", cacheManager = CACHE_MANAGER)
    public void delete(MomentData momentData, String mid) {
        try {
            logger.info("delete moment mid={}", mid);
            mongoTemplate.remove(momentData);
        } catch (Exception e) {
            logger.error("delete moment error uid={} mid={}", momentData.getUid(), mid, e);
        }
    }

    @Cacheable(value = "momentCache", key = "#p0", cacheManager = CACHE_MANAGER)
    public MomentData getMoment(String mid) {
        try {
            return mongoTemplate.findOne(new Query(Criteria.where("_id").is(mid).and("is_blocked").is(0)), MomentData.class);
        } catch (Exception e) {
            logger.error("getMoment error mid={} {}", mid, e.getMessage(), e);
            return null;
        }
    }

    @CacheEvict(value = "momentCache", key = "#p1", cacheManager = CACHE_MANAGER)
    public void updateImage(List<MomentData.Image> imageList, String mid) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.set("imgs", imageList);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateImage error mid={} {}", mid, e.getMessage(), e);
        }
    }

    @CacheEvict(value = "momentCache", key = "#p0", cacheManager = CACHE_MANAGER)
    public void addLikes(String mid, String aid) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.push("likes", aid);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("add moment likes error mid={} {}", mid, e.getMessage(), e);
        }
    }

    @CacheEvict(value = "momentCache", key = "#p0", cacheManager = CACHE_MANAGER)
    public void removeLikes(String mid, String aid) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.pull("likes", aid);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("remove moment likes error mid={} {}", mid, e.getMessage(), e);
        }
    }

    @CacheEvict(value = "momentCache", key = "#p0", cacheManager = CACHE_MANAGER)
    public void incrMomentComments(String mid, int change) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.inc("comments", change);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("incrMomentComments error mid={} {}", mid, e.getMessage(), e);
        }
    }

    @CacheEvict(value = "momentCache", key = "#p0", cacheManager = CACHE_MANAGER)
    public void incrMomentGifted(String mid, int change, int giftTotalPrice) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.inc("gifted", change);
            update.inc("giftTotalPrice", giftTotalPrice);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("incrMomentGifted error mid={} change={} {}", mid, e.getMessage(), e);
        }
    }

    @CacheEvict(value = "momentCache", key = "#p0", cacheManager = CACHE_MANAGER)
    public void incrMomentRepost(String mid) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.inc("repost", 1);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("incrMomentRepost error mid={} {}", mid, e.getMessage(), e);
        }
    }

    @CacheEvict(value = "momentCache", key = "#p0", cacheManager = CACHE_MANAGER)
    public void setMomentTopicHot(String mid, int topicId, int topicHotTime) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.set("topicId", topicId);
            update.set("topicHotTime", topicHotTime);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("setMomentTopicHot error mid={} {}", mid, e.getMessage(), e);
        }
    }

    /**
     * 设置首条评论id
     */
    @CacheEvict(value = "momentCache", key = "#p0", cacheManager = CACHE_MANAGER)
    public void setMomentFirstCommentId(String mid, String commentId) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.set("firstCommentId", commentId);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("setMomentFirstCommentId error mid={} {}", mid, e.getMessage(), e);
        }
    }

    public void incrReports(String mid) {
        try {
            Query query = new Query(Criteria.where("_id").is(mid));
            Update update = new Update();
            update.inc("reports", 1);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("incrReports error mid={} {}", mid, e.getMessage(), e);
        }
    }

    public List<MomentData> getLast3DaysMomentList() {
        try {
            ObjectId last3DaysId = new ObjectId(Date.from(LocalDateTime.now().plusHours(-70).atZone(ZoneId.systemDefault()).toInstant()));
            Criteria criteria = Criteria.where("_id").gt(last3DaysId).and("show").is(MomentConstant.MOMENT_PUBLIC);
            return mongoTemplate.find(new Query(criteria), MomentData.class);
        } catch (Exception e) {
            logger.error("get last 3 days moment list error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public List<MomentData> findRecentlyMomentList(String uid, int startTime) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("c_time").gt(startTime);
            return mongoTemplate.find(new Query(criteria), MomentData.class);
        } catch (Exception e) {
            logger.error("get findRecentlyMomentList error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public List<MomentData> findRecentlyMomentListByTime(String uid, int startTime, int endTime) {
        try {
            Criteria criteria = Criteria.where("_id").gte(new ObjectId(new Date(startTime * 1000L))).lte(new ObjectId(new Date(endTime * 1000L)));
            criteria.and("uid").is(uid);
            return mongoTemplate.find(new Query(criteria), MomentData.class);
        } catch (Exception e) {
            logger.error("get findRecentlyMomentListByTime error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    /**
     * 查询最近n天公开的帖子
     */
    public List<MomentData> findPublicMomentListByTime(int startTime, int endTime) {
        try {
            Criteria criteria = Criteria.where("_id").gte(new ObjectId(new Date(startTime * 1000L))).lte(new ObjectId(new Date(endTime * 1000L)));
            criteria.and("show").is(MomentConstant.MOMENT_PUBLIC);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.ASC, "_id");
            query.with(sort);
            return mongoTemplate.find(query, MomentData.class);
        } catch (Exception e) {
            logger.error("get findMomentListByTime error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    //    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
//            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<MomentData> getRecommendMomentList(Integer topicId, Integer start, Integer size) {
        List<MomentData> dataList = new ArrayList<>();
        try {
            Criteria criteria = Criteria.where("topicId").is(topicId).and("topicHotTime").gt(0);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "topicHotTime");
            query.with(sort).skip(start).limit(size);
            dataList = mongoTemplate.find(query, MomentData.class);
        } catch (Exception e) {
            logger.error("get moment list error. {}", e.getMessage(), e);
        }
        return dataList;
    }

    public List<MomentData> getLatestMomentList(Integer topicId, Integer start, Integer size) {
        List<MomentData> dataList = new ArrayList<>();
        try {
            Criteria criteria = Criteria.where("topicId").is(topicId);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort).skip(start).limit(size);
            dataList = mongoTemplate.find(query, MomentData.class);
        } catch (Exception e) {
            logger.error("get moment list error. {}", e.getMessage(), e);
        }
        return dataList;
    }

     @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
             key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<Integer> getTopicIdCountMomentsCache(int day, int maxLimit) {
        return getTopicIdCountMoments(day, maxLimit);
    }

    public List<Integer> getTopicIdCountMoments(int day, int maxLimit) {
        int endTime = DateHelper.getNowSeconds();
        int startTime = (int) (endTime - TimeUnit.DAYS.toSeconds(7));
        Criteria criteria = Criteria.where("_id").gte(new ObjectId(new Date(startTime * 1000L)))
                .lte(new ObjectId(new Date(endTime * 1000L))).and("topicId").gt(0);
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("topicId").count().as("count"),
                Aggregation.sort(Sort.by(Sort.Order.desc("count"))),
                Aggregation.limit(maxLimit));
        List<Map> mapList = mongoTemplate.aggregate(agg, TABLE_NAME, Map.class).getMappedResults();
        List<Integer> topicIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(mapList)) {
            for (Map recordMg : mapList) {
                topicIdList.add(Integer.valueOf(recordMg.get("_id").toString()));
            }
        }
        return topicIdList;
    }
}
